# This is the rules file.

groups:
- name: example
  rules:

  - alert: interfaces_interface_state
    expr: count_over_time(openconfig_interfaces:interfaces_interface_state{target="G1R626U000313"}[5m]) == 0 # 5min之内一直离线
    for: 5m
    labels:
        severity: page
    annotations:
        summary: "interface {{ $labels.target }} down in 5min"
        description: "{{ $labels.target }} interface state down"

  - alert: lldp_interfaces_interface_state_counters_frame_discard
    expr: rate(openconfig_lldp:lldp_interfaces_interface_state_counters_frame_discard{target="G1R626U000313"}[5m]) > 5 # 5min之内一直离线
    for: 5m
    labels:
        severity: page
    annotations:
        summary: "{{ $labels.target }} frame_discard increase in 5min"
        description: "{{ $labels.target }} frame_discard increase in 5min"
