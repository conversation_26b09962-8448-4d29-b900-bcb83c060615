import json
import logging
import traceback
import threading
import redis
import socket
import paramiko
from cryptography.fernet import Fernet
from collections import defaultdict
from flask import Blueprint, jsonify, Response, request
from sqlalchemy import and_, or_, func
from server.db.models import inventory
from server.db.models.monitor import Event
from server.db.models.otn import OtnTempData, OtnDeviceBasic, FmtDeviceCards, DcsDeviceCards, DcsDeviceBasic, M6200DeviceCards
from server.db.models.inventory import SwitchNeInfo
from server.util.permission import admin_permission
from server.util import fmt_util, dcp920_util, utils
from util import m6200_util

inven_db = inventory.inven_db
otn_module = Blueprint("otn_module", __name__, template_folder="templates")
LOG = logging.getLogger(__name__)

mapModel2Series = {
    "DCP920": 1,
    "FMT": 2,
    "D6000": 3,
    "M6200": 4,
}

@otn_module.route("/action/add", methods=["POST"])
@admin_permission.require(http_exception=403)
def add_device():
    """
        简单化处理；group的从属关系交给otn那边redis读取=>redux前端维护
    """
    data = request.get_json()
    name = data["name"]
    model = data["model"]
    ip = data["ip"]
    group = data["group"].split(":")[-1]
    info = {}

    # 数据存库并触发一次设备基本信息同步
    db_session = inven_db.get_session()
    try:
        otnDeviceBasic = db_session.query(OtnDeviceBasic).filter(OtnDeviceBasic.ip == ip).first()
        if otnDeviceBasic:
            raise ValueError("The device already exists")
        with db_session.begin():
            otnDeviceBasic = OtnDeviceBasic(name=name, model=model, ip=ip, series=mapModel2Series[model])
            db_session.add(otnDeviceBasic)
            sni = SwitchNeInfo()
            sni.sn = data["ip"]
            sni.switch_menu_tree_id = 10000 if group == "otnRoot" else int(group) + 10000
            inven_db.insert(sni, db_session)
        new_fmt_obj = db_session.query(OtnDeviceBasic).filter(OtnDeviceBasic.ip == ip).first()
        id = new_fmt_obj.id
        thread = threading.Thread(target=async_get_device_info, args=(id, ip, model,))
        thread.start()
    except ValueError as v:
        LOG.error(str(v))
        info = {"apiResult": "fail", "apiMessage": str(v)}
    except Exception as e:
        LOG.exception(traceback.format_exc())
        info = {"apiResult": "fail", "apiMessage": "Add device info failed"}
    else:
        info = {"apiResult": "complete", "apiMessage": "Add device info success"}
    finally:
        return jsonify(info)

def async_get_device_info(id, ip, model):
    if ip is None:
        return None
    print("async_get_device_info ip:" + ip)
    if model == "FMT" or model == "D6000":
        fmt_util.beat_sync_otn_device_info_single(id, ip)
        fmt_util.subscribe_trap_message(ip, model)
    elif model == "DCP920":
        dcp920_util.beat_sync_dcp920_device_info_single(id, ip)
    elif model == "M6200":
        m6200_util.sync_otn_device_info_single(id, ip)
        m6200_util.subscribe_trap_message(ip, model)

@otn_module.route("/action/query", methods=["GET"])
@admin_permission.require(http_exception=403)
def query_device():
    """
        简单化处理；group的从属关系交给otn那边redis读取=>redux前端维护
    """
    res = dict()
    res["apiResult"] = "success"
    res["apiMessage"] = ""
    res["neInfo"] = list()
    res["groupInfo"] = list()
    try:
        ne_switches = inven_db.get_collection(SwitchNeInfo)
        for ne_switch in ne_switches:
            item = inven_db.get_model(OtnDeviceBasic, filters={'ip': [ne_switch.sn]})
            if item:
                ne_dict = dict()
                ne_dict["id"] = f"config:ne:{item.model.lower()}:{item.ip}"
                ne_dict["value"] = {}
                ne_dict["value"]["host"] = item.ip
                ne_dict["value"]["port"] = 4001
                ne_dict["value"]["name"] = item.name
                ne_dict["value"]["username"] = ""
                ne_dict["value"]["password"] = ""
                ne_dict["value"]["ne_id"] = f"{item.ip}:4001"
                ne_dict["value"]["state"] = ""
                group_id = ne_switch.switch_menu_tree_id
                ne_dict["value"]["group"] = "otnRoot" if group_id == 10000 else f"nms:group:{group_id - 10000}"
                ne_dict["value"]["type"] = str(item.series + 5)
                ne_dict["value"]["runState"] = item.reachable_status
                ne_dict["value"]["lng"] = item.longitude
                ne_dict["value"]["lat"] = item.latitude
                res["neInfo"].append(ne_dict)
    except Exception as e:
        LOG.exception(traceback.format_exc())
        res["apiResult"] = "fail"
        res["apiMessage"] = "Get otn map info failed"
    finally:
        return jsonify(res)

@otn_module.route("/action/delete", methods=["DELETE"])
@admin_permission.require(http_exception=403)
def del_device():
    data = request.args
    id = data.get("id")
    ip = data.get("ip")
    if id is None and ip is None:
        return Response(json.dumps({"data": "", "errorCode": 1, "errorMsg": "At least one of IP and ID is required!"}),
                        mimetype="application/json")
    db_session = inven_db.get_session()
    device_basic = db_session.query(OtnDeviceBasic).filter(OtnDeviceBasic.ip == ip).first()
    if not device_basic:
        LOG.error(f"Device not found for IP {ip}")
        return Response(json.dumps({"data": "", "errorCode": 1, "errorMsg": "Device is not exist!"}),
                        mimetype="application/json")
    if device_basic.series == 4:  # M6200
        m6200_util.unsubscribe_trap_message(ip, device_basic.model)
    if id is None:
        db_session.query(OtnDeviceBasic).filter(OtnDeviceBasic.ip == ip).delete()
        db_session.query(SwitchNeInfo).filter(SwitchNeInfo.sn == ip).delete()
        db_session.query(OtnTempData).filter(OtnTempData.ip == ip).delete()
    else:
        db_session.query(OtnDeviceBasic).filter(OtnDeviceBasic.id == id).delete()
        db_session.query(OtnTempData).filter(OtnTempData.id == id).delete()

    result = {"data": "", "errorCode": 0, "errorMsg": ""}
    return Response(json.dumps(result), mimetype="application/json")

@otn_module.route("/action/modify", methods=["PUT"])
@admin_permission.require(http_exception=403)
def modify_device():
    data = request.args
    id = data.get("id")
    ip = data.get("ip")
    name = data.get("name")
    group = data.get("group")
    if id is None and ip is None:
        return Response(json.dumps({"data": "", "errorCode": 1, "errorMsg": "At least one of IP and ID is required!"}),
                        mimetype="application/json")

    db_session = inven_db.get_session()
    if id is None:
        db_session.query(OtnDeviceBasic).filter(OtnDeviceBasic.ip == ip).update({
            OtnDeviceBasic.name: name})
    else:
        db_session.query(OtnDeviceBasic).filter(OtnDeviceBasic.id == id).update({
            OtnDeviceBasic.name: name})

    result = {"data": "", "errorCode": 0, "errorMsg": ""}
    return Response(json.dumps(result), mimetype="application/json")

@otn_module.route('/map/set_otn_location', methods=['POST'])
@admin_permission.require(http_exception=403)
def set_otn_location():
    """
        neList: [neKey1]
        groupList: [groupKey1,...],
        lng: xxx,
        lat: xxx
    """
    try:
        data = request.get_json()
        ne_list = data["neList"]
        # group_list = data.get("groupList", [])
        lng = data["lng"]
        lat = data["lat"]
        session = inven_db.get_session()
        with session.begin(subtransactions=True):
            # if group_list:
            #     for group_key in group_list:
            #         switch_tree_info = session.query(SwitchMenuTreeInfo).filter(SwitchMenuTreeInfo.group_id == group_key)
            #         switch_tree_info.update({"longitude": lng, "latitude": lat})
            for ne_key in ne_list:
                otn_ne = session.query(OtnDeviceBasic).filter(
                    OtnDeviceBasic.ip == ne_key.split(":")[-1])
                if otn_ne.first():
                    otn_ne.update({"longitude": lng, "latitude": lat})
    except Exception as e:
        LOG.exception(traceback.format_exc())
        info = {"apiResult": "fail", "apiMessage": "Set switch location failed"}
    else:
        info = {"apiResult": "complete", "apiMessage": "Set switch location success"}
    finally:
        return info

@otn_module.route('/map/set_otn_tree_group_id', methods=['POST'])
@admin_permission.require(http_exception=403)
def set_otn_tree_group_id():
    """
        neList: [neKey1]
        groupList: [groupKey1,...],
        lng: xxx,
        lat: xxx
    """
    try:
        data = request.get_json()
        source_id = data["sourceId"]
        target_id = data["targetId"]
        session = inven_db.get_session()
        with session.begin(subtransactions=True):
            otn_device = session.query(OtnDeviceBasic).filter(OtnDeviceBasic.ip == source_id.split(":")[-1]).first()
            otn_ne = session.query(SwitchNeInfo).filter(SwitchNeInfo.sn == otn_device.ip)
            if otn_ne.first():
                group_id = 10000 if target_id == "otnRoot" else int(target_id.split(":")[-1]) + 10000
                otn_ne.update({"switch_menu_tree_id": group_id})
    except Exception as e:
        LOG.exception(traceback.format_exc())
        info = {"apiResult": "fail", "apiMessage": "Set otn group failed"}
    else:
        info = {"apiResult": "complete", "apiMessage": "Set otn group success"}
    finally:
        return info

mapLayer2Series = {
    "l0": [2],
    "l1": [3],
    "performance": [4],
    "l0-oeo": [4]
}

@otn_module.route("/get_otn_device_ips", methods=["GET"])
@admin_permission.require(http_exception=403)
def get_otn_device_ips():
    otn_ips = {"data": [], "errorMsg": ""}
    filter_card = request.args.get("filterCard")
    layer = request.args.get("layer")
    try:
        with inven_db.get_session() as db_session:
            if filter_card == "ALL":
                device_objs = db_session.query(OtnDeviceBasic).filter(OtnDeviceBasic.series.in_(mapLayer2Series[layer])).all()
                otn_ips["data"] = [{"ip": i.ip, "name": i.name, "type": str(i.series+5)} for i in device_objs if device_objs]
            elif filter_card == "OEO":
                device_ids_query = db_session.query(M6200DeviceCards).filter(or_(
                    M6200DeviceCards.model.like('%OEO%'),
                    M6200DeviceCards.type.like('%OEO%')
                )).all()
                device_ids = [row.device_id for row in device_ids_query]
                device_objs = db_session.query(OtnDeviceBasic).filter(OtnDeviceBasic.id.in_(device_ids),
                                                                      OtnDeviceBasic.series.in_(mapLayer2Series["l0-oeo"])).all()
                otn_ips["data"] = [{"ip": i.ip, "name": i.name, "type": str(i.series+5)} for i in device_objs if device_objs]
            elif filter_card == "EDFA":
                device_ids_query = db_session.query(FmtDeviceCards).filter(or_(
                    FmtDeviceCards.model.like('%EDFA%'),
                    FmtDeviceCards.model.like('%FMTPA-Array%'),
                    FmtDeviceCards.model.like('%FMTBA-Array%'),
                    FmtDeviceCards.model.like('%HPA%'),
                    FmtDeviceCards.model.like('%SOA%'),

                )).all()
                device_ids = [row.device_id for row in device_ids_query]
                device_objs = db_session.query(OtnDeviceBasic).filter(OtnDeviceBasic.id.in_(device_ids),
                                                                      OtnDeviceBasic.series.in_(mapLayer2Series[layer])).all()
    except Exception as e:
        otn_ips["errorCode"] = 500
        otn_ips["errorMsg"] = f"Error: {str(e)}"
    else:
        otn_ips["errorCode"] = 200
        otn_ips["data"] = [{"ip": i.ip, "name": i.name, "type": str(i.series+5)} for i in device_objs if device_objs]
    finally:
        return jsonify(otn_ips)

@otn_module.route('/get_card_list', methods=['GET'])
@admin_permission.require(http_exception=403)
def get_card_list():
    db_session = inven_db.get_session()
    fmt_list = db_session.query(FmtDeviceCards.type, func.count(FmtDeviceCards.type)).group_by(FmtDeviceCards.type).all()
    dcs_list = db_session.query(DcsDeviceCards.type, func.count(DcsDeviceCards.type)).group_by(DcsDeviceCards.type).all()
    m6200_list = db_session.query(M6200DeviceCards.type, func.count(M6200DeviceCards.type)).group_by(M6200DeviceCards.type).all()
    card_counts = defaultdict(int)
    for type_name, count in fmt_list:
        card_counts[type_name] += count
    for type_name, count in dcs_list:
        card_counts[type_name] += count
    for type_name, count in m6200_list:
        card_counts[type_name] += count
    return jsonify({"status": 200, "data": dict(card_counts)})

@otn_module.route("/config/set_device_note", methods=["POST"])
@admin_permission.require(http_exception=403)
def modify_port_note():
    data = request.get_json()
    ip = data.get("ip")
    note = data.get("note")
    if (ip is None) or (note is None):
        return Response(
            json.dumps({"data": "", "errorCode": 1, "errorMsg": "Ip and note is required!"}),
            mimetype="application/json")
    db_session = inven_db.get_session()
    db_result = db_session.query(OtnTempData).filter(OtnTempData.ip == ip).update({OtnTempData.description: note})
    if db_result > 0:
        result = {"data": note, "errorCode": 0, "errorMsg": ""}
    else:
        result = {"data": "", "errorCode": 1, "errorMsg": "Update note failed"}
    return Response(json.dumps(result), mimetype="application/json")


@otn_module.route("/get_filter_otn_event", methods=["POST"])
@admin_permission.require(http_exception=403)
def get_fmt_all_event():
    data = request.get_json()
    info = {}
    try:
        neIp = data.get("NeIp", "")
        with inven_db.get_session() as db_session:
            pre_query = db_session.query(Event).filter(Event.sn == neIp, Event.status == "unread")
            page_num, page_size, total_count, alarms_list = utils.query_helper(Event, pre_query=pre_query)
    except Exception as e:
        info = {"status": 500, "msg": f"Error: {e}"}
    else:
        info = {"data": [pk.make_dict() for pk in alarms_list],
                "page": page_num,
                "pageSize": page_size,
                "total": total_count,
                "status": 200}
    finally:
        return jsonify(info)

token_key = b'k8jajQxb2_XWatk4T6jGLTEVAyXxuYeeTJl7U7R_SYw='
cipher = Fernet(token_key)

redis_client = redis.Redis(
    host='tnms_redis',
    port=6379,
    db=0,
    username='redis',
    password='fssecret'
)

def decrypt_password(encrypted_password: str) -> str:
    try:
        decrypted_bytes = cipher.decrypt(encrypted_password.encode())
        return decrypted_bytes.decode()
    except Exception as e:
        print("Password decryption failed:", e)
        return None

@otn_module.route("/check_ne_password", methods=["POST"])
def check_ne_password():
    data = request.get_json()
    host = data.get("host")
    port = data.get("port")
    username = data.get("username")
    encrypted_password = data.get("password")

    password = decrypt_password(encrypted_password)
    if not password:
        return jsonify({"apiResult": "fail", "apiMessage": "Password decryption failed"})

    client = paramiko.SSHClient()
    client.set_missing_host_key_policy(paramiko.AutoAddPolicy())

    try:
        client.connect(hostname=host, username=username, password=password, timeout=10)
        redis_client.json().set(f"config:ne:{host}:{port}", '$.runState', 1)
        return jsonify({"apiResult": "complete", "apiMessage": "Login success"})
    except Exception as e:
        redis_client.json().set(f"config:ne:{host}:{port}", '$.runState', 0)
        return jsonify({"apiResult": "fail", "apiMessage": f"Login failed: {str(e)}"})
    finally:
        client.close()

@otn_module.route("/store_mask_info", methods=["GET"])
@admin_permission.require(http_exception=403)
def store_mask_info():
    rc = redis.StrictRedis(host='redis-service', port=6379, db=0, decode_responses=True)
    try:
        ip = request.args.get('ip')
        encrypted_password = request.args.get('password')
        username = request.args.get('username')
        password = decrypt_password(encrypted_password)
        if not ip or not encrypted_password or not username:
            return jsonify({"error": "Missing required parameters"}), 400

        password = decrypt_password(encrypted_password)
        if password is None:
            return jsonify({"error": "Password decryption failed"}), 500

        ssh = paramiko.SSHClient()
        ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())

        try:
            ssh.connect(
                hostname=ip,
                port=22,
                username=username,
                password=password,
                timeout=10,
                banner_timeout=10,
                auth_timeout=10,
                look_for_keys=False,
                allow_agent=False
            )
        except TimeoutError:
            return jsonify({"error": "Connection timed out. Please check the target host's network connectivity."}), 500
        except paramiko.SSHException as e:
            return jsonify({"error": f"SSH connection error: {e}"}), 500
        shell = ssh.invoke_shell()
        shell.settimeout(10)
        shell.send('display ip-address\n')
        output = ''
        while True:
            try:
                data = shell.recv(1024).decode('utf-8')
                output += data
                if not data:
                    break
            except socket.timeout:
                break
        lines = output.strip().split('\n')
        routes_info = {}
        current_route = []
        for line in lines:
            line = line.strip()
            if line.startswith("Route") or line.startswith("lo:"):
                if current_route:
                    last_line = current_route[-1].strip()
                    if len(last_line.split(': ')) > 1:
                        ip_addr = last_line.split(': ')[0].strip()
                        routes_info[ip_addr] = "\n".join(current_route)
                current_route = [line]
            else:
                current_route.append(line)
        rc.hmset(f"mask_info:{ip}", routes_info)

        return jsonify({"success": True}), 200

    except Exception as e:
        import traceback
        traceback.print_exc()
        return jsonify({"error": f"Failed to connect or execute command: {e}"}), 500

    finally:
        if 'shell' in locals() and shell:
            shell.close()
        if 'ssh' in locals() and ssh:
            ssh.close()


token_key = b'k8jajQxb2_XWatk4T6jGLTEVAyXxuYeeTJl7U7R_SYw='
cipher = Fernet(token_key)
def decrypt_password(encrypted_password: str) -> str:
    try:
        decrypted_bytes = cipher.decrypt(encrypted_password.encode())
        return decrypted_bytes.decode()
    except Exception as e:
        print("Password decryption failed:", e)
        return None
    
@otn_module.route("/get", methods=["GET"])
@admin_permission.require(http_exception=403)
def get_info():
    rc = redis.StrictRedis(host='redis-service', port=6379, db=0, decode_responses=True)
    ip = request.args.get('ip')
    if ip:  
        redis_key = f"mask_info:{ip}"
        redis_data = rc.hgetall(redis_key)
        if redis_data:
            result = {"data": redis_data, "errorCode": 0, "errorMsg": ""}
        else:
            result = {"data": "", "errorCode": 1, "errorMsg": "Data is empty!"}
    else:
        result = {"data": "", "errorCode": 2, "errorMsg": "Missing required parameter: 'ip'"}

    return Response(json.dumps(result), mimetype="application/json")