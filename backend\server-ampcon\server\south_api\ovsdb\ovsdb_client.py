import logging
from ovs import stream
from ovs import jsonrpc
from ovs.jsonrpc import Message

try:
    from server import cfg
except:
    cfg = None

DEFAULT_LIMIT = 2000


class ParamTypeError(Exception):
    pass


LOG = logging.getLogger(__name__)


class OvsdbClient(object):

    LOCK = "lock"
    UNLOCK = "unlock"
    MONITOR_COND = "monitor_cond"
    MONITOR = "monitor"

    LOCK_REPLY = "locked"
    LOCK_REPLY_STOLEN = "stolen"

    TRANSACT = "transact"

    INSERT = "insert"
    DELETE = "delete"
    UPDATE = "update"
    SELECT = "select"

    table_name = None
    database = None

    _clients = {}

    def __init__(self, remote):
        self.remote = remote
        self.stream = None
        self.rpc = None

    def connect(self):
        _, self.stream = stream.Stream.open(self.remote)
        self.rpc = jsonrpc.Connection(self.stream)

    def _transact(self, params):
        msg = Message.create_request(self.TRANSACT, params)
        return self.process_msg(msg)

    def _add(self, params):
        msg = Message.create_request(self.TRANSACT, self.wrap_params(params))
        error, result = self.process_msg(msg)
        return error == 0

    def _delete(self, params):
        msg = Message.create_request(self.TRANSACT, self.wrap_params(params))
        error, result = self.process_msg(msg)
        return error == 0

    def _update(self, params):
        msg = Message.create_request(self.TRANSACT, self.wrap_params(params))
        error, result = self.process_msg(msg)
        if error == 0 and result[0]['count'] > 0:
            return True

    def _get(self, params):
        msg = Message.create_request(self.TRANSACT, self.wrap_params(params))
        return self.process_msg(msg)

    def wrap_params(self, params):
        if type(params) == dict:
            return [self.database, params]
        elif type(params) == list:
            return [self.database].extend(params)
        else:
            raise ParamTypeError('params must be dict or list')

    def process_msg(self, msg):
        self.rpc.send_block(msg)
        error, result = self.rpc.recv_block()
        if not result:
            LOG.warning('%s process msg %s error %s', self.remote, msg, error)
        return error, result.result if error == 0 else None

    def __getattr__(self, item):

        def wrapper(*args, **kwargs):
            method = '_%s' % item
            error, self.stream = stream.Stream.open(self.remote)
            if error != 0:
                return None
            self.rpc = jsonrpc.Connection(self.stream)
            r = getattr(self, method, None)
            result = None
            if r:
                result = r(*args, **kwargs)
            self.rpc.close()
            return result
        return wrapper

    @classmethod
    def get_ovsdb_client(cls, *args, **kwargs):
        if args[0] in cls._clients:
            return cls._clients.get(args[0])
        else:
            _client = cls(*args, **kwargs)
            cls._clients[args[0]] = _client
            return _client


class MacVlan(OvsdbClient):

    table_name = 'Mac_Vlan'
    database = 'hardware_vtep'

    def add_mac_vlan(self, mac, vlan, priority=0):
        operation = {
            'op': self.INSERT,
            'table': self.table_name,
            'row': {
                'MAC': mac,
                'vlan': vlan,
                'priority': priority
            }
        }
        return self.add(operation)

    def update_mac_vlan(self, mac, vlan, priority=0):
        error, rows = self.get_entry(mac)
        if error != 0:
            return False

        if len(rows) > 0:
            operation = {
                'op': self.UPDATE,
                'table': self.table_name,
                'where': self.mac_condition(mac),
                'row': {
                    'MAC': mac,
                    'vlan': vlan,
                    'priority': priority
                }
            }
            return self.update(operation)
        else:
            if self.overfull():
                return False
            return self.add_mac_vlan(mac, vlan, priority=priority)

    def overfull(self):
        limit = cfg.CONF.mac_vlan_limit if cfg else DEFAULT_LIMIT
        return self.current_rows() >= limit

    def del_mac_vlan(self, mac):
        operation = {
            'op': self.DELETE,
            'table': self.table_name,
            'where': self.mac_condition(mac)
        }
        return self.delete(operation)

    def get_all(self):
        operation = {
            'op': self.SELECT,
            'table': self.table_name,
            'where': []
        }
        error, result = self.get(operation)
        rows = result[0]['rows']
        return error, rows

    def current_rows(self):
        error, rows = self.get_all()
        return len(rows) if error == 0 else 0

    def get_entry(self, mac):
        operation = {
            'op': self.SELECT,
            'table': self.table_name,
            'where': self.mac_condition(mac)
        }
        error, result = self.get(operation)
        rows = result[0]['rows']
        return error, rows

    def mac_condition(self, mac):
        return [['MAC', '==', mac]]


class MacLearning(OvsdbClient):
    table_name = 'Mac_Vlan'
    database = 'hardware_vtep'

    def get_all(self):
        operation = {
            'op': self.SELECT,
            'table': self.table_name,
            'where': []
        }
        error, result = self.get(operation)
        rows = result[0]['rows']
        return error, rows


def get_mac_vlan_client(sn, ip, port=6640, **kwargs):
    return MacVlan.get_ovsdb_client('tcp:%s:%d' % (ip, port), **kwargs)


def get_mac_learing_client(sn, ip, port=6640, **kwargs):
    return MacLearning.get_ovsdb_client('tcp:%s:%d' % (ip, port), **kwargs)


if __name__ == '__main__':
    # client = get_mac_vlan_client('test', '************')
    # print client.update_mac_vlan('00:00:00:00:75:30', 11, priority=3)
    # print client.current_rows()
    # print client.get_entry('00:00:00:00:75:30')
    # print client.update_mac_vlan('00:00:00:00:75:30', 11, priority=3)
    # print client.del_mac_vlan('00:00:00:00:75:30')
    # print client.add_mac_vlan('00:00:00:00:75:30', 11, priority=3)
    client = get_mac_learing_client('test', '************')
    print(client.get_all())

