package main

import (
	"gnmi_exporter/collector"
	"gnmi_exporter/config"
	"log"
	"net/http"

	"github.com/prometheus/client_golang/prometheus"
	"github.com/prometheus/client_golang/prometheus/promhttp"

	_ "net/http/pprof"
)

func main() {
	go http.ListenAndServe(":9999", nil)
	log.SetFlags(log.Ldate | log.Ltime | log.Lshortfile)

	config, err := config.LoadFile([]string{collector.ConfigFile})

	if err != nil {
		log.Fatalln("load config failed")
	}

	gnmicollector := collector.NewGNMICollector(*config)

	prometheus.Register(gnmicollector)

	http.Handle("/metrics", promhttp.Handler())
	if err := http.ListenAndServe(":5000", nil); err != nil {
		log.Fatalln(err)
	}
}
