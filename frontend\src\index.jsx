import React, {useEffect, useLayoutEffect} from "react";
import ReactDOM from "react-dom/client";
import {RouterProvider} from "react-router-dom";
import router from "@/router/route";
import {Provider, useDispatch} from "react-redux";
import store from "@/store/store";
import {ConfigProvider, App, Modal, Empty} from "antd";
import EmptyPic from "@/assets/images/App/empty.png";
import "normalize.css";
import "@/styles/table-styles.css";
import "@/index.css";

const root = ReactDOM.createRoot(document.getElementById("root"));

document.title = import.meta.env.VITE_APP_EXPORT_MODULE;
function useAppInitialization() {
    const dispatch = useDispatch();
    const [modal, contextHolder] = Modal.useModal();

    useEffect(() => {
        if (["AmpCon-SUPER", "AmpCon-T"].includes(import.meta.env.VITE_APP_EXPORT_MODULE)) {
            import("@/modules-otn/utils/util").then(({setRootModal}) => {
                setRootModal(modal);
            });
        }
    }, [modal]);

    useLayoutEffect(() => {
        if (["AmpCon-SUPER", "AmpCon-T"].includes(import.meta.env.VITE_APP_EXPORT_MODULE)) {
            import("@/modules-otn/apis/api").then(({initAxios}) => {
                initAxios(dispatch);
            });
        }
    }, [dispatch]);

    return contextHolder;
}

const AppMain = () => {
    const contextHolder = useAppInitialization();

    return (
        <App>
            <RouterProvider router={router} />
            {contextHolder}
        </App>
    );
};

const AppRender = () => {
    return (
        <Provider store={store}>
            <ConfigProvider
                theme={{
                    token: {
                        colorPrimary: "#14C9BB",
                        borderRadiusLG: "5px",
                        fontFamily: "Lato",
                        borderRadius: 5
                    },
                    components: {
                        Menu: {
                            itemColor: "#FFFFFF",
                            itemSelectedColor: "#FFFFFF"
                        },
                        Tabs: {
                            itemColor: "#929A9E",
                            borderRadius: 2
                        },
                        Button: {
                            defaultBorderColor: "#14C9BB",
                            colorText: "#14C9BB",
                            height: "60px",
                            borderRadius: 2
                        },
                        Input: {borderRadius: 2},
                        Select: {borderRadius: 2},
                        InputNumber: {borderRadius: 2},
                        Radio: {borderRadius: 2},
                        DatePicker: {borderRadius: 2},
                        Table: {
                            tableLayout: "auto",
                            borderRadius: 2
                        }
                    }
                }}
                renderEmpty={customizeRenderEmpty}
            >
                <AppMain />
            </ConfigProvider>
        </Provider>
    );
};

root.render(<AppRender />);

const customizeRenderEmpty = () => {
    return (
        <div className="custom-empty-container">
            <Empty image={EmptyPic} description="No Data" imageStyle={{marginTop: 16, marginBottom: 0}} />
        </div>
    );
};
