[DEFAULT]

# log file name and location
# log-file =

# show debug output
# verbose = False

# log to standard error
# use-stderr = True

# log file directory
# log-dir

# log configuration file
# log-config-file =

# run mode
# standalone = True

# pid file
# pid-file = None

[message]

# how to process parsed BGP message?

# Whether the BGP message is written to disk
# write_disk = True

# the BGP messages storage path
# write_dir = ~/data/bgp/

# The Max size of one BGP message file, the unit is MB
# write_msg_max_size = 500

# Whether write keepalive message to disk
# write_keepalive = False

# The output format of bgp messagees
# two options: list and json, default value is list
# format = json


[bgp]

# BGP global configuration items

# peer configuration file
# config_file =

# The interval to start each BGP peer
# peer_start_interval = 10

# The Global config for address family and sub address family
# if you want to support more than one address family, you can set afi_safi = ipv4, ipv6, ....
# we support: ipv4, ipv6, flowspec, vpnv4, evpn, vpnv6
afi_safi = ipv4,evpn

# role tag
# tag =

# ===================== items for peer configuration ================================
# the following parameters will be ignored if conf_file is configured
# and this configuration only support one bgp peer, if you need start more peers in
# one yabgp process, please use conf_file to configure them.

# remote as number
remote_as = 65000

# remote ip address
remote_addr = '************'

# local as number
local_as = 65000

# local ip address
local_addr = '************'

# The MD5 string
# md5 =

# Whether maintain bgp rib table
# rib = False

# ======================= BGP capacity =============================

# support 4 bytes AS
# four_bytes_as = True

# support route refresh
# route_refresh = True

# support cisco route refresh
# cisco_route_refresh = True

# BGP add path feature.This field indicates whether the sender is
# (a) able to receive multiple paths from its peer
# (b) able to send multiple paths to its peer
# (c) both send and receive for the <AFI, SAFI>.
# now we only support ipv4, so there are
# 'ipv4_send', 'ipv4_receive' and 'ipv4_both', the default value is
# ipv4_both

# add_path =

# suport graceful restart or not
# graceful_restart = True

# support cisco multi session or not
# cisco_multi_session = True

# support enhanced route refresh or not
# enhanced_route_refresh = True

[rest]
# Address to bind the API server to.
bind_host = 0.0.0.0

# Port the bind the API server to.
bind_port = 8801

# username and password for api server
username = admin
password = admin