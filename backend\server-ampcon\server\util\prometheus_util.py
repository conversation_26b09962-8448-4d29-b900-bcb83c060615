import logging
import requests
import traceback
import copy
from datetime import datetime, timedelta
from server import cfg

LOG = logging.getLogger(__name__)

def format_query_time(start_time=None, end_time=None):
    delta_time = "5m"
    timestamp = None 
    step = "15s"

    now_timestamp = datetime.now().timestamp()

    if end_time:
        if end_time > now_timestamp:
            end_time = now_timestamp
        timestamp = end_time
    else:
        end_time = now_timestamp
    
    if start_time:
        if start_time >= end_time:
            start_time = end_time - 300 # 5分钟 = 300秒
        else:
            time_delta = datetime.fromtimestamp(end_time) - datetime.fromtimestamp(start_time)
            step = f"{(time_delta.days +1) * 15}s"
            minutes_diff = int(time_delta.total_seconds() // 60)
            delta_time = f"{minutes_diff}m"
    else:
        start_time = end_time - 300 # 5分钟 = 300秒
        
    return timestamp, delta_time, start_time, end_time, step


def generate_time_points_dict(start_timestamp, end_timestamp, step):
    time_points_dict = {}
    interval = timedelta(seconds=int(step[:-1]))

    current_time = datetime.fromtimestamp(start_timestamp)
    end = datetime.fromtimestamp(end_timestamp)

    while current_time <= end:
        formatted_time = current_time.strftime('%Y-%m-%d %H:%M:%S')
        time_points_dict[formatted_time] = [formatted_time, None]
        current_time += interval

    return time_points_dict


def query_prometheus(query, time=None):
    try:
        response = requests.get(f'http://{cfg.CONF.prometheus_url}/api/v1/query', params={'query': query, 'time': time})
        data = response.json()
        if data['status'] != 'success':
            LOG.error("query prometheus failed")
            LOG.error(data)
            return []
        else:
            # print(data)
            return data['data']['result']
    except Exception as e:
        LOG.error(f"query_prometheus error: {traceback.format_exc()}")
        return []


def query_range_prometheus(query, start_time, end_time, step="15s"):
    try:
        response = requests.get(f'http://{cfg.CONF.prometheus_url}/api/v1/query_range', params={'query': query, 'start': start_time, 'end': end_time, 'step': step})
        data = response.json()
        if data['status'] != 'success':
            LOG.error("query prometheus failed")
            LOG.error(data)
            return []
        else:
            # print(data)
            return data['data']['result']
    except Exception as e:
        LOG.error(f"query_range_prometheus error: {traceback.format_exc()}")
        return []


def query_metric_filter_by_interface(metricName, targetName, interface_list, time=None):
    interfaces = "|".join(interface_list)
    query_template  = '{{__name__="{name}", target="{target_value}", interface_name=~"{interfaces}"}}'
    modified_query = query_template.format(name=metricName, target_value=targetName, interfaces=interfaces)
    # print(modified_query)
    result = query_prometheus(modified_query, time=time)
    # print(result)
    metric_data = {}
    for data in result:
        metric = data["metric"]
        if data["metric"].get("interface_name", None):
            name = data["metric"]["interface_name"]
            metric.pop("interface_name")
        else:
            continue

        if name not in metric_data:
            metric_data[name] = []

        metric.pop("__name__")
        metric.pop("instance")
        metric.pop("job")
        metric.pop("target")
        metric_data[name] = metric
        # print(name, metric) 
    return metric_data


def query_metric_value_filter_by_interface(metricName, targetName, interface_list, time=None):
    interfaces = "|".join(interface_list)
    query_template  = '{{__name__="{name}", target="{target_value}", interface_name=~"{interfaces}"}}'
    modified_query = query_template.format(name=metricName, target_value=targetName, interfaces=interfaces)
    # print(modified_query)
    result = query_prometheus(modified_query, time=time)
    # print(result)
    metric_data = {}
    for data in result:
        metric = data["metric"]
        if data["metric"].get("interface_name", None):
            name = data["metric"]["interface_name"]
            metric.pop("interface_name")
        else:
            continue

        if name not in metric_data:
            metric_data[name] = []

        metric.pop("__name__")
        metric.pop("instance")
        metric.pop("job")
        metric.pop("target")
        metric_data[name] = metric
        # print(name, metric) 
    return metric_data

def get_port_speed_usage(metricName, targetName, interface_list, keyName, time=None):

    interfaces = "|".join(interface_list)
    step1 = '( {{__name__="{name}", target="{targetName}", interface_name=~"{interfaces}"}}' 
    step2 = ' / on(interface_name, target)'
    step3 = '{{__name__="openconfig_interfaces:interfaces_interface_openconfig_if_ethernet:ethernet_state_port_speed", target="{targetName}", interface_name=~"{interfaces}"}}'
    step4 = ' * 100 )'

    query_template = step1 + step2 + step3 + step4
    modified_query = query_template.format(name=metricName, targetName=targetName, interfaces=interfaces)
    # print(modified_query)
    result = query_prometheus(modified_query, time=time)
    # print(result)
    metric_data = {}
    for data in result:
        metric = data["metric"]
        if data["metric"].get("interface_name", None):
            name = data["metric"]["interface_name"]
            metric.pop("interface_name")
        else:
            continue
        
        if data["value"][1] == "+Inf" or data["value"][1] == "NaN":
            rawValue = 0
            value = 0
        else:
            rawValue = data["value"][1]
            value = float(data["value"][1])
        if name not in metric_data:
            metric_data[name] = []

        # metric.pop("__name__")
        # metric.pop("instance")
        # metric.pop("job")
        metric.pop("target")
        if value == 0:
            metric[keyName] = "0.00%"
        elif 0 < value < 0.01:
            metric[keyName] = "0.01%"
        else:
            metric[keyName] = str(round(value, 2)) + "%"
        metric['raw_' + keyName] = rawValue
        metric_data[name] = metric
        # print(name, metric) 
    return metric_data


def query_metric(metricName, targetName, time=None):
    query_template  = '{{__name__="{name}", target="{target_value}"}}'
    modified_query = query_template.format(name=metricName, target_value=targetName)
    # print(modified_query)
    result = query_prometheus(modified_query, time=time)
    # print(result)
    metric_data = []
    for data in result:
        metric = data["metric"]
        metric.pop("__name__")
        metric.pop("instance")
        metric.pop("job")
        metric.pop("target")
        metric_data.append(metric)
        # print(name, metric) 
    return metric_data


def query_counters_with_prefix(metricPrefix ,targetName, interface_list, time=None):
    # 构造查询表达式
    interfaces = "|".join(interface_list)
    query_template  = '{{__name__=~"{prefix}.*", target="{target_value}", interface_name=~"{interfaces}"}}'
    query_template = query_template.format(prefix=metricPrefix, target_value=targetName, interfaces=interfaces)

    # print(query_template)
    result = query_prometheus(query_template, time)
    # print(result)
    counters_data = {}
    for data in result:
        name = data["metric"]["__name__"][len(metricPrefix):]
        interface_name = data["metric"]["interface_name"]
        if interface_name not in counters_data:
            counters_data[interface_name] = {}
        counters_data[interface_name][name] = int(float(data["value"][1]))

    return counters_data


def query_ai_with_prefix(metricPrefix ,targetName, interface_list, time=None):
    interfaces = "|".join(interface_list)
    query_template  = '{{__name__=~"{prefix}.*", target="{target_value}", interface_name=~"{interfaces}"}}'
    query_template = query_template.format(prefix=metricPrefix, target_value=targetName, interfaces=interfaces)
    result = query_prometheus(query_template, time)

    counters_data = {}
    for data in result:
        name = data["metric"]["__name__"][len(metricPrefix):]
        interface_name = data["metric"]["interface_name"]
        queue_name = data["metric"].get("queue_name", None)

        if interface_name not in counters_data:
            counters_data[interface_name] = {}

        if queue_name is None:
            counters_data[interface_name][name] = int(float(data["value"][1]))
        else:
            if queue_name not in counters_data[interface_name]:
                counters_data[interface_name][queue_name] = {}

            counters_data[interface_name][queue_name][name] = int(float(data["value"][1]))

    return counters_data


def query_counters(metricName ,targetName, interface_list, time=None):
    interfaces = "|".join(interface_list)
    query_template  = '{{__name__="{name}", target="{target_value}", interface_name=~"{interfaces}"}}'
    query_template = query_template.format(name=metricName, target_value=targetName, interfaces=interfaces)

    # print(query_template)
    result = query_prometheus(query_template, time)
    # print(result)
    counters_data = {}
    for data in result:
        if data["metric"].get("interface_name", None):
            name = data["metric"]["interface_name"]
        else:
            continue

        counters_data[name] = float(data["value"][1])

    return counters_data


def get_target_interfaces(target, time=None):
    query_template = '{{__name__="openconfig_interfaces:interfaces_interface", target="{target_value}"}}'
    result = query_prometheus(query_template.format(target_value=target), time=time)

    interface_list = [data["metric"]["interface_name"] for data in result if not data["metric"]["interface_name"].startswith("eth")]
    # print(interface_list)
    return interface_list


def query_counter_delta_topk(metric_name, metric_prefix="openconfig_interfaces:interfaces_interface_state_counters_", topk=5, target=None, start_time=None, end_time=None):
    
    timestamp, delta_time, start_time, end_time, step = format_query_time(start_time, end_time)
    time_points_dict = generate_time_points_dict(start_time, end_time, step)

    if target:
        query_template = 'topK(' + str(topk) + ', max_over_time(' + metric_prefix + metric_name + '{target="' + target + '"}['+ delta_time +']))'
    else:
        query_template = 'topK(' + str(topk) + ', max_over_time(' + metric_prefix + metric_name + '['+ delta_time +']))'
    print(query_template)   
    result = query_prometheus(query_template, timestamp)

    query_templates = []
    for metric in result:
        if 'queue_name' in metric['metric']:
            query_template = f"{metric_prefix}{metric_name}{{target=\"{metric['metric']['target']}\", interface_name=\"{metric['metric']['interface_name']}\", queue_name=\"{metric['metric']['queue_name']}\"}}"
        else:
            query_template = f"{metric_prefix}{metric_name}{{target=\"{metric['metric']['target']}\", interface_name=\"{metric['metric']['interface_name']}\"}}"
        query_templates.append(query_template)

    final_query = " or ".join(query_templates)
    result = query_range_prometheus(final_query, start_time, end_time, step)

    res = []
    for metric in result:
        time_points_value = copy.deepcopy(time_points_dict)
        for timestamp, value in metric["values"]:
            date_time = datetime.fromtimestamp(timestamp)
            date_time_str = date_time.strftime("%Y-%m-%d %H:%M:%S")
            time_points_value[date_time_str] = [date_time_str, value]

        info ={
            'target': metric['metric']['target'],
            'interface_name': metric['metric']['interface_name'],
            "values": list(time_points_value.values())
        }

        if 'queue_name' in metric['metric']:
            info['queue_name'] = metric['metric']['queue_name']

        res.append(info)

    return res

def query_modules_topk(metric_name, topk=5, target=None, start_time=None, end_time=None, mode="topK"):
    
    timestamp, delta_time, start_time, end_time, step = format_query_time(start_time, end_time)
    time_points_dict = generate_time_points_dict(start_time, end_time, step)

    # 对于模块数据由于不是单调递增的 所以根据sum_over_time获取delta time内时间序列的总和然后排序
    if target:
        query_template = mode + '(' + str(topk) + ', sum_over_time(' + metric_name + '{target="' + target + '"}['+ delta_time +']))'
    else:
        query_template = mode + '(' + str(topk) + ', sum_over_time(' + metric_name + '['+ delta_time +']))'
    # print(query_template)   
    result = query_prometheus(query_template, timestamp)

    query_templates = []
    for metric in result:
        query_template = metric_name  + '{target="' + metric['metric']['target'] + '"' + ', interface_name="' + metric["metric"]["interface_name"] + '"}'
        query_templates.append(query_template)

    final_query = " or ".join(query_templates)
    result = query_range_prometheus(final_query, start_time, end_time, step)

    res = []
    for metric in result:
        time_points_value = copy.deepcopy(time_points_dict)
        for timestamp, value in metric["values"]:
            date_time = datetime.fromtimestamp(timestamp)
            date_time_str = date_time.strftime("%Y-%m-%d %H:%M:%S")
            time_points_value[date_time_str] = [date_time_str, value]

        info ={
            'target': metric['metric']['target'],
            'interface_name': metric['metric']['interface_name'],
            "values": list(time_points_value.values())
        }
        res.append(info)
    
    return res

def query_cpu_usage_topk(topk=5, target=None, start_time=None, end_time=None):
    
    timestamp, delta_time, start_time, end_time, step = format_query_time(start_time, end_time)
    time_points_dict = generate_time_points_dict(start_time, end_time, step)
    
    # 获取每个核的total avg并计算平均值
    if target:
        query_template = 'topk(' + str(topk) + ', avg by (target) (avg_over_time(openconfig_system:system_cpus_cpu_state_total_avg{target="' + target + '"}[' + delta_time + '])))'
    else:
        query_template = 'topk(' + str(topk) + ', avg by (target) (avg_over_time(openconfig_system:system_cpus_cpu_state_total_avg['+ delta_time +'])))'

    result = query_prometheus(query_template, timestamp)

    query_templates = []
    for metric in result:
        query_template = 'avg by (target) (openconfig_system:system_cpus_cpu_state_total_avg{target="' + metric['metric']['target'] + '"})'
        query_templates.append(query_template)

    final_query = " or ".join(query_templates)
    result = query_range_prometheus(final_query, start_time, end_time, step)

    res = []
    for metric in result:
        time_points_value = copy.deepcopy(time_points_dict)
        for timestamp, value in metric["values"]:
            date_time = datetime.fromtimestamp(timestamp)
            date_time_str = date_time.strftime("%Y-%m-%d %H:%M:%S")
            time_points_value[date_time_str] = [date_time_str, round(float(value), 2)]

        info = {
            'target': metric['metric']['target'],
            "interface_name": "cpu",
            "values": list(time_points_value.values())
        }
        res.append(info)

    return res


def query_memory_usage_topk(topk=5, target=None, start_time=None, end_time=None):
    
    timestamp, delta_time, start_time, end_time, step = format_query_time(start_time, end_time)
    time_points_dict = generate_time_points_dict(start_time, end_time, step)
    
    # 获取每个核的total avg并计算平均值
    if target:
        query_template = 'topk(' + str(topk) + ', 100 * avg by (target) (avg_over_time(openconfig_system:system_memory_state_used{target="' + target + '"}[' + delta_time +']) / avg_over_time(openconfig_system:system_memory_state_physical{target="' + target + '"}[' + delta_time +'])))'
    else:
        query_template = 'topk(' + str(topk) + ', 100 * avg by (target) (avg_over_time(openconfig_system:system_memory_state_used[' + delta_time +']) / avg_over_time(openconfig_system:system_memory_state_physical[' + delta_time +'])))'

    result = query_prometheus(query_template, timestamp)

    query_templates = []
    for metric in result:
        query_template = '100 * avg by (target) (openconfig_system:system_memory_state_used{target="' + metric['metric']['target'] + '"} / openconfig_system:system_memory_state_physical{target="' + metric['metric']['target'] + '"})'
        query_templates.append(query_template)

    final_query = " or ".join(query_templates)
    result = query_range_prometheus(final_query, start_time, end_time, step)

    res = []
    for metric in result:
        time_points_value = copy.deepcopy(time_points_dict)
        for timestamp, value in metric["values"]:
            date_time = datetime.fromtimestamp(timestamp)
            date_time_str = date_time.strftime("%Y-%m-%d %H:%M:%S")
            time_points_value[date_time_str] = [date_time_str, round(float(value), 2)]

        info = {
            'target': metric['metric']['target'],
            "interface_name": "memory",
            "values": list(time_points_value.values())
        }
        res.append(info)

    return res

def query_rate_topk(metric_name, topk=5, target=None, filter=[], start_time=None, end_time=None, mode="topK"):
    
    timestamp, delta_time, start_time, end_time, step = format_query_time(start_time, end_time)
    time_points_dict = generate_time_points_dict(start_time, end_time, step)

    # 对于模块数据由于不是单调递增的 所以根据sum_over_time获取delta time内时间序列的总和然后排序
    if target:
        if filter:
            interface_name = "|".join(filter)
            query_template = f'{mode}({str(topk)}, sum_over_time({metric_name}{{target="{target}", interface_name=~"{interface_name}"}}[{delta_time}]))'
        else:
            query_template = f'{mode}({str(topk)}, sum_over_time({metric_name}{{target="{target}"}}[{delta_time}]))'
    else:
        query_template = f'{mode}({str(topk)}, sum_over_time({metric_name}[{delta_time}]))'
    # print(query_template)   
    result = query_prometheus(query_template, timestamp)

    query_templates = []
    for metric in result:
        query_template = metric_name  + '{target="' + metric['metric']['target'] + '"' + ', interface_name="' + metric["metric"]["interface_name"] + '"}'
        query_templates.append(query_template)

    final_query = " or ".join(query_templates)
    result = query_range_prometheus(final_query, start_time, end_time, step)

    res = []
    for metric in result:
        time_points_value = copy.deepcopy(time_points_dict)
        for timestamp, value in metric["values"]:
            date_time = datetime.fromtimestamp(timestamp)
            date_time_str = date_time.strftime("%Y-%m-%d %H:%M:%S")
            time_points_value[date_time_str] = [date_time_str, value]

        info ={
            'target': metric['metric']['target'],
            'interface_name': metric['metric']['interface_name'],
            "values": list(time_points_value.values())
        }
        res.append(info)
    
    return res
    

def query_attenuation_topk(topk=5, target=None, start_time=None, end_time=None):
    
    timestamp, delta_time, start_time, end_time, step = format_query_time(start_time, end_time)
    time_points_dict = generate_time_points_dict(start_time, end_time, step)

    if target:
        query_template = 'topk(' + str(topk) + ', sum_over_time(openconfig_platform:components_component_openconfig_platform_transceiver:transceiver_state_output_power{target="' + target + '"}['+ delta_time +']) -' +\
                            'sum_over_time(openconfig_platform:components_component_openconfig_platform_transceiver:transceiver_state_input_power{target="' + target + '"}['+ delta_time +']))'
    else:
        query_template = 'topk(' + str(topk) + ', sum_over_time(openconfig_platform:components_component_openconfig_platform_transceiver:transceiver_state_output_power['+ delta_time +']) -' +\
                            'sum_over_time(openconfig_platform:components_component_openconfig_platform_transceiver:transceiver_state_input_power['+ delta_time +']))'

    result = query_prometheus(query_template, timestamp)

    metriclabel = []
    for metric in result:
        info = {
            'name': "attenuation",
            'target': metric['metric']['target'],
            'interface_name': metric["metric"]["interface_name"],
        }
        metriclabel.append(info)

    query_templates = []
    for metric in result:
        query_template = "openconfig_platform:components_component_openconfig_platform_transceiver:transceiver_state_output_power - openconfig_platform:components_component_openconfig_platform_transceiver:transceiver_state_input_power" \
                        + '{target="' + metric['metric']['target'] + '"' + ', interface_name="' + metric["metric"]["interface_name"] + '"}'
        query_templates.append(query_template)

    final_query = " or ".join(query_templates)
    result = query_range_prometheus(final_query, start_time, end_time, step)


    res = []
    for metric in result:
        time_points_value = copy.deepcopy(time_points_dict)
        for timestamp, value in metric["values"]:
            date_time = datetime.fromtimestamp(timestamp)
            date_time_str = date_time.strftime("%Y-%m-%d %H:%M:%S")
            time_points_value[date_time_str] = [date_time_str, round(float(value), 2)]

        info ={
            'target': metric['metric']['target'],
            'interface_name': metric['metric']['interface_name'],
            "values": list(time_points_value.values())
        }
        res.append(info)

    return res


def query_lldp_neighbor_state(target_list, time=None):
    query_template = '{__name__="openconfig_lldp:lldp_interfaces_interface_neighbors_neighbor_state", target=~"' + '|'.join(target_list) + '"}'
    result = query_prometheus(query_template, time)
    lldp_neighbor_state = {}
    for res in result:
        state = {
            "source": res["metric"]["target"],
            "target_mac": res["metric"]["chassis_id"],
            "source_port": res["metric"]["interface_name"],
            "target_port": res["metric"]["port_id"],
        }
        source = state['source']
        source_port = state['source_port']
        key = f"{source}_{source_port}"
        # 暂定如果有重复的只取一个，有重复的说明当前时刻有变化
        if key not in lldp_neighbor_state:
            lldp_neighbor_state[key] = state

    return lldp_neighbor_state
 
 
def query_lldp_state(target, mac="", time=None):
    query_template = '{__name__="openconfig_lldp:lldp_state", target="' + target + '"}'
    result = query_prometheus(query_template, time)
    if result and result[0]["metric"].get("chassis_id"):
        state = {
            "mac_addr" : result[0]["metric"]["chassis_id"],
            "system_name" : result[0]["metric"]["system_name"],
            "system_description" : result[0]["metric"]["system_description"],
            "monitor_status": "online"
        }
    else:
        state = {
            "monitor_status": "offline",
            "mac_addr" : mac
        }
    return state
 
    
def query_node_metric(instance, metric, search_fields = {}):
    if not search_fields.get("value"):
        query_template  = '{{__name__="{name}", instance="{instance}"}}'
        modified_query = query_template.format(name=metric, instance=instance)
    else:
        query_template = '{{__name__="{name}", instance="{instance}", {field}=~"(?i).*{value}.*"}}'
    
        fields = search_fields.get("fields", [])
        value = search_fields.get("value", "")
        search_conditions = []
        for field in fields:
            search_conditions.append(query_template.format(name=metric, instance=instance, field=field, value=value))
        modified_query = " or ".join(search_conditions)

    result = query_prometheus(modified_query)
    
    metric_data = {}
    for data in result:
        if not data["metric"].get("device"):
            return data["metric"]
        else:
            device = data["metric"]["device"]
                
            if device not in metric_data:
                metric_data[device] = []
            
            metric = data["metric"]
            metric.pop("__name__")
            metric.pop("instance")
            metric.pop("job")
            metric.pop("device")
            metric_data[device] = metric 
    return metric_data


def query_node_range_metric(metric, start_time=None, end_time=None):
    query_template  = '{{__name__="{name}"}}'
    
    _, _, start_time, end_time, step = format_query_time(start_time, end_time)
    modified_query = query_template.format(name=metric)
    result = query_range_prometheus(modified_query, start_time, end_time, step)
    metric_data = {}
    for data in result:
        
        instance = data["metric"]["instance"]
            
        if instance not in metric_data:
            metric_data[instance] = []
        
        metric = data["metric"]
        metric.pop("__name__")
        metric.pop("instance")
        metric.pop("job")
        metric_data[instance].append(metric) 
    return metric_data


def query_node_topk(metric_name, topk=5, filter=None, start_time=None, end_time=None):
    
    timestamp, delta_time, start_time, end_time, step = format_query_time(start_time, end_time)
    time_points_dict = generate_time_points_dict(start_time, end_time, step)

    if filter:
        filter_list = []
        for key, value in filter.items():
            instance = key + ":9100"
            devices = "|".join(value)
            filter_template = f"max_over_time({metric_name}{{instance=\"{instance}\", device=~\"{devices}\"}}[{delta_time}])"
            filter_list.append(filter_template)
            
        filter_query = " or ".join(filter_list)
        query_template = f'topk({str(topk)}, ({filter_query}))'
    else:
        query_template = f'topk({str(topk)}, max_over_time({metric_name}[{delta_time}]) *on(device, instance) max by (instance, device) (max_over_time(node_nic_info[{delta_time}])))'

    result = query_prometheus(query_template, timestamp)
        
    query_templates = []
    for metric in result:
        query_template = f"{metric_name}{{instance=\"{metric['metric']['instance']}\", device=\"{metric['metric']['device']}\"}}"
        query_templates.append(query_template)

    final_query = " or ".join(query_templates)
    result = query_range_prometheus(final_query, start_time, end_time, step)
    
    res = []
    for metric in result:
        time_points_value = copy.deepcopy(time_points_dict)
        for timestamp, value in metric["values"]:
            date_time = datetime.fromtimestamp(timestamp)
            date_time_str = date_time.strftime("%Y-%m-%d %H:%M:%S")
            time_points_value[date_time_str] = [date_time_str, value]

        info ={
            'instance': metric['metric']['instance'].split(":")[0],
            'device': metric['metric']['device'],
            "values": list(time_points_value.values())
        }

        res.append(info)

    return res


def query_dlb_rate_topk(dividend_metric_name, divisor_metric_name, topk=5, target=None, filter=[], start_time=None, end_time=None, mode="topK"):
    
    timestamp, delta_time, start_time, end_time, step = format_query_time(start_time, end_time)
    time_points_dict = generate_time_points_dict(start_time, end_time, step)
    
    if target:
        if filter:
            interface_name = "|".join(filter)
            dividend_metric = f'{{__name__="{dividend_metric_name}", target="{target}", interface_name=~"{interface_name}"}}'
            divisor_metric = f'({{__name__="{divisor_metric_name}", target="{target}", interface_name=~"{interface_name}"}})'
        else:
            dividend_metric = f'{{__name__="{dividend_metric_name}", target="{target}"}}'
            divisor_metric = f'({{__name__="{divisor_metric_name}", target="{target}"}})'
    else:
        dividend_metric = f'{{__name__="{dividend_metric_name}"}}'
        divisor_metric = f'({{__name__="{divisor_metric_name}"}})'
    metric_template = f'({dividend_metric} / on(interface_name, target) {divisor_metric} * 100)'
    query_template = f'{mode}({str(topk)}, max_over_time({metric_template}[{delta_time}:{step}]))'
    
    # print(query_template)   
    result = query_prometheus(query_template, timestamp)

    query_templates = []
    for metric in result:
        # print(metric['metric']['target'], metric['metric']['interface_name'])
        dividend_metric = f'{{__name__="{dividend_metric_name}", target="{metric["metric"]["target"]}", interface_name="{metric["metric"]["interface_name"]}"}}'
        divisor_metric = f'({{__name__="{divisor_metric_name}", target="{metric["metric"]["target"]}", interface_name="{metric["metric"]["interface_name"]}"}})'
        metric_template = f'({dividend_metric} / on(interface_name, target) {divisor_metric} * 100)'
        query_templates.append(metric_template)
        
    final_query = " or ".join(query_templates)
    # print(final_query)
    result = query_range_prometheus(final_query, start_time, end_time, step)
    # print(result)
    
    res = []
    for metric in result:
        time_points_value = copy.deepcopy(time_points_dict)
        for timestamp, value in metric["values"]:
            date_time = datetime.fromtimestamp(timestamp)
            date_time_str = date_time.strftime("%Y-%m-%d %H:%M:%S")
            if value == "+Inf" or value == "NaN":
                value = 0
            time_points_value[date_time_str] = [date_time_str, value]

        info ={
            'target': metric['metric']['target'],
            'interface_name': metric['metric']['interface_name'],
            "values": list(time_points_value.values())
        }
        res.append(info)

    return res 