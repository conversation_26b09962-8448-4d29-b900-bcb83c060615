import React from "react";
import {Tooltip} from "antd";
import Icon from "@ant-design/icons";
import {
    dashBoardSvg,
    maintainSvg,
    monitorSvg,
    resourceSvg,
    serviceSvg,
    settingSvg,
    topologySvg
} from "@/utils/common/iconSvg";

const dcItems = [
    {
        label: (
            <Tooltip title={<div className="fixed-tooltip">Dashboard</div>} placement="right">
                Dashboard
            </Tooltip>
        ),
        key: "/dashboard",
        icon: <Icon component={dashBoardSvg} />,
        children: [
            {
                key: "/dashboard/global_view",
                label: (
                    <Tooltip title={<div className="fixed-tooltip">Global View</div>} placement="right">
                        Global View
                    </Tooltip>
                )
            },
            {
                key: "/dashboard/switch_view",
                label: (
                    <Tooltip title={<div className="fixed-tooltip">Switch View</div>} placement="right">
                        Switch View
                    </Tooltip>
                )
            },
            {
                key: "/dashboard/telemetry_dashboard",
                label: (
                    <Tooltip title={<div className="fixed-tooltip">Telemetry Dashboard</div>} placement="right">
                        Telemetry Dashboard
                    </Tooltip>
                )
            }
        ]
    },
    {
        label: (
            <Tooltip title={<div className="fixed-tooltip">Resource</div>} placement="right">
                Resource
            </Tooltip>
        ),
        key: "/resource",
        icon: <Icon component={resourceSvg} />,
        children: [
            {
                key: "/resource/upgrade_management",
                label: (
                    <Tooltip title={<div className="fixed-tooltip">Upgrade Management</div>} placement="right">
                        Upgrade Management
                    </Tooltip>
                )
            },
            {
                key: "/resource/auth_management",
                label: (
                    <Tooltip title={<div className="fixed-tooltip">Authority Management</div>} placement="right">
                        Authority Management
                    </Tooltip>
                ),
                children: [
                    {
                        key: "/resource/auth_management/device_license_management",
                        label: (
                            <Tooltip
                                className="menu-dot"
                                title={<div className="fixed-tooltip">Device License Management</div>}
                                placement="right"
                            >
                                Device License Management
                            </Tooltip>
                        )
                    },
                    {
                        key: "/resource/auth_management/group_management",
                        label: (
                            <Tooltip
                                className="menu-dot"
                                title={<div className="fixed-tooltip">Group Management</div>}
                                placement="right"
                            >
                                Group Management
                            </Tooltip>
                        )
                    },
                    {
                        key: "/resource/auth_management/fabric_management",
                        label: (
                            <Tooltip
                                className="menu-dot"
                                title={<div className="fixed-tooltip">Fabric Management</div>}
                                placement="right"
                            >
                                Fabric Management
                            </Tooltip>
                        )
                    }
                ]
            },
            {
                key: "/resource/pool",
                label: (
                    <Tooltip title={<div className="fixed-tooltip">Pool</div>} placement="right">
                        Pool
                    </Tooltip>
                ),
                children: [
                    {
                        key: "/resource/pool/ip_pool",
                        label: (
                            <Tooltip
                                className="menu-dot"
                                title={<div className="fixed-tooltip">IP Pool</div>}
                                placement="right"
                            >
                                IP Pool
                            </Tooltip>
                        )
                    },
                    {
                        key: "/resource/pool/asn_pool",
                        label: (
                            <Tooltip
                                className="menu-dot"
                                title={<div className="fixed-tooltip">ASN Pool</div>}
                                placement="right"
                            >
                                ASN Pool
                            </Tooltip>
                        )
                    },
                    {
                        key: "/resource/pool/area_pool",
                        label: (
                            <Tooltip
                                className="menu-dot"
                                title={<div className="fixed-tooltip">Area Pool</div>}
                                placement="right"
                            >
                                Area Pool
                            </Tooltip>
                        )
                    }
                ]
            }
        ]
    },
    {
        label: (
            <Tooltip title={<div className="fixed-tooltip">Service</div>} placement="right">
                Service
            </Tooltip>
        ),
        key: "/service",
        icon: <Icon component={serviceSvg} />,
        children: [
            {
                key: "/service/switch",
                label: (
                    <Tooltip title={<div className="fixed-tooltip">Switch</div>} placement="right">
                        Switch
                    </Tooltip>
                )
            },
            {
                key: "/service/nics",
                label: (
                    <Tooltip title={<div className="fixed-tooltip">NICs</div>} placement="right">
                        NICs
                    </Tooltip>
                ),
                children: [
                    {
                        key: "/service/nics/inventory",
                        label: (
                            <Tooltip
                                className="menu-dot"
                                title={<div className="fixed-tooltip">Inventory</div>}
                                placement="right"
                            >
                                Inventory
                            </Tooltip>
                        )
                    },
                    {
                        key: "/service/nics/monitoring",
                        label: (
                            <Tooltip
                                className="menu-dot"
                                title={<div className="fixed-tooltip">Monitoring</div>}
                                placement="right"
                            >
                                Monitoring
                            </Tooltip>
                        )
                    }
                ]
            },
            {
                key: "/service/global_configuration",
                label: (
                    <Tooltip title={<div className="fixed-tooltip">Global Configuration</div>} placement="right">
                        Global Configuration
                    </Tooltip>
                )
            },
            {
                key: "/service/switch_configuration",
                label: (
                    <Tooltip title={<div className="fixed-tooltip">Switch Configuration</div>} placement="right">
                        Switch Configuration
                    </Tooltip>
                )
            },
            {
                key: "/service/config_files_view",
                label: (
                    <Tooltip title={<div className="fixed-tooltip">Config Files View</div>} placement="right">
                        Config Files View
                    </Tooltip>
                )
            },
            {
                key: "/service/switch_model",
                label: (
                    <Tooltip title={<div className="fixed-tooltip">Switch Model</div>} placement="right">
                        Switch Model
                    </Tooltip>
                )
            },
            {
                key: "/service/system_config",
                label: (
                    <Tooltip title={<div className="fixed-tooltip">System Config</div>} placement="right">
                        System Config
                    </Tooltip>
                )
            },
            {
                key: "/service/config_template",
                label: (
                    <Tooltip title={<div className="fixed-tooltip">Config Template</div>} placement="right">
                        Config Template
                    </Tooltip>
                )
            }
        ]
    },
    {
        label: (
            <Tooltip title={<div className="fixed-tooltip">Monitor</div>} placement="right">
                Monitor
            </Tooltip>
        ),
        key: "/monitor",
        icon: <Icon component={monitorSvg} />,
        children: [
            {
                key: "/monitor/alarm",
                label: (
                    <Tooltip title={<div className="fixed-tooltip">Alarm</div>} placement="right">
                        Alarm
                    </Tooltip>
                )
            },
            {
                key: "/monitor/network",
                label: (
                    <Tooltip title={<div className="fixed-tooltip">Network</div>} placement="right">
                        Network
                    </Tooltip>
                ),
                children: [
                    {
                        key: "/monitor/network/dlb",
                        label: (
                            <Tooltip
                                className="menu-dot"
                                title={<div className="fixed-tooltip">DLB</div>}
                                placement="right"
                            >
                                DLB
                            </Tooltip>
                        )
                    }
                ]
            }
        ]
    },
    {
        label: (
            <Tooltip title={<div className="fixed-tooltip">Maintain</div>} placement="right">
                Maintain
            </Tooltip>
        ),
        key: "/maintain",
        icon: <Icon component={maintainSvg} />,
        children: [
            {
                key: "/maintain/automation",
                label: (
                    <Tooltip title={<div className="fixed-tooltip">Automation</div>} placement="right">
                        Automation
                    </Tooltip>
                ),
                children: [
                    {
                        key: "/maintain/automation/playbooks",
                        label: (
                            <Tooltip
                                className="menu-dot"
                                title={<div className="fixed-tooltip">Playbooks</div>}
                                placement="right"
                            >
                                Playbooks
                            </Tooltip>
                        )
                    },
                    {
                        key: "/maintain/automation/other_devices",
                        label: (
                            <Tooltip
                                className="menu-dot"
                                title={<div className="fixed-tooltip">Other Devices</div>}
                                placement="right"
                            >
                                Other Devices
                            </Tooltip>
                        )
                    },
                    {
                        key: "/maintain/automation/ansible_jobs_list",
                        label: (
                            <Tooltip
                                className="menu-dot"
                                title={<div className="fixed-tooltip">Ansible Jobs List</div>}
                                placement="right"
                            >
                                Ansible Jobs List
                            </Tooltip>
                        )
                    },
                    {
                        key: "/maintain/automation/schedule",
                        label: (
                            <Tooltip
                                className="menu-dot"
                                title={<div className="fixed-tooltip">Schedule</div>}
                                placement="right"
                            >
                                Schedule
                            </Tooltip>
                        )
                    }
                ]
            },
            {
                key: "/maintain/system_backup",
                label: (
                    <Tooltip title={<div className="fixed-tooltip">System Backup</div>} placement="right">
                        System Backup
                    </Tooltip>
                )
            },
            {
                key: "/maintain/cli_configuration",
                label: (
                    <Tooltip title={<div className="fixed-tooltip">CLI Configuration</div>} placement="right">
                        CLI Configuration
                    </Tooltip>
                )
            }
        ]
    },
    {
        label: (
            <Tooltip title={<div className="fixed-tooltip">System</div>} placement="right">
                System
            </Tooltip>
        ),
        key: "/system",
        icon: <Icon component={settingSvg} />,
        children: [
            {
                key: "/system/user_management",
                label: (
                    <Tooltip title={<div className="fixed-tooltip">User Management</div>} placement="right">
                        User Management
                    </Tooltip>
                )
            },
            {
                key: "/system/software_license",
                label: (
                    <Tooltip title={<div className="fixed-tooltip">Software License</div>} placement="right">
                        Software License
                    </Tooltip>
                ),
                children: [
                    {
                        key: "/system/software_license/license_view",
                        label: (
                            <Tooltip
                                className="menu-dot"
                                title={<div className="fixed-tooltip">License View</div>}
                                placement="right"
                            >
                                License View
                            </Tooltip>
                        )
                    },
                    {
                        key: "/system/software_license/license_management",
                        label: (
                            <Tooltip
                                className="menu-dot"
                                title={<div className="fixed-tooltip">License Management</div>}
                                placement="right"
                            >
                                License Management
                            </Tooltip>
                        )
                    },
                    {
                        key: "/system/software_license/license_log",
                        label: (
                            <Tooltip
                                className="menu-dot"
                                title={<div className="fixed-tooltip">License Log</div>}
                                placement="right"
                            >
                                License Log
                            </Tooltip>
                        )
                    }
                ]
            }
        ]
    },
    {
        label: (
            <Tooltip title={<div className="fixed-tooltip">Topo</div>} placement="right">
                Topo
            </Tooltip>
        ),
        key: "/topo",
        icon: <Icon component={topologySvg} />,
        children: [
            {
                key: "/topo/topology",
                label: (
                    <Tooltip title={<div className="fixed-tooltip">Topology</div>} placement="right">
                        Topology
                    </Tooltip>
                )
            },
            {
                key: "/topo/unit",
                label: (
                    <Tooltip title={<div className="fixed-tooltip">Unit</div>} placement="right">
                        Unit
                    </Tooltip>
                )
            },
            {
                key: "/topo/rack",
                label: (
                    <Tooltip title={<div className="fixed-tooltip">Rack</div>} placement="right">
                        Rack
                    </Tooltip>
                )
            },
            {
                key: "/topo/fabric",
                label: (
                    <Tooltip title={<div className="fixed-tooltip">Fabric</div>} placement="right">
                        Fabric
                    </Tooltip>
                )
            }
        ]
    }
];

const campusItems = [
    {
        label: <>Dashboard</>,
        key: "/dashboard",
        icon: <Icon component={dashBoardSvg} />,
        children: [
            {
                key: "/dashboard/global_view",
                label: <>Global View</>
            },
            {
                key: "/dashboard/switch_view",
                label: <>Switch View</>
            },
            {
                key: "/dashboard/telemetry_dashboard",
                label: <>Telemetry Dashboard</>
            }
        ]
    },
    {
        label: <>Resource</>,
        key: "/resource",
        icon: <Icon component={resourceSvg} />,
        children: [
            {
                key: "/resource/upgrade_management",
                label: <>Upgrade Management</>
            },
            {
                key: "/resource/auth_management",
                label: <>Authority Management</>,
                children: [
                    {
                        key: "/resource/auth_management/device_license_management",
                        label: <>Device License Management</>
                    },
                    {
                        key: "/resource/auth_management/group_management",
                        label: <>Group Management</>
                    },
                    {
                        key: "/resource/auth_management/site_management",
                        label: <>Site Management</>
                    }
                ]
            }
        ]
    },
    {
        label: <>Service</>,
        key: "/service",
        icon: <Icon component={serviceSvg} />,
        children: [
            {
                key: "/service/switch",
                label: <>Switch</>
            },
            {
                key: "/service/global_configuration",
                label: <>Global Configuration</>
            },
            {
                key: "/service/switch_configuration",
                label: <>Switch Configuration</>
            },
            {
                key: "/service/config_files_view",
                label: <>Config Files View</>
            },
            {
                key: "/service/switch_model",
                label: <>Switch Model</>
            },
            {
                key: "/service/system_config",
                label: <>System Config</>
            },
            {
                key: "/service/config_template",
                label: <>Config Template</>
            }
        ]
    },
    {
        label: <>Monitor</>,
        key: "/monitor",
        icon: <Icon component={monitorSvg} />,
        children: [
            {
                key: "/monitor/alarm",
                label: <>Alarm</>
            }
        ]
    },
    {
        label: <>Maintain</>,
        key: "/maintain",
        icon: <Icon component={maintainSvg} />,
        children: [
            {
                key: "/maintain/automation",
                label: <>Automation</>,
                children: [
                    {
                        key: "/maintain/automation/playbooks",
                        label: <>Playbooks</>
                    },
                    {
                        key: "/maintain/automation/other_devices",
                        label: <>Other Devices</>
                    },
                    {
                        key: "/maintain/automation/ansible_jobs_list",
                        label: <>Ansible Jobs List</>
                    },
                    {
                        key: "/maintain/automation/schedule",
                        label: <>Schedule</>
                    }
                ]
            },
            {
                key: "/maintain/system_backup",
                label: <>System Backup</>
            },
            {
                key: "/maintain/cli_configuration",
                label: <>CLI Configuration</>
            }
        ]
    },
    {
        label: <>System</>,
        key: "/system",
        icon: <Icon component={settingSvg} />,
        children: [
            {
                key: "/system/user_management",
                label: <>User Management</>
            },
            {
                key: "/system/software_license",
                label: <>Software License</>,
                children: [
                    {
                        key: "/system/software_license/license_view",
                        label: <>License View</>
                    },
                    {
                        key: "/system/software_license/license_management",
                        label: <>License Management</>
                    },
                    {
                        key: "/system/software_license/license_log",
                        label: <>License Log</>
                    }
                ]
            }
        ]
    },
    {
        label: <>Topo</>,
        key: "/topo",
        icon: <Icon component={topologySvg} />,
        children: [
            {
                key: "/topo/topology",
                label: <>Topology</>
            },
            {
                key: "/topo/unit",
                label: <>Unit</>
            },
            {
                key: "/topo/rack",
                label: <>Rack</>
            },
            {
                key: "/topo/fabric",
                label: <>Fabric</>
            }
        ]
    }
];

const tItems = [
    {
        label: (
            <Tooltip title={<div className="fixed-tooltip">Dashboard</div>} placement="right">
                Dashboard
            </Tooltip>
        ),
        key: "/dashboard",
        icon: <Icon component={dashBoardSvg} />,
        children: [
            {
                key: "/dashboard/global_view",
                label: (
                    <Tooltip title={<div className="fixed-tooltip">Global View</div>} placement="right">
                        Global View
                    </Tooltip>
                )
            },
            {
                key: "/dashboard/switch_view",
                label: (
                    <Tooltip title={<div className="fixed-tooltip">Switch View</div>} placement="right">
                        Switch View
                    </Tooltip>
                )
            },
            {
                key: "/dashboard/telemetry_dashboard",
                label: (
                    <Tooltip title={<div className="fixed-tooltip">Telemetry Dashboard</div>} placement="right">
                        Telemetry Dashboard
                    </Tooltip>
                )
            }
        ]
    },
    {
        label: (
            <Tooltip title={<div className="fixed-tooltip">Resource</div>} placement="right">
                Resource
            </Tooltip>
        ),
        key: "/resource",
        icon: <Icon component={resourceSvg} />,
        children: [
            {
                key: "/resource/upgrade_management",
                label: (
                    <Tooltip title={<div className="fixed-tooltip">Upgrade Management</div>} placement="right">
                        Upgrade Management
                    </Tooltip>
                )
            },
            {
                key: "/resource/auth_management",
                label: (
                    <Tooltip title={<div className="fixed-tooltip">Authority Management</div>} placement="right">
                        Authority Management
                    </Tooltip>
                ),
                children: [
                    {
                        key: "/resource/auth_management/device_license_management",
                        label: (
                            <Tooltip
                                className="menu-dot"
                                title={<div className="fixed-tooltip">Device License Management</div>}
                                placement="right"
                            >
                                Device License Management
                            </Tooltip>
                        )
                    },
                    {
                        key: "/resource/auth_management/group_management",
                        label: (
                            <Tooltip
                                className="menu-dot"
                                title={<div className="fixed-tooltip">Group Management</div>}
                                placement="right"
                            >
                                Group Management
                            </Tooltip>
                        )
                    },
                    {
                        key: "/resource/auth_management/site_management",
                        label: (
                            <Tooltip
                                className="menu-dot"
                                title={<div className="fixed-tooltip">Site Management</div>}
                                placement="right"
                            >
                                Site Management
                            </Tooltip>
                        )
                    }
                ]
            }
        ]
    },
    {
        label: (
            <Tooltip title={<div className="fixed-tooltip">Service</div>} placement="right">
                Service
            </Tooltip>
        ),
        key: "/service",
        icon: <Icon component={serviceSvg} />,
        children: [
            {
                key: "/service/switch",
                label: (
                    <Tooltip title={<div className="fixed-tooltip">Switch</div>} placement="right">
                        Switch
                    </Tooltip>
                )
            },
            {
                key: "/service/global_configuration",
                label: (
                    <Tooltip title={<div className="fixed-tooltip">Global Configuration</div>} placement="right">
                        Global Configuration
                    </Tooltip>
                )
            },
            {
                key: "/service/switch_configuration",
                label: (
                    <Tooltip title={<div className="fixed-tooltip">Switch Configuration</div>} placement="right">
                        Switch Configuration
                    </Tooltip>
                )
            },
            {
                key: "/service/config_files_view",
                label: (
                    <Tooltip title={<div className="fixed-tooltip">Config Files View</div>} placement="right">
                        Config Files View
                    </Tooltip>
                )
            },
            {
                key: "/service/switch_model",
                label: (
                    <Tooltip title={<div className="fixed-tooltip">Switch Model</div>} placement="right">
                        Switch Model
                    </Tooltip>
                )
            },
            {
                key: "/service/system_config",
                label: (
                    <Tooltip title={<div className="fixed-tooltip">System Config</div>} placement="right">
                        System Config
                    </Tooltip>
                )
            },
            {
                key: "/service/config_template",
                label: (
                    <Tooltip title={<div className="fixed-tooltip">Config Template</div>} placement="right">
                        Config Template
                    </Tooltip>
                )
            }
        ]
    },
    {
        label: (
            <Tooltip title={<div className="fixed-tooltip">Monitor</div>} placement="right">
                Monitor
            </Tooltip>
        ),
        key: "/monitor",
        icon: <Icon component={monitorSvg} />,
        children: [
            {
                key: "/monitor/alarm",
                label: (
                    <Tooltip title={<div className="fixed-tooltip">Alarm</div>} placement="right">
                        Alarm
                    </Tooltip>
                )
            },
            {
                key: "/monitor/network",
                label: (
                    <Tooltip title={<div className="fixed-tooltip">Network</div>} placement="right">
                        Network
                    </Tooltip>
                ),
                children: [
                    {
                        key: "/monitor/network/dlb",
                        label: (
                            <Tooltip
                                className="menu-dot"
                                title={<div className="fixed-tooltip">DLB</div>}
                                placement="right"
                            >
                                DLB
                            </Tooltip>
                        )
                    }
                ]
            }
        ]
    },
    {
        label: (
            <Tooltip title={<div className="fixed-tooltip">Maintain</div>} placement="right">
                Maintain
            </Tooltip>
        ),
        key: "/maintain",
        icon: <Icon component={maintainSvg} />,
        children: [
            {
                key: "/maintain/automation",
                label: (
                    <Tooltip title={<div className="fixed-tooltip">Automation</div>} placement="right">
                        Automation
                    </Tooltip>
                ),
                children: [
                    {
                        key: "/maintain/automation/playbooks",
                        label: (
                            <Tooltip
                                className="menu-dot"
                                title={<div className="fixed-tooltip">Playbooks</div>}
                                placement="right"
                            >
                                Playbooks
                            </Tooltip>
                        )
                    },
                    {
                        key: "/maintain/automation/other_devices",
                        label: (
                            <Tooltip
                                className="menu-dot"
                                title={<div className="fixed-tooltip">Other Devices</div>}
                                placement="right"
                            >
                                Other Devices
                            </Tooltip>
                        )
                    },
                    {
                        key: "/maintain/automation/ansible_jobs_list",
                        label: (
                            <Tooltip
                                className="menu-dot"
                                title={<div className="fixed-tooltip">Ansible Jobs List</div>}
                                placement="right"
                            >
                                Ansible Jobs List
                            </Tooltip>
                        )
                    },
                    {
                        key: "/maintain/automation/schedule",
                        label: (
                            <Tooltip
                                className="menu-dot"
                                title={<div className="fixed-tooltip">Schedule</div>}
                                placement="right"
                            >
                                Schedule
                            </Tooltip>
                        )
                    }
                ]
            },
            {
                key: "/maintain/system_backup",
                label: (
                    <Tooltip title={<div className="fixed-tooltip">System Backup</div>} placement="right">
                        System Backup
                    </Tooltip>
                )
            },
            {
                key: "/maintain/cli_configuration",
                label: (
                    <Tooltip title={<div className="fixed-tooltip">CLI Configuration</div>} placement="right">
                        CLI Configuration
                    </Tooltip>
                )
            }
        ]
    },
    {
        label: (
            <Tooltip title={<div className="fixed-tooltip">System</div>} placement="right">
                System
            </Tooltip>
        ),
        key: "/system",
        icon: <Icon component={settingSvg} />,
        children: [
            {
                key: "/system/user_management",
                label: (
                    <Tooltip title={<div className="fixed-tooltip">User Management</div>} placement="right">
                        User Management
                    </Tooltip>
                )
            },
            {
                key: "/system/software_license",
                label: (
                    <Tooltip title={<div className="fixed-tooltip">Software License</div>} placement="right">
                        Software License
                    </Tooltip>
                ),
                children: [
                    {
                        key: "/system/software_license/license_view",
                        label: (
                            <Tooltip
                                className="menu-dot"
                                title={<div className="fixed-tooltip">License View</div>}
                                placement="right"
                            >
                                License View
                            </Tooltip>
                        )
                    },
                    {
                        key: "/system/software_license/license_management",
                        label: (
                            <Tooltip
                                className="menu-dot"
                                title={<div className="fixed-tooltip">License Management</div>}
                                placement="right"
                            >
                                License Management
                            </Tooltip>
                        )
                    },
                    {
                        key: "/system/software_license/license_log",
                        label: (
                            <Tooltip
                                className="menu-dot"
                                title={<div className="fixed-tooltip">License Log</div>}
                                placement="right"
                            >
                                License Log
                            </Tooltip>
                        )
                    }
                ]
            }
        ]
    },
    {
        label: (
            <Tooltip title={<div className="fixed-tooltip">System</div>} placement="right">
                Topo
            </Tooltip>
        ),
        key: "/topo",
        icon: <Icon component={topologySvg} />,
        children: [
            {
                key: "/topo/topology",
                label: (
                    <Tooltip title={<div className="fixed-tooltip">Topology</div>} placement="right">
                        Topology
                    </Tooltip>
                )
            },
            {
                key: "/topo/unit",
                label: (
                    <Tooltip title={<div className="fixed-tooltip">Unit</div>} placement="right">
                        Unit
                    </Tooltip>
                )
            },
            {
                key: "/topo/rack",
                label: (
                    <Tooltip title={<div className="fixed-tooltip">Rack</div>} placement="right">
                        Rack
                    </Tooltip>
                )
            },
            {
                key: "/topo/fabric",
                label: (
                    <Tooltip title={<div className="fixed-tooltip">Fabric</div>} placement="right">
                        Fabric
                    </Tooltip>
                )
            }
        ]
    }
];

const superItems = [
    {
        label: (
            <Tooltip title={<div className="fixed-tooltip">Dashboard</div>} placement="right">
                Dashboard
            </Tooltip>
        ),
        key: "/dashboard",
        icon: <Icon component={dashBoardSvg} />,
        children: [
            {
                key: "/dashboard/global_view",
                label: (
                    <Tooltip title={<div className="fixed-tooltip">Global View</div>} placement="right">
                        Global View
                    </Tooltip>
                )
            },
            {
                key: "/dashboard/switch_view",
                label: (
                    <Tooltip title={<div className="fixed-tooltip">Switch View</div>} placement="right">
                        Switch View
                    </Tooltip>
                )
            },
            {
                key: "/dashboard/telemetry_dashboard",
                label: (
                    <Tooltip title={<div className="fixed-tooltip">Telemetry Dashboard</div>} placement="right">
                        Telemetry Dashboard
                    </Tooltip>
                )
            }
        ]
    },
    {
        label: (
            <Tooltip title={<div className="fixed-tooltip">Resource</div>} placement="right">
                Resource
            </Tooltip>
        ),
        key: "/resource",
        icon: <Icon component={resourceSvg} />,
        children: [
            {
                key: "/resource/upgrade_management",
                label: (
                    <Tooltip title={<div className="fixed-tooltip">Upgrade Management</div>} placement="right">
                        Upgrade Management
                    </Tooltip>
                )
            },
            {
                key: "/resource/auth_management",
                label: (
                    <Tooltip title={<div className="fixed-tooltip">Authority Management</div>} placement="right">
                        Authority Management
                    </Tooltip>
                ),
                children: [
                    {
                        key: "/resource/auth_management/device_license_management",
                        label: (
                            <Tooltip
                                className="menu-dot"
                                title={<div className="fixed-tooltip">Device License Management</div>}
                                placement="right"
                            >
                                Device License Management
                            </Tooltip>
                        )
                    },
                    {
                        key: "/resource/auth_management/group_management",
                        label: (
                            <Tooltip
                                className="menu-dot"
                                title={<div className="fixed-tooltip">Group Management</div>}
                                placement="right"
                            >
                                Group Management
                            </Tooltip>
                        )
                    },
                    {
                        key: "/resource/auth_management/site_management",
                        label: (
                            <Tooltip
                                className="menu-dot"
                                title={<div className="fixed-tooltip">Site Management</div>}
                                placement="right"
                            >
                                Site Management
                            </Tooltip>
                        )
                    }
                ]
            }
        ]
    },
    {
        label: (
            <Tooltip title={<div className="fixed-tooltip">Service</div>} placement="right">
                Service
            </Tooltip>
        ),
        key: "/service",
        icon: <Icon component={serviceSvg} />,
        children: [
            {
                key: "/service/switch",
                label: (
                    <Tooltip title={<div className="fixed-tooltip">Switch</div>} placement="right">
                        Switch
                    </Tooltip>
                )
            },
            {
                key: "/service/global_configuration",
                label: (
                    <Tooltip title={<div className="fixed-tooltip">Global Configuration</div>} placement="right">
                        Global Configuration
                    </Tooltip>
                )
            },
            {
                key: "/service/switch_configuration",
                label: (
                    <Tooltip title={<div className="fixed-tooltip">Switch Configuration</div>} placement="right">
                        Switch Configuration
                    </Tooltip>
                )
            },
            {
                key: "/service/config_files_view",
                label: (
                    <Tooltip title={<div className="fixed-tooltip">Config Files View</div>} placement="right">
                        Config Files View
                    </Tooltip>
                )
            },
            {
                key: "/service/switch_model",
                label: (
                    <Tooltip title={<div className="fixed-tooltip">Switch Model</div>} placement="right">
                        Switch Model
                    </Tooltip>
                )
            },
            {
                key: "/service/system_config",
                label: (
                    <Tooltip title={<div className="fixed-tooltip">System Config</div>} placement="right">
                        System Config
                    </Tooltip>
                )
            },
            {
                key: "/service/config_template",
                label: (
                    <Tooltip title={<div className="fixed-tooltip">Config Template</div>} placement="right">
                        Config Template
                    </Tooltip>
                )
            }
        ]
    },
    {
        label: (
            <Tooltip title={<div className="fixed-tooltip">Monitor</div>} placement="right">
                Monitor
            </Tooltip>
        ),
        key: "/monitor",
        icon: <Icon component={monitorSvg} />,
        children: [
            {
                key: "/monitor/alarm",
                label: (
                    <Tooltip title={<div className="fixed-tooltip">Alarm</div>} placement="right">
                        Alarm
                    </Tooltip>
                )
            }
        ]
    },
    {
        label: (
            <Tooltip title={<div className="fixed-tooltip">Maintain</div>} placement="right">
                Maintain
            </Tooltip>
        ),
        key: "/maintain",
        icon: <Icon component={maintainSvg} />,
        children: [
            {
                key: "/maintain/automation",
                label: (
                    <Tooltip title={<div className="fixed-tooltip">Automation</div>} placement="right">
                        Automation
                    </Tooltip>
                ),
                children: [
                    {
                        key: "/maintain/automation/playbooks",
                        label: (
                            <Tooltip
                                className="menu-dot"
                                title={<div className="fixed-tooltip">Playbooks</div>}
                                placement="right"
                            >
                                Playbooks
                            </Tooltip>
                        )
                    },
                    {
                        key: "/maintain/automation/other_devices",
                        label: (
                            <Tooltip
                                className="menu-dot"
                                title={<div className="fixed-tooltip">Other Devices</div>}
                                placement="right"
                            >
                                Other Devices
                            </Tooltip>
                        )
                    },
                    {
                        key: "/maintain/automation/ansible_jobs_list",
                        label: (
                            <Tooltip
                                className="menu-dot"
                                title={<div className="fixed-tooltip">Ansible Jobs List</div>}
                                placement="right"
                            >
                                Ansible Jobs List
                            </Tooltip>
                        )
                    },
                    {
                        key: "/maintain/automation/schedule",
                        label: (
                            <Tooltip
                                className="menu-dot"
                                title={<div className="fixed-tooltip">Schedule</div>}
                                placement="right"
                            >
                                Schedule
                            </Tooltip>
                        )
                    }
                ]
            },
            {
                key: "/maintain/system_backup",
                label: (
                    <Tooltip title={<div className="fixed-tooltip">System Backup</div>} placement="right">
                        System Backup
                    </Tooltip>
                )
            },
            {
                key: "/maintain/cli_configuration",
                label: (
                    <Tooltip title={<div className="fixed-tooltip">CLI Configuration</div>} placement="right">
                        CLI Configuration
                    </Tooltip>
                )
            }
        ]
    },
    {
        label: (
            <Tooltip title={<div className="fixed-tooltip">System</div>} placement="right">
                System
            </Tooltip>
        ),
        key: "/system",
        icon: <Icon component={settingSvg} />,
        children: [
            {
                key: "/system/user_management",
                label: (
                    <Tooltip title={<div className="fixed-tooltip">User Management</div>} placement="right">
                        User Management
                    </Tooltip>
                )
            },
            {
                key: "/system/software_license",
                label: (
                    <Tooltip title={<div className="fixed-tooltip">Software License</div>} placement="right">
                        Software License
                    </Tooltip>
                ),
                children: [
                    {
                        key: "/system/software_license/license_view",
                        label: (
                            <Tooltip
                                className="menu-dot"
                                title={<div className="fixed-tooltip">License View</div>}
                                placement="right"
                            >
                                License View
                            </Tooltip>
                        )
                    },
                    {
                        key: "/system/software_license/license_management",
                        label: (
                            <Tooltip
                                className="menu-dot"
                                title={<div className="fixed-tooltip">License Management</div>}
                                placement="right"
                            >
                                License Management
                            </Tooltip>
                        )
                    },
                    {
                        key: "/system/software_license/license_log",
                        label: (
                            <Tooltip
                                className="menu-dot"
                                title={<div className="fixed-tooltip">License Log</div>}
                                placement="right"
                            >
                                License Log
                            </Tooltip>
                        )
                    }
                ]
            }
        ]
    },
    {
        label: (
            <Tooltip title={<div className="fixed-tooltip">System</div>} placement="right">
                Topo
            </Tooltip>
        ),
        key: "/topo",
        icon: <Icon component={topologySvg} />,
        children: [
            {
                key: "/topo/topology",
                label: (
                    <Tooltip title={<div className="fixed-tooltip">Topology</div>} placement="right">
                        Topology
                    </Tooltip>
                )
            },
            {
                key: "/topo/unit",
                label: (
                    <Tooltip title={<div className="fixed-tooltip">Unit</div>} placement="right">
                        Unit
                    </Tooltip>
                )
            },
            {
                key: "/topo/rack",
                label: (
                    <Tooltip title={<div className="fixed-tooltip">Rack</div>} placement="right">
                        Rack
                    </Tooltip>
                )
            },
            {
                key: "/topo/fabric",
                label: (
                    <Tooltip title={<div className="fixed-tooltip">Fabric</div>} placement="right">
                        Fabric
                    </Tooltip>
                )
            }
        ]
    }
];

const getSidebarItems = () => {
    switch (import.meta.env.VITE_APP_EXPORT_MODULE) {
        case "AmpCon-DC":
            return dcItems;
        case "AmpCon-CAMPUS":
            return campusItems;
        case "AmpCon-T":
            return tItems;
        case "AmpCon-SUPER":
            return superItems;
        default:
            return getSidebarItems();
    }
};

const all_items = getSidebarItems();

const excludeItemsByKey = (items, excludeKeys) => {
    const excludeRecursive = list => {
        return list.reduce((acc, item) => {
            if (item && !excludeKeys.includes(item.key)) {
                if (item.children) {
                    const filteredChildren = excludeRecursive(item.children);
                    acc.push({...item, children: filteredChildren});
                } else {
                    acc.push(item);
                }
            }
            return acc;
        }, []);
    };

    return excludeRecursive(items);
};

const adminExcludeKeys = [
    "/resource/auth_management/group_management",
    "/service/system_config",
    "/system/user_management",
    "/service/system_config",
    "/service/switch_model"
];
const operatorExcludeKeys = [
    "/resource/auth_management/group_management",
    "/service/system_config",
    "/system",
    "/service/system_config",
    "/service/switch_model"
];
const readonlyExcludeKeys = [
    "/resource",
    "/maintain/automation",
    "/maintain/system_backup",
    "/service/system_config",
    "/service/global_configuration",
    "/service/switch_configuration",
    "/service/switch_model",
    "/service/system_config",
    "/system"
];

const sidebar_items = {
    superuser: all_items,
    superadmin: excludeItemsByKey(all_items, adminExcludeKeys),
    admin: excludeItemsByKey(all_items, operatorExcludeKeys),
    readonly: excludeItemsByKey(all_items, readonlyExcludeKeys)
};

export default sidebar_items;
