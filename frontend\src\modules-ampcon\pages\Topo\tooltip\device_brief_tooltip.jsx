import {forwardRef, useEffect, useImperativeHandle, useState} from "react";
import {Flex} from "antd";

const DeviceInfoBody = ({deviceInfoKeys, deviceInfoValues, getStatusIcon}) => (
    <Flex
        style={{
            margin: "16px",
            textAlign: "Left",
            fontFamily: "Lato, Lato",
            fontSize: "14px",
            fontStyle: "normal",
            textTransform: "none"
        }}
    >
        <Flex vertical>
            {deviceInfoKeys.map((key, index) => (
                <div
                    style={{
                        height: "17px",
                        color: "#929A9E",
                        fontWeight: 400,
                        marginBottom: index === deviceInfoKeys.length - 1 ? "4px" : "12px"
                    }}
                >
                    {key}
                </div>
            ))}
        </Flex>
        <div style={{width: "16px"}} />
        <Flex vertical>
            {deviceInfoValues.map((value, index) => (
                <div
                    style={{
                        height: "17px",
                        color: "#212519",
                        fontWeight: "bold",
                        marginBottom: index === deviceInfoValues.length - 1 ? "4px" : "12px"
                    }}
                >
                    {index === 4 && getStatusIcon(value)}
                    {value}
                </div>
            ))}
        </Flex>
        <div style={{width: "2px"}} />
    </Flex>
);

const DeviceBriefTooltip = forwardRef((props, ref) => {
    const baseStyle = {
        backgroundColor: "#FFFFFF",
        boxShadow: "0px 1px 12px 1px #E6E8EA",
        borderRadius: "4px",
        position: "absolute",
        display: "block",
        color: "white",
        pointerEvents: "none",
        zIndex: 1000,
        transform: "none",
        whiteSpace: "pre"
    };

    const [isTooltipVisible, setTooltipVisible] = useState(false);
    const [, setDeviceInfo] = useState(null);
    const [deviceInfoKeys, setDeviceInfoKeys] = useState([]);
    const [deviceInfoValues, setDeviceInfoValues] = useState([]);
    const [deviceStyle, setDeviceStyle] = useState(baseStyle);

    useEffect(() => {
        const handleMouseMove = event => {
            calculateDeviceBriefTooltipStyle(event.clientX, event.clientY);
        };

        window.addEventListener("mousemove", handleMouseMove);

        return () => {
            window.removeEventListener("mousemove", handleMouseMove);
        };
    }, []);

    useImperativeHandle(ref, () => ({
        showDeviceBriefTooltip: deviceInfo => {
            setDeviceInfo(deviceInfo);
            setDeviceInfoKeys(Object.keys(deviceInfo));
            setDeviceInfoValues(Object.values(deviceInfo));
            setTooltipVisible(true);
        },
        hideDeviceBriefTooltip: () => {
            setTooltipVisible(false);
        }
    }));

    const calculateDeviceBriefTooltipStyle = (x, y) => {
        const baseStyleTemp = {...baseStyle};
        const deviceBriefTooltipHidden = document.getElementsByClassName("device_brief_tooltip_hidden")[0];
        if (deviceBriefTooltipHidden) {
            const rectHidden = deviceBriefTooltipHidden.getBoundingClientRect();
            if (x + 30 + rectHidden.width > window.innerWidth) {
                baseStyleTemp.right = "30px";
                delete baseStyleTemp.left;
            } else {
                baseStyleTemp.left = `${x + 10}px`;
                delete baseStyleTemp.right;
            }
            if (y + 30 + rectHidden.height > window.innerHeight) {
                baseStyleTemp.bottom = "30px";
                delete baseStyleTemp.top;
            } else {
                baseStyleTemp.top = `${y + 10}px`;
                delete baseStyleTemp.bottom;
            }
        }
        setDeviceStyle(baseStyleTemp);
    };

    const getStatusIcon = value => {
        value = value?.toString();
        return (
            <svg
                style={{
                    width: "8px",
                    height: "8px",
                    borderRadius: "50%",
                    backgroundColor: value?.toLowerCase() === "online" ? "#14C9BB" : "#F53F3F",
                    marginRight: "4px"
                }}
            />
        );
    };

    return (
        <>
            {isTooltipVisible ? (
                <div ref={ref} className="device_brief_tooltip" style={deviceStyle}>
                    <DeviceInfoBody
                        deviceInfoKeys={deviceInfoKeys}
                        deviceInfoValues={deviceInfoValues}
                        getStatusIcon={getStatusIcon}
                    />
                </div>
            ) : null}
            <div
                ref={ref}
                className="device_brief_tooltip_hidden"
                style={{
                    backgroundColor: "#FFFFFF",
                    boxShadow: "0px 1px 12px 1px #E6E8EA",
                    borderRadius: "4px",
                    position: "absolute",
                    right: window.innerWidth / 2,
                    bottom: window.innerHeight / 2,
                    display: "block",
                    color: "white",
                    pointerEvents: "none",
                    zIndex: -1,
                    transform: "none",
                    whiteSpace: "pre"
                }}
            >
                <DeviceInfoBody
                    deviceInfoKeys={deviceInfoKeys}
                    deviceInfoValues={deviceInfoValues}
                    getStatusIcon={getStatusIcon}
                />
            </div>
        </>
    );
});

export default DeviceBriefTooltip;
