import {But<PERSON>, <PERSON><PERSON><PERSON>, Flex, Input, Modal} from "antd";
import {forwardRef, useImperativeHandle, useState} from "react";
import {ReloadOutlined} from "@ant-design/icons";
import {queryLog} from "@/modules-ampcon/apis/dashboard_api";

const LogViewTextareaModal = forwardRef((props, ref) => {
    useImperativeHandle(ref, () => ({
        showLogViewTextareaModal: sn => {
            queryLog(sn).then(response => {
                setLogContent(response);
            });
            setSelectedSwitchSN(sn);
            setIsShowModal(true);
        },
        hideLogViewTextareaModal: () => {
            resetModal();
        }
    }));

    const readonlyStyle = {
        minHeight: "330px",
        height: "58vh",
        resize: "vertical",
        border: "1px solid rgb(217, 217, 217)",
        fontSize: "16px",
        borderRadius: "4px",
        boxShadow: "none",
        maxHeight: "calc(100vh - 500px)"
    };

    const [isShowModal, setIsShowModal] = useState(false);
    const [selectedSwitchSN, setSelectedSwitchSN] = useState("");
    const [logContent, setLogContent] = useState("");

    const resetModal = () => {
        setIsShowModal(false);
        setSelectedSwitchSN("");
        setLogContent("");
    };

    return (
        <Modal
            className="ampcon-middle-modal"
            title={
                <>
                    <div style={{display: "flex", justifyContent: "space-between", alignItems: "center"}}>
                        {`${selectedSwitchSN} logs`}
                        <Button
                            type="text"
                            className="ant-modal-close"
                            style={{marginRight: "30px"}}
                            icon={<ReloadOutlined className="anticon anticon-close ant-modal-close-icon" />}
                            onClick={() => {
                                queryLog(selectedSwitchSN).then(response => {
                                    setLogContent(response);
                                });
                            }}
                        />
                    </div>
                    <Divider style={{marginTop: 8, marginBottom: 0}} />
                </>
            }
            open={isShowModal}
            onCancel={() => {
                resetModal();
            }}
            footer={null}
        >
            <Flex vertical style={{flex: 1}}>
                <Input.TextArea style={readonlyStyle} value={logContent} rows={19} readOnly />
            </Flex>
        </Modal>
    );
});

export default LogViewTextareaModal;
