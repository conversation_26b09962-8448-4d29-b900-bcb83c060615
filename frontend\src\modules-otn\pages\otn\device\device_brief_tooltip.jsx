import {forwardRef, useEffect, useImperativeHandle, useState, useRef} from "react";
import {useDispatch, useSelector} from "react-redux";
import {Flex, message} from "antd";
import {confirmModalAction} from "@/modules-ampcon/components/custom_modal";
import {getTopo} from "@/modules-ampcon/apis/fmt";
import {
    setShowAutoDiscoverLinksByNode,
    setAutoDiscoveredConnectionsByIp,
    setShowAutoDiscoverLinksByIp
} from "@/store/modules/otn/mapSlice";

const DeviceInfoBody = ({deviceInfoKeys, deviceInfoValues, getStatusIcon, title}) => (
    <div style={{weight: "290px", height: "234px"}}>
        <Flex
            style={{
                fontWeight: "bold",
                color: "#212519",
                height: "40px",
                alignItems: "center",
                backgroundColor: "#F8FAFB ",
                padding: "5px 10px",
                fontFamily: "Lato-Bold, Lato"
            }}
        >
            {title}
        </Flex>
        <Flex
            style={{
                margin: "16px",
                textAlign: "Left",
                fontFamily: "Lato, Lato",
                fontSize: "14px",
                fontStyle: "normal",
                textTransform: "none"
            }}
        >
            <Flex vertical>
                {deviceInfoKeys.map((key, index) => (
                    <div
                        style={{
                            height: "17px",
                            color: "#929A9E",
                            fontWeight: 400,
                            marginBottom: index === deviceInfoKeys.length - 1 ? "4px" : "12px"
                        }}
                    >
                        {key}
                    </div>
                ))}
            </Flex>
            <div style={{width: "16px"}} />
            <Flex vertical>
                {deviceInfoValues.map((value, index) => (
                    <div
                        style={{
                            height: "17px",
                            color: "#212519",
                            marginBottom: index === deviceInfoValues.length - 1 ? "4px" : "12px"
                        }}
                    >
                        {index === 5 && getStatusIcon(value)}
                        {value}
                    </div>
                ))}
            </Flex>
            <div style={{width: "2px"}} />
        </Flex>
    </div>
);

export const DeviceBriefTooltip = forwardRef((props, ref) => {
    const baseStyle = {
        backgroundColor: "#FFFFFF",
        boxShadow: "0px 1px 12px 1px #E6E8EA",
        borderRadius: "4px",
        position: "absolute",
        display: "block",
        color: "white",
        pointerEvents: "none",
        zIndex: 1000,
        transform: "none",
        whiteSpace: "pre"
    };

    const [isTooltipVisible, setTooltipVisible] = useState(false);
    const [deviceInfo, setDeviceInfo] = useState(null);
    const [deviceInfoKeys, setDeviceInfoKeys] = useState([]);
    const [deviceInfoValues, setDeviceInfoValues] = useState([]);
    const [deviceStyle, setDeviceStyle] = useState(baseStyle);
    const deviceInfoRef = useRef(null);
    useEffect(() => {
        deviceInfoRef.current = deviceInfo;
    }, [deviceInfo]);
    useEffect(() => {
        const handleMouseMove = event => {
            calculateDeviceBriefTooltipStyle(event.clientX, event.clientY);
        };

        window.addEventListener("mousemove", handleMouseMove);

        return () => {
            window.removeEventListener("mousemove", handleMouseMove);
        };
    }, []);

    useImperativeHandle(ref, () => ({
        showDeviceBriefTooltip: deviceInfo => {
            const keys = Object.keys(deviceInfo);
            const values = Object.values(deviceInfo);
            setDeviceInfo(deviceInfo);
            setDeviceInfoKeys(keys);
            setDeviceInfoValues(values);
            setTooltipVisible(true);
        },
        hideDeviceBriefTooltip: () => {
            setTooltipVisible(false);
        }
    }));

    const calculateDeviceBriefTooltipStyle = (x, y) => {
        const adjustedX = x - 610;
        const adjustedY = y - 150;
        const baseStyleTemp = {...baseStyle};
        const deviceBriefTooltipHidden = document.getElementsByClassName("device_brief_tooltip_hidden")[0];
        if (deviceBriefTooltipHidden) {
            const rectHidden = deviceBriefTooltipHidden.getBoundingClientRect();
            if (adjustedX + 550 + rectHidden.width > window.innerWidth) {
                baseStyleTemp.right = "30px";
                delete baseStyleTemp.left;
            } else {
                baseStyleTemp.left = `${adjustedX + 50}px`;
                delete baseStyleTemp.right;
            }
            if (adjustedY + 200 + rectHidden.height > window.innerHeight) {
                baseStyleTemp.bottom = "30px";
                delete baseStyleTemp.top;
            } else {
                baseStyleTemp.top = `${adjustedY + 10}px`;
                delete baseStyleTemp.bottom;
            }
        }
        setDeviceStyle(baseStyleTemp);
    };

    const getStatusIcon = value => {
        value = value?.toString();
        return (
            <svg
                style={{
                    width: "8px",
                    height: "8px",
                    borderRadius: "50%",
                    backgroundColor: value?.toLowerCase() === "online" ? "#14C9BB" : "#808080",
                    marginRight: "4px"
                }}
            />
        );
    };

    return (
        <>
            {isTooltipVisible ? (
                <div ref={ref} className="device_brief_tooltip" style={deviceStyle}>
                    <DeviceInfoBody
                        deviceInfoKeys={deviceInfoKeys}
                        deviceInfoValues={deviceInfoValues}
                        getStatusIcon={getStatusIcon}
                        title={props.title}
                    />
                </div>
            ) : null}
            <div
                ref={ref}
                className="device_brief_tooltip_hidden"
                style={{
                    backgroundColor: "#FFFFFF",
                    boxShadow: "0px 1px 12px 1px #E6E8EA",
                    borderRadius: "4px",
                    position: "absolute",
                    right: window.innerWidth / 2,
                    bottom: window.innerHeight / 2,
                    display: "block",
                    color: "white",
                    pointerEvents: "none",
                    zIndex: -1,
                    transform: "none",
                    whiteSpace: "pre"
                }}
            >
                <DeviceInfoBody
                    deviceInfoKeys={deviceInfoKeys}
                    deviceInfoValues={deviceInfoValues}
                    getStatusIcon={getStatusIcon}
                    title={props.title}
                />
            </div>
        </>
    );
});

export const ContextMenu = ({visible, position, onClose, scene, ip, setVirtualNodeInfo}) => {
    const {showAutoDiscoverLinksByIp, autoDiscoveredConnectionsByIp} = useSelector(state => state.map);
    const showAutoDiscoverLinksByNode = useSelector(state => state.map.showAutoDiscoverLinksByNode);
    const [menuPosition, setMenuPosition] = useState(position);
    const currentNodeAutoDiscover = showAutoDiscoverLinksByNode[ip] || false;
    const [hoveredItem, setHoveredItem] = useState(currentNodeAutoDiscover);
    const dispatch = useDispatch();
    const menuRef = useRef(null);
    const currentIpVisible = !!showAutoDiscoverLinksByIp[ip];
    const hasAutoDiscovered = !!autoDiscoveredConnectionsByIp[ip]?.length;

    const handleAutoDiscover = async () => {
        confirmModalAction(
            "Are you sure you want to Auto-discover? It will replace all links on the current topology.",
            async () => {
                try {
                    const res = await getTopo(ip);
                    if (res.connectionInfo) {
                        dispatch(setAutoDiscoveredConnectionsByIp({ip, connections: res.connectionInfo}));
                        dispatch(setVirtualNodeInfo(res.lldp_info));
                        dispatch(setShowAutoDiscoverLinksByIp({ip, visible: true}));
                        message.success("Auto-discover completed successfully");
                    } else {
                        message.error(`Auto-discover failed: ${res.apiMessage}`);
                    }
                } catch (error) {
                    message.error("Auto-discover request failed");
                }
            }
        );
    };

    // useEffect(() => {
    //     setHasAutoDiscovered(currentNodeAutoDiscover);
    // }, [ip, showAutoDiscoverLinksByNode]);

    const handleRefresh = async () => {
        if (!hasAutoDiscovered) return;
        try {
            const res = await getTopo(ip);
            if (res.connectionInfo) {
                dispatch(setAutoDiscoveredConnectionsByIp({ip, connections: res.connectionInfo}));
                dispatch(setVirtualNodeInfo(res.lldp_info));
                dispatch(setShowAutoDiscoverLinksByIp({ip, visible: true}));
                message.success("Refresh successfully");
            } else {
                message.error(`Refresh failed: ${res.apiMessage}`);
            }
        } catch (error) {
            message.error("Refresh request failed");
        }
    };
    useEffect(() => {
        if (!scene || !visible) return;
        const updateMenuPosition = () => {
            const containerPoint = scene.lngLatToContainer([position.lng, position.lat]);
            setMenuPosition({
                x: containerPoint.x + 10,
                y: containerPoint.y + 20
            });
        };
        updateMenuPosition();
        const handleMapMove = () => {
            updateMenuPosition();
        };
        scene.on("move", handleMapMove);
        return () => {
            scene.off("move", handleMapMove);
        };
    }, [scene, visible, position, showAutoDiscoverLinksByIp]);

    const toggleLinkVisibility = () => {
        dispatch(setShowAutoDiscoverLinksByIp({ip, visible: !currentIpVisible}));
    };

    useEffect(() => {
        if (!visible || !menuRef.current) return;
        const menuWidth = menuRef.current.offsetWidth;
        const menuHeight = menuRef.current.offsetHeight;
        const mapContainer = scene.getContainer();
        const mapRect = mapContainer.getBoundingClientRect();
        if (menuPosition.x + menuWidth > mapRect.width) {
            setMenuPosition(prev => ({
                ...prev,
                x: mapRect.width - menuWidth
            }));
        }
        if (menuPosition.y + menuHeight > mapRect.height) {
            setMenuPosition(prev => ({
                ...prev,
                y: mapRect.height - menuHeight
            }));
        }
    }, [visible, menuPosition, scene]);

    if (!visible) return null;

    return (
        <div
            ref={menuRef}
            style={{
                position: "absolute",
                minWidth: "220px",
                minHeight: "32px",
                left: menuPosition.x,
                top: menuPosition.y,
                background: "white",
                boxShadow: "0px 0px 5px rgba(0,0,0,0.2)",
                borderRadius: "5px",
                padding: "10px",
                zIndex: 1000
            }}
            onClick={() => {
                onClose();
                setHoveredItem(null);
            }}
        >
            <div
                style={{
                    padding: "5px",
                    cursor: "pointer",
                    backgroundColor: hoveredItem === "autoDiscover" ? "#f0f0f0" : "transparent"
                }}
                onMouseEnter={() => setHoveredItem("autoDiscover")}
                onMouseLeave={() => setHoveredItem(null)}
                onClick={handleAutoDiscover}
            >
                Auto-discover
            </div>
            <div
                style={{
                    padding: "5px",
                    cursor: hasAutoDiscovered ? "pointer" : "not-allowed",
                    backgroundColor: hoveredItem === "refresh" ? "#f0f0f0" : "transparent",
                    opacity: hasAutoDiscovered ? 1 : 0.5
                }}
                onMouseEnter={() => hasAutoDiscovered && setHoveredItem("refresh")}
                onMouseLeave={() => setHoveredItem(null)}
                onClick={hasAutoDiscovered ? handleRefresh : undefined}
            >
                Refresh
            </div>
            <div
                style={{
                    padding: "5px",
                    cursor: hasAutoDiscovered ? "pointer" : "not-allowed",
                    backgroundColor: hoveredItem === "hideShow" ? "#f0f0f0" : "transparent",
                    opacity: hasAutoDiscovered ? 1 : 0.5
                }}
                onMouseEnter={() => hasAutoDiscovered && setHoveredItem("hideShow")}
                onMouseLeave={() => setHoveredItem(null)}
                onClick={hasAutoDiscovered ? toggleLinkVisibility : undefined}
            >
                {currentIpVisible ? "Hide Auto-discover Link" : "Show Auto-discover Link"}
            </div>
        </div>
    );
};
