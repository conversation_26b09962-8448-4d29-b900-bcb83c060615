import React, {memo, useRef, useEffect, useState, useMemo} from "react";
import {Flex, Empty} from "antd";
import EmptyPic from "@/assets/images/App/empty.png";
import * as echarts from "echarts";

export const PieEcharts = memo(
    ({
        name,
        fetchDataApi,
        refreshInterval = 600000,
        chartType = "pie",
        colorList = [],
        height = "28vh",
        maxWidth = "450px",
        showPercent = true
    }) => {
        const [seriesData, setSeriesData] = useState([]);

        const fetchData = async () => {
            await fetchDataApi().then(res => {
                if (res.status === 200) {
                    setSeriesData(
                        res.data.map(item => ({
                            ...item,
                            name: item.name
                        }))
                    );
                }
            });
        };

        useEffect(() => {
            fetchData();
            const intervalId = setInterval(fetchData, refreshInterval);
            return () => {
                clearInterval(intervalId);
            };
        }, [name, fetchDataApi]);

        return (
            <BasePieEcharts
                seriesData={seriesData}
                name={name}
                chartType={chartType}
                colorList={colorList}
                height={height}
                maxWidth={maxWidth}
                showPercent={showPercent}
            />
        );
    }
);

export const BasePieEcharts = ({
    seriesData,
    name,
    chartType = "pie",
    colorList = [],
    height = "28vh",
    maxWidth = "450px",
    onClicked = null,
    showPercent = true
}) => {
    let chartRadius;
    switch (chartType) {
        case "pie":
            chartRadius = ["0%", "70%"];
            break;
        case "ring":
            chartRadius = ["45%", "65%"];
            break;
        default:
            chartRadius = ["0%", "70%"];
    }

    const total = seriesData.reduce((sum, item) => sum + item.value, 0);
    const chartRef = useRef();
    useEffect(() => {
        const myChart = echarts.init(chartRef.current);
        const option = {
            tooltip: {
                trigger: "item",
                backgroundColor: "rgba(0, 0, 0, 0.6)", // 设置提示框背景颜色为黑色，透明度为0.6
                textStyle: {
                    color: "#fff" // 设置提示框中的字体颜色为白色
                },
                formatter(params) {
                    const parts = params.name.split(" ");
                    if (parts.length > 1) {
                        parts[parts.length - 1] = parts[parts.length - 1].replace(/\d+(\.\d+)?/g, "");
                    }
                    const bWithoutNumbers = parts.join(" ");
                    return showPercent
                        ? `${params.seriesName} <br/>${bWithoutNumbers}: ${params.value}%`
                        : `${params.seriesName} <br/>${bWithoutNumbers}: ${params.value}`;
                }
            },
            legend: {
                orient: "vertical",
                top: "center",
                left: maxWidth === "450px" ? "10px" : "0px",
                itemGap: 15,
                itemWidth: 12, // 设置图例 item 的宽度
                itemHeight: 12, // 设置图例 item 的高度
                formatter(name) {
                    const nameSplit = name.split(/\s+/);
                    if (
                        nameSplit[0] === "Usage" ||
                        nameSplit[0] === "Free" ||
                        nameSplit[0] === "Used" ||
                        nameSplit[0] === "Unused" ||
                        nameSplit[0] === "Normal" ||
                        nameSplit[0] === "Abnormal" ||
                        nameSplit[0] === "Expired"
                    ) {
                        return showPercent
                            ? `{name|${nameSplit[0]}}{count|${nameSplit[1]}%}`
                            : `{name|${nameSplit[0]}}{count|${nameSplit[1]}}`;
                    }
                    return name;
                }
            },
            textStyle: {
                rich: {
                    name: {
                        color: "#929A9E",
                        lineHeight: 20,
                        textAlign: "center",
                        display: "inline-block",
                        width: maxWidth === "450px" ? 65 : 40
                    },
                    count: {
                        fontSize: "14px",
                        fontWeight: 700,
                        lineHeight: 20,
                        textAlign: "center",
                        display: "inline-block",
                        width: 10
                    }
                }
            },
            series: [
                {
                    name,
                    type: "pie",
                    center: ["70%", "50%"],
                    radius: chartRadius,
                    label: {
                        show: chartType === "ring",
                        position: "center",
                        formatter: () => {
                            if (showPercent) {
                                return "";
                            }
                            return `{value|${total}}\n{total|Total}`;
                        },
                        rich: {
                            value: {
                                color: "#333",
                                fontSize: maxWidth === "450px" ? 20 : "16px",
                                fontWeight: "bold",
                                lineHeight: 30
                            },
                            total: {
                                color: "#333",
                                fontSize: maxWidth === "450px" ? 16 : "14px",
                                lineHeight: 20
                            }
                        }
                    },
                    data: seriesData,
                    emphasis: {
                        itemStyle: {
                            shadowBlur: 10,
                            shadowOffsetX: 0,
                            shadowColor: "rgba(0, 0, 0, 0.5)"
                        }
                    }
                }
            ]
        };
        if (colorList.length !== 0) {
            option.color = colorList;
        }
        myChart.setOption(option);
        myChart.on("click", params => {
            if (onClicked) {
                onClicked(params);
            }
        });

        const handleResize = () => {
            myChart.resize();
        };

        window.addEventListener("resize", handleResize);

        return () => {
            window.removeEventListener("resize", handleResize);
        };
    }, [seriesData]);
    return (
        <Flex justify="center" align="center">
            <div style={{height, width: "100%", maxWidth}} ref={chartRef} />
        </Flex>
    );
};

export const BarEcharts = memo(({fetchDataApi, refreshInterval = 600000, colorList = []}) => {
    const [xAxis, setXAxis] = useState([]);
    const [yAxisData, setYAxisData] = useState([]);

    const fetchData = async () => {
        await fetchDataApi().then(res => {
            // if (res.status === 200) {
            //     setXAxis(res.key);
            //     setYAxisData(res.value);
            // }
            setXAxis(res.keys);
            setYAxisData(res.values);
        });
    };

    useEffect(() => {
        fetchData();
        const intervalId = setInterval(fetchData, refreshInterval);
        return () => {
            clearInterval(intervalId);
        };
    }, []);

    return <BaseBarEcharts xAxis={xAxis} yAxisData={yAxisData} colorList={colorList} />;
});

export const StaticBarEcharts = ({data, colorList = [], width = "", onClicked}) => {
    const [xAxis, setXAxis] = useState([]);
    const [yAxisData, setYAxisData] = useState([]);

    const formatData = () => {
        setXAxis(data.keys);
        setYAxisData(data.values);
    };

    useEffect(() => {
        formatData();
    }, [data]);

    return (
        <BaseBarEcharts xAxis={xAxis} yAxisData={yAxisData} colorList={colorList} width={width} onClicked={onClicked} />
    );
};

export const BaseBarEcharts = ({xAxis, yAxisData, colorList = [], width = "", onClicked = null}) => {
    const chartRef = useRef();
    useEffect(() => {
        const myChart = echarts.init(chartRef.current);
        const option = {
            tooltip: {
                trigger: "axis",
                formatter(params) {
                    const {name} = params[0];
                    const {value} = params[0];
                    return `${name}: ${value}`;
                }
            },
            xAxis: {
                data: xAxis,
                axisLabel: {
                    interval: 0
                    // 可以根据需求设置间隔，比如 interval: 1 表示隔一个标签显示一个
                }
            },
            yAxis: {},
            grid: {
                left: 25,
                right: "3%",
                top: "8%",
                bottom: "10%"
            },
            series: [
                {
                    type: "bar",
                    barWidth: width || "40%",
                    data: yAxisData
                }
            ]
        };
        if (colorList.length !== 0) {
            option.color = colorList;
        }
        myChart.setOption(option);
        myChart.on("click", params => {
            if (onClicked) {
                onClicked(params);
            }
        });

        const handleResize = () => {
            myChart.resize();
        };

        window.addEventListener("resize", handleResize);

        return () => {
            window.removeEventListener("resize", handleResize);
        };
    }, [yAxisData]);
    return (
        <div style={{display: "flex", justifyContent: "center", alignItems: "center", height: "100%"}}>
            <div style={{height: "100%", width: "100%"}} ref={chartRef} />
        </div>
    );
};

export const Linechart = ({title, chartData, chartXAxis}) => {
    const LinechartRef = useRef();
    useEffect(() => {
        const myzhChart = echarts.init(LinechartRef.current);
        const LineOption = {
            tooltip: {
                trigger: "axis",
                backgroundColor: "rgba(0, 0, 0, 0.6)",
                textStyle: {
                    color: "#fff"
                },
                formatter(params) {
                    let tooltipContent = `${params[0].axisValueLabel}<br/>`;
                    params.forEach(item => {
                        tooltipContent += `${item.marker} ${item.seriesName}: ${item.value}%<br/>`;
                    });
                    return tooltipContent;
                }
            },
            xAxis: {
                type: "category",
                data: chartData,
                axisLabel: {
                    interval: 0
                }
            },
            yAxis: {
                type: "value",
                min: 0,
                max: 100,
                axisLabel: {
                    formatter: "{value} %"
                }
            },
            grid: {
                left: "9%",
                right: "2%",
                top: "5%",
                bottom: "12%"
            },
            series: [
                {
                    name: `${title}`,
                    type: "line",
                    data: chartXAxis,
                    symbol: "none",
                    itemStyle: {
                        color: "#14C9BB"
                    }
                }
            ]
        };
        myzhChart.setOption(LineOption);
        const handleResize = () => {
            myzhChart.resize();
        };

        window.addEventListener("resize", handleResize);

        return () => {
            window.removeEventListener("resize", handleResize);
        };
    }, [chartData, chartXAxis]);
    return (
        <div>
            <div style={{height: "95%", width: "100%"}} ref={LinechartRef} />
        </div>
    );
};

export const MultiLineChart = ({title, chartData, chartXAxis}) => {
    const chartRef = useRef(null);
    // 生成静态数据的函数（示例数据）
    const generateRankingData = () => {
        const rankingMap = new Map();

        rankingMap.set("te1", [0, 100, 200, 300, 200, 500, 600, 600, 300, 300]);
        rankingMap.set("te2", [500, 600, 700, 600, 400, 400, 400, 400, 400, 400]);
        rankingMap.set("te3", [200, 200, 200, 400, 100, 400, 300, 200, 500, 400]);
        rankingMap.set("te4", [200, 300, 100, 200, 500, 200, 600, 700, 500, 600]);
        rankingMap.set("te5", [500, 500, 500, 500, 500, 500, 500, 500, 500, 500]);

        return rankingMap;
    };

    // 为每个系列指定颜色
    const lineColors = {
        te1: "#F67B7B",
        te2: "#F9A07A",
        te3: "#F9D779",
        te4: "#77D2F6",
        te5: "green"
    };
    const generateSeriesList = () => {
        const seriesList = [];
        const rankingMap = generateRankingData();
        rankingMap.forEach((data, name) => {
            const lineColor = lineColors[name]; // 获取每条线的颜色
            const series = {
                name,
                type: "line",
                // smooth: true,
                symbol: "circle",
                symbolSize: 10,
                showSymbol: false,
                emphasis: {
                    itemStyle: {
                        color: lineColor,
                        borderWidth: 2,
                        borderColor: "#FFFFFF",
                        shadowBlur: 10,
                        shadowColor: lineColor
                    }
                },
                lineStyle: {
                    width: 2,
                    color: lineColor
                },
                itemStyle: {
                    color: lineColor
                },
                data
            };
            seriesList.push(series);
        });
        return seriesList;
    };
    useEffect(() => {
        const myChart = echarts.init(chartRef.current);

        const option = {
            tooltip: {
                trigger: "axis"
            },
            legend: {
                orient: "horizontal",
                bottom: 0,
                top: "93%",
                data: ["te1", "te2", "te3", "te4", "te5"],
                itemWidth: 10,
                itemHeight: 10,
                icon: "rect",
                itemGap: 35
            },
            grid: {
                left: "2%",
                right: "2%",
                top: "4%",
                bottom: "8%",
                containLabel: true
            },
            xAxis: {
                type: "category",
                boundaryGap: false,
                data: chartData,
                // data: ["0:00", "1:00", "2:00", "3:00", "4:00", "5:00", "6:00", "7:00", "8:00", "9:00"]
                axisLabel: {
                    interval: 0
                }
            },
            yAxis: {
                type: "value",
                axisLabel: {
                    formatter: "{value}"
                },
                min: 0,
                max: 1000,
                interval: 200,
                splitLine: {
                    show: true
                }
            },
            series: generateSeriesList()
        };

        // 渲染图表
        myChart.setOption(option);

        // 窗口尺寸变化时调整图表
        const handleResize = () => {
            myChart.resize();
        };

        window.addEventListener("resize", handleResize);

        // 清理工作：销毁图表实例
        return () => {
            window.removeEventListener("resize", handleResize);
            myChart.dispose();
        };
    }, [chartData, chartXAxis]);

    return (
        <div style={{width: "100%", height: "100%"}}>
            <div ref={chartRef} style={{height: "290px"}} />
        </div>
    );
};

export const CustomLineChart = ({chartOption}) => {
    const chartRef = useRef(null);

    useEffect(() => {
        let myChart;

        const refreshChart = async () => {
            if (chartRef.current) {
                if (myChart) {
                    myChart.dispose();
                }
                await new Promise(resolve => {
                    setTimeout(resolve, 0);
                });

                myChart = echarts.init(chartRef.current);
                myChart.setOption(chartOption, true);
            }
        };

        refreshChart();

        const handleResize = () => {
            if (myChart) {
                myChart.resize();
            }
        };

        window.addEventListener("resize", handleResize);

        return () => {
            window.removeEventListener("resize", handleResize);
            if (myChart) {
                myChart.dispose();
            }
        };
    }, [chartOption]);

    return <div ref={chartRef} style={{width: "100%"}} />;
};

export const TelemetryChart = ({chartData, xAxisData, xAxisInterval, timeRange}) => {
    const option = useMemo(
        () => ({
            tooltip: {
                trigger: "axis",
                formatter: params => {
                    const sortedParams = params.sort((a, b) => b.value[1] - a.value[1]);

                    let content = `
            <div style="width: 100%; margin: 0; padding: 0;">
                <div style="background-color: #F8FAFB; width: calc(100% + 22px); padding: 5px;padding-left:14px; margin: -11px -12px 10px -11px;border-bottom: 1px solid #F2F2F2;">
                    <div style="font-size: 16px;front-weight: 600 ; color: #212519">${params[0].name}</div>
                </div>
        `;
                    sortedParams.forEach(item => {
                        content += `
                    <div style="display: flex; justify-content: space-between; align-items: center;">
                      <div style="display: flex; align-items: center;">
                        <span style="display:inline-block;margin-right:5px;border-radius:1px;width:12px;height:12px;background-color:${item.color};"></span>
                        <span style="front-weight: 400; font-size: 14px; color: #929A9E; margin-letf: 2px;">${item.seriesName}</span>
                      </div>
                      <span style="margin-left: 20px;front-weight: 400; font-size: 14px; color: #212519">${item.value[1]}</span>
                    </div>
                `;
                    });

                    content += `</div>`;
                    return content;
                },
                position(pos, params, el, elRect, size) {
                    const obj = {};
                    const [x, y] = pos;
                    const tooltipWidth = el.getBoundingClientRect().width;
                    const parentRect = el.parentElement.getBoundingClientRect();
                    const rightSpace = parentRect.width - x;
                    if (y > window.innerHeight / 2) {
                        obj.bottom = "30px";
                        delete obj.top;
                    }
                    if (rightSpace < x - 10 - tooltipWidth) {
                        obj.left = `${x - tooltipWidth - 10}px`;
                    } else {
                        obj.left = `${x + 10}px`;
                    }

                    return obj;
                }
            },
            legend: {
                data: chartData.map(item => item.name),
                orient: "horizontal", // 设置图例的方向为水平
                top: "90%", // 设置图例的垂直位置
                left: "center", // 设置图例的水平位置
                right: "5%",
                textStyle: {
                    // 图例文字样式
                    fontSize: 15
                },
                itemWidth: 10, // 图例图形的宽度
                itemHeight: 10, // 图例图形的高度
                type: "scroll",
                itemGap: 30,
                pageIconColor: "#A2ACB2", // 默认可点击色值
                pageIconInactiveColor: "#E3E5EB", // 不可点击色值
                width: "95%",
                icon: "rect"
            },
            grid: {
                left: "3%",
                right: "3%",
                top: "5%",
                bottom: "10%",
                containLabel: true,
                width: "95%",
                height: "75%"
            },
            xAxis: {
                type: "category",
                data: xAxisData,
                axisLabel: {
                    interval: xAxisInterval,
                    formatter(value) {
                        const date = new Date(value);
                        const startDate = new Date(timeRange[0] || Date.now() - 5 * 60 * 1000);
                        const endDate = new Date(timeRange[1] || Date.now());
                        const hour = date.getHours().toString().padStart(2, "0");
                        const minute = date.getMinutes().toString().padStart(2, "0");
                        const second = date.getSeconds().toString().padStart(2, "0");
                        if (startDate.getMonth() !== endDate.getMonth() || startDate.getDate() !== endDate.getDate()) {
                            return `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, "0")}-${date.getDate().toString().padStart(2, "0")} ${hour}:${minute}`;
                        }
                        return `${hour}:${minute}:${second}`;
                    }
                },
                splitLine: {
                    show: true
                }
            },
            yAxis: {
                type: "value",
                axisLabel: {
                    formatter(value) {
                        if (value > 1e9) {
                            return `${value.toExponential(2)}`;
                        }
                        if (value >= 1000000) {
                            return `${value / 1000000}M`;
                        }
                        if (value >= 1000) {
                            return `${value / 1000}k`;
                        }
                        return value;
                    }
                }
            },
            series: chartData.map(item => ({
                name: item.name,
                type: "line",
                data: item.data,
                symbol: "none"
            })),
            width: "100%",
            height: "180px"
        }),
        [chartData]
    );

    return (
        // eslint-disable-next-line react/jsx-no-useless-fragment
        <>
            {option.series.length === 0 ? (
                <div style={{display: "flex", justifyContent: "center", alignItems: "center"}}>
                    <Empty image={EmptyPic} description="No Data" imageStyle={{display: "block", margin: 0}} />
                </div>
            ) : (
                <CustomLineChart chartOption={option} />
            )}
        </>
    );
};
