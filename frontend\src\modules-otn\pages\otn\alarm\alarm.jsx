import React, {useD<PERSON><PERSON><PERSON>V<PERSON><PERSON>, useEffect, useMemo, useRef, useState} from "react";
import {message, Popover, Select, Tag} from "antd";
import {useDispatch, useSelector} from "react-redux";
import dayjs from "dayjs";
import {useLocation} from "react-router-dom";
import {useRequest} from "ahooks";
import {CustomTable} from "@/modules-otn/components/common/custom_table";
import Icon, {QuestionCircleOutlined} from "@ant-design/icons";
import {apiRpc, clearHistoryAlarm, objectAdd, objectGet, objectUpdate, requestSyncTimer} from "@/modules-otn/apis/api";
import {ALARM_COLOR, filterEvents, MODAL_WIDTH, sortArr} from "@/modules-otn/utils/util";
import {gLabelList} from "@/store/modules/otn/languageOTNSlice";
import useUserRight from "@/modules-otn/hooks/useUserRight";
import {openModalRpc} from "@/modules-otn/components/form/edit_rpc";
import {themeVariable} from "@/modules-otn/config/theme";
import {updateAlarm, updateSwitchAlarm, updateTNMSAlarm} from "@/store/modules/otn/notificationSlice";
import {getFMTDeviceCard, getDCSDeviceCard} from "@/modules-ampcon/apis/fmt";
import {getOTNDeviceIP} from "@/modules-ampcon/apis/otn";
import {openAlarmMaskDialog} from "@/modules-otn/pages/otn/alarm/alarm_mask";
import {
    configureIcon,
    alarmMaskButtonIcon,
    deleteCommonIcon,
    deleteCommonDisabledIcon
} from "@/modules-otn/pages/otn/device/device_icons";
import openCustomEditForm from "@/modules-otn/components/form/edit_form_custom";
import {
    getHistorySwitchAlarm,
    ackSwitchAlarm,
    getSwitchAlarm,
    clearSwitchHistoryAlarm
} from "@/modules-ampcon/apis/monitor_api";
import styles from "./alarm.module.scss";
import {AlarmFaultHandlingCN, AlarmFaultHandlingEN} from "./helper";
import MutilColumnForm from "../common/mutil_column_form";

const Alarm = ({
    serviceType,
    head = false,
    alarmType = "currentAlarm",
    filter,
    paginationEnable,
    tabSelectionType = null,
    scroll = false
}) => {
    const {neNameMap, neTypeMap} = useSelector(state => state.neName);
    const currentUser = useSelector(state => state.user.userInfo);
    const dispatch = useDispatch();
    const location = useLocation();
    const [data, setData] = useState([]);
    const readyOnlyRight = useUserRight();
    const deferredData = useDeferredValue(data);
    const {alarms, switchAlarms} = useSelector(state => state.notification);
    const {tableFilter} = useSelector(state => state.map);
    const {language, labelList} = useSelector(state => state.languageOTN);
    const [commonData, setCommonData] = useState({});
    const AlarmFaultHandling = useMemo(() => {
        return language === "en" ? AlarmFaultHandlingEN : AlarmFaultHandlingCN;
    }, [language]);

    const [selectNe, setSelectNe] = useState();
    const selectNeRef = useRef(selectNe);
    const [neOptions, setNeOptions] = useState([]);
    const [selectResource, setSelectResource] = useState();
    const selectResourceRef = useRef(selectResource);
    const [resourceOptions, setResourceOptions] = useState([]);
    const [selectSeverity, setSelectSeverity] = useState();
    const selectSeverityRef = useRef(selectSeverity);
    const [SeverityOptions, setSeverityOptions] = useState([]);
    const [filterData, setFilterData] = useState(null);
    const callOTNAPI = (apiType, tabType, args = null) => {
        const apiTypeMapping = {
            OTNDeviceIP: getOTNDeviceIP,
            DeviceCard: type => {
                const deviceTypeMapping = {
                    7: getFMTDeviceCard,
                    8: getDCSDeviceCard
                };
                return deviceTypeMapping[type] || null;
            }
        };

        const tabTypeMapping = {
            power: "ALL",
            linecard: "ALL",
            edfa: "EDFA",
            oeo: "OEO"
        };

        if (!apiTypeMapping[apiType] || !tabTypeMapping[tabType]) {
            return Promise.reject(new Error("Invalid apiType or tabType"));
        }

        const apiFunction = apiTypeMapping[apiType];
        if (apiType === "DeviceCard" && args && args.deviceType) {
            const {deviceType, ...otherArgs} = args;
            const selectedFunction = apiFunction(deviceType);
            if (!selectedFunction) {
                return Promise.reject(new Error(`No function found for deviceType: ${deviceType}`));
            }

            return selectedFunction(tabTypeMapping[tabType], otherArgs);
        }
        return apiFunction(tabTypeMapping[tabType], args);
    };

    useEffect(() => {
        const getSelectNE = async () => {
            try {
                const [deviceResponse, objectResponse] = await Promise.all([
                    callOTNAPI("OTNDeviceIP", "power", {layer: "l0"}),
                    objectGet("config:ne", {})
                ]);

                const {data: deviceList, errorCode: deviceCode} = deviceResponse;
                if (deviceCode !== 200) {
                    throw new Error(`Failed to retrieve device list: ${deviceResponse.errorMsg}`);
                }

                const {documents} = objectResponse;
                if (!objectResponse) {
                    throw new Error(`Failed to retrieve network element list: ${objectResponse.apiMessage}`);
                }
                const deviceResources = deviceList.map(item => item);
                const neResources = documents.map(item => item);
                const allResources = [...deviceResources, ...neResources];
                const uniqueResources = [...new Set(allResources)];

                const activeNeNames = new Set(deferredData.map(item => item.ne_id.split(":")[0]));

                const filteredResources = uniqueResources.filter(item => {
                    const ip = item.ip || item?.value?.host;
                    return activeNeNames.has(ip);
                });

                setNeOptions(
                    filteredResources.map(item => ({
                        label: item.name || item.value.name,
                        value: item.name || item.value.name,
                        key: item.ip || item.value.host
                    }))
                );
            } catch (error) {
                console.error("Failed to load resource options:", error);
                message.error("Failed to retrieve resource list, please try again");
            }
        };
        getSelectNE();
    }, [deferredData]);
    const updateSelectNe = v => {
        setSelectNe(v);
        selectNeRef.current = v ? neOptions.find(option => option.label === v)?.key : null;
        filterAlarms();
    };

    const updateSelectResource = v => {
        setSelectResource(v);
        selectResourceRef.current = v;
    };

    const updateSeverity = v => {
        setSelectSeverity(v);
        selectSeverityRef.current = v;
    };

    const filterAlarms = rs => {
        if (!selectNeRef.current && !selectResourceRef.current && !selectSeverityRef.current) {
            setFilterData(null);
            return;
        }
        setFilterData(
            (rs ?? deferredData).filter(
                i =>
                    !(
                        (selectNeRef.current && i.ne_id.split(":")[0] !== selectNeRef.current) ||
                        (selectResourceRef.current && i.resource !== selectResourceRef.current) ||
                        (selectSeverityRef.current && i.severity !== selectSeverityRef.current)
                    )
            )
        );
    };

    const getHistoryAlarms = async () => {
        const otnHistoryAlarm =
            (await objectGet("nms:alarmhistory", {}))?.documents?.map?.(item => ({
                key: item.id,
                ...item.value.data,
                ne_id: item.value.ne_id,
                name: neNameMap[item.value.ne_id]
            })) ?? [];

        const switchHistoryAlarm = await getHistorySwitchAlarm().then(
            res =>
                res?.data?.map?.(item => ({
                    key: `${item.ne_id}:${item.data.id}`,
                    ne_id: item.ne_id.split(":").pop(),
                    name: item.ne_id.split(":").pop(),
                    ...item.data.state
                })) ?? []
        );
        return [...otnHistoryAlarm, ...switchHistoryAlarm];
    };

    const {run: loadData, loading} = useRequest(
        () => {
            try {
                if (alarmType === "historyAlarm") {
                    return getHistoryAlarms();
                }
                // current
                let newAlarms = [...alarms, ...switchAlarms];
                if (!head) {
                    if (tableFilter?.type === "NODE_NE") {
                        newAlarms = filterEvents(newAlarms, tableFilter);
                    } else if (tableFilter?.type === "NODE_GROUP" && tableFilter.idList.length > 0) {
                        newAlarms = newAlarms.filter(item => tableFilter.idList.includes(item.ne_id));
                    } else if (
                        tableFilter?.type &&
                        ["ots", "oms", "och", "client"].includes(tableFilter?.type) &&
                        tableFilter.servicePortList
                    ) {
                        const filterKeys = [];
                        const fiterFun = (ne_id, keyStr, alarmList) => {
                            const relateInfo = keyStr.replace(/\w+-/, "-");
                            if (!filterKeys.includes(`${ne_id}_${relateInfo}`)) {
                                filterKeys.push(`${ne_id}_${relateInfo}`);
                                const regExp = new RegExp(`^[A-z]+${relateInfo}$`);
                                newAlarms.map(i => {
                                    if (
                                        i.ne_id === ne_id &&
                                        regExp.test(i.resource) &&
                                        alarmList.filter(a => a.id === i.id && a.ne_id === i.ne_id).length === 0
                                    ) {
                                        alarmList.push(i);
                                    }
                                });
                            }
                        };
                        const _alarms = [];
                        tableFilter.servicePortList.map(item => {
                            fiterFun(item.ne_id, "CHASSIS-1", _alarms); // ne
                            fiterFun(item.ne_id, item.card, _alarms); // card
                            if (item.ports) {
                                item.ports.map(port => {
                                    let _port = port;
                                    if (typeof port !== "string") {
                                        [_port] = Object.values(port) ?? [];
                                    }
                                    fiterFun(item.ne_id, _port, _alarms); // port
                                    const tsvName = `TRANSCEIVER${_port.substring(
                                        _port.indexOf("-"),
                                        _port.lastIndexOf("-")
                                    )}`;
                                    if (!filterKeys.includes(`${item.ne_id}_${tsvName}`)) {
                                        filterKeys.push(`${item.ne_id}_${tsvName}`);
                                        newAlarms.map(i => {
                                            if (i.ne_id === item.ne_id && i.resource.startsWith(tsvName)) {
                                                if (_alarms.filter(item => item.id === i.id).length === 0) {
                                                    _alarms.push(i);
                                                }
                                            }
                                        });
                                    }
                                    if (_port.endsWith("OUT") || _port.endsWith("IN")) {
                                        fiterFun(item.ne_id, _port.replace(/OUT|IN/, ""), _alarms); // port
                                    }
                                });
                            }
                        });
                        const ramanNE = tableFilter.provision?.portList?.raman;
                        if (ramanNE) {
                            newAlarms
                                .filter(i => ramanNE.includes(i.ne_id))
                                .forEach(i => {
                                    _alarms.push(i);
                                });
                        }
                        newAlarms = _alarms;
                    } else if (serviceType) {
                        newAlarms = newAlarms.filter(item => neTypeMap[item.ne_id] === "5");
                    }
                }
                let _alarms = newAlarms?.map?.(item => {
                    if (item["type-id"] === "AmpCon-T-ALARM") return;
                    let neName = neNameMap[item.ne_id];
                    if (!neName || neName === "") {
                        neName = item.ne_id;
                    }
                    return {
                        ...item,
                        key: `${item.ne_id}_${item.id}`,
                        name: neName,
                        "time-created": item["time-created"] ? item["time-created"] : item["start-time"]
                    };
                });
                if (filter) {
                    const [, filterChassis, filterSlot, filterPort] = filter.object?.split?.("-") ?? [];
                    _alarms = _alarms.filter(i => {
                        if (i.ne_id !== filter.ne_id) {
                            return false;
                        }
                        if (filter.object) {
                            const [, chassis, slot, port] = i.resource.split("-");
                            return (
                                i.resource === filter.card ||
                                (chassis === filterChassis &&
                                    slot === filterSlot &&
                                    (!filterPort || (filterPort && port && filterPort === port)))
                            );
                        }
                        return true;
                    });
                }
                return _alarms;
            } catch (e) {
                return [];
            }
        },
        {
            manual: true,
            onSuccess: rs => {
                const formattedData = rs.map(item => {
                    if (item.severity) {
                        item.severity = item.severity.charAt(0).toUpperCase() + item.severity.slice(1).toLowerCase();
                    }
                    return item;
                });
                setData(formattedData);
                filterAlarms(formattedData);
            },
            onError: () => {
                setData([]);
            }
        }
    );
    const refreshHandle = () => {
        try {
            if (alarmType === "currentAlarm") {
                objectGet("ne:alarm", {}).then(res => {
                    dispatch(updateAlarm(res.documents));
                });
                getSwitchAlarm().then(res => {
                    if (res.status === 200) {
                        dispatch(updateSwitchAlarm(res.data));
                    } else {
                        console.log("Error fetching data:", res.msg);
                    }
                });
            }
            loadData(alarmType);
        } catch (e) {
            console.log(e);
        }
    };

    useEffect(() => {
        loadData(alarmType);
    }, [alarms, switchAlarms, neNameMap, tableFilter, alarmType, filter]);

    let rowButtonsConfig;
    const globButtonsConfig = [];

    if (alarmType === "currentAlarm") {
        rowButtonsConfig = [
            {
                label: gLabelList.ack_alarm,
                disabled: () => {
                    return readyOnlyRight?.disabled;
                },
                async onClick() {
                    const originValue = {...this};
                    openModalRpc(
                        originValue.ne_id,
                        "alarm-ack",
                        "5",
                        gLabelList.ack_alarm,
                        {
                            id: originValue.id,
                            "operator-name": currentUser.username ?? ""
                        },
                        ["id"],
                        async () => {
                            requestSyncTimer({
                                ne_id: originValue.ne_id,
                                sync: {
                                    type: ["system"]
                                }
                            });
                        },
                        null,
                        async values => {
                            if (originValue.deviceType === 1 || originValue.deviceType === 3) {
                                const ackRet = await ackSwitchAlarm({
                                    eventId: originValue.id,
                                    operatorName: currentUser.username,
                                    operatorText: values["operator-text"]
                                });
                                if (ackRet.status === 200) {
                                    await getSwitchAlarm().then(res => {
                                        if (res.status === 200) {
                                            dispatch(updateSwitchAlarm(res.data));
                                        } else {
                                            // eslint-disable-next-line no-console
                                            console.log("Error fetching data:", res.msg);
                                        }
                                    });
                                }
                                return {
                                    response: {
                                        result: ackRet.status === 200
                                    }
                                };
                            }
                            const neType = neTypeMap?.[originValue.ne_id];
                            if (neType === "5") {
                                return apiRpc({
                                    ne_id: originValue.ne_id,
                                    rpcName: "alarm-ack",
                                    rpcConfig: {
                                        ...values,
                                        id: originValue.id,
                                        "operator-name": currentUser.username,
                                        "time-acknowledged": (Date.parse(new Date()) / 1000).toString()
                                    }
                                });
                            }
                            if (neType === "2") {
                                const dbKey = `ne:2:currentAlarmEntry:${originValue.id}`;
                                const ov = (await objectGet("", {DBKey: dbKey})).documents[0];
                                return objectUpdate({
                                    key: dbKey,
                                    msg: false,
                                    data: {
                                        data: {
                                            id: ov.value.data.id,
                                            state: {
                                                ...ov.value.data.state,
                                                ...values,
                                                "operator-name": currentUser.username,
                                                "time-acknowledged": (Date.parse(new Date()) * 1000000).toString()
                                            }
                                        }
                                    },
                                    success: () => {
                                        refreshHandle();
                                    }
                                });
                            }
                        },
                        readyOnlyRight.disabled
                    );
                }
            }
        ];
        globButtonsConfig.push({
            label: gLabelList.alarm_mask,
            icon: <Icon component={alarmMaskButtonIcon} />,
            type: "primary",
            onClick() {
                openAlarmMaskDialog();
            }
        });
        globButtonsConfig.push({
            label: gLabelList.configure,
            icon: <Icon component={configureIcon} />,
            onClick: async () => {
                openCustomEditForm({
                    title: "Configure",
                    width: MODAL_WIDTH.twoCol,
                    columnNum: 2,
                    labelCol: 5,
                    wrapperCol: 18,
                    readOnly: readyOnlyRight.disabled,
                    columns: [
                        [
                            {
                                dataIndex: "enable",
                                inputType: "switch"
                            }
                        ],
                        [
                            {
                                dataIndex: "sender",
                                required: (v, formValues) => formValues?.enable !== false,
                                disabled: v => v?.enable !== true,
                                placeholder: "Please input sender"
                            },
                            {
                                dataIndex: "host",
                                title: "SMTP",
                                pattern: /\w+.\w+/g,
                                message: "mail_service_patter",
                                required: (v, formValues) => formValues?.enable !== false,
                                disabled: v => v?.enable !== true,
                                placeholder: "Please input SMTP"
                            }
                        ],
                        [
                            {
                                dataIndex: "username",
                                title: "User Name",
                                required: (v, formValues) => formValues?.enable !== false,
                                disabled: v => v?.enable !== true,
                                placeholder: "Please input user name"
                            },
                            {
                                dataIndex: "password",
                                inputType: "password",
                                init: false,
                                required: (v, formValues) => formValues?.enable !== false,
                                disabled: v => v?.enable !== true
                            }
                        ],
                        [
                            {
                                dataIndex: "level",
                                inputType: "checkboxgroup",
                                options: ["CRITICAL", "MAJOR", "MINOR", "WARNING", "INFO"],
                                required: (v, formValues) => formValues?.enable !== false,
                                disabled: v => v?.enable !== true
                            }
                        ],
                        [
                            {
                                dataIndex: "port",
                                pattern: /((^[1-5]?\d{1,4}$)|(^6([0-4]\d{3}|(5([0-4]\d{2}|(5([0-2]\d|3[0-5])))))$))/g,
                                message: "number_patter",
                                required: (v, formValues) => formValues?.enable !== false,
                                disabled: v => v?.enable !== true,
                                placeholder: "Please input port"
                            },
                            {
                                dataIndex: "ssl",
                                title: "SSL",
                                inputType: "checkbox",
                                disabled: v => v?.enable !== true
                            }
                        ],
                        [
                            {
                                type: "split"
                            }
                        ],
                        [
                            {
                                dataIndex: "to",
                                title: "recipient",
                                placeholder: "separated_semicolons",
                                pattern: /\w+@\w+.\w/g,
                                message: "send_to_patter",
                                required: (v, formValues) => formValues?.enable !== false,
                                disabled: v => v?.enable !== true
                            },
                            {
                                dataIndex: "subject",
                                required: (v, formValues) => formValues?.enable !== false,
                                disabled: v => v?.enable !== true,
                                placeholder: "Please input subject"
                            }
                        ]
                    ],
                    getData: async () => {
                        const rs = await objectGet("", {DBKey: "nms:mail:1"}, null, false);
                        if (rs?.documents) {
                            return {...rs.documents[0].value, password: ""};
                        }
                        return {};
                    },
                    saveFun: async (diffValue, values) => {
                        const rs = await objectGet("", {DBKey: "nms:mail:1"}, null, false);
                        if (rs?.documents) {
                            if (values.enable) {
                                const updateValues = {...values};
                                if (values.password === "") {
                                    delete updateValues.password;
                                }
                                return await objectUpdate({
                                    key: "nms:mail:1",
                                    data: updateValues
                                });
                            }
                            return await objectUpdate({
                                key: "nms:mail:1",
                                data: {enable: false}
                            });
                        }
                        return await objectAdd({
                            DBKey: "nms:mail:1",
                            data: values
                        });
                    }
                });
            }
        });
    } else {
        globButtonsConfig.push({
            label: gLabelList.clear,
            confirm: {title: gLabelList.clear_history_alarm_warning},
            disabled: readyOnlyRight.disabled,
            icon: <Icon component={readyOnlyRight.disabled ? deleteCommonDisabledIcon : deleteCommonIcon} />,
            onClick() {
                const keys = commonData.historyAlarm?.selectedRowKeys;
                if (keys) {
                    const otnAlarms = keys.filter(i => i.startsWith("nms"));
                    const switchAlarms = keys.filter(i => !i.startsWith("nms"));
                    if (otnAlarms.length > 0) {
                        clearHistoryAlarm({keys: otnAlarms}).then(rs => {
                            if (rs.apiResult === "fail") {
                                message.error(gLabelList.operation_fail).then();
                                return;
                            }
                            loadData(alarmType);
                        });
                    }
                    if (switchAlarms.length > 0) {
                        const clearList = [];
                        switchAlarms.forEach(item => {
                            const parts = item.split(":");
                            clearList.push(parseInt(parts[1], 10));
                        });
                        clearSwitchHistoryAlarm({idList: clearList}).then(res => {
                            if (res.status === 200) {
                                message.success(res.msg);
                            } else {
                                message.error(res.msg);
                            }
                            refreshHandle();
                        });
                    }
                } else {
                    message.error("please select one data at least");
                }
            }
        });
    }

    return (
        <>
            {head && (
                <div style={{width: "100%"}}>
                    <MutilColumnForm
                        fields={[
                            {
                                label: labelList.ne_name,
                                render: (
                                    <Select
                                        value={selectNe ?? null}
                                        style={{width: 280}}
                                        placeholder={labelList.please_select}
                                        allowClear
                                        // onDropdownVisibleChange={open => {
                                        //     if (open) {
                                        //         const neNameSet = new Set(deferredData.map(i => i.name));
                                        //         setNeOptions(
                                        //             sortArr(Array.from(neNameSet)).map(i => ({
                                        //                 label: i,
                                        //                 value: i,
                                        //                 key: i
                                        //             }))
                                        //         );
                                        //     }
                                        // }}
                                        onChange={v => {
                                            updateSelectNe(v);
                                            updateSelectResource(null);
                                            updateSeverity(null);
                                            filterAlarms();
                                        }}
                                        options={neOptions}
                                    />
                                )
                            },
                            {
                                label: labelList.resource,
                                render: (
                                    <Select
                                        value={selectResource ?? null}
                                        placeholder={labelList.please_select}
                                        allowClear
                                        style={{
                                            width: 280
                                        }}
                                        onDropdownVisibleChange={open => {
                                            if (open) {
                                                let filterData = deferredData;
                                                if (selectNeRef.current) {
                                                    filterData = filterData.filter(
                                                        i => i.ne_id.split(":")[0] === selectNeRef.current
                                                    );
                                                }
                                                if (selectSeverityRef.current) {
                                                    filterData = filterData.filter(
                                                        i => i.severity === selectSeverityRef.current
                                                    );
                                                }
                                                const resourceSet = new Set(filterData.map(i => i.resource));
                                                setResourceOptions(
                                                    sortArr(Array.from(resourceSet)).map(i => ({
                                                        label: i,
                                                        value: i,
                                                        key: i
                                                    }))
                                                );
                                            }
                                        }}
                                        onChange={v => {
                                            updateSelectResource(v);
                                            filterAlarms();
                                        }}
                                        options={resourceOptions}
                                    />
                                )
                            },
                            {
                                label: labelList.severity,
                                render: (
                                    <Select
                                        value={selectSeverity ?? null}
                                        placeholder={labelList.please_select}
                                        allowClear
                                        style={{
                                            width: 280
                                        }}
                                        onDropdownVisibleChange={open => {
                                            if (open) {
                                                let filterData = deferredData;
                                                if (selectNeRef.current) {
                                                    filterData = filterData.filter(
                                                        i => i.ne_id.split(":")[0] === selectNeRef.current
                                                    );
                                                }
                                                if (selectResourceRef.current) {
                                                    filterData = filterData.filter(
                                                        i => i.resource === selectResourceRef.current
                                                    );
                                                }
                                                const resourceSet = new Set(filterData.map(i => i.severity));
                                                setSeverityOptions(
                                                    sortArr(Array.from(resourceSet)).map(i => ({
                                                        label: i.charAt(0).toUpperCase() + i.slice(1).toLowerCase(),
                                                        value: i,
                                                        key: i
                                                    }))
                                                );
                                            }
                                        }}
                                        onChange={v => {
                                            updateSeverity(v);
                                            filterAlarms();
                                        }}
                                        options={SeverityOptions}
                                    />
                                )
                            }
                        ]}
                    />
                </div>
            )}
            <CustomTable
                type={alarmType}
                initHead={head}
                initTitle={false}
                scroll={scroll}
                initDataSource={filterData ?? deferredData}
                loading={loading}
                refreshParent={refreshHandle}
                defaultFilters={location?.state}
                initRowOperation={rowButtonsConfig}
                paginationEnable={paginationEnable}
                columnFormat={{
                    // eslint-disable-next-line react/no-unstable-nested-components
                    name: name => {
                        const selectedOption = neOptions.find(option => option.key === name || option.label === name);
                        if (selectedOption) {
                            const {value} = selectedOption;
                            return value;
                        }
                    },
                    severity: severity => {
                        try {
                            if (severity) {
                                const {color, backgroundColor, borderColor} = ALARM_COLOR[severity.toUpperCase()];
                                return (
                                    <Tag style={{color, backgroundColor, borderColor}}>
                                        {gLabelList[severity.toLowerCase()]}
                                    </Tag>
                                );
                            }
                        } catch (e) {
                            return severity;
                        }
                    },
                    "time-created": time => {
                        try {
                            if (time?.includes("T")) {
                                return dayjs(time).format("YYYY/MM/DD HH:mm:ss");
                            }
                            return dayjs(time / 1000000).format("YYYY/MM/DD HH:mm:ss");
                        } catch (e) {
                            return time;
                        }
                    },
                    "time-cleared": time => {
                        try {
                            if (time?.includes("T")) {
                                return dayjs(time).format("YYYY/MM/DD HH:mm:ss");
                            }
                            return dayjs(time / 1000000).format("YYYY/MM/DD HH:mm:ss");
                        } catch (e) {
                            return time;
                        }
                    },
                    resource: r => r,
                    "time-acknowledged": val => {
                        return !val || val === "0000000000" ? "" : new Date(val / 1000000).toLocaleString();
                    },
                    // eslint-disable-next-line react/no-unstable-nested-components
                    "alarm-abbreviate": val => {
                        const content = AlarmFaultHandling?.[val];
                        if (!content) return <span>{val}</span>;
                        const dom = (
                            <>
                                <div className={styles["abbreviate-popover-item-label"]}>
                                    {gLabelList.alarm_meaning}：
                                </div>
                                <div>{content?.alarmMeaning}</div>
                                <div className={styles["abbreviate-popover-item-label"]}>
                                    {gLabelList.report_object}：
                                </div>
                                <div>{content?.reportObject}</div>
                                <div className={styles["abbreviate-popover-item-label"]}>
                                    {gLabelList.error_description}：
                                </div>
                                <div>
                                    {content?.errorDescription?.split("；")?.map(text => (
                                        <div key={text}>{text}</div>
                                    ))}
                                </div>
                                <div className={styles["abbreviate-popover-item-label"]}>
                                    {gLabelList.treatment_suggestion}：
                                </div>
                                <div>
                                    {content?.treatmentSuggestion?.split("；")?.map(text => (
                                        <div key={text}>{text}</div>
                                    ))}
                                </div>
                            </>
                        );
                        return (
                            <div style={{display: "flex", alignItems: "center"}}>
                                <Popover
                                    title={val}
                                    content={dom}
                                    overlayInnerStyle={{maxWidth: 320, maxHeight: 400, overflowY: "auto"}}
                                >
                                    <QuestionCircleOutlined
                                        style={{color: themeVariable.colorPrimary, float: "left"}}
                                    />
                                </Popover>
                                <span style={{paddingLeft: 4}}>{val}</span>
                            </div>
                        );
                    }
                }}
                buttons={globButtonsConfig}
                tabSelectionType={tabSelectionType}
                commonData={commonData}
                setCommonData={setCommonData}
            />
        </>
    );
};
export default Alarm;
