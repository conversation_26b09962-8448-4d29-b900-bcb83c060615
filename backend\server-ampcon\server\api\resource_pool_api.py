import json
import logging
import traceback
import threading
from flask import Blueprint, jsonify, Response, request
from datetime import timedelta, datetime, date

from server.util.permission import admin_permission, readonly_permission
from server.util.utils import is_name_valid
from server.db.models.resource_pool import resource_pool_asn_db,resource_pool_area_db,resource_pool_ip_db,is_ranges_conflict

resource_pool_blueprint_mold = Blueprint("resource_pool_blueprint", __name__, template_folder="templates")
LOG = logging.getLogger(__name__)


@resource_pool_blueprint_mold.route("/resource_pool_asn_table_data", methods=["POST"])
@readonly_permission.require(http_exception=403)
def resource_pool_asn_table_data():
    try:
        return resource_pool_asn_db.query_all_resource_pool()
    except Exception as e:
        LOG.error(f"resource_pool_asn_table_data error: {e}")
        return jsonify({'status': 500, 'info': 'Failed to get resource pool data'})


@resource_pool_blueprint_mold.route("/delete_asn_pool_record", methods=["POST"])
@readonly_permission.require(http_exception=403)
def delete_pool_asn_record():
    try:
        data = request.get_json()
        if resource_pool_asn_db.delete_resource_pool_by_id(data["id"]):
            return jsonify({'status': 200, 'info': 'Delete resource pool successfully'})
        else:
            return jsonify({'status': 500, 'info': 'Failed to delete resource pool'})
    except Exception as e:
        LOG.error(f"delete_pool_asn_record error: {e}")
        return jsonify({'status': 500, 'info': 'Failed to delete resource pool'})


@resource_pool_blueprint_mold.route("/add_asn_pool", methods=["POST"])
@readonly_permission.require(http_exception=403)
def add_asn_pool():
    try:
        data = request.get_json()
        name = data.get('poolName')
        ranges = data.get('ranges')
        if not name or not is_name_valid(name) or len(name) >= 64:
            return jsonify({'status': 500, 'info': 'Invalid name'})
        if resource_pool_asn_db.query_resource_pool_by_name(name).first():
            return jsonify({'status': 500, 'info': 'Resource pool already exists'})
        if len(ranges) == 0:
            return jsonify({'status': 500, 'info': 'Ranges is empty'})
        if is_ranges_conflict(ranges):
            return jsonify({'status': 500, 'info': 'Ranges conflict'})
        if resource_pool_asn_db.add_resource_pool(name, ranges):
            return jsonify({'status': 200, 'info': 'Add resource pool successfully'})
        else:
            return jsonify({'status': 500, 'info': 'Failed to add resource pool'})
    except Exception as e:
        LOG.error(f"add_asn_pool error: {e}")
        return jsonify({'status': 500, 'info': 'Failed to add resource pool'})


@resource_pool_blueprint_mold.route("/edit_asn_pool", methods=["POST"])
@readonly_permission.require(http_exception=403)
def edit_asn_pool():
    try:
        data = request.get_json()
        name = data.get('poolName')
        ranges = data.get('ranges')
        pool_id = data.get('poolId')
        if not resource_pool_asn_db.query_resource_pool_by_id(pool_id).first():
            return jsonify({'status': 500, 'info': 'Resource pool not found'})
        if not name or not is_name_valid(name) or len(name) >= 64:
            return jsonify({'status': 500, 'info': 'Invalid name'})
        add_ranges = ranges.get('add', [])
        modify_ranges = ranges.get('modify', [])
        if is_ranges_conflict(add_ranges + modify_ranges):
            return jsonify({'status': 500, 'info': 'Ranges conflict'})
        if resource_pool_asn_db.edit_resource_pool(pool_id, name, ranges):
            return jsonify({'status': 200, 'info': 'Edit resource pool successfully'})
        else:
            return jsonify({'status': 500, 'info': 'Failed to add resource pool'})
    except Exception as e:
        LOG.error(f"add_asn_pool error: {e}")
        return jsonify({'status': 500, 'info': 'Failed to add resource pool'})


@resource_pool_blueprint_mold.route("/clone_asn_pool", methods=["POST"])
@readonly_permission.require(http_exception=403)
def clone_asn_pool():
    try:
        data = request.get_json()
        pool_id = data.get('poolId')
        new_pool_name = data.get('poolName')
        if not new_pool_name or not is_name_valid(new_pool_name) or len(new_pool_name) >= 64:
            return jsonify({'status': 500, 'info': 'Invalid name'})
        if not resource_pool_asn_db.query_resource_pool_by_id(pool_id).first():
            return jsonify({'status': 500, 'info': 'Resource pool not found'})
        if resource_pool_asn_db.clone_resource_pool(pool_id, new_pool_name):
            return jsonify({'status': 200, 'info': 'Clone resource pool successfully'})
        else:
            return jsonify({'status': 500, 'info': 'Failed to add resource pool'})
    except Exception as e:
        LOG.error(f"add_asn_pool error: {e}")
        return jsonify({'status': 500, 'info': 'Failed to add resource pool'})


@resource_pool_blueprint_mold.route("/generate_pool_record", methods=["POST"])
@readonly_permission.require(http_exception=403)
def generate_pool_record():
    try:
        data = request.get_json()
        pool_id = data.get('poolId')
        record_num = data.get('recordNum')
        if record_num <= 0:
            return jsonify({'status': 500, 'info': 'Invalid record number'})
        if not resource_pool_asn_db.query_resource_pool_by_id(pool_id).first():
            return jsonify({'status': 500, 'info': 'Resource pool not found'})
        if resource_pool_asn_db.generate_resource_from_pool(pool_id, record_num):
            return jsonify({'status': 200, 'info': 'Generate resource pool record successfully'})
        else:
            return jsonify({'status': 500, 'info': 'Failed to add resource pool'})
    except Exception as e:
        LOG.error(f"add_asn_pool error: {e}")
        return jsonify({'status': 500, 'info': 'Failed to add resource pool'})


# todo just for test
@resource_pool_blueprint_mold.route("/delete_first_ten_record", methods=["POST"])
@readonly_permission.require(http_exception=403)
def delete_first_ten_record():
    try:
        data = request.get_json()
        pool_id = data.get('poolId')
        record_num = data.get('recordNum')
        if resource_pool_asn_db.delete_first_n_used_record(pool_id, record_num):
            return jsonify({'status': 200, 'info': 'Delete first ten record successfully'})
        else:
            return jsonify({'status': 500, 'info': 'Failed to delete first ten record'})
    except Exception as e:
        LOG.error(f"delete_first_ten_record error: {e}")
        return jsonify({'status': 500, 'info': 'Failed to delete first ten record'})


@resource_pool_blueprint_mold.route("/get_resource_pool_dropdown_list", methods=["POST"])
@readonly_permission.require(http_exception=403)
def get_resource_pool_dropdown_list():
    try:
        data = request.get_json()
        pool_type_list = data.get('poolTypeList')
        res_dropdown_list = {}
        for pool_type in pool_type_list:
            if pool_type == 'asn':
                asn_pool_data_list = resource_pool_asn_db.query_resource_pool_data_list()
                res_dropdown_list['asn'] = [{
                    'id': asn_pool_data.id,
                    'name': asn_pool_data.name,
                } for asn_pool_data in asn_pool_data_list]
            # todo add other pool type
            elif pool_type == 'ipv4':
                res_dropdown_list['ipv4'] = []
            elif pool_type == 'area':
                res_dropdown_list['area'] = []
        return jsonify({'status': 200, 'data': res_dropdown_list})
    except Exception as e:
        LOG.error(f"get_resource_pool_data error: {e}")
        return jsonify({'status': 500, 'info': 'Failed to get resource pool data'})


# area start
@resource_pool_blueprint_mold.route("/resource_pool_area_table_data", methods=["POST"])
@readonly_permission.require(http_exception=403)
def resource_pool_area_table_data():
    try:
        return resource_pool_area_db.query_all_resource_pool()
    except Exception as e:
        LOG.error(f"resource_pool_area_table_data error: {e}")
        return jsonify({'status': 500, 'info': 'Failed to get resource pool data'})


@resource_pool_blueprint_mold.route("/delete_area_pool", methods=["POST"])
@readonly_permission.require(http_exception=403)
def delete_pool_area_record():
    try:
        data = request.get_json()
        if resource_pool_area_db.delete_resource_pool_by_id(data["id"]):
            return jsonify({'status': 200, 'info': 'Delete resource pool successfully'})
        else:
            return jsonify({'status': 500, 'info': 'Failed to delete resource pool'})
    except Exception as e:
        LOG.error(f"delete_pool_area_record error: {e}")
        return jsonify({'status': 500, 'info': 'Failed to delete resource pool'})
    
    
@resource_pool_blueprint_mold.route("/delete_area_pool_record", methods=["POST"])
@readonly_permission.require(http_exception=403)
def delete_area_pool_n_record():
    try:
        data = request.get_json()
        pool_id = data.get('poolId')
        record_num = data.get('recordNum')
        if resource_pool_area_db.delete_first_n_used_record(pool_id, record_num):
            return jsonify({'status': 200, 'info': 'Delete first ten record successfully'})
        else:
            return jsonify({'status': 500, 'info': 'Failed to delete first ten record'})
    except Exception as e:
        LOG.error(f"delete_first_ten_record error: {e}")
        return jsonify({'status': 500, 'info': 'Failed to delete first ten record'})

@resource_pool_blueprint_mold.route("/add_area_pool", methods=["POST"])
@readonly_permission.require(http_exception=403)
def add_area_pool():
    try:
        data = request.get_json()
        name = data.get('poolName')
        ranges = data.get('ranges')
        if not name or not is_name_valid(name) or len(name) >= 64:
            return jsonify({'status': 500, 'info': 'Invalid name'})
        if resource_pool_area_db.query_resource_pool_by_name(name).first():
            return jsonify({'status': 500, 'info': 'Resource pool already exists'})
        if len(ranges) == 0:
            return jsonify({'status': 500, 'info': 'Ranges is empty'})
        if is_ranges_conflict(ranges):
            return jsonify({'status': 500, 'info': 'Ranges conflict'})
        if resource_pool_area_db.add_resource_pool(name, ranges):
            return jsonify({'status': 200, 'info': 'Add resource pool successfully'})
        else:
            return jsonify({'status': 500, 'info': 'Failed to add resource pool'})
    except Exception as e:
        LOG.error(f"add_area_pool error: {e}")
        return jsonify({'status': 500, 'info': 'Failed to add resource pool'})


@resource_pool_blueprint_mold.route("/edit_area_pool", methods=["POST"])
@readonly_permission.require(http_exception=403)
def edit_area_pool():
    try:
        data = request.get_json()
        name = data.get('poolName')
        ranges = data.get('ranges')
        pool_id = data.get('poolId')
        if not resource_pool_area_db.query_resource_pool_by_id(pool_id).first():
            return jsonify({'status': 500, 'info': 'Resource pool not found'})
        if not name or not is_name_valid(name) or len(name) >= 64:
            return jsonify({'status': 500, 'info': 'Invalid name'})
        add_ranges = ranges.get('add', [])
        modify_ranges = ranges.get('modify', [])
        all_ranges = ranges.get('all', [])
        if is_ranges_conflict(all_ranges):
            return jsonify({'status': 500, 'info': 'Ranges conflict'})
        if resource_pool_area_db.edit_resource_pool(pool_id, name, ranges):
            return jsonify({'status': 200, 'info': 'Edit resource pool successfully'})
        else:
            return jsonify({'status': 500, 'info': 'Failed to add resource pool'})
    except Exception as e:
        LOG.error(f"add_area_pool error: {e}")
        return jsonify({'status': 500, 'info': 'Failed to add resource pool'})


@resource_pool_blueprint_mold.route("/clone_area_pool", methods=["POST"])
@readonly_permission.require(http_exception=403)
def clone_area_pool():
    try:
        data = request.get_json()
        pool_id = data.get('poolId')
        new_pool_name = data.get('poolName')
        if not new_pool_name or not is_name_valid(new_pool_name) or len(new_pool_name) >= 64:
            return jsonify({'status': 500, 'info': 'Invalid name'})
        if not resource_pool_area_db.query_resource_pool_by_id(pool_id).first():
            return jsonify({'status': 500, 'info': 'Resource pool not found'})
        if resource_pool_area_db.clone_resource_pool(pool_id, new_pool_name):
            return jsonify({'status': 200, 'info': 'Clone resource pool successfully'})
        else:
            return jsonify({'status': 500, 'info': 'Failed to add resource pool'})
    except Exception as e:
        LOG.error(f"add_area_pool error: {e}")
        return jsonify({'status': 500, 'info': 'Failed to add resource pool'})


@resource_pool_blueprint_mold.route("/generate_area_pool_record", methods=["POST"])
@readonly_permission.require(http_exception=403)
def generate_area_pool_record():
    try:
        data = request.get_json()
        pool_id = data.get('poolId')
        record_num = data.get('recordNum')
        if record_num <= 0:
            return jsonify({'status': 500, 'info': 'Invalid record number'})
        if not resource_pool_area_db.query_resource_pool_by_id(pool_id).first():
            return jsonify({'status': 500, 'info': 'Resource pool not found'})
        if resource_pool_area_db.generate_resource_from_area_pool(pool_id, record_num):
            return jsonify({'status': 200, 'info': 'Generate resource pool record successfully'})
        else:
            return jsonify({'status': 500, 'info': 'Failed to add resource pool'})
    except Exception as e:
        LOG.error(f"add_area_pool error: {e}")
        return jsonify({'status': 500, 'info': 'Failed to add resource pool'})
# area end

@resource_pool_blueprint_mold.route("/resource_pool_ip_table_data", methods=["POST"])
@readonly_permission.require(http_exception=403)
def resource_pool_ip_table_data():
    try:
        return resource_pool_ip_db.query_all_resource_pool()
    except Exception as e:
        LOG.error(f"resource_pool_ip_table_data error: {e}")
        return jsonify({'status': 500, 'info': 'Failed to get resource pool data'})


@resource_pool_blueprint_mold.route("/delete_ip_pool_record", methods=["POST"])
@readonly_permission.require(http_exception=403)
def delete_pool_ip_record():
    try:
        data = request.get_json()
        if resource_pool_ip_db.delete_resource_pool_by_id(data["id"]):
            return jsonify({'status': 200, 'info': 'Delete resource pool successfully'})
        else:
            return jsonify({'status': 500, 'info': 'Failed to delete resource pool'})
    except Exception as e:
        LOG.error(f"delete_pool_ip_record error: {e}")
        return jsonify({'status': 500, 'info': 'Failed to delete resource pool'})


@resource_pool_blueprint_mold.route("/add_ip_pool", methods=["POST"])
@readonly_permission.require(http_exception=403)
def add_ip_pool():
    try:
        data = request.get_json()
        name = data.get('poolName')
        ranges = data.get('ranges')
        if not name or not is_name_valid(name) or len(name) >= 64:
            return jsonify({'status': 500, 'info': 'Invalid name'})
        if resource_pool_ip_db.query_resource_pool_by_name(name).first():
            return jsonify({'status': 500, 'info': 'Resource pool already exists'})
        if len(ranges) == 0:
            return jsonify({'status': 500, 'info': 'Ranges is empty'})
        if is_ranges_conflict(ranges):
            return jsonify({'status': 500, 'info': 'Ranges conflict'})
        if resource_pool_ip_db.add_resource_pool(name, ranges):
            return jsonify({'status': 200, 'info': 'Add resource pool successfully'})
        else:
            return jsonify({'status': 500, 'info': 'Failed to add resource pool'})
    except Exception as e:
        LOG.error(f"add_ip_pool error: {e}")
        return jsonify({'status': 500, 'info': 'Failed to add resource pool'})


@resource_pool_blueprint_mold.route("/edit_ip_pool", methods=["POST"])
@readonly_permission.require(http_exception=403)
def edit_ip_pool():
    try:
        data = request.get_json()
        name = data.get('poolName')
        ranges = data.get('ranges')
        pool_id = data.get('poolId')
        if not resource_pool_ip_db.query_resource_pool_by_id(pool_id).first():
            return jsonify({'status': 500, 'info': 'Resource pool not found'})
        if not name or not is_name_valid(name) or len(name) >= 64:
            return jsonify({'status': 500, 'info': 'Invalid name'})
        add_ranges = ranges.get('add', [])
        modify_ranges = ranges.get('modify', [])
        if is_ranges_conflict(add_ranges + modify_ranges):
            return jsonify({'status': 500, 'info': 'Ranges conflict'})
        if resource_pool_ip_db.edit_resource_pool(pool_id, name, ranges):
            return jsonify({'status': 200, 'info': 'Edit resource pool successfully'})
        else:
            return jsonify({'status': 500, 'info': 'Failed to add resource pool'})
    except Exception as e:
        LOG.error(f"add_ip_pool error: {e}")
        return jsonify({'status': 500, 'info': 'Failed to add resource pool'})


@resource_pool_blueprint_mold.route("/clone_ip_pool", methods=["POST"])
@readonly_permission.require(http_exception=403)
def clone_ip_pool():
    try:
        data = request.get_json()
        pool_id = data.get('poolId')
        new_pool_name = data.get('poolName')
        if not new_pool_name or not is_name_valid(new_pool_name) or len(new_pool_name) >= 64:
            return jsonify({'status': 500, 'info': 'Invalid name'})
        if not resource_pool_ip_db.query_resource_pool_by_id(pool_id).first():
            return jsonify({'status': 500, 'info': 'Resource pool not found'})
        if resource_pool_ip_db.clone_resource_pool(pool_id, new_pool_name):
            return jsonify({'status': 200, 'info': 'Clone resource pool successfully'})
        else:
            return jsonify({'status': 500, 'info': 'Failed to add resource pool'})
    except Exception as e:
        LOG.error(f"add_ip_pool error: {e}")
        return jsonify({'status': 500, 'info': 'Failed to add resource pool'})


@resource_pool_blueprint_mold.route("/generate_ip_pool_record", methods=["POST"])
@readonly_permission.require(http_exception=403)
def generate_ip_pool_record():
    try:
        data = request.get_json()
        pool_id = data.get('poolId')
        record_num = data.get('recordNum')
        if record_num <= 0:
            return jsonify({'status': 500, 'info': 'Invalid record number'})
        if not resource_pool_ip_db.query_resource_pool_by_id(pool_id).first():
            return jsonify({'status': 500, 'info': 'Resource pool not found'})
        if resource_pool_ip_db.generate_resource_from_pool(pool_id, record_num):
            return jsonify({'status': 200, 'info': 'Generate resource pool record successfully'})
        else:
            return jsonify({'status': 500, 'info': 'Failed to add resource pool'})
    except Exception as e:
        LOG.error(f"add_ip_pool error: {e}")
        return jsonify({'status': 500, 'info': 'Failed to add resource pool'})

@resource_pool_blueprint_mold.route("/delete_first_ten_record_in_ip", methods=["POST"])
@readonly_permission.require(http_exception=403)
def delete_first_ten_record_in_ip():
    try:
        data = request.get_json()
        pool_id = data.get('poolId')
        record_num = data.get('recordNum')
        if resource_pool_ip_db.delete_first_n_used_record(pool_id, record_num):
            return jsonify({'status': 200, 'info': 'Delete first ten record successfully'})
        else:
            return jsonify({'status': 500, 'info': 'Failed to delete first ten record'})
    except Exception as e:
        LOG.error(f"delete_first_ten_record error: {e}")
        return jsonify({'status': 500, 'info': 'Failed to delete first ten record'})