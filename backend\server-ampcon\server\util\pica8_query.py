#!/usr/bin/python3
# -*- coding: utf-8 -*-
"""
@author: <PERSON><PERSON>
@project: Pica8AmpCon
@file: pica8_query.py
@function:
@time: 2022/3/29 11:18
"""
import requests
import logging
from geocoder.osm import OsmQuery


class Pica8Query(OsmQuery):
    def __init__(self, location, **kwargs):
        super(OsmQuery, self).__init__(location, **kwargs)
        
    def _initialize(self):
        # query URL and get valid JSON (also stored in self.json)
        json_response = self._connect()

        # catch errors
        has_error = self._catch_errors(
            json_response) if json_response else True

        # creates instances for results
        if not has_error:
            self._parse_results(json_response)
    
    def _connect(self):
        """ - Query self.url (validated cls._URL)
            - Analyse reponse and set status, errors accordingly
            - On success:

                 returns the content of the response as a JSON object
                 This object will be passed to self._parse_json_response
        """
        self.status_code = 'Unknown'
        
        try:
            # make request and get response
            self.response = response = self.rate_limited_get(
                self.url,
                params=self.params,
                headers=self.headers,
                timeout=self.timeout,
                proxies=self.proxies,
                verify=False
            )
            
            # check that response is ok
            self.status_code = response.status_code
            response.raise_for_status()
            
            # rely on json method to get non-empty well formatted JSON
            json_response = response.json()
            self.url = response.url
        except requests.exceptions.RequestException as err:
            # store real status code and error
            self.error = u'ERROR - {}'.format(str(err))
            return False
        # return response within its JSON format
        return json_response


if __name__ == '__main__':
    logging.basicConfig(level=logging.INFO)
    pp = Pica8Query("Ottawa, Ontario", proxies={})
    print(pp.lng, pp.lat, pp.current_result)
