import Hierarchy from "@antv/hierarchy";
import {CircularLayout, GridLayout, DagreLayout} from "@antv/layout";
import {Point, Shape} from "@antv/x6";
import cloneDeep from "lodash/cloneDeep";

function getRandomInt(min, max) {
    return Math.floor(Math.random() * (max - min + 1)) + min;
}

function findCycleEdges(nodes, edges) {
    const fakeRootId = -1000;
    const nodeMap = new Map(nodes.map(node => [node.id, node]));
    const visited = new Set();
    const recStack = new Set();
    const cycleEdges = [];

    function dfs(nodeId, parentId) {
        if (!nodeMap.has(nodeId)) return false;
        if (recStack.has(nodeId)) return true;
        if (visited.has(nodeId)) return false;

        visited.add(nodeId);
        recStack.add(nodeId);

        const neighbors = edges
            .filter(edge => edge.source === nodeId || edge.target === nodeId)
            .map(edge => (edge.source === nodeId ? edge.target : edge.source));

        for (const neighbor of neighbors) {
            if (neighbor !== parentId && dfs(neighbor, nodeId)) {
                cycleEdges.push({source: nodeId, target: neighbor});
                return true;
            }
        }

        recStack.delete(nodeId);
        return false;
    }

    for (const node of nodes) {
        if (!visited.has(node.id) && dfs(node.id, null)) {
            const cycleEdgesWithoutFakeRoot = cycleEdges.filter(
                edge => edge.source !== fakeRootId && edge.target !== fakeRootId
            );
            if (cycleEdgesWithoutFakeRoot.length === 0) {
                return cycleEdges;
            }
            return cycleEdgesWithoutFakeRoot;
        }
    }

    return [];
}

function treeDataFormat(nodes, edges) {
    const nodesTemp = cloneDeep(nodes);
    let edgesTemp = cloneDeep(edges);
    const removedEdges = [];
    while (true) {
        const cycleEdges = findCycleEdges(nodesTemp, edgesTemp);
        if (cycleEdges.length === 0) {
            break;
        }
        const toBeRemovedCycleEdge = cycleEdges[getRandomInt(1, 100) % cycleEdges.length];
        edgesTemp = edgesTemp.filter(
            edge =>
                !(
                    (edge.source === toBeRemovedCycleEdge.source && edge.target === toBeRemovedCycleEdge.target) ||
                    (edge.source === toBeRemovedCycleEdge.target && edge.target === toBeRemovedCycleEdge.source)
                )
        );
        removedEdges.push(toBeRemovedCycleEdge);
    }
    return [nodesTemp, edgesTemp, removedEdges];
}

// Function to build tree structure from nodes and edges
function buildTree(nodes, edges, rootIdList) {
    const fakeRootId = -1000;
    nodes.push({id: fakeRootId, label: "fake root"});
    rootIdList.forEach(rootId => {
        const node = nodes.find(node => node.id === rootId);
        if (node) {
            edges.push({source: -1000, target: node.id});
        }
    });
    edges.every(edge => {
        if (edge.source > edge.target) {
            const temp = edge.source;
            edge.source = edge.target;
            edge.target = temp;
        }
    });
    const [nodesTemp, edgesTemp, removedEdges] = treeDataFormat(nodes, edges);
    // Create a map for easy lookup of nodes by their id
    const nodeMap = new Map(nodesTemp.map(node => [node.id, node]));

    // Create a map to store children for each node
    const childrenMap = new Map();

    // Initialize children maps for undirected edges
    for (const edge of edgesTemp) {
        if (!childrenMap.has(edge.source)) {
            childrenMap.set(edge.source, []);
        }
        if (!childrenMap.has(edge.target)) {
            childrenMap.set(edge.target, []);
        }
        childrenMap.get(edge.source).push(edge.target);
        childrenMap.get(edge.target).push(edge.source);
    }

    // Recursive function to build the tree and track depth
    function buildSubTree(nodeId, parentId = null, layer = 0, visited = new Set()) {
        if (visited.has(nodeId)) return null;
        visited.add(nodeId);
        const node = nodeMap.get(nodeId);
        if (!node) return null;

        const children = (childrenMap.get(nodeId) || []).filter(childId => childId !== parentId);
        const childNodes = children
            .map(childId => buildSubTree(childId, nodeId, layer + 1, visited))
            .filter(child => child !== null);

        return {
            id: node.id,
            label: node.label,
            layer,
            children: childNodes
        };
    }

    // Start building the tree from the rootId
    return [buildSubTree(fakeRootId), removedEdges];
}

export const hierarchyLayoutDataTraverse = (nodes, edges, rootId) => {
    const [data, removedEdges] = buildTree(nodes, edges, rootId);
    const result = Hierarchy.compactBox(data, {
        direction: "TB",
        getHeight: () => 80,
        getWidth: () => 120,
        getVGap: () => 80,
        getHGap: () => 1,
        getSide: () => {
            return "bottom";
        }
    });
    const model = {nodes: [], edges: []};
    const traverse = data => {
        if (data) {
            model.nodes?.push({
                id: `${data.id}`,
                label: `${data.data.label}`,
                x: data.x + 1050,
                y: data.y + 350,
                shape: "custom-node",
                layer: data.data.layer,
                attrs: {
                    body: {
                        fill: "#5F95FF",
                        stroke: "transparent"
                    }
                }
            });
        }
        if (data.children) {
            data.children.forEach(item => {
                model.edges?.push({
                    source: `${data.id}`,
                    target: `${item.id}`,
                    attrs: {
                        line: {
                            stroke: "#A2B1C3",
                            strokeWidth: 1,
                            targetMarker: null
                        }
                    }
                });
                traverse(item);
            });
        }
    };
    traverse(result);
    return model;
};

export const girdLayoutDataTraverse = (nodes, edges, width, height) => {
    const gridLayout = new GridLayout({
        type: "grid",
        width: width - 160,
        height,
        sortBy: "label",
        rows: Math.ceil(Math.sqrt(nodes.length)),
        cols: Math.max(2, Math.ceil(Math.sqrt(nodes.length)))
    });
    return gridLayout.layout({nodes, edges});
};

export const circularLayoutDataTraverse = (nodes, edges, width, height) => {
    if (nodes.length === 1) {
        return false;
    }
    const circularLayout = new CircularLayout({
        type: "circular",
        center: [(width - 160) / 2, height / 2],
        radius: height / 2
    });

    return circularLayout.layout({nodes, edges});
};

export const ellipticalLayoutDataTraverse = (nodes, edges, width, height) => {
    const cx = width / 2;
    const cy = height / 2;

    const rx = (width / 2 - 80) * 0.8;
    const ry = (height / 2 - 80) * 0.8;
    const ratio = rx / ry;
    const center = new Point(cx, cy);
    const start = new Point(cx, cy - ry);
    const stepAngle = 360 / nodes.length;
    nodes.forEach((node, index) => {
        const angle = stepAngle * index;
        const p = start.clone().rotate(-angle, center).scale(ratio, 1, center).round();
        node.x = p.x;
        node.y = p.y;
    });

    return {nodes, edges};
};

export const formatDate = date => {
    if (!date) {
        return null;
    }
    const yyyy = date.getFullYear();
    const mm = String(date.getMonth() + 1).padStart(2, "0");
    const dd = String(date.getDate()).padStart(2, "0");
    const hh = String(date.getHours()).padStart(2, "0");
    const min = String(date.getMinutes()).padStart(2, "0");
    return `${yyyy}-${mm}-${dd} ${hh}:${min}`;
};

export const convertToUTCString = dateString => {
    if (!dateString) {
        return null;
    }
    const date = new Date(dateString);
    const utcYear = date.getUTCFullYear();
    const utcMonth = String(date.getUTCMonth() + 1).padStart(2, "0");
    const utcDay = String(date.getUTCDate()).padStart(2, "0");
    const utcHours = String(date.getUTCHours()).padStart(2, "0");
    const utcMinutes = String(date.getUTCMinutes()).padStart(2, "0");
    const utcSeconds = String(date.getUTCSeconds()).padStart(2, "0");
    return `${utcYear}-${utcMonth}-${utcDay} ${utcHours}:${utcMinutes}:${utcSeconds}`;
};

export const calculateVertices = (sourcePoint, targetPoint) => {
    if (sourcePoint === undefined || targetPoint === undefined) {
        return [];
    }
    let offsetMax = 30;
    if (Math.abs(sourcePoint.y - targetPoint.y) <= 50) {
        return [];
    }
    if (Math.abs(sourcePoint.y - targetPoint.y) <= 150) {
        offsetMax = (30 * Math.abs(sourcePoint.y - targetPoint.y)) / 150;
    }

    const midNode = {
        x: (targetPoint.x + sourcePoint.x) / 2,
        y: (targetPoint.y + sourcePoint.y) / 2
    };

    let offset = (targetPoint.y - sourcePoint.y) * 0.45;
    if (offset > offsetMax) {
        offset = offsetMax;
    } else if (offset < -1 * offsetMax) {
        offset = -1 * offsetMax;
    }

    if (Math.abs(sourcePoint.y - targetPoint.y) >= 300) {
        offset = (offset * Math.abs(sourcePoint.y - targetPoint.y)) / 300;
    }

    const vertices = [
        {x: targetPoint.x + (sourcePoint.x - targetPoint.x) * 0.2 + 20, y: midNode.y + offset},
        {x: sourcePoint.x - (sourcePoint.x - targetPoint.x) * 0.2 + 20, y: midNode.y - offset}
    ];

    const sourceDistance = Math.sqrt((vertices[0].x - sourcePoint.x) ** 2 + (vertices[0].y - sourcePoint.y) ** 2);
    const targetDistance = Math.sqrt((vertices[0].x - targetPoint.x) ** 2 + (vertices[0].y - targetPoint.y) ** 2);
    if (sourceDistance < targetDistance) {
        return [
            vertices[0],
            {x: midNode.x + 20, y: midNode.y}, // 1/3平缓
            vertices[1]
        ];
    }
    return [
        vertices[1],
        {x: midNode.x + 20, y: midNode.y}, // 1/3平缓
        vertices[0]
    ];
};

export const calculateSwitchViewTopoVertices = (sourcePoint, targetPoint) => {
    const midNode = {
        x: (targetPoint.x + sourcePoint.x) / 2,
        y: (targetPoint.y + sourcePoint.y) / 2
    };
    let offset = (targetPoint.x - sourcePoint.x) * 0.45;
    offset = (offset * Math.abs(sourcePoint.x - targetPoint.x)) / 300;

    const vertices = [
        {x: midNode.x + offset, y: targetPoint.y - (sourcePoint.x - targetPoint.x) * 0.2},
        {x: midNode.x - offset, y: sourcePoint.y + (sourcePoint.x - targetPoint.x) * 0.2}
    ];

    const sourceDistance = Math.sqrt((vertices[0].x - sourcePoint.x) ** 2 + (vertices[0].y - sourcePoint.y) ** 2);
    const targetDistance = Math.sqrt((vertices[0].x - targetPoint.x) ** 2 + (vertices[0].y - targetPoint.y) ** 2);

    if (sourceDistance < targetDistance) {
        return [
            vertices[0],
            {x: midNode.x, y: midNode.y}, // 1/3平缓
            vertices[1]
        ];
    }
    return [
        vertices[1],
        {x: midNode.x, y: midNode.y}, // 1/3平缓
        vertices[0]
    ];
};

export const unitTopoLayout = (graph, point, UnitInfo) => {
    const {leafNodes, accessNodes} = UnitInfo;

    const dagreLayout = new DagreLayout({
        type: "dagre",
        rankdir: "TB",
        align: "UL",
        begin: [point.x, point.y],
        ranksep: 50,
        nodesep: 50,
        controlPoints: true
    });
    if (leafNodes === undefined || accessNodes === undefined) {
        return false;
    }

    const leafNodes_1 = leafNodes.map(node => {
        if (node.name === "") return;
        const uniqueId = `id_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        return {
            id: uniqueId,
            label: node.name,
            name: node.name,
            width: 120,
            height: 30,
            type: "leaf",
            shape: "leaf-node"
        };
    });
    const accessNodes_1 = accessNodes.map(node => {
        if (node.name === "") {
            return;
        }
        const uniqueId = `id_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        return {
            id: uniqueId,
            label: node.name,
            name: node.name,
            width: 120,
            height: 30,
            type: "access",
            shape: "access-node"
        };
    });
    const edges = accessNodes.map(node => {
        if (node.name !== "" && node.leaf !== "") {
            const targetNode = accessNodes_1.find(item => item?.name === node.name)?.id;
            const sourceNode = leafNodes_1.find(item => item?.name === node.leaf)?.id;
            return {source: sourceNode, target: targetNode};
        }
        return;
    });

    const data = {
        nodes: leafNodes_1.filter(item => item !== undefined).concat(accessNodes_1.filter(item => item !== undefined)),
        edges: edges.filter(item => item !== undefined)
    };

    if (data.nodes.length === 0) {
        return false;
    }
    dagreLayout.layout(data);

    data.nodes.forEach(node => {
        graph.addNode(node);
    });
    data.edges.forEach(edge => {
        graph.addEdge(edge);
    });
    return true;
};
