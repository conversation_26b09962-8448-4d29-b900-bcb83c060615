export const isRangeConflict = (ranges, i, isStart = true) => {
    const range = ranges[i];
    if (isStart && !range?.startASN) {
        return false;
    }
    if (!isStart && !range?.endASN) {
        return false;
    }
    for (let j = 0; j < ranges.length; j++) {
        if (i === j) continue; // �����������Ƚ�
        const otherRange = ranges[j];
        if (isStart && range.startASN >= otherRange.startASN && range.startASN <= otherRange.endASN) {
            return true;
        }
        if (!isStart && range.endASN <= otherRange.endASN && range.endASN >= otherRange.startASN) {
            return true;
        }
        if (range.startASN <= otherRange.startASN && range.endASN >= otherRange.endASN) {
            return true;
        }
    }
    return false;
};

export const isRangeListConflict = ranges => {
    for (let i = 0; i < ranges.length; i++) {
        const range = ranges[i];
        if (!range?.startASN || !range?.endASN) {
            return true;
        }
        if (range.startASN >= range.endASN) {
            return true;
        }
        if (isRangeConflict(ranges, i, true) || isRangeConflict(ranges, i, false)) {
            return true;
        }
    }
    return false;
};

export const rangeInputValidator = (formRef, value, index, isStart = true) => {
    if (value === 0) {
        return Promise.reject(new Error("Value must be between 1 and 4294967295"));
    }
    if (!value) {
        return Promise.reject(new Error("Can not be empty"));
    }
    if (value < 1 || value > 4294967295) {
        return Promise.reject(new Error("Value must be between 1 and 4294967295"));
    }
    const startInputValue = formRef.getFieldsValue().fields[index]?.startASN;
    const endInputValue = formRef.getFieldsValue().fields[index]?.endASN;
    if (startInputValue >= endInputValue) {
        return Promise.reject(
            new Error(
                isStart ? "Start value must be less than end value" : "End value must be greater than start value"
            )
        );
    }
    const filterFields = formRef.getFieldsValue().fields.filter(item => item !== undefined);
    if (
        (isStart && isRangeConflict(filterFields, index, true)) ||
        (!isStart && isRangeConflict(filterFields, index, false))
    ) {
        return Promise.reject(new Error("Conflict with other ranges"));
    }
    return Promise.resolve();
};

export const rangeInputValidatorWithDefaultValueCheck = (formRef, formData, value, index, isStart = true) => {
    if (value === 0) {
        return Promise.reject(new Error("Value must be between 1 and 4294967295"));
    }
    if (!value) {
        return Promise.reject(new Error("Can not be empty"));
    }
    if (value < 1 || value > 4294967295) {
        return Promise.reject(new Error("Value must be between 1 and 4294967295"));
    }
    const startDefaultValue = formData[index] ? formData[index].startASN : "";
    const endDefaultValue = formData[index] ? formData[index].endASN : "";
    const startInputValue = formRef.getFieldsValue().fields[index]?.startASN;
    const endInputValue = formRef.getFieldsValue().fields[index]?.endASN;
    if (startInputValue >= endInputValue) {
        return Promise.reject(
            new Error(
                isStart ? "Start value must be less than end value" : "End value must be greater than start value"
            )
        );
    }
    if (
        (isStart &&
            formData[index].status &&
            startInputValue > startDefaultValue &&
            startInputValue < endDefaultValue) ||
        (!isStart && formData[index].status && endInputValue < endDefaultValue && endInputValue > startDefaultValue)
    ) {
        return Promise.reject(
            new Error(`Existing range cannot be reduced (${startDefaultValue} - ${endDefaultValue})`)
        );
    }
    const filterFields = formRef.getFieldsValue().fields.filter(item => item !== undefined);
    if (
        (isStart && isRangeConflict(filterFields, index, true)) ||
        (!isStart && isRangeConflict(filterFields, index, false))
    ) {
        return Promise.reject(new Error("Conflict with other ranges"));
    }
    return Promise.resolve();
};
