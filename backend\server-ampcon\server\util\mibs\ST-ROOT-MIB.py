# SNMP MIB module (ST-ROOT-MIB) expressed in pysnmp data model.
#
# This Python module is designed to be imported and executed by the
# pysnmp library.
#
# See https://www.pysnmp.com/pysnmp for further information.
#
# Notes
# -----
# ASN.1 source file://./ST-ROOT-MIB.mib
# Produced by pysmi-1.5.11 at Tue Apr 22 03:03:06 2025
# On host pica8 platform Linux version 5.15.0-122-generic by user root
# Using Python version 3.11.10 (main, Sep  7 2024, 18:35:41) [GCC 11.4.0]

if 'mibBuilder' not in globals():
    import sys

    sys.stderr.write(__doc__)
    sys.exit(1)

# Import base ASN.1 objects even if this MIB does not use it

(Integer,
 OctetString,
 ObjectIdentifier) = mibBuilder.importSymbols(
    "ASN1",
    "Integer",
    "OctetString",
    "ObjectIdentifier")

(NamedValues,) = mibBuilder.importSymbols(
    "ASN1-ENUMERATION",
    "NamedValues")
(ConstraintsIntersection,
 ConstraintsUnion,
 SingleValueConstraint,
 ValueRangeConstraint,
 ValueSizeConstraint) = mibBuilder.importSymbols(
    "ASN1-REFINEMENT",
    "ConstraintsIntersection",
    "ConstraintsUnion",
    "SingleValueConstraint",
    "ValueRangeConstraint",
    "ValueSizeConstraint")

# Import SMI symbols from the MIBs this MIB depends on

(ModuleCompliance,
 NotificationGroup) = mibBuilder.importSymbols(
    "SNMPv2-CONF",
    "ModuleCompliance",
    "NotificationGroup")

(Bits,
 Counter32,
 Counter64,
 Gauge32,
 Integer32,
 IpAddress,
 ModuleIdentity,
 MibIdentifier,
 NotificationType,
 ObjectIdentity,
 MibScalar,
 MibTable,
 MibTableRow,
 MibTableColumn,
 TimeTicks,
 Unsigned32,
 enterprises,
 iso) = mibBuilder.importSymbols(
    "SNMPv2-SMI",
    "Bits",
    "Counter32",
    "Counter64",
    "Gauge32",
    "Integer32",
    "IpAddress",
    "ModuleIdentity",
    "MibIdentifier",
    "NotificationType",
    "ObjectIdentity",
    "MibScalar",
    "MibTable",
    "MibTableRow",
    "MibTableColumn",
    "TimeTicks",
    "Unsigned32",
    "enterprises",
    "iso")

(DisplayString,
 PhysAddress,
 TextualConvention) = mibBuilder.importSymbols(
    "SNMPv2-TC",
    "DisplayString",
    "PhysAddress",
    "TextualConvention")


# MODULE-IDENTITY

stTransport = ModuleIdentity(
    (1, 3, 6, 1, 4, 1, 52642, 1, 1)
)
if mibBuilder.loadTexts:
    stTransport.setLastUpdated("201605120945Z")
if mibBuilder.loadTexts:
    stTransport.setDescription("Root OID for proprietary MIBs of PON and OTN NEs.")


# Types definitions


# TEXTUAL-CONVENTIONS



# MIB Managed Objects in the order of their OIDs

_EnterpriseRoot_ObjectIdentity = ObjectIdentity
enterpriseRoot = _EnterpriseRoot_ObjectIdentity(
    (1, 3, 6, 1, 4, 1, 52642)
)
_EnterpriseProducts_ObjectIdentity = ObjectIdentity
enterpriseProducts = _EnterpriseProducts_ObjectIdentity(
    (1, 3, 6, 1, 4, 1, 52642, 1)
)

# Managed Objects groups


# Notification objects


# Notifications groups


# Agent capabilities


# Module compliance


# Export all MIB objects to the MIB builder

mibBuilder.exportSymbols(
    "ST-ROOT-MIB",
    **{"enterpriseRoot": enterpriseRoot,
       "enterpriseProducts": enterpriseProducts,
       "stTransport": stTransport}
)
