.tile {
    background-color: #ffffff;
    border-radius: 3px;
    padding-left: 20px;
    padding-right: 20px;
    -webkit-transition: all 0.3s ease-in-out;
    -o-transition: all 0.3s ease-in-out;
    transition: all 0.3s ease-in-out;
}

.closeIcon {
    fill: #A2ACB2;

    &:hover {
        fill: #14c9bb;
    }
}



.treeButton {
    width: 24px;
    height: 30px;
    background: #ffffff;
    border-radius: 2px 2px 2px 2px;
    border: 1px solid #dcdcdc;
    color: #000000;
    &:hover {
        background-color: #d3d3d3; // Light gray color
    }
    &:disabled {
        background: #f4f5f7;
        border: 1px solid #dadce1;

        &:hover {
            background: #f4f5f7;
            border: 1px solid #dadce1;
        }
    }
}

.UpStyle {
    background: rgba(43, 193, 116, 0.1) !important;
    border-radius: 2px !important;
    border: 1px solid #2bc174 !important;
    color: #2bc174 !important;
    font-size: 14px !important;
}

.DownStyle {
    background: rgba(245, 63, 63, 0.1) !important;
    border-radius: 2px !important;
    border: 1px solid #f53f3f !important;
    color: #f53f3f !important;
    font-size: 14px !important;
}

.treeContainer {
    *::-webkit-scrollbar {
        width: 5px;
        height: 5px
    }

    *::-webkit-scrollbar-track {
        background: transparent
    }

    *::-webkit-scrollbar-corner {
        background-color: transparent
    }

    *::-webkit-scrollbar-thumb {
        border-radius: 6px;
        background: #e0e6ed;
    }

    *::-webkit-scrollbar-thumb:hover {
        background: #C7CFD8;
    }
}
.treeNoScrollbar {
    *::-webkit-scrollbar {
        display: none;
    }
}

.actionLink a {
    color: #14C9BB;
}

.actionLink a:hover {
    color: #34DCCF;
}