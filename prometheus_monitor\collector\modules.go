package collector

import (
	"fmt"
	"gnmi_exporter/config"
	"log"
	"path/filepath"
	"strconv"
	"time"

	"github.com/openconfig/gnmi/proto/gnmi"
)

// openconfig_platform:components_component_openconfig_platform_transceiver
type OpenconfigPlatform struct {
	powerMap map[string]transceiverState
	ampconAddress     string
	targetName        string
}

type transceiverState struct {
	inputPower float64
	outputPower float64
	transportType string
	currentPowerDiff float64
	prevPowerDiff float64
}

type PowerThreshold struct {
    inputPowerMin  float64
    inputPowerMax  float64
    outputPowerMin float64
    outputPowerMax float64
}

// 定义阈值映射表
var powerThresholds = map[string]PowerThreshold{
    // CFP系列
    "openconfig-transport-types:CFP": {
		outputPowerMin: -8, outputPowerMax: 2,
        inputPowerMin: -10, inputPowerMax: 0,
    },
    "openconfig-transport-types:CFP2": {
		outputPowerMin: -8, outputPowerMax: 2,
        inputPowerMin: -10, inputPowerMax: 0,
    },
    "openconfig-transport-types:CFP2_ACO": {
		outputPowerMin: -5, outputPowerMax: 5,
        inputPowerMin: -10, inputPowerMax: 0,
    },
    "openconfig-transport-types:CFP4": {
		outputPowerMin: -8, outputPowerMax: 2,
        inputPowerMin: -10, inputPowerMax: 0,
    },
    
    // QSFP系列
    "openconfig-transport-types:QSFP": {
		outputPowerMin: -8, outputPowerMax: 2,
        inputPowerMin: -10, inputPowerMax: 0,
    },
    "openconfig-transport-types:QSFP28": {
		outputPowerMin: -8, outputPowerMax: 2,
        inputPowerMin: -10, inputPowerMax: 0,
    },
    "openconfig-transport-types:QSFP28_DD": {
		outputPowerMin: -8, outputPowerMax: 2,
        inputPowerMin: -10, inputPowerMax: 0,
    },
    "openconfig-transport-types:QSFP56": {
		outputPowerMin: -8, outputPowerMax: 2,
        inputPowerMin: -10, inputPowerMax: 0,
    },
    "openconfig-transport-types:QSFP56_DD": {
		outputPowerMin: -8, outputPowerMax: 2,
        inputPowerMin: -10, inputPowerMax: 0,
    },
    "openconfig-transport-types:QSFP56_DD_TYPE1": {
		outputPowerMin: -8, outputPowerMax: 2,
        inputPowerMin: -10, inputPowerMax: 0,
    },
    "openconfig-transport-types:QSFP56_DD_TYPE2": {
		outputPowerMin: -8, outputPowerMax: 2,
        inputPowerMin: -10, inputPowerMax: 0,
    },
	"openconfig-transport-types:QSFP_PLUS": {
		outputPowerMin: -8, outputPowerMax: 2,
        inputPowerMin: -10, inputPowerMax: 0,
    },
    
    // SFP系列
    "openconfig-transport-types:SFP": {
		outputPowerMin: -9, outputPowerMax: -1,
        inputPowerMin: -17, inputPowerMax: -1,
    },
    "openconfig-transport-types:SFP_PLUS": {
		outputPowerMin: -9, outputPowerMax: -1,
        inputPowerMin: -17, inputPowerMax: -1,
    },
    "openconfig-transport-types:SFP28": {
		outputPowerMin: -8, outputPowerMax: 4,
        inputPowerMin: -12, inputPowerMax: -1,
    },
    "openconfig-transport-types:SFP56": {
		outputPowerMin: -8, outputPowerMax: 4,
        inputPowerMin: -12, inputPowerMax: -1,
    },
    "openconfig-transport-types:SFP_DD": {
		outputPowerMin: -8, outputPowerMax: 4,
        inputPowerMin: -12, inputPowerMax: -1,
    },
    
    // 其他类型
    "openconfig-transport-types:CPAK": {
		outputPowerMin: -8, outputPowerMax: 2,
        inputPowerMin: -10, inputPowerMax: 0,
    },
    "openconfig-transport-types:CSFP": {
		outputPowerMin: -9, outputPowerMax: -1,
        inputPowerMin: -17, inputPowerMax: -1,
    },
    "openconfig-transport-types:DSFP": {
		outputPowerMin: -8, outputPowerMax: 4,
        inputPowerMin: -12, inputPowerMax: -1,
    },
    "openconfig-transport-types:XFP": {
		outputPowerMin: -8, outputPowerMax: 2,
        inputPowerMin: -10, inputPowerMax: 0,
    },
    "openconfig-transport-types:X2": {
		outputPowerMin: -8, outputPowerMax: 2,
        inputPowerMin: -10, inputPowerMax: 0,
    },
    "openconfig-transport-types:OSFP": {
		outputPowerMin: -8, outputPowerMax: 2,
        inputPowerMin: -10, inputPowerMax: 0,
    },
}


func NewOpenconfigPlatform(targetName string) Hook {
	var ampconAddress string
	config, err := config.LoadFile([]string{"gnmi.yaml"})
	if err != nil {
		log.Fatalln("load config failed")
		ampconAddress = "nginx-service:443"
	} else {
		ampconAddress = config.GlobalConfig.AmpconAddress
	}

	return OpenconfigPlatform{
		powerMap: map[string]transceiverState{},
		ampconAddress:     ampconAddress,
		targetName:        targetName,
	}
}

func (i OpenconfigPlatform) AfterSubscribe(result interface{}) {
	var alerts []Alert
	for _, update := range result.(*gnmi.Notification).Update {
		baseMetric := updateToBaseMetric(time.Now(), update)

		name := filepath.Base(baseMetric.Name)
		parentDir := filepath.Dir(baseMetric.Name)
		parent := filepath.Base(parentDir)
		// if parent != "state" {
		// 	continue
		// }
		// fmt.Println(name)
		// fmt.Println(name, parentDir, parent, baseMetric.Name)
		switch name {
		case "instant":
			if parent == "fsconfig-platform-transceiver-extensions:laser-temperature" {
				interfaceName := baseMetric.Label["interface_name"]
				laserTemperature := baseMetric.Value
				// fmt.Println(parentDir, interfaceName, laserTemperature)
				val, err := strconv.ParseFloat(laserTemperature, 64)
				if err != nil {
					log.Printf("Error parsing value %s: %v", laserTemperature, err)
					continue
				}
			
				if val < 0 || val > 90 {
					alert := i.generateAlert(interfaceName, "warn", "found interface: %s on switch %s laser_temperature change to: %s", val)
					log.Printf("found interface: %s on switch %s laser_temperature change to: %s", interfaceName, i.targetName, laserTemperature)
					alerts = append(alerts, alert)
				}
			}
			if parent == "output-power" {
				interfaceName := baseMetric.Label["interface_name"]
				outputPower := baseMetric.Value
				// fmt.Println(interfaceName, outputPower)
				i.updateTransceiverState(i.powerMap, interfaceName, outputPower, "output_power")
			}

			if parent == "input-power" {
				interfaceName := baseMetric.Label["interface_name"]
				inputPower := baseMetric.Value
				// fmt.Println(interfaceName, inputPower)
				i.updateTransceiverState(i.powerMap, interfaceName, inputPower, "input_power")
			}

			// openconfig_platform:components_component_openconfig_platform_transceiver:transceiver_state_fsconfig_platform_transceiver_extensions:laser_temperature
			// interfaceName := baseMetric.Label["interface_name"]
			// if isAlert {
			// 	alerts = append(alerts, alert)
			// }
		case "form-factor":
			interfaceName := baseMetric.Label["interface_name"]
			formFactor := baseMetric.Value
			// fmt.Println(parentDir, interfaceName, formFactor)
			i.updateTransceiverState(i.powerMap, interfaceName, formFactor, "form_factor")
		default:
		}
	}
	
	// fmt.Println(i.powerMap)
	// 遍历powerMap检查功率差值变化
	for interfaceName, transceiverState := range i.powerMap {
		// 计算新的功率差值
		powerDiff := Decimal(transceiverState.outputPower-transceiverState.inputPower)
		
		transceiverState.prevPowerDiff = transceiverState.currentPowerDiff
		transceiverState.currentPowerDiff = powerDiff
		i.powerMap[interfaceName] = transceiverState
		
		// 更新功率差值状态
		if transceiverState.currentPowerDiff != transceiverState.prevPowerDiff && transceiverState.prevPowerDiff != 0 {
			// 生成告警
			alert := i.generateOutputInputPowerDiffAlert(interfaceName, "info", "found interface: %s on switch %s output_power-input_power changed from %.2f to %.2f",
				transceiverState.prevPowerDiff, transceiverState.currentPowerDiff)
			alerts = append(alerts, alert)
		}

		var inputPowerAlert, outputPowerAlert bool
		threshold, ok := powerThresholds[transceiverState.transportType]
		if ok {
			if transceiverState.outputPower < threshold.outputPowerMin || transceiverState.outputPower > threshold.outputPowerMax {
				outputPowerAlert = true
			}
			if transceiverState.inputPower < threshold.inputPowerMin || transceiverState.inputPower > threshold.inputPowerMax {
				inputPowerAlert = true
			}
		}
		if outputPowerAlert {
			alert := i.generatePowerRangeAlert(interfaceName, "warn", "found interface: %s transceiver type: %s on switch %s output_power : %.2f out of range [%.2f, %.2f]",
				transceiverState.transportType, transceiverState.outputPower, threshold.outputPowerMin, threshold.outputPowerMax)	
			alerts = append(alerts, alert)
		}
		if inputPowerAlert {
			alert := i.generatePowerRangeAlert(interfaceName, "warn", "found interface: %s transceiver type: %s on switch %s input_power : %.2f out of range [%.2f, %.2f]",
				transceiverState.transportType, transceiverState.inputPower, threshold.inputPowerMin, threshold.inputPowerMax)	
			alerts = append(alerts, alert)
		}
	}

	if len(alerts) > 0 {
		err := sendAlertLog("https://"+i.ampconAddress+"/ampcon/monitor/alert_log", alerts)
		if err != nil {
			log.Println("Error sending alert:", err)
		}
	}
}


func (i OpenconfigPlatform) generateAlert(interfaceName, severity, description string, value float64) Alert {
	alert := Alert{
		Labels: map[string]string{
			"target":   i.targetName,
			"severity": severity,
		},
		Annotations: map[string]string{
			"description": fmt.Sprintf(description, interfaceName, i.targetName, value),
		},
	}
	// log.Printf("generate alert: %v", alert)
	log.Println(alert.Annotations["description"])
	return alert
}

func (i OpenconfigPlatform) generateOutputInputPowerDiffAlert(interfaceName, severity, description string, prevPowerDiff, currentPowerDiff float64) Alert {
    alert := Alert{
        Labels: map[string]string{
            "target":   i.targetName,
            "severity": severity,
        },
        Annotations: map[string]string{
            // 使用 %.2f 格式化浮点数
            "description": fmt.Sprintf(description,
                interfaceName, i.targetName, 
                Decimal(prevPowerDiff), 
                Decimal(currentPowerDiff)),
        },
    }
    log.Printf("found interface: %s on switch %s output_power-input_power changed from %.2f to %.2f",
        interfaceName, i.targetName, prevPowerDiff, currentPowerDiff)
    return alert
}

func (i OpenconfigPlatform) generatePowerRangeAlert(interfaceName, severity, description, transportType string, value, min, max float64) Alert {
    alert := Alert{
        Labels: map[string]string{
            "target":   i.targetName,
            "severity": severity,
        },
        Annotations: map[string]string{
            // 使用 %.2f 格式化浮点数
			
            "description": fmt.Sprintf(description, interfaceName, transportType, i.targetName, 
                Decimal(value), 
                Decimal(min), 
                Decimal(max)),
        },
    }
    log.Printf(alert.Annotations["description"])
    return alert
}

func (i OpenconfigPlatform) updateTransceiverState(powerMap map[string]transceiverState, interfaceName string, value string, updateType string) {    // 1. 先获取结构体
    transceiver, exists := powerMap[interfaceName]
    if !exists {
        // 如果不存在，创建新的结构体
        transceiver = transceiverState{}
    }

    // 2. 更新结构体的属性
    switch updateType {
    case "input_power":
        val, err := strconv.ParseFloat(value, 64)
        if err != nil {
            log.Printf("Error parsing value %s to float: %v", value, err)
            return
        }
        transceiver.inputPower = Decimal(val)
    case "output_power":
        val, err := strconv.ParseFloat(value, 64)
        if err != nil {
            log.Printf("Error parsing value %s to float: %v", value, err)
            return
        }
        transceiver.outputPower = Decimal(val)
    case "form_factor":
        transceiver.transportType = value
    }

    // 3. 将更新后的结构体放回map
    powerMap[interfaceName] = transceiver

}

func Decimal(value float64) float64 {
	f, _ := strconv.ParseFloat(fmt.Sprintf("%.2f", value), 64)
	return f
}
