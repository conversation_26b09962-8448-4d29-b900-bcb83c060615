import logging
import os
from datetime import datetime
import requests
from server.constants import PICOS_V_USERNAME, PICOS_V_PASSWORD
from server.db.models import automation
from server.db.models.inventory import inven_db, Switch, Group, SystemConfig
from server.db.models.automation import An<PERSON><PERSON><PERSON>, AnsibleDevice
from server.ansible_lib.ansible_common import PicaPlayBookRunner
import json
from celery_app import my_celery_app
from celery_app.automation_task import AmpConBaseTask
from server.util.yaml_util import YAMLEditor
from server import cfg

automation_db = automation.automation_db

LOG = logging.getLogger(__name__)


def init_start_check(playbook_name, inventory_pool, username, passwd):
    if len(inventory_pool) == 0:
        LOG.error(":::No hosts is selected for playbook %s", playbook_name)
        return False
    
    playbook_entry = automation_db.get_collection(automation.Playbook, filters={'name': [playbook_name]})
    if not playbook_entry:
        LOG.error(":::Can not find the playbook %s", playbook_name)
        return False
    
    if username == '' or passwd == '':
        LOG.error(":::Can not find the username / password settings")
        return False
    
    return True


def generate_inventory_list(switch_list, group_list):
    inventory_pool = set()
    # handle switch_list
    for sw_sn in switch_list:
        inventory_pool.add(sw_sn)
    
    # handle group_list
    for group_name in group_list:
        groupQuery = inven_db.get_group_switchs(group_name)
        group_switch_sn = [i.switch_sn for i in groupQuery]
        for sw_sn in group_switch_sn:
            inventory_pool.add(sw_sn)
    
    inventory_pool = list(inventory_pool)
    return inventory_pool


def generate_job_name(playbook_name):
    return '{0}:::{1}'.format(datetime.now().strftime('%Y-%m-%d %H:%M:%S'), playbook_name)


def get_username_passwd():
    session = automation_db.get_session()
    # todo system_config
    system_config = session.query(SystemConfig).first()
    if not system_config:
        # TODO: handle no username & password settings
        return '', ''
    username = system_config.switch_op_user
    passwd = system_config.switch_op_password
    return username, passwd


def add_job_record(job_name, playbook_name, playbook_path, schedule_type, schedule_params, create_user):
    new_job = AnsibleJob()
    new_job.name = job_name
    new_job.create_user = create_user
    new_job.playbook_name = playbook_name
    new_job.playbook_path = playbook_path
    new_job.schedule_type = schedule_type
    new_job.schedule_param = json.dumps(schedule_params)
    automation_db.insert(new_job)


@my_celery_app.task(name="ansible_job_start", base=AmpConBaseTask)
def ansible_job_start(playbook_name='', playbook_path='', job_name='', schedule_type='DIRECT', schedule_params={},
                      switch_list=[], group_list=[], _vars={}, **kwargs):
    other_device_info = kwargs.get("other_device", [])
    tmp_devices = dict()
    ip_name_dict = dict()
    if other_device_info:
        for device in other_device_info:
            device_name = device.get("device_name", "")
            username = device.get("device_user", "")
            passwd = device.get("device_pwd", "")
            device_ssh_key_path = device.get("device_ssh_key_path", "")
            device_ip = device.get("device_ip", "")
            device_port = device.get("device_port", "")
            if not device_ssh_key_path:
                if f"{username}&&{passwd}" in tmp_devices:
                    tmp_devices[f"{username}&&{passwd}&&{device_port}"].append(device_ip)
                else:
                    tmp_devices[f"{username}&&{passwd}&&{device_port}"] = [device_ip]
            else:
                if f"{username}&=&{device_ssh_key_path}&=&{device_port}" in tmp_devices:
                    tmp_devices[f"{username}&=&{device_ssh_key_path}&=&{device_port}"].append(device_ip)
                else:
                    tmp_devices[f"{username}&=&{device_ssh_key_path}&=&{device_port}"] = [device_ip]
            ip_name_dict[device_ip] = device_name
        
        playbook_entry = automation_db.get_collection(automation.Playbook, filters={'name': [playbook_name]})
        if not playbook_entry:
            LOG.error(":::Can not find the playbook %s", playbook_name)
            return False
        for user_info, hosts in tmp_devices.items():
            username, passwd, device_port = "", "", ""
            runner = PicaPlayBookRunner(playbook_name=playbook_name, job_name=job_name, host_sn_dict=dict((host, ip_name_dict[host]) for host in hosts))
            if "&=&" in user_info:
                username, key_file, device_port = user_info.split("&=&")
                _vars["private_key_file"] = key_file
            else:
                username, passwd, device_port = user_info.split("&&")
            _vars["ansible_ssh_port"] = device_port
            runner.run_playbook_file(username, passwd, playbook_path, hosts, _vars, use_local=False)
    if switch_list or group_list:
        # initialization
        inventory_pool = generate_inventory_list(switch_list=switch_list, group_list=group_list)
        username, passwd = get_username_passwd()
        # check status
        if not init_start_check(playbook_name=playbook_name, inventory_pool=inventory_pool, username=username,
                                passwd=passwd):
            # TODO: handle the check status not passed
            return
        
        # host should get from database from serice table
        inventory_pool = automation_db.get_collection(Switch, filters={'sn': inventory_pool})
        hosts = []
        host_sn_dict = {}
        hosts_picos_v = []
        host_sn_dict_picos_v = {}
        host_sn_dict_all = {}
        for switch in inventory_pool:
            host_sn_dict_all.update({switch.mgt_ip: switch.sn})
            if switch.sn != 'PICOS-V':
                hosts.append(switch.mgt_ip)
                host_sn_dict.update({switch.mgt_ip: switch.sn})
            else:
                hosts_picos_v.append(switch.mgt_ip)
                host_sn_dict_picos_v.update({switch.mgt_ip: switch.sn})
        
        _vars.update({'running_tag': job_name})
        
        runner = PicaPlayBookRunner(playbook_name=playbook_name, job_name=job_name, host_sn_dict=host_sn_dict_all)
        
        if hosts:
            runner.host_sn_dict = host_sn_dict
            runner.run_playbook_file(None, None, playbook_path, hosts, _vars)
        
        if hosts_picos_v:
            runner.host_sn_dict = host_sn_dict_picos_v
            runner.run_playbook_file(PICOS_V_USERNAME, PICOS_V_PASSWORD, playbook_path, hosts_picos_v, _vars)
    
    LOG.info(":::Finish to start a task to run playbook")


@my_celery_app.task(name="deploy_node_exporter", base=AmpConBaseTask)
def deploy_node_exporter(devices):
    tmp_devices = dict()
    ip_name_dict = dict()
    new_target = []
    _vars={}
    job_name = '{0}:::{1}'.format(datetime.now().strftime('%Y-%m-%d %H:%M:%S'), "enable_monitor")
    playbook_path = "/usr/share/automation/server/monitor/node_exporter/enable_node_exporter.yml"
    target_json = "/usr/share/automation/server/monitor/settings/node_target.json"
    for device in devices:
        device_name = device.get("device_name", "")
        username = device.get("device_user", "")
        passwd = device.get("device_pwd", "")
        device_ssh_key_path = device.get("device_ssh_key_path", "")
        device_ip = device.get("device_ip", "")
        device_port = device.get("device_port", "")
        device_sudo_pass = device.get("device_sudo_pass", "")
        if not device_ssh_key_path:
            if f"{username}&&{passwd}" in tmp_devices:
                tmp_devices[f"{username}&&{passwd}&&{device_port}"].append(device_ip)
            else:
                tmp_devices[f"{username}&&{passwd}&&{device_port}"] = [device_ip]
        else:
            if f"{username}&=&{device_ssh_key_path}&=&{device_port}" in tmp_devices:
                tmp_devices[f"{username}&=&{device_ssh_key_path}&=&{device_port}"].append(device_ip)
            else:
                tmp_devices[f"{username}&=&{device_ssh_key_path}&=&{device_port}"] = [device_ip]
        ip_name_dict[device_ip] = device_name
        
    for user_info, hosts in tmp_devices.items():
        username, passwd, device_port = "", "", ""
        runner = PicaPlayBookRunner(playbook_name="", job_name=job_name, host_sn_dict=dict((host, ip_name_dict[host]) for host in hosts))
        if "&=&" in user_info:
            username, key_file, device_port = user_info.split("&=&")
            _vars["private_key_file"] = key_file
        else:
            username, passwd, device_port = user_info.split("&&")
        _vars["ansible_ssh_port"] = device_port
        _vars["ansible_become_pass"] = device_sudo_pass
        runner.run_playbook_file(username, passwd, playbook_path, hosts, _vars, use_local=False)
        
    LOG.info(":::Finish enable monitor")
    
    for host in ip_name_dict.keys():
        new_target.append(host+":9100")
     
    if not os.path.exists(target_json):
        data = [
            {
                "targets": list(set(new_target))
            }
        ]
        with open(target_json, 'w') as json_file:
            json.dump(data, json_file, indent=4)
    else:
        with open(target_json, 'r') as json_file:
            data = json.load(json_file)

        existing_targets = set()
        if data and "targets" in data[0]:
            existing_targets = set(data[0]["targets"])
        updated_targets = existing_targets.union(new_target)
        data[0]["targets"] = list(updated_targets)

        with open(target_json, 'w') as json_file:
            json.dump(data, json_file, indent=4)

    LOG.info(":::Finish reload prometheus")        
    
    for device in devices:
        device_ip = device.get("device_ip", "")
        inven_db.update_switch_montior(name=device_ip, device_type=3)
            
    LOG.info(":::Finish update mysql ")


@my_celery_app.task(name="delete_deploy_node_exporter", base=AmpConBaseTask)
def delete_deploy_node_exporter(devices):
    tmp_devices = dict()
    ip_name_dict = dict()
    new_target = []
    _vars={}
    job_name = '{0}:::{1}'.format(datetime.now().strftime('%Y-%m-%d %H:%M:%S'), "delete_monitor")
    playbook_path = "/usr/share/automation/server/monitor/node_exporter/delete_node_exporter.yml"
    target_json = "/usr/share/automation/server/monitor/settings/node_target.json"
    for device in devices:
        device_name = device.get("device_name", "")
        username = device.get("device_user", "")
        passwd = device.get("device_pwd", "")
        device_ssh_key_path = device.get("device_ssh_key_path", "")
        device_ip = device.get("device_ip", "")
        device_port = device.get("device_port", "")
        device_sudo_pass = device.get("device_sudo_pass", "")
        if not device_ssh_key_path:
            if f"{username}&&{passwd}" in tmp_devices:
                tmp_devices[f"{username}&&{passwd}&&{device_port}"].append(device_ip)
            else:
                tmp_devices[f"{username}&&{passwd}&&{device_port}"] = [device_ip]
        else:
            if f"{username}&=&{device_ssh_key_path}&=&{device_port}" in tmp_devices:
                tmp_devices[f"{username}&=&{device_ssh_key_path}&=&{device_port}"].append(device_ip)
            else:
                tmp_devices[f"{username}&=&{device_ssh_key_path}&=&{device_port}"] = [device_ip]
        ip_name_dict[device_ip] = device_name
        
    for user_info, hosts in tmp_devices.items():
        username, passwd, device_port = "", "", ""
        runner = PicaPlayBookRunner(playbook_name="", job_name=job_name, host_sn_dict=dict((host, ip_name_dict[host]) for host in hosts))
        if "&=&" in user_info:
            username, key_file, device_port = user_info.split("&=&")
            _vars["private_key_file"] = key_file
        else:
            username, passwd, device_port = user_info.split("&&")
        _vars["ansible_ssh_port"] = device_port
        _vars["ansible_become_pass"] = device_sudo_pass
        runner.run_playbook_file(username, passwd, playbook_path, hosts, _vars, use_local=False)
        
    LOG.info(":::Finish delete monitor")
    
    for host in ip_name_dict.keys():
        new_target.append(host+":9100")
    
    with open(target_json, 'r') as json_file:
        data = json.load(json_file)

    existing_targets = set()
    if data and "targets" in data[0]:
        existing_targets = set(data[0]["targets"])
    updated_targets = existing_targets - set(new_target)
    data[0]["targets"] = list(updated_targets)

    with open(target_json, 'w') as json_file:
        json.dump(data, json_file, indent=4)  

    LOG.info(":::Finish reload prometheus")        
    
    for device in devices:
        device_ip = device.get("device_ip", "")
        inven_db.delete_switch_montior(name=device_ip, device_type=3)
            
    LOG.info(":::Finish update mysql ")
        