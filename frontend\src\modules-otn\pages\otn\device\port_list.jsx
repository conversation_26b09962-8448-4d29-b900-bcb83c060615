import styles from "@/modules-otn/pages/otn/device/cardPanel.module.scss";
import {CustomTable} from "@/modules-otn/components/common/custom_table";
import {Card, Switch, theme, Typography, message, Space} from "antd";
import {useSelector} from "react-redux";
import {
    convertToArray,
    getDeviceStateValue,
    getText,
    mHz_to_nm,
    NULL_VALUE,
    DebounceButton,
    mHz_to_nm_ITU
} from "@/modules-otn/utils/util";
import React, {useEffect, useRef, useState} from "react";
import {apiEditRpc, apiRpc, NEGet, NESet, netconfGetByXML, objectGet} from "@/modules-otn/apis/api";
import openDisplayDialog from "@/modules-otn/pages/otn/device/display_info";
import openCustomEditForm from "@/modules-otn/components/form/edit_form_custom";
import {openModalTable} from "@/modules-otn/components/form/edit_table";
import {useNavigate} from "react-router-dom";
import {editTableIcon} from "@/modules-otn/pages/otn/device/device_icons";
import Icon from "@ant-design/icons";
import useUserRight from "@/modules-otn/hooks/useUserRight";
import {smallModal} from "@/modules-otn/components/modal/custom_modal";

const {Paragraph} = Typography;

const slotConfig = {
    OA: 2,
    OP: 1,
    OLA: 2,
    OTDR: 1,
    TFF: 1,
    OCM: 1,
    WSS: 2,
    LINECARD: {"11MC2": 2, "20MC2": 2, "2MC2": 1, "4MC4": 1}
};

const NULL_Port = {
    cardName: NULL_VALUE,
    cardType: NULL_VALUE,
    "card-name": NULL_VALUE,
    empty: NULL_VALUE,
    "equipment-mismatch": NULL_VALUE,
    "slot-note": NULL_VALUE
};

const PortList = ({data}) => {
    const readyOnlyRight = useUserRight();
    const [dataList, setDataList] = useState([]);
    const [loading, setLoading] = useState(false);
    const currentTypeRef = useRef();
    const [rowIndex, setRowIndex] = useState("");
    const {labelList} = useSelector(state => state.languageOTN);
    const navigate = useNavigate();
    const {
        token: {colorPrimary}
    } = theme.useToken();

    const {ne_id, name, type, neData, stateData, needLoadingState} = data;
    currentTypeRef.current = type;

    const willUpdateDataList = (data, updateType) => {
        if (updateType === currentTypeRef.current) {
            setDataList(data);
        }
    };

    const switchComponent = (value, rowData, disabled, getEditXML, submit) => {
        if (value === NULL_VALUE) {
            return NULL_VALUE;
        }
        return (
            <Switch
                disabled={readyOnlyRight.disabled ?? disabled ?? false}
                defaultChecked={!!value}
                onChange={newVal => {
                    if (newVal === value) {
                        return;
                    }
                    if (submit) {
                        submit(newVal).then(rs => {
                            console.log(rs);
                            setTimeout(() => {
                                loadDate().then();
                            }, 1000);
                        });
                        return;
                    }
                    apiEditRpc({
                        ne_id,
                        params: getEditXML(newVal),
                        success: () => {
                            loadDate().then();
                        }
                    });
                }}
            />
        );
    };

    const editComponent = (value, rowData, submit) => {
        if (!value || value === NULL_VALUE) {
            return value;
        }
        return (
            <Paragraph
                style={{margin: 0, display: "flex"}}
                editable={
                    readyOnlyRight.disabled
                        ? false
                        : {
                              maxLength: 32,
                              icon: <Icon component={editTableIcon} />,
                              text: value,
                              onChange: newVal => {
                                  if (!newVal || newVal === value) {
                                      return;
                                  }
                                  if (submit) {
                                      submit(newVal).then(() => {
                                          setTimeout(() => {
                                              loadDate().then();
                                          }, 1000);
                                      });
                                      return;
                                  }
                                  apiEditRpc({
                                      ne_id,
                                      params: {
                                          components: {
                                              component: {
                                                  name: rowData.name,
                                                  config: {
                                                      description: newVal
                                                  }
                                              }
                                          }
                                      },
                                      success: () => {
                                          setTimeout(loadDate, 1500);
                                      }
                                  });
                              },
                              triggerType: ["icon", "text"]
                          }
                }
            >
                <div style={{flex: 1}}>{value}</div>
            </Paragraph>
        );
    };

    const TableConfigs = {
        chassis: {
            columns: [
                {dataIndex: "slot-no"},
                {dataIndex: "empty"},
                {dataIndex: "equipment-mismatch"},
                {dataIndex: "card-name"},
                {
                    dataIndex: "slot-note",
                    render: (value, rowData) => editComponent(value, rowData)
                }
            ],
            getData: async () => {
                const slots = neData?.subcomponents?.subcomponent;
                if (slots) {
                    const cardInfoList = [];
                    for (let i = 0; i < slots.length; i++) {
                        const cardName = slots[i].name;
                        const slotNum = parseInt(cardName.substring(cardName.lastIndexOf("-") + 1)) ?? -1;
                        if (slotNum >= 1 && slotNum <= 8) {
                            // const rs = (
                            //     await netconfGetByXML({
                            //         msg: true,
                            //         ne_id,
                            //         xml: {
                            //             components: {
                            //                 $: {
                            //                     xmlns: "http://openconfig.net/yang/platform"
                            //                 },
                            //                 component: {
                            //                     name: cardName
                            //                 }
                            //             }
                            //         }
                            //     })
                            // )?.components?.component;
                            const rs = (await objectGet("", {DBKey: `ne:5:component:${ne_id}:${cardName}`}))
                                .documents?.[0]?.value?.data;
                            const actualType = rs?.state?.["actual-vendor-type"];
                            const preconfType = rs?.config?.["vendor-type-preconf"];
                            cardInfoList.push({
                                "slot-no": slotNum,
                                name: cardName,
                                cardType: cardName.substring(0, cardName.indexOf("-")),
                                "card-name": actualType ?? preconfType,
                                empty: (rs?.state?.empty ?? "TURE").toUpperCase(),
                                "equipment-mismatch": (
                                    (actualType && preconfType && actualType !== preconfType) ??
                                    false
                                )
                                    .toString()
                                    .toUpperCase(),
                                "slot-note": rs?.config?.description
                            });
                        }
                    }
                    for (let i = 1; i <= 8; i++) {
                        const card = cardInfoList.find(s => s["slot-no"] === i);
                        if (!card) {
                            if (i % 2 === 0) {
                                const preCard = cardInfoList.find(s => s["slot-no"] === i - 1);
                                if (!preCard || preCard.cardName === NULL_VALUE) {
                                    cardInfoList.push({...NULL_Port, "slot-no": i});
                                } else {
                                    let slotSpace = 0;
                                    if (preCard.cardType === "LINECARD") {
                                        slotSpace = slotConfig[preCard.cardType][preCard["card-name"]];
                                    } else {
                                        slotSpace = slotConfig[preCard.cardType];
                                    }
                                    if (slotSpace === 1) {
                                        cardInfoList.push({...NULL_Port, "slot-no": i});
                                    } else {
                                        // cardInfoList.push({...preCard, "slot-no": i});
                                    }
                                }
                            } else {
                                cardInfoList.push({...NULL_Port, "slot-no": i});
                            }
                        }
                    }
                    willUpdateDataList(
                        cardInfoList.sort((a, b) => (a["slot-no"] > b["slot-no"] ? 1 : -1)),
                        "chassis"
                    );
                }
            }
        },
        "D7000-AUX": {
            columns: [
                {dataIndex: "no"},
                {dataIndex: "name", label: "port-name"},
                {dataIndex: "port-ip"},
                {
                    dataIndex: "port-note",
                    render: (value, rowData) => editComponent(value, rowData)
                }
            ],
            getData: async () => {
                const getIpAddress = (interfaces, interfaceName) => {
                    const itf = interfaces.find(itf => itf?.value?.data?.name === interfaceName);
                    if (itf) {
                        const subItf = convertToArray(itf?.value?.data?.subinterfaces)[0].subinterface;
                        if (subItf) {
                            const address = convertToArray(convertToArray(subItf)?.[0]?.ipv4?.addresses)?.[0].address;
                            if (address) {
                                return convertToArray(address).find(i => i.ip !== "")?.ip ?? "--";
                            }
                        }
                    }
                    return "--";
                };

                const ports = neData?.subcomponents?.subcomponent;
                const portList = [];
                if (ports) {
                    // const interfaces =
                    //     (
                    //         await netconfGetByXML({
                    //             msg: true,
                    //             ne_id,
                    //             xml: {
                    //                 interfaces: {
                    //                     $: {
                    //                         xmlns: "http://openconfig.net/yang/interfaces"
                    //                     },
                    //                     interface: {}
                    //                 }
                    //             }
                    //         })
                    //     )?.interfaces?.interface ?? [];
                    const interfaces = (await objectGet("ne:5:interface", {ne_id})).documents;
                    for (let i = 0; i < ports.length; i++) {
                        // const rs = (
                        //     await netconfGetByXML({
                        //         msg: true,
                        //         ne_id,
                        //         xml: {
                        //             components: {
                        //                 $: {
                        //                     xmlns: "http://openconfig.net/yang/platform"
                        //                 },
                        //                 component: {
                        //                     name: ports[i].name
                        //                 }
                        //             }
                        //         }
                        //     })
                        // )?.components?.component;
                        const rs = (await objectGet("", {DBKey: `ne:5:component:${ne_id}:${ports[i].name}`}))
                            .documents?.[0]?.value?.data;
                        if (rs) {
                            portList.push({
                                no: i + 1,
                                name: rs.name ?? NULL_VALUE,
                                "port-ip": getIpAddress(interfaces, rs.name.replace("PORT", "INTERFACE")),
                                "port-note": rs.config?.description ?? NULL_VALUE
                            });
                        }
                    }
                }
                willUpdateDataList(portList, "D7000-AUX");
            }
        },
        LINECARD: {
            columns: [
                {dataIndex: "name", label: "port-name", fixed: "left"},
                {dataIndex: "module-type"},
                {dataIndex: "module-wavelength", unit: "nm"},
                {dataIndex: "input-optical-power", unit: "dBm"},
                {dataIndex: "output-optical-power", unit: "dBm"},
                {
                    dataIndex: "module-state",
                    render: value => {
                        if (value === NULL_VALUE) {
                            return NULL_VALUE;
                        }
                        return (
                            <div style={{display: "flex", alignItems: "center"}}>
                                <div
                                    style={{
                                        width: 5,
                                        height: 5,
                                        backgroundColor: "#14c9bb",
                                        borderRadius: "50%",
                                        marginRight: 5
                                    }}
                                />
                                <div>{value}</div>
                            </div>
                        );
                    }
                },
                {
                    dataIndex: "laser-switch",
                    render: (value, rowData) =>
                        switchComponent(value, rowData, rowData["module-state"] === NULL_VALUE, newVal => {
                            return {
                                components: {
                                    $: {
                                        xmlns: "http://openconfig.net/yang/platform"
                                    },
                                    component: {
                                        name: rowData.name.replace("PORT", "TRANSCEIVER"),
                                        transceiver: {
                                            $: {
                                                xmlns: "http://openconfig.net/yang/platform/transceiver"
                                            },
                                            config: {
                                                enabled: newVal
                                            }
                                        }
                                    }
                                }
                            };
                        })
                },
                {
                    dataIndex: "port-note",
                    render: (value, rowData) => editComponent(value, rowData)
                },
                {
                    dataIndex: labelList.operation,
                    sorter: false,
                    fixed: "right",
                    render: (value, rowData) => (
                        <DebounceButton
                            disabled={rowData["module-state"] === NULL_VALUE}
                            onClick={() => {
                                openDisplayDialog({
                                    title: rowData.name.replace("PORT", "TRANSCEIVER"),
                                    width: 900,
                                    columns: [
                                        [{dataIndex: "sn"}, {dataIndex: "distance", unit: "km"}],
                                        [{dataIndex: "pn"}, {dataIndex: "temperature", unit: "°C"}]
                                    ],
                                    getData: async () => {
                                        const transceiver = (
                                            await netconfGetByXML({
                                                msg: true,
                                                ne_id,
                                                xml: {
                                                    components: {
                                                        $: {
                                                            xmlns: "http://openconfig.net/yang/platform"
                                                        },
                                                        component: {
                                                            name: rowData.name.replace("PORT", "TRANSCEIVER")
                                                        }
                                                    }
                                                }
                                            })
                                        )?.components?.component;
                                        return {
                                            sn: transceiver?.state?.["serial-no"] ?? NULL_VALUE,
                                            pn: transceiver?.state?.["part-no"] ?? NULL_VALUE,
                                            "input-power":
                                                transceiver?.transceiver?.state?.["input-power"]?.instant ?? NULL_VALUE,
                                            "output-power":
                                                transceiver?.transceiver?.state?.["output-power"]?.instant ??
                                                NULL_VALUE,
                                            distance:
                                                transceiver?.transceiver?.state?.["transmission-distance"] ??
                                                NULL_VALUE,
                                            temperature: transceiver?.state?.temperature?.instant ?? NULL_VALUE
                                        };
                                    }
                                });
                            }}
                            type="link"
                            title={labelList.more}
                            style={{color: rowData["module-state"] === NULL_VALUE ? "#b9b9b9" : colorPrimary}}
                        >
                            {labelList.more}
                        </DebounceButton>
                    )
                }
            ],
            getData: async () => {
                const ports = neData?.subcomponents?.subcomponent;
                const portList = [];
                const allInfo = (await objectGet("ne:5:component", {ne_id})).documents;
                for (let i = 0; i < ports.length; i++) {
                    const portName = ports[i].name;
                    if (portName.startsWith("PORT")) {
                        const port = allInfo.find(neInfo => neInfo?.value?.data?.state?.name === portName)?.value?.data;
                        const moduleType = getDeviceStateValue(stateData, portName, "actual-vendor-type");
                        portList.push({
                            name: ports[i].name,
                            "module-type": moduleType ?? NULL_VALUE,
                            "module-wavelength": mHz_to_nm(
                                getDeviceStateValue(stateData, portName.replace("PORT", "OCH"), "frequency")
                            ),
                            "input-optical-power":
                                getDeviceStateValue(stateData, portName, "input-power") ?? NULL_VALUE,
                            "output-optical-power":
                                getDeviceStateValue(stateData, portName, "output-power") ?? NULL_VALUE,
                            "module-state": moduleType ? "Present" : NULL_VALUE,
                            "laser-switch": getDeviceStateValue(stateData, portName, "enabled") ?? NULL_VALUE,
                            "port-note": port?.config?.description ?? NULL_VALUE
                        });
                    }
                }
                willUpdateDataList(portList, "LINECARD");
            }
        },
        OA: {
            columns: [
                {dataIndex: "no", fixed: "left"},
                {dataIndex: "name", label: "port-name"},
                {dataIndex: "input-optical-power", unit: "dBm"},
                {dataIndex: "output-optical-power", unit: "dBm"},
                {
                    dataIndex: "laser-switch",
                    render: (value, rowData) =>
                        switchComponent(value, rowData, rowData["laser-switch"] === NULL_VALUE, newVal => {
                            return {
                                components: {
                                    $: {
                                        xmlns: "http://openconfig.net/yang/platform"
                                    },
                                    component: {
                                        name: rowData.name.replace("PORT", "TRANSCEIVER"),
                                        transceiver: {
                                            $: {
                                                xmlns: "http://openconfig.net/yang/platform/transceiver"
                                            },
                                            config: {
                                                enabled: newVal
                                            }
                                        }
                                    }
                                }
                            };
                        })
                },
                {
                    dataIndex: "port-note",
                    render: (value, rowData) => editComponent(value, rowData)
                }
            ],
            getData: async () => {
                const ports = neData?.subcomponents?.subcomponent;

                const portList = [];
                let index = 0;
                for (let i = 0; i < ports.length; i++) {
                    const portName = ports[i].name;
                    if (portName.startsWith("PORT")) {
                        index++;
                        let transceiver;
                        if (portName.endsWith("OSC")) {
                            transceiver = (
                                await netconfGetByXML({
                                    msg: true,
                                    ne_id,
                                    xml: {
                                        components: {
                                            $: {
                                                xmlns: "http://openconfig.net/yang/platform"
                                            },
                                            component: {
                                                name: portName.replace("PORT", "TRANSCEIVER")
                                            }
                                        }
                                    }
                                })
                            )?.components?.component;
                        }
                        const port = (await objectGet("", {DBKey: `ne:5:component:${ne_id}:${portName}`}))
                            .documents?.[0]?.value?.data;
                        portList.push({
                            no: index,
                            name: portName,
                            "input-optical-power":
                                getDeviceStateValue(stateData, portName, "input-power") ?? NULL_VALUE,
                            "output-optical-power":
                                getDeviceStateValue(stateData, portName, "output-power") ?? NULL_VALUE,
                            "laser-switch":
                                transceiver?.transceiver?.config?.enabled ??
                                getDeviceStateValue(stateData, portName, "enabled") ??
                                NULL_VALUE,
                            "port-note": port?.config?.description ?? NULL_VALUE
                        });
                    }
                }
                willUpdateDataList(portList, "OA");
            }
        },
        WSS: {
            columns: [
                {dataIndex: "no", fixed: "left"},
                {dataIndex: "name", label: "port-name"},
                {dataIndex: "input-optical-power", unit: "dBm"},
                {dataIndex: "output-optical-power", unit: "dBm"},
                {
                    dataIndex: "laser-switch",
                    render: (value, rowData) =>
                        switchComponent(value, rowData, rowData["laser-switch"] === NULL_VALUE, newVal => {
                            return {
                                components: {
                                    $: {
                                        xmlns: "http://openconfig.net/yang/platform"
                                    },
                                    component: {
                                        name: rowData.name.replace("PORT", "TRANSCEIVER"),
                                        transceiver: {
                                            $: {
                                                xmlns: "http://openconfig.net/yang/platform/transceiver"
                                            },
                                            config: {
                                                enabled: newVal
                                            }
                                        }
                                    }
                                }
                            };
                        })
                },
                {
                    dataIndex: "port-note",
                    render: (value, rowData) => editComponent(value, rowData)
                },
                {
                    dataIndex: labelList.operation,
                    sorter: false,
                    fixed: "right",
                    render: (value, rowData, index) => {
                        const result = rowData?.name?.split("-")?.[rowData.name.split("-").length - 1];
                        if (result?.includes("AD")) {
                            setRowIndex(index);
                            return (
                                <DebounceButton
                                    containerType="div"
                                    className={styles.operate}
                                    onClick={() => {
                                        openModalTable(
                                            "frequency-channel",
                                            name,
                                            "5",
                                            `${name} - Frequency Channels`,
                                            {
                                                width: "80%",
                                                rowOperation: [{type: "delete"}],
                                                createEnable: true,
                                                refresh: true
                                            },
                                            ne_id,
                                            `ne:5:component:${ne_id}:${name}`,
                                            null,
                                            null,
                                            "component"
                                        );
                                    }}
                                >
                                    WSS
                                </DebounceButton>
                            );
                        }
                        return NULL_VALUE;
                    },
                    onCell: (_, index) => {
                        const indexArray = [
                            rowIndex - 1,
                            rowIndex - 2,
                            rowIndex - 3,
                            rowIndex - 4,
                            rowIndex - 5,
                            rowIndex - 6,
                            rowIndex - 7,
                            rowIndex - 8,
                            rowIndex
                        ];
                        if (indexArray.includes(index)) {
                            if (index === rowIndex - 8) {
                                return {
                                    rowSpan: 9
                                };
                            }
                            return {
                                rowSpan: 0
                            };
                        }
                    }
                }
            ],
            getData: async () => {
                const ports = neData?.subcomponents?.subcomponent;
                const portList = [];
                let index = 0;
                const allInfo = (await objectGet("ne:5:component", {ne_id})).documents;
                for (let i = 0; i < ports.length; i++) {
                    const portName = ports[i].name;
                    if (portName.startsWith("PORT")) {
                        index++;
                        let transceiver;
                        if (portName.endsWith("OSC")) {
                            transceiver = (
                                await netconfGetByXML({
                                    msg: true,
                                    ne_id,
                                    xml: {
                                        components: {
                                            $: {
                                                xmlns: "http://openconfig.net/yang/platform"
                                            },
                                            component: {
                                                name: portName.replace("PORT", "TRANSCEIVER")
                                            }
                                        }
                                    }
                                })
                            )?.components?.component;
                        }
                        const port = allInfo.find(neInfo => neInfo?.value?.data?.state?.name === portName)?.value?.data;
                        portList.push({
                            no: index,
                            name: portName,
                            "input-optical-power":
                                getDeviceStateValue(stateData, portName, "input-power") ?? NULL_VALUE,
                            "output-optical-power":
                                getDeviceStateValue(stateData, portName, "output-power") ?? NULL_VALUE,
                            "laser-switch":
                                transceiver?.transceiver?.config?.enabled ??
                                getDeviceStateValue(stateData, portName, "enabled") ??
                                NULL_VALUE,
                            "port-note": port?.config?.description ?? NULL_VALUE
                        });
                    }
                }
                willUpdateDataList(portList, "WSS");
            }
        },
        TFF: {
            columns: [
                {dataIndex: "no", fixed: "left"},
                {dataIndex: "port-name"},
                {dataIndex: "port-wavelength", unit: "nm"},
                {dataIndex: "input-optical-power", unit: "dBm"},
                {dataIndex: "port-used"},
                {
                    dataIndex: "port-note",
                    render: (value, rowData) => editComponent(value, rowData)
                }
            ],
            getData: async () => {
                const services = (await objectGet("nms:provision", {type: "och"}))?.documents;
                const ports = neData?.subcomponents?.subcomponent;
                const portList = [];
                for (let i = 0; i < ports.length; i++) {
                    const portName = ports[i].name;
                    const port = (await objectGet("", {DBKey: `ne:5:component:${ne_id}:${portName}`})).documents?.[0]
                        ?.value?.data;
                    portList.push({
                        no: i + 1,
                        name: portName,
                        "port-name": port?.state?.["panel-description"],
                        "input-optical-power":
                            port?.port?.["optical-port"]?.state?.["input-power"]?.instant ?? NULL_VALUE,
                        "port-wavelength":
                            portName.indexOf("CH") > -1
                                ? mHz_to_nm(
                                      parseInt(`19${parseInt(port?.state?.["panel-description"].substring(1))}00`)
                                  )
                                : NULL_VALUE,
                        "port-used": services.find(service =>
                            service.value.ne.find(neInfo => neInfo.ne === ne_id && neInfo.port === portName)
                        )
                            ? "Yes"
                            : "No",
                        "port-note": port?.config?.description ?? NULL_VALUE
                    });
                }
                willUpdateDataList(portList, "TFF");
            }
        },
        MUX: {
            columns: [
                {dataIndex: "no"},
                {dataIndex: "port-name"},
                {dataIndex: "port-wavelength", unit: "nm"},
                {dataIndex: "port-used"},
                {
                    dataIndex: "port-note",
                    render: (value, rowData) => editComponent(value, rowData)
                }
            ],
            getData: async () => {
                const services = (await objectGet("nms:provision", {type: "och"}))?.documents;
                const ports = neData?.subcomponents?.subcomponent;
                const portList = [];
                const allInfo = (await objectGet("ne:5:component", {ne_id})).documents;
                for (let i = 0; i < ports.length; i++) {
                    const portName = ports[i].name;
                    const port = allInfo.find(neInfo => neInfo?.value?.data?.state?.name === portName)?.value?.data;
                    portList.push({
                        no: i + 1,
                        name: portName,
                        "port-name": port?.state?.["panel-description"] ?? portName,
                        "port-wavelength":
                            portName.indexOf("CH") > -1
                                ? mHz_to_nm_ITU(
                                      parseInt(`19${parseInt(port?.state?.["panel-description"].substring(1))}00`)
                                  )
                                : NULL_VALUE,
                        "port-used": services.find(service =>
                            service.value.ne.find(neInfo => neInfo.ne === ne_id && neInfo.port === portName)
                        )
                            ? "Yes"
                            : "No",
                        "port-note": port?.config?.description ?? NULL_VALUE
                    });
                }
                willUpdateDataList(portList, "MUX");
            }
        },
        OTDR: {
            columns: [
                {dataIndex: "no", fixed: "left"},
                {dataIndex: "name", label: "port-name"},
                {dataIndex: "input-optical-power", unit: "dBm"},
                {
                    dataIndex: "port-note",
                    render: (value, rowData) => editComponent(value, rowData)
                },
                {
                    dataIndex: labelList.operation,
                    sorter: false,
                    fixed: "right",
                    render: () => {
                        return (
                            <div
                                className={styles.operate}
                                onClick={() => {
                                    const otdrLink =
                                        import.meta.env.VITE_APP_EXPORT_MODULE === "AmpCon-T"
                                            ? "/maintain/link_measure/otdr"
                                            : "/maintain/transport_config/link_measure/otdr";
                                    navigate(otdrLink, {state: "otdr"});
                                }}
                            >
                                Detection
                            </div>
                        );
                    },
                    onCell: (_, index) => {
                        if (index === 0) {
                            return {
                                rowSpan: dataList?.length
                            };
                        }
                        return {
                            rowSpan: 0
                        };
                    }
                }
            ],
            getData: async () => {
                const ports = neData?.subcomponents?.subcomponent;
                const portList = [];
                let index = 0;
                for (let i = 0; i < ports.length; i++) {
                    const portName = ports[i].name;
                    if (portName.startsWith("PORT")) {
                        index++;
                        const port = (await objectGet("", {DBKey: `ne:5:component:${ne_id}:${portName}`}))
                            .documents?.[0]?.value?.data;
                        portList.push({
                            no: index,
                            name: portName,
                            "input-optical-power":
                                getDeviceStateValue(stateData, portName, "input-power") ?? NULL_VALUE,
                            "port-note": port?.config?.description ?? NULL_VALUE
                        });
                    }
                }
                willUpdateDataList(portList, "OTDR");
            }
        },
        OCM: {
            columns: [
                {dataIndex: "no", fixed: "left"},
                {dataIndex: "name", label: "port-name"},
                {dataIndex: "input-optical-power", unit: "dBm"},
                {
                    dataIndex: "port-note",
                    render: (value, rowData) => editComponent(value, rowData)
                },
                {
                    dataIndex: labelList.operation,
                    sorter: false,
                    fixed: "right",
                    render: () => {
                        return (
                            <div
                                className={styles.operate}
                                onClick={() => {
                                    const ocmPath =
                                        import.meta.env.VITE_APP_EXPORT_MODULE === "AmpCon-T"
                                            ? "/maintain/link_measure/ocm"
                                            : "/maintain/transport_config/link_measure/ocm";
                                    navigate(ocmPath, {state: "ocm"});
                                }}
                            >
                                Sweep
                            </div>
                        );
                    },
                    onCell: (_, index) => {
                        if (index === 0) {
                            return {
                                rowSpan: dataList?.length
                            };
                        }
                        return {
                            rowSpan: 0
                        };
                    }
                }
            ],
            getData: async () => {
                const ports = neData?.subcomponents?.subcomponent;

                const portList = [];
                let index = 0;
                for (let i = 0; i < ports.length; i++) {
                    const portName = ports[i].name;
                    if (portName.startsWith("PORT")) {
                        index++;
                        const port = (await objectGet("", {DBKey: `ne:5:component:${ne_id}:${portName}`}))
                            .documents?.[0]?.value?.data;
                        portList.push({
                            no: index,
                            name: portName,
                            "input-optical-power":
                                getDeviceStateValue(stateData, portName, "input-power") ?? NULL_VALUE,
                            "port-note": port?.config?.description ?? NULL_VALUE
                        });
                    }
                }
                willUpdateDataList(portList, "OCM");
            }
        },
        OLP: {
            columns: [
                {
                    dataIndex: "protection-group",
                    onCell: (_, index) => {
                        return {
                            rowSpan: index % 3 === 0 ? 3 : 0
                        };
                    }
                },
                {dataIndex: "port-list"},
                {
                    dataIndex: "work-status",
                    onCell: (_, index) => {
                        return {
                            rowSpan: index % 3 === 0 ? 3 : 0
                        };
                    }
                },
                {
                    dataIndex: "manual-switching",
                    onCell: (_, index) => {
                        return {
                            rowSpan: index % 3 === 0 ? 3 : 0
                        };
                    },
                    render: (value, rowData) => {
                        const activeCSS = {
                            color: "#14C9BB",
                            backgroundColor: "#E7F9F8"
                        };
                        const inactiveCSS = {
                            color: "#B3BBC8",
                            backgroundColor: "#F4F5F7"
                        };

                        const changeAps = (apsType, protectionGroup) => {
                            const modal = smallModal({
                                content: `Change to ${apsType}?`,
                                okButtonProps: {
                                    disabled: readyOnlyRight.disabled
                                },
                                // eslint-disable-next-line no-unused-vars
                                onOk: _ => {
                                    modal.destroy();
                                    apiRpc({
                                        ne_id,
                                        rpcName: "switch-olp",
                                        rpcConfig: {
                                            name: protectionGroup,
                                            "switch-to-port": apsType
                                        }
                                    }).then(rs => {
                                        if (rs.message === "SUCCESS") {
                                            message.success(labelList.save_success).then();
                                            setTimeout(loadDate, 2000);
                                        } else {
                                            message.error(labelList.save_failed).then();
                                        }
                                    });
                                }
                            });
                        };
                        return (
                            <div style={{display: "flex"}}>
                                <a
                                    className={styles.cycle_div}
                                    style={value === "PRIMARY" ? activeCSS : inactiveCSS}
                                    onClick={() => {
                                        if (value === "PRIMARY") {
                                            return;
                                        }
                                        changeAps("PRIMARY", rowData["protection-group"]);
                                    }}
                                >
                                    P
                                </a>
                                <a
                                    className={styles.cycle_div}
                                    style={{
                                        ...(value === "SECONDARY" ? activeCSS : inactiveCSS),
                                        marginLeft: 15
                                    }}
                                    onClick={() => {
                                        if (value === "SECONDARY") {
                                            return;
                                        }
                                        changeAps("SECONDARY", rowData["protection-group"]);
                                    }}
                                >
                                    S
                                </a>
                            </div>
                        );
                    }
                },
                {
                    dataIndex: "protection-config",
                    onCell: (_, index) => {
                        return {
                            rowSpan: index % 3 === 0 ? 3 : 0
                        };
                    },
                    render: (value, rowData) => {
                        return (
                            <Space size={24}>
                                <DebounceButton
                                    onClick={() => {
                                        openDisplayDialog({
                                            title: "Display",
                                            columns: [
                                                [{dataIndex: "protection-revert-type", dot: true}],
                                                [
                                                    {
                                                        dataIndex: "wait-to-restore-time",
                                                        label: "wtr-time",
                                                        unit: "ms",
                                                        dot: true
                                                    }
                                                ],
                                                [{dataIndex: "hold-off-time", unit: "ms", dot: true}],
                                                [{dataIndex: "primary-switch-threshold", unit: "dBm", dot: true}],
                                                [{dataIndex: "primary-switch-hysteresis", unit: "dB", dot: true}],
                                                [{dataIndex: "secondary-switch-threshold", unit: "dBm", dot: true}],
                                                [{dataIndex: "relative-switch-threshold", unit: "dB", dot: true}],
                                                [
                                                    {
                                                        dataIndex: "relative-switch-threshold-offset",
                                                        unit: "dB",
                                                        dot: true
                                                    }
                                                ],
                                                [{dataIndex: "force-to-port", dot: true}]
                                            ],
                                            getData: async () => {
                                                const apsInfo = (
                                                    await netconfGetByXML({
                                                        msg: true,
                                                        ne_id,
                                                        xml: {
                                                            aps: {
                                                                $: {
                                                                    xmlns: "http://openconfig.net/yang/optical-transport-line-protection"
                                                                },
                                                                "aps-modules": {
                                                                    "aps-module": {
                                                                        name: rowData["protection-group"]
                                                                    }
                                                                }
                                                            }
                                                        }
                                                    })
                                                )?.aps?.["aps-modules"]?.["aps-module"]?.state;
                                                if (apsInfo) {
                                                    return {
                                                        ...apsInfo,
                                                        "protection-revert-type":
                                                            apsInfo.revertive === "false"
                                                                ? "Non-Revertive"
                                                                : "Revertive"
                                                    };
                                                }
                                                return {};
                                            }
                                        });
                                    }}
                                    type="link"
                                    title="Display"
                                    style={{color: colorPrimary}}
                                >
                                    Display
                                </DebounceButton>
                                <DebounceButton
                                    disabled={readyOnlyRight.disabled}
                                    style={{color: readyOnlyRight.disabled ? "" : colorPrimary}}
                                    type="link"
                                    title="Modify"
                                    onClick={() => {
                                        openCustomEditForm({
                                            title: "Modify",
                                            width: 1200,
                                            columnNum: 2,
                                            columns: [
                                                [
                                                    {
                                                        dataIndex: "revertive",
                                                        label: "protection-revert-type",
                                                        inputType: "radio",
                                                        data: {
                                                            options: [
                                                                {value: "false", title: "Non-Revertive"},
                                                                {value: "true", title: "Revertive"}
                                                            ]
                                                        }
                                                    }
                                                ],
                                                [
                                                    {
                                                        dataIndex: "wait-to-restore-time",
                                                        label: "wtr-time",
                                                        unit: "ms",
                                                        inputType: "number"
                                                    },
                                                    {dataIndex: "hold-off-time", unit: "ms", inputType: "number"}
                                                ],
                                                [
                                                    {
                                                        dataIndex: "primary-switch-threshold",
                                                        unit: "dBm",
                                                        inputType: "number",
                                                        step: 0.01
                                                    },
                                                    {
                                                        dataIndex: "primary-switch-hysteresis",
                                                        unit: "dB",
                                                        inputType: "number",
                                                        step: 0.01
                                                    }
                                                ],
                                                [
                                                    {
                                                        dataIndex: "secondary-switch-threshold",
                                                        unit: "dBm",
                                                        inputType: "number",
                                                        step: 0.01
                                                    },
                                                    {
                                                        dataIndex: "relative-switch-threshold",
                                                        unit: "dB",
                                                        inputType: "number",
                                                        step: 0.01
                                                    }
                                                ],
                                                [
                                                    {
                                                        dataIndex: "relative-switch-threshold-offset",
                                                        unit: "dB",
                                                        inputType: "number",
                                                        step: 0.01
                                                    },
                                                    {
                                                        dataIndex: "force-to-port",
                                                        inputType: "select",
                                                        data: {options: ["NONE", "PRIMARY", "SECONDARY"]}
                                                    }
                                                ]
                                            ],
                                            getData: async () => {
                                                const apsInfo = (
                                                    await netconfGetByXML({
                                                        msg: true,
                                                        ne_id,
                                                        xml: {
                                                            aps: {
                                                                $: {
                                                                    xmlns: "http://openconfig.net/yang/optical-transport-line-protection"
                                                                },
                                                                "aps-modules": {
                                                                    "aps-module": {
                                                                        name: rowData["protection-group"]
                                                                    }
                                                                }
                                                            }
                                                        }
                                                    })
                                                )?.aps?.["aps-modules"]?.["aps-module"]?.config;
                                                return apsInfo ?? {};
                                            },
                                            saveFun: async diffValue => {
                                                return await apiEditRpc({
                                                    ne_id,
                                                    params: {
                                                        aps: {
                                                            $: {
                                                                xmlns: "http://openconfig.net/yang/optical-transport-line-protection"
                                                            },
                                                            "aps-modules": {
                                                                "aps-module": {
                                                                    name: rowData["protection-group"],
                                                                    config: diffValue
                                                                }
                                                            }
                                                        }
                                                    }
                                                });
                                            }
                                        });
                                    }}
                                >
                                    Modify
                                </DebounceButton>
                            </Space>
                        );
                    }
                }
            ],
            getData: async () => {
                const ports = neData?.subcomponents?.subcomponent;
                const aps = (await objectGet("ne:5:aps-module", {ne_id})).documents;
                const portList = [];
                for (let i = 0; i < ports.length; i++) {
                    const portName = ports[i].name;
                    if (portName.startsWith("PORT")) {
                        const apsName = portName.substring(0, portName.lastIndexOf("-")).replace("PORT", "APS");
                        const workStatus = aps.find(a => a.value.data.name === apsName)?.value?.data?.state?.[
                            "active-path"
                        ];
                        portList.push({
                            "protection-group": apsName,
                            "port-list": portName,
                            "work-status": workStatus,
                            "manual-switching": workStatus
                        });
                    }
                }
                willUpdateDataList(portList, "OLP");
            }
        },
        OLA: {
            columns: [
                {dataIndex: "no", fixed: "left"},
                {dataIndex: "name", label: "port-name"},
                {dataIndex: "input-optical-power", unit: "dBm"},
                {dataIndex: "output-optical-power", unit: "dBm"},
                {
                    dataIndex: "port-note",
                    render: (value, rowData) => editComponent(value, rowData)
                },
                {
                    dataIndex: "laser-switch",
                    fixed: "right",
                    render: (value, rowData) =>
                        switchComponent(value, rowData, rowData["laser-switch"] === NULL_VALUE, newVal => {
                            return {
                                components: {
                                    $: {
                                        xmlns: "http://openconfig.net/yang/platform"
                                    },
                                    component: {
                                        name: rowData.name.replace("PORT", "TRANSCEIVER"),
                                        transceiver: {
                                            $: {
                                                xmlns: "http://openconfig.net/yang/platform/transceiver"
                                            },
                                            config: {
                                                enabled: newVal
                                            }
                                        }
                                    }
                                }
                            };
                        })
                }
            ],
            getData: async () => {
                const ports = neData?.subcomponents?.subcomponent;
                const portList = [];
                let index = 0;
                for (let i = 0; i < ports.length; i++) {
                    const portName = ports[i].name;
                    if (portName.startsWith("PORT")) {
                        index++;
                        let transceiver;
                        if (portName.indexOf("OSC") > -1) {
                            transceiver = (
                                await netconfGetByXML({
                                    msg: true,
                                    ne_id,
                                    xml: {
                                        components: {
                                            $: {
                                                xmlns: "http://openconfig.net/yang/platform"
                                            },
                                            component: {
                                                name: portName.replace("PORT", "TRANSCEIVER")
                                            }
                                        }
                                    }
                                })
                            )?.components?.component;
                        }
                        const port = (await objectGet("", {DBKey: `ne:5:component:${ne_id}:${portName}`}))
                            .documents?.[0]?.value?.data;
                        portList.push({
                            no: index,
                            name: portName,
                            "input-optical-power":
                                getDeviceStateValue(stateData, portName, "input-power") ?? NULL_VALUE,
                            "output-optical-power":
                                getDeviceStateValue(stateData, portName, "output-power") ?? NULL_VALUE,
                            "laser-switch":
                                transceiver?.transceiver?.config?.enabled ??
                                getDeviceStateValue(stateData, portName, "enabled") ??
                                NULL_VALUE,
                            "port-note": port?.config?.description ?? NULL_VALUE
                        });
                    }
                }
                willUpdateDataList(portList, "OLA");
            }
        },
        RAMAN_BOX: {
            columns: [
                {dataIndex: "no", fixed: "left"},
                // {dataIndex: "port-name", fixed: "left"},
                {dataIndex: "optical-power", unit: "dBm"},
                {
                    dataIndex: "config-gain",
                    unit: "dB",
                    render: (value, rowData) =>
                        editComponent(value, rowData, async newVal => {
                            if (/\./.test(newVal) && newVal?.split(".")[1]?.length > 1) {
                                message.error(labelList.one_decimal_warning);
                                return false;
                            }
                            const rs = await NEGet({
                                ne_id,
                                parameter: {ramanChannelEntry: {}}
                            });
                            const {ramanChannelConfigGainMin, ramanChannelConfigGainMax} = rs.ramanChannelEntry[0];
                            if (newVal < ramanChannelConfigGainMin / 10 || newVal > ramanChannelConfigGainMax / 10) {
                                message.error(
                                    labelList.edit_range_warning.format(
                                        (ramanChannelConfigGainMin / 10).toFixed(1),
                                        (ramanChannelConfigGainMax / 10).toFixed(1)
                                    )
                                );
                                return false;
                            }
                            return await NESet({
                                ne_id,
                                parameter: {
                                    ramanChannelEntry: {
                                        instance: rowData.instance,
                                        ramanChannelConfigGain: newVal * 10
                                    }
                                }
                            });
                        }),
                    onCell: (_, index) => {
                        if (index === 0) {
                            return {
                                rowSpan: dataList?.length
                            };
                        }
                        return {
                            rowSpan: 0
                        };
                    }
                },
                {
                    dataIndex: "actual-gain",
                    unit: "dB",
                    onCell: (_, index) => {
                        if (index === 0) {
                            return {
                                rowSpan: dataList?.length
                            };
                        }
                        return {
                            rowSpan: 0
                        };
                    }
                },
                {
                    dataIndex: "pump-control",
                    fixed: "right",
                    render: (value, rowData) =>
                        switchComponent(value, rowData, false, null, async newVal => {
                            return await NESet({
                                ne_id,
                                parameter: {
                                    ramanChannelEntry: {
                                        instance: rowData.instance,
                                        ramanChannelPumpCtrl: newVal ? 0 : 1
                                    }
                                }
                            });
                        }),
                    onCell: (_, index) => {
                        if (index === 0) {
                            return {
                                rowSpan: dataList?.length
                            };
                        }
                        return {
                            rowSpan: 0
                        };
                    }
                }
            ],
            getData: async () => {
                const data = [];
                (
                    await NEGet({
                        ne_id,
                        parameter: {ramanChannelEntry: {}}
                    })
                )?.ramanChannelEntry?.forEach?.(item => {
                    data.push({
                        instance: item.instance,
                        no: item.ramanChannelIndex,
                        // "port-name": item.ramanChannelWorkMode === "1" ? "IN" : "OUT",
                        "optical-power": (item.ramanChannelSignalPower / 10).toFixed(2),
                        "config-gain": (item.ramanChannelConfigGain / 10).toFixed(1),
                        "actual-gain": (item.ramanChannelOutputGain / 10).toFixed(1),
                        "pump-control": item.ramanChannelPumpCtrl === "0"
                    });
                });
                willUpdateDataList(data, "RAMAN_BOX");
            }
        }
    };

    const tableConfig = TableConfigs[type] ?? {};
    const columns =
        tableConfig.columns?.map?.(i => ({
            ...i,
            title:
                (labelList[i.label] ?? getText[i.label] ?? labelList[i.dataIndex] ?? getText(i.dataIndex)) +
                (i.unit ? ` (${i.unit})` : "")
        })) ?? [];

    const loadDate = async () => {
        try {
            await tableConfig?.getData?.();
        } catch (e) {
            console.log(e);
            willUpdateDataList([]);
            setLoading(false);
        }
    };

    useEffect(() => {
        if (needLoadingState) {
            setDataList([]);
            setLoading(true);
        }

        loadDate().then(() => {
            setLoading(false);
        });
    }, [data]);

    const titleConfig = {
        chassis: "Slot List",
        RAMAN_BOX: "Channel"
    };

    return (
        <Card loading={!type} title={titleConfig[type] ?? "Port List"} className={styles.card}>
            <CustomTable
                type="port_list"
                initColumns={columns}
                initDataSource={dataList}
                scroll={false}
                loading={loading}
            />
        </Card>
    );
};
export default PortList;
