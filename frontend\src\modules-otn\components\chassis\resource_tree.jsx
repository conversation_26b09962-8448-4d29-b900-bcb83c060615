import {Modal, Tree, Dropdown} from "antd";
import {useSelector} from "react-redux";
import {useState} from "react";
import {openModalEdit} from "@/modules-otn/components/form/edit_form";
import {openModalRpc} from "@/modules-otn/components/form/edit_rpc";
import {openModalTable} from "@/modules-otn/components/form/edit_table";
import {openModalCreate} from "@/modules-otn/components/form/create_form";
import {apiEditRpc} from "@/modules-otn/apis/api";
import {openContainerEdit} from "@/modules-otn/components/form/edit_form_container";
import useUserRight from "@/modules-otn/hooks/useUserRight";
import {openCustomTable} from "@/modules-otn/components/form/edit_table_custom";
import {openEditComponent} from "@/modules-otn/components/form/edit_component";

const ResourceTree = ({
    treeData,
    ne_id,
    updateSelectedTreeNode,
    updateSelectedNode,
    dbClickEnable = true,
    contextMenuEnable = true,
    defaultSelected
}) => {
    const [menuItems, setMenuItems] = useState([]);
    const {neTypeMap} = useSelector(state => state.neName);
    const {labelList} = useSelector(state => state.languageOTN);
    const [selectedKeys, setSelectedKeys] = useState(defaultSelected ?? "");
    const readyOnlyRight = useUserRight();
    const popupContextMenu = e => {
        const nodeValues = e.node;
        const type = nodeValues?.objectType;
        const menuItems = [];
        if (type === "aps") {
            menuItems.push({
                label: (
                    <a
                        onClick={() => {
                            openModalRpc(
                                ne_id,
                                "switch-olp",
                                neTypeMap[ne_id],
                                `Switch OLP (${nodeValues.title})`,
                                {name: nodeValues.title},
                                ["name"],
                                null,
                                null,
                                null,
                                readyOnlyRight.disabled
                            );
                        }}
                    >
                        Switch OLP
                    </a>
                ),
                key: "switch-olp"
            });
        } else if (type === "channel" && nodeValues.title.startsWith("ODU") && nodeValues?.delayTestMode) {
            menuItems.push({
                label: (
                    <a
                        onClick={() => {
                            openModalRpc(
                                ne_id,
                                "get-oduk-delay",
                                neTypeMap[ne_id],
                                `Get Delay Result (${nodeValues.title})`,
                                {index: nodeValues.id},
                                ["index"],
                                null,
                                null,
                                null,
                                readyOnlyRight.disabled
                            );
                        }}
                    >
                        Get ODUk Delay
                    </a>
                ),
                key: "get-oduk-delay"
            });
        } else if (type === "interface") {
            if (!nodeValues.title.endsWith("ETH")) {
                const {children} = e.node;
                if (
                    (children.length === 0 || children[0].entityType !== "address") &&
                    ["NMInterface", "OSCInterface"].includes(e.node.entityType)
                ) {
                    menuItems.push({
                        label: (
                            <a
                                onClick={() => {
                                    openModalCreate({
                                        categoryName: "ipv4-address",
                                        type: neTypeMap[ne_id],
                                        title: `${labelList.create}IPv4 - ${nodeValues.title}`,
                                        keys: nodeValues.title,
                                        ne_id,
                                        callback: () => {}
                                    });
                                }}
                            >{`${labelList.create} IPv4`}</a>
                        ),
                        key: "ipv4-address"
                    });
                }

                if (["NMInterface", "OSCInterface"].includes(e.node.entityType)) {
                    menuItems.push({
                        label: (
                            <a
                                onClick={() => {
                                    openModalRpc(
                                        ne_id,
                                        "set-neighbor-ip",
                                        neTypeMap[ne_id],
                                        "Neighbor IP",
                                        {},
                                        [],
                                        null,
                                        null,
                                        null,
                                        readyOnlyRight.disabled
                                    );
                                }}
                            >
                                Neighbor IP
                            </a>
                        ),
                        key: "set-neighbor-ip"
                    });
                }
                if (!["softwareLoopback"].includes(e.node.entityType)) {
                    menuItems.push({
                        label: (
                            <a
                                onClick={() => {
                                    openModalEdit(
                                        "lldp-interface",
                                        nodeValues.title,
                                        neTypeMap[ne_id],
                                        `ne:${neTypeMap[ne_id]}:interface1:${ne_id}:${nodeValues.title}`,
                                        ne_id,
                                        null,
                                        null,
                                        readyOnlyRight.disabled
                                    );
                                }}
                            >
                                LLDP Interface
                            </a>
                        ),
                        key: "lldp-interface"
                    });
                    menuItems.push({
                        label: (
                            <a
                                onClick={() => {
                                    openModalTable(
                                        "neighbor",
                                        nodeValues.title,
                                        neTypeMap[ne_id],
                                        `${nodeValues.title} - LLDP Neighbors`,
                                        {
                                            width: "98%"
                                        },
                                        ne_id,
                                        `ne:5:interface1:${ne_id}:${nodeValues.title}`,
                                        null,
                                        null,
                                        "lldp-interface"
                                    );
                                }}
                            >
                                LLDP Neighbors
                            </a>
                        ),
                        key: "neighbor"
                    });
                }
            }
        } else if (type === "ipv4-address") {
            menuItems.push({
                label: (
                    <a
                        onClick={() => {
                            Modal.confirm({
                                title: `${labelList.delete_confirm + nodeValues.title}?`,
                                okText: labelList.ok,
                                cancelText: labelList.cancel,
                                centered: true,
                                onOk: async () => {
                                    await apiEditRpc({
                                        ne_id,
                                        params: {
                                            interfaces: {
                                                interface: {
                                                    name: nodeValues.parent,
                                                    subinterfaces: {
                                                        subinterface: {
                                                            ipv4: {
                                                                addresses: {
                                                                    address: {
                                                                        "@nc:operation": "delete",
                                                                        ip: nodeValues.title
                                                                    }
                                                                }
                                                            }
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    });
                                }
                            });
                        }}
                    >
                        {labelList.delete}
                    </a>
                ),
                key: "delete-ipv4-address"
            });
        } else if (nodeValues.title.startsWith("WSS")) {
            menuItems.push({
                label: (
                    <a
                        onClick={() => {
                            openModalTable(
                                "frequency-channel",
                                nodeValues.title,
                                neTypeMap[ne_id],
                                `${nodeValues.title} - Frequency Channels`,
                                {
                                    width: "80%",
                                    rowOperation: [{type: "delete"}],
                                    createEnable: true,
                                    refresh: true
                                }
                            );
                        }}
                    >
                        Frequency Channel
                    </a>
                ),
                key: "frequency-channel"
            });
        } else if (nodeValues.objectType === "channel-monitor") {
            menuItems.push({
                label: (
                    <a
                        onClick={() => {
                            openCustomTable(
                                "ocm-channel",
                                [nodeValues.title],
                                "5",
                                nodeValues.title,
                                {
                                    initColumns: [
                                        {dataIndex: "channel"},
                                        {dataIndex: "lower-frequency", unit: "MHz"},
                                        {dataIndex: "upper-frequency", unit: "MHz"},
                                        {dataIndex: "power", unit: "dBm"}
                                    ],
                                    formatData: data => {
                                        data.reverse();
                                        return data.map((i, index) => ({...i, channel: index + 1}));
                                    }
                                },
                                ne_id
                            );
                        }}
                    >
                        {labelList.test_result}
                    </a>
                ),
                key: "ocm-channel"
            });
        }

        menuItems.push({
            label: (
                <a
                    onClick={() => {
                        handleProperty(nodeValues);
                    }}
                >
                    {labelList.property}
                </a>
            ),
            key: "Property"
        });
        setMenuItems(menuItems);
    };

    function handleProperty(nodeValues, type = "") {
        if (nodeValues.title === "Port AD List") return;
        if (nodeValues.type === "container") {
            openContainerEdit(
                nodeValues.objectType,
                null,
                neTypeMap[ne_id],
                `ne:${neTypeMap[ne_id]}:${nodeValues.objectType}:${ne_id}`,
                labelList.system_information,
                ne_id
            );
            return;
        }
        if (nodeValues.id === "") return;
        if (type === "ETH") {
            openModalEdit(
                "tpid",
                nodeValues.id,
                neTypeMap[ne_id],
                `ne:${neTypeMap[ne_id]}:${nodeValues.objectType}:${ne_id}:${nodeValues.id}`,
                ne_id,
                null,
                null,
                readyOnlyRight.disabled
            );
        } else if (type === "pvid") {
            openModalEdit(
                "ptp-extension",
                nodeValues.id,
                neTypeMap[ne_id],
                `ne:${neTypeMap[ne_id]}:${nodeValues.objectType}:${ne_id}:${nodeValues.id}`,
                ne_id,
                null,
                null,
                readyOnlyRight.disabled
            );
        } else {
            openEditComponent(
                nodeValues.objectType,
                ["ipv4-address", "ipv6-address", "ipv4-neighbor", "ipv6-neighbor"].includes(nodeValues.objectType)
                    ? [nodeValues.parent, nodeValues.title]
                    : nodeValues.id,
                neTypeMap[ne_id],
                ne_id,
                null,
                null,
                readyOnlyRight.disabled,
                nodeValues.objectType === "channel" ? nodeValues.title : null,
                {
                    component: null
                },
                2
            );
        }
    }
    let isDBClick = false;
    return (
        <Dropdown menu={{items: menuItems}} trigger={["contextMenu"]}>
            <Tree
                treeData={treeData}
                // defaultExpandedKeys={["root"]}
                defaultExpandedKeys={[treeData[0].key]}
                rootStyle={{background: "transparent"}}
                showLine
                selectedKeys={[selectedKeys]}
                onRightClick={e => {
                    if (contextMenuEnable) {
                        if (e.node.key === "PortGroup") setMenuItems([]);
                        else popupContextMenu(e);
                    }
                }}
                blockNode
                onSelect={(selectNodes, node) => {
                    isDBClick = false;
                    setTimeout(() => {
                        if (isDBClick) return;
                        setSelectedKeys(selectNodes?.[0]);
                        if (selectNodes?.[0] === "PortGroup") {
                            updateSelectedTreeNode?.(null);
                            updateSelectedNode?.(null);
                            return;
                        }
                        updateSelectedTreeNode?.(node.selected ? selectNodes?.[0] : null);
                        updateSelectedNode?.(node.selected ? node?.selectedNodes?.[0] : null);
                    }, 300);
                }}
                onDoubleClick={(e, node) => {
                    isDBClick = true;
                    if (dbClickEnable) {
                        handleProperty(node);
                    }
                }}
            />
        </Dropdown>
    );
};
export default ResourceTree;
