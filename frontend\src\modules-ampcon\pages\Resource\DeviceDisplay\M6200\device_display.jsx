import {But<PERSON>, <PERSON>, Flex, message, Radio, Spin, Typography} from "antd";
import React, {forwardRef, useDeferredValue, useEffect, useRef, useState} from "react";
import {debounce} from "lodash";
import {useDispatch, useSelector} from "react-redux";
import {useRequest} from "ahooks";
import {chassisConfig, SIDES, LED_STATUS} from "./device_display_config";
import styles from "./device_display.module.scss";
import {normalizeNumber} from "@/modules-ampcon/utils/util";
import CardInfo from "../components/card_info";
import PortList from "../components/port_list";
import {getM6200Info, queryM6200Info} from "@/modules-ampcon/apis/m6200_api";
import {setSelectedItem, setTableFilter} from "@/store/modules/otn/mapSlice";
import {NULL_VALUE} from "../utils";
import Icon from "@ant-design/icons";
import {offLineSvg} from "@/modules-otn/utils/iconSvg";

const {Text} = Typography;

const LED_CLASS_MAP = {
    [LED_STATUS.GREEN]: styles.LED_ACTIVE,
    [LED_STATUS.RED]: styles.LED_CRITICAL,
    [LED_STATUS.GREEN_FLASHING]: styles.LED_GREEN_FLASHING,
    [LED_STATUS.RED_FLASHING]: styles.LED_RED_FLASHING,
    [LED_STATUS.GREEN_FLASHING_SLOW]: styles.LED_GREEN_FLASHING_SLOW,
    [LED_STATUS.RED_FLASHING_SLOW]: styles.LED_RED_FLASHING_SLOW
};
const portListHide = ["PSU", "FAN"];

const DeviceDisplay = () => {
    const {runAsync} = useRequest(getM6200Info, {manual: true});
    const {tableFilter} = useSelector(state => state.map);
    // const deferredTableFilter = useDeferredValue(tableFilter);
    const {selectedItem, onSelectItem} = useSelector(state => state.map);
    const selectedItemRef = useRef(selectedItem);
    // 0:面板 1: 背板 2: mux
    const [showView, setShowView] = useState(0);
    const [data, setData] = useState();
    const [cardData, setCardData] = useState();
    const [portData, setPortData] = useState();
    const [loading, setLoading] = useState(false);
    const chassisRef = useRef();
    const chassisWrapRef = useRef();
    const containerRef = useRef();
    const [offLine, setOffLine] = useState(false);
    const [powerOff, setPowerOff] = useState(false);

    const syncData = async ip => {
        if (ip) {
            setLoading(true);
            try {
                await queryM6200Info(ip).then(res => {
                    const hasData = res?.data && Object.keys(res?.data || {}).length > 0;
                    const hasIPInfo = res?.data?.IP && res.data.IP.length > 0;

                    // 离线判断
                    if (!hasData || !hasIPInfo || res.errorCode !== 0) {
                        setData(null);
                        setCardData(null);
                        setPortData(null);
                        setOffLine(true); // 设备不可达
                        setPowerOff(false);
                        if (res.errorCode !== 0) {
                            message.error(`${ip} synchronize failed`);
                        }
                        return;
                    }

                    message.success(`${ip} synchronize succeeded`);

                    const powerOffState = false;

                    // 处理设备数据
                    const _data = transformData(res.data);
                    const _cardData = transformCardData(res.data);
                    const _portData = transformPortData(res.data);
                    let slotIndex = "0";
                    let type = "chassis";
                    if (tableFilter.resource) {
                        slotIndex = tableFilter.resource.value;
                        type = tableFilter.resource.type;
                    }

                    setData(_data);
                    setCardData({
                        ne_id: tableFilter.id,
                        type,
                        neData: _cardData[slotIndex],
                        nmuData: _cardData[0],
                        slotIndex,
                        deviceType: "M6200"
                    });
                    setPortData({
                        ne_id: tableFilter.id,
                        type,
                        neData: _portData[slotIndex],
                        slotIndex,
                        deviceType: "M6200"
                    });

                    // 设置状态
                    setOffLine(false);
                    setPowerOff(powerOffState);
                });
            } catch (error) {
                // 网络错误或其他异常，设备不可达
                message.error(`${ip} synchronize failed`);
                console.log(error);
                setData(null);
                setCardData(null);
                setPortData(null);
                setOffLine(true);
                setPowerOff(false);
            } finally {
                setLoading(false);
            }
        }
    };

    const sync = () => {
        syncData(selectedItem?.value?.host).then();
    };

    useEffect(() => {
        updateData().then();
        if (selectedItemRef.current.id !== selectedItem.id) {
            setShowView(0);
        }
    }, [selectedItem]);

    useEffect(() => {
        selectedItemRef.current = selectedItem;
    }, [selectedItem]);

    const transformCardData = data => {
        console.log("M6200 transformCardData:", data);
        console.log("M6200 boardInfos:", data?.boardInfos);
        const result = [];
        data?.boardInfos?.forEach(board => {
            const {slotIndex} = board;
            result[slotIndex] = board;
        });
        // 找到NMU板卡的位置
        let nmuSlotIndex = 0;
        for (let i = 0; i < result.length; i++) {
            if (
                result[i] &&
                (result[i].slotActualCardType === "M6200-NMU" || result[i].slotRequiredCardType === "M6200-NMU")
            ) {
                nmuSlotIndex = i;
                break;
            }
        }

        if (data) {
            // 创建包含设备基本信息的NMU数据
            const nmuBoard = result[nmuSlotIndex] || {};
            const deviceInfo = {
                ...nmuBoard,
                "Slot number": data.slot_number || data["Slot number"] || 8,
                SN: data?.sn || nmuBoard?.sn,
                Model: data?.model || nmuBoard?.model || "M6200",
                "Production date": data.production_date || data["Production date"] || nmuBoard?.production_date,
                "Software version": data.software_version || data["Software version"] || nmuBoard?.software_version,
                "Hardware version": data.hardware_version || data["Hardware version"] || nmuBoard?.hardware_version,
                IP: data?.IP || data?.ip,
                temperature: data?.temperature || nmuBoard?.temperature,
                Temperature: data?.temperature || nmuBoard?.temperature,
                Power: data?.power || nmuBoard?.power,
                description: data?.description || nmuBoard?.description
            };

            // 将设备信息存储在索引0，用于Chassis Info
            result[0] = deviceInfo;
            // 如果NMU不在索引0，也更新NMU的实际位置
            if (nmuSlotIndex !== 0) {
                result[nmuSlotIndex] = deviceInfo;
            }
        }

        console.log("M6200 transformCardData result:", result);
        return result;
    };

    const transformPortData = data => {
        const result = {
            1: [],
            2: [],
            3: [],
            4: [],
            5: [],
            6: [],
            7: [],
            8: [],
            0: []
        };
        if (data?.boardInfos) {
            console.log("M6200 transformPortData - boardInfos:", data.boardInfos);
            const slotListData = data.boardInfos
                .filter(board => board.slotIndex !== 0) // 排除NMU槽位
                .map(board => {
                    let cardType = board.slotActualCardType || board.boardType;
                    // 处理板卡类型名称，移除设备型号前缀
                    if (cardType && cardType.startsWith(`${data.model}-`)) {
                        cardType = cardType.substring(`${data.model}-`.length);
                    }
                    // 特殊类型映射
                    if (cardType === "otu6h") {
                        cardType = "OEO100G";
                    }

                    return {
                        "slot-no": board.slotIndex,
                        empty: board.slotActualCardType || board.boardType ? "Present" : "Absent",
                        "equipment-mismatch": "Normal",
                        "card-name": cardType || "Empty",
                        "slot-note": board.slotDesc || ""
                    };
                });
            console.log("M6200 transformPortData - slotListData:", slotListData);
            result[0] = slotListData;
        }

        data?.boardInfos?.forEach(board => {
            const {slotIndex} = board;
            if (slotIndex === 0) return; // 跳过NMU的端口数据

            let portsData = [];
            const portsDataArray = Object.values(board.ports_data || {});

            portsData = portsDataArray.map(item => {
                const newItem = {};
                Object.keys(item).forEach(key => {
                    const newKey = key.toLowerCase().replace(/[ _]/g, "-");
                    if (newKey === "slot-no" || newKey === "no") {
                        newItem[newKey] = item[key];
                    } else if (newKey === "port-note") {
                        newItem[newKey] = item[key];
                    } else {
                        newItem[newKey] = normalizeNumber(item[key]) || NULL_VALUE;
                    }
                });
                return newItem;
            });
            result[slotIndex] = portsData;
        });

        return result;
    };

    const transformData = data => {
        console.log("M6200 transformData input:", data);
        return (
            SIDES.map(side => {
                const children = data?.boardInfos
                    ?.filter(card => side === chassisConfig.M6200[card.slotIndex]?.side)
                    .map(card => {
                        console.log("M6200 transformData processing card:", card);
                        const type = card.boardType;
                        const {side, ...style} = chassisConfig.M6200[card.slotIndex];
                        let cardType = card.slotActualCardType;
                        if (cardType && cardType.startsWith(`${data.model}-`)) {
                            cardType = cardType.substring(`${data.model}-`.length);
                        }
                        if (cardType === "otu6h") {
                            cardType = "OEO100G";
                        }
                        if (!cardType) {
                            cardType = card.boardType;
                        }
                        const className = `${styles.CARD_common}  ${styles[`CARD_${cardType}`]}`;
                        const title = `Name: ${cardType}-1-${card.slotIndex}`;
                        const getValueByPath = path => {
                            return path
                                ? path.split(".").reduce((acc, key) => {
                                      // 如果 key 包含数组索引
                                      const match = key.match(/^(\w+)\[(\d+)]$/);
                                      if (match) {
                                          const [, prop, index] = match;
                                          return acc?.[prop]?.[parseInt(index)];
                                      }
                                      return acc?.[key];
                                  }, card)
                                : null;
                        };
                        const cardItems = Object.entries(chassisConfig[`${data.model}-${cardType}`] || {}).map(
                            ([key, {top, bottom, left, transform, status}]) => {
                                let className = key.startsWith("LED") ? styles.LED_COMMON : "";
                                if (key.startsWith("LED") && status?.key) {
                                    const statusValues = {};
                                    statusValues.key = getValueByPath(status.key);
                                    Object.entries(status).forEach(([fieldName, fieldPath]) => {
                                        if (fieldName !== "judge" && fieldName !== "key") {
                                            statusValues[fieldName] = getValueByPath(fieldPath);
                                        }
                                    });
                                    if (status?.judge) {
                                        className += ` ${LED_CLASS_MAP[status.judge(statusValues)] || ""}`;
                                    }
                                } else if (key.startsWith("PORT")) {
                                    if (status?.key) {
                                        const keyValue = getValueByPath(status.key);
                                        if (status?.judge({key: keyValue})) {
                                            className = styles.PORT2;
                                        }
                                    }
                                }
                                const type = key.startsWith("LED") ? "led" : "port";
                                const style = {top, bottom, left, transform};
                                const title = `Name: ${key}`;
                                return {
                                    title,
                                    type,
                                    className,
                                    style
                                };
                            }
                        );
                        return {
                            slotIndex: card?.slotIndex,
                            title,
                            type,
                            className,
                            style,
                            cardItems
                        };
                    });
                return {
                    className: `${styles[`chassis${side.charAt(0).toUpperCase() + side.slice(1)}`]}`,
                    children,
                    nodeType: "chassis",
                    type: "chassis"
                };
            }) ?? []
        );
    };

    const updateData = async () => {
        const neIP = selectedItem?.value?.host;
        if (!neIP) return;

        try {
            const neData = (await runAsync(neIP)).data;
            const powerOffState = false;

            const _data = transformData(neData);
            const _cardData = transformCardData(neData);
            const _portData = transformPortData(neData);
            let slotIndex = "0";
            let type = "chassis";
            if (tableFilter.resource) {
                slotIndex = tableFilter.resource.value;
                type = tableFilter.resource.type;
            }

            setData(_data);
            setCardData({
                ne_id: tableFilter.id,
                type,
                neData: _cardData[slotIndex],
                nmuData: _cardData[0],
                slotIndex,
                deviceType: "M6200"
            });
            setPortData({
                ne_id: tableFilter.id,
                type,
                neData: _portData[slotIndex],
                slotIndex,
                local_info: neData.local_info,
                deviceType: "M6200"
            });

            setOffLine(false);
            setPowerOff(powerOffState);
        } catch (error) {
            console.error("Failed to fetch device data:", error);
            setData(null);
            setCardData(null);
            setPortData(null);
            setOffLine(true);
            setPowerOff(false);
        }
    };

    useEffect(() => {
        if (chassisWrapRef.current && chassisRef.current) {
            chassisRef.current.style = `zoom: ${(chassisWrapRef?.current.clientWidth || 0) / 1856}`;
        }

        const observer = new ResizeObserver(
            debounce(() => {
                if (chassisRef.current)
                    chassisRef.current.style = `zoom: ${(chassisWrapRef?.current.clientWidth || 0) / 1856}`;
            }, 5)
        );
        observer.observe(containerRef.current);

        return () => {
            observer.disconnect();
        };
    }, []);
    const bt = ["Front", "Rear"];

    return (
        <div className={styles.wrap}>
            {loading && (
                <div className={styles.loading}>
                    <Spin />
                </div>
            )}
            <div className={styles.deviceDisplay} style={{flex: 1}}>
                <div className={styles.viewDisplay}>
                    <Card style={{margin: "0 24px"}} ref={containerRef}>
                        <Flex vertical gap="middle" ref={chassisWrapRef}>
                            <Flex justify="space-between" style={{marginTop: "12px"}}>
                                {!offLine ? (
                                    <Radio.Group
                                        value={showView}
                                        onChange={e => {
                                            setShowView(e.target.value);
                                        }}
                                        className={styles.customRadioGroup}
                                    >
                                        {bt.map((item, i) => {
                                            return (
                                                <Radio.Button value={i} key={i}>
                                                    {item}
                                                </Radio.Button>
                                            );
                                        })}
                                    </Radio.Group>
                                ) : (
                                    <span /> /* 占位符，以确保按钮容器被推到右侧 */
                                )}
                                <div>
                                    <Button onClick={sync} style={{marginLeft: "10px"}}>
                                        Synchronize
                                    </Button>
                                </div>
                            </Flex>
                            <ViewCard
                                showView={showView}
                                selectedItem={selectedItem}
                                data={data}
                                ref={chassisRef}
                                offLine={offLine}
                                powerOff={powerOff}
                            />
                        </Flex>
                    </Card>
                </div>
                {!offLine && cardData && (
                    <>
                        {/* 始终显示Chassis Info */}
                        <CardInfo
                            data={{
                                ...cardData,
                                type: "chassis",
                                neData: cardData?.nmuData,
                                nmuData: cardData?.nmuData
                            }}
                        />

                        {/* 只有当选中的不是chassis时才显示选中板卡的信息 */}
                        {cardData?.type !== "chassis" && <CardInfo data={cardData} />}

                        {/* 根据板卡类型决定是否显示Port List */}
                        {!portListHide.includes(portData?.type) && <PortList data={portData} />}
                    </>
                )}
            </div>
        </div>
    );
};

const ViewCard = forwardRef(({showView, selectedItem, data, offLine, powerOff}, ref) => {
    const dispatch = useDispatch();
    const {name, className, children, nodeType, type} = data?.[showView] ?? {};
    // 指向选中的模块元素
    const selectedPortRef = useRef({style: {}});
    let isDBClick;

    const setSelectPort = selectPort => {
        if (selectPort) {
            dispatch(
                setSelectedItem({
                    ...selectedItem,
                    selectPort: {type: selectPort.type, value: selectPort.value},
                    resource: {type: selectPort.type, value: selectPort.value}
                })
            );
            dispatch(
                setTableFilter({
                    type: "NODE_NE",
                    id: selectedItem.value.ne_id,
                    resource: {type: selectPort.type, value: selectPort.value}
                })
            );
        } else {
            const newSelect = {...selectedItem};
            delete newSelect.selectPort;
            dispatch(setSelectedItem(newSelect));
            dispatch(setTableFilter({type: "NODE_NE", id: selectedItem.value.ne_id}));
        }
    };

    // 机框图模块MouseMove
    const handleModuleMouseMove = e => {
        e.stopPropagation();
        e.target.style.boxShadow = "inset 0px 0px 80px 4px rgb(0 255 0 / 30%)";
    };
    // 机框图模块MouseOut
    const handleModuleMouseOut = e => {
        e.stopPropagation();

        if (e.target.title === selectedPortRef.current?.title) {
            return;
        }
        e.target.style.boxShadow = "";
    };

    // 机框图模块Click
    const handleModuleClick = e => {
        isDBClick = false;
        const {dataset} = e.currentTarget;
        e.stopPropagation();
        if (e.button === 0) {
            // console.log("Left button clicked");
            // setOpenDropdown(false);
        }
        if (!dataset.type) {
            return;
        }

        setTimeout(() => {
            if (!isDBClick) {
                if (e.target.title === selectedPortRef.current?.title) {
                    selectedPortRef.current.style.boxShadow = "";
                    selectedPortRef.current = {style: {}};
                    return;
                }
                e.target.style.boxShadow = "inset 0px 0px 80px 4px rgb(0 255 0 / 30%)";
                selectedPortRef.current.style.boxShadow = "";
                selectedPortRef.current = e.target;
                setSelectPort(dataset);
            }
        }, 300);
    };
    return (
        <Flex vertical align="center" justify="center" gap="24px">
            {!offLine && (
                <div
                    ref={ref}
                    className={className}
                    onMouseMove={handleModuleMouseMove}
                    onMouseOut={handleModuleMouseOut}
                    onClick={handleModuleClick}
                    onBlur={() => {}}
                    data-type="chassis"
                    data-value="0"
                    title="CHASSIS"
                >
                    {children?.map(({title, type, style, className, cardItems, slotIndex, nmuItems}) => {
                        if (slotIndex !== 0) {
                            return (
                                <div
                                    key={slotIndex}
                                    style={style}
                                    className={className}
                                    onClick={handleModuleClick}
                                    data-type={type}
                                    data-value={slotIndex}
                                    title={title}
                                >
                                    {cardItems?.map(({title, type, className, style}, index) => (
                                        <div
                                            key={index}
                                            className={className}
                                            style={style}
                                            data-type={type}
                                            data-value={slotIndex}
                                            title={title}
                                        />
                                    ))}
                                </div>
                            );
                        }
                        // return nmuItems?.map(({title, style, className}, index) => (
                        //     <div key={index} style={style} className={className} />
                        // ));
                    })}
                </div>
            )}
            <div
                style={{
                    width: "100%",
                    textAlign: "center",
                    fontSize: 14,
                    paddingBottom: "20px"
                }}
            >
                <Text>{`M6200 (${selectedItem?.value?.host})`}</Text>
                {offLine && <Icon component={offLineSvg} style={{marginLeft: 4}} />}
            </div>
        </Flex>
    );
});

export default DeviceDisplay;
