/* eslint-disable */
import {request} from "@/utils/common/request";

const baseUrl = "/ampcon/dc_blueprint";

export function fetchFabricInfo(page, pageSize, filterFields = [], sortFields = [], searchFields = {}) {
    return request({
        url: `${baseUrl}/fabric_template/list`,
        method: "POST",
        data: {
            filterFields,
            sortFields,
            searchFields,
            page,
            pageSize
        }
    });
}

export function cloneFabricInfo(data) {
    return request({
        url: `${baseUrl}/fabric_template/clone`,
        method: "POST",
        data
    });
}

export function delFabricInfo(data) {
    return request({
        url: `${baseUrl}/fabric_template/delete`,
        method: "POST",
        data
    });
}

export function addFabricInfo(data) {
    return request({
        url: `${baseUrl}/fabric_template/save`,
        method: "POST",
        data
    });
}
export function viewFabricInfo(data) {
    return request({
        url: `${baseUrl}/fabric_template/view`,
        method: "POST",
        data
    });
}