import pyexcel
from server.db.models.general import CliTree
from server.db import session as db
from server.db.models import inventory

def parse_cli(path):

    clear_cli_tree()

    db_session = db.get_session()
    db_session.begin()
    sheet = pyexcel.get_sheet(file_name=path)
    l = len(sheet[0])
    cur_prefix = [0]*l
    cur_id = 1
    cli_node = CliTree()
    cli_node.id = 0
    cli_node.name = 'root'
    cli_node.description = 'root'
    db_session.add(cli_node)

    for row in sheet:
        flag = False
        for c in sheet.column_range():
            cli_node = CliTree()
            cell_value = row[c].strip()

            if cell_value == '':
                if flag:
                    break
                else:
                    continue
            else:
                flag = True
                name, _, desc = cell_value.partition(' ')
                desc = desc.strip() if desc != '' else ''
                cli_node.description = desc
                cli_node.level = c
                node_id = cur_id
                cur_id += 1
                cli_node.id = node_id
                cli_node.pid = 0 if c == 0 else cur_prefix[c - 1]
                cur_prefix[c] = node_id

                if '<' in name or '[' in name or 'param' in name:
                    cli_node.is_param = True
                    if '<' in cell_value:
                        name = name.strip('<>')
                        cli_node.name = 'param:' + name
                        cli_node.param_type = name
                    elif '[' in name:
                        cli_node.name = 'param:int'
                        cli_node.param_type = 'int'
                        cli_node.param_check = name
                    elif 'param' in name:
                        cli_node.name = name
                        cli_node.param_type = name.replace('param:', '').strip()
                else:
                    cli_node.name = name

            print(cli_node)
            db_session.add(cli_node)

    db_session.commit()


def clear_cli_tree():
    db_session = db.get_session()
    db_session.query(CliTree).delete()


def set_tail_back(start, cur_prefix, default=0):
    for i in range(start, len(cur_prefix)):
        cur_prefix[i] = default


def get_node_id(cell_index, cur_prefix):
    if cur_prefix[cell_index] == 0:
        if cell_index == 0:
            return 1
        return cur_prefix[cell_index - 1]*10 + 1

    return cur_prefix[cell_index] + 1


if __name__ == '__main__':
    from server import cfg

    cfg.CONF(default_config_files=['../automation.ini'])
    inventory.db.create()
    parse_cli('cli_tree.xlsx')
