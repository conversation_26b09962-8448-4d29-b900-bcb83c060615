.otnView {
  display: grid;
  height: 100%;
  width: 100%;
  gap: 18px 24px;
  grid-template-columns: repeat(12, 1fr);
  grid-template-rows: 0.5fr 1fr 1.5fr;
  > div {

      &:nth-child(1),
      &:nth-child(2),
      &:nth-child(3),
      &:nth-child(4) {
          grid-column: span 3;
      }

      &:nth-child(6),
      &:nth-child(8),
      &:nth-child(7) {
          grid-column: span 4;
          grid-row: 2;
      }

      &:nth-child(5) {
          grid-column: 1 / -1;
          grid-row: 3;
      }
  }

    &_echartCard {
      flex: 1.97;
      overflow: hidden;
      margin-bottom: 18px;
    }

    &_alarmCard {
      flex: 2.4;
      overflow: hidden;
      margin-bottom: 18px;

      &_alarmBody {
        flex: 1;
        display: flex;
        padding: 14px 0;
        height: calc(100% - 10px);
        vertical-align: center;
        flex-direction: column;
        justify-content: flex-start;
        overflow: auto;

        &_head {
          display: flex;
          height: 40px;
          background-color: #F2F4F5;
        }

        &_body {
          display: flex;
          flex-direction: row;
          height: 49px;
          border-bottom: 1px solid #E7E7E7
        }

        &_td {
          padding-left: 16px;
          padding-right: 16px;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          align-content: center;
        }

        &_td1 {
          width: 120px;
        }

        &_td2 {
          flex: 1;
          border-left: 0;
        }

        &_alarm {
          display: flex;
          flex-direction: row;
          align-items: flex-start;
          padding-bottom: 12px;

          &_dot {
            width: 10px;
            height: 10px;
            margin-top: 7px;
            border-radius: 50%;
          }

          &_text {
            flex: 1;
            padding-left: 8px;
          }
        }
      }
    }

    &_custom_title {
      font-size: 18px;
      font-weight: 700;
    }

    &_neRight {
      display: flex;
      align-items: center;
    }

    &_neOnlineCricle {
      width: 8px;
      height: 8px;
      background: #78D047;
      border-radius: 50%;
    }

    &_neOfflineCricle {
      width: 8px;
      height: 8px;
      background: #C5CACD;
      border-radius: 50%;
    }

    &_onlineText {
      font-size: 14px;
      font-weight: 400;
      color: #929A9E;
      margin: 0 24px 0 6px;
    }

    &_sizeText {
      font-weight: 700;
      font-size: 18px;
      color: #212519;
    }


    &_header {

      &_cardBody {
        height: 100%;
        width: 100%;
        display: flex;
        justify-content: space-around;
        align-items: center;
        overflow: hidden;
      }

      &_value {
        font-weight: 700;
        font-size: 20px;
        text-align: center;

      }

      &_title {
        color: #929A9E;
        font-size: 16px;
        text-align: left;
      }
    }

    &_alarms_value {
      display: flex;
      font-weight: 700;
      font-size: 20px;
    }

    &_alarms_number {
      margin-left: 8px;
    }

    &_footer {
      font-size: 14px;
      display: flex;
      justify-content: center;
      overflow: hidden;
    }
    &_recentAlarms{
      :global{
        .ant-card-body{
          overflow: hidden;
        }
      }
    }
  }

  .echartsContainer {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-around;

    &_echart {
      height: 96%;
      min-height: 114px;
    }

    &_histogramchart {
      height: 72px;
      width: 110px
    }
  }
  .globalView {
    &_section {
      display: flex;
      flex: 3.4;
    }

    &_cpuUtilization {
      height: 100%;
      display: flex;
      flex-direction: column;
      justify-content: space-between;

      &_alarmBody {
        height: 100%;
        padding: 0 20px
      }
    }
  }

