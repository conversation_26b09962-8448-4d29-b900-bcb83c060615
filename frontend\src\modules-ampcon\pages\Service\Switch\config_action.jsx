import {message, Dropdown, Input, Spin, Upload} from "antd";
import {DownOutlined, UpOutlined} from "@ant-design/icons";
import {AmpConCustomModal} from "@/modules-ampcon/components/custom_table";
import {useState} from "react";
import {queryAgentConfig, queryConfig} from "@/modules-ampcon/apis/dashboard_api";
import {queryDeployConfig} from "@/modules-ampcon/apis/config_api";
import {backupSNConfig} from "@/modules-ampcon/apis/config_backup_api";
import {uploadConfig} from "@/modules-ampcon/apis/rma_api";
import {confirmModalAction} from "@/modules-ampcon/components/custom_modal";

const ConfigAction = ({record, isForbidden}) => {
    const [configModal, setConfigLogModal] = useState(false);
    const [modalTitle, setModalTitle] = useState("");
    const [configContent, setConfigContent] = useState("");
    const [spinning, setSpinning] = useState(false);
    const [hoverStatus, setHoverStatus] = useState(false);

    const renderModal = value => {
        return <Input.TextArea value={value} autoSize={{minRows: 20, maxRows: 20}} />;
    };

    const backupConfig = async record => {
        setSpinning(true);
        backupSNConfig(record.sn, record.mgt_ip)
            .then(response => {
                setSpinning(false);
                if (response.status !== 200) {
                    message.error(response.info);
                } else {
                    message.success(response.info);
                }
            })
            .catch(() => {
                setSpinning(false);
                message.error("Backup failed");
            });
    };

    const handleUploadConfig = async ({file}) => {
        await uploadConfig(file, record.sn, record.mgt_ip)
            .then(response => {
                if (response.status === 200) {
                    message.success(response.info);
                } else {
                    message.error(response.info);
                }
            })
            .catch(error => {
                message.error(error);
            });
    };

    const dropdownMenu = [
        record.status !== "Imported"
            ? {
                  label: "Agent Config",
                  onClick: async () => {
                      setModalTitle("Agent Config");
                      setConfigLogModal(true);
                      const content = await queryAgentConfig(record.sn);
                      setConfigContent(content);
                  }
              }
            : null,
        record.status !== "Imported"
            ? {
                  label: "Init Deploy Config",
                  onClick: async () => {
                      setModalTitle("Init Deploy Config");
                      setConfigLogModal(true);
                      const response = await queryDeployConfig(record.sn);
                      setConfigContent(response.content);
                      // console.log(record);
                  }
              }
            : null,
        // 状态为'Provisioning Success', 'DECOM', 'DECOM-Init', 'DECOM-Pending', 'RMA'显示
        ["Provisioning Success", "DECOM", "DECOM-Init", "DECOM-Pending", "RMA"].includes(record.status)
            ? {
                  label: "Current Config",
                  onClick: async () => {
                      setModalTitle("Current Config");
                      setConfigLogModal(true);
                      const content = await queryConfig(`${record.sn}.rma`);
                      setConfigContent(content);
                  }
              }
            : null,
        isForbidden
            ? null
            : {
                  label: "Backup Config",
                  onClick: () => {
                      confirmModalAction(`Please confirm you want to backup config for switch ${record.sn}`, () => {
                          backupConfig(record);
                      });
                  }
              },
        isForbidden
            ? null
            : {
                  label: (
                      <Upload customRequest={handleUploadConfig} showUploadList={false}>
                          Upload Config
                      </Upload>
                  )
              }
    ];

    return (
        <>
            {!dropdownMenu.every(e => e === null) ? (
                <Dropdown menu={{items: dropdownMenu}} trigger={["hover"]} onOpenChange={val => setHoverStatus(val)}>
                    <a onClick={e => e.preventDefault()}>
                        <div>Config View {hoverStatus ? <UpOutlined /> : <DownOutlined />}</div>
                    </a>
                </Dropdown>
            ) : null}
            {/* modal start */}
            <AmpConCustomModal
                title={modalTitle}
                isModalOpen={configModal}
                childItems={renderModal(configContent)}
                onCancel={() => {
                    setConfigLogModal(false);
                }}
                modalClass="ampcon-middle-modal"
            />
            <Spin spinning={spinning} tip="Loading..." size="large" fullscreen />
            {/* modal end */}
        </>
    );
};

export default ConfigAction;
