.globalView {
    display: grid;
    height: 100%;
    width: 100%;
    gap: 18px 24px;
    grid-template-columns: repeat(12, 1fr);
    grid-template-rows: 0.5fr 1fr 1.5fr;
    > div {

        &:nth-child(1),
        &:nth-child(2),
        &:nth-child(3),
        &:nth-child(4) {
            grid-column: span 3;
        }
    
        &:nth-child(6),
        &:nth-child(8),
        &:nth-child(7) {
            grid-column: span 4;
            grid-row: 2;
        }
    
        &:nth-child(5) {
            grid-column: 1 / -1;
            grid-row: 3;
        }
    }

    &_custom_title {
        font-size: 18px;
    }
    &_cpuUtilization {
        height: 100%;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
    }
    &_recentAlarms{
        :global{
          .ant-card-body{
            overflow: hidden;
          }
        }
        &_alarmBody {
            flex: 1;
            display: flex;
            padding: 14px 0;
            height: calc(100% - 10px);
            vertical-align: center;
            flex-direction: column;
            justify-content: flex-start;
            overflow: auto;
            &_alarmList {
                display: flex;
                flex-direction: row;
                align-items: flex-start;
                padding-bottom: 12px;
                &_dot {
                    width: 10px;
                    height: 10px;
                    margin-top: 7px;
                    border-radius: 50%;
                }
        
                &_text {
                flex: 1;
                padding-left: 8px;
                }
            }
        }
    }
}

.alarms {
    height: 100%;
    width: 100%;
    display: flex;
    justify-content: space-around;
    align-items: center;
    overflow: hidden;
    &_alarms_value {
        display: flex;
        font-weight: 700;
        font-size: 20px;
    }
    &_alarms_number {
        margin-left: 8px;
    }
    &_title {
        color: #929A9E;
        font-size: 16px;
        text-align: left;
    }
}

@media (min-width: 1921px) and (max-width: 2560px) {
    .globalView {
        &_headerCard {
            height: 20vh;
            canvas {
                // scale: 1.1;
            }
        }
        &_cpuUtilization {
            canvas {
                // scale: 0.9;
            }
        }
    }
}