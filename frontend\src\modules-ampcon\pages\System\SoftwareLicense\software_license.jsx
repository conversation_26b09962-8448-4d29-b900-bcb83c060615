// eslint-disable-next-line no-unused-vars
import {importWhiteSvg, refreshSvg, exportSvg, readSvg} from "@/utils/common/iconSvg";
// import styles from "@/modules-ampcon/pages/System/SoftwareLicense/LicenseManagement/license_management.module.scss";
import {Button, Card, Divider, Space, Radio, Form, Input, Row, message} from "antd";
import Icon from "@ant-design/icons/lib/components/Icon";
import {useState, useEffect} from "react";
import {AmpConCustomModal, AmpConCustomStaticTable} from "@/modules-ampcon/components/custom_table";
import {getLicenseInfo, importLicense, importLicenseFile, getLocalKey} from "@/modules-ampcon/apis/dashboard_api";
import TitleH2 from "@/modules-ampcon/components/title_h2";
// import {AmpConCustomModal, AmpConCustomStaticTable, confirmModalAction} from "@/modules-ampcon/components/custom_table";
import styles from "./software_license.module.scss";

export const ImportLicenseModal = ({isModalOpen, onCancelFunc}) => {
    const [formInstance] = Form.useForm();
    const [isShowCopy, setIsShowCopy] = useState(true);
    const [isShowAdd, setIsShowAdd] = useState(false);

    const onSubmit = async values => {
        let response = {};
        if (values.licensemethod === "Copy") {
            response = await importLicense(values.licensekey);
        } else {
            response = await importLicenseFile(values);
        }
        if (response.status !== 200) {
            message.error(response.msg);
        } else {
            message.success(response.msg);
            onCancel();
        }
    };

    const onCancel = () => {
        formInstance.resetFields();
        onCancelFunc();
    };

    useEffect(() => {
        formInstance.resetFields();
        setIsShowCopy(true);
        setIsShowAdd(false);
    }, [isModalOpen]);

    return (
        <AmpConCustomModal
            modalClass="ampcon-middle-modal"
            title="Import"
            onCancel={onCancel}
            childItems={
                <Form
                    layout="horizontal"
                    form={formInstance}
                    onFinish={onSubmit}
                    labelAlign="left"
                    labelCol={{span: 6}}
                >
                    <Form.Item name="licensemethod" label="License Method" initialValue="Copy">
                        <Radio.Group
                            defaultValue="Copy"
                            onChange={e => {
                                if (e.target.value === "Copy") {
                                    setIsShowCopy(true);
                                    setIsShowAdd(false);
                                    formInstance.setFieldValue("licensekey", "");
                                } else {
                                    setIsShowAdd(true);
                                    setIsShowCopy(false);
                                    formInstance.setFieldValue("licensekey", "");
                                }
                            }}
                        >
                            <Radio value="Copy">Copy License.txt</Radio>
                            <Radio value="Add">Add License.lic</Radio>
                        </Radio.Group>
                    </Form.Item>
                    {isShowCopy && (
                        <Form.Item
                            name="licensekey"
                            label="License Key"
                            rules={[{required: true, message: "Please input license key!"}]}
                        >
                            <Input.TextArea rows={10} style={{width: "280px"}} />
                        </Form.Item>
                    )}
                    {isShowAdd && (
                        <>
                            {" "}
                            <Form.Item
                                name="licensekey"
                                label="License Key"
                                rules={[{required: true, message: "Please select license file!"}]}
                                valuePropName="file"
                            >
                                <Input type="file" aria-required="true" style={{width: "280px"}} />
                            </Form.Item>
                        </>
                    )}
                </Form>
            }
            footer={
                <>
                    {/* eslint-disable-next-line no-nested-ternary */}
                    <Divider style={{marginTop: isShowCopy ? "32px" : isShowAdd ? "210px" : "0px"}} />
                    <Row justify="end">
                        <Space size={16}>
                            <Button className={styles.buttonWidth} onClick={onCancel}>
                                Cancel
                            </Button>
                            <Button type="primary" onClick={formInstance.submit} className={styles.buttonWidth}>
                                Apply
                            </Button>
                        </Space>
                    </Row>
                </>
            }
            isModalOpen={isModalOpen}
        />
    );
};

export const ObtainModal = ({isModalOpen, onCancelFunc}) => {
    // console.log("ObtainModal", isModalOpen);
    const [formInstance] = Form.useForm();
    const onCancel = () => {
        formInstance.resetFields();
        onCancelFunc();
    };

    const onSubmit = async values => {
        const element = document.createElement("a");
        element.setAttribute("download", "IdentificationCode.lic");
        const blob = new Blob([values.loaclkey], {type: "text/plain"});
        const fileUrl = URL.createObjectURL(blob);

        element.setAttribute("href", fileUrl);
        element.style.display = "none";
        document.body.appendChild(element);
        element.click();
        document.body.removeChild(element);
        onCancelFunc();
    };

    useEffect(() => {
        // formInstance.resetFields();
        getLocalKey().then(response => {
            if (response.status !== 200) {
                message.error(response.msg);
            } else {
                formInstance.setFieldValue("loaclkey", response.localKey);
            }
        });
    }, [isModalOpen]);

    return (
        <AmpConCustomModal
            modalClass="ampcon-middle-modal"
            title="Obtain Identification Code"
            onCancel={onCancel}
            childItems={
                <Form
                    layout="horizontal"
                    form={formInstance}
                    onFinish={onSubmit}
                    labelAlign="left"
                    labelCol={{span: 6}}
                >
                    <Form.Item name="loaclkey" label="Identification Code">
                        <Input.TextArea rows={10} style={{width: "280px"}} readOnly />
                    </Form.Item>
                </Form>
            }
            footer={
                <>
                    <Divider style={{marginTop: "52px", marginBottom: "20px", marginLeft: "-24px"}} />
                    <Row justify="end">
                        <Space size={16}>
                            <Button className={styles.buttonWidth} onClick={onCancel}>
                                Cancel
                            </Button>
                            <Button type="primary" onClick={formInstance.submit} className={styles.buttonWidth}>
                                Export
                            </Button>
                        </Space>
                    </Row>
                </>
            }
            isModalOpen={isModalOpen}
        />
    );
};

const SoftwareLisence = () => {
    const [isModalOpenImport, setIsModalOpenImport] = useState(false);
    const [isModalOpenObtain, setIsModalOpenObtain] = useState(false);
    // const [selectedLicenseFile, setSelectedLicenseFile] = useState(["All Licenses"]);

    const [licenseInfo, setLincenseInfo] = useState({});
    // const [tableData, setTableData] = useState([]);

    const licenseColumns = [
        {
            title: "License ID",
            dataIndex: "license_file",
            sorter: (a, b) => a.license_file.localeCompare(b.license_file)
        },
        {
            title: "License Type",
            dataIndex: "license_type",
            sorter: (a, b) => a.license_type.localeCompare(b.license_type)
        },
        {title: "Valid Date", dataIndex: "valid_date", sorter: (a, b) => a.valid_date.localeCompare(b.valid_date)},
        {
            title: "Expiration Date",
            dataIndex: "expiration_date",
            render: (_, record) => (record.license_type === "trial" ? record.expiration_date : "--")
        }
    ];

    const getLicenseInfoApi = async () => {
        const transformedData = [];
        const response = await getLicenseInfo();
        if (response.status !== 200) {
            message.error("Get License Info Failed");
        } else {
            for (const item of response.data) {
                transformedData.push({
                    id: item.license_id,
                    license_file: item.license_id,
                    license_type: item.license_type,
                    license_file_status: item.status.toLowerCase(),
                    valid_date: item.create_time,
                    expiration_date: item.expire_time
                });
            }
        }

        const data = {
            data: transformedData,
            total: transformedData.length
        };
        setLincenseInfo(data);
    };

    useEffect(() => {
        getLicenseInfoApi().then();
    }, []);

    return (
        <Card style={{paddingBottom: "20px", flex: 1}}>
            <TitleH2>Software License</TitleH2>
            <AmpConCustomStaticTable
                columns={licenseColumns}
                data={licenseInfo}
                extraButton={
                    <Space size={16}>
                        <Button
                            type="primary"
                            style={{width: "98px"}}
                            onClick={() => {
                                setIsModalOpenImport(true);
                            }}
                        >
                            <Icon component={importWhiteSvg} />
                            Import
                        </Button>
                        <Button
                            onClick={() => {
                                setIsModalOpenObtain(true);
                            }}
                        >
                            <Icon component={readSvg} />
                            Obtain
                        </Button>
                        <Button
                            style={{width: "104px"}}
                            onClick={() => {
                                getLicenseInfoApi().then();
                                message.success("License info refresh success");
                            }}
                        >
                            <Icon component={refreshSvg} />
                            Refresh
                        </Button>
                    </Space>
                }
            />
            <ImportLicenseModal
                onCancelFunc={() => {
                    setIsModalOpenImport(false);
                    getLicenseInfoApi().then();
                }}
                isModalOpen={isModalOpenImport}
            />
            <ObtainModal
                onCancelFunc={() => {
                    setIsModalOpenObtain(false);
                }}
                isModalOpen={isModalOpenObtain}
            />
        </Card>
    );
};
export default SoftwareLisence;
