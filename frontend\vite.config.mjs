import {defineConfig} from "vite";
import react from "@vitejs/plugin-react-swc";
import path from "path";
import svgr from "vite-plugin-svgr";

export default defineConfig({
    envPrefix: ["VITE_", "REACT_"],
    css: {
        preprocessorOptions: {
            scss: {
                api: "modern-compiler"
            }
        }
    },
    plugins: [
        svgr({
            svgrOptions: {
                icon: false,
                dimensions: true,
                expandProps: "end"
            }
        }),
        react({
            swcOptions: {
                jsc: {
                    cache: true,
                    parser: {
                        syntax: "ecmascript",
                        jsx: true
                    },
                    transform: {
                        react: {
                            runtime: "automatic"
                        }
                    }
                }
            }
        })
    ],
    optimizeDeps: {
        include: ["react", "react-dom", "antd", "@ant-design/icons"],
        exclude: ["@antv/x6", "@antv/x6-react-shape"]
    },
    resolve: {
        alias: {
            "@": path.resolve(__dirname, "src")
        }
    },
    server: {
        proxy: {
            "/ampcon": {
                target: "https://************",
                changeOrigin: true,
                secure: false
            },
            "/otn": {
                target: "https://************",
                changeOrigin: true,
                secure: false
            },
            "/otn/api/ws": {
                target: "wss://************",
                changeOrigin: true,
                ws: true,
                secure: false
            }
        },
        port: 3000,
        open: false,
        hot: true
    }
});
