export const SIDES = ["front", "rear"];

const CARDWIDTH = 393;
const INIT = 180;

export const LED_STATUS = Object.freeze({
    GREEN: 1,
    GREEN_FLASHING: 2,
    RED: 3,
    RED_FLASHING: 4,
    GREEN_FLASHING_SLOW: 5,
    RED_FLASHING_SLOW: 6
});

const M6200_CHASSIS_CONFIG = {
    1: {
        side: SIDES[0],
        left: 313,
        bottom: 4
    },
    2: {
        side: SIDES[0],
        left: 1051,
        bottom: 4
    },
    3: {
        side: SIDES[0],
        left: 313,
        bottom: 99
    },
    4: {
        side: SIDES[0],
        left: 1051,
        bottom: 99
    },
    5: {
        side: SIDES[0],
        left: 313,
        bottom: 192
    },
    6: {
        side: SIDES[0],
        left: 1051,
        bottom: 192
    },
    7: {
        side: SIDES[0],
        left: 313,
        bottom: 286
    },
    8: {
        side: SIDES[0],
        left: 1051,
        bottom: 286
    },
    9: {
        side: SIDES[0],
        left: 420,
        top: 5
    },
    10: {
        side: SIDES[0],
        left: 78,
        top: 5
    },
    16: {
        side: SIDES[0],
        left: 1428,
        top: 5
    }
};

const M6200_NMU_CONFIG = {
    "LED-RUN": {
        top: 51,
        left: 100,
        status: {
            key: "cardLedStatus",
            judge: statusValues => {
                const {key} = statusValues;
                return getLEDStatus(key, 1);
            }
        }
    },
    "LED-ALARM": {
        top: 51,
        left: 75,
        status: {
            key: "cardLedStatus",
            judge: statusValues => {
                const {key} = statusValues;
                return getLEDStatus(key, 0);
            }
        }
    },
    "LED-ACTIVE": {
        top: 51,
        left: 124,
        status: {
            key: "cardLedStatus",
            judge: statusValues => {
                const {key} = statusValues;
                return getLEDStatus(key, 5);
            }
        }
    }
};

function arrayAnyZero(arr, start, end) {
    start = Math.max(0, start);
    end = Math.min(arr.length - 1, end);
    for (let i = start; i <= end; i++) {
        if (parseInt(arr[i]) === 0) {
            return true;
        }
    }
    return false;
}

function arrayALLZero(arr, start, end) {
    start = Math.max(0, start);
    end = Math.min(arr.length - 1, end);
    for (let i = start; i <= end; i++) {
        if (parseInt(arr[i]) !== 0) {
            return false;
        }
    }
    return true;
}

function getLEDStatus(originData, index) {
    const cleanHexString = originData.startsWith("0x") ? originData.substring(2) : originData;
    const hexDataList = [];
    for (let i = 0; i < cleanHexString.length; i += 2) {
        const hexPair = cleanHexString.substring(i, i + 2);
        hexDataList.push(parseInt(hexPair, 16));
    }
    const status = hexDataList[index];
    switch (status) {
        case 0:
        case 1:
            return null;
        case 2:
            return LED_STATUS.GREEN_FLASHING_SLOW;
        case 3:
            return LED_STATUS.GREEN_FLASHING;
        case 4:
            return LED_STATUS.GREEN;
        case 5:
            return LED_STATUS.RED_FLASHING_SLOW;
        case 6:
            return LED_STATUS.RED_FLASHING;
        case 7:
            return LED_STATUS.RED;
        default:
            return null;
    }
}

const M6200_EDFA_CONFIG = {
    "LED-RUN": {
        top: 52,
        left: 103,
        status: {
            key: "cardLedStatus",
            judge: statusValues => {
                const {key} = statusValues;
                return getLEDStatus(key, 1);
            }
        }
    },
    "LED-ALARM": {
        top: 27,
        left: 103,
        status: {
            key: "cardLedStatus",
            judge: statusValues => {
                const {key} = statusValues;
                return getLEDStatus(key, 0);
            }
        }
    },
    "LED-IN": {
        top: 52,
        left: 144,
        status: {
            key: "cardLedStatus",
            judge: statusValues => {
                const {key} = statusValues;
                return getLEDStatus(key, 8);
            }
        }
    },
    "LED-OUT": {
        top: 27,
        left: 144,
        status: {
            key: "cardLedStatus",
            judge: statusValues => {
                const {key} = statusValues;
                return getLEDStatus(key, 9);
            }
        }
    },
    "LED-RxTx": {
        top: 69,
        left: 236,
        status: {
            key: "cardLedStatus",
            judge: statusValues => {
                const {key} = statusValues;
                return getLEDStatus(key, 10);
            }
        }
    }
};

const M6200_OEO10G_CONFIG = {
    "LED-SYS": {
        top: 50,
        left: 85,
        status: {
            key: "cardLedStatus",
            judge: statusValues => {
                const {key} = statusValues;
                return getLEDStatus(key, 1);
            }
        }
    },
    "LED-1": {
        top: 70,
        left: 123,
        status: {
            key: "cardLedStatus",
            judge: statusValues => {
                const {key} = statusValues;
                return getLEDStatus(key, 8);
            }
        }
    },
    "LED-2": {
        top: 70,
        left: 180,
        status: {
            key: "cardLedStatus",
            judge: statusValues => {
                const {key} = statusValues;
                return getLEDStatus(key, 9);
            }
        }
    },
    "LED-3": {
        top: 70,
        left: 236,
        status: {
            key: "cardLedStatus",
            judge: statusValues => {
                const {key} = statusValues;
                return getLEDStatus(key, 10);
            }
        }
    },
    "LED-4": {
        top: 70,
        left: 292,
        status: {
            key: "cardLedStatus",
            judge: statusValues => {
                const {key} = statusValues;
                return getLEDStatus(key, 11);
            }
        }
    },
    "LED-5": {
        top: 70,
        left: 355,
        status: {
            key: "cardLedStatus",
            judge: statusValues => {
                const {key} = statusValues;
                return getLEDStatus(key, 12);
            }
        }
    },
    "LED-6": {
        top: 70,
        left: 411,
        status: {
            key: "cardLedStatus",
            judge: statusValues => {
                const {key} = statusValues;
                return getLEDStatus(key, 13);
            }
        }
    },
    "LED-7": {
        top: 70,
        left: 468,
        status: {
            key: "cardLedStatus",
            judge: statusValues => {
                const {key} = statusValues;
                return getLEDStatus(key, 14);
            }
        }
    },
    "LED-8": {
        top: 70,
        left: 523,
        status: {
            key: "cardLedStatus",
            judge: statusValues => {
                const {key} = statusValues;
                return getLEDStatus(key, 15);
            }
        }
    },
    "LED-9": {
        top: 70,
        left: 586,
        status: {
            key: "cardLedStatus",
            judge: statusValues => {
                const {key} = statusValues;
                return getLEDStatus(key, 16);
            }
        }
    },
    "LED-10": {
        top: 70,
        left: 640,
        status: {
            key: "cardLedStatus",
            judge: statusValues => {
                const {key} = statusValues;
                return getLEDStatus(key, 17);
            }
        }
    }
};

const M6200_OEO100G_CONFIG = {
    "LED-RUN": {
        top: 52,
        left: 84,
        status: {
            key: "cardLedStatus",
            judge: statusValues => {
                const {key} = statusValues;
                return getLEDStatus(key, 1);
            }
        }
    },
    "LED-ALARM": {
        top: 27,
        left: 84,
        status: {
            key: "cardLedStatus",
            judge: statusValues => {
                const {key} = statusValues;
                return getLEDStatus(key, 0);
            }
        }
    },
    "LED-1": {
        top: 69,
        left: 144,
        status: {
            key: "cardLedStatus",
            judge: statusValues => {
                const {key} = statusValues;
                return getLEDStatus(key, 8);
            }
        }
    },
    "LED-2": {
        top: 69,
        left: 228,
        status: {
            key: "cardLedStatus",
            judge: statusValues => {
                const {key} = statusValues;
                return getLEDStatus(key, 9);
            }
        }
    },
    "LED-3": {
        top: 69,
        left: 312,
        status: {
            key: "cardLedStatus",
            judge: statusValues => {
                const {key} = statusValues;
                return getLEDStatus(key, 10);
            }
        }
    },
    "LED-4": {
        top: 69,
        left: 396,
        status: {
            key: "cardLedStatus",
            judge: statusValues => {
                const {key} = statusValues;
                return getLEDStatus(key, 11);
            }
        }
    },
    "LED-5": {
        top: 69,
        left: 480,
        status: {
            key: "cardLedStatus",
            judge: statusValues => {
                const {key} = statusValues;
                return getLEDStatus(key, 12);
            }
        }
    },
    "LED-6": {
        top: 69,
        left: 564,
        status: {
            key: "cardLedStatus",
            judge: statusValues => {
                const {key} = statusValues;
                return getLEDStatus(key, 13);
            }
        }
    }
};

const M6200_PSU_CONFIG = {
    "LED-SYS": {
        top: 17,
        left: 294,
        status: {
            key: "businessInfo.query.power_state",
            judge: statusValues => {
                const {key} = statusValues;
                if (key === "1") {
                    return LED_STATUS.GREEN;
                }
                return null;
            }
        }
    },
    "LED-ALARM": {
        top: 124,
        left: 11
    }
};

const M6200_FAN_CONFIG = {
    "LED-SYS": {
        top: 26,
        left: 99,
        status: {
            key: "businessInfo.query",
            judge: statusValues => {
                const {key} = statusValues;
                if (key) {
                    return LED_STATUS.GREEN;
                }
                return null;
            }
        }
    },
    "LED-ALARM": {
        top: 26,
        left: 122,
        status: {
            key: "businessInfo.query.state",
            judge: statusValues => {
                const {key} = statusValues;
                if (key === "0") {
                    return LED_STATUS.RED;
                }
                return null;
            }
        }
    }
};

export const chassisConfig = {
    M6200: M6200_CHASSIS_CONFIG,
    "M6200-NMU": M6200_NMU_CONFIG,
    "M6200-EDFA": M6200_EDFA_CONFIG,
    "M6200-OEO10G": M6200_OEO10G_CONFIG,
    "M6200-OEO100G": M6200_OEO100G_CONFIG,
    PSU: M6200_PSU_CONFIG,
    FAN: M6200_FAN_CONFIG
};
