import {forwardRef, useImperativeHandle, useState} from "react";
import {Button, Divider, Form, Input, message, Modal} from "antd";
import TextArea from "antd/es/input/TextArea";
import {editTopology} from "@/modules-ampcon/apis/monitor_api";

const EditTopoModal = forwardRef((props, ref) => {
    const title = "Edit Topology";
    const editTopoNameLabel = "Name";
    const editTopoDescLabel = "Description";

    const {saveCallback} = props;

    useImperativeHandle(ref, () => ({
        showEditTopoModal: node => {
            setSelectedNode(node);
            editTopoForm.setFieldsValue({name: node.title.props.children[0], description: node.description});
            setIsShowModal(true);
        },
        hideEditTopoModal: () => {
            setIsShowModal(false);
        }
    }));

    const [editTopoForm] = Form.useForm();

    const [isShowModal, setIsShowModal] = useState(false);
    const [selectedNode, setSelectedNode] = useState(null);

    return isShowModal ? (
        <Modal
            className="ampcon-middle-modal"
            title={
                <div>
                    {title}
                    <Divider style={{marginTop: 8, marginBottom: 0}} />
                </div>
            }
            open={isShowModal}
            onOk={() => {}}
            onCancel={() => {
                setIsShowModal(false);
            }}
            footer={
                <div>
                    <Divider style={{marginTop: 0, marginBottom: 20}} />
                    <Button
                        onClick={() => {
                            setIsShowModal(false);
                        }}
                    >
                        Cancel
                    </Button>
                    <Button
                        type="primary"
                        onClick={async () => {
                            try {
                                await editTopoForm.validateFields();
                            } catch (errorInfo) {
                                message.error("Please input valid data");
                                return;
                            }
                            const formData = editTopoForm.getFieldsValue();
                            formData.id = selectedNode.key;
                            editTopology(formData).then(response => {
                                if (response.status !== 200) {
                                    message.error(response.info);
                                } else {
                                    setIsShowModal(false);
                                    message.success(response.info);
                                    saveCallback();
                                }
                            });
                        }}
                    >
                        Save
                    </Button>
                </div>
            }
        >
            <Form
                layout="horizontal"
                labelAlign="left"
                labelCol={{span: 5}}
                wrapperCol={{span: 17}}
                labelWrap
                className="label-wrap"
                form={editTopoForm}
                style={{minHeight: "267.23px"}}
            >
                <Form.Item
                    name="name"
                    label={editTopoNameLabel}
                    rules={[
                        {required: true, message: "Please input your topology name!"},
                        {max: 32, message: "Enter a maximum of 32 characters"},
                        {
                            validator: (_, value) => {
                                if (value === "All") {
                                    return Promise.reject(new Error("Please input a valid topology name!"));
                                }
                                if (value.trim() !== value) {
                                    return Promise.reject(
                                        new Error("Topology name should not have leading or trailing spaces.")
                                    );
                                }
                                if (/\s/.test(value)) {
                                    return Promise.reject(new Error("Topology name should not contain spaces."));
                                }
                                if (!/^[\s\w:-]+$/.test(value)) {
                                    return Promise.reject(
                                        new Error(
                                            "Topology name can only contain letters, numbers, underscores, hyphens and colons."
                                        )
                                    );
                                }
                                return Promise.resolve();
                            }
                        }
                    ]}
                    initialValue={selectedNode.title}
                >
                    <Input style={{width: "280px"}} />
                </Form.Item>
                <Form.Item name="description" label={editTopoDescLabel} initialValue={selectedNode.description}>
                    <TextArea rows={5} style={{width: "280px"}} />
                </Form.Item>
            </Form>
        </Modal>
    ) : null;
});

export default EditTopoModal;
