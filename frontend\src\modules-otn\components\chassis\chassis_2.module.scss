.chassisView {
  position: relative;
  display: flex;
  flex: 1;
  flex-direction: column;
  margin: 0;
  border: 1px solid #95b8e7;
  border-radius: 3px;
}

.wrap {
  display: flex;
  flex-direction: column;
}

.header {
  display: flex;
  align-items: center;
  padding: 5px 10px;
  font-size: 12px;
  border-bottom: 1px solid #ddd;
  flex-direction: row-reverse;
}

.diagram {
  flex: 1;
  display: flex;
  flex-direction: column;
  position: relative;
  overflow: auto;
}

.button {
  margin-left: 50px;
}

.select-entity-view {
  padding: 5px;
}

.loading {
  font-size: 18px;
  font-weight: 800;
}

.content {
  padding: 24px;
}

.head_div {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  padding-top: 5px;
  padding-left: 15px;
  text-align: center;
  margin-bottom: 24px;
}

.head_div a {
  padding: 0 10px;
  font-size: 12px;
  color: #090909;
  border: solid 1px #918f8f;
}

$ONE_SLOT_WIDTH: 290px;
$TWO_SLOT_WIDTH: 578px;
$ONE_U_HEIGHT: 114px;

$CHASSIS_HEIGHT_TYPE_1: 136.5px;
$CHASSIS_WIDTH_TYPE_1: 1300px;
$CHASSIS_HEIGHT: 247px;
$CHASSIS_WIDTH: 1264px;

/*
Equipment
*/
.device {
  position: relative;
  top: 4px;
  width: $CHASSIS_WIDTH;
  height: $CHASSIS_HEIGHT_TYPE_1;
  background-size: 100% 100%;
}

.front {
  background-image: url("img_2/front.png");
}

.rear {
  background-image: url("img_2/rear.png");
}

.border_label {
  position: absolute;
  color: #f0f0f0;
  width: auto;
  height: 14px;
  padding: 0px 8px;
  background-color: #9DA6B2;
  line-height: 14px;
  border-radius: 2px;
  font-size: 10px;
  left:65px;
  bottom: 14px;
  font-family: Lato;
  font-weight: 600;
}

.led_label {
  position: absolute;
  font-size: 16px;
  font-weight: 400;
  align-content: center;
  text-align: center;
  color: #ABB3BF;
  width:354px;
  height: 16px;
  line-height: 0px;
  left:184px;
  font-family: Lato;
}

.led_label_1 {
  top: 50px;
}

.led_label_2 {
  top: 70px;
}

.led_COMMON {
  box-shadow: none !important;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  transform: scale(0.5);
  position: absolute;
  left: 828px
}

.led_NORMAL {
  background-color: #2bbf2b;
}

.led_CRITICAL {
  background-color: red;
}

.led_OFFLINE {
  background-color: #8a8989;
}

.led_border {
  position: absolute;
  color: #cfe778;
  font-size: large;
}

.led_border_top {
  left: 270px;
  top: 47px;
}

.led_border_bottom {
  left: 295px;
  top: 73px;
}

.power_ac220v {
  background: url("img_2/power_ac220v.png") no-repeat center center;
  background-size: contain;
}


.power_left.power_ac220v,
.power_right.power_ac220v {
  box-shadow: none !important;
  width: 227px;
  height: 126px;

  .power_on {
    background: url("img_2/power_ac220v_on.png") no-repeat center center;
    background-size: contain;
  }

  .power_off {
    background: url("img_2/power_ac220v_off.png") no-repeat center center;
    background-size: contain;
  }
}


.power_left.power_ac220v {
  position: absolute;
  left: 741px;
  top: 4px;
}

.power_right.power_ac220v {
  position: absolute;
  left: 975px;
  top: 4px;
}

.power_right.power_ac220v > .power,
.power_left.power_ac220v > .power {
  width: 50px;
  height: 75px;
  position: absolute;
  left: 58px;
  top: 25px;
}

.power_dc48v {
  background: url("img_2/power_dc48v.png") no-repeat center center;
  background-size: contain;
}


.power_left.power_dc48v,
.power_right.power_dc48v {
  box-shadow: none !important;
  width: 227px;
  height: 126px;

  .power_on {
    background: url("img_2/power_dc48v_on.png") no-repeat center center;
    background-size: contain;
  }

  .power_off {
    background: url("img_2/power_dc48v_off.png") no-repeat center center;
    background-size: contain;
  }
}


.power_left.power_dc48v {
  position: absolute;
  left: 741px;
  top: 4px;
}

.power_right.power_dc48v {
  position: absolute;
  left: 975px;
  top: 4px;
}

.power_right.power_dc48v > .power,
.power_left.power_dc48v > .power {
  width: 68px;
  height: 44px;
  position: absolute;
  left: 25px;
  top: 11px;
}


