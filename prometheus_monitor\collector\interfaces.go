package collector

import (
	"fmt"
	"gnmi_exporter/config"
	"log"
	"path/filepath"
	"strings"
	"time"

	"github.com/openconfig/gnmi/proto/gnmi"
)

type OpenconfigInterfaces struct {
	adminStatus   map[string]string
	operStatus    map[string]string
	mtu           map[string]string
	loopbackMode  map[string]string
	duplexMode    map[string]string
	portSpeed     map[string]string
	ampconAddress string
	targetName    string
}

func NewOpenconfigInterfaces(targetName string) Hook {
	var ampconAddress string
	config, err := config.LoadFile([]string{"gnmi.yaml"})
	if err != nil {
		log.Fatalln("load config failed")
		ampconAddress = "nginx-service:443"
	} else {
		ampconAddress = config.GlobalConfig.AmpconAddress
	}

	return OpenconfigInterfaces{
		adminStatus:   map[string]string{},
		operStatus:    map[string]string{},
		mtu:           map[string]string{},
		loopbackMode:  map[string]string{},
		duplexMode:    map[string]string{},
		portSpeed:     map[string]string{},
		ampconAddress: ampconAddress,
		targetName:    targetName,
	}
}

func (i OpenconfigInterfaces) AfterSubscribe(result interface{}) {
	var alerts []Alert
	for _, update := range result.(*gnmi.Notification).Update {
		baseMetric := updateToBaseMetric(time.Now(), update)

		name := filepath.Base(baseMetric.Name)
		parentDir := filepath.Dir(baseMetric.Name)
		parent := filepath.Base(parentDir)
		if parent != "state" {
			continue
		}
		// fmt.Println(name, parentDir, parent, baseMetric.Name)
		switch name {
		case "admin-status":
			interfaceName := baseMetric.Label["interface_name"]
			isAlert, alert := i.updateStatus(i.adminStatus, interfaceName, name, baseMetric.Value)
			if isAlert {
				alerts = append(alerts, alert)
			}
		case "oper-status":
			interfaceName := baseMetric.Label["interface_name"]
			isAlert, alert := i.updateStatus(i.operStatus, interfaceName, name, baseMetric.Value)
			if isAlert {
				alerts = append(alerts, alert)
			}
		case "mtu":
			interfaceName := baseMetric.Label["interface_name"]
			isAlert, alert := i.updateStatus(i.mtu, interfaceName, name, baseMetric.Value)
			if isAlert {
				alerts = append(alerts, alert)
			}
		case "loopback-mode":
			interfaceName := baseMetric.Label["interface_name"]
			isAlert, alert := i.updateStatus(i.loopbackMode, interfaceName, name, baseMetric.Value)
			if isAlert {
				alerts = append(alerts, alert)
			}
		case "duplex-mode", "negotiated-duplex-mode":
			interfaceName := baseMetric.Label["interface_name"]
			isAlert, alert := i.updateStatus(i.duplexMode, interfaceName, "duplex-mode", baseMetric.Value)
			if isAlert {
				alerts = append(alerts, alert)
			}
		case "port-speed", "negotiated-port-speed":
			interfaceName := baseMetric.Label["interface_name"]
			var portSpeed string
			parts := strings.Split(baseMetric.Value, ":")
			if len(parts) == 2 {
				speedParts := strings.Split(parts[1], "_")
				if len(speedParts) > 1 {
					portSpeed = speedParts[1]
				} else {
					portSpeed = parts[1]
				}
			} else {
				portSpeed = baseMetric.Value
			}

			isAlert, alert := i.updateStatus(i.portSpeed, interfaceName, "port-speed", portSpeed)
			if isAlert {
				alerts = append(alerts, alert)
			}
		default:
		}
	}

	if len(alerts) > 0 {
		err := sendAlertLog("https://"+i.ampconAddress+"/ampcon/monitor/alert_log", alerts)
		if err != nil {
			log.Println("Error sending alert:", err)
		}
	}
}

func (i OpenconfigInterfaces) updateStatus(statusMap map[string]string, interfaceName, name, value string) (bool, Alert) {
	if oldStatus, exists := statusMap[interfaceName]; exists {
		if oldStatus != value {
			statusMap[interfaceName] = value
			severity := "warn"
			if name == "admin-status" || name == "mtu" || name == "loopback-mode" {
				severity = "info"
			}
			alert := Alert{
				Labels: map[string]string{
					"target":   i.targetName,
					"severity": severity,
				},
				Annotations: map[string]string{
					"description": fmt.Sprintf("found interface: %s on switch %s %s change to %s", interfaceName, i.targetName, name, value),
				},
			}
			log.Printf("found interface: %s on switch %s %s  %s change to %s, severity: %s", interfaceName, i.targetName, name, oldStatus, value, severity)
			return true, alert
		}
	} else {
		statusMap[interfaceName] = value
		// fmt.Printf("Added key '%s' '%s' to new value: '%s'.\n", interfaceName, name, value)
	}
	return false, Alert{}
}
