<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" fill="none" version="1.1" width="16" height="16" viewBox="0 0 16 16"><g><g></g><g><path d="M6.48167,4.5L4.858478,4.5C4.660496,4.5,4.5,4.654309,4.5,4.844658L4.5,6.32979C4.5,6.52014,4.660496,6.67445,4.858478,6.67445C5.0564599999999995,6.67445,5.216956,6.52014,5.216956,6.32979L5.216956,5.189316L6.48167,5.189316C6.6796500000000005,5.189316,6.84014,5.035007,6.84014,4.844658C6.84014,4.654309,6.6796500000000005,4.5,6.48167,4.5ZM11.14152,4.5L9.51869,4.5C9.32071,4.5,9.16021,4.654309,9.16021,4.844658C9.16021,5.035007,9.32071,5.189316,9.51869,5.189316L10.78304,5.189316L10.78304,6.32979C10.78304,6.52014,10.943539999999999,6.67445,11.14152,6.67445C11.339500000000001,6.67445,11.5,6.52014,11.5,6.32979L11.5,4.844658C11.5,4.654309,11.339500000000001,4.5,11.14152,4.5ZM6.48167,10.810690000000001L5.216956,10.810690000000001L5.216956,9.66987C5.216956,9.47952,5.0564599999999995,9.32521,4.858478,9.32521C4.660496,9.32521,4.5,9.47952,4.5,9.66987L4.5,11.155339999999999C4.5,11.345690000000001,4.660496,11.5,4.858478,11.5L6.48167,11.5C6.6796500000000005,11.5,6.84014,11.345690000000001,6.84014,11.155339999999999C6.84014,10.96499,6.6796500000000005,10.810690000000001,6.48167,10.810690000000001ZM11.14152,9.32521C10.943539999999999,9.32521,10.78304,9.47952,10.78304,9.66987L10.78304,10.810690000000001L9.51869,10.810690000000001C9.32071,10.810690000000001,9.16021,10.96499,9.16021,11.155339999999999C9.16021,11.345690000000001,9.32071,11.5,9.51869,11.5L11.14152,11.5C11.339500000000001,11.5,11.5,11.345690000000001,11.5,11.155339999999999L11.5,9.66987C11.5,9.47952,11.339500000000001,9.32521,11.14152,9.32521Z" fill="currentColor" fill-opacity="1" style="mix-blend-mode:passthrough"/><path d="M6.87376,5.232541Q7.04014,5.072574,7.04014,4.844658Q7.04014,4.616741,6.87376,4.4567748Q6.7107,4.3,6.48167,4.3L4.858478,4.3Q4.629442,4.3,4.4663809,4.4567748Q4.3,4.616741,4.3,4.844658L4.3,6.32979Q4.3,6.55771,4.4663809,6.71767Q4.629442,6.8744499999999995,4.858478,6.8744499999999995Q5.087514,6.8744499999999995,5.2505749999999995,6.71767Q5.416956,6.55771,5.416956,6.32979L5.416956,5.389316L6.48167,5.389316Q6.7107,5.389316,6.87376,5.232541ZM11.7,6.32979L11.7,4.844658Q11.7,4.616742,11.533619999999999,4.4567747Q11.370560000000001,4.3,11.14152,4.3L9.51869,4.3Q9.28966,4.3,9.12659,4.4567748Q8.96021,4.616741,8.96021,4.844658Q8.96021,5.072574,9.12659,5.232541Q9.28966,5.389316,9.51869,5.389316L10.58304,5.389316L10.58304,6.32979Q10.58304,6.55771,10.74942,6.71767Q10.91249,6.8744499999999995,11.14152,6.8744499999999995Q11.370560000000001,6.8744499999999995,11.533619999999999,6.71767Q11.7,6.5577000000000005,11.7,6.32979ZM6.48167,4.7Q6.640140000000001,4.7,6.640140000000001,4.844658Q6.640140000000001,4.989316,6.48167,4.989316L5.016956,4.989316L5.016956,6.32979Q5.016956,6.47445,4.858478,6.47445Q4.7,6.47445,4.7,6.32979L4.7,4.844658Q4.7,4.7,4.858478,4.7L6.48167,4.7ZM11.14152,4.7Q11.3,4.7,11.3,4.844658L11.3,6.32979Q11.3,6.3873999999999995,11.25639,6.42933Q11.20946,6.47445,11.14152,6.47445Q10.983039999999999,6.47445,10.983039999999999,6.32979L10.983039999999999,4.989316L9.51869,4.989316Q9.36021,4.989316,9.36021,4.844658Q9.36021,4.7,9.51869,4.7L11.14152,4.7ZM6.87376,11.543230000000001Q7.04014,11.38326,7.04014,11.155339999999999Q7.04014,10.92742,6.87376,10.76746Q6.7107,10.61069,6.48167,10.61069L5.416956,10.61069L5.416956,9.66987Q5.416956,9.44195,5.2505749999999995,9.28198Q5.087514,9.12521,4.858478,9.12521Q4.629442,9.12521,4.4663809,9.28198Q4.3,9.44195,4.3,9.66987L4.3,11.155339999999999Q4.3,11.38326,4.4663811,11.54322Q4.629443,11.7,4.858478,11.7L6.48167,11.7Q6.7107,11.7,6.87376,11.543230000000001ZM11.7,11.155339999999999L11.7,9.66987Q11.7,9.44195,11.533619999999999,9.28198Q11.370560000000001,9.12521,11.14152,9.12521Q10.91248,9.12521,10.74942,9.28198Q10.58304,9.44195,10.58304,9.66987L10.58304,10.61069L9.51869,10.61069Q9.28966,10.61069,9.1266,10.76746Q8.96021,10.927430000000001,8.96021,11.155339999999999Q8.96021,11.38326,9.1266,11.54322Q9.28966,11.7,9.51869,11.7L11.14152,11.7Q11.370560000000001,11.7,11.533619999999999,11.543230000000001Q11.7,11.38326,11.7,11.155339999999999ZM6.48167,11.01068Q6.640140000000001,11.01068,6.640140000000001,11.155339999999999Q6.640140000000001,11.3,6.48167,11.3L4.858478,11.3Q4.790541,11.3,4.743611,11.25488Q4.7,11.21295,4.7,11.155339999999999L4.7,9.66987Q4.7,9.525210000000001,4.858478,9.525210000000001Q5.016956,9.525210000000001,5.016956,9.66987L5.016956,11.01068L6.48167,11.01068ZM11.14152,9.525210000000001Q11.3,9.525210000000001,11.3,9.66987L11.3,11.155339999999999Q11.3,11.21295,11.25639,11.25488Q11.20946,11.3,11.14152,11.3L9.51869,11.3Q9.450759999999999,11.3,9.40382,11.25488Q9.36021,11.21295,9.36021,11.155339999999999Q9.36021,11.01068,9.51869,11.01068L10.983039999999999,11.01068L10.983039999999999,9.66987Q10.983039999999999,9.525210000000001,11.14152,9.525210000000001Z" fill-rule="evenodd" fill="currentColor" fill-opacity="1"/></g><g><path d="M14.0345,1L1.965517,1C1.433034,1,1,1.433035,1,1.965517L1,14.0345C1,14.567,1.433035,15,1.965517,15L14.0345,15C14.567,15,15,14.567,15,14.0345L15,1.965517C15,1.433034,14.567,1,14.0345,1ZM1.965517,14.0345L1.965517,1.965517L14.0345,1.965517L14.0354,14.0345L1.965517,14.0345Z" fill="currentColor" fill-opacity="1" style="mix-blend-mode:passthrough"/><path d="M15.25,14.0345L15.25,1.965517Q15.25,1.462601,14.8937,1.106301Q14.5374,0.75,14.0345,0.75L1.965517,0.75Q1.462602,0.75,1.106301,1.106301Q0.75,1.462602,0.75,1.965517L0.75,14.0345Q0.75,14.5374,1.106301,14.8937Q1.462601,15.25,1.965517,15.25L14.0345,15.25Q14.5374,15.25,14.8937,14.8937Q15.25,14.5374,15.25,14.0345ZM14.5401,1.459854Q14.75,1.669708,14.75,1.965517L14.75,14.0345Q14.75,14.3303,14.5401,14.5401Q14.3303,14.75,14.0345,14.75L1.965517,14.75Q1.669708,14.75,1.459854,14.5401Q1.25,14.3303,1.25,14.0345L1.25,1.965517Q1.25,1.669709,1.459854,1.459854Q1.669709,1.25,1.965517,1.25L14.0345,1.25Q14.3303,1.25,14.5401,1.459854ZM1.715517,14.2845L14.2855,14.2845L14.2845,1.715517L1.715517,1.715517L1.715517,14.2845ZM13.7854,13.7845L13.7845,2.2155199999999997L2.2155199999999997,2.2155199999999997L2.2155199999999997,13.7845L13.7854,13.7845Z" fill-rule="evenodd" fill="currentColor" fill-opacity="1"/></g></g></svg>