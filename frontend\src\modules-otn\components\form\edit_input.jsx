import {Cascader, DatePicker, Input, InputNumber, Select} from "antd";
import dayjs from "dayjs";
import {isUndefined} from "lodash";
import {
    formatUnitsMap,
    getValueByJPath,
    needAscii16FormatKeys,
    needAscii16FormatWithFlagKeys
} from "@/modules-otn/utils/util";
import AsciiHexAndPlainTextConverter from "@/modules-otn/components/input/asciihex_and_plaintext_converter";

const DECIMAL64_MAX = "9223372036854775807";
const DECIMAL64_MIN = "-9223372036854775808";

const {Option} = Select;

function getRangeSide(rangeSide, fractionDigits) {
    return `${rangeSide.slice(0, rangeSide.length - fractionDigits)}.${rangeSide.slice(
        rangeSide.length - fractionDigits,
        rangeSide.length
    )}`;
}

const EditInput = yangConfig => {
    const {key, useDefine, config, form, datas, setDatas, parameter, initVals, path} = yangConfig;
    const _yangCfg = {...config, ...useDefine};
    const {
        type,
        units,
        enum: eNum,
        range,
        description,
        length,
        inputType,
        data,
        defaultValue,
        step,
        parserFun,
        "fraction-digits": digits
    } = _yangCfg;
    let {disabled} = _yangCfg;
    const formatUnits = formatUnitsMap[units] ?? units;

    if (defaultValue && form) {
        const v =
            typeof defaultValue === "function"
                ? defaultValue(form.getFieldsValue(), parameter, initVals)
                : defaultValue;
        form.setFieldValue(key, v);
    }
    if (disabled && typeof disabled === "function") {
        if (isUndefined(datas[`${key}_disabled`])) {
            disabled(datas, initVals).then(rs => {
                setDatas({...datas, [`${key}_disabled`]: rs});
            });
            disabled = true;
        } else {
            disabled = datas[`${key}_disabled`];
        }
    }
    if (!inputType && !type) {
        return (
            <Input
                key={key}
                addonAfter={formatUnits}
                disabled={disabled}
                style={{width: 280}}
                onChange={v => {
                    if (setDatas) {
                        setDatas({...datas, [key]: v});
                    }
                }}
            />
        );
    }
    if (inputType === "text" || type === "text") {
        return (
            <Input
                key={key}
                addonAfter={formatUnits}
                disabled={disabled}
                style={{width: 280}}
                onChange={v => {
                    if (setDatas) {
                        setDatas({...datas, [key]: v});
                    }
                }}
            />
        );
    }
    if (type === "cascader") {
        return (
            <Cascader
                key={key}
                style={{width: 280}}
                options={datas?.[`${key}_options`]}
                onChange={v => {
                    if (v.length > 1) {
                        v = v[v.length - 1];
                    }
                    if (setDatas) {
                        setDatas({...datas, [key]: v});
                    }
                    if (form) {
                        form.setFieldValue(key, v);
                    }
                }}
                onDropdownVisibleChange={async open => {
                    if (open) {
                        if (data) {
                            data(form?.getFieldsValue(), parameter, initVals).then(rs => {
                                setDatas({...datas, [`${key}_options`]: rs});
                            });
                        }
                    }
                }}
            />
        );
    }

    if (type === "select") {
        return (
            <Select
                style={{width: 280}}
                key={key}
                // value={datas[key]}
                // onChange={v => {
                // if (setDatas) {
                //     setDatas({...datas, [key]: v});
                // }
                // }}
                disabled={disabled}
                onDropdownVisibleChange={async open => {
                    if (open) {
                        if (data) {
                            data(form?.getFieldsValue(), parameter, initVals).then(rs => {
                                setDatas({...datas, [`${key}_options`]: rs});
                            });
                        }
                    }
                }}
                filterSort={(optionA, optionB) => {
                    return (optionA?.label ?? "")
                        .toString()
                        .toLowerCase()
                        .localeCompare((optionB?.label ?? "").toString().toLowerCase(), "ZH-CN", {numeric: true});
                }}
                options={
                    datas?.[`${key}_options`]?.map(item => {
                        return {
                            label: item,
                            value: item
                        };
                    }) ?? []
                }
            />
        );
    }

    if (type === "enumeration") {
        const enumValues = Object.keys(eNum);
        const options = enumValues.map(enumKey => (
            <Option value={enumKey} key={enumKey}>
                {enumKey}
            </Option>
        ));
        return (
            <Select
                key={key}
                style={{width: 280}}
                onChange={v => {
                    if (setDatas) {
                        setDatas({...datas, [key]: v});
                    }
                }}
            >
                {options}
            </Select>
        );
    }

    if (type === "leafref") {
        return (
            <Input
                key={key}
                style={{width: 280}}
                addonAfter={formatUnits}
                onChange={v => {
                    if (setDatas) {
                        setDatas({...datas, [key]: v});
                    }
                }}
            />
        );
    }

    if (type === "boolean") {
        const options = [
            <Option key="true" value="true">
                true
            </Option>,
            <Option key="false" value="false">
                false
            </Option>
        ];
        return (
            <Select
                key={key}
                style={{width: 280}}
                onChange={v => {
                    if (setDatas) {
                        setDatas({...datas, [key]: v});
                    }
                }}
            >
                {options}
            </Select>
        );
    }
    if (type === "date-and-time") {
        return (
            <DatePicker
                key={key}
                style={{width: 280}}
                format={str => {
                    return dayjs(str).format("YYYY-MM-DD HH:mm:ss");
                }}
                showTime
            />
        );
    }

    if (type === "timeticks64") {
        return (
            <DatePicker
                style={{width: 280}}
                key={key}
                format={str => {
                    return dayjs(parseInt(str) / 1000000).format("YYYY-MM-DD HH:mm:ss");
                }}
                showTime
            />
        );
    }

    if (range) {
        const rangeArray = range.split("..");
        if (rangeArray) {
            if (rangeArray[1] === "max") {
                if (type.startsWith("uint")) {
                    return (
                        <InputNumber
                            key={key}
                            style={{width: 280}}
                            min={rangeArray[0]}
                            max={2 ** type.split("int")[1] - 1}
                            addonAfter={formatUnits}
                            step={step}
                            parser={v => {
                                if (parserFun) {
                                    return parserFun(v);
                                }
                                return v;
                            }}
                        />
                    );
                }
                if (type === "decimal64") {
                    const fractionDigits = parseInt(digits) ?? 1;
                    const step = `0.${"0".repeat(fractionDigits - 1)}1`;
                    return (
                        <InputNumber
                            key={key}
                            style={{width: 280}}
                            min={rangeArray[0]}
                            step={step}
                            max={getRangeSide(DECIMAL64_MAX, fractionDigits)}
                            addonAfter={formatUnits}
                            onChange={v => {
                                if (setDatas) {
                                    setDatas({...datas, [key]: v});
                                }
                            }}
                            parser={v => {
                                if (parserFun) {
                                    return parserFun(v);
                                }
                                return v;
                            }}
                        />
                    );
                }
            } else {
                return (
                    <InputNumber
                        key={key}
                        style={{width: 280}}
                        min={rangeArray[0]}
                        max={rangeArray[1]}
                        addonAfter={formatUnits}
                        onChange={v => {
                            if (setDatas) {
                                setDatas({...datas, [key]: v});
                            }
                        }}
                        step={step}
                        parser={v => {
                            if (parserFun) {
                                return parserFun(v);
                            }
                            return v;
                        }}
                    />
                );
            }
        } else {
            // eslint-disable-next-line no-console
            console.error("range is not digit:", range);
        }
    }

    if (type.startsWith("uint")) {
        return (
            <InputNumber
                key={key}
                style={{width: 280}}
                min={0}
                max={2 ** type.split("int")[1] - 1}
                addonAfter={formatUnits}
                onChange={v => {
                    if (setDatas) {
                        setDatas({...datas, [key]: v});
                    }
                }}
                step={step}
                parser={v => {
                    if (parserFun) {
                        return parserFun(v);
                    }
                    return v;
                }}
            />
        );
    }

    if (description?.match(/password/)) {
        return (
            <Input
                type="password"
                placeholder="Please input password"
                addonAfter={formatUnits}
                key={key}
                style={{width: 280}}
            />
        );
    }

    if (type === "decimal64") {
        const fractionDigits = parseInt(digits) ?? 1;
        const step = `0.${"0".repeat(fractionDigits - 1)}1`;
        return (
            <InputNumber
                key={key}
                style={{width: 280}}
                min={getRangeSide(DECIMAL64_MIN, fractionDigits)}
                max={getRangeSide(DECIMAL64_MAX, fractionDigits)}
                step={step}
                addonAfter={formatUnits}
                onChange={v => {
                    if (setDatas) {
                        setDatas({...datas, [key]: v});
                    }
                }}
                parser={v => {
                    if (parserFun) {
                        return parserFun(v);
                    }
                    return v;
                }}
            />
        );
    }

    if (length?.split("..").pop() > 20) {
        return (
            <Input.TextArea
                key={key}
                style={{width: 280}}
                addonAfter={formatUnits}
                onChange={v => {
                    if (setDatas) {
                        setDatas({...datas, [key]: v});
                    }
                }}
            />
        );
    }

    if ([...needAscii16FormatKeys, ...needAscii16FormatWithFlagKeys].includes(key)) {
        const defaultValue = getValueByJPath(initVals, path);
        return (
            <AsciiHexAndPlainTextConverter
                defaultValue={defaultValue}
                isContainFlag={needAscii16FormatWithFlagKeys.includes(key)}
                onChange={v => {
                    const oldValue = structuredClone(initVals);
                    const value = getValueByJPath(oldValue, path.slice(0, -1));
                    value[key] = v;
                    form?.setFieldsValue?.(value);
                }}
            />
        );
    }
    return (
        <Input
            key={key}
            addonAfter={formatUnits}
            style={{width: 280}}
            disabled={disabled}
            onChange={v => {
                if (setDatas) {
                    setDatas({...datas, [key]: v});
                }
            }}
        />
    );
};

export default EditInput;
