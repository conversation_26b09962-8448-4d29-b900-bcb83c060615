
.logoContainer {
  display: flex;
  justify-content: center; /* 水平居中对齐 */
  align-items: center; /* 垂直居中对齐 */
  height: 64px; /* 与Header高度相同 */
  margin-left: 8px;
}

.layoutStyle {
  height: 100vh;
  overflow: hidden
}

.contentContainer {
  margin: 24px;
  height: 100%;
  overflow: auto;
  //border-radius: 10px;
  display: flex;
  flex-direction: column;
}

.iconList {
  display: flex;
  margin-left: auto;
  margin-right: 30px;

  &_iconDiv {
    display: flex;
    flex-direction: row;
    justify-content: center;
    width: 56px;
    height: 32px;
    margin-right: -4px;
  }
  &_iconDiv:hover{
    background: #F4F6F9;
    border-radius: 2px 2px 2px 2px;
    cursor: pointer;
  }

  &_iconUserDiv {
    display: flex;
    flex-direction: row;
    justify-content: center;
    width: 90px;
    height: 32px;
  }
  &_iconUserDiv:hover{
    background: #F4F6F9;
    border-radius: 2px 2px 2px 2px;
    cursor: pointer;
  }

  &_icon {
    display: flex;
    width: 20px;
  }

  &_label {
    display: flex;
    align-items: center;
    font-size: 16px;
    padding-left: 8px;
    font-weight: 700;
  }

  &_userLabel {
    display: flex;
    align-items: center;
    font-size: 14px;
    padding-left: 8px;
    font-weight: bolder;
    margin-right: 2px;
  }
}

.breadCrumb {
  min-width: 640px;
  display: flex;
  align-items: center;
}

.breadCrumbInternal {
  margin: 5px 1px;
}

.layoutHeader {
  padding: 0;
  background: #FFFFFF;
}

.collapsedButton {
  font-size: 16px !important;
  width: 64px !important;
  height: 64px !important;
}

.themeBackground {
  background: #14C9BB !important;
}

.helpSvg {
  display: flex;
  flex-direction: row;
  justify-content: center;
  width: 32px;
  height: 32px;
}

.helpSvg:hover {
  background: #F4F6F9;
  border-radius: 2px 2px 2px 2px;
  cursor: pointer;
  width: 32px;
  height: 32px;
}