import React, {useEffect, useState} from "react";
import {Card, Flex, message, Tag, Typography} from "antd";
import Icon, {CheckCircleFilled, CloseCircleFilled} from "@ant-design/icons";
import {normalizeNumber} from "@/modules-ampcon/utils/util";
import {editCommonIcon, editTableIcon} from "@/utils/common/iconSvg";
import styles from "./card.module.scss";
import {getText, DebounceButton, NULL_VALUE} from "../utils";
import openCustomEditForm, {openCustomEditFormForD6000} from "./custom_edit_form";
import {batchModifyDCSConfig, modifyDCSConfig} from "@/modules-ampcon/apis/fmt";
import {modifyDeviceNote} from "@/modules-ampcon/apis/otn";

const {Paragraph} = Typography;
const paddingType = ["PSU", "FAN"];

const fanSpeedMap = {
    1: "MUTE",
    3: "ORDINARY",
    5: "STRONG",
    10: "TOP SPEED"
};

const CardInfo = ({data, refresh}) => {
    const {ne_id, type, name, neData, nmuData, slotIndex, deviceType} = data ?? {};
    const [neState, setNEState] = useState(neData ?? []);
    const [nmuState, setNMUState] = useState(nmuData ?? []);

    const editComponent = cfg => {
        return (
            <Paragraph
                key={cfg.key}
                style={{marginBottom: 0, display: "flex"}}
                editable={{
                    maxLength: 32,
                    icon: <Icon component={editTableIcon} />,
                    onChange: newVal => {
                        if (!newVal || newVal === cfg.value) {
                            return;
                        }
                        cfg.change(newVal).then(rs => {
                            if (rs.apiResult !== "fail") {
                                refresh();
                            }
                        });
                    },
                    text: cfg.value,
                    triggerType: ["icon", "text"]
                }}
            >
                <div style={{paddingRight: 15}} title={cfg.value}>
                    {cfg.value}
                </div>
            </Paragraph>
        );
    };

    const createColumns = cardType => {
        const columns = [
            {dataIndex: "card-type", getData: () => cardType},
            {dataIndex: "slot-num", getData: rs => rs?.slotIndex},
            {dataIndex: "hardware-version", getData: rs => rs?.["Hardware version"]},
            // 仅在 cardType 为 EDFA 或 DEDFA 或 TDCM 时添加 temperature 配置项
            ...(["EDFA", "DEDFA", "DCM", "TDCM"].includes(cardType)
                ? [
                      {
                          dataIndex: "temperature",
                          unit: "°C",
                          getData: rs => normalizeNumber(rs?.["Module temperature"])
                      }
                  ]
                : [{dataIndex: ""}]),
            {dataIndex: "serial-no", label: "SN", getData: rs => rs?.["Serial number"]?.slice(0, -4)},
            {dataIndex: "mfg-name", label: "manufacture", getData: () => "FS"},
            {dataIndex: "firmware-version", getData: rs => rs?.["Software version"]},
            {dataIndex: ""},
            {dataIndex: "board-model", label: "PN", getData: () => cardType},
            {dataIndex: "mfg-date", label: "production-date", getData: rs => rs?.["Production date"]},
            {dataIndex: "software-version", getData: rs => rs?.["Software version"]}
        ];

        return columns;
    };
    const deviceChassisMap = {
        FMT: "FMT04-CH1U",
        D6000: "D6000-CH1U"
    };
    const SHOW_INFO_CONFIG = {
        chassis: {
            title: "Chassis Info",
            columns: [
                {dataIndex: "chassis-type", getData: () => "CHASSIS"},
                {dataIndex: "slot-num", getData: () => 4},
                {dataIndex: "hardware-version", getData: rs => rs?.["Hardware version"]},
                ...(deviceType !== "FMT"
                    ? [{dataIndex: "temperature", unit: "°C", getData: rs => rs?.Temperature}]
                    : [{dataIndex: ""}]),
                {dataIndex: "serial-no", label: "SN", getData: rs => rs?.SN?.slice(0, -4)},
                {dataIndex: "mfg-name", label: "manufacture", getData: () => "FS"},
                {dataIndex: "firmware-version", getData: rs => rs?.["Software version"]},
                ...(deviceType !== "FMT"
                    ? [{dataIndex: "actual-power", unit: "W", getData: rs => rs?.Power}]
                    : [{dataIndex: ""}]),
                {dataIndex: "part-no", label: "PN", getData: () => deviceChassisMap[deviceType]},
                {dataIndex: "mfg-date", label: "production-date", getData: rs => rs?.["Production date"]},
                {dataIndex: "software-version", getData: rs => rs?.["Software version"]},
                {
                    dataIndex: "description",
                    label: "other-info",
                    // getData: rs => rs?.description,
                    render: rs => {
                        return editComponent({
                            key: "description",
                            value: rs?.description,
                            change: async newVal => {
                                return await modifyDeviceNote({ip: ne_id.split(":")[0], note: newVal}).then(
                                    response => {
                                        if (response.errorCode === 0) {
                                            setNMUState(prevData => {
                                                const newData = {...prevData};
                                                newData.description = newVal;
                                                return newData;
                                            });
                                            message.success("Update note successfully");
                                        } else {
                                            message.error("Update note failed");
                                        }
                                    }
                                );
                            }
                        });
                    }
                }
            ]
        },
        EDFA: {columns: createColumns("EDFA")},
        DEDFA: {columns: createColumns("DEDFA")},
        OLP: {columns: createColumns("OLP")},
        DCM: {columns: createColumns("DCM")},
        TDCM: {columns: createColumns("TDCM")},
        OEO: {columns: createColumns("OEO")},
        VOA: {columns: createColumns("VOA")},
        OPD: {columns: createColumns("OPD")},
        "4T4E4C": {columns: createColumns("4T4E4C")},
        "4ME4C": {columns: createColumns("4ME4C")},
        PSU: {
            title: "PSU Info",
            columns: [
                {dataIndex: "card-type", getData: () => "PSU"},
                {
                    dataIndex: "input-current",
                    unit: "A",
                    getData: rs =>
                        rs?.businessInfo.config.power_switch === "1" && rs?.businessInfo.query.power_state === "1"
                            ? rs?.businessInfo.query.input_current
                            : NULL_VALUE
                },
                {dataIndex: "hardware-version", getData: rs => rs?.["Hardware version"]},
                {
                    dataIndex: "power-switch-status",
                    getData: rs => (
                        <Tag
                            className={
                                rs?.businessInfo.config.power_switch === "1" ? styles.normalTag : styles.abnormalTag
                            }
                        >
                            {rs?.businessInfo.config.power_switch === "1" ? "On" : "Off"}
                        </Tag>
                    )
                },
                {dataIndex: "slot-num", getData: rs => rs?.slotIndex},
                {
                    dataIndex: "output-current",
                    unit: "A",
                    getData: rs =>
                        rs?.businessInfo.config.power_switch === "1" && rs?.businessInfo.query.power_state === "1"
                            ? rs?.businessInfo.query.output_current
                            : NULL_VALUE
                },
                {dataIndex: "firmware-version", getData: rs => rs?.["Software version"]},
                {
                    dataIndex: "power-operation-status",
                    getData: rs => (
                        <Tag
                            className={
                                rs?.businessInfo.query.power_state === "1" ? styles.normalTag : styles.abnormalTag
                            }
                        >
                            {rs?.businessInfo.query.power_state === "1" ? "Normal" : "Abnormal"}
                        </Tag>
                    )
                },
                {dataIndex: "serial-no", label: "SN", getData: rs => rs?.["Serial number"]?.slice(0, -4)},
                {
                    dataIndex: "input-voltage",
                    unit: "V",
                    getData: rs =>
                        rs?.businessInfo.config.power_switch === "1" && rs?.businessInfo.query.power_state === "1"
                            ? rs?.businessInfo.query.input_voltage
                            : NULL_VALUE
                },
                {dataIndex: "software-version", getData: rs => rs?.["Software version"]},
                {dataIndex: "mfg-date", label: "production-date", getData: rs => rs?.["Production date"]},
                {dataIndex: "part-no", label: "PN", getData: () => `${deviceType}-PSU`},
                {
                    dataIndex: "output-voltage",
                    unit: "V",
                    getData: rs =>
                        rs?.businessInfo.config.power_switch === "1" && rs?.businessInfo.query.power_state === "1"
                            ? rs?.businessInfo.query.output_voltage
                            : NULL_VALUE
                },
                {
                    dataIndex: "temperature",
                    unit: "°C",
                    getData: rs =>
                        rs?.businessInfo.config.power_switch === "1" && rs?.businessInfo.query.power_state === "1"
                            ? rs?.businessInfo.query.power_temperature
                            : NULL_VALUE
                },
                {dataIndex: ""}
            ],
            editConfig: {
                columns: [
                    [
                        {
                            dataIndex: "power-switch-status",
                            label: "Power Switch Status",
                            inputType: "select",
                            visible: true,
                            setKey: "power_switch",
                            data: {
                                options: [
                                    {label: "On", value: "1"},
                                    {label: "Off", value: "0"}
                                ]
                            }
                        }
                    ]
                ],
                getData: async () => {
                    return {
                        "power-switch-status": neData?.businessInfo?.config?.power_switch ?? NULL_VALUE
                    };
                },
                setDataAPI: () => {
                    const ip = ne_id.split(":")[0];
                    return {
                        APIName: deviceType === "D6000" ? batchModifyDCSConfig : modifyDCSConfig,
                        APIParameter: () => {
                            return {
                                ip,
                                slotIndex,
                                cardId: deviceType === "D6000" ? undefined : slotIndex
                            };
                        }
                    };
                }
            }
        },
        FAN: {
            title: "FAN Info",
            columns: [
                {dataIndex: "card-type", getData: () => "FAN"},
                {dataIndex: "slot-num", getData: rs => rs?.slotIndex},
                {dataIndex: "hardware-version", getData: rs => rs?.["Hardware version"]},
                {
                    dataIndex: "fan-state",
                    getData: rs => (
                        <Tag className={rs?.businessInfo.query.state === "1" ? styles.normalTag : styles.abnormalTag}>
                            {rs?.businessInfo.query.state === "1" ? "Normal" : "Abnormal"}
                        </Tag>
                    )
                },
                {dataIndex: "serial-no", label: "SN", getData: rs => rs?.["Serial number"]?.slice(0, -4)},
                {dataIndex: "mfg-date", label: "production-date", getData: rs => rs?.["Production date"]},
                {dataIndex: "firmware-version", getData: rs => rs?.["Software version"]},
                {dataIndex: "fan-speed", getData: rs => fanSpeedMap[Math.trunc(rs?.businessInfo?.config?.work_speed)]},
                {dataIndex: "part-no", label: "PN", getData: () => `${deviceType}-FAN`},
                {dataIndex: "temperature", unit: "°C", getData: rs => normalizeNumber(rs?.["Module temperature"])},
                {dataIndex: "software-version", getData: rs => rs?.["Software version"]}
            ],
            editConfig: {
                // width: 600,
                columns: [
                    [
                        {
                            dataIndex: "work_speed",
                            label: "FAN Speed",
                            inputType: "select",
                            setKey: "set_speed",
                            visible: true,
                            data: {
                                options: [
                                    {label: "MUTE", value: "1"},
                                    {label: "ORDINARY", value: "3"},
                                    {label: "STRONG", value: "5"},
                                    {label: "TOP SPEED", value: "10"}
                                ]
                            }
                        }
                    ]
                ],
                getData: async () => {
                    return {
                        work_speed: Number(neData?.businessInfo?.config?.work_speed).toString() ?? NULL_VALUE
                    };
                },
                setDataAPI: () => {
                    const ip = ne_id.split(":")[0];
                    return {
                        APIName: deviceType === "D6000" ? batchModifyDCSConfig : modifyDCSConfig,
                        APIParameter: () => {
                            return {
                                ip,
                                slotIndex: deviceType === "D6000" ? "16" : slotIndex,
                                cardId: deviceType === "D6000" ? undefined : slotIndex
                            };
                        }
                    };
                }
            }
        },
        NMU: {columns: createColumns("NMU")}
    };
    useEffect(() => {
        setNEState(neData ?? []);
        setNMUState(nmuData ?? []);
    }, [slotIndex, neData, nmuData]);

    const showInfoConfig = SHOW_INFO_CONFIG[type] ?? {columns: []};

    let state = "Active";
    if (!neData) {
        state = "Absent";
    } else {
        state = "Present";
    }

    return (
        <Card
            style={{
                height: paddingType.includes(type) ? "100%" : "224px",
                borderRadius: "8px"
            }}
            loading={!type}
            title={
                <div>
                    {showInfoConfig.title ?? "Card Info"}
                    <Tag
                        className={["Absent", "Mismatch"].includes(state) ? styles.card_tagColor : styles.card_tag}
                        style={{marginLeft: 16}}
                        icon={["Absent", "Mismatch"].includes(state) ? <CloseCircleFilled /> : <CheckCircleFilled />}
                    >
                        {type === "chassis" && neData ? "Active" : state}
                    </Tag>
                </div>
            }
            className={styles.card}
            extra={
                showInfoConfig.editConfig && (
                    <DebounceButton
                        containerType="Icon"
                        title="Edit"
                        component={editCommonIcon}
                        style={{cursor: "pointer", width: 28, alignSelf: "flex-start", paddingTop: 10}}
                        onClick={() => {
                            const columnNum = ["FAN", "PSU"].includes(type) ? 1 : 2;
                            openCustomEditFormForD6000({
                                title: "Modify",
                                // width: showInfoConfig.editConfig?.width ?? 900,
                                columns: showInfoConfig.editConfig?.columns ?? [],
                                getData: showInfoConfig.editConfig?.getData ?? {},
                                setDataAPI: showInfoConfig.editConfig?.setDataAPI,
                                columnNum
                            });
                        }}
                    />
                )
            }
        >
            <div style={{display: "flex"}}>
                <Flex wrap="wrap" style={{flex: 1}}>
                    {showInfoConfig.columns.map(i => {
                        const _v =
                            i?.render?.(type === "chassis" ? nmuState : neState) ||
                            i?.getData?.(type === "chassis" ? nmuState : neState) ||
                            (type === "chassis" ? nmuState : neState)?.[i.dataIndex] ||
                            NULL_VALUE;
                        return i.dataIndex ? (
                            <div key={i.dataIndex} className={styles.item}>
                                <div className={styles.dot} />
                                <div
                                    className={styles.item_label}
                                >{`${getText(i.label ?? i.dataIndex) + (i.unit ? ` (${i.unit})` : "")}`}</div>
                                <span style={{padding: "0 8px 0 2px"}}>:</span>
                                <div className={styles.item_value} title={typeof _v === "string" ? _v : ""}>
                                    {_v}
                                </div>
                            </div>
                        ) : (
                            <div className={styles.item} />
                        );
                    })}
                </Flex>
            </div>
        </Card>
    );
};

export default CardInfo;
