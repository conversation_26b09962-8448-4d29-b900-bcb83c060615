export const adminSvg = () => (
    <svg width="24px" height="24px" viewBox="0 0 1024 1024">
        <path
            fill="#ffffff"
            d="M781.2 510.9l62.4 44.3-173.2 247.4-62.7 21.7-1.1-63.9 174.6-249.5m-5.7-68.9c-11.7 0-23.3 5.3-30.1 15.4L553.7 731.2c-3.7 5.5-7.3 14.6-7.3 21.9l1.8 104.1c0 21.4 18 36.4 36.7 36.4 3.6 0 7.3-0.6 10.8-1.7l100.4-34.7c7.3-1.8 14.6-7.3 18.3-12.8l191.7-273.8c11-16.4 7.3-38.3-9.1-51.1l-100.4-71.2c-6.4-4.2-13.8-6.3-21.1-6.3z"
        />
        <path
            fill="#ffffff"
            d="M415 111c-125.9 0-228 102.1-228 228 0 81.7 43 153.4 107.6 193.6-91.1 42-153.7 131.9-171.6 244.4l-8.5 67.5C110 880.3 137.9 912 174 912h269.6c1.1 0 2.1-0.1 3.2-0.2 1.1 0.1 2.1 0.2 3.2 0.2 16.6 0 30-13.4 30-30s-13.4-30-30-30c-1.1 0-2.2 0.1-3.2 0.2-1-0.1-2.1-0.2-3.2-0.2H208c-18.1 0-32-15.8-29.8-33.7l4.1-32.7c5.3-32.5 14.9-62.7 28.6-90 13.4-26.6 30.4-49.7 50.5-68.9 41.2-39 94.2-59.7 153.5-59.7 30 0 58.8 5.5 85.5 16.5 5.9 2.4 11.7 5.1 17.3 8 4.4 2.3 9.2 3.4 13.9 3.4 9 0 17.8-4 23.6-11.5 11.5-14.8 6.9-36.5-9.8-45.1-3.5-1.8-7.1-3.6-10.8-5.3C599.7 492.8 643 421 643 339c0-125.9-102.1-228-228-228z m0 396c-44.9 0-87.1-17.5-118.8-49.2S247 383.9 247 339s17.5-87.1 49.2-118.8S370.1 171 415 171c44.9 0 87.1 17.5 118.8 49.2C565.5 251.9 583 294.1 583 339s-17.5 87.1-49.2 118.8C502.1 489.5 459.9 507 415 507z"
        />
    </svg>
);

export const operatorSvg = () => (
    <svg width="24px" height="24px" viewBox="0 0 1024 1024">
        <path
            fill="#ffffff"
            d="M848 720V672c0-53.024-26.976-80-80-80s-80 26.976-80 80v48h160z m-224 175.904a16 16 0 0 0 15.872 16.096h256.256a16.096 16.096 0 0 0 15.872-16.096v-127.808l-288-0.096v127.904zM576 928.096V736a16 16 0 0 1 16-16H640V672a128 128 0 1 1 256 0v48h48c8.832 0 16 7.104 16 16v192.096a32 32 0 0 1-32.064 31.904h-319.872A32 32 0 0 1 576 928.096z m208-60.736a16 16 0 0 1-32 0V832h-16.16a16 16 0 0 1 0-32h64.32a16 16 0 0 1 0 32H784v35.36zM371.872 534.336A256 256 0 1 1 767.84 320a256 256 0 0 1-255.936 256C299.872 576 128 747.936 128 960H64c0-198.528 129.088-366.88 307.872-425.664zM511.904 512a192 192 0 0 0 191.968-192 192 192 0 1 0-191.968 192z"
        />
    </svg>
);

export const readonlySvg = () => (
    <svg width="24px" height="24px" viewBox="0 0 1024 1024">
        <path
            fill="#ffffff"
            d="M512 170.666667a128 128 0 1 0 0 256 128 128 0 0 0 0-256z m-213.333333 128c0-117.824 95.509333-213.333333 213.333333-213.333334s213.333333 95.509333 213.333333 213.333334-95.509333 213.333333-213.333333 213.333333-213.333333-95.509333-213.333333-213.333333zM656.256 538.410667C611.413333 521.344 562.816 512 512 512 288.149333 512 106.666667 693.482667 106.666667 917.333333h85.333333c0-176.725333 143.274667-320 320-320 26.24 0 51.776 3.157333 76.202667 9.130667l68.053333-68.053333zM680.234667 609.834667A42.666667 42.666667 0 0 1 710.4 597.333333h93.866667a42.666667 42.666667 0 0 1 30.165333 12.501334l70.4 70.4A42.666667 42.666667 0 0 1 917.333333 710.4v93.866667a42.666667 42.666667 0 0 1-12.501333 30.165333l-70.4 70.4A42.666667 42.666667 0 0 1 804.266667 917.333333h-93.866667a42.666667 42.666667 0 0 1-30.165333-12.501333l-70.4-70.4A42.666667 42.666667 0 0 1 597.333333 804.266667v-93.866667a42.666667 42.666667 0 0 1 12.501334-30.165333l70.4-70.4zM728.064 682.666667L682.666667 728.064v58.538667L728.064 832h58.538667L832 786.602667v-58.538667L786.602667 682.666667h-58.538667z"
        />
    </svg>
);

export const offLineSvg = () => (
    <svg viewBox="0 0 1024 1024" width="16" height="16">
        <path
            d="M160.1 195.4a4 4 0 0 0-2.9-1.2 3.9 3.9 0 0 0-2.8 1.2l-45.2 45.3a3.9 3.9 0 0 0 0 5.6l178.3 178.4-117.7 117.6c-84.5 84.6-84.6 221.7 0 306.3l5.6 5.6c84.6 84.6 221.7 84.5 306.3 0l117.6-117.7 177.4 177.3a3.9 3.9 0 0 0 5.6 0l45.3-45.2a4.2 4.2 0 0 0 0-5.7z m273.5 605a148.5 148.5 0 1 1-210-210l114.8-114.9 80 80.1-64.4 64.3a4.2 4.2 0 0 0 0 5.7l45.3 45.2a3.9 3.9 0 0 0 2.8 1.2 4 4 0 0 0 2.9-1.2l64.3-64.3 79.2 79.1z m420.6-625l-5.6-5.6c-84.6-84.6-221.7-84.5-306.3 0L424 288.2l50.8 50.9 115.6-115.5a148.5 148.5 0 1 1 210 210L684.9 549.2l50.9 50.8 118.4-118.3c84.5-84.6 84.6-221.7 0-306.3zM565.5 508.7a3.9 3.9 0 0 0 2.8-1.2L670.8 405a4 4 0 0 0 0-5.7L625.6 354a4.3 4.3 0 0 0-2.8-1.1 4.4 4.4 0 0 0-2.9 1.1L517.4 456.6a3.9 3.9 0 0 0 0 5.6l45.3 45.3a3.8 3.8 0 0 0 2.8 1.2z"
            fill="#d81e06"
        />
    </svg>
);

export const fsSvg = () => (
    <svg width="65" height="31" viewBox="0 0 58.2400016784668 28">
        <defs>
            <clipPath id="master_svg0_381_8266">
                <rect x="0" y="0" width="58.2400016784668" height="28" rx="0" />
            </clipPath>
        </defs>
        <g clipPath="url(#master_svg0_381_8266)">
            <g>
                <path
                    d="M44.17483359375,8.1737871875L44.17483359375,5.1201171875L33.54443359375,5.1201171875L33.54443359375,22.4778171875L37.10322359375,22.4778171875L37.10322359375,15.4750171875L43.64683359375,15.4750171875L43.64683359375,12.4213771875L37.10322359375,12.4213771875L37.10322359375,8.1737871875L44.17483359375,8.1737871875Z"
                    fill="#4C4948"
                    fillOpacity="1"
                />
            </g>
            <g>
                <path
                    d="M52.073135625,12.19187421875C49.524585625,11.27347421875,49.065385625,10.63059421875,49.065385625,9.66628421875C49.065385625,8.83972421875,49.731225625,7.87540421875,51.636895625,7.87540421875C52.968575625,7.87540421875,53.978805625,8.24276421875,54.598725625,8.56420421875L55.287515625,8.931564218750001L56.343715625,6.03861421875L55.861515624999996,5.78605121875C55.241615625,5.46461221875,53.886965625,4.91357421875,51.728735625,4.91357421875C49.937855625,4.91357421875,48.399545625,5.39573321875,47.251545625,6.3141342187500005C46.103555625,7.23252421875,45.483633625,8.51828421875,45.483633625,9.94180421875C45.483633625,12.30667421875,47.113785625,13.98274421875,50.465935625,15.10777421875C52.830815625,15.95727421875,53.290015625,16.691974218749998,53.290015625,17.72517421875C53.290015625,19.05687421875,52.187935625,19.86047421875,50.351135625,19.86047421875C49.111305625,19.86047421875,47.756665625,19.49317421875,46.700515625,18.896174218749998L46.034675625,18.50587421875L44.978515625,21.42177421875L45.414752625,21.69727421875C46.493865625,22.36317421875,48.445465625,22.84527421875,50.190415625,22.84527421875C54.828325625,22.84527421875,56.894715625,20.18197421875,56.894715625,17.56447421875C56.894715625,14.37306421875,54.460965625,13.06435421875,52.073135625,12.19187421875Z"
                    fill="#4C4948"
                    fillOpacity="1"
                />
            </g>
            <g>
                <g>
                    <path
                        d="M25.7609953125,9.459308583755494L22.7072953125,9.459308583755494L11.1354953125,9.459308583755494C9.8727053125,9.459308583755494,8.8395153125,8.426108583755493,8.8395153125,7.1633085837554935C8.8395153125,5.900508583755493,9.8727053125,4.867318583755493,11.1354953125,4.867318583755493L22.7072953125,4.867318583755493L24.1078953125,4.867318583755493C21.5822953125,2.0662085837554933,17.9316953125,0.29828858375549316,13.8676953125,0.29828858375549316C6.2909553125,0.29828858375549316,0.1376953125,6.451548583755494,0.1376953125,14.028288583755494C0.1376953125,21.605088583755492,6.2909553125,27.758388583755494,13.8676953125,27.758388583755494C21.4444953125,27.758388583755494,27.5977953125,21.605088583755492,27.5977953125,14.028288583755494C27.5977953125,12.352288583755493,27.2992953125,10.722088583755493,26.7252953125,9.229698583755493C26.4497953125,9.367458583755493,26.1053953125,9.459308583755494,25.7609953125,9.459308583755494ZM13.6151953125,22.730188583755492L9.9415853125,22.730188583755492C8.9313453125,22.730188583755492,8.1047953125,21.903588583755493,8.1047953125,20.893388583755492C8.1047953125,19.883088583755494,8.9313453125,19.05658858375549,9.9415853125,19.05658858375549L13.5921953125,19.05658858375549C14.6024953125,19.05658858375549,15.4289953125,19.883088583755494,15.4289953125,20.893388583755492C15.4289953125,21.903588583755493,14.6253953125,22.730188583755492,13.6151953125,22.730188583755492ZM19.2173953125,16.324288583755493L10.5155953125,16.324288583755493C9.3905453125,16.324288583755493,8.4491953125,15.405888583755493,8.4491953125,14.257888583755493C8.4491953125,13.132888583755493,9.3675853125,12.191488583755493,10.5155953125,12.191488583755493L19.2173953125,12.191488583755493C20.3423953125,12.191488583755493,21.2837953125,13.109888583755493,21.2837953125,14.257888583755493C21.2837953125,15.382988583755493,20.3423953125,16.324288583755493,19.2173953125,16.324288583755493Z"
                        fill="#C00000"
                        fillOpacity="1"
                    />
                </g>
                <g>
                    <path
                        d="M19.21741875,12.191843271255493L10.51560875,12.191843271255493C9.39057575,12.191843271255493,8.44921875,13.110240271255494,8.44921875,14.258233271255493C8.44921875,15.383273271255494,9.36761575,16.324633271255493,10.51560875,16.324633271255493L19.21741875,16.324633271255493C20.34241875,16.324633271255493,21.283818750000002,15.406233271255493,21.283818750000002,14.258233271255493C21.283818750000002,13.133201271255492,20.34241875,12.191843271255493,19.21741875,12.191843271255493Z"
                        fill="#FFFFFF"
                        fillOpacity="1"
                    />
                </g>
                <g>
                    <path
                        d="M13.61536046875,19.056589365005493L9.94177046875,19.056589365005493C8.93153746875,19.056589365005493,8.10498046875,19.883146365005494,8.10498046875,20.893379365005494C8.10498046875,21.903619365005493,8.93153746875,22.730179365005494,9.94177046875,22.730179365005494L13.59240046875,22.730179365005494C14.60264046875,22.730179365005494,15.42920046875,21.903619365005493,15.42920046875,20.893379365005494C15.42920046875,19.883146365005494,14.625600468750001,19.056589365005493,13.61536046875,19.056589365005493Z"
                        fill="#FFFFFF"
                        fillOpacity="1"
                    />
                </g>
                <g>
                    <path
                        d="M28.05702015625,7.163614521255493C28.05702015625,5.900824521255493,27.02382015625,4.867624521255493,25.76102015625,4.867624521255493L24.10791015625,4.867624521255493C25.23295015625,6.1304245212554935,26.15134015625,7.599854521255493,26.74830015625,9.230014521255494C27.50598015625,8.862654521255493,28.05702015625,8.082014521255493,28.05702015625,7.163614521255493Z"
                        fill="#C00000"
                        fillOpacity="1"
                    />
                </g>
                <g>
                    <path
                        d="M22.707399609375003,4.867624521255493L11.135589609375,4.867624521255493C9.872799609375,4.867624521255493,8.839599609375,5.900824521255493,8.839599609375,7.163614521255493C8.839599609375,8.426414521255493,9.872799609375,9.459614521255492,11.135589609375,9.459614521255492L22.707399609375003,9.459614521255492L25.761099609375,9.459614521255492C26.105499609375,9.459614521255492,26.426899609375,9.390734521255492,26.725399609375,9.230014521255494C26.128399609375,7.599854521255493,25.209999609375,6.1304245212554935,24.084999609375,4.867624521255493L22.707399609375003,4.867624521255493Z"
                        fill="#FFFFFF"
                        fillOpacity="1"
                    />
                </g>
            </g>
        </g>
    </svg>
);

export const rootTopologySvg = () => (
    <svg viewBox="0 0 1024 1024" width="1em" height="1em">
        <path d="M285.696 747.52h-66.56v-29.696h168.448v29.696h-66.56v196.096h-35.84V747.52zM406.016 829.44c0-72.704 40.448-116.224 98.304-116.224s98.816 43.52 98.816 116.224-40.448 117.76-98.816 117.76-98.304-44.544-98.304-117.76z m160.256 0c0-53.248-24.576-85.504-61.952-85.504s-61.952 32.256-61.952 85.504c0 53.248 24.064 87.04 61.952 87.04s61.952-33.792 61.952-87.04zM649.728 717.312h68.608c50.176 0 86.528 17.408 86.528 68.096 0 49.664-35.84 72.192-84.992 72.192h-34.304v85.504h-35.84v-225.792z m66.56 112.128c36.352 0 53.248-13.824 53.248-43.52s-18.944-39.424-54.784-39.424h-29.696V829.44h31.232zM859.648 76.288H164.352c-4.608 0-8.192 3.584-8.192 8.192v59.904c0 4.608 3.584 8.192 8.192 8.192H860.16c4.608 0 8.192-3.584 8.192-8.192V84.48c-0.512-4.608-4.096-8.192-8.704-8.192zM518.144 211.968c-3.072-4.096-9.216-4.096-12.8 0L393.728 353.792c-4.096 5.12-0.512 12.8 6.144 12.8h73.728V650.24c0 4.608 3.584 8.192 8.192 8.192h59.904c4.608 0 8.192-3.584 8.192-8.192V367.104h74.24c6.656 0 10.24-7.68 6.144-12.8l-112.128-142.336z" />
    </svg>
);

export const criticalSvg = () => (
    <svg width="20" height="20" viewBox="0 0 20 20">
        <g>
            <g />
            <g>
                <path
                    d="M6.66753,20C5.4064700000000006,17.3519,6.07019,15.8293,7.06576,14.439Q8.1277,12.8502,8.39319,11.3275Q9.25601,12.3868,8.92416,14.108C10.38433,12.453,10.64981,9.80488,10.4507,8.81185C13.7693,11.1289,15.2294,16.2265,13.3047,19.9338C23.5259,14.108,15.8268,5.43554,14.4994,4.508710000000001C14.964,5.50174,15.0303,7.15679,14.1011,7.95122Q12.5746,2.12544,8.791409999999999,1C9.25601,3.97909,7.1985,7.223,5.2073599999999995,9.67247C5.14099,8.48084,5.0746199999999995,7.68641,4.4109,6.49477C4.27816,8.67944,2.618874,10.4007,2.154274,12.5854C1.556931,15.5645,2.618874,17.6829,6.66753,20Z"
                    fill="#F53F3F"
                    fillOpacity="1"
                />
            </g>
        </g>
    </svg>
);

export const majorSvg = () => (
    <svg width="20" height="20" viewBox="0 0 20 20">
        <g>
            <g />
            <g>
                <path
                    d="M4.742131,11.1211L9.62868,11.1211L9.08857,18.1894C9.03093,18.943,9.98732,19.3016,10.424959999999999,18.6889Q12.67716,15.5486,16.6052,10.06862C16.959600000000002,9.57335,16.6095,8.87954,16.0053,8.87954L11.11876,8.8774L11.65887,1.809096C11.71651,1.0555133,10.76012,0.699002,10.320350000000001,1.3116889999999999L4.142253,9.93199C3.787877,10.42726,4.135849,11.1211,4.742131,11.1211Z"
                    fill="#FF7B43"
                    fillOpacity="1"
                />
            </g>
        </g>
    </svg>
);

export const minorSvg = () => (
    <svg width="20" height="20" viewBox="0 0 20 20">
        <g>
            <g />
            <g>
                <path
                    d="M15.0674,5.18913L16,4.21957C16.2652,3.94348,16.2652,3.52826,16,3.25C15.7326,2.97391,15.3326,2.97391,15.0674,3.25L14.1348,4.21957C13.8674,4.4956499999999995,13.8674,4.91087,14.1348,5.18913C14.4,5.46522,14.8326,5.46522,15.0674,5.18913ZM10,3.76957C10.36739,3.76957,10.66739,3.4587,10.66739,3.07826L10.66739,1.693478C10.66739,1.313044,10.36739,1,10,1C9.63261,1,9.33261,1.31087,9.33261,1.693478L9.33261,3.07826C9.33261,3.4587,9.63261,3.76957,10,3.76957ZM17,9.65435L18.3348,9.65435C18.7022,9.65435,19,9.34348,19,8.96087C19,8.58044,18.7,8.26957,18.3348,8.26957L17,8.26957C16.6326,8.26957,16.3348,8.58044,16.3348,8.96087C16.3326,9.34348,16.6326,9.65435,17,9.65435ZM1.665217,9.65435L3,9.65435C3.36522,9.65435,3.66739,9.34348,3.66739,8.96087C3.66739,8.58044,3.36739,8.26957,3,8.26957L1.665217,8.26957C1.3,8.26957,1,8.58044,1,8.96087C1,9.34348,1.3,9.65435,1.665217,9.65435ZM5.06522,5.18913C5.33261,5.46522,5.73261,5.46522,5.99783,5.18913C6.26522,4.9130400000000005,6.26522,4.49783,5.99783,4.21957L5.06522,3.25C4.797829999999999,2.97391,4.39783,2.97391,4.13044,3.25C3.86304,3.52609,3.86304,3.9413,4.13044,4.21957L5.06522,5.18913ZM18.3326,17.6152L15.3326,17.6152L15.3326,10.69348C15.3326,7.64783,12.9326,5.15435,10,5.15435C7.06739,5.15435,4.66739,7.64565,4.66739,10.69348L4.66739,17.6152L1.665217,17.6152C1.3,17.6152,1,17.9283,1,18.3087C1,18.6891,1.3,19,1.665217,19L18.3326,19C18.7,19,18.9978,18.6891,18.9978,18.3087C19,17.9283,18.7,17.6152,18.3326,17.6152Z"
                    fill="#FFBB00"
                    fillOpacity="1"
                />
            </g>
        </g>
    </svg>
);
export const warningSvg = () => (
    <svg width="20" height="20" viewBox="0 0 20 20">
        <g>
            <g />
            <g>
                <path
                    d="M17.6767,14.7667L15.996,11.4096C15.9786,11.3728,15.9678,11.3337,15.9678,11.2925L15.9635,7.57542C15.9613,5.47831,14.5408,3.71301,12.61287,3.18602L12.61287,3.18386C12.61287,1.978072,11.63479,1,10.42901,1C9.2254,1,8.24949,1.973735,8.245149999999999,3.17735C6.29552,3.69566,4.85768,5.47398,4.85985,7.58627L4.86419,11.3034C4.86419,11.3446,4.85552,11.3836,4.836,11.4205L3.157444,14.7733C2.667323,15.7535,3.380817,16.909399999999998,4.47817,16.9072L16.360300000000002,16.898600000000002C17.4555,16.9007,18.169,15.747,17.6767,14.7667ZM8.449010000000001,17.5361C8.22997,17.5361,8.1107,17.7899,8.24732,17.9612C8.75479,18.5945,9.53552,19,10.40949,19C11.28564,19,12.06636,18.5945,12.576,17.959C12.71262,17.7877,12.59335,17.534,12.37431,17.5361L8.449010000000001,17.5361Z"
                    fill="#00B7FF"
                    fillOpacity="1"
                />
            </g>
        </g>
    </svg>
);

export const playSvg = () => (
    <svg width="16" height="16" viewBox="0 0 16 16">
        <g>
            <g />
            <g>
                <path
                    d="M14.8504,9.06948L11.9316,14.3622C11.7247,14.7397,11.3287,14.9747,10.89817,14.9756L5.10183,14.9756C4.6713000000000005,14.9747,4.27532,14.7397,4.0684000000000005,14.3622L1.14957,9.06948C0.9501434,8.707329999999999,0.9501434,8.26826,1.14957,7.90612L4.0684000000000005,2.613438C4.27532,2.235889,4.6713000000000005,2.000844071,5.10183,2L10.89817,2C11.3287,2.000844071,11.7247,2.235889,11.9316,2.613438L14.8504,7.90612C15.0499,8.26826,15.0499,8.707329999999999,14.8504,9.06948ZM13.9543,8.57094C13.9827,8.51922,13.9827,8.45655,13.9543,8.40482L11.0406,3.1121499999999997C11.0111,3.0581899999999997,10.95467,3.02456,10.89322,3.02439L5.10678,3.02439C5.04533,3.02456,4.98886,3.0581899999999997,4.95944,3.1121499999999997L2.04574,8.40482C2.01731,8.45655,2.01731,8.51922,2.04574,8.57094L4.95944,13.8636C4.9889,13.9175,5.04537,13.9511,5.10678,13.9512L10.89322,13.9512C10.95463,13.9511,11.0111,13.9175,11.0406,13.8636L13.9543,8.57094ZM7.95595,11.1536C6.4472,11.1536,5.22425,9.93065,5.22425,8.421890000000001C5.22425,6.91314,6.4472,5.690189999999999,7.95595,5.690189999999999C9.46454,5.690189999999999,10.68766,6.91314,10.68766,8.421890000000001C10.68766,9.93065,9.46453,11.1536,7.95595,11.1536ZM7.95595,10.12921C8.898900000000001,10.12921,9.66327,9.364840000000001,9.66327,8.421890000000001C9.66327,7.47894,8.898900000000001,6.71458,7.95595,6.71458C7.013,6.71458,6.24864,7.47894,6.24864,8.421890000000001C6.24864,9.364840000000001,7.013,10.12921,7.95595,10.12921Z"
                    fill="#FFFFFF"
                    fillOpacity="1"
                />
                <path
                    d="M14.9818,9.14184Q15.342,8.4878,14.9818,7.83376L14.9818,7.83368L12.0631,2.541344Q11.6851,1.851542,10.89817,1.85L5.10154,1.85Q4.31493,1.851542,3.93705,2.541001L1.0181749,7.83376Q0.658012,8.48779,1.0182198,9.141919999999999L3.93686,14.4342Q4.31493,15.1241,5.10183,15.1256L10.89846,15.1256Q11.6851,15.1241,12.063,14.4346L14.9818,9.14184ZM14.719,7.97847L14.7191,7.97855Q14.9995,8.48784,14.719,8.997119999999999L11.8003,14.2897Q11.5072,14.8244,10.89787,14.8256L5.10183,14.8256Q4.4928,14.8244,4.19993,14.2901L1.28092,8.99704Q1.000491083,8.4878,1.2809650000000001,7.97847L4.19975,2.685876Q4.4928,2.151194,5.10213,2.15L10.89817,2.15Q11.5072,2.151194,11.8001,2.6855320000000003L14.719,7.97847ZM11.172,13.936L11.1722,13.9356L14.0857,8.64328Q14.1711,8.48788,14.0857,8.33257L11.172,3.03981Q11.0821,2.8749029999999998,10.89363,2.87439L5.10678,2.874389Q4.917949999999999,2.874904,4.82775,3.04034L1.914335,8.33248Q1.82892,8.48788,1.9142869999999998,8.6432L4.82804,13.936Q4.91816,14.1008,5.10646,14.1012L10.89322,14.1012Q11.0816,14.1008,11.172,13.936ZM13.8229,8.4986L10.90915,13.7913L10.90894,13.7917Q10.90374,13.8012,10.89322,13.8012L5.10711,13.8012Q5.09626,13.8012,5.09085,13.7913L2.17719,8.49869Q2.1712499999999997,8.48788,2.17714,8.47716L5.09114,3.1839500000000003Q5.09634,3.17442,5.10678,3.17439L10.89281,3.17439Q10.90367,3.17442,10.90915,3.1844900000000003L13.8228,8.47708Q13.8288,8.48788,13.8229,8.4986ZM9.99359,10.4596Q10.83765,9.61557,10.83766,8.421890000000001Q10.83765,7.22822,9.99359,6.38419Q9.14955,5.54019,7.95595,5.54019Q6.76225,5.54019,5.91825,6.38419Q5.07425,7.2282,5.07425,8.421890000000001Q5.07425,9.615590000000001,5.91825,10.45959Q6.76226,11.3036,7.95595,11.3036Q9.14955,11.3036,9.99359,10.4596ZM9.78146,6.59633Q10.53766,7.35249,10.53766,8.421890000000001Q10.53765,9.49129,9.78146,10.24746Q9.02528,11.0036,7.95595,11.0036Q6.88652,11.0036,6.13038,10.24746Q5.37425,9.49133,5.37425,8.421890000000001Q5.37425,7.35246,6.13038,6.59633Q6.88652,5.84019,7.95595,5.84019Q9.02529,5.84019,9.78146,6.59633ZM6.64262,7.10856Q6.09863,7.65255,6.09864,8.421890000000001Q6.09864,9.19124,6.64262,9.73522Q7.18661,10.27921,7.95595,10.27921Q8.7253,10.27921,9.26928,9.73522Q9.81327,9.191230000000001,9.81327,8.421890000000001Q9.81327,7.65255,9.26928,7.10856Q8.7253,6.56458,7.95595,6.56458Q7.18661,6.56458,6.64262,7.10856ZM6.85475,9.52309Q6.39864,9.066970000000001,6.39864,8.421890000000001Q6.39864,7.77681,6.85475,7.3207Q7.31087,6.86458,7.95595,6.86458Q8.60103,6.86458,9.05715,7.3207Q9.51327,7.77681,9.51327,8.421890000000001Q9.51327,9.066970000000001,9.05715,9.52309Q8.60103,9.97921,7.95595,9.97921Q7.31087,9.97921,6.85475,9.52309Z"
                    fillRule="evenodd"
                    fill="#FFFFFF"
                    fillOpacity="1"
                />
            </g>
        </g>
    </svg>
);

export const pauseSvg = () => (
    <svg width="16" height="16" viewBox="0 0 16 16">
        <g>
            <g />
            <g>
                <path
                    d="M8.5,16C4.35783,16,1,12.6422,1,8.5C1,4.35783,4.35783,1,8.5,1C12.6422,1,16,4.35783,16,8.5C16,12.6422,12.6422,16,8.5,16ZM8.50723,14.5994C11.8723,14.5994,14.6012,11.8705,14.6012,8.50542C14.6012,5.14036,11.8723,2.4114500000000003,8.50723,2.4114500000000003C5.14217,2.4114500000000003,2.4132499999999997,5.14036,2.4132499999999997,8.50542C2.4132499999999997,11.8705,5.14036,14.5994,8.50723,14.5994ZM6.15602,5.68614L10.84398,5.68614C11.1024,5.68614,11.312,5.89578,11.312,6.15422L11.312,10.84398C11.312,11.1024,11.1024,11.312,10.84398,11.312L6.15602,11.312C5.89759,11.312,5.68795,11.1024,5.68795,10.84398L5.68795,6.15602C5.68795,5.89578,5.89759,5.68614,6.15602,5.68614Z"
                    fill="#14C9BB"
                    fillOpacity="1"
                />
            </g>
        </g>
    </svg>
);

export const pauseDisabledSvg = () => (
    <svg width="16" height="16" viewBox="0 0 16 16">
        <g>
            <g />
            <g>
                <path
                    d="M8.5,16C4.35783,16,1,12.6422,1,8.5C1,4.35783,4.35783,1,8.5,1C12.6422,1,16,4.35783,16,8.5C16,12.6422,12.6422,16,8.5,16ZM8.50723,14.5994C11.8723,14.5994,14.6012,11.8705,14.6012,8.50542C14.6012,5.14036,11.8723,2.4114500000000003,8.50723,2.4114500000000003C5.14217,2.4114500000000003,2.4132499999999997,5.14036,2.4132499999999997,8.50542C2.4132499999999997,11.8705,5.14036,14.5994,8.50723,14.5994ZM6.15602,5.68614L10.84398,5.68614C11.1024,5.68614,11.312,5.89578,11.312,6.15422L11.312,10.84398C11.312,11.1024,11.1024,11.312,10.84398,11.312L6.15602,11.312C5.89759,11.312,5.68795,11.1024,5.68795,10.84398L5.68795,6.15602C5.68795,5.89578,5.89759,5.68614,6.15602,5.68614Z"
                    fill="#B3BBC8"
                    fillOpacity="1"
                />
            </g>
        </g>
    </svg>
);

export const resultSvg = () => (
    <svg width="16" height="16.150390625" viewBox="0 0 16 16.150390625">
        <g>
            <g />
            <g>
                <g>
                    <path
                        d="M12.4046,16.00000204143524L3.13285,16.00000204143524C1.956382,16.00000204143524,1,15.135702041435241,1,14.074202041435242L1,3.931172041435242C1,2.8707480414352418,1.956382,2.0064028114352417,3.13285,2.0064028114352417L4.585430000000001,2.0064028114352417C4.92253,2.0064028114352417,5.19568,2.2534620414352418,5.19568,2.5596640414352416C5.19568,2.8667470414352416,4.92253,3.1129220414352416,4.585430000000001,3.1129220414352416L3.13285,3.1129220414352416C2.63169,3.1129220414352416,2.2244900000000003,3.4810720414352416,2.2244900000000003,3.9332520414352414L2.2244900000000003,14.077302041435242C2.2244900000000003,14.528502041435242,2.6316100000000002,14.896602041435242,3.13285,14.896602041435242L12.4046,14.896602041435242C12.9068,14.896602041435242,13.313,14.52840204143524,13.313,14.076302041435241L13.313,3.9322120414352417C13.313,3.4789920414352418,12.9068,3.1118820414352415,12.4046,3.1118820414352415L10.95302,3.1118820414352415C10.61481,3.1118820414352415,10.34174,2.8648260414352418,10.34174,2.5576630414352417C10.34174,2.2515410414352415,10.61481,2.0044020414352417,10.95198,2.0044020414352417L12.4046,2.0044020414352417C13.581,2.0044020414352417,14.5373,2.8677870414352418,14.5373,3.929172041435242L14.5373,14.073202041435241C14.5374,15.135602041435241,13.581,16.00000204143524,12.4046,16.00000204143524Z"
                        fill="#14C9BB"
                        fillOpacity="1"
                    />
                    <path
                        d="M14.0127,15.546302041435242Q14.6874,14.936702041435241,14.6873,14.073202041435241L14.6873,3.929172041435242Q14.6873,3.0664120414352416,14.0126,2.4574160414352417Q13.3445,1.8544020414352418,12.4046,1.8544020414352418L10.95198,1.8544020414352418Q10.64116,1.8544020414352418,10.4197,2.0550822414352417Q10.19174,2.2616450414352416,10.19174,2.5576630414352417Q10.19174,2.854450041435242,10.41983,3.061122041435242Q10.64141,3.261882041435242,10.95302,3.261882041435242L12.4046,3.261882041435242Q12.7235,3.261882041435242,12.9466,3.4634020414352418Q13.163,3.6588820414352416,13.163,3.9322120414352417L13.163,14.076302041435241Q13.163,14.349002041435241,12.9465,14.544702041435242Q12.7232,14.746602041435242,12.4046,14.746602041435242L3.13285,14.746602041435242Q2.81477,14.746602041435242,2.5912100000000002,14.544802041435242Q2.3744899999999998,14.349202041435241,2.3744899999999998,14.077302041435242L2.3744899999999998,3.9332520414352414Q2.3744899999999998,3.6606820414352415,2.59129,3.4648320414352414Q2.81478,3.2629220414352416,3.13285,3.2629220414352416L4.585430000000001,3.2629220414352416Q4.89633,3.2629220414352416,5.11762,3.062672041435242Q5.34568,2.856281041435242,5.34568,2.5596640414352416Q5.34568,2.263604041435242,5.11768,2.0570435414352417Q4.89621,1.8564030414352417,4.585430000000001,1.8564030414352417L3.13285,1.8564030414352417Q2.19305,1.8564030414352417,1.52473,2.4597920414352417Q0.85,3.0689720414352415,0.85,3.931172041435242L0.85,14.074202041435242Q0.85,14.937102041435242,1.52471,15.546502041435241Q2.19294,16.150002041435243,3.13285,16.150002041435243L12.4046,16.150002041435243Q13.3446,16.150002041435243,14.0127,15.546302041435242ZM14.3873,3.929172041435242L14.3873,14.073202041435241Q14.3874,14.803502041435241,13.8116,15.323702041435242Q13.2291,15.850002041435241,12.4046,15.850002041435241L3.13285,15.850002041435241Q2.30837,15.850002041435241,1.725791,15.323802041435242Q1.15,14.803802041435242,1.15,14.074202041435242L1.15,3.931172041435242Q1.15,3.2023020414352414,1.72577,2.6824650414352416Q2.30844,2.1564030414352415,3.13285,2.1564030414352415L4.585430000000001,2.1564030414352415Q4.78052,2.1564030414352415,4.916259999999999,2.2793710414352417Q5.04568,2.3966220414352417,5.04568,2.5596640414352416Q5.04568,2.7231650414352417,4.91632,2.8402320414352418Q4.78074,2.9629250414352417,4.585430000000001,2.9629250414352417L3.13285,2.9629250414352417Q2.6993400000000003,2.962926041435242,2.39018,3.2422120414352418Q2.07449,3.527412041435242,2.07449,3.9332520414352414L2.07449,14.077302041435242Q2.07449,14.482602041435241,2.3902,14.767502041435241Q2.6994100000000003,15.046602041435241,3.13285,15.046602041435241L12.4046,15.046602041435241Q12.8387,15.046602041435241,13.1477,14.767302041435242Q13.463,14.482202041435242,13.463,14.076302041435241L13.463,3.9322120414352417Q13.463,3.525602041435242,13.1477,3.2407820414352417Q12.839,2.9618850414352416,12.4046,2.9618850414352416L10.95302,2.9618850414352416Q10.75711,2.9618850414352416,10.62126,2.8388020414352417Q10.49174,2.7214400414352418,10.49174,2.5576630414352417Q10.49174,2.394647041435242,10.62114,2.2773910414352416Q10.75687,2.1544020414352416,10.95198,2.1544020414352416L12.4046,2.1544020414352416Q13.2291,2.1544020414352416,13.8116,2.6801200414352415Q14.3873,3.1997620414352417,14.3873,3.929172041435242Z"
                        fillRule="evenodd"
                        fill="#14C9BB"
                        fillOpacity="1"
                    />
                </g>
                <g>
                    <path
                        d="M7.312277960052491,11.128C7.159077960052491,11.1286,7.01424796005249,11.0677,6.906307960052491,10.95898L4.97689796005249,9.04925C4.75288596005249,8.826360000000001,4.75403596005249,8.464030000000001,4.97689796005249,8.239989999999999L4.97689796005249,8.237960000000001C5.2031109600524905,8.01355,5.57099796005249,8.01355,5.79720796005249,8.237960000000001L7.316457960052491,9.73851L10.104017960052492,6.97336C10.33035796005249,6.7482,10.69798796005249,6.7482,10.92432796005249,6.97336C11.14867796005249,7.19519,11.150387960052491,7.55687,10.92851796005249,7.78118L10.92432796005249,7.78569L7.72661796005249,10.95898C7.6172079600524905,11.0667,7.47000796005249,11.1274,7.316457960052491,11.128L7.312277960052491,11.128ZM9.33148796005249,1L6.20719796005249,1C5.29683296005249,1,4.55853796005249,1.734294,4.55853796005249,2.63769C4.55853796005249,3.54206,5.29683296005249,4.27635,6.20719796005249,4.27635L9.33148796005249,4.27635C10.241847960052489,4.27635,10.980147960052491,3.54206,10.980147960052491,2.63769C10.980147960052491,1.734294,10.24080796005249,1.000000152649,9.33148796005249,1ZM9.33148796005249,2.15046C9.57191796005249,2.15049,9.83949796005249,2.32476,9.86385796005249,2.5944000000000003C9.89154796005249,2.89916,9.63625796005249,3.15449,9.33148796005249,3.12685L6.20719796005249,3.12685C5.92255796005249,3.14872,5.67952796005249,2.92366,5.67952796005249,2.63818C5.67952796005249,2.35269,5.92255796005249,2.12763,6.20719796005249,2.1494999999999997L9.33148796005249,2.1494999999999997L9.33148796005249,2.15046Z"
                        fill="#14C9BB"
                        fillOpacity="1"
                    />
                    <path
                        d="M10.602957960052489,3.90254Q11.13014796005249,3.37844,11.13014796005249,2.63769Q11.13014796005249,1.897739,10.602517960052491,1.37365Q10.07533796005249,0.85,9.33148796005249,0.85L6.20719796005249,0.85Q5.46272596005249,0.85,4.93574896005249,1.373673Q4.40853796005249,1.897579,4.40853796005249,2.63769Q4.40853796005249,3.37844,4.93572796005249,3.90254Q5.46263396005249,4.426349999999999,6.20719796005249,4.426349999999999L9.33148796005249,4.426349999999999Q10.07604796005249,4.426349999999999,10.602957960052489,3.90254ZM10.39110796005249,1.586494Q10.83014796005249,2.02259,10.83014796005249,2.63769Q10.83014796005249,3.25366,10.39144796005249,3.68978Q9.95230796005249,4.12635,9.33148796005249,4.12635L6.20719796005249,4.12635Q5.5863779600524905,4.12635,5.14723496005249,3.68978Q4.708537960052491,3.25366,4.708537960052491,2.63769Q4.708537960052491,2.0224,5.1472129600524905,1.586471Q5.58643796005249,1.15,6.20719796005249,1.15L9.33148796005249,1.15Q9.95166796005249,1.15,10.39110796005249,1.586494ZM5.7346979600524906,2.16853Q5.52953096005249,2.35854,5.52953196005249,2.63818Q5.52953196005249,2.9178100000000002,5.7346979600524906,3.10782Q5.9376379600524904,3.29577,6.21262796005249,3.27685L9.32506796005249,3.27685Q9.619007960052489,3.30078,9.82841796005249,3.09133Q10.04036796005249,2.87936,10.01324796005249,2.5808999999999997Q9.98988796005249,2.32224,9.77042796005249,2.15264Q9.63641796005249,2.04908,9.48148796005249,2.01599L9.48148796005249,1.9995L6.21263796005249,1.9995Q5.9376379600524904,1.980582,5.7346979600524906,2.16853ZM9.18148796005249,2.2995L6.20143796005249,2.2995L6.1957079600524905,2.29906Q6.04755796005249,2.28768,5.938547960052491,2.38863Q5.8295279600524905,2.4895899999999997,5.8295279600524905,2.63818Q5.8295279600524905,2.78676,5.938547960052491,2.88772Q6.04755796005249,2.98867,6.1957079600524905,2.97729L6.20143796005249,2.9768499999999998L9.33827796005249,2.9768499999999998L9.34503796005249,2.9774599999999998Q9.50365796005249,2.99185,9.61626796005249,2.87922Q9.72888796005249,2.76659,9.71446796005249,2.6078900000000003Q9.686697960052491,2.30051,9.331467960052489,2.30046L9.18148796005249,2.30044L9.18148796005249,2.2995ZM7.312277960052491,11.278L7.316457960052491,11.278L7.31708796005249,11.278Q7.617647960052491,11.2768,7.83227796005249,11.0654L11.032197960052489,7.88998L11.036927960052491,7.88487Q11.245257960052491,7.673,11.24374796005249,7.37558Q11.24224796005249,7.07676,11.03011796005249,6.86701Q10.81646796005249,6.65449,10.51417796005249,6.65449Q10.21187796005249,6.65449,9.99837796005249,6.86687L7.31623796005249,9.52746L5.90284796005249,8.13147Q5.68932796005249,7.91965,5.387053960052491,7.91965Q5.084776960052491,7.91965,4.8712559600524905,8.13147L4.826897960052491,8.17548L4.826897960052491,8.18166Q4.65958096005249,8.37895,4.65931996005249,8.6449Q4.659025960052491,8.944569999999999,4.8713769600524905,9.15586L6.79986796005249,11.0647Q7.01281796005249,11.2791,7.312277960052491,11.278ZM7.316457960052491,10.97802L7.31583796005249,10.97802L7.312277960052491,10.97802Q7.13724796005249,10.97867,7.01274796005249,10.85328L5.08241796005249,8.94264Q4.95914896005249,8.81999,4.959319960052491,8.645199999999999Q4.95949196005249,8.47018,5.08324196005249,8.345780000000001L5.10767996005249,8.32121Q5.22583696005249,8.21965,5.387053960052491,8.21965Q5.56576796005249,8.21965,5.69156796005249,8.34445L7.316687960052491,9.94957L10.209657960052489,7.07985Q10.33567796005249,6.95449,10.51417796005249,6.95449Q10.69266796005249,6.95449,10.818547960052491,7.0797Q10.94287796005249,7.20264,10.94375796005249,7.3771Q10.94463796005249,7.55158,10.82186796005249,7.67569L10.82017796005249,7.67741L10.81646796005249,7.68141L7.62095796005249,10.8525Q7.49444796005249,10.97706,7.316457960052491,10.97802Z"
                        fillRule="evenodd"
                        fill="#14C9BB"
                        fillOpacity="1"
                    />
                </g>
            </g>
        </g>
    </svg>
);

export const resultDisabledSvg = () => (
    <svg width="16" height="16.150390625" viewBox="0 0 16 16.150390625">
        <g>
            <g />
            <g>
                <g>
                    <path
                        d="M12.4046,16.00000204143524L3.13285,16.00000204143524C1.956382,16.00000204143524,1,15.135702041435241,1,14.074202041435242L1,3.931172041435242C1,2.8707480414352418,1.956382,2.0064028114352417,3.13285,2.0064028114352417L4.585430000000001,2.0064028114352417C4.92253,2.0064028114352417,5.19568,2.2534620414352418,5.19568,2.5596640414352416C5.19568,2.8667470414352416,4.92253,3.1129220414352416,4.585430000000001,3.1129220414352416L3.13285,3.1129220414352416C2.63169,3.1129220414352416,2.2244900000000003,3.4810720414352416,2.2244900000000003,3.9332520414352414L2.2244900000000003,14.077302041435242C2.2244900000000003,14.528502041435242,2.6316100000000002,14.896602041435242,3.13285,14.896602041435242L12.4046,14.896602041435242C12.9068,14.896602041435242,13.313,14.52840204143524,13.313,14.076302041435241L13.313,3.9322120414352417C13.313,3.4789920414352418,12.9068,3.1118820414352415,12.4046,3.1118820414352415L10.95302,3.1118820414352415C10.61481,3.1118820414352415,10.34174,2.8648260414352418,10.34174,2.5576630414352417C10.34174,2.2515410414352415,10.61481,2.0044020414352417,10.95198,2.0044020414352417L12.4046,2.0044020414352417C13.581,2.0044020414352417,14.5373,2.8677870414352418,14.5373,3.929172041435242L14.5373,14.073202041435241C14.5374,15.135602041435241,13.581,16.00000204143524,12.4046,16.00000204143524Z"
                        fill="#B3BBC8"
                        fillOpacity="1"
                    />
                    <path
                        d="M14.0127,15.546302041435242Q14.6874,14.936702041435241,14.6873,14.073202041435241L14.6873,3.929172041435242Q14.6873,3.0664120414352416,14.0126,2.4574160414352417Q13.3445,1.8544020414352418,12.4046,1.8544020414352418L10.95198,1.8544020414352418Q10.64116,1.8544020414352418,10.4197,2.0550822414352417Q10.19174,2.2616450414352416,10.19174,2.5576630414352417Q10.19174,2.854450041435242,10.41983,3.061122041435242Q10.64141,3.261882041435242,10.95302,3.261882041435242L12.4046,3.261882041435242Q12.7235,3.261882041435242,12.9466,3.4634020414352418Q13.163,3.6588820414352416,13.163,3.9322120414352417L13.163,14.076302041435241Q13.163,14.349002041435241,12.9465,14.544702041435242Q12.7232,14.746602041435242,12.4046,14.746602041435242L3.13285,14.746602041435242Q2.81477,14.746602041435242,2.5912100000000002,14.544802041435242Q2.3744899999999998,14.349202041435241,2.3744899999999998,14.077302041435242L2.3744899999999998,3.9332520414352414Q2.3744899999999998,3.6606820414352415,2.59129,3.4648320414352414Q2.81478,3.2629220414352416,3.13285,3.2629220414352416L4.585430000000001,3.2629220414352416Q4.89633,3.2629220414352416,5.11762,3.062672041435242Q5.34568,2.856281041435242,5.34568,2.5596640414352416Q5.34568,2.263604041435242,5.11768,2.0570435414352417Q4.89621,1.8564030414352417,4.585430000000001,1.8564030414352417L3.13285,1.8564030414352417Q2.19305,1.8564030414352417,1.52473,2.4597920414352417Q0.85,3.0689720414352415,0.85,3.931172041435242L0.85,14.074202041435242Q0.85,14.937102041435242,1.52471,15.546502041435241Q2.19294,16.150002041435243,3.13285,16.150002041435243L12.4046,16.150002041435243Q13.3446,16.150002041435243,14.0127,15.546302041435242ZM14.3873,3.929172041435242L14.3873,14.073202041435241Q14.3874,14.803502041435241,13.8116,15.323702041435242Q13.2291,15.850002041435241,12.4046,15.850002041435241L3.13285,15.850002041435241Q2.30837,15.850002041435241,1.725791,15.323802041435242Q1.15,14.803802041435242,1.15,14.074202041435242L1.15,3.931172041435242Q1.15,3.2023020414352414,1.72577,2.6824650414352416Q2.30844,2.1564030414352415,3.13285,2.1564030414352415L4.585430000000001,2.1564030414352415Q4.78052,2.1564030414352415,4.916259999999999,2.2793710414352417Q5.04568,2.3966220414352417,5.04568,2.5596640414352416Q5.04568,2.7231650414352417,4.91632,2.8402320414352418Q4.78074,2.9629250414352417,4.585430000000001,2.9629250414352417L3.13285,2.9629250414352417Q2.6993400000000003,2.962926041435242,2.39018,3.2422120414352418Q2.07449,3.527412041435242,2.07449,3.9332520414352414L2.07449,14.077302041435242Q2.07449,14.482602041435241,2.3902,14.767502041435241Q2.6994100000000003,15.046602041435241,3.13285,15.046602041435241L12.4046,15.046602041435241Q12.8387,15.046602041435241,13.1477,14.767302041435242Q13.463,14.482202041435242,13.463,14.076302041435241L13.463,3.9322120414352417Q13.463,3.525602041435242,13.1477,3.2407820414352417Q12.839,2.9618850414352416,12.4046,2.9618850414352416L10.95302,2.9618850414352416Q10.75711,2.9618850414352416,10.62126,2.8388020414352417Q10.49174,2.7214400414352418,10.49174,2.5576630414352417Q10.49174,2.394647041435242,10.62114,2.2773910414352416Q10.75687,2.1544020414352416,10.95198,2.1544020414352416L12.4046,2.1544020414352416Q13.2291,2.1544020414352416,13.8116,2.6801200414352415Q14.3873,3.1997620414352417,14.3873,3.929172041435242Z"
                        fillRule="evenodd"
                        fill="#B3BBC8"
                        fillOpacity="1"
                    />
                </g>
                <g>
                    <path
                        d="M7.312277960052491,11.128C7.159077960052491,11.1286,7.01424796005249,11.0677,6.906307960052491,10.95898L4.97689796005249,9.04925C4.75288596005249,8.826360000000001,4.75403596005249,8.464030000000001,4.97689796005249,8.239989999999999L4.97689796005249,8.237960000000001C5.2031109600524905,8.01355,5.57099796005249,8.01355,5.79720796005249,8.237960000000001L7.316457960052491,9.73851L10.104017960052492,6.97336C10.33035796005249,6.7482,10.69798796005249,6.7482,10.92432796005249,6.97336C11.14867796005249,7.19519,11.150387960052491,7.55687,10.92851796005249,7.78118L10.92432796005249,7.78569L7.72661796005249,10.95898C7.6172079600524905,11.0667,7.47000796005249,11.1274,7.316457960052491,11.128L7.312277960052491,11.128ZM9.33148796005249,1L6.20719796005249,1C5.29683296005249,1,4.55853796005249,1.734294,4.55853796005249,2.63769C4.55853796005249,3.54206,5.29683296005249,4.27635,6.20719796005249,4.27635L9.33148796005249,4.27635C10.241847960052489,4.27635,10.980147960052491,3.54206,10.980147960052491,2.63769C10.980147960052491,1.734294,10.24080796005249,1.000000152649,9.33148796005249,1ZM9.33148796005249,2.15046C9.57191796005249,2.15049,9.83949796005249,2.32476,9.86385796005249,2.5944000000000003C9.89154796005249,2.89916,9.63625796005249,3.15449,9.33148796005249,3.12685L6.20719796005249,3.12685C5.92255796005249,3.14872,5.67952796005249,2.92366,5.67952796005249,2.63818C5.67952796005249,2.35269,5.92255796005249,2.12763,6.20719796005249,2.1494999999999997L9.33148796005249,2.1494999999999997L9.33148796005249,2.15046Z"
                        fill="#B3BBC8"
                        fillOpacity="1"
                    />
                    <path
                        d="M10.602957960052489,3.90254Q11.13014796005249,3.37844,11.13014796005249,2.63769Q11.13014796005249,1.897739,10.602517960052491,1.37365Q10.07533796005249,0.85,9.33148796005249,0.85L6.20719796005249,0.85Q5.46272596005249,0.85,4.93574896005249,1.373673Q4.40853796005249,1.897579,4.40853796005249,2.63769Q4.40853796005249,3.37844,4.93572796005249,3.90254Q5.46263396005249,4.426349999999999,6.20719796005249,4.426349999999999L9.33148796005249,4.426349999999999Q10.07604796005249,4.426349999999999,10.602957960052489,3.90254ZM10.39110796005249,1.586494Q10.83014796005249,2.02259,10.83014796005249,2.63769Q10.83014796005249,3.25366,10.39144796005249,3.68978Q9.95230796005249,4.12635,9.33148796005249,4.12635L6.20719796005249,4.12635Q5.5863779600524905,4.12635,5.14723496005249,3.68978Q4.708537960052491,3.25366,4.708537960052491,2.63769Q4.708537960052491,2.0224,5.1472129600524905,1.586471Q5.58643796005249,1.15,6.20719796005249,1.15L9.33148796005249,1.15Q9.95166796005249,1.15,10.39110796005249,1.586494ZM5.7346979600524906,2.16853Q5.52953096005249,2.35854,5.52953196005249,2.63818Q5.52953196005249,2.9178100000000002,5.7346979600524906,3.10782Q5.9376379600524904,3.29577,6.21262796005249,3.27685L9.32506796005249,3.27685Q9.619007960052489,3.30078,9.82841796005249,3.09133Q10.04036796005249,2.87936,10.01324796005249,2.5808999999999997Q9.98988796005249,2.32224,9.77042796005249,2.15264Q9.63641796005249,2.04908,9.48148796005249,2.01599L9.48148796005249,1.9995L6.21263796005249,1.9995Q5.9376379600524904,1.980582,5.7346979600524906,2.16853ZM9.18148796005249,2.2995L6.20143796005249,2.2995L6.1957079600524905,2.29906Q6.04755796005249,2.28768,5.938547960052491,2.38863Q5.8295279600524905,2.4895899999999997,5.8295279600524905,2.63818Q5.8295279600524905,2.78676,5.938547960052491,2.88772Q6.04755796005249,2.98867,6.1957079600524905,2.97729L6.20143796005249,2.9768499999999998L9.33827796005249,2.9768499999999998L9.34503796005249,2.9774599999999998Q9.50365796005249,2.99185,9.61626796005249,2.87922Q9.72888796005249,2.76659,9.71446796005249,2.6078900000000003Q9.686697960052491,2.30051,9.331467960052489,2.30046L9.18148796005249,2.30044L9.18148796005249,2.2995ZM7.312277960052491,11.278L7.316457960052491,11.278L7.31708796005249,11.278Q7.617647960052491,11.2768,7.83227796005249,11.0654L11.032197960052489,7.88998L11.036927960052491,7.88487Q11.245257960052491,7.673,11.24374796005249,7.37558Q11.24224796005249,7.07676,11.03011796005249,6.86701Q10.81646796005249,6.65449,10.51417796005249,6.65449Q10.21187796005249,6.65449,9.99837796005249,6.86687L7.31623796005249,9.52746L5.90284796005249,8.13147Q5.68932796005249,7.91965,5.387053960052491,7.91965Q5.084776960052491,7.91965,4.8712559600524905,8.13147L4.826897960052491,8.17548L4.826897960052491,8.18166Q4.65958096005249,8.37895,4.65931996005249,8.6449Q4.659025960052491,8.944569999999999,4.8713769600524905,9.15586L6.79986796005249,11.0647Q7.01281796005249,11.2791,7.312277960052491,11.278ZM7.316457960052491,10.97802L7.31583796005249,10.97802L7.312277960052491,10.97802Q7.13724796005249,10.97867,7.01274796005249,10.85328L5.08241796005249,8.94264Q4.95914896005249,8.81999,4.959319960052491,8.645199999999999Q4.95949196005249,8.47018,5.08324196005249,8.345780000000001L5.10767996005249,8.32121Q5.22583696005249,8.21965,5.387053960052491,8.21965Q5.56576796005249,8.21965,5.69156796005249,8.34445L7.316687960052491,9.94957L10.209657960052489,7.07985Q10.33567796005249,6.95449,10.51417796005249,6.95449Q10.69266796005249,6.95449,10.818547960052491,7.0797Q10.94287796005249,7.20264,10.94375796005249,7.3771Q10.94463796005249,7.55158,10.82186796005249,7.67569L10.82017796005249,7.67741L10.81646796005249,7.68141L7.62095796005249,10.8525Q7.49444796005249,10.97706,7.316457960052491,10.97802Z"
                        fillRule="evenodd"
                        fill="#B3BBC8"
                        fillOpacity="1"
                    />
                </g>
            </g>
        </g>
    </svg>
);

export const testConfigSvg = () => (
    <svg width="16" height="16" viewBox="0 0 16 16">
        <g>
            <g />
            <g>
                <path
                    d="M8.49983,8.06239L8.49983,1.499977C8.49983,1.223855,8.2736,1,7.99748,1C7.72136,1,7.49513,1.223855,7.49513,1.499977L7.49513,8.06335C6.63719,8.28602,5.99814,9.06856,5.99814,9.99982C5.99814,10.93249,6.63595,11.716,7.49954,11.9373C7.49677,11.9581,7.4953,11.979,7.49513,11.9999L7.49513,14.4998C7.49513,14.7759,7.72136,14.9998,7.99748,14.9998C8.2736,14.9998,8.49983,14.7759,8.49983,14.4998L8.49983,11.9999C8.49943,11.9793,8.497779999999999,11.9587,8.49486,11.9383C9.36031,11.7182,10.00106,10.93362,10.00106,9.99993C10.00123,9.06726,9.36342,8.28382,8.49983,8.06239ZM8.70641,10.70689C8.24147,11.1718,7.46204,11.0692,7.13326,10.4998C6.80449,9.93037,7.10532,9.20405,7.74045,9.03385C8.375589999999999,8.863669999999999,8.9993,9.34224,8.9993,9.99976C9.00005,10.26515,8.894580000000001,10.5198,8.70641,10.70689L8.70641,10.70689ZM3.49436,3.06251C3.49717,3.04177,3.4987,3.02089,3.49893,2.9999700000000002L3.49893,1.499977C3.49893,1.224097,3.27528,1.000451501,2.9994,1.000451501C2.7235199999999997,1.000451501,2.49988,1.224097,2.49988,1.499977L2.49988,2.9999700000000002C2.4994899999999998,3.02115,2.5004299999999997,3.04234,2.5027,3.06341C1.639111,3.28467,1,4.068099999999999,1,5.00089C1,5.93368,1.639224,6.71678,2.50281,6.93832C2.5006,6.95875,2.4996400000000003,6.97929,2.49993,6.99984L2.49993,14.4998C2.49993,14.7757,2.72358,14.9993,2.99946,14.9993C3.27534,14.9993,3.49898,14.7757,3.49898,14.4998L3.49898,6.9999C3.49876,6.9796,3.49731,6.95934,3.49464,6.93922C4.36003,6.71909,5.0001,5.93453,5.0001,5.00089C5.0001,4.06726,4.359859999999999,3.28252,3.49436,3.06251ZM3.70647,5.70802C3.24153,6.17297,2.4621,6.07037,2.1333200000000003,5.50093C1.804542,4.9315,2.1053800000000003,4.20518,2.7405,4.03498C3.37564,3.8648,3.99936,4.34337,3.99936,5.00089C4.00009,5.26626,3.89463,5.52089,3.70647,5.70802ZM14.9992,5.00089C14.9992,4.06692,14.3578,3.28252,13.4922,3.06251C13.4943,3.04174,13.4953,3.02085,13.4949,2.9999700000000002L13.4949,1.499977C13.4949,1.224097,13.2712,1.000451578,12.9953,1.000451578C12.7195,1.000451578,12.4958,1.224097,12.4958,1.499977L12.4958,2.9999700000000002C12.496,3.02119,12.4976,3.04238,12.5005,3.06341C11.6369,3.28467,10.9987,4.068099999999999,10.9987,5.00089C10.9987,5.93368,11.6365,6.71706,12.5001,6.93837C12.4973,6.95909,12.4959,6.97996,12.4957,7.00086L12.4957,14.4998C12.4953,14.7759,12.7191,15,12.9952,15C13.2714,15,13.4951,14.7759,13.4947,14.4998L13.4947,7.00086C13.4951,6.9803,13.4943,6.95973,13.4922,6.93928C14.3579,6.7192,14.9992,5.93481,14.9992,5.00089ZM13.7063,5.70796C13.2413,6.17284,12.462,6.07016,12.1333,5.50073C11.8046,4.931290000000001,12.1055,4.20505,12.7406,4.03492C13.3756,3.86476,13.9992,4.3433600000000006,13.9992,5.00089C13.9999,5.26625,13.8945,5.52086,13.7063,5.70796Z"
                    fill="#FFFFFF"
                    fillOpacity="1"
                />
                <path
                    d="M9.76882,11.3915Q10.25105,10.7799,10.25106,9.99998L10.25106,9.99993Q10.25119,9.2209,9.77089,8.60994Q9.36025,8.08757,8.74983,7.87659L8.74983,1.499977Q8.74983,1.189079,8.528400000000001,0.9690901Q8.307870000000001,0.75,7.99748,0.75Q7.68709,0.75,7.46656,0.9690901Q7.24513,1.189079,7.24513,1.499977L7.24513,7.87812Q6.63795,8.09,6.22832,8.611419999999999Q5.74814,9.22265,5.74814,9.99982Q5.74814,10.77889,6.22846,11.3898Q6.63767,11.9103,7.24514,12.1216L7.24513,14.4998Q7.24513,14.8107,7.46656,15.0307Q7.68709,15.2498,7.99748,15.2498Q8.30788,15.2498,8.528400000000001,15.0307Q8.74983,14.8107,8.74983,14.4998L8.74983,12.1228Q9.3583,11.9122,9.76882,11.3915ZM4.76826,6.39235Q5.2501,5.78084,5.2501,5.00089Q5.2501,4.22092,4.76815,3.60935Q4.35761,3.08841,3.74891,2.87789L3.74893,1.499977Q3.74893,1.189514,3.5294,0.9699826Q3.30987,0.750452,2.9994,0.750452Q2.68894,0.750452,2.46941,0.9699826Q2.24988,1.189514,2.24988,1.499978L2.24988,2.87854Q1.641206,3.08966,1.231063,3.6107Q0.75,4.221830000000001,0.75,5.00089Q0.75,5.7799,1.231138,6.39098Q1.641256,6.91186,2.2499599999999997,7.12312L2.24993,14.4998Q2.24993,14.8102,2.4694599999999998,15.0298Q2.68899,15.2493,2.99946,15.2493Q3.30992,15.2493,3.52945,15.0298Q3.74898,14.8102,3.74898,14.4998L3.74898,7.12388Q4.35776,6.91332,4.76826,6.39235ZM13.7447,7.12458L13.7447,14.4994Q13.7452,14.8102,13.5256,15.0301Q13.306,15.25,12.9952,15.25Q12.6845,15.25,12.4649,15.0301Q12.2453,14.8102,12.2457,14.4998L12.2457,7.12267Q11.6382,6.9114,11.229,6.39095Q10.7487,5.78003,10.7487,5.00089Q10.7487,4.221769999999999,11.2292,3.61078Q11.6385,3.09046,12.2458,2.8792L12.2458,1.499977Q12.2458,1.189514,12.4653,0.9699829Q12.6849,0.750452,12.9953,0.750452Q13.3058,0.750452,13.5253,0.9699822Q13.7449,1.189515,13.7449,1.499977L13.7448,2.8772200000000003Q14.355,3.08756,14.7666,3.60911Q15.2492,4.22077,15.2492,5.00089Q15.2492,5.78099,14.7666,6.39263Q14.355,6.91421,13.7447,7.12458ZM9.37781,8.918949999999999Q9.75117,9.39388,9.75106,9.99989L9.75106,9.99993Q9.75106,10.60648,9.37619,11.0819Q9.00706,11.5501,8.43324,11.696L8.21565,11.7514L8.24737,11.9736Q8.24958,11.9891,8.24983,11.9999L8.24983,14.4998Q8.24983,14.7498,7.99748,14.7498Q7.74513,14.7498,7.74513,14.4998L7.74513,12.0019Q7.74525,11.986,7.74735,11.9703L7.77665,11.7502L7.5616,11.6951Q6.98916,11.5484,6.62153,11.0808Q6.24814,10.60587,6.24814,9.99982Q6.24814,9.39556,6.6215,8.920300000000001Q6.98852,8.45312,7.55794,8.30533L7.74513,8.25675L7.74513,1.499977Q7.74513,1.376168,7.80704,1.31368Q7.87012,1.25,7.99748,1.25Q8.24983,1.25,8.24983,1.499977L8.24983,8.25638L8.43774,8.30456Q9.01021,8.45134,9.37781,8.918949999999999ZM4.37544,3.91883Q4.7501,4.39426,4.7501,5.00089Q4.7501,5.60752,4.3755299999999995,6.08289Q4.00666,6.55102,3.43301,6.69694L3.21758,6.75173L3.24681,6.97209Q3.24883,6.98732,3.24898,6.9999L3.24898,14.4998Q3.24898,14.7493,2.99946,14.7493Q2.8960999999999997,14.7493,2.82302,14.6762Q2.74993,14.6031,2.74993,14.4998L2.74991,6.99629Q2.74969,6.9807,2.75136,6.96519L2.77463,6.74995L2.56493,6.69616Q1.9921069999999999,6.54921,1.623983,6.08167Q1.25,5.60668,1.25,5.00089Q1.25,4.39501,1.623945,3.91996Q1.992057,3.45231,2.56475,3.30559L2.7744400000000002,3.25186L2.7512600000000003,3.03664Q2.74954,3.02064,2.74988,2.9999700000000002L2.74988,1.499977Q2.74988,1.39662,2.82296,1.323536Q2.89604,1.2504520000000001,2.9994,1.2504520000000001Q3.10276,1.2504520000000001,3.17584,1.323536Q3.24893,1.39662,3.24893,1.499977L3.24894,2.9972700000000003Q3.24877,3.01312,3.24664,3.02882L3.21658,3.24984L3.43276,3.3048Q4.00647,3.45064,4.37544,3.91883ZM14.3741,6.08292Q14.0047,6.55103,13.4306,6.69698L13.2213,6.75019L13.2435,6.96498Q13.2451,6.98053,13.2447,7.00086L13.2447,14.5001Q13.2449,14.6036,13.1718,14.6768Q13.0987,14.75,12.9952,14.75Q12.8918,14.75,12.8187,14.6768Q12.7456,14.6036,12.7457,14.4998L12.7457,7.00292Q12.7458,6.98708,12.7479,6.97141L12.7772,6.75132L12.5622,6.6962Q11.9897,6.5495,11.6221,6.08192Q11.2487,5.60701,11.2487,5.00089Q11.2487,4.39484,11.6223,3.91988Q11.99,3.45227,12.5625,3.30559L12.7786,3.25024L12.7482,3.02931Q12.746,3.01338,12.7458,2.9999700000000002L12.7458,1.499977Q12.7458,1.2504520000000001,12.9953,1.2504520000000001Q13.2449,1.2504520000000001,13.2449,1.499977L13.2449,3.00465Q13.2452,3.02047,13.2435,3.03624L13.2208,3.25149L13.4306,3.3048Q14.0047,3.45072,14.374,3.91883Q14.7492,4.39428,14.7492,5.00089Q14.7492,5.60748,14.3741,6.08292ZM14.2492,5.00089L14.2492,5.00023Q14.249,4.38427,13.7602,4.0092Q13.2712,3.63391,12.6759,3.79343Q12.0805,3.95293,11.8445,4.52239Q11.6086,5.09186,11.9167,5.62571Q12.2249,6.15956,12.836,6.24007Q13.4472,6.32058,13.8826,5.88523Q14.2504,5.51949,14.2492,5.00089ZM4.249359999999999,5.00089L4.249359999999999,5.0002Q4.24908,4.38425,3.76029,4.0092099999999995Q3.27124,3.63395,2.67579,3.7935Q2.0803700000000003,3.95306,1.844477,4.52258Q1.608587,5.09209,1.916814,5.62594Q2.22504,6.15978,2.83621,6.24023Q3.44737,6.32068,3.88276,5.88528Q4.2505500000000005,5.5195,4.249359999999999,5.00089ZM13.4558,4.40585Q13.7492,4.6310199999999995,13.7492,5.00089L13.7492,5.00156Q13.7501,5.31188,13.53,5.53069Q13.268,5.79266,12.9013,5.74435Q12.5347,5.69605,12.3498,5.37575Q12.1649,5.05545,12.3064,4.71377Q12.448,4.3721,12.8052,4.27641Q13.1625,4.18069,13.4558,4.40585ZM3.45592,4.405889999999999Q3.74936,4.6310400000000005,3.74936,5.00089L3.74936,5.00159Q3.75022,5.31192,3.53018,5.53076Q3.26816,5.79278,2.90146,5.74451Q2.53476,5.69624,2.3498200000000002,5.37593Q2.1648899999999998,5.05562,2.30642,4.71391Q2.44795,4.372199999999999,2.8052099999999998,4.27646Q3.16248,4.1807300000000005,3.45592,4.405889999999999ZM8.88268,10.88417Q9.25052,10.51845,9.2493,9.99976L9.2493,9.99905Q9.24902,9.38311,8.76024,9.00808Q8.271180000000001,8.632819999999999,7.67574,8.79237Q7.08031,8.95193,6.84442,9.52144Q6.60853,10.09096,6.91676,10.62481Q7.22499,11.1587,7.83615,11.2391Q8.44731,11.3196,8.88268,10.88417ZM8.45587,9.40476Q8.7493,9.62991,8.7493,9.99976L8.7493,10.00047Q8.75018,10.31084,8.530149999999999,10.5296Q8.26811,10.79165,7.9014,10.74338Q7.5347,10.69511,7.34977,10.3748Q7.16483,10.05449,7.30636,9.71278Q7.4479,9.37107,7.80516,9.27533Q8.16243,9.1796,8.45587,9.40476Z"
                    fillRule="evenodd"
                    fill="#FFFFFF"
                    fillOpacity="1"
                />
            </g>
        </g>
    </svg>
);

export const testConfigDisabledSvg = () => (
    <svg width="16" height="16" viewBox="0 0 16 16">
        <g>
            <g />
            <g>
                <path
                    d="M8.49983,8.06239L8.49983,1.499977C8.49983,1.223855,8.2736,1,7.99748,1C7.72136,1,7.49513,1.223855,7.49513,1.499977L7.49513,8.06335C6.63719,8.28602,5.99814,9.06856,5.99814,9.99982C5.99814,10.93249,6.63595,11.716,7.49954,11.9373C7.49677,11.9581,7.4953,11.979,7.49513,11.9999L7.49513,14.4998C7.49513,14.7759,7.72136,14.9998,7.99748,14.9998C8.2736,14.9998,8.49983,14.7759,8.49983,14.4998L8.49983,11.9999C8.49943,11.9793,8.497779999999999,11.9587,8.49486,11.9383C9.36031,11.7182,10.00106,10.93362,10.00106,9.99993C10.00123,9.06726,9.36342,8.28382,8.49983,8.06239ZM8.70641,10.70689C8.24147,11.1718,7.46204,11.0692,7.13326,10.4998C6.80449,9.93037,7.10532,9.20405,7.74045,9.03385C8.375589999999999,8.863669999999999,8.9993,9.34224,8.9993,9.99976C9.00005,10.26515,8.894580000000001,10.5198,8.70641,10.70689L8.70641,10.70689ZM3.49436,3.06251C3.49717,3.04177,3.4987,3.02089,3.49893,2.9999700000000002L3.49893,1.499977C3.49893,1.224097,3.27528,1.000451501,2.9994,1.000451501C2.7235199999999997,1.000451501,2.49988,1.224097,2.49988,1.499977L2.49988,2.9999700000000002C2.4994899999999998,3.02115,2.5004299999999997,3.04234,2.5027,3.06341C1.639111,3.28467,1,4.068099999999999,1,5.00089C1,5.93368,1.639224,6.71678,2.50281,6.93832C2.5006,6.95875,2.4996400000000003,6.97929,2.49993,6.99984L2.49993,14.4998C2.49993,14.7757,2.72358,14.9993,2.99946,14.9993C3.27534,14.9993,3.49898,14.7757,3.49898,14.4998L3.49898,6.9999C3.49876,6.9796,3.49731,6.95934,3.49464,6.93922C4.36003,6.71909,5.0001,5.93453,5.0001,5.00089C5.0001,4.06726,4.359859999999999,3.28252,3.49436,3.06251ZM3.70647,5.70802C3.24153,6.17297,2.4621,6.07037,2.1333200000000003,5.50093C1.804542,4.9315,2.1053800000000003,4.20518,2.7405,4.03498C3.37564,3.8648,3.99936,4.34337,3.99936,5.00089C4.00009,5.26626,3.89463,5.52089,3.70647,5.70802ZM14.9992,5.00089C14.9992,4.06692,14.3578,3.28252,13.4922,3.06251C13.4943,3.04174,13.4953,3.02085,13.4949,2.9999700000000002L13.4949,1.499977C13.4949,1.224097,13.2712,1.000451578,12.9953,1.000451578C12.7195,1.000451578,12.4958,1.224097,12.4958,1.499977L12.4958,2.9999700000000002C12.496,3.02119,12.4976,3.04238,12.5005,3.06341C11.6369,3.28467,10.9987,4.068099999999999,10.9987,5.00089C10.9987,5.93368,11.6365,6.71706,12.5001,6.93837C12.4973,6.95909,12.4959,6.97996,12.4957,7.00086L12.4957,14.4998C12.4953,14.7759,12.7191,15,12.9952,15C13.2714,15,13.4951,14.7759,13.4947,14.4998L13.4947,7.00086C13.4951,6.9803,13.4943,6.95973,13.4922,6.93928C14.3579,6.7192,14.9992,5.93481,14.9992,5.00089ZM13.7063,5.70796C13.2413,6.17284,12.462,6.07016,12.1333,5.50073C11.8046,4.931290000000001,12.1055,4.20505,12.7406,4.03492C13.3756,3.86476,13.9992,4.3433600000000006,13.9992,5.00089C13.9999,5.26625,13.8945,5.52086,13.7063,5.70796Z"
                    fill="#14C9BB"
                    fillOpacity="1"
                />
                <path
                    d="M9.76882,11.3915Q10.25105,10.7799,10.25106,9.99998L10.25106,9.99993Q10.25119,9.2209,9.77089,8.60994Q9.36025,8.08757,8.74983,7.87659L8.74983,1.499977Q8.74983,1.189079,8.528400000000001,0.9690901Q8.307870000000001,0.75,7.99748,0.75Q7.68709,0.75,7.46656,0.9690901Q7.24513,1.189079,7.24513,1.499977L7.24513,7.87812Q6.63795,8.09,6.22832,8.611419999999999Q5.74814,9.22265,5.74814,9.99982Q5.74814,10.77889,6.22846,11.3898Q6.63767,11.9103,7.24514,12.1216L7.24513,14.4998Q7.24513,14.8107,7.46656,15.0307Q7.68709,15.2498,7.99748,15.2498Q8.30788,15.2498,8.528400000000001,15.0307Q8.74983,14.8107,8.74983,14.4998L8.74983,12.1228Q9.3583,11.9122,9.76882,11.3915ZM4.76826,6.39235Q5.2501,5.78084,5.2501,5.00089Q5.2501,4.22092,4.76815,3.60935Q4.35761,3.08841,3.74891,2.87789L3.74893,1.499977Q3.74893,1.189514,3.5294,0.9699826Q3.30987,0.750452,2.9994,0.750452Q2.68894,0.750452,2.46941,0.9699826Q2.24988,1.189514,2.24988,1.499978L2.24988,2.87854Q1.641206,3.08966,1.231063,3.6107Q0.75,4.221830000000001,0.75,5.00089Q0.75,5.7799,1.231138,6.39098Q1.641256,6.91186,2.2499599999999997,7.12312L2.24993,14.4998Q2.24993,14.8102,2.4694599999999998,15.0298Q2.68899,15.2493,2.99946,15.2493Q3.30992,15.2493,3.52945,15.0298Q3.74898,14.8102,3.74898,14.4998L3.74898,7.12388Q4.35776,6.91332,4.76826,6.39235ZM13.7447,7.12458L13.7447,14.4994Q13.7452,14.8102,13.5256,15.0301Q13.306,15.25,12.9952,15.25Q12.6845,15.25,12.4649,15.0301Q12.2453,14.8102,12.2457,14.4998L12.2457,7.12267Q11.6382,6.9114,11.229,6.39095Q10.7487,5.78003,10.7487,5.00089Q10.7487,4.221769999999999,11.2292,3.61078Q11.6385,3.09046,12.2458,2.8792L12.2458,1.499977Q12.2458,1.189514,12.4653,0.9699829Q12.6849,0.750452,12.9953,0.750452Q13.3058,0.750452,13.5253,0.9699822Q13.7449,1.189515,13.7449,1.499977L13.7448,2.8772200000000003Q14.355,3.08756,14.7666,3.60911Q15.2492,4.22077,15.2492,5.00089Q15.2492,5.78099,14.7666,6.39263Q14.355,6.91421,13.7447,7.12458ZM9.37781,8.918949999999999Q9.75117,9.39388,9.75106,9.99989L9.75106,9.99993Q9.75106,10.60648,9.37619,11.0819Q9.00706,11.5501,8.43324,11.696L8.21565,11.7514L8.24737,11.9736Q8.24958,11.9891,8.24983,11.9999L8.24983,14.4998Q8.24983,14.7498,7.99748,14.7498Q7.74513,14.7498,7.74513,14.4998L7.74513,12.0019Q7.74525,11.986,7.74735,11.9703L7.77665,11.7502L7.5616,11.6951Q6.98916,11.5484,6.62153,11.0808Q6.24814,10.60587,6.24814,9.99982Q6.24814,9.39556,6.6215,8.920300000000001Q6.98852,8.45312,7.55794,8.30533L7.74513,8.25675L7.74513,1.499977Q7.74513,1.376168,7.80704,1.31368Q7.87012,1.25,7.99748,1.25Q8.24983,1.25,8.24983,1.499977L8.24983,8.25638L8.43774,8.30456Q9.01021,8.45134,9.37781,8.918949999999999ZM4.37544,3.91883Q4.7501,4.39426,4.7501,5.00089Q4.7501,5.60752,4.3755299999999995,6.08289Q4.00666,6.55102,3.43301,6.69694L3.21758,6.75173L3.24681,6.97209Q3.24883,6.98732,3.24898,6.9999L3.24898,14.4998Q3.24898,14.7493,2.99946,14.7493Q2.8960999999999997,14.7493,2.82302,14.6762Q2.74993,14.6031,2.74993,14.4998L2.74991,6.99629Q2.74969,6.9807,2.75136,6.96519L2.77463,6.74995L2.56493,6.69616Q1.9921069999999999,6.54921,1.623983,6.08167Q1.25,5.60668,1.25,5.00089Q1.25,4.39501,1.623945,3.91996Q1.992057,3.45231,2.56475,3.30559L2.7744400000000002,3.25186L2.7512600000000003,3.03664Q2.74954,3.02064,2.74988,2.9999700000000002L2.74988,1.499977Q2.74988,1.39662,2.82296,1.323536Q2.89604,1.2504520000000001,2.9994,1.2504520000000001Q3.10276,1.2504520000000001,3.17584,1.323536Q3.24893,1.39662,3.24893,1.499977L3.24894,2.9972700000000003Q3.24877,3.01312,3.24664,3.02882L3.21658,3.24984L3.43276,3.3048Q4.00647,3.45064,4.37544,3.91883ZM14.3741,6.08292Q14.0047,6.55103,13.4306,6.69698L13.2213,6.75019L13.2435,6.96498Q13.2451,6.98053,13.2447,7.00086L13.2447,14.5001Q13.2449,14.6036,13.1718,14.6768Q13.0987,14.75,12.9952,14.75Q12.8918,14.75,12.8187,14.6768Q12.7456,14.6036,12.7457,14.4998L12.7457,7.00292Q12.7458,6.98708,12.7479,6.97141L12.7772,6.75132L12.5622,6.6962Q11.9897,6.5495,11.6221,6.08192Q11.2487,5.60701,11.2487,5.00089Q11.2487,4.39484,11.6223,3.91988Q11.99,3.45227,12.5625,3.30559L12.7786,3.25024L12.7482,3.02931Q12.746,3.01338,12.7458,2.9999700000000002L12.7458,1.499977Q12.7458,1.2504520000000001,12.9953,1.2504520000000001Q13.2449,1.2504520000000001,13.2449,1.499977L13.2449,3.00465Q13.2452,3.02047,13.2435,3.03624L13.2208,3.25149L13.4306,3.3048Q14.0047,3.45072,14.374,3.91883Q14.7492,4.39428,14.7492,5.00089Q14.7492,5.60748,14.3741,6.08292ZM14.2492,5.00089L14.2492,5.00023Q14.249,4.38427,13.7602,4.0092Q13.2712,3.63391,12.6759,3.79343Q12.0805,3.95293,11.8445,4.52239Q11.6086,5.09186,11.9167,5.62571Q12.2249,6.15956,12.836,6.24007Q13.4472,6.32058,13.8826,5.88523Q14.2504,5.51949,14.2492,5.00089ZM4.249359999999999,5.00089L4.249359999999999,5.0002Q4.24908,4.38425,3.76029,4.0092099999999995Q3.27124,3.63395,2.67579,3.7935Q2.0803700000000003,3.95306,1.844477,4.52258Q1.608587,5.09209,1.916814,5.62594Q2.22504,6.15978,2.83621,6.24023Q3.44737,6.32068,3.88276,5.88528Q4.2505500000000005,5.5195,4.249359999999999,5.00089ZM13.4558,4.40585Q13.7492,4.6310199999999995,13.7492,5.00089L13.7492,5.00156Q13.7501,5.31188,13.53,5.53069Q13.268,5.79266,12.9013,5.74435Q12.5347,5.69605,12.3498,5.37575Q12.1649,5.05545,12.3064,4.71377Q12.448,4.3721,12.8052,4.27641Q13.1625,4.18069,13.4558,4.40585ZM3.45592,4.405889999999999Q3.74936,4.6310400000000005,3.74936,5.00089L3.74936,5.00159Q3.75022,5.31192,3.53018,5.53076Q3.26816,5.79278,2.90146,5.74451Q2.53476,5.69624,2.3498200000000002,5.37593Q2.1648899999999998,5.05562,2.30642,4.71391Q2.44795,4.372199999999999,2.8052099999999998,4.27646Q3.16248,4.1807300000000005,3.45592,4.405889999999999ZM8.88268,10.88417Q9.25052,10.51845,9.2493,9.99976L9.2493,9.99905Q9.24902,9.38311,8.76024,9.00808Q8.271180000000001,8.632819999999999,7.67574,8.79237Q7.08031,8.95193,6.84442,9.52144Q6.60853,10.09096,6.91676,10.62481Q7.22499,11.1587,7.83615,11.2391Q8.44731,11.3196,8.88268,10.88417ZM8.45587,9.40476Q8.7493,9.62991,8.7493,9.99976L8.7493,10.00047Q8.75018,10.31084,8.530149999999999,10.5296Q8.26811,10.79165,7.9014,10.74338Q7.5347,10.69511,7.34977,10.3748Q7.16483,10.05449,7.30636,9.71278Q7.4479,9.37107,7.80516,9.27533Q8.16243,9.1796,8.45587,9.40476Z"
                    fillRule="evenodd"
                    fill="#14C9BB"
                    fillOpacity="1"
                />
            </g>
        </g>
    </svg>
);

export const SpectrogramSvg = () => (
    <svg width="16.25" height="16" viewBox="0 0 16.25 16">
        <g>
            <g />
            <g>
                <g>
                    <path
                        d="M14.6944,14.2222L0.805556,14.2222C0.49873100000000004,14.2222,0.25,13.9735,0.25,13.6667L0.25,2.555556C0.25,2.248731,0.49873100000000004,2,0.805556,2C1.112381,2,1.36111,2.248731,1.36111,2.555556L1.36111,13.1111L14.6944,13.1111C15.0013,13.1111,15.25,13.3598,15.25,13.6667C15.25,13.9735,15.0013,14.2222,14.6944,14.2222Z"
                        fill="#14C9BB"
                        fillOpacity="1"
                    />
                    <path
                        d="M15.2641,14.2363Q15.5,14.0003,15.5,13.6667Q15.5,13.333,15.2641,13.0971Q15.0281,12.8611,14.6944,12.8611L1.61111,12.8611L1.61111,2.555556Q1.61111,2.221884,1.37517,1.9859418Q1.1392280000000001,1.75,0.805556,1.75Q0.471883,1.75,0.2359417,1.9859418Q0,2.221884,0,2.555556L0,13.6667Q0,14.0003,0.2359417,14.2363Q0.471883,14.4722,0.805556,14.4722L14.6944,14.4722Q15.0281,14.4722,15.2641,14.2363ZM14.6944,13.3611Q15,13.3611,15,13.6667Q15,13.9722,14.6944,13.9722L0.805556,13.9722Q0.5,13.9722,0.5,13.6667L0.5,2.555556Q0.5,2.4289899999999998,0.589495,2.339495Q0.67899,2.25,0.805556,2.25Q1.111111,2.25,1.111111,2.555556L1.111111,13.3611L14.6944,13.3611Z"
                        fillRule="evenodd"
                        fill="#14C9BB"
                        fillOpacity="1"
                    />
                </g>
                <g>
                    <path
                        d="M3.4272093969116213,9.77777651309967C3.195084396911621,9.77810651309967,2.9872213969116213,9.634086513099671,2.9059885969116213,9.416636513099672C2.824755596911621,9.19919651309967,2.8872782969116213,8.95416651309967,3.062765396911621,8.80222651309967L5.482443396911622,5.77726651309967C5.693513396911621,5.594116513099671,6.006303396911621,5.596226513099671,6.214863396911621,5.7822365130996705L9.20314339691162,8.44616651309967L12.107723396911622,4.169996513099671C12.316823396911621,3.94524741309967,12.669803396911622,3.9325141130996704,12.894533396911621,4.14161551309967C13.119333396911621,4.350716513099671,13.132933396911621,4.70251951309967,12.923833396911622,4.92726851309967L9.646783396911621,9.60116651309967C9.441173396911621,9.82275651309967,9.093613396911621,9.83880651309967,8.86832339691162,9.637266513099672L5.838183396911621,6.93724651309967L3.790544396911621,9.64277651309967C3.6896703969116214,9.73011651309967,3.560637396911621,9.77805651309967,3.4272093969116213,9.77777651309967Z"
                        fill="#14C9BB"
                        fillOpacity="1"
                    />
                    <path
                        d="M3.426690396911621,10.02777651309967Q3.727081396911621,10.02839651309967,3.9541833969116214,9.83178651309967L3.9740333969116213,9.81458651309967L5.874083396911621,7.3040765130996705L8.701633396911621,9.82358651309967Q8.94678339691162,10.04289651309967,9.276503396911622,10.02762651309967Q9.60630339691162,10.012356513099672,9.83004339691162,9.77120651309967L9.84167339691162,9.75867651309967L13.11943339691162,5.08381651309967Q13.333933396911622,4.8437585130996705,13.321733396911622,4.519141513099671Q13.309133396911621,4.185864513099671,13.064833396911622,3.9585820130996705Q12.820463396911622,3.7312195130996706,12.486253396911621,3.7432745130996703Q12.15204339691162,3.7553295130996704,11.924683396911622,3.9997069830996703L11.91166339691162,4.01370711309967L9.15660339691162,8.06975651309967L6.381263396911621,5.595666513099671Q6.154463396911622,5.39337651309967,5.8512833969116205,5.391326513099671Q5.548133396911621,5.38927651309967,5.318593396911622,5.5884465130996706L5.301423396911622,5.6033465130996705L2.8809717969116213,8.62926651309967Q2.480144396911621,8.99110651309967,2.671796396911621,9.50412651309967Q2.8677173869116213,10.02857651309967,3.426690396911621,10.02777651309967ZM3.609264396911621,9.468036513099669L5.802293396911621,6.5704065130996705L9.035003396911621,9.45093651309967Q9.253233396911622,9.64616651309967,9.45277339691162,9.44240651309967L12.72905339691162,4.769613513099671L12.740813396911621,4.75697751309967Q12.82684339691162,4.664506513099671,12.822073396911621,4.53800551309967Q12.817283396911622,4.4111955130996705,12.72426339691162,4.32464851309967Q12.631513396911622,4.23836051309967,12.504283396911621,4.242949513099671Q12.38544339691162,4.24723651309967,12.30233339691162,4.3284195130996705L9.249683396911621,8.82256651309967L6.048453396911621,5.968816513099671Q5.856383396911621,5.797506513099671,5.662063396911622,5.9529365130996705L3.243701396911621,8.976246513099671L3.226407396911621,8.99122651309967Q3.065865396911621,9.130226513099672,3.140180396911621,9.32915651309967Q3.2144953969116212,9.52808651309967,3.427728396911621,9.52777651309967Q3.529675396911621,9.52798651309967,3.609264396911621,9.468036513099669Z"
                        fillRule="evenodd"
                        fill="#14C9BB"
                        fillOpacity="1"
                    />
                </g>
            </g>
        </g>
    </svg>
);
export const criticalIcon = () => (
    <svg xmlns="http://www.w3.org/2000/svg" fill="none" version="1.1" width="40" height="40" viewBox="0 0 40 40">
        <g>
            <g>
                <rect x="0" y="0" width="40" height="40" rx="8" fill="#F53F3F" fillOpacity="0.10000000149011612" />
            </g>
            <g>
                <g />
                <g>
                    <path
                        d="M16.66753,30C15.40647,27.3519,16.07019,25.8293,17.06576,24.439Q18.1277,22.8502,18.39319,21.3275Q19.25601,22.3868,18.92416,24.108C20.38433,22.453,20.649810000000002,19.80488,20.450699999999998,18.81185C23.7693,21.1289,25.2294,26.2265,23.3047,29.9338C33.5259,24.108,25.8268,15.43554,24.4994,14.50871C24.964,15.50174,25.0303,17.15679,24.101100000000002,17.95122Q22.5746,12.12544,18.79141,11C19.25601,13.97909,17.1985,17.223,15.20736,19.67247C15.14099,18.48084,15.07462,17.686410000000002,14.4109,16.49477C14.27816,18.67944,12.618874,20.4007,12.154274,22.5854C11.556931,25.564500000000002,12.618874,27.6829,16.66753,30Z"
                        fill="#F53F3F"
                        fillOpacity="1"
                    />
                </g>
            </g>
            <g>
                <g />
            </g>
        </g>
    </svg>
);
export const majorIcon = () => (
    <svg xmlns="http://www.w3.org/2000/svg" fill="none" version="1.1" width="40" height="40" viewBox="0 0 40 40">
        <g>
            <g>
                <g>
                    <rect x="0" y="0" width="40" height="40" rx="8" fill="#FF7B43" fillOpacity="0.10000000149011612" />
                </g>
                <g>
                    <g />
                    <g>
                        <path
                            d="M14.742131,21.1211L19.62868,21.1211L19.08857,28.1894C19.030929999999998,28.943,19.98732,29.3016,20.42496,28.6889Q22.67716,25.5486,26.6052,20.06862C26.959600000000002,19.573349999999998,26.6095,18.87954,26.0053,18.87954L21.11876,18.8774L21.65887,11.809096C21.71651,11.0555133,20.76012,10.699002,20.32035,11.311689L14.142253,19.93199C13.787877,20.42726,14.135849,21.1211,14.742131,21.1211Z"
                            fill="#FF7B43"
                            fillOpacity="1"
                        />
                    </g>
                </g>
            </g>
            <g>
                <g>
                    <g />
                </g>
            </g>
        </g>
    </svg>
);
export const minorIcon = () => (
    <svg xmlns="http://www.w3.org/2000/svg" fill="none" version="1.1" width="40" height="40" viewBox="0 0 40 40">
        <g>
            <g>
                <g>
                    <rect x="0" y="0" width="40" height="40" rx="8" fill="#FFBB00" fillOpacity="0.10000000149011612" />
                </g>
                <g>
                    <g>
                        <g>
                            <g />
                            <g>
                                <path
                                    d="M28.3326,27.61524837341309L25.3326,27.61524837341309L25.3326,20.693478373413086C25.3326,17.647828373413084,22.9326,15.154348373413086,20,15.154348373413086C17.06739,15.154348373413086,14.667390000000001,17.645648373413085,14.667390000000001,20.693478373413086L14.667390000000001,27.61524837341309L11.665217,27.61524837341309C11.3,27.61524837341309,11,27.928248373413084,11,28.30874837341309C11,28.689148373413087,11.3,29.000048373413087,11.665217,29.000048373413087L28.3326,29.000048373413087C28.7,29.000048373413087,28.9978,28.689148373413087,28.9978,28.30874837341309C29,27.928248373413084,28.7,27.61524837341309,28.3326,27.61524837341309Z"
                                    fill="#FFBB00"
                                    fillOpacity="1"
                                />
                            </g>
                        </g>
                    </g>
                </g>
                <g>
                    <g />
                    <g>
                        <path
                            d="M25.0674,15.189129999999999L26,14.219570000000001C26.2652,13.943480000000001,26.2652,13.52826,26,13.25C25.732599999999998,12.97391,25.3326,12.97391,25.0674,13.25L24.1348,14.219570000000001C23.8674,14.49565,23.8674,14.91087,24.1348,15.189129999999999C24.4,15.46522,24.8326,15.46522,25.0674,15.189129999999999ZM20,13.76957C20.36739,13.76957,20.667389999999997,13.4587,20.667389999999997,13.07826L20.667389999999997,11.693478C20.667389999999997,11.313044,20.36739,11,20,11C19.63261,11,19.332610000000003,11.31087,19.332610000000003,11.693478L19.332610000000003,13.07826C19.332610000000003,13.4587,19.63261,13.76957,20,13.76957ZM27,19.65435L28.3348,19.65435C28.7022,19.65435,29,19.34348,29,18.96087C29,18.58044,28.7,18.26957,28.3348,18.26957L27,18.26957C26.6326,18.26957,26.3348,18.58044,26.3348,18.96087C26.3326,19.34348,26.6326,19.65435,27,19.65435ZM11.665217,19.65435L13,19.65435C13.36522,19.65435,13.667390000000001,19.34348,13.667390000000001,18.96087C13.667390000000001,18.58044,13.36739,18.26957,13,18.26957L11.665217,18.26957C11.3,18.26957,11,18.58044,11,18.96087C11,19.34348,11.3,19.65435,11.665217,19.65435ZM15.06522,15.189129999999999C15.332609999999999,15.46522,15.732610000000001,15.46522,15.99783,15.189129999999999C16.26522,14.91304,16.26522,14.49783,15.99783,14.219570000000001L15.06522,13.25C14.79783,12.97391,14.397829999999999,12.97391,14.13044,13.25C13.86304,13.52609,13.86304,13.9413,14.13044,14.219570000000001L15.06522,15.189129999999999Z"
                            fill="#FFBB00"
                            fillOpacity="1"
                        />
                    </g>
                </g>
            </g>
            <g>
                <g>
                    <g />
                </g>
            </g>
        </g>
    </svg>
);
export const warningIcon = () => (
    <svg xmlns="http://www.w3.org/2000/svg" fill="none" version="1.1" width="40" height="40" viewBox="0 0 40 40">
        <g>
            <g>
                <g>
                    <rect x="0" y="0" width="40" height="40" rx="8" fill="#00B7FF" fillOpacity="0.10000000149011612" />
                </g>
                <g>
                    <g />
                    <g>
                        <path
                            d="M27.6767,24.7667L25.996000000000002,21.409599999999998C25.9786,21.372799999999998,25.9678,21.3337,25.9678,21.2925L25.9635,17.57542C25.9613,15.47831,24.5408,13.71301,22.61287,13.18602L22.61287,13.18386C22.61287,11.978072000000001,21.634790000000002,11,20.429009999999998,11C19.2254,11,18.24949,11.973735,18.24515,13.17735C16.29552,13.69566,14.85768,15.473980000000001,14.85985,17.58627L14.86419,21.3034C14.86419,21.3446,14.85552,21.3836,14.836,21.4205L13.157444,24.7733C12.667323,25.753500000000003,13.380817,26.909399999999998,14.47817,26.9072L26.360300000000002,26.898600000000002C27.4555,26.9007,28.169,25.747,27.6767,24.7667Z"
                            fill="#00B7FF"
                            fillOpacity="1"
                        />
                    </g>
                    <g>
                        <path
                            d="M18.449010030700684,27.5361466408C18.229973730700685,27.5361466408,18.110696730700685,27.7898808125,18.247323030700684,27.9612068125C18.754793030700682,28.5944628125,19.535518030700683,29.0000028125,20.409488030700683,29.0000028125C21.28563803070068,29.0000028125,22.066358030700684,28.5944628125,22.575998030700685,27.9590378125C22.712628030700685,27.7877148125,22.59334803070068,27.5339779825,22.374308030700682,27.5361466408L18.449010030700684,27.5361466408Z"
                            fill="#00B7FF"
                            fillOpacity="1"
                        />
                    </g>
                </g>
                <g>
                    <g>
                        <g>
                            <g />
                        </g>
                    </g>
                </g>
            </g>
            <g>
                <g>
                    <g />
                </g>
            </g>
        </g>
    </svg>
);
