# -*- coding: utf-8 -*-
import time

from sqlalchemy import (
    Column,
    Integer,
    String,
    Text,
    ForeignKey,
    Boolean,
    DateTime,
    Enum,
    JSON,
    Table
)
from sqlalchemy.orm import relationship, exc

from server.db.db_common import DBCommon
from server.db.models.base import Base


class DCFabricUnit(Base):
    __tablename__ = "dc_fabric_unit"
    id = Column(Integer, primary_key=True, autoincrement=True)
    name = Column(String(128), nullable=False)
    description = Column(String(128), nullable=True)
    unit_info = Column(JSON)


class DCFabricTemplate(Base):
    __tablename__ = "dc_fabric_template"
    id = Column(Integer, primary_key=True, autoincrement=True)
    name = Column(String(128), nullable=False)
    description = Column(String(128), nullable=True)
    type = Column(String(32), nullable=False)
    underlay_routing_protocol = Column(String(32), nullable=False)
    overlay_control_protocol = Column(String(32), nullable=False)
    template_info = Column(JSON)
    
    
class DCFabricTopology(Base):
    __tablename__ = "dc_fabric_topology"
    id = Column(Integer, primary_key=True, autoincrement=True)
    fabric_id = Column(Integer, ForeignKey('fabric.id', ondelete='CASCADE', onupdate='CASCADE'))
    template_name = Column(String(128), nullable=False)
    fabric_config = Column(JSON)
    

class DCFabricTopologyNode(Base):
    __tablename__ = "dc_fabric_topology_node"
    id = Column(Integer, primary_key=True, autoincrement=True)
    logic_name = Column(String(128), nullable=False)
    switch_sn = Column(String(128), nullable=True)
    fabric_topo_id = Column(Integer, ForeignKey('dc_fabric_topology.id', ondelete='CASCADE', onupdate='CASCADE'))
    type = Column(String(32), nullable=False)
    node_info = Column(JSON)
    
class DCFabricDB(DBCommon):

    def update_fabric_unit(self, unit_id, unit_name, unit_info, description=None):
        session = self.get_session()
        with session.begin(subtransactions=True):
            existing_unit = None
            if unit_id:
                existing_unit = session.query(DCFabricUnit).filter(DCFabricUnit.id == unit_id).first()
                unit = session.query(DCFabricUnit).filter(DCFabricUnit.name == unit_name).first()
                if unit and unit.id != unit_id:
                    raise Exception(f"unit_name: {unit_name} is already exists")
            else:
                existing_unit = session.query(DCFabricUnit).filter(DCFabricUnit.name == unit_name).first()
            if existing_unit:
                existing_unit.name = unit_name
                existing_unit.unit_info = unit_info
                existing_unit.description = description
            else:
                unit = DCFabricUnit(name=unit_name, description=description, unit_info=unit_info)
                session.add(unit)
                
    def get_fabric_unit_by_id(self, unit_id):
        session = self.get_session()
        unit = session.query(DCFabricUnit).filter(DCFabricUnit.id == unit_id).first()
        return unit
    
    def del_fabric_unit_by_id(self, unit_id):
        session = self.get_session()
        session.query(DCFabricUnit).filter(DCFabricUnit.id == unit_id).delete()
        
    def check_fabric_unit_name(self, name):
        session = self.get_session()
        existing_unit = session.query(DCFabricUnit).filter(DCFabricUnit.name == name).first()
        if existing_unit:
            return True
        else:
            return False
        
    def update_fabric_template(self, template_id, template_name, type, underlay_routing_protocol, overlay_control_protocol, template_info, description=None):
        session = self.get_session()
        with session.begin(subtransactions=True):
            existing_template = None
            if template_id: 
                existing_template = session.query(DCFabricTemplate).filter(DCFabricTemplate.id == template_id).first()
                template = session.query(DCFabricTemplate).filter(DCFabricTemplate.name == template_name).first()
                if template and template.id != template_id:
                    raise Exception(f"template_name: {template_name} is already exists")
            else:
                existing_template = session.query(DCFabricTemplate).filter(DCFabricTemplate.name == template_name).first()
            if existing_template:
                existing_template.name = template_name
                existing_template.type = type
                existing_template.underlay_routing_protocol = underlay_routing_protocol
                existing_template.overlay_control_protocol = overlay_control_protocol
                existing_template.template_info = template_info
                existing_template.description = description
            else:
                unit = DCFabricTemplate(name=template_name, description=description, type=type, template_info=template_info,
                                        underlay_routing_protocol=underlay_routing_protocol, overlay_control_protocol=overlay_control_protocol)
                session.add(unit)
                
    def get_fabric_template_by_id(self, template_id):
        session = self.get_session()
        template = session.query(DCFabricTemplate).filter(DCFabricTemplate.id == template_id).first()
        return template
    
    def del_fabric_template_by_id(self, template_id):
        session = self.get_session()
        session.query(DCFabricTemplate).filter(DCFabricTemplate.id == template_id).delete()
        
    def check_fabric_template_name(self, template_name):
        session = self.get_session()
        existing_template = session.query(DCFabricTemplate).filter(DCFabricTemplate.name == template_name).first()
        if existing_template:
            return True
        else:
            return False
        
    def update_fabric_topology(self, fabric_id, template_name, fabric_config):
        session = self.get_session()
        with session.begin(subtransactions=True):
            topo = session.query(DCFabricTopology).filter(DCFabricTopology.fabric_id == fabric_id).first()
            if topo:
                topo.template_name = template_name
                topo.fabric_config = fabric_config
            else:
                topo = DCFabricTopology(fabric_id=fabric_id, template_name=template_name, fabric_config=fabric_config)
                session.add(topo)
        return topo
                
    def get_fabric_topo_by_fabric_id(self, fabric_id):
        session = self.get_session()
        topology = session.query(DCFabricTopology).filter(DCFabricTopology.fabric_id == fabric_id).first()
        return topology
    
    def update_fabric_topology_node_info(self, logic_name, switch_sn, fabric_topo_id, type, node_info):
        session = self.get_session()
        with session.begin(subtransactions=True):
            node = session.query(DCFabricTopologyNode).filter(DCFabricTopologyNode.fabric_topo_id == fabric_topo_id, 
                                                              DCFabricTopologyNode.logic_name == logic_name).first()
            if node:
                node.switch_sn = switch_sn
                node.type = type
                node.node_info = node_info
            else:
                node = DCFabricTopologyNode(logic_name=logic_name, switch_sn=switch_sn, fabric_topo_id=fabric_topo_id, type=type, node_info=node_info)
                session.add(node)


dc_fabric_db = DCFabricDB()

