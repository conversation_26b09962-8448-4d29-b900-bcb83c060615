# revision identifiers, used by Alembic.
revision = 'v4'
down_revision = 'v3'
branch_labels = None
depends_on = None

from datetime import datetime
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

def upgrade():
    op.execute(
        '''update automation.hardware_mapping set hardware_model='7312-54X' where switch_model = 'as7312_54x';''')

def downgrade():
    op.execute(
        '''update automation.hardware_mapping set hardware_model='7312-54X' where switch_model = 'as7312_54x';''')
