#!/usr/bin/python3

#
#   Copyright (c) 2016, 2017, 2018 Pica8
#
#   Name: pica_lic.py
#
#   Python REST Client to access Picos License Web Service
#

import urllib3
import requests
import json
import logging
import datetime

# import sys,os
# sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
from server import cfg
from server.db.models.inventory import inven_db
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

LOG = logging.getLogger(__name__)

# LIC_URL = 'https://stage.license.pica8.com'

LIC_TYPE = {"1G": "1", "10G": "2", "100G": "3", "S10G": "2",  "S25G": "4", "S40G": "5",
            "1GE": "1", "10GE": "2", "100GE": "3", "400GE": "6", "25GE": "4", "40GE": "5", "200GE": "7",
            "Base": "1", "L2": "1", "L3": "2", "OF": "3", "EE": "4", "NPB": "5", "CC": "6", "All": "4"}
LIC_SPEED = {"1G": "1GE", "10G": "10/25/40GE", "100G": "100GE", "1GE": "1GE", "10GE": "10/25/40GE", "100GE": "100GE"}
LIC_FEATURE = {"Base": "Base Product", "L2": "Base Product", "L3": "Layer3", "OF": "OpenFlow",
               "EE": "Base Product & Layer3 & OpenFlow", "NPB": "NPBOS", "CC": "Enterprise Edition + PicaPilot",
               "All": "Base Product & Layer3 & OpenFlow"}


class LicensePortalException(Exception):
    pass


#
# Pica8 user license REST API
#
class PicaLicense(object):
    
    def __init__(self, token=None):
        self._token = token
        self.expire_time = None
        self.avaliable_lics = None
        self.username = None
        self.password = None
        self.license_portal_url = None
    
    def fresh(self, url, user_name, password):
        self.auth(url, user_name, password)
    
    def auth(self, url=None, user_name=None, password=None):
        # url = '%s/auth' % LIC_URL
        headers = {'content-type': 'application/json'}
        self.username = user_name if user_name else self.username
        self.password = password if password else self.password
        self.license_portal_url = url if url else self.license_portal_url
        url = '%s/auth' % self.license_portal_url
        params = {'username': self.username, 'password': self.password, 'description': 'automation tool token'}
        now = datetime.datetime.now()
        try:
            res = requests.post(url, data=json.dumps(params), headers=headers, proxies=self.proxies, verify=self.verify,
                                timeout=30)
        except Exception:
            raise Exception("Call license portal api refresh token failed")
        if res.status_code == 200:
            if 'error' not in res.text:
                self._token = res.text
                self.expire_time = now + datetime.timedelta(hours=23)
                return
            else:
                raise Exception(res.text)
        
        LOG.error('error get license protal token msg:[%s]', res.json()['error'])
        raise Exception(res.json()['error'])
    
    @staticmethod
    def load_user_from_db():
        from server.db.models.inventory import inven_db
        sys_config = inven_db.get_global_system_config()
        if sys_config:
            if sys_config.license_portal_user and sys_config.license_portal_password:
                return sys_config.license_portal_user, sys_config.license_portal_password, sys_config.license_portal_url
        
        raise Exception('no license portal user and pass configured')
    
    @property
    def token(self):
        if self._token and self.expire_time and datetime.datetime.now() < self.expire_time:
            return self._token
        self.auth()
        if not self._token:
            raise Exception("License Portal token is None")
        return self._token
    
    @property
    def proxies(self):
        return cfg.CONF.license_portal_proxy
    
    @property
    def verify(self):
        return cfg.CONF.license_portal_cert_verify
    
    def license_details(self, hardware_id, loop_idx):
        """
            @loop_idx: 0=> call PLL license  1=> call APL license.
        """
        license_portal_url = self.license_portal_url
        # url = '%s/get_license_details' % (LIC_URL)
        headers = {'content-type': 'application/json', 'Authorization': 'Bearer ' + self.token}
        lic_hwid = {'hardware_id': hardware_id}
        
        if loop_idx == 0:
            url = '%s/get_pool_license_details' % license_portal_url
        else:
            url = '%s/get_license_details' % license_portal_url
        
        # data either read from a file or passed as a dict
        res = requests.get(url, data=json.dumps(lic_hwid), headers=headers, proxies=self.proxies, verify=self.verify,
                           timeout=10)
        
        # returns license count of given type
        self.handle_response(res, 'get license %s details error by license portal server internal error!' % hardware_id, loop_idx)
        return res.json()
    
    def resolve_subscription_support(self, speed):
        if not speed.isdigit():
            speed = LIC_TYPE[speed]
        inventory_list = list()
        inventory_infos = self.get_inventory_all()[1]
        for inventory_info in inventory_infos['response']:
            if inventory_info["expiry_dates"] and speed == LIC_TYPE[inventory_info["speed"]] and inventory_info["remaining"] > 0:
                inventory_list.append(inventory_info["type"])
        if "Subscription & Premium support" in inventory_list:
            feature = "11"
        elif "Subscription & Standard support" in inventory_list:
            feature = "10"
        elif "Subscription & Basic support" in inventory_list:
            feature = "9"
        elif "Enterprise Edition" in inventory_list:
            feature = "4"
        else:
            feature = None
        return feature
    
    def license_create(self, speed='1G', feature=LIC_TYPE['EE'], hwid='', name='', expiry_date_flag='true',
                       purge_flag='false', sn=None):
        license_portal_url = self.license_portal_url
        pll_tag = True if speed in ("S25G", "S40G") else False
        speed = LIC_TYPE[speed]
        # url = '%s/get_license_key' % LIC_URL
        headers = {'content-type': 'application/json', 'Authorization': 'Bearer ' + self.token}
        
        for idx, url in enumerate(['%s/get_pool_license_key' % license_portal_url, '%s/get_license_key' % license_portal_url]):
            # idx: 0=> call PLL license  1=> call APL license.
            if idx == 0 and pll_tag:
                continue
            if idx == 1:
                _, _, status = self.license_exists(hwid)
                if status:
                    LOG.info("%s already exists in PLL" % hwid)
                else:
                    LOG.info("%s already not exists in PLL" % hwid)
                    feature = self.resolve_subscription_support(speed)
                    if not feature:
                        raise LicensePortalException("no license assign in the %s speed" % speed)
                
            lic = {
                "type": speed,
                "feature": feature,
                "hardware_id": hwid,
                "name": name,
                "expiry_date_flag": expiry_date_flag,
                "purge_flag": purge_flag
            }
            
            # data either read from a file or passed as a dict
            # res = requests.post(url, data=open("lic2.json"), headers=headers)
            res = requests.post(url, data=json.dumps(lic), headers=headers, proxies=self.proxies, verify=self.verify,
                                timeout=10)
            
            if self.handle_response(res, 'create %s license error by license portal server internal error!' % hwid, idx):
                break
        return idx, res.text
    
    def license_get(self, hardware_id):
        hardware_id = hardware_id.strip()
        license_portal_url = self.license_portal_url
        # url = '%s/check_license' % (LIC_URL)
        headers = {'content-type': 'application/json', 'Authorization': 'Bearer ' + self.token}
        lic_hwid = {'hardware_id': hardware_id}
        
        for idx, url in enumerate(['%s/check_pool_license' % license_portal_url, '%s/check_license' % license_portal_url]):
            # data either read from a file or passed as a dict
            res = requests.get(url, data=json.dumps(lic_hwid), headers=headers, proxies=self.proxies, verify=self.verify,
                               timeout=10)
            if self.handle_response(res, 'get %s license error by license portal server internal error!' % hardware_id, idx):
                break
        return idx, res.text
    
    def license_exists(self, hardware_id):
        hardware_id = hardware_id.strip()
        license_portal_url = self.license_portal_url
        # url = '%s/license_exists' % (LIC_URL)
        headers = {'content-type': 'application/json', 'Authorization': 'Bearer ' + self.token}
        lic_hwid = {'hardware_id': hardware_id}
        
        for idx, url in enumerate(['%s/pool_license_exists' % license_portal_url, '%s/license_exists' % license_portal_url]):
        
            # data either read from a file or passed as a dict
            res = requests.get(url, data=json.dumps(lic_hwid), headers=headers, proxies=self.proxies, verify=self.verify,
                               timeout=10)
            if self.handle_response(res, 'check license exist error by license portal server internal error!', idx) and res.text == 'True':
                break
        return idx, res.text == 'True', res.text in ('True', 'Expires')
    
    def get_inventory_all(self):
        user_name, license_portal_url = self.username, self.license_portal_url
        # url = '%s/get_inventory/%s' % (LIC_URL, self.user_name)
        headers = {'content-type': 'application/json', 'Authorization': 'Bearer ' + self.token}
        
        ret_list = []
        for url in ['%s/get_pool_license_inventory' % license_portal_url, '%s/get_inventory/%s' % (license_portal_url, user_name)]:
            # data either read from a file or passed as a dict
            res = requests.get(url, headers=headers, proxies=self.proxies, verify=self.verify, timeout=10)
            # returns list of available entries
            if res.status_code == 200:
                ret = res.json()
            else:
                ret = {'error': res.status_code}
            ret_list.append(ret)
        return ret_list
    
    def license_decom(self, hardware_id):
        hardware_id = hardware_id.strip()
        license_portal_url = self.license_portal_url
        headers = {'content-type': 'application/json', 'Authorization': 'Bearer ' + self.token}
        lic_hwid = {'hardware_id': hardware_id}
        url = '%s/set_pool_license_decom' % license_portal_url
        
        res = requests.get(url, data=json.dumps(lic_hwid), headers=headers, proxies=self.proxies, verify=self.verify,
                           timeout=10)
        return res.text == 'success'
    
    @staticmethod
    def handle_response(response, error_message, loop_index=1):
        if loop_index == 1:
            if response.status_code != 200:
                raise LicensePortalException(error_message)
            if 'error' in response.text:
                raise LicensePortalException(response.json()['error'])
        else:
            if response.status_code != 200:
                LOG.error("call APL failed, retry call PLL")
            elif 'error' in response.text:
                LOG.error("call APL failed, retry call PLL, the error %s" % response.text)
            else:
                return True


class License:
    def __init__(self, speed='1GE', features='EE', hwid='', expr_date='', key='', name='', status=False):
        self.speed = speed
        self.features = features
        self.hwid = hwid
        self.expr_date = expr_date
        self.key = key
        self.name = name
        self.status = status
    
    def show(self):
        print("License Info\n  Type: %s, Features: %s, Hardware ID: %s, Expiration Date: %s, Name: %s, Status: %s" \
              % (self.speed, self.features, self.hwid, self.expr_date, self.name, self.status))
        if self.key != '':
            print("  License Key\n%s\n" % self.key)


def portal_licenseinfo(lic, hwid, speed='1GE', features='EE'):
    ret_lic = lic.inventory(speed, features)
    if ret_lic:
        available_lics = ret_lic[0]['remaining']
        idx, status, _ = lic.license_exists(hwid)
        portal_lic = License(speed, features, hwid, status=status)
        if portal_lic.status:
            res = json.loads(lic.license_details(hwid, idx))
            portal_lic.expr_date = res['expiry_date']
            portal_lic.name = res['licence_name']
            _, portal_lic.key = lic.license_get(hwid)
        return available_lics, portal_lic


def pica8_license(sn=None, system_config_name=None):
    if sn:
        system_config = inven_db.get_system_config_by_sn(sn)
    elif system_config_name:
        system_config = inven_db.get_system_config_by_config_name(system_config_name)
    else:
        return PicaLicense()
    if not system_config:
        system_config = inven_db.get_global_system_config()
    pica8_license_instance = PicaLicense()
    pica8_license_instance.fresh(system_config.license_portal_url, system_config.license_portal_user,
                                 system_config.license_portal_password)
    return pica8_license_instance


if __name__ == "__main__":
    pass
    # print(pica8_license.resolve_subscription_support("1"))
    # print(pica8_license.license_create(hwid="AAAA-BAED-E62C-4AAB", name="charlie_11"))
    # print(pica8_license.license_create(hwid="4F09-F176-2300-7145", name="charlie_11"))
    # print(pica8_license.license_get("AAAA-BAED-E62C-4AAB"))
    # print(pica8_license.license_details("AAAA-BAED-E62C-4AAB", 1))
    # print(pica8_license.license_details("AAAA-BAED-E62C-4AAB", 0))
    # print(pica8_license.license_exists("AAAA-BAED-E62C-4AAB"))
    # print(pica8_license.get_inventory_all())
    # print(pica8_license.license_decom("AAAA-BAED-E62C-4AAB"))
