#!/usr/bin/python3
# -*- coding: utf-8 -*-
"""
@author: <PERSON><PERSON>
@project: Pica8AmpCon
@file: sys_conf_crud.py
@function:
@time: 2022/1/4 17:02
"""

import re

import flask
from werkzeug.security import generate_password_hash

from util import utils
from .__init__ import general_model, traceback, request, jsonify, inventory, inven_db, json, User, user_db


def sysconf_to_dict(model):
    """
        switch_op_user = Column(String(32))
        switch_op_password = Column(String(32))
        license_portal_url = Column(String(255))
        license_portal_user = Column(String(32))
        license_portal_password = Column(String(32))
        license_portal_token = Column(String(255))
        security_config = Column(String(255))
        parking_security_config = Column(String(255))
        templates_set = Column(String(255), default='Network Access Device') # not used
        customized_fields = Column(Text(2056)) # not used
        retrieve_config_num = Column(Integer, default=100)
    """
    return dict(
        switch_op_user=model.switch_op_user,
        switch_op_password=model.switch_op_password,
        license_portal_url=model.license_portal_url,
        license_portal_user=model.license_portal_user,
        license_portal_password=model.license_portal_password,
        # license_portal_token=model.license_portal_token,
        security_config=model.security_config,
        # parking_security_config=model.parking_security_config,
        # templates_set=model.templates_set,
        # customized_fields=model.customized_fields,
        retrieve_config_num=model.retrieve_config_num,
    )


@general_model.route('/settings/system_config', methods=['POST', 'GET'])
def sys_conf():
    """
    :return: POST ==> code 200 success, 500 error
             GET  ==>{
                      "customized_fields": null,
                      "license_portal_password": "ans",
                      "license_portal_token": null,
                      "license_portal_url": "https://license.pica8.com",
                      "license_portal_user": "ans",
                      "parking_security_config": null,
                      "retrieve_config_num": 100,
                      "security_config": "config_gen/security/security.config",
                      "switch_op_password": "12345678",
                      "switch_op_user": "admin",
                      "templates_set": "Network Access Device"
                    }
    """
    if request.method == 'GET':
        db_system_config = inven_db.get_model(inventory.SystemConfig)
        if not db_system_config:
            return jsonify([])
        return jsonify(sysconf_to_dict(db_system_config))
    # # No update API in v1
    # else:
    #     status, msg = 500, "success"
    #     try:
    #         params = json.loads(request.get_data(as_text=True))
    #         # strong check params
    #         op_user = params.get("switch_op_user", "")
    #         op_pwd = params.get("switch_op_password", "")
    #         if not any([op_user, op_pwd]):
    #             raise ValueError("switch_op_user/switch_op_password is required")
    #         sys_obj = inventory.SystemConfig(
    #             switch_op_user=op_user,
    #             switch_op_password=op_pwd,
    #             license_portal_url=params.get("license_portal_url", ""),
    #             license_portal_user=params.get("license_portal_user", ""),
    #             license_portal_password=params.get("license_portal_password", ""),
    #             license_portal_token=params.get("license_portal_token", ""),
    #             security_config=params.get("security_config", ""),
    #             parking_security_config=params.get("parking_security_config", ""),
    #             templates_set=params.get("templates_set", ""),
    #             customized_fields=params.get("customized_fields", ""),
    #             retrieve_config_num=params.get("retrieve_config_num", ""),
    #         )
    #         # TODO need to call API
    #         inven_db.insert(sys_obj)
    #     except ValueError as v:
    #         status, msg = 500, "ERROR:[%s]" % v
    #     except Exception as e:
    #         status, msg = 500, "ERROR:[%s, %s]" % (e, traceback.format_exc())
    #     finally:
    #         return jsonify({"status_code": status, "msg": msg})
    

@general_model.route('/settings/update_user', methods=['POST'])
def update_user():
    """
        username: required
        password: required
        type: optional
        email: optional
    """
    msg = {}
    try:
        data = request.get_json()
        username = data.get("username", "")
        password = data.get("password", "")
        user_type = data.get("user_type", "")
        group_name_list = data.get("group_name", "").split(',')
        if not username or not password:
            raise ValueError("Username/Password is required")
        user_role = data.get("type", "")
        user_role_map = {'readonly': 'readonly', 'operator': 'admin', 'admin': 'superadmin', 'superadmin': 'superuser'}
        if user_role and user_role.lower() not in user_role_map.keys():
            raise ValueError("User role not valid, check it ('ReadOnly', 'Operator', 'Admin', 'SuperAdmin')")
        user_role = user_role_map[user_role.lower()]

        if user_type and user_type.lower() not in ('group', 'global'):
            raise ValueError("User type not valid, check it ('group', 'global')")

        email = data.get("email", "")
        regex = re.compile(r'([A-Za-z0-9]+[.-_])*[A-Za-z0-9]+@[A-Za-z0-9-]+(\.[A-Z|a-z]{2,})+')
        if email and not re.fullmatch(regex, email):
            raise ValueError("The email not valid")
        
        db_session = inven_db.get_session()
        user_query = db_session.query(User).filter(User.name == username).first()
        with db_session.begin():
            if not user_query:
                if user_type == 'group' and group_name_list:
                    group_id_list = utils.get_group_id_list_by_name(group_name_list)
                else:
                    group_id_list = []
                session_query = user_db.get_session()
                user_query = session_query.query(User).filter(User.name == username).first()
                if not user_query:
                    obj1 = User(name=username, password=password,
                                type=user_role, email=email, user_type=user_type)
                    user_db.insert(obj1)
                    user_db.update_user_group_mapping(user_db.query_user(username).id, group_id_list)
                    msg = {'info': 'User Added Successfully', 'status': '200'}
                msg = {"status": 200, "msg": "add user success"}
            else:
                if user_type == 'group' and group_name_list:
                    group_id_list = utils.get_group_id_list_by_name(group_name_list)
                else:
                    group_id_list = []
                updates = {'passwd': generate_password_hash(password), 'name': username, 'email': email,
                           'type': user_role, 'user_type': user_type, 'group_id_list': group_id_list}
                user_db.update_user({'name': [username]}, updates)
                msg = {"status": 200, "msg": "update user success"}
    except ValueError as v:
        msg = {"status": 400, "msg": str(v)}
    except Exception as e:
        msg = {"status": 500, "msg": str(e)}
    finally:
        return jsonify(msg)
