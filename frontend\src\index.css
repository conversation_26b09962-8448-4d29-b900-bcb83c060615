@font-face {
    font-family: Lato;
    src: url('./assets/fonts/Lato-Regular.ttf');
}

/*È«¾Ö±äÁ¿*/
:root {
    --primary-color: #14C9BB;
    --primary-color-hover: #34DCCF;
}

.tree-modal-action {
    padding-bottom: 8px !important;
    border-bottom: 1px solid #E7E7E7;
}


body {
    font-family: Lato, serif;
}

.ant-menu-inline {
    background: var(--primary-color) !important;
}

.l7-control-container .l7-right {
    z-index: 3 !important;
}

.ant-pagination {
    font-size: 12px !important;
}

.ant-tree .ant-tree-treenode {
    padding: 0;
}


.ant-badge .ant-badge-count-sm {
    min-width: 12px;
    height: 12px;
    font-size: 10px;
    line-height: 12px;
}


/* ¶¨Î»ÍøÔªÊ±Êó±êÖ¸ÕëÑùÊ½ */
.locationing {
    cursor: crosshair !important;
}

.fix-map-tabs-zoom {
    display: block !important;
    position: absolute !important;
    top: -9999px;
    left: -9999px;
}

.ant-input-disabled {
    cursor: text !important;
}

.ant-form-item .ant-form-item-label > label::after {
    content: "" !important;
}

.ant-form-item .ant-form-item-label > label.ant-form-item-required:not(.ant-form-item-required-mark-optional)::before {
    content: "" !important;
    display: none !important;
}

.ant-form-item .ant-form-item-label > label.ant-form-item-required:not(.ant-form-item-required-mark-optional)::after {
    display: inline-block;
    margin-inline-start: 4px;
    color: #ff4d4f;
    font-size: 14px;
    font-family: SimSun, sans-serif;
    line-height: 1;
    content: "*" !important;
}

.label-wrap .ant-form-item-label {
    word-wrap: break-word;
    word-break: break-all;
}

@keyframes ant-line {
    to {
        stroke-dashoffset: -1000
    }
}

.questioncircle-color {
    color: rgba(0, 0, 0, 0.45) !important;
}

.questioncircle-color:hover {
    cursor: help;
}
/* .ant-select-dropdown {
    padding: 0 !important;
} */
.ant-select-item-option {
    font-weight: 400 !important;
    font-size: 14px !important;
}

.ant-select-item-option:not(.ant-select-item-option-disabled) {
    color: #212519 !important;
}

.ant-select-item-option-selected {
    color: var(--primary-color) !important;
}

.ant-menu-item-selected {
    color: var(--primary-color) !important;
    background-color: #FFFFFF !important;
    .menu-dot::before {
        color: #14C9BB;
    }
}

.ant-menu-item-active:not(.ant-menu-item-selected) {
    color: #FFFFFF !important;
    background-color: #5AD9CF !important;
}

.ant-menu-submenu-active:not(:has(.ant-menu-submenu-title)) {
    background-color: #5AD9CF !important;
}

.ant-menu-submenu-inline.ant-menu-submenu-active > .ant-menu-submenu-title {
    background-color: #5AD9CF !important;
}

.ant-menu > .ant-menu-submenu-vertical.ant-menu-submenu-active > .ant-menu-submenu-title,
.ant-menu-vertical .ant-menu-item.ant-menu-submenu-active > .ant-menu-title-content {
    background-color: #5AD9CF;
}

/* ant-tabs start */
.ant-tabs,
.ant-tabs-content-holder,
.ant-tabs-content,
.ant-tabs-tabpane {
    display: flex;
    flex: 1;
    /* overflow: hidden; */
}

.ant-tabs {
    background: #fff;
}

.ant-tabs-tabpane {
    padding: 16px 24px 0;
    flex-direction: column;
}

.ant-modal-content .ant-tabs-tabpane.ant-tabs-tabpane-active {
    padding-left: 0 !important;
    flex-direction: column;
}

.ant-tabs-nav {
    padding-left: 0 !important;
    background: #F0F2F5;
    .ant-tabs-tab {
        margin: 0 !important;
        padding: 15px 40px 14px 40px;
        font-weight: 600;
        font-size: 16px;
        line-height: 19px;
        text-align: left;
        font-style: normal;
        text-transform: none;
        background: #EAEDF1;
        border-radius: 4px 4px 0 0;
        border: 1px solid #C5D0D6;
        border-bottom: none;
        /* margin-left: 1px !important;
        margin-right: 1px !important; */
    }
    .ant-tabs-tab:first-child{
        margin-left: 0px !important;
    }

    .ant-tabs-tab-active {
        background: #FFFFFF;
        border: none;
        border-radius: 4px 4px 0 0;
    }

    .ant-tabs-ink-bar {
        top: 0;
        /*border-radius: 4px 4px 0 0;*/
    }
}

.radioGroupTabs .ant-tabs-nav-wrap:has(.ant-tabs-ink-bar) {
    background: #FFFFFF;
}

.radioGroupTabs .ant-tabs-nav .ant-tabs-ink-bar {
    display: none;
}

.radioGroupTabs .ant-tabs-nav .ant-tabs-tab {
    background-color: #FFF;
    border: 1px solid #DCDCDC;
    font-size: 14px;
    font-weight: 400;
    border-radius: 0px;
    margin-left: -1px !important;
    padding: 0 15px;
    height: 32px;
    -webkit-border-radius: 0px;
    -moz-border-radius: 0px;
    -ms-border-radius: 0px;
    -o-border-radius: 0px;
}
.radioGroupTabs .ant-tabs-nav .ant-tabs-tab:first-child{
    margin-left: 0px !important;
}
.radioGroupTabs .ant-tabs-nav-list > .ant-tabs-tab:first-child {
    border-top-left-radius: 2px;
    border-bottom-left-radius: 2px;
}

.radioGroupTabs .ant-tabs-nav-list > .ant-tabs-tab:nth-last-child(2)  {
    border-top-right-radius: 2px;
    border-bottom-right-radius: 2px;
}

.radioGroupTabs  .ant-tabs-nav .ant-tabs-tab-active {
    position: sticky;
    z-index: 2;
    color: var(--primary-color);
    border-color: var(--primary-color);
    background-color: rgba(20,201,187,0.1);
}

.radioGroupTabs .ant-tabs-nav::before {
    border: none;
}

.radioGroupTabs .ant-tabs-tabpane {
    padding: 0;
}

.sameLevelTabs .ant-tabs-nav .ant-tabs-ink-bar {
    bottom: 0;
    top: unset;
}

.sameLevelTabs .ant-tabs-nav .ant-tabs-tab {
    background: #FFFFFF;
    border: none;
}

.sameLevelTabs .ant-tabs-nav-wrap:has(.ant-tabs-ink-bar) {
    background: #FFFFFF;
}

#formPanel .ant-tabs-nav{
    background:#FFFFFF;
}

.ant-tabs-nav::before {
    border: none;
}

/* ant-tabs end */

/* rc-dock start */
.dock-tab {
    height: auto;
    background: #fff;
}

.dock-hbox {
    gap: 10px;
}

.dock-vbox {
    gap: 1.9px;
}

.dock-panel, .dock-top {
    border-radius: 8px;
}

.dock-bar, .dock-top {
    background: #fff !important;
}

.dock-bar {
    font-size: 18px;
    border-bottom: 0 !important;
}

.drag-initiator {
    padding: 10px 0;
    color: #929A9E;
    font-weight: 600;
}

.dock-tab-active .drag-initiator {
    color: var(--primary-color) !important;
    border-bottom: 3px solid var(--primary-color);
}

.dock-tab:hover,
.dock-tab-active,
.dock-tab-active:hover {
    color: var(--primary-color);
}

.dock-ink-bar {
    display: none;
    background-color: var(--primary-color);
    margin-top: 0;
}

.dock-tab {
    border: 0;
}

.dock-extra-content {
    position: relative;
    right: 30px;
    top: 15px;
}

.dock-panel.dock-style-main {
    border: none;

    .dock-bar {
        background: none;
        /* border-bottom: 1px solid d; */
    }

    /* .dock-tab {
        background: rgb(79, 208, 200);
    } */
}

.dock-panel {
    border: none;
}

.dock-panel.dock-style-card {
    .dock-tab {
        color: #000;
        border: 0;
    }

    .dock-panel-min-btn, .dock-panel-max-btn {
        display: none;
    }
}

.dock-tabpane {
    display: flex;
}

/* rc-dock end */

.ant-card {
    display: flex;
    flex-direction: column;
}

.ant-card-body {
    padding-top: 12px !important;
    padding-bottom: 12px !important;
    flex: 1;
}

.collapse .ant-card-body {
    padding-top: 0 !important;
    padding-bottom: 0 !important;
    flex: 1;
}

.actionLink a {
    color: var(--primary-color);
}

.actionLink a:hover {
    color: var(--primary-color-hover);
}

/* calendar start */
.fc-button {
    box-shadow: none !important;
}

.fc-button-primary {
    background-color: #FFFFFF !important;
    color: #B3BBC8 !important;
    border-color: rgba(0, 0, 0, 0.25) !important;
}

.fc-button-active {
    background-color: #E7F9F8 !important;
    color: var(--primary-color) !important;
    border-color: var(--primary-color) !important;
    font-weight: 550 !important;
}

/*.fc-today-button {*/
/*    background-color: #FFFFFF !important;*/
/*    color: var(--primary-color) !important;*/
/*    border-color: var(--primary-color) !important;*/
/*}*/

/*.fc-today-button:disabled {*/
/*    background-color: #FFFFFF !important;*/
/*    color: #929A9E !important;*/
/*    border-color: #929A9E !important;*/
/*}*/

.fc-col-header-cell-cushion,
.fc-daygrid-day-number {
    color: #212529;
}

.fc .fc-daygrid-day.fc-day-today,
.fc-highlight {
    background-color: #E7F9F8 !important;
}

.fc-daygrid-day-bg .fc-daygrid-bg-harness .fc-highlight + .fc-daygrid-day-top .fc-daygrid-day-number {
    color: #007D71
}

/* calendar end   */

/* button reset  */
.ant-btn-default:not([disabled]):hover {
    background-color: #14C9BB1A !important;
}

.ant-btn-primary:not([disabled]):hover {
    background-color: var(--primary-color-hover) !important;
}

.ant-btn-link:not([disabled]):hover {
    color: var(--primary-color-hover) !important;
}

.ant-btn-minus:not([disabled]):hover {
    color: #F53F3F !important;
}

.ant-modal .ant-modal-footer .ant-btn + .ant-btn:not(.ant-dropdown-trigger),
.ant-modal-confirm .ant-modal-confirm-btns .ant-btn+.ant-btn{
    margin-inline-start: 16px;
}

/* DatePicker */
.ant-picker-now-btn {
    color: var(--primary-color);
}

.ant-picker-now-btn:hover {
    color: var(--primary-color-hover);
}
/* Drawer */
.ant-drawer-title {
    font-size: 20px !important;
}
/* Modal */
.ant-modal-mask {
    background-color: rgba(0, 0, 0, 0.3) !important;
}

.ant-modal-title,
.ant-modal-confirm-title{
    font-size: 20px !important;
    font-family: Lato, serif;
}

.ant-modal-confirm-btns span{
    font-family: Lato, serif !important;
}

.ant-modal-confirm-btns {
    padding-top: 16px;
    border-top: 1px solid #E7E7E7;
}

.ant-modal-confirm-paragraph {
    max-width: 100% !important;
}

.big-modal .ant-modal-close,
.middle-modal .ant-modal-close,
.small-modal .ant-modal-close {
    display: none;
}

.big-modal .ant-modal-content,
.middle-modal .ant-modal-content,
.small-modal .ant-modal-content {
    padding: 0;
}

.big-modal .ant-modal-confirm-btns  {
    margin-top: 80px;
}

.middle-modal .ant-modal-confirm-btns,
.small-modal .ant-modal-confirm-btns  {
    margin-top: 40px;
}

.big-modal span.anticon.anticon-close,
.middle-modal span.anticon.anticon-close,
.small-modal span.anticon.anticon-close {
    cursor: pointer;
    fill: #A2ACB2;
    color:  #A2ACB2;
}

.big-modal span.anticon.anticon-close:hover,
.middle-modal span.anticon.anticon-close:hover,
.small-modal span.anticon.anticon-close:hover {
    fill: var(--primary-color);
    color: var(--primary-color);
}

.big-modal .ant-modal-confirm-btns,
.middle-modal .ant-modal-confirm-btns,
.small-modal .ant-modal-confirm-btns {
    padding: 16px 0;
    margin: 40px 24px 0;
}

.big-modal .ant-modal-confirm-btns .ant-btn,
.ampcon-mini-modal .ant-modal-confirm-btns .ant-btn-primary,
.ampcon-mini-modal .ant-modal-confirm-btns .ant-btn-default,
.middle-modal .ant-modal-confirm-btns .ant-btn,
.small-modal .ant-modal-confirm-btns .ant-btn {
    /*width: 100px;*/
    font-weight: 600;
    border-radius: 2px;
    -webkit-border-radius: 2px;
    -moz-border-radius: 2px;
    -ms-border-radius: 2px;
    -o-border-radius: 2px;
    /* margin-left: 8px; */
}
/* .ant-space-gap-col-small {
    column-gap: 16px;
  } */

.big-modal .ant-modal-confirm-content {
    height: calc(100vh - 321px);
    min-height: 326px; /* 326(modal content height) + 105(modal btns height) + 69(modal title height) = 500(big modal min height) */
    padding: 16px 24px 0 24px;
    max-height: 584px;
    overflow: auto;
}

.middle-modal .ant-modal-confirm-content {
    height: calc(100vh - 321px);
    min-height: 276px; /* 276(modal content height) + 105(modal btns height) + 69(modal title height) = 450(middle modal min height) */
    padding: 16px 24px 0 24px;
    max-height: 298px;
    overflow: auto;
}

.small-modal .ant-modal-confirm-content {
    height: calc(100vh - 321px);
    min-height: 66px; /* 66(modal content height) + 105(modal btns height) + 69(modal title height) = 240(small modal min height) */
    padding: 16px 24px 0 24px;
    max-height: 104px;
    overflow: auto;
}

.ampcon-max-modal{
    width: 1360px !important;
    min-height: 500px !important;
    .ant-divider {
        width: calc(100% + 48px);
        margin-left: -24px;
    }

    .ant-modal-header {
        padding-left: 24px;
        padding-right: 24px;
    }

    .ant-modal-footer {
        padding-left: 24px;
        padding-right: 24px;
    }

    .ant-modal-content {
        min-height: 500px;
        padding-left: 0;
        padding-right: 0;
    }

    .ant-modal-body {
        height: calc(100% - 135px);
        min-height: 420px;
        max-height: calc(100vh - 350px);
        /* min-height: 650px !important; */
        overflow-y: auto;
        overflow-x: hidden;
        padding-left: 24px;
        padding-right: 24px;
        padding-top: 16px;
    }
}
.ampcon-max-modal .ant-tabs-nav {
    margin: 0;
}

.ampcon-middle-modal{
    width: 680px !important;
    min-height: 450px !important;

    .ant-modal-content {
        min-height: 450px !important;
        width: 680px !important;
        padding-left: 0;
        padding-right: 0;
    }
    .ant-modal-header {
        padding-left: 24px;
        padding-right: 24px;
    }

    .ant-modal-footer {
        padding-left: 24px;
        padding-right: 24px;
    }

    /*.ant-modal-confirm-btns {
        margin-top: 200px;
    }*/
    .ant-modal-body {
        overflow-y: auto;
        overflow-x: hidden;
        padding: 16px 32px 20px !important;
        max-height: calc(100vh - 350px);
    }
    .middle-modal-textarea {
        height: 347.2px !important;
        min-height: 347.2px !important;
    }

}
.ampcon-custom-modal-style {
    .ant-modal-content {
        min-height: 450px !important;
        width: 680px !important;
        padding-left: 0;
        padding-right: 0;
    }
    .ant-modal-header {
        padding-left: 24px;
        padding-right: 24px;
    }

    .ant-modal-footer {
        padding-left: 24px;
        padding-right: 24px;
    }
    .ant-modal-body {
        padding: 24px 32px 0px !important;
        max-height: 420px;
        overflow-y: auto;
        overflow-x: hidden;
    }
}
.syslog-config-modal {
    .ant-modal-body {
        max-height: 360px;
    }
}
.tree-modal-action-style {
    .ant-modal-content {
        min-height: 450px !important;
        width: 680px !important;
        padding-left: 0;
        padding-right: 0;
    }
    .ant-modal-header {
        padding-left: 24px;
        padding-right: 24px;
    }

    .ant-modal-footer {
        padding-left: 24px;
        padding-right: 24px;
    }
    .ant-modal-body {
        padding: 0 32px !important;
        max-height: 420px;
    }
    .ant-modal-confirm-body {
        height: 332px;
    }
}
.import-license-style {
    .ant-modal-body {
        min-height: 394px;
    }
}

.ampcon-mini-modal .ant-modal-content{
    width: 480px !important;
    height: auto;
    min-height: 240px;
    padding: 0px;
    .ant-modal-confirm-content {
        padding-left: 24px !important;
        padding-right: 24px !important;
    }
    .ant-modal-confirm-btns {
        margin-top:  40px !important;
    }
}

.ampcon-max-modal button.ant-modal-close:hover,
.ampcon-middle-modal button.ant-modal-close:hover,
.ampcon-custom-modal-style button.ant-modal-close:hover,
.ampcon-mini-modal button.ant-modal-close:hover,
.ant-modal-confirm-title span.anticon.anticon-close:hover{
    fill: var(--primary-color);
    color: var(--primary-color);
    background-color: #FFFFFF;
    border-color: #2bbf2b;
}

.ant-modal-close {
    background-color: #FFFFFF !important;
}

.ant-modal-title {
    font-size: 20px !important;
}

.ant-form-item-tooltip {
    padding-left: 2px;
}

.ant-layout {
    background: #F0F2F5;
}

.ant-tooltip:has(.fixed-tooltip) {
    position: fixed;
    left: 210px !important;
}

.ant-radio-button-wrapper-checked {
    background-color: #14C9BB1A !important;
}

.ant-menu-inline {
    border: none !important;
    .ant-menu-item.ant-menu-item-selected {
        .dash-board-dark-svg {
            margin-left: 0 !important;
            display: block;
            width: auto;
        }

        .dash-board-light-svg {
            display: none;
            width: 0;
        }
    }
    .ant-menu-item {
        width: 100% !important;
        border-radius: 0;
        margin: 0;
        .dash-board-dark-svg {
            display: none;
            width: 0;
        }
    }
}
.ant-menu-inline .ant-menu-submenu-title {
    width: 100%;
    border-radius: 0;
    margin-left: 0;
    margin-top: 0;
}

.ant-menu-vertical{
    border: none !important;
    .ant-menu-title-content{
        color: black;
    }
    .ant-menu-submenu-arrow{
        color: black !important;
    }

    .ant-menu-item-active {
        background-color: white !important;

        .ant-menu-title-content {
            color: var(--primary-color);
        }

        .ant-menu-submenu-arrow {
            color: var(--primary-color) !important;
        }
    }
    .ant-menu-item-active:has(svg),
    .ant-menu-item-selected:has(svg),
    .ant-menu-submenu-selected .ant-menu-submenu-title:has(svg) {
        background-color: #5AD9CF !important;
    }
    .ant-menu-submenu-selected .ant-menu-submenu-title:not(:has(svg)) {
        background-color: #14C9BB1A !important;
    }
    .ant-menu-submenu-title:has(svg) .ant-menu-item-icon svg ,
    .ant-menu-item:has(svg) .ant-menu-item-icon svg ,
    .ant-menu-item:has(svg) .dash-board-light-svg svg{
        margin-top: 10px;
    }
    .dash-board-dark-svg {
        display: none;
    }
}

.ant-menu-submenu-vertical.ant-menu-submenu-active{
    .ant-menu-title-content {
        color: var(--primary-color) !important;
    }
    .ant-menu-submenu-arrow {
        color: var(--primary-color) !important;
    }
}

.ant-menu-submenu {
    .ant-menu-submenu-vertical.ant-menu-submenu-active > .ant-menu-submenu-title {
        background-color: white !important;
    }
    .ant-menu-sub {
        .ant-menu-submenu {
            .ant-menu-sub {
                .ant-menu-item {
                    margin-left: 0;
                    padding-left: 47px !important;
                }
            }
        }
    }
}

.ant-menu-vertical {
    .ant-menu-item-selected.ant-menu-item-only-child {
        background-color: #14C9BB1A !important;

        .ant-menu-title-content {
            color: var(--primary-color) !important;
        }
    }
}

body:has(.ant-spin-fullscreen.ant-spin-fullscreen-show) div {
    pointer-events: none !important;
}

.ant-spin-fullscreen.ant-spin-fullscreen-show{
    z-index: 2000;
}
.ant-table-wrapper .ant-table-row-expand-icon,
.ant-table-wrapper .ant-table-row-expand-icon:active,
.ant-table-wrapper .ant-table-row-expand-icon:focus {
    color: #d9d9d9 !important;
    border-radius: 2px;
}

.ant-table-wrapper .ant-table-row-expand-icon:hover {
    color: var(--primary-color) !important;
}

.ant-select:not(.ant-select-disabled):not(.ant-select-customize-input):not(.ant-pagination-size-changer):hover .ant-select-selector,
.ant-select-focused.ant-select:not(.ant-select-disabled):not(.ant-select-customize-input):not(.ant-pagination-size-changer) .ant-select-selector{
    border-color: var(--primary-color) !important;
}

.ant-select-dropdown .ant-select-item-option-selected {
    color: #14C9BB !important;
    font-weight: normal !important;
}

.ant-menu-vertical.ant-menu-submenu-active .ant-menu-item:has(.ant-menu-item-icon){
    background-color: #5AD9CF !important;
}
.ant-menu-root> .ant-menu-submenu-selected> .ant-menu-submenu-title {
    background-color: #5AD9CF !important;
    margin-bottom: 0;
}
.ant-tree-directory .ant-tree-treenode-selected .ant-tree-switcher,
.ant-tree-directory .ant-tree-treenode-selected .ant-tree-node-selected,
.ant-tree-list-holder-inner .ant-tree-treenode-selected {
    color:#14C9BB !important;
}

.ant-tree-treenode-selected .ant-tree-node-selected .ant-tree-title {
    color:#14C9BB !important;
}

.ant-tree.ant-tree-directory .ant-tree-treenode-selected::before,
.ant-tree.ant-tree-directory .ant-tree-treenode-selected {
    /* background-color: #e6fff9; */
    background-color: #fff;
}

.ant-tree .ant-tree-list-holder-inner {
    align-items: stretch;
}
.ant-tree .ant-tree-node-content-wrapper {
    flex: auto ;
}
.ant-tree.ant-tree-directory .ant-tree-treenode-selected:hover::before{
    background: rgba(0, 0, 0, 0.04);
}
.ant-picker-datetime-panel .ant-picker-time-panel .ant-picker-content .ant-picker-time-panel-cell-selected .ant-picker-time-panel-cell-inner {
    background-color: var(--primary-color);
    color: #FFFFFF;
}
.ant-picker-datetime-panel .ant-picker-time-panel .ant-picker-content .ant-picker-time-panel-cell-selected .ant-picker-time-panel-cell-inner:hover {
    /* background-color: var(--primary-color); */
    color: #212529;
}

.ant-picker-dropdown .ant-picker-cell-in-view.ant-picker-cell-today .ant-picker-cell-inner:hover {
    background-color: #E7F9F8 !important;
    color: #14C9BB !important;
}
.ant-picker-dropdown .ant-picker-cell-in-view.ant-picker-cell-today .ant-picker-cell-inner::before{
    border: 1px solid #34DCCF;
}
.ant-picker-dropdown .ant-picker-cell-in-view.ant-picker-cell-today .ant-picker-cell-inner {
    color: #14C9BB !important;
}
.ant-picker-dropdown .ant-picker-cell-in-view.ant-picker-cell-selected .ant-picker-cell-inner {
    color: #FFF !important;
}
.ant-picker-dropdown .ant-picker-cell-in-view.ant-picker-cell-selected .ant-picker-cell-inner:hover{
    background-color: #34DCCF !important;
    color: #FFF !important;
}

.ant-layout-content .service_layer1 > div:nth-child(2) > div:nth-child(1) > div  {
    border: 1px solid #E7E7E7;
}
.amplifier_style > div:nth-child(2) > div:nth-child(1)  > div {
    border:1px solid #E7E7E7;
}

.fc-today-button.fc-button.fc-button-primary:enabled {
    border-color: rgba(0, 0, 0, 0) !important;
    background-color: var(--primary-color) !important;
    color: white !important;
}

.fc-today-button.fc-button.fc-button-primary:disabled {
    cursor: not-allowed !important;
    border: 1px solid #DADCE1 !important;
    color: #B3BBC8 !important;
    background-color: rgba(0, 0, 0, 0.04) !important;
    box-shadow: none;
    font-weight: 500 !important;
}

.fc-today-button.fc-button.fc-button-primary:enabled:hover {
    background-color: var(--primary-color-hover) !important;
}

.fc-prev-button.fc-button.fc-button-primary:hover {
    color: var(--primary-color) !important;
}

.fc-next-button.fc-button.fc-button-primary:hover {
    color: var(--primary-color) !important;
}

.fc-button-primary {
    width: 100px;
}

.fc-button-primary:hover {
    color: var(--primary-color) !important;
}

.ant-form-item-control-input-content:hover{
    color:var(--primary-color) !important;
}

.ant-modal-close-x:hover{
    color:var(--primary-color) !important;
}

.ant-modal-close:hover{
    border: 0 !important;
}

.ant-tag{
    border-radius: 2px;
}

.ant-checkbox span.ant-checkbox-inner{
    border-radius: 2px;
}

.ant-tree-select-dropdown .ant-select-tree-checkbox .ant-select-tree-checkbox-inner {
    border-radius: 2px;
  }

.ant-select-tree-title {
    white-space: nowrap;
  }

.ant-tag-close-icon {
    color:var(--primary-color) !important;
}

.ant-descriptions-row .ant-descriptions-item-label,
.ant-descriptions-row .ant-descriptions-item-content {
    padding: 4.75px 16px !important;
}

.failedTag {
    background-color: rgba(245, 63, 63, 0.1);
    color: rgb(245, 63, 63);
    border-color: rgb(245, 63, 63);
    font-size: 14px;
    /* line-height: 32px;
    width: 80px; */
    text-align: center;
}
.successTag {
    background-color: rgba(84, 196, 28, 0.1);
    color: #2BC174;
    border-color: #2BC174;
    font-size: 14px;
    /* line-height: 32px;
    width: 80px; */
    text-align: center;
}
.ant-descriptions-row .ant-descriptions-item-label,
.ant-descriptions-row .ant-descriptions-item-content {
    padding: 4.75px 16px !important;
}

.rotate-icon{
    transition: transform 0.3s ease;
}

.ant-dropdown-trigger:hover .rotate-icon {
    transform: rotateX(180deg);
}

button.ant-table-row-expand-icon.ant-table-row-expand-icon-collapsed {
    border-radius: 2px;
}
.ant-space-align-center {
    align-items: first baseline !important;
}

.pieStyle > .ant-card-body > div > div,
.barStyle > .ant-card-body > div > div {
    display: flex;
    justify-content: center;
    align-items: center;
}
/* .ant-card-body > div {
    padding-top: 12px;
} */
.switchViewbar .ant-card-body > div > div > div {
    display: flex;
    justify-content: center !important;
    align-items: center !important;
}
.ant-table-cell {
    height: 48px !important;
    padding: 0 16px !important;
}
.imageStyle {
    width: 96px;
    height: 96px;
    background-image: url("data:image/png;base64,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");
    background-size: cover; /* »òÆäËûÊÊºÏÄãµÄ²¼¾ÖµÄÖµ */
    background-position: center;
}
.ant-select-selector {
    border-radius: 2px !important;
}
.userModalflex > div > div:nth-child(2) > div > div {
    display: flex;
}

.menu-dot::before {
    content: "·";
    color: #fff;
    margin-right: 8px;
    font-size: 20px;
    font-weight: 800;
}

.ant-modal-header {
    .ant-modal-title {
        margin-top: -6px;
    }
}
.ant-modal-body {
    .ant-divider:nth-child(1) {
        margin-top: 0;
    }
}
.ampcon-middle-modal .ant-divider,
.ampcon-custom-modal-style .ant-divider {
    width: 680px;
    /* margin: 24px 0; */
    margin-left: -24px;
}
.ant-tree .ant-tree-node-content-wrapper.ant-tree-node-selected {
    background-color: #fff;
}
.ant-tree .ant-tree-node-content-wrapper.ant-tree-node-selected:hover {
    background-color: rgba(0, 0, 0, 0.04);
}
.ant-tree-title .ant-btn {
    padding: 1px 6px;
}
.ant-menu-submenu-selected .ant-menu-submenu-open .ant-menu-inline,
.ant-menu-root .ant-menu-submenu-selected .ant-menu-inline:has(.ant-menu-submenu-selected),
.ant-menu-root .ant-menu-submenu .ant-menu-sub:has(.ant-menu-item-selected){
    background-color: #2BCEC1 !important;
}
.drop-down-click,
.fc-daygrid-day-top a:hover {
    color: #14c9bb
}
.drop-down-click:hover {
    color: #34dccf;
}
.playbook-button {
    align-items: center !important;
}
.ant-tree-title .ant-btn {
    padding: 1px 6px;
}
.ant-menu-submenu-selected .ant-menu-submenu-open .ant-menu-inline {
    background-color: #2BCEC1 !important;
}
.drop-down-click,
.fc-daygrid-day-top a:hover {
    color: #14c9bb
}
.drop-down-click:hover {
    color: #34dccf;
}
.playbook-button {
    align-items: center !important;
}
.ant-modal-root .ant-modal-wrap {
    overflow: hidden;
}
.ant-btn-link {
    color: #14C9BB;
}
.ant-form-inline {
    flex-wrap:nowrap;
}


/* * {
    scrollbar-width: thin !important;
} */

/* ::-webkit-scrollbar {
    width: 6px;
    height: 8px;
    border-radius: 3px;
}

::-webkit-scrollbar-thumb {
    border-radius: 3px;
}

::-webkit-scrollbar-track {
    border-radius: 3px;
} */

/* ::-webkit-scrollbar {
    display: none;
}

::-ms-scrollbar {
    display: none;
}

::-moz-scrollbar {
    display: none;
}

::-o-scrollbar {
    display: none !important;
} */
.ant-table-wrapper .ant-table-row-expand-icon, .ant-table-wrapper .ant-table-row-expand-icon:active, .ant-table-wrapper .ant-table-row-expand-icon:focus {
    color: #474747 !important;
}
.custom-empty-container {
    margin-bottom: 16px;
}
.ant-table {
    width: 100%;
    overflow-x: auto;
}

.ant-table-tbody > tr > td {
    word-break: keep-all;
    white-space: nowrap;
    text-overflow: clip;
    overflow: visible;
}

.scrollable-container {
    background: #fff;
    min-height: 100%;
}

.ant-table-wrapper .ant-table-container .ant-table-content table,
.ant-table-wrapper .ant-table-container .ant-table-content table th,
.ant-table-wrapper .ant-table-container .ant-table-content table td,
.ant-table-cell,
.ant-table-column {
    white-space: nowrap !important;
    word-break: keep-all !important;
    overflow: visible !important;
    text-overflow: clip !important;
}

.ant-table-wrapper .ant-table {
    table-layout: auto !important;
    width: auto !important;
}

.ant-table-wrapper .ant-table-thead > tr > th,
.ant-table-wrapper .ant-table-tbody > tr > td {
    max-width: none !important;
    width: auto !important;
}

.chart-center .ant-card-body >div {
    height: 100%;
    display: flex;
    justify-content: center;
}
.linechart .ant-card-body >div,
.chart-center .ant-card-body >div >div {
    min-height: 160px;
    height: 100% !important;
    display: flex;
    align-items: center;
}
.ant-modal-confirm-body .ant-modal-confirm-title > div,
.ant-modal-confirm-btns {
    width: calc(100% - 40px);
    margin-left: 0px !important;
}

.ant-modal-confirm-body .ant-modal-confirm-title >div {
    padding: 16px 16px 20px 24px !important;
    margin-top: 0px;
    padding-bottom: 10px;
}
.tree-modal-action-style {
    width: 680px !important;
    .ant-modal-content {
        padding-top: 15px;
    }
    .ant-modal-confirm-body .ant-modal-confirm-title > div {
        width: 680px !important;
        margin-left: -32px !important;
        padding: 0px 0px 8px 0px !important;
    }
    .tree-modal-action >span {
        margin-left: 24px;
    }
    .ant-modal-close-x {
        margin-top: 2px;
    }
    .ant-modal-confirm-btns {
        width: 680px !important;
        margin-left: -32px !important;
        padding-bottom: 0px !important;
        margin-top: 0px !important;
    }
    .ant-modal-confirm-body {
        margin-bottom: 30px !important;
    }
}

.middle-modal .ant-modal-confirm-body:not(:has(.tree-modal-action)) .ant-modal-confirm-title >div {
    padding: 20px 20px 13px 20px !important;
}
.middle-modal {
    .ant-modal-confirm-content {
        padding-left: 32px;
        padding-right: 32px;
    }
}
.small-modal .ant-modal-confirm-body:not(:has(.tree-modal-action)) .ant-modal-confirm-title >div {
    padding: 20px 20px 24px 20px !important;
}
.small-modal {
    .ant-modal-confirm-content {
        height: 70px;
        min-height: 70px;
    }
}
.ant-modal-confirm-btns {
    padding: 20px !important;
    width: 100%;
}

.group-management-tabs .ant-tabs-tabpane {
    padding-top: 0;
    width: 60vw;
}

.ant-menu .ant-menu-item .anticon +span {
    margin-inline-start: 4px;
}

.ant-menu .ant-menu-submenu-title .anticon +span {
    margin-inline-start: 4px;
}
.actionLink{
    gap: 0px 10px !important;
}

*::-webkit-scrollbar {
    width: 5px;
    height: 5px
}

*::-webkit-scrollbar-track {
    background: transparent
}

*::-webkit-scrollbar-corner {
    background-color: transparent
}

*::-webkit-scrollbar-thumb {
    border-radius: 6px;
    background: #e0e6ed;
}

*::-webkit-scrollbar-thumb:hover {
    background: #C7CFD8;
}


/* 调整 Spin 的大小 */
.custom-spin .ant-spin-dot {
    font-size: 18px;
    color: #14c9bb; /* Ant Design 的主要颜色 */
}

.ant-tour-content {
    width: 600px !important;
}

.ant-tour-header {
    padding: 16px !important;
    .ant-tour-title {
        font-size: 20px !important;
        font-weight: bold;
    }
}

.ant-tour-buttons {
    .ant-btn {
        margin-inline-start: 16px !important;
        height: 32px !important;
        padding: 2px 16px !important;
        border-radius: 2px !important;
    }
}

.ant-tour-description {
    overflow-wrap: break-word !important;
    word-break: break-all !important;
    font-size: 18px !important;
    .ant-divider {
        margin: 0px !important;
        width: calc(100% + 32px) !important;
        margin-left: -16px !important;
    }
    p {
        margin-bottom: 50px !important;
    }
}

.ant-tour-footer {
    padding: 16px !important;
}

.no-interaction {
    pointer-events: none;
}

.ant-picker-range .ant-picker-active-bar {
    display: none !important; 
}

.telemetry-view-div {
    display: grid;
    height: 100%;
    width: 100%;
    margin-top: 18px;
    margin-bottom: 18px;
    gap: 18px 24px;
    grid-template-columns: repeat(2, 1fr);
    grid-auto-rows: minmax(auto, 1fr);
}

.monitoring-view-div {
    display: grid;
    height: 100%;
    width: 100%;
    margin-top: 18px;
    margin-bottom: 18px;
    gap: 18px 24px;
    grid-template-columns: repeat(2, 1fr);
    grid-auto-rows: minmax(auto, 1fr);
}

@media (max-width: 1300px) {
    .monitoring-view-div {
        grid-template-columns: 1fr; /* 小屏幕上每行显示一个元素 */
    }
}

@media (max-width: 1300px) {
    .telemetry-view-div {
        grid-template-columns: 1fr; /* 小屏幕上每行显示一个元素 */
    }
}

.up-tag {
    background: rgba(43,193,116,0.1) !important;
    border-radius: 2px !important;
    border: 1px solid #2BC174 !important;
    color: #2BC174 !important;
    font-size: 14px !important;
}

.down-tag {
    background: rgba(245,63,63,0.1) !important;
    border-radius: 2px !important;
    border: 1px solid #F53F3F !important;
    color: #F53F3F !important;
    font-size: 14px !important;
}

.ant-slider-mark-text {
    white-space: nowrap;
}

.x6-widget-minimap {
    position: absolute;
    bottom: 0;
    right: 0;
}

.sub-menu-render-left .x6-menu-submenu-menu {
  left: auto !important;
  right: 100% !important;
  top: 0;
}

.logs_container__V62Xc > .custom_table_fixFixedTableLeftBorder__1usKb {
    padding-bottom: 24px !important;
}
.upgrade_container__Uc6sZ {
    margin-bottom: 24px !important;
}

.ant-table-cell.ant-table-cell-row-hover {
    background-color: rgb(250, 250, 250) !important;
}

:where(.css-dev-only-do-not-override-8f7yq9).ant-table-wrapper .ant-table-tbody .ant-table-row.ant-table-row-selected>.ant-table-cell {
    background: none;
}

.ant-table-container .ant-table-sticky-scroll {
    display: none;
}

.ant-table-sticky-scroll .ant-table-sticky-scroll-bar {
    display: none;
}

a.disabled {
    pointer-events: none;
    color: #B3BBC8;
    cursor: not-allowed;
}

.ant-table-wrapper tr.ant-table-expanded-row > th,
.ant-table-wrapper tr.ant-table-expanded-row > td {
    background-color: #f7f7f7;
}

.ant-table-wrapper tr.ant-table-expanded-row:hover > th,
.ant-table-wrapper tr.ant-table-expanded-row:hover > td {
    background-color: #f7f7f7;
}

.switch-view-flow-chart-container path {
    cursor: default !important;
}


.ant-table-wrapper .ant-table-tbody .ant-table-row.ant-table-row-selected .ant-table-cell-row-hover {
    background: #E6FDFB !important;
}
.ant-table-wrapper .ant-table-tbody .ant-table-row.ant-table-row-selected >.ant-table-cell {
    background-color: #F5FFFE;
}

.ant-picker-dropdown .ant-picker-ranges {
    margin-top: 0;
    padding: 8px 12px;
}

.middle-modal .ant-modal-content {
    border-radius: 0 !important;
}

.middle-modal .ant-modal-content,
.middle-modal .ant-modal-content > *,
.middle-modal .ant-modal-footer,
.middle-modal .ant-modal-confirm-btns,
.middle-modal .ant-modal-confirm-content {
    border-radius: 0 !important;
}

.ant-modal-confirm-body-wrapper .ant-modal-confirm-btns {
    border-radius: 0 !important;
}

.middle-modal .ant-modal-confirm-body > .ant-modal-confirm-btns {
    border-radius: 0 !important;
}

.middle-modal .ant-modal-content *,
.middle-modal .ant-modal-wrap *,
.middle-modal .ant-modal-confirm *,
.middle-modal .ant-modal-body *,
.middle-modal .ant-modal-footer * {
    border-radius: 0 !important;
}

.middle-modal div[style*="height: 32px"] {
    border-radius: 0 !important;
}