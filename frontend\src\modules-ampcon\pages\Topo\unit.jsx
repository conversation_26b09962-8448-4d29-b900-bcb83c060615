import React, {useEffect, useState} from "react";
import {Space, Table, Button, Form, Card, message} from "antd";
import Icon from "@ant-design/icons";
import {
    TableFilterDropdown,
    handleTableChange,
    createMatchMode,
    createColumnConfig,
    GlobalSearchInput,
    createFilterFields
} from "@/modules-ampcon/components/custom_table";
import topoStyle from "@/modules-ampcon/pages/Topo/topo.module.scss";
import {useTableInitialElement} from "@/modules-ampcon/hooks/useModalTable";
import {addSvg} from "@/utils/common/iconSvg";
import {confirmModalAction} from "@/modules-ampcon/components/custom_modal";
import {fetchUnitListInfo, delUnitInfo} from "@/modules-ampcon/apis/unit_api";
import {useNavigate} from "react-router-dom";

const TopoUnit = ({setComponent}) => {
    const navigate = useNavigate();
    const [
        selectModalOpen,
        setSelectModalOpen,
        searchFields,
        setSearchFields,
        data,
        setData,
        loading,
        setLoading,
        pagination,
        setPagination
    ] = useTableInitialElement([], true);

    const checkSortedColumn = columns => {
        for (const columnKey in columns) {
            if (Object.prototype.hasOwnProperty.call(columns, columnKey)) {
                const columnConfig = columns[columnKey];
                if (columnConfig.defaultSortOrder === "descend") {
                    return columnConfig.dataIndex;
                }
            }
        }
        return undefined;
    };
    const [sorter, setSorter] = useState({});
    const [filters, setFilters] = useState({});

    const fetchData = async () => {
        setLoading(true);

        const filterFields = filters ? createFilterFields(filters, matchModes) : [];
        const sortFields = [];
        if (sorter.field && sorter.order) {
            sortFields.push({
                field: sorter.field,
                order: sorter.order === "ascend" ? "asc" : "desc"
            });
        }

        try {
            const response = await fetchUnitListInfo(
                pagination.current,
                pagination.pageSize,
                filterFields,
                sortFields,
                searchFields
            );

            setData(response.data);
            setPagination(prev => ({
                ...prev,
                total: response.total,
                current: response.page,
                pageSize: response.pageSize
            }));
        } catch (error) {
            // error
        } finally {
            setLoading(false);
        }
    };

    const del_topo = async record => {
        const response = await delUnitInfo(record.id);
        if (response.status === 200) {
            message.success(response.info);
        } else {
            message.error(response.info);
        }
        await fetchData();
    };

    useEffect(() => {
        fetchData().then(() => {
            const sortedColumn = checkSortedColumn(tableColumns);
            if (sortedColumn) {
                sorter.field = sortedColumn;
                sorter.order = "descend";
                tableChange("", "", sorter);
            }
        });
    }, []);

    useEffect(() => {
        fetchData().then();
    }, [searchFields]);

    const addTopo = () => {
        navigate("/topo/unit/detail", {state: {actionType: "Add"}});
    };

    const editTopo = record => {
        navigate("/topo/unit/detail", {state: {actionType: "Edit", data: record}});
    };

    const handleSearchChange = e => {
        setSearchFields({
            fields: ["name", "email"],
            value: e.target.value
        });
    };

    const tableColumns = [
        createColumnConfig("Unit Name", "name"),
        createColumnConfig("Description", "description", TableFilterDropdown, "", "", "descend"),
        createColumnConfig("Leaf Count", "leaf_count", TableFilterDropdown),
        createColumnConfig("Mlag Count", "mlag_count"),
        createColumnConfig("Access Count", "access_count"),
        {
            title: "Operation",
            render: (_, record) => (
                <Space size="middle" className={topoStyle.actionLink}>
                    <a onClick={() => editTopo(record)}>Edit</a>
                    <a onClick={() => navigate("/topo/unit/detail", {state: {actionType: "View"}})}>View</a>
                    <a onClick={() => confirmModalAction("Are you sure want to delete?", () => del_topo(record))}>
                        Clone
                    </a>
                    <a onClick={() => confirmModalAction("Are you sure want to delete?", () => del_topo(record))}>
                        Delete
                    </a>
                </Space>
            )
        }
    ];

    const matchModes = createMatchMode([
        {name: "name", matchMode: "exact"},
        {name: "email", matchMode: "fuzzy"},
        {name: "ctime", matchMode: "fuzzy"}
    ]);

    const tableChange = async (pagination, filters, sorter) => {
        setSorter(sorter);
        setFilters(filters);
        await handleTableChange(
            pagination,
            filters,
            sorter,
            setPagination,
            searchFields,
            fetchUnitListInfo,
            "",
            setData,
            matchModes,
            setLoading
        );
    };

    return (
        <Card style={{display: "flex", flex: 1}}>
            <h2 style={{margin: "8px 0 20px"}}>Unit</h2>
            <Space size={16} style={{marginBottom: "30px"}}>
                <Button type="primary" block onClick={() => addTopo()}>
                    <Icon component={addSvg} />
                    Unit
                </Button>
            </Space>
            <GlobalSearchInput onChange={handleSearchChange} />
            <div>
                <Table
                    columns={tableColumns}
                    bordered
                    rowKey={record => record.id}
                    loading={loading}
                    dataSource={data}
                    pagination={pagination}
                    onChange={tableChange}
                />
            </div>
        </Card>
    );
};

export default TopoUnit;
