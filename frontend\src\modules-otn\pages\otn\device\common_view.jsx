import ViewItem from "@/modules-otn/pages/otn/device/view_item";
import {CloseOutlined} from "@ant-design/icons";
import {Button} from "antd";
import {useState} from "react";
import {useDispatch} from "react-redux";
import {NE_TYPE_CONFIG, NULL_VALUE} from "@/modules-otn/utils/util";
import {objectGet} from "@/modules-otn/apis/api";
import {setSelectedItem} from "@/store/modules/otn/mapSlice";
import styles from "./common_view.module.scss";

const CommonView = () => {
    const [nodeInfo, setNodeInfo] = useState(null);
    const dispatch = useDispatch();
    return (
        <div className={styles.wrap}>
            <ViewItem setNodeInfo={setNodeInfo} type="switch" />
            <ViewItem setNodeInfo={setNodeInfo} type="otn" />
            {nodeInfo && (
                <div className={styles.container}>
                    <div className={styles.container_header}>
                        <p className={styles.container_header_text}>Information</p>
                        <div className={styles.container_background}>
                            <CloseOutlined
                                style={{fontSize: 12, padding: 2}}
                                onClick={() => {
                                    setNodeInfo(null);
                                }}
                            />
                        </div>
                    </div>
                    <div className={styles.container_content}>
                        <div style={{display: "flex", alignItems: "center"}}>
                            <span className={styles.container_round} />
                            <div style={{marginLeft: 10}}>
                                {nodeInfo?.nodeType === "switch" ? (
                                    <span style={{color: "#B3BBC8"}}>SN: </span>
                                ) : (
                                    <span style={{color: "#B3BBC8"}}>Name: </span>
                                )}
                                {nodeInfo?.name ?? NULL_VALUE}
                            </div>
                        </div>
                        <div style={{display: "flex", alignItems: "center"}}>
                            <span className={styles.container_round} />
                            <div style={{marginLeft: 10}}>
                                {nodeInfo?.nodeType === "switch" ? (
                                    <span style={{color: "#B3BBC8"}}>IP/PORT: </span>
                                ) : (
                                    <span style={{color: "#B3BBC8"}}>IP: </span>
                                )}
                                <span> {nodeInfo?.ne_id ?? NULL_VALUE}</span>
                            </div>
                        </div>
                        <div style={{display: "flex", alignItems: "center"}}>
                            <span className={styles.container_round} />
                            <div style={{marginLeft: 10}}>
                                {nodeInfo?.nodeType === "switch" ? (
                                    <>
                                        <span style={{color: "#B3BBC8"}}>Reachable:</span>
                                        <span>{nodeInfo.runState === 1 ? "Connected" : "Disconnected"}</span>
                                    </>
                                ) : (
                                    <span style={{color: "#B3BBC8"}}>
                                        Type: {NE_TYPE_CONFIG[parseInt(nodeInfo?.type)]}
                                    </span>
                                )}
                            </div>
                        </div>
                    </div>
                    <Button className={styles.container_button} type="primary">
                        <span
                            style={{color: "#fff"}}
                            onClick={async () => {
                                if (nodeInfo?.nodeType === "switch") {
                                    dispatch(
                                        setSelectedItem({
                                            id: `switch:${nodeInfo?.host}`,
                                            value: {
                                                group: "switchRoot",
                                                type: "switch",
                                                nodeType: "switch_ne"
                                            }
                                        })
                                    );
                                } else if (nodeInfo?.type === "6") {
                                    dispatch(
                                        setSelectedItem({
                                            id: `dcp920:${nodeInfo.host}`,
                                            action: "selectChassisDCP920",
                                            value: {
                                                ...nodeInfo,
                                                nodeType: "otn_ne"
                                            }
                                        })
                                    );
                                } else if (nodeInfo?.type === "7") {
                                    dispatch(
                                        setSelectedItem({
                                            id: `fmt:${nodeInfo.host}`,
                                            action: "selectChassisFMT",
                                            value: {
                                                ...nodeInfo,
                                                nodeType: "otn_ne"
                                            }
                                        })
                                    );
                                } else if (nodeInfo?.type === "8") {
                                    dispatch(
                                        setSelectedItem({
                                            id: `dcs:${nodeInfo.host}`,
                                            action: "selectChassisDCS",
                                            value: {
                                                ...nodeInfo,
                                                nodeType: "otn_ne"
                                            }
                                        })
                                    );
                                } else if (nodeInfo?.type === "9") {
                                    dispatch(
                                        setSelectedItem({
                                            id: `m6200:${nodeInfo.host}`,
                                            action: "selectChassisM6200",
                                            value: {
                                                ...nodeInfo,
                                                nodeType: "otn_ne"
                                            }
                                        })
                                    );
                                } else {
                                    const rs = await objectGet("", {
                                        DBKey: `config:ne:${nodeInfo?.ne_id}`,
                                        security: false
                                    });
                                    if (rs.apiResult === "fail") {
                                        return;
                                    }
                                    const data = rs.documents[0];
                                    dispatch(
                                        setSelectedItem({
                                            id: data.id,
                                            action: "selectChassis",
                                            value: {
                                                ...data.value,
                                                nodeType: data.value.type === "5" ? "otn_ne" : "switch_ne"
                                            }
                                        })
                                    );
                                }
                            }}
                        >
                            More Information
                        </span>
                    </Button>
                </div>
            )}
        </div>
    );
};

export default CommonView;
