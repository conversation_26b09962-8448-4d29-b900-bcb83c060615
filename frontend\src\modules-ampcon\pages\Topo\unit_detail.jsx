import React, {useEffect, useState, useRef} from "react";
import {Space, Button, Form, Card, Input, Tabs, Select, message} from "antd";
import Icon from "@ant-design/icons";
import {addGreenSvg, deleteGreySvg, backUpGraySvg} from "@/utils/common/iconSvg";
import topoStyle from "@/modules-ampcon/pages/Topo/unit.module.scss";
import {confirmModalAction} from "@/modules-ampcon/components/custom_modal";
import UnitTopo from "@/modules-ampcon/pages/Topo/unit_topo";
import {useLocation, useNavigate} from "react-router-dom";
import {saveUnitInfo} from "@/modules-ampcon/apis/unit_api";

// class LeafUnit extends Array {
//     toSpliced(index, delIndex, ...arg) {
//         return this.constructor(...Array.prototype.toSpliced.call(this, index, delIndex, ...arg));
//     }
// }
// class AccessUnit extends Array {
//     toSpliced(index, delIndex, ...arg) {
//         return this.constructor(...Array.prototype.toSpliced.call(this, index, delIndex, ...arg));
//     }
// }

const UnitForm = ({unitType, subForm, editDisabled}) => {
    // const [formData, setFormData] = useState();
    const formItemCSS = {
        width: "280px",
        height: "62px",
        marginBottom: "16px"
    };
    const fieldName = {LeafUnit: "leaf", AccessUnit: "access"}[unitType];
    const [formItemNames, formItemLabels] = [
        {
            LeafUnit: ["name", "strategy", "mlag_peerlink_vlanid", "mlag_vlanid", "l3_interface"],
            AccessUnit: ["name", "link_description", "leaf", "access_method", "peer_leaf"]
        }[unitType],
        {
            LeafUnit: ["name", "Leaf Strategy", "Mlag Peer-Link VLAN ID", "Mlag VLAN ID", "L3 Peer-link Interface"],
            AccessUnit: ["name", "Link Description", "Leaf", "Access Method", "Peer Leaf"]
        }[unitType]
    ];
    const formRules = {
        LeafUnit: [
            [
                {
                    required: true,
                    message: "Please input your topology name!"
                },
                {
                    max: 256,
                    message: "Enter a maximum of 256 characters"
                }
            ],
            [],
            [
                {
                    required: true,
                    message: "Please input your Peer-Link VLAN ID!"
                }
            ],
            [
                {
                    required: true,
                    message: "Please input your Mlag VLAN ID!"
                }
            ],
            [
                {
                    required: true,
                    message: "Please input your Peer-Link Interface!"
                }
            ]
        ],
        AccessUnit: [
            [
                {
                    required: true,
                    message: "Please input your topology name!"
                },
                {
                    max: 256,
                    message: "Enter a maximum of 256 characters"
                }
            ],
            [],
            [],
            [],
            []
        ]
    }[unitType];
    const initUnitData = {
        LeafUnit: {
            // key: crypto.randomUUID(),
            name: "",
            strategy: "", // none || mlag
            mlag_peerlink_vlanid: "", // 多个vlan用分号隔开
            mlag_vlanid: "",
            l3_interface: ""
        },
        AccessUnit: {
            // key: crypto.randomUUID(),
            name: "",
            link_description: "",
            leaf: "",
            access_method: "", // single|| dual
            peer_leaf: "" // None || leaf_node_name
        }
    }[unitType];
    const addItem = () => {
        const fieldValue = subForm.getFieldValue(fieldName) || [];
        subForm.setFieldValue(fieldName, [...fieldValue, initUnitData]);
    };
    const copyItem = index => {
        const fieldValue = subForm.getFieldValue(fieldName);
        subForm.setFieldValue(fieldName, [...fieldValue, fieldValue[index]]);
    };
    const delItem = index => {
        const fieldValue = subForm.getFieldValue(fieldName);
        subForm.setFieldValue(fieldName, fieldValue.toSpliced(index, 1));
    };
    return (
        <div>
            <div style={{height: "280px", overflowY: "auto"}}>
                {/* {formData.map((item, index) => ( */}
                <Form
                    // key={item.key}
                    form={subForm}
                    layout="vertical"
                    className="label-wrap"
                    style={{
                        display: "flex",
                        justifyContent: "space-evenly",
                        flexFlow: "wrap",
                        border: "1px dashed #DCDCDC",
                        borderRadius: "4px",
                        width: "632px",
                        paddingBottom: "16px",
                        marginTop: "16px"
                    }}
                    disabled={editDisabled}
                >
                    <Form.List name={{LeafUnit: "leaf", AccessUnit: "access"}[unitType]}>
                        {fields => (
                            <>
                                {fields.map(({key, name, fieldKey, ...restField}, index) => (
                                    <div key={key} style={{display: "contents"}}>
                                        <h3
                                            style={{
                                                width: "100%",
                                                padding: "24px",
                                                margin: "0px",
                                                display: "flex",
                                                justifyContent: "space-between"
                                            }}
                                        >
                                            <span>
                                                {
                                                    {
                                                        LeafUnit: "Leaf",
                                                        AccessUnit: "Access"
                                                    }[unitType]
                                                }
                                            </span>
                                            <span>
                                                <Icon
                                                    component={backUpGraySvg}
                                                    onClick={() => {
                                                        copyItem(index);
                                                    }}
                                                />
                                                <Icon
                                                    component={deleteGreySvg}
                                                    onClick={() => delItem(index)}
                                                    style={{marginLeft: "16px"}}
                                                />
                                            </span>
                                        </h3>
                                        <Form.Item
                                            style={formItemCSS}
                                            name={[name, formItemNames[0]]}
                                            label={formItemLabels[0]}
                                            rules={formRules[0]}
                                            initialValue=""
                                            layout="vertical"
                                        >
                                            <Input style={{width: "280px"}} />
                                        </Form.Item>
                                        <Form.Item
                                            name={[name, formItemNames[1]]}
                                            label={formItemLabels[1]}
                                            rules={formRules[1]}
                                            layout="vertical"
                                            style={formItemCSS}
                                        >
                                            {
                                                {
                                                    LeafUnit: <Input style={{width: "280px"}} />,
                                                    AccessUnit: (
                                                        <Select
                                                            style={{width: "280px"}}
                                                            options={[
                                                                {value: "None", label: "None"},
                                                                {value: "mlag", label: "mlag"}
                                                            ]}
                                                        />
                                                    )
                                                }[unitType]
                                            }
                                        </Form.Item>
                                        <Form.Item
                                            name={[name, formItemNames[2]]}
                                            label={formItemLabels[2]}
                                            rules={formRules[2]}
                                            layout="vertical"
                                            style={formItemCSS}
                                        >
                                            <Input style={{width: "280px"}} />
                                        </Form.Item>
                                        <Form.Item
                                            name={[name, formItemNames[3]]}
                                            label={formItemLabels[3]}
                                            rules={formRules[3]}
                                            layout="vertical"
                                            style={formItemCSS}
                                        >
                                            <Input style={{width: "280px"}} />
                                        </Form.Item>
                                        <Form.Item
                                            name={[name, formItemNames[4]]}
                                            label={formItemLabels[4]}
                                            rules={formRules[4]}
                                            layout="vertical"
                                            style={(() => {
                                                return {
                                                    ...formItemCSS,
                                                    paddingLeft: "24px",
                                                    flex: 1
                                                };
                                            })()}
                                        >
                                            {
                                                {
                                                    LeafUnit: <Input style={{width: "280px"}} />,
                                                    AccessUnit: (
                                                        <Select
                                                            style={{width: "280px"}}
                                                            options={[
                                                                {value: "None", label: "None"},
                                                                {value: "mlag", label: "mlag"}
                                                            ]}
                                                        />
                                                    )
                                                }[unitType]
                                            }
                                        </Form.Item>
                                    </div>
                                ))}
                            </>
                        )}
                    </Form.List>
                </Form>
                {/* ))} */}
            </div>
            <Button
                style={{
                    border: "1px dashed #14c9bb",
                    borderRadius: "4px",
                    width: "100%",
                    height: "48px",
                    marginTop: "16px"
                }}
                onClick={() => addItem()}
                disabled={editDisabled}
            >
                <Icon component={addGreenSvg} />
                Add
            </Button>
        </div>
    );
};

const TopoUnitDetail = () => {
    const location = useLocation();
    const navigate = useNavigate();
    const [actionType, setActionType] = useState();
    const [form] = Form.useForm();
    const [subForm] = Form.useForm();
    const [accessNodes, setAccessNodes] = useState([]);
    const [leafNodes, setLeafNodes] = useState([]);
    const [editDisabled, setEditDisabled] = useState(false);

    useEffect(() => {
        if (location.state.actionType === "View") {
            setEditDisabled(true);
        }
        setActionType(location.state.actionType);
        form.setFieldsValue(location.state.data);
        subForm.setFieldsValue({
            leaf: location?.state?.data?.unit_info?.leaf || [
                {
                    name: "",
                    strategy: "", // none || mlag
                    mlag_peerlink_vlanid: "", // 多个vlan用分号隔开
                    mlag_vlanid: "",
                    l3_interface: ""
                }
            ],
            access: location?.state?.data?.unit_info?.access || [
                {
                    name: "",
                    link_description: "",
                    leaf: "",
                    access_method: "", // single|| dual
                    peer_leaf: "" // None || leaf_node_name
                }
            ]
        });
    }, []);

    useEffect(() => {
        setLeafNodes(subForm.getFieldValue("leaf"));
        setAccessNodes(subForm.getFieldValue("access"));
    }, [subForm.getFieldValue("leaf"), subForm.getFieldValue("access")]);

    const createUnit = async values => {
        try {
            console.log(subForm.getFieldsValue(true));
            const flag = await subForm.validateFields();
            console.log(flag);
            if (flag) {
                const payload = {
                    ...values,
                    unit_info: subForm.getFieldsValue(true)
                };
                saveUnitInfo(payload).then(res => {
                    console.log(res);
                    if (res.status === 200) {
                        message.success(res.info);
                    } else {
                        message.error(res.info);
                    }
                });
            }
        } catch (err) {
            // console.log(err);
        }
    };
    return (
        <Card className={topoStyle.unitCard}>
            <Space
                size={16}
                direction="vertical"
                style={{
                    display: "flex",
                    height: "100%",
                    flexDirection: "column",
                    justifyContent: "space-between"
                }}
            >
                <div>
                    <a style={{color: "#14c9bb"}} onClick={() => navigate(-1)}>
                        Back
                    </a>
                    <h2 style={{margin: "8px 0 20px"}}>{{Add: "Create Unit", Edit: "Edit Unit"}[actionType]}</h2>
                    <h3>Basic Info</h3>
                    <Form
                        layout="horizontal"
                        labelAlign="left"
                        labelCol={{span: 5}}
                        wrapperCol={{span: 17}}
                        // labelWrap
                        className="label-wrap"
                        form={form}
                        onFinish={createUnit}
                        style={{display: "inline-flex"}}
                        disabled={editDisabled}
                    >
                        <Form.Item
                            name="name"
                            label="Unit Name"
                            rules={[
                                {required: true, message: "Please input your topology name!"},
                                {max: 32, message: "Enter a maximum of 32 characters"}
                            ]}
                            initialValue=""
                            layout="inline"
                        >
                            <Input style={{width: "280px", marginLeft: "32px"}} />
                        </Form.Item>
                        <Form.Item
                            name="description"
                            label="Description"
                            initialValue=""
                            layout="inline"
                            style={{marginLeft: "80px"}}
                        >
                            <Input style={{width: "280px", marginLeft: "32px"}} />
                        </Form.Item>
                    </Form>
                    <div style={{display: "flex", justifyContent: "space-between"}}>
                        <div style={{width: "632px"}}>
                            <Tabs
                                rootClassName={topoStyle.unitComponent}
                                items={[
                                    {
                                        key: "Leafs",
                                        label: "Leafs",
                                        children: (
                                            <UnitForm
                                                unitType="LeafUnit"
                                                subForm={subForm}
                                                editDisabled={editDisabled}
                                            />
                                        )
                                    },
                                    {
                                        key: "Access",
                                        label: "Access",
                                        children: (
                                            <UnitForm
                                                unitType="AccessUnit"
                                                subForm={subForm}
                                                editDisabled={editDisabled}
                                            />
                                        )
                                    }
                                ]}
                            />
                        </div>
                        <div style={{width: "574px", height: "auto", marginLeft: "32px"}}>
                            <h3 style={{margin: "0", lineHeight: "51px"}}>Topology</h3>
                            <div
                                style={{
                                    flex: "1",
                                    width: "100%",
                                    height: "400px",
                                    marginRight: "8px",
                                    marginLeft: "8px",
                                    borderRadius: "5px",
                                    boxShadow: "0 12px 5px -10px rgb(0 0 0 / 10%), 0 0 4px 0 rgb(0 0 0 / 10%)"
                                }}
                            >
                                <UnitTopo accessNodes={accessNodes} leafNodes={leafNodes} />
                            </div>
                        </div>
                    </div>
                </div>
                <div
                    style={{
                        width: "calc(100% + 48px)",
                        height: "68px",
                        transform: "translateX(-24px)",
                        borderTop: "1px solid #E7E7E7"
                    }}
                >
                    <div style={{position: "absolute", padding: "16px 24px", right: "0"}}>
                        <Button type="default">Cancel</Button>
                        <Button type="primary" style={{marginLeft: "16px"}} onClick={form.submit}>
                            Create
                        </Button>
                    </div>
                </div>
            </Space>
        </Card>
    );
};

export default TopoUnitDetail;
