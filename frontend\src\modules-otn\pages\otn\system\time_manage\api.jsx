import {requestAxios, netconfGetByXML} from "@/modules-otn/apis/api";

export const apiSetNTPConfig = async params => (await requestAxios("time/setNtpConfig", params)).data;
export const apiAddNTPTemplate = async params => (await requestAxios("time/addNtpTemplate", params)).data;
export const apiGetNTPTemplates = async params => (await requestAxios("time/getNtpTemplates", params)).data;
export const apiDelNTPTemplate = async params => (await requestAxios("time/delNtpTemplate", params)).data;
export const apiEditNtpTemplate = async params => (await requestAxios("time/editNtpTemplate", params)).data;

export const getDevicesTime = async () => {
    const safeNeListRs = (await requestAxios("nelist/get_safe_nelist")).data;
    const {apiResult, data} = safeNeListRs;
    if (apiResult === "fail") return;

    const formatAllNeTime = await Promise.allSettled(
        data.map(neInfo => {
            const {ne_id, state, runState, name, type} = neInfo;
            if (state === 1 && runState === 1) {
                if (type === "5") {
                    return netconfGetByXML({
                        ne_id,
                        xml: {
                            system: {
                                $: {
                                    xmlns: "http://openconfig.net/yang/system"
                                },
                                clock: {},
                                state: {},
                                ntp: {}
                            }
                        }
                    })
                        .then(res => ({ne_id, name, type, state, ...res}))
                        .catch(e => {
                            // eslint-disable-next-line no-console
                            console.log(e.message);
                        });
                }
            }
            return {ne_id, name, type, state};
        })
    );
    return formatAllNeTime
        .filter(({status, value}) => status === "fulfilled" && value)
        .map(formatAllNeTimeItem => {
            const {name, ne_id, system, type, state} = formatAllNeTimeItem.value;
            const currentDatetime = system?.state?.["current-datetime"];
            const timezone = system?.clock?.state?.["timezone-name"];
            const ntp = system?.ntp;
            return {
                key: ne_id,
                ne_id,
                name,
                ntp,
                type,
                state,
                "current-datetime": currentDatetime,
                "timezone-name": timezone
            };
        });
};
