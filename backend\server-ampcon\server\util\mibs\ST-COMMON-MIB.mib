    ST-COMMON-MIB DEFINITIONS ::= BEGIN
 
        IMPORTS
            usmUserName            
                FROM SNMP-USER-BASED-SM-MIB            
            TimeTicks, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>teger32, Unsigned32, Counter32, 
            OBJECT-TYPE, MODULE-IDENTITY, NOTIFICATION-TYPE            
                FROM SNMPv2-SMI            
            DisplayString, MacA<PERSON><PERSON>, RowStatus, VariablePointer, TruthValue, 
            DateAnd<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TEXTUAL-CONVENTION            
                FROM SNMPv2-TC            
            enterpriseProducts            
                FROM ST-ROOT-MIB;
    
    
        stCommon MODULE-IDENTITY 
            LAST-UPDATED "201707101130Z"        -- July 10, 2017 at 11:30 GMT
            ORGANIZATION 
                ""
            CONTACT-INFO 
                ""
            DESCRIPTION 
                "ISSUE 12:
                - add pumpTempAbnormal(1903) alarm"
            REVISION "201801021048Z"        -- January 02, 2018 at 10:48 GMT
            DESCRIPTION 
                "ISSUE 12:
                - add sysLogTargetAddrTable"
            ::= { enterpriseProducts 10 }

        
    
--
-- Textual conventions
--
    
        EventCategory ::= TEXTUAL-CONVENTION
            STATUS current
            DESCRIPTION 
                "Category of event used for trap counters."
            SYNTAX INTEGER
                {
                default(0),
                currentAlarms(1),
                fileService(2),
                performance(3),
                configurationOthers(4),
                dcn(5),
                configurationNEAndShelf(6),
                configurationCards(7),
                configurationPorts(8),
                configurationConnections(9),
                configurationTransponderLines(10),
                configurationTransponderClients(11),
                configurationOptical(12),
                configurationChannelMonitoring(13),
                security(14),
                layer2Switch(15),
                securityEvents(16),
                gmpls(17),
                mplstp(18),
                configurationCardProtection(19)
                }

        TypeOfChange ::= TEXTUAL-CONVENTION
            STATUS current
            DESCRIPTION 
                "Indicates the type of an event which notifies a configuration change."
            SYNTAX INTEGER
                {
                entryModified(2),
                entryAdded(1),
                entryRemoved(3)
                }

--          OLT's AlarmCode: 1-150 (1-50: Equipment Alam; 51-100: PON Alarm; 101-150: UpLink Alarm);
-- ONU's AlarmCode: 200-400 (201-250: Equipment Alam; 251-300: PON Alarm; 301-350: UNI Alarm; 351-400: SubPort Alarm);
        AlarmCode ::= TEXTUAL-CONVENTION
            STATUS current
            DESCRIPTION 
                "alarm code definition."
            SYNTAX INTEGER
                {
                olt-pon-los(101),
                olt-eth-los(102),
                onu-off(201),
                onu-equip(202),
                onu-power(203),
                onu-bat-missing(204),
                onu-fail(205),
                onu-bat-low(206),
                onu-physical-instusion(207),
                onu-selftest-fail(208),
                onu-dying-gasp(209),
                onu-temp-yellow(210),
                onu-temp-red(211),
                onu-volt-yellow(212),
                onu-volt-red(213),
                onu-menual-poweroff(214),
                onu-inv-image(215),
                onu-pse-overload-yellow(216),
                onu-pse-overload-red(217),
                onu-pon-low-recv-optpower(251),
                onu-pon-high-recv-optpower(252),
                onu-pon-sf(253),
                onu-pon-sd(254),
                onu-pon-low-trans-optpower(255),
                onu-pon-high-trans-optpower(256),
                onu-pon-laser-bias-current(257),
                onu-uni-lan-los(301),
                onu-uni-fcs-err(302),
                onu-uni-excessive-collision-count(303),
                onu-uni-late-collision-count(304),
                onu-uni-frame-toolong(305),
                onu-uni-recvbuf-overflow(306),
                onu-uni-transbuf-overflow(307),
                onu-uni-single-collision-frame-count(308),
                onu-uni-multi-collision-frame-count(309),
                onu-uni-sqe-count(310),
                onu-uni-defer-trans-count(311),
                onu-uni-internal-mac-transerr-count(312),
                onu-uni-carrier-sense-err-count(313),
                onu-uni-alignment-err-count(314),
                onu-uni-internal-mac-recvserr-count(315),
                onu-uni-pppoe-filter-frame-count(316),
                onu-uni-dropevent(317),
                onu-uni-undersize-pack(318),
                onu-uni-fragment(319),
                onu-uni-jabber(320),
                onu-subport-lost-pack(351),
                onu-subport-misinsert-pack(352),
                onu-subport-impaired-block(353),
                fcLos(1000),
                fcLosSync(1001),
                fcCsfOpu(1002),
                fcProtna(1003),
                fcLpbkNe(1004),
                fcLpbkFe(1005),
                etynLos(1050),
                etynLosSync(1051),
                etynLf(1052),
                etynRf(1053),
                etynCsfLosGfp(1054),
                etynCsfLosSyncGfp(1055),
                etynCsfFdiGfp(1056),
                etynCsfRdiGfp(1057),
                etynLofdGfp(1058),
                etynPlmGfp(1059),
                etynCsfOpu(1060),
                etynProtNa(1061),
                etynLpbkNe(1062),
                etynLpbkFe(1063),
                ocnstmnLos(1100),
                ocnstmnGenAisIngress(1101),
                ocnstmnLofIngress(1102),
                ocnstmnAislMsaisIngress(1103),
                ocnstmnRfilMsrfi(1104),
                ocnstmnTimlRstim(1105),
                ocnstmnCsfOpu(1106),
                ocnstmnGenAisEgress(1107),
                ocnstmnLofEgress(1108),
                ocnstmnAislMsaisEgress(1109),
                ocnstmnProtna(1110),
                ocnstmnLpbkNe(1111),
                ocnstmnLpbkFe(1112),
                prbsLockLos(1115),
                ochLos(1150),
                otuLos(1200),
                otuLof(1201),
                otuLom(1202),
                otuTim(1203),
                otuBdi(1204),
                otuDeg(1205),
                otuAis(1206),
                otuProtna(1207),
                otuLpbkNe(1208),
                otuLpbkFe(1209),
                otuLol(1210),
                oduDeg(1250),
                oduLck(1251),
                oduOci(1252),
                oduAis(1253),
                oduBdi(1254),
                oduTim(1255),
                oduPlm(1256),
                oduLoomfi(1257),
                oduMsim(1258),
                oduLofLom(1259),
                oduLtc(1260),
                otussf(1261),
                odussf(1262),
                tcaBBE(1270),
                tcaES(1271),
                tcaSES(1272),
                tcaUAS(1273),
                tcaPreFEC(1274),
                tcaRxOPowerHigh(1275),
                tcaRxOPowerLow(1276),
                tcaChkSeqErr(1277),
                tcaAlignErr(1278),
                tcaJabber(1279),
                tcaFragment(1280),
                tcaDropEvts(1281),
                tcaOverSize(1282),
                tcaUnderSize(1283),
                tcaCollision(1284),
                tcapostfec(1285),
                tcaRxErrBytes(1288),
                tcaTxErrBytes(1289),
                tcaTxOPowerHigh(1290),
                tcaTxOPowerLow(1291),
                tcaTempLow(1292),
                tcaTempHigh(1293),
                eqptMissng(1300),
                eqptFail(1301),
                eqptDeg(1302),
                eqptMismatch(1303),
                eqptPowerSupplyIssue(1304),
                eqptTempMajor(1305),
                eqptTempCritical(1306),
                eqptCommFail(1307),
                eqptLatchOpen(1308),
                eqptFanMajor(1309),
                eqptFanCritical(1310),
                eqptLowTempMajor(1311),
                eqptLowTempCritical(1312),
                eqptSdCardMountFail(1313),
                pluggableMissing(1350),
                pluggableFail(1351),
                pluggableMismatch(1352),
                pluggableTxFail(1353),
                pluggableRxPowerTooHigh(1354),
                pluggableRxPowerTooLow(1355),
                pluggableTxPowerTooHigh(1356),
                pluggableTxPowerTooLow(1357),
                pluggableBiasCurrentTooHigh(1358),
                pluggableBiasCurrentTooLow(1359),
                pluggableTempTooHigh(1360),
                pluggableTempTooLow(1361),
                pluggableVccTooHigh(1362),
                pluggableVccTooLow(1363),
                pluggableRx1PowerTooHigh(1374),
                pluggableRx1PowerTooLow(1375),
                pluggableRx2PowerTooHigh(1376),
                pluggableRx2PowerTooLow(1377),
                ponLos(1380),
                ponDsErr(1381),
                remoteDeviceDyingGasp(1390),
                fopNr(1400),
                fopPm(1401),
                swMismatch(1450),
                swDownloadFail(1451),
                swMibMismatch(1452),
                swMibFail(1453),
                swStorgeFull(1454),
                secPasswordExpired(1500),
                secLoginExceeded(1501),
                shelfMDI1(1600),
                shelfMDI2(1601),
                shelfMDI3(1602),
                shelfMDI4(1603),
                shlefTempMajor(1604),
                shelfTempCritical(1605),
                thirdPartyModuleAbsent(1700),
                thirdPartyModuleFail(1701),
                thirdParthModuleTemp1TooHigh(1702),
                thirdParthModuleTemp1TooLow(1703),
                thirdParthModuleTemp2TooHigh(1704),
                thirdParthModuleTemp2TooLow(1705),
                thirdParthModuleTemp3TooHigh(1706),
                thirdParthModuleTemp3TooLow(1707),
                thirdParthModuleCommFail(1708),
                opticalLos(1800),
                opticalBelowThreshold(1801),
                opticalSwitch(1802),
                remoteloss(1803),
                snr(1804),
                heartLoss(1805),
                opticalRx1Los(1806),
                opticalRx2Los(1807),
                edfaRxLOS(1900),
                edfaTXLOS(1901),
                edfaCurrentTooHigh(1902),
                pumpTempAbnormal(1903),
                ponRemoteLos(1904),
                ramanRFLTooHigh(1905),
                ramanRxLOS(1906),
                ramanCurrentTooHigh(1907),
                linkFailure(2000),
                pllInclkLos(2200),
                pllXoLos(2201),
                pllLol(2202),
                pllInclkOof(2203),
                ponPllLol(2204),
                phyQpllLol(2205),
                phyAbnormal(2206),
                opu1FifoEmpty(2207),
                opu1FifoUnderflow(2208),
                opu1FifoFull(2209),
                opu1FifoOverflow(2210),
                onuOffline(2211),
                powerDiffBad(2212),
                powerOverThr(2213),
                localLOS(2228),
                localAIS(2229),
                remoteLOS(2230),
                remoteAIS(2231)
                }

        AlarmPathType ::= TEXTUAL-CONVENTION
            STATUS current
            DESCRIPTION 
                "Alarm Path Type"
            SYNTAX INTEGER
                {
                ne(1),
                slot(2),
                port(3),
                tp(4),
                pon(5),
                onu(6),
                eqpt(7),
                etyn(8),
                stmn(9),
                och(10),
                otuk(11),
                oduk(12),
                pluggable(13),
                lane(14),
                vps(15),
                olp(16),
                oa(17),
                qos(18),
                opm(19),
                stat(21)
                }

        AlarmState ::= TEXTUAL-CONVENTION
            STATUS current
            DESCRIPTION 
                "Common alarm state."
            SYNTAX INTEGER
                {
                set(1),
                clear(2)
                }

--          table entry or scalar modified
-- new table entry added
-- table entry removed
        CardType ::= TEXTUAL-CONVENTION
            STATUS current
            DESCRIPTION 
                "Specifies the type of card provisioned for use in this slot. Empty specifies no rovisioning."
            SYNTAX INTEGER
                {
                empty(0),
                m1H101U(1),
                t20X1U(2),
                m2X161U(3),
                t10X1U(4),
                M6500-M2X16(6),
                m2X16A(7),
                t10X(8),
                M6500-T20(10),
                m1H21U(13),
                t2H1U(14),
                m1H5(15),
                m1H51U(16),
                g161U(20),
                g081U(21),
                g16e1U(22),
                g08e1U(23),
                e161U(24),
                e081U(26),
                g16(27),
                g08(28),
                e16(29),
                e08(30),
                pae(41),
                paeGponOlt(42),
                paeGponOnu(43),
                paeEpon1Olt(44),
                paeEpon1Onu(45),
                paeEpon2Olt(46),
                paeEpon2Onu(47),
                pae1uGponOlt(50),
                pae1uGponOnu(51),
                pae1uEpon1Olt(52),
                pae1uEpon1Onu(53),
                pae1uEpon2Olt(54),
                pae1uEpon2Onu(55),
                paeRpc6480GponOlt(60),
                paeRpc6480Epon1Olt(61),
                paeRpc6480Epon2Olt(62),
                omu2(70),
                M6200-RB(71),
                omu48(72),
                dcm1(73),
                M6200-DCM40(74),
                ssa(75),
                ssm(76),
                sr2(77),
                oa1(78),
                oa21(79),
                otu10(80),
                oa22(81),
                sr22(82),
                ssa2(83),
                olp1to1(84),
                M6200-OLP2(85),
                otu10AM2(86),
                M6200-OEO10G(87),
                soas(88),
                oa4(89),
                omd40(90),
                omd80(91),
                omd40e(92),
                omd80e(93),
                M6500-EDFA(94),
                oa26800(95),
                M6500-OLP2(96),
                omd6800(97),
                M6500-5UNMU(101),
                sxc4(102),
                upl4(103),
                M6200-NMU(104),
                otnSi(105),
                stnBSc(106),
                M6500-NMU(107),
                M6500-TMXP5(108),
                M6500-TMXP5(109),
                M6500-TMXP5(110),
                m2h20-100g1u(111),
                m2h20-20x10g1u(112),
                m2h20-100g-10x10g1u(113),
                M6500-TMXP5(114),
                m2h20-4x40g-4x10g1u(115),
                t4h-4x25gdwdm(116),
                t4h-dco(117),
                t4h-greycfp(118),
                sax(120),
                otu4h(121),
                ssa2AM2(122),
                M6200-OEO100G(123),
                otu6FA(124),
                otu10A(125),
                M6500-TMXP5(126),
                m2h20-100g-2x40g1u(127),
                m2h20-2x40g-12x10g1u(129),
                M6200-D2160M(130),
                osc(131),
                otu6h(132),
                ssah(133),
                ba-16t(134),
                ba-64t(135),
                M6200-RA(136),
                bypassGbp(137),
                otu4x(138),
                M6800-TSP16(140),
                soa(141),
                soa6(142),
                sah(143),
                oa26(144),
                edfa(145),
                M6200-OLP1Z(146),
                M6200-OLP1(147),
                opm(148),
                osw(149),
                stnSc5u(150),
                otnPwrAc1U(201),
                otnPwrDc1U(202),
                pwrDc10U(203),
                M6500-2UPSM(204),
                M6500-2UPSM(205),
                otnpwrDce1U(206),
                stnBDc(207),
                stnBAc(208),
                M6500-2UPSM(209),
                M6500-2UPSM(210),
                M6500-5UPSM(211),
                M6500-5UPSM(212),
                M6500-5UPSM(213),
                M6500-5UPSM(214),
                M6800-1UPSM(215),
                dciDC1U(216),
                otnFan1U(300),
                fan10U(301),
                fanOlt10U(302),
                M6500-2UFAN(303),
                M6500-2UFAN(304),
                M6500-5UFAN(305),
                stnIAFan1U(306),
                M6500-5UFAN(307),
                M6800-1UFAN(308),
                stnFan3U(309),
                M6200-EDFA(500),
                M6200-SOA(501),
                oa23n(502),
                oa24n(503),
                oa25n(504),
                oa26n(505),
                soan(510),
                soasn(511),
                soas2n(512),
                pmu04(517),
                pmu08(518),
                oeo-64t(519),
                sah1(520),
                sah2(521),
                sax1(522),
                sax2(523),
                otu2ha(530),
                omp06(535),
                omp12(536),
                omp18(537),
                omu96(540),
                odu96(541),
                otu6(545),
                e1(546),
                otdr(552),
                pmu4(553),
                pmu8(554),
                pmu16(555),
                osu8(556),
                osu16(557),
                osu32(558),
                olpTbp(559),
                sr2-LC(560),
                opm-osw(561),
                otu2h8(565),
                otu2dh(567),
                otu1h4(568),
                omp06-I(569),
                omp06-II(570),
                omp12-I(571),
                omp12-II(572),
                omp18-I(573),
                omp18-II(574),
                otu10ce(631)
                }

--   default
-- STN6100
-- SAN3700
-- STN6200
-- STN6200B
-- STN6800
-- Common
-- STN6200
-- Power
-- Fan
        StPortMode ::= TEXTUAL-CONVENTION
            STATUS current
            DESCRIPTION 
                " "
            SYNTAX INTEGER
                {
                none(0),
                ge(1),
                gettt(2),
                xgeBMP(3),
                xgeGFPF(4),
                xgeGFPFextp(5),
                ge100(6),
                feCbr(7),
                stm1GMP(8),
                oc3GMP(9),
                stm4GMP(10),
                oc12GMP(11),
                stm16AMP(12),
                oc48AMP(13),
                stm64AMP(14),
                oc192AMP(15),
                fc1(16),
                fc2(17),
                fc4(18),
                fc8(19),
                fc10(20),
                otu1(21),
                ochOSOtu1(22),
                otu2(23),
                ochOSOtu2(24),
                otu2e(25),
                ochOSOtu2e(26),
                otu3(27),
                ochOSOtu3(28),
                otu4(29),
                ochOSOtu4(30),
                otu0ll(31),
                otuc2(32),
                ochOSOtuc2(33),
                otuc4(34),
                ochOSOtuc4(35),
                och4(36),
                stm16BMP(54),
                oc48BMP(55),
                stm64BMP(56),
                oc192BMP(57),
                ge40GMP(58),
                ge40GFPF(59),
                ge100GMP(60),
                ge100GFPF(61),
                oltge(100),
                xge(101),
                xgeWan(102),
                gpon(110),
                epon(111),
                gponOlt(112),
                gponOnu(113),
                eponOlt(114),
                eponOnu(115),
                ssamp(130),
                ssama(131),
                vps(132),
                olp(133),
                oa(134),
                oap(135),
                f10G2G51G25(136),
                f100G40G(137),
                f40G(138),
                f100Gdwdm(139),
                f100G(140),
                f25G(141),
                mgmto(150),
                mgmte(151),
                bpLanE(152),
                bpWanE(153),
                osw(166),
                opm(201)
                }

        StAvailabilityState ::= TEXTUAL-CONVENTION
            STATUS current
            DESCRIPTION 
                " "
            SYNTAX INTEGER
                {
                normal(0),
                failed(1),
                degraded(2),
                notInstalled(3),
                mismatch(4),
                loopback(5),
                remotefailed(6),
                latchopen(7),
                intest(8),
                empty(9),
                notconnected(10),
                reserved(11),
                unsync(12)
                }

        AlarmSeverity ::= TEXTUAL-CONVENTION
            STATUS current
            DESCRIPTION 
                "Common alarm severity definition."
            SYNTAX INTEGER
                {
                notInAlarm(0),
                warning(1),
                minor(2),
                major(3),
                critical(4),
                indeterminate(5)
                }

        AlarmType ::= TEXTUAL-CONVENTION
            STATUS current
            DESCRIPTION 
                "Common alarm category definition."
            SYNTAX INTEGER
                {
                communication(1),
                qos(2),
                equipment(3),
                processerror(4),
                environment(5),
                security(6),
                control-plane(7)
                }

        AlarmSummary ::= TEXTUAL-CONVENTION
            STATUS current
            DESCRIPTION 
                "Set of bits, each indicating presence of any alarm of a particular severity."
            SYNTAX INTEGER
                {
                none(0),
                critical(1),
                major(2),
                minor(3)
                }

        PresenceState ::= TEXTUAL-CONVENTION
            STATUS current
            DESCRIPTION 
                "Presence state."
            SYNTAX INTEGER
                {
                required(1),
                planned(2),
                autoInService(3)
                }

        EquipmentState ::= TEXTUAL-CONVENTION
            STATUS current
            DESCRIPTION 
                "TransConnect equipment state."
            SYNTAX INTEGER
                {
                undefined(0),
                spare(1),
                reserved(2),
                inService(3),
                outOfService(4),
                oOSMaintenance(5),
                planned(6),
                pending(7),
                pendingForMaintenance(8),
                pendingReserved(9),
                spareForMaintenance(10)
                }

--          Throughout the document FFU means "for future use".
-- ----------------------------------------------------------------------
--  Textual Conventions
-- ----------------------------------------------------------------------
        LinkDirection ::= TEXTUAL-CONVENTION
            STATUS current
            DESCRIPTION 
                "Direction of DWDM line interface."
            SYNTAX Unsigned32

        CommissioningStatusShelf ::= TEXTUAL-CONVENTION
            STATUS current
            DESCRIPTION 
                "Indicates status of shelf with respect to match with required equipping."
            SYNTAX INTEGER
                {
                notCommissioned(0),
                commissioned(1)
                }

        ShelfNumberWithinBay ::= TEXTUAL-CONVENTION
            STATUS current
            DESCRIPTION 
                "Number of shelf within bay."
            SYNTAX Unsigned32 (0..8)

        EquipmentProvisioningMode ::= TEXTUAL-CONVENTION
            STATUS current
            DESCRIPTION 
                "Indicates whether shelf or slot can be provisioned using SNMP."
            SYNTAX INTEGER
                {
                locked(0),
                free(1)
                }

        GenericOperation ::= TEXTUAL-CONVENTION
            STATUS current
            DESCRIPTION 
                "Used by the SNMP manager in order to invoke a specific operation.
                Setting the value to 'invoked' means to trigger the operation.
                After successful execution of the operation the value will be 'idle'.
                A failure of the operation is either indicated by an immediate error response on the attempt to set the value to 'invoked' or (in case that the agent must respond before knowing about success or failure) by the value 'failed' after having determined that the operation has failed. It depends on the kind of operation and the timing constraints of the internal procedures in order to execute the operation whether the value 'failed' or an immediate error response is used. In case of an immediate error response the value 'idle' is immediately taken after execution of the operation."
            SYNTAX INTEGER
                {
                idle(0),
                invoked(1),
                failed(2)
                }

        FilterMaintenanceInterval ::= TEXTUAL-CONVENTION
            STATUS current
            DESCRIPTION 
                "Indicates conditions for filter endurance."
            SYNTAX INTEGER
                {
                interval12Months(0),
                interval10Months(1),
                interval8Months(2),
                interval6Months(3)
                }

        EnableSwitch ::= TEXTUAL-CONVENTION
            STATUS current
            DESCRIPTION 
                "Parameter to disable or enable a certain functionality or to indicate availability of a certain functionality."
            SYNTAX INTEGER
                {
                disabled(0),
                enabled(1)
                }

        OperationalState ::= TEXTUAL-CONVENTION
            STATUS current
            DESCRIPTION 
                "Indicates whether a resource is able to provide service."
            SYNTAX INTEGER
                {
                enabled(0),
                disabled(1)
                }

        AdministrativeState ::= TEXTUAL-CONVENTION
            STATUS current
            DESCRIPTION 
                "The administrative state represents permission to use or prohibition against using a resource, imposed through the management services."
            SYNTAX INTEGER
                {
                unlocked(0),
                locked(2)
                }

--          FFU: shuttingDown (1),
        CardMode ::= TEXTUAL-CONVENTION
            STATUS current
            DESCRIPTION 
                "Indicates working mode of transponder, filter or WSS card"
            SYNTAX INTEGER
                {
                notApplicable(0),
                transponder(1),
                regenerator(2),
                mux(3),
                demux(4),
                dsTributary(5),
                tributary(6),
                ds5(7),
                ds(8),
                add(9),
                drop(10),
                aggregator(11),
                notAssigned(12),
                forward(13),
                backward(14)
                }

        ResetOperation ::= TEXTUAL-CONVENTION
            STATUS current
            DESCRIPTION 
                "Used by the SNMP manager in order to trigger a particular reset operation.
                Once the operation is started the value will be 'idle'."
            SYNTAX INTEGER
                {
                idle(0),
                cold(1),
                warm(2)
                }

        CardUploadOperation ::= TEXTUAL-CONVENTION
            STATUS current
            DESCRIPTION 
                "Used by the SNMP manager in order to invoke a card related upload operation.
                Setting the value to the type of file to upload means to trigger the upload procedure for this file.
                The 'idle' value is used for responses to retrieval operations."
            SYNTAX INTEGER
                {
                idle(0),
                pmHistory15min(1),
                pmHistory24h(2),
                diagnosticData(3),
                pmHistoryData(4)
                }

        PMNumberOfRecords ::= TEXTUAL-CONVENTION
            STATUS current
            DESCRIPTION 
                "Number of performance records."
            SYNTAX Unsigned32 (0..32)

        PMParameterName ::= TEXTUAL-CONVENTION
            STATUS current
            DESCRIPTION 
                "Indicates performance parameter."
            SYNTAX INTEGER
                {
                notApplicable(0),
                maximumBitErrorRate(1),
                averageBitErrorRate(2),
                codingViolations(3),
                backgroundBlockErrors(4),
                erroredSeconds(5),
                severelyErroredSeconds(6),
                severelyErroredFramingSeconds(7),
                unavailableSeconds(8),
                codingViolationsOrDisparityErrors(9),
                framesReceivedOK(10),
                octetsReceivedOK(11),
                erroredFramesReceived(12),
                framesTransmittedOK(13),
                octetsTransmittedOK(14),
                erroredFramesTransmitted(15),
                gfpErroredSuperblocks(16),
                gfpDiscardedFrames(17),
                inputPowerMin(18),
                inputPowerMax(19),
                inputPowerAverage(20),
                powerScans(21),
                lossOfSignalCount(22),
                lossOfSynchronizationCount(23),
                linkFailureCount(24),
                gfpFCSErrors(25),
                outputPowerMin(26),
                outputPowerMax(27),
                outputPowerAverage(28),
                degreeOfPolarizationMin(29),
                degreeOfPolarizationMax(30),
                degreeOfPolarizationAverage(31),
                pcsErroredBlockCounter(32),
                pcsBIPErroredBlockCounter(33),
                transcodingErrors(34),
                droppedFrames(35),
                dispersionCompensationMin(36),
                dispersionCompensationMax(37),
                dispersionCompensationAverage(38),
                inputPowerMinTimestamp(39),
                inputPowerMaxTimestamp(40),
                outputPowerMinTimestamp(41),
                outputPowerMaxTimestamp(42),
                delayMeasuredMaxTimestamp(44),
                delayMeasuredMax(45),
                delayMeasuredAverage(47),
                delayMeasuredNumberOfSnapshots(48),
                inputSpanLossMin(49),
                inputSpanLossMinTimestamp(50),
                inputSpanLossMax(51),
                inputSpanLossMaxTimestamp(52),
                inputSpanLossAverage(53),
                outputTiltMin(54),
                outputTiltMinTimestamp(55),
                outputTiltMax(56),
                outputTiltMaxTimestamp(57),
                outputTiltAverage(58),
                channelCountMin(59),
                channelCountMinTimestamp(60),
                channelCountMax(61),
                channelCountMaxTimestamp(62),
                chromaticDispersionMin(63),
                chromaticDispersionMax(64),
                chromaticDispersionMaxTimestamp(65),
                chromaticDispersionAverage(66),
                broadcastFramesReceived(67),
                multicastFramesReceived(68),
                giantFramesReceived(69),
                runtFramesReceived(70),
                differentialGroupDelayMin(71),
                differentialGroupDelayMax(72),
                differentialGroupDelayMaxTimeStamp(73),
                differentialGroupDelayAverage(74),
                electricalSignalNoiseRatioMin(75),
                electricalSignalNoiseRatioMax(76),
                electricalSignalNoiseRatioMaxTimestamp(77),
                electricalSignalNoiseRatioAverage(78),
                polarizationDependentLossMin(79),
                polarizationDependentLossMax(80),
                polarizationDependentLossMaxTimestamp(81),
                polarizationDependentLossAverage(82),
                pcsBERCount(83),
                shortFramesReceived(85),
                jabberFramesReceived(86),
                broadcastFramesTransmitted(87),
                multicastFramesTransmitted(88),
                giantFramesTransmitted(89),
                runtFramesTransmitted(90),
                shortFramesTransmitted(91),
                jabberFramesTransmitted(92),
                truncatedFrames(93)
                }

        PMLogType ::= TEXTUAL-CONVENTION
            STATUS current
            DESCRIPTION 
                "Kind of performance log."
            SYNTAX INTEGER
                {
                otn(0),
                ethernetWAN(1),
                rmon(2),
                mplsTpAll(3),
                mplsTpAC(4),
                mplsTpLSP(5),
                mplsTpPW(6)
                }

        FileTransferTypeOfProtocol ::= TEXTUAL-CONVENTION
            STATUS current
            DESCRIPTION 
                "Protocol to use for file transfer."
            SYNTAX INTEGER
                {
                ftps(0),
                ftp(1),
                sftp(2)
                }

        UsageState ::= TEXTUAL-CONVENTION
            STATUS current
            DESCRIPTION 
                "Indicates whether resource is in use."
            SYNTAX INTEGER
                {
                busy(0),
                idle(1)
                }

--          FFU: active (2)
        ResetState ::= TEXTUAL-CONVENTION
            STATUS current
            DESCRIPTION 
                "Indicates whether the card is currently executing a reset operation."
            SYNTAX INTEGER
                {
                idle(0),
                resetting(1)
                }

        PMStorageControl ::= TEXTUAL-CONVENTION
            STATUS current
            DESCRIPTION 
                "Used to trigger operations which control storage of PM records.
                The value is only set by the SNMP manager in an SNMP set operation in order to execute the operation.
                For SNMP retrieval operations the value is always 'notApplicable'."
            SYNTAX INTEGER
                {
                notApplicable(0),
                clearHistory15min(1),
                clearHistory24h(2),
                clearCurrentAndPreviousData15min(3),
                clearAllPMData(4),
                clearCurrentAndPreviousData24h(5)
                }

        HibernationMode ::= TEXTUAL-CONVENTION
            STATUS current
            DESCRIPTION 
                "Configuration of hibernation."
            SYNTAX INTEGER
                {
                alwaysRunning(0),
                auto(1)
                }

        HibernationState ::= TEXTUAL-CONVENTION
            STATUS current
            DESCRIPTION 
                "Status of hibernation."
            SYNTAX INTEGER
                {
                running(0),
                hibernating(1),
                runningForTest(2)
                }

               
    
-- 
-- Node definitions
-- 
-- ----------------------------------------------------------------------
-- MIB Structure
-- ----------------------------------------------------------------------
        commonMIB OBJECT IDENTIFIER ::= { stCommon 1 }

        
--         ----------------------------------------------------------------------
-- Common MIB
-- ----------------------------------------------------------------------
        system OBJECT IDENTIFIER ::= { commonMIB 1 }

        
        sysInfoScalars OBJECT IDENTIFIER ::= { system 1 }

        
        sysHostName OBJECT-TYPE
            SYNTAX DisplayString
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                "Host Name used in CLI prompt. Must be a-z A-Z or 0-9."
            ::= { sysInfoScalars 1 }

        
        sysHostAliasName OBJECT-TYPE
            SYNTAX DisplayString
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                "Host Alias Name used in Web managment."
            ::= { sysInfoScalars 2 }

        
        sysDescription OBJECT-TYPE
            SYNTAX DisplayString
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                "Description."
            ::= { sysInfoScalars 3 }

        
        sysSwVersion OBJECT-TYPE
            SYNTAX DisplayString
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "Description."
            ::= { sysInfoScalars 4 }

        
        sysHwVersion OBJECT-TYPE
            SYNTAX DisplayString
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "Description."
            ::= { sysInfoScalars 5 }

        
        sysSerialNum OBJECT-TYPE
            SYNTAX DisplayString
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "Description."
            ::= { sysInfoScalars 6 }

        
        sysMacPoolInitAddress OBJECT-TYPE
            SYNTAX MacAddress
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The first Mac-address in the system MAC-Address pool"
            ::= { sysInfoScalars 7 }

        
        sysMacPoolSize OBJECT-TYPE
            SYNTAX Integer32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The number of Mac-Address in the mac-address pool, 
                together with sysMacPoolInitAddress defined the mac-address
                can be used in the system."
            ::= { sysInfoScalars 8 }

        
        sysTemperatureTable OBJECT-TYPE
            SYNTAX SEQUENCE OF SysTemperatureEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "SNTP Server Table"
            ::= { system 2 }

        
        sysTemperatureEntry OBJECT-TYPE
            SYNTAX SysTemperatureEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                " "
            INDEX { sysTempMeterIndex }
            ::= { sysTemperatureTable 1 }

        
        SysTemperatureEntry ::=
            SEQUENCE { 
                sysTempMeterIndex
                    Unsigned32,
                sysTempMeterDesc
                    DisplayString,
                sysTempMeterAlarmEnable
                    INTEGER,
                sysTempMeterThreshold
                    Integer32,
                sysTempTableRowStatus
                    RowStatus
             }

        sysTempMeterIndex OBJECT-TYPE
            SYNTAX Unsigned32 (1..4294967295)
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "index of temperature meter"
            ::= { sysTemperatureEntry 1 }

        
        sysTempMeterDesc OBJECT-TYPE
            SYNTAX DisplayString
            MAX-ACCESS read-create
            STATUS current
            DESCRIPTION
                "The name of temerature meter"
            ::= { sysTemperatureEntry 2 }

        
        sysTempMeterAlarmEnable OBJECT-TYPE
            SYNTAX INTEGER
                {
                disable(0),
                enable(1)
                }
            MAX-ACCESS read-create
            STATUS current
            DESCRIPTION
                "Temperature Alarm enable/disable"
            ::= { sysTemperatureEntry 3 }

        
        sysTempMeterThreshold OBJECT-TYPE
            SYNTAX Integer32
            MAX-ACCESS read-create
            STATUS current
            DESCRIPTION
                "Temperature exceeds threshold will triger alarm when 
                sysTempMeterAlarmEnable is set enable"
            ::= { sysTemperatureEntry 4 }

        
        sysTempTableRowStatus OBJECT-TYPE
            SYNTAX RowStatus
            MAX-ACCESS read-create
            STATUS current
            DESCRIPTION
                "RowStatus"
            ::= { sysTemperatureEntry 5 }

        
--           Managment Information.
        sysMangement OBJECT IDENTIFIER ::= { commonMIB 2 }

        
        sysMangementScalars OBJECT IDENTIFIER ::= { sysMangement 1 }

        
        maxMgmtEtherPortNum OBJECT-TYPE
            SYNTAX Integer32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "Maximum Ethernet Management interfaces on the controller"
            ::= { sysMangementScalars 1 }

        
        maxMgmtVlanIfNum OBJECT-TYPE
            SYNTAX Integer32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "Maximum in-band Management Vlan interfaces "
            ::= { sysMangementScalars 2 }

        
        mgmtDefaultRouteGateway OBJECT-TYPE
            SYNTAX IpAddress
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                "Default Route for managment network "
            ::= { sysMangementScalars 3 }

        
        sysCurrentTime OBJECT-TYPE
            SYNTAX DisplayString
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                "Current time of system. using gmt time format 
                with format 'YYYY-MM-DD HH:MM:SS'"
            ::= { sysMangementScalars 4 }

        
        sysTimeZone OBJECT-TYPE
            SYNTAX DisplayString
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                "GMT Time zone . format '+ HH:MM' or '- HH:MM'"
            ::= { sysMangementScalars 5 }

        
        nodeIP OBJECT-TYPE
            SYNTAX IpAddress
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                " Node IP of the Network Element "
            ::= { sysMangementScalars 6 }

        
        defaultRtRedist OBJECT-TYPE
            SYNTAX Integer32
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                "option of default route redistribution to ospf"
            ::= { sysMangementScalars 7 }

        
        mgmtEthIfTable OBJECT-TYPE
            SYNTAX SEQUENCE OF MgmtEthIfEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "Management Ethetnet Interface Table"
            ::= { sysMangement 2 }

        
        mgmtEthIfEntry OBJECT-TYPE
            SYNTAX MgmtEthIfEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                " "
            INDEX { mgmtEthIfId }
            ::= { mgmtEthIfTable 1 }

        
        MgmtEthIfEntry ::=
            SEQUENCE { 
                mgmtEthIfId
                    Unsigned32,
                mgmtEthIfName
                    DisplayString,
                mgmtEthIfIpMode
                    INTEGER,
                mgmtEthIfIpAddr
                    IpAddress,
                mgmtEthIfIpAddrMask
                    IpAddress,
                mgmtEthIfTableRowStatus
                    RowStatus,
                mgmtEthOspfEnable
                    INTEGER,
                mgmtEthOspfAreaId
                    IpAddress
             }

        mgmtEthIfId OBJECT-TYPE
            SYNTAX Unsigned32 (1..65535)
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "Name of management interface"
            ::= { mgmtEthIfEntry 1 }

        
        mgmtEthIfName OBJECT-TYPE
            SYNTAX DisplayString (SIZE (0..20))
            MAX-ACCESS read-create
            STATUS current
            DESCRIPTION
                "Name of management interface"
            ::= { mgmtEthIfEntry 2 }

        
        mgmtEthIfIpMode OBJECT-TYPE
            SYNTAX INTEGER
                {
                disable(0),
                dhcp(1),
                manual(2)
                }
            MAX-ACCESS read-create
            STATUS current
            DESCRIPTION
                "The method of system to get managment ip address"
            ::= { mgmtEthIfEntry 3 }

        
        mgmtEthIfIpAddr OBJECT-TYPE
            SYNTAX IpAddress
            MAX-ACCESS read-create
            STATUS current
            DESCRIPTION
                "Ipv4 address of managment ethernet interface"
            ::= { mgmtEthIfEntry 4 }

        
        mgmtEthIfIpAddrMask OBJECT-TYPE
            SYNTAX IpAddress
            MAX-ACCESS read-create
            STATUS current
            DESCRIPTION
                "Ipv4 address mask "
            ::= { mgmtEthIfEntry 5 }

        
        mgmtEthIfTableRowStatus OBJECT-TYPE
            SYNTAX RowStatus
            MAX-ACCESS read-create
            STATUS current
            DESCRIPTION
                "RowStatus"
            ::= { mgmtEthIfEntry 6 }

        
        mgmtEthOspfEnable OBJECT-TYPE
            SYNTAX INTEGER
                {
                disabled(0),
                enabled(1)
                }
            MAX-ACCESS read-create
            STATUS current
            DESCRIPTION
                "MGMT Ethernet port ospf enable"
            ::= { mgmtEthIfEntry 7 }

        
        mgmtEthOspfAreaId OBJECT-TYPE
            SYNTAX IpAddress
            MAX-ACCESS read-create
            STATUS current
            DESCRIPTION
                "OSPF Area Id "
            ::= { mgmtEthIfEntry 8 }

        
        mgmtVlanTable OBJECT-TYPE
            SYNTAX SEQUENCE OF MgmtVlanEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "Management Ethetnet Interface Table"
            ::= { sysMangement 3 }

        
        mgmtVlanEntry OBJECT-TYPE
            SYNTAX MgmtVlanEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                " "
            INDEX { mgmtVlanId }
            ::= { mgmtVlanTable 1 }

        
        MgmtVlanEntry ::=
            SEQUENCE { 
                mgmtVlanId
                    Unsigned32,
                mgmtVlanIpAddr
                    IpAddress,
                mgmtVlanIpAddrMask
                    IpAddress,
                mgmtVlanTableRowStatus
                    RowStatus
             }

        mgmtVlanId OBJECT-TYPE
            SYNTAX Unsigned32 (1..4294967295)
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "vlan id that enabled in-band managment"
            ::= { mgmtVlanEntry 1 }

        
        mgmtVlanIpAddr OBJECT-TYPE
            SYNTAX IpAddress
            MAX-ACCESS read-create
            STATUS current
            DESCRIPTION
                "Ipv4 address of managment ethernet interface"
            ::= { mgmtVlanEntry 2 }

        
        mgmtVlanIpAddrMask OBJECT-TYPE
            SYNTAX IpAddress
            MAX-ACCESS read-create
            STATUS current
            DESCRIPTION
                "Ipv4 address mask "
            ::= { mgmtVlanEntry 3 }

        
        mgmtVlanTableRowStatus OBJECT-TYPE
            SYNTAX RowStatus
            MAX-ACCESS read-create
            STATUS current
            DESCRIPTION
                "RowStatus"
            ::= { mgmtVlanEntry 4 }

        
--           SNTP Information.
        sSNTP OBJECT IDENTIFIER ::= { commonMIB 5 }

        
        sntpScalars OBJECT IDENTIFIER ::= { sSNTP 1 }

        
        sntpEnable OBJECT-TYPE
            SYNTAX INTEGER
                {
                disable(0),
                enable(1)
                }
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                "Enable/Disable sntp server"
            ::= { sntpScalars 1 }

        
        sntpSyncInterval OBJECT-TYPE
            SYNTAX INTEGER (5..600)
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                "Inteval to sync with NTP server, Unit is seconde"
            ::= { sntpScalars 2 }

        
        sntpSyncStatus OBJECT-TYPE
            SYNTAX INTEGER
                {
                notsync(0),
                sync(1)
                }
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "If system synced with NTP"
            ::= { sntpScalars 3 }

        
        sntpLastSyncTime OBJECT-TYPE
            SYNTAX DisplayString
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The most recent time synced with NTP server"
            ::= { sntpScalars 4 }

        
        sntpSyncedTime OBJECT-TYPE
            SYNTAX DisplayString
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "Current synced time"
            ::= { sntpScalars 5 }

        
        sntpServerCount OBJECT-TYPE
            SYNTAX Unsigned32 (0..4294967295)
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The number of NTP server configured in the system."
            ::= { sntpScalars 6 }

        
        sntpServerTable OBJECT-TYPE
            SYNTAX SEQUENCE OF SntpServerEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "SNTP Server Table"
            ::= { sSNTP 2 }

        
        sntpServerEntry OBJECT-TYPE
            SYNTAX SntpServerEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                " "
            INDEX { sntpServerIp }
            ::= { sntpServerTable 1 }

        
        SntpServerEntry ::=
            SEQUENCE { 
                sntpServerIp
                    IpAddress,
                sntpServerStatus
                    INTEGER,
                sntpServerTableRowStatus
                    RowStatus
             }

        sntpServerIp OBJECT-TYPE
            SYNTAX IpAddress
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "Ipv4 unicast address of NTP server"
            ::= { sntpServerEntry 1 }

        
        sntpServerStatus OBJECT-TYPE
            SYNTAX INTEGER
                {
                unknown(0),
                unreachable(1),
                unsupport(2),
                support(3)
                }
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "status of NTP server"
            ::= { sntpServerEntry 2 }

        
        sntpServerTableRowStatus OBJECT-TYPE
            SYNTAX RowStatus
            MAX-ACCESS read-create
            STATUS current
            DESCRIPTION
                "RowStatus"
            ::= { sntpServerEntry 3 }

        
        commonEventsMIB OBJECT IDENTIFIER ::= { stCommon 2 }

        
--         ----------------------------------------------------------------------
-- Common Events MIB
-- ----------------------------------------------------------------------
        trapCategory OBJECT-TYPE
            SYNTAX EventCategory
            MAX-ACCESS accessible-for-notify
            STATUS current
            DESCRIPTION
                "Identifies the event category for which changes on the corresponding part of the MIB are summarized in a particular counter."
            ::= { commonEventsMIB 1 }

        
--         DEFVAL    { contextSpecific }
        trapCounterValue OBJECT-TYPE
            SYNTAX Counter32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "Counter for all events of a certain category which are notified.
                Each time a currentAlarmTrap, a configurationChangedTrap or a protectionSwitchTrap for this manager and event category is issued to the set of currently registered managers this counter value is incremented by one."
            ::= { commonEventsMIB 2 }

        
        typeOfChange OBJECT-TYPE
            SYNTAX TypeOfChange
            MAX-ACCESS accessible-for-notify
            STATUS current
            DESCRIPTION
                "Indicates type of change event."
            ::= { commonEventsMIB 3 }

        
        eventTimeStamp OBJECT-TYPE
            SYNTAX TimeTicks
            MAX-ACCESS accessible-for-notify
            STATUS current
            DESCRIPTION
                "Time stamp for notification of events occurring in the NE."
            ::= { commonEventsMIB 4 }

        
        eventDetailIdentifier OBJECT-TYPE
            SYNTAX VariablePointer
            MAX-ACCESS accessible-for-notify
            STATUS current
            DESCRIPTION
                "Indicates identifier of scalar object, table entry or object within table entry."
            ::= { commonEventsMIB 5 }

        
        eventDetailIntegerValue OBJECT-TYPE
            SYNTAX INTEGER
            MAX-ACCESS accessible-for-notify
            STATUS current
            DESCRIPTION
                "Indicates changed value of object if eventDetailIdentifier has a syntax derived from INTEGER."
            ::= { commonEventsMIB 6 }

        
        eventDetailStringValue OBJECT-TYPE
            SYNTAX OCTET STRING
            MAX-ACCESS accessible-for-notify
            STATUS current
            DESCRIPTION
                "Indicates changed value of object if eventDetailIdentifier has a syntax derived from OCTET STRING."
            ::= { commonEventsMIB 7 }

        
        lastChangeFlag OBJECT-TYPE
            SYNTAX TruthValue
            MAX-ACCESS accessible-for-notify
            STATUS current
            DESCRIPTION
                "False (true) if further traps related to the originating request will (will not) be sent."
            ::= { commonEventsMIB 8 }

        
        alarmMIB OBJECT IDENTIFIER ::= { stCommon 3 }

        
--         ----------------------------------------------------------------------
-- Alarm MIB
-- ----------------------------------------------------------------------
-- ----------------------------------------------------------------------
-- Current Alarms
-- ----------------------------------------------------------------------
        sCurrentAlarmTable OBJECT-TYPE
            SYNTAX SEQUENCE OF SCurrentAlarmEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "Active Alarm Table"
            ::= { alarmMIB 1 }

        
        sCurrentAlarmEntry OBJECT-TYPE
            SYNTAX SCurrentAlarmEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                " "
            INDEX { alarmSourceH, alarmSourceL, alarmCode }
            ::= { sCurrentAlarmTable 1 }

        
        SCurrentAlarmEntry ::=
            SEQUENCE { 
                alarmSourceH
                    Unsigned32,
                alarmSourceL
                    Unsigned32,
                alarmCode
                    AlarmCode,
                alarmSourceType
                    AlarmPathType,
                alarmSystemType
                    DisplayString,
                alarmSeverity
                    AlarmSeverity,
                alarmType
                    Unsigned32,
                alarmProbableCause
                    DisplayString,
                alarmState
                    AlarmState,
                alarmDTS
                    Unsigned32,
                alarmTimeStamp
                    DateAndTime,
                alarmRowStatus
                    RowStatus,
             }

        alarmSourceH OBJECT-TYPE
            SYNTAX Unsigned32 (0..4294967295)
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "Gives the high path of the alarm: IfIndex"
            ::= { sCurrentAlarmEntry 1 }

        
        alarmSourceL OBJECT-TYPE
            SYNTAX Unsigned32 (0..4294967295)
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "Gives the low path of the alarm: IfIndex"
            ::= { sCurrentAlarmEntry 2 }

        
        alarmCode OBJECT-TYPE
            SYNTAX AlarmCode
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "Unique ID identifying the type of alarm."
            ::= { sCurrentAlarmEntry 3 }

        
        alarmSourceType OBJECT-TYPE
            SYNTAX AlarmPathType
            MAX-ACCESS read-create
            STATUS current
            DESCRIPTION
                "Gives the path type of the alarm:PON, ONU etc."
            ::= { sCurrentAlarmEntry 4 }

        
        alarmSystemType OBJECT-TYPE
            SYNTAX DisplayString (SIZE (0..32))
            MAX-ACCESS read-create
            STATUS current
            DESCRIPTION
                "System Type: SAN3100-G16 etc."
            ::= { sCurrentAlarmEntry 5 }

        
        alarmSeverity OBJECT-TYPE
            SYNTAX INTEGER
                {
                notInAlarm(0),
                warning(1),
                minor(2),
                major(3),
                critical(4),
                indeterminate(5),
                notreport(6)
                }
            MAX-ACCESS read-create
            STATUS current
            DESCRIPTION
                "The severity of the alarm."
            ::= { sCurrentAlarmEntry 6 }

        
        alarmType OBJECT-TYPE
            SYNTAX Unsigned32
            MAX-ACCESS read-create
            STATUS current
            DESCRIPTION
                "Made up of 4 bytes, CardType(higher 2 bytes)+AlarmType(lower 2 bytes)"
            DEFVAL { 0 }
            ::= { sCurrentAlarmEntry 7 }

        
        alarmProbableCause OBJECT-TYPE
            SYNTAX DisplayString (SIZE (0..32))
            MAX-ACCESS read-create
            STATUS current
            DESCRIPTION
                "Short description of the alarm according to event code"
            ::= { sCurrentAlarmEntry 8 }

        
        alarmState OBJECT-TYPE
            SYNTAX AlarmState
                {
                set(1),
                clear(2)
                }
            MAX-ACCESS read-create
            STATUS current
            DESCRIPTION
                "Alarm state"
            ::= { sCurrentAlarmEntry 9 }

        
        alarmDTS OBJECT-TYPE
            SYNTAX Unsigned32
            MAX-ACCESS read-create
            STATUS current
            DESCRIPTION
                "Indicates the time at which the alarm state changes."
            ::= { sCurrentAlarmEntry 10 }

        
--          DEFVAL    { contextSpecific }
        alarmTimeStamp OBJECT-TYPE
            SYNTAX DateAndTime
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "As a member of CurrentAlarmEntry it indicates when this alarm was raised. As part of a currentAlarmTrap it indicates when the alarm was raised resp. cleared."
            ::= { sCurrentAlarmEntry 11 }

        
        alarmRowStatus OBJECT-TYPE
            SYNTAX RowStatus
                {
                active(1),
                notInService(2),
                notReady(3),
                createAndGo(4),
                createAndWait(5),
                destroy(6)
                }
            MAX-ACCESS read-create
            STATUS current
            DESCRIPTION
                "Row Status of active alarm table."
            ::= { sCurrentAlarmEntry 12 }
            
        alarmEventsMIB OBJECT IDENTIFIER ::= { stCommon 4 }

        
--         ----------------------------------------------------------------------
-- Alarm Events MIB
-- ----------------------------------------------------------------------
        currentAlarmTrap NOTIFICATION-TYPE
            OBJECTS { alarmSourceH, alarmSourceL, alarmCode, alarmSourceType, alarmSystemType, 
                alarmSeverity, alarmType, alarmProbableCause, alarmState, alarmDTS, 
                alarmTimeStamp, alarmRowStatus, alarmShelfTag }
            STATUS current
            DESCRIPTION 
                "This trap is sent to all SNMP managers if an alarm is raised or cleared.
                trapCounterValue is the current value of the alarm trap counter.
                alarmSeverity indicates the assigned severity (i.e. anything but cleared) if an alarm is raised. It also indicates if an alarm is cleared (without indicating the assigned severity).
                alarmTimeStamp indicates the time when the alarm was raised resp. cleared."
            REFERENCE 
                "NE-FSpec FM - TMN Alarm Event Notification"
            ::= { alarmEventsMIB 2 }

        
        configMIB OBJECT IDENTIFIER ::= { stCommon 7 }

        
--          ----------------------------------------------------------------------
-- Shelves
-- ----------------------------------------------------------------------
        shelfTable OBJECT-TYPE
            SYNTAX SEQUENCE OF ShelfEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "Table of present shelves within this NE."
            ::= { configMIB 1 }

        
        shelfEntry OBJECT-TYPE
            SYNTAX ShelfEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "Additional parameters for present shelf."
            INDEX { shelfId }
            ::= { shelfTable 1 }

        
        ShelfEntry ::=
            SEQUENCE { 
                shelfId
                    Unsigned32,
                shelfType
                    INTEGER,
                shelfSN
                    DisplayString,
                shelfPartNumber
                    DisplayString,
                shelfCLEI
                    DisplayString,
                shelfHwVersion
                    DisplayString,
                shelfMacAddressMgmt1
                    DisplayString,
                shelfMacAddressMgmt2
                    DisplayString,
                shelfMacAddressMgmt3
                    DisplayString,
                shelfAssignShelfId
                    Unsigned32,
                shelfSwVersion
                    DisplayString,
                shelfLocation
                    DisplayString,
                shelfTemperature
                    Integer32,
                shelfMDI1Enable
                    INTEGER,
                shelfMDI2Enable
                    INTEGER,
                shelfMDO1Condition
                    INTEGER,
                shelfMDO2Condition
                    INTEGER,
                shelfLampTest
                    INTEGER,
                shelfAlarmCutOperation
                    INTEGER,
                shelfRowStatus
                    RowStatus,
                shelfFanSpeedPwm
                    Integer32,
                shelfFanTopSpeed
                    INTEGER,
                shelfFanSpeedAutoRegulating
                    INTEGER,
                shelfOneIpRole
                    INTEGER
             }

        shelfId OBJECT-TYPE
            SYNTAX Unsigned32 (1..128)
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "Shelf identifier of equipment supporting the object."
            ::= { shelfEntry 1 }

        
        shelfType OBJECT-TYPE
            SYNTAX INTEGER
                {
                unknown(0),
                otnSingleCard(20),
                otn1uAC(21),
                otn1uDC(22),
                otn2uDC(23),
                otn10uDC(24),
                otn1uA(25),
                stn2uAC(26),
                stn2uDC(27),
                stn1u(28),
                stnRpc6480(29),
                otn6250DC(30),
                otn6250AC(31),
                otn6250DCAC(32),
                stnB1U(40),
                stnB2U(41),
                olt1uA(51),
                olt1uB(52),
                olt10uDC(53),
                otn10uDC4halfslot(60),
                otn10uDC6halfslot(61),
                otn10uDC8halfslot(62),
                otn10uDC10halfslot(63),
                otn10uDC12halfslot(64),
                otn5uDC7F(70),
                otn5uDC6F(71),
                otn5uDC4F(72),
                otn5uDC2F(73),
                otn5uDC0F(74),
                otn5uAC6F(75),
                otn5uAC4F(76),
                otn5uAC2F(77),
                otn5uAC0F(78),
                otn5uDCAC6F(79),
                otn5uDCAC4F(80),
                otn5uDCAC2F(81),
                otn5uDCAC0F(82),
                stnIA1u(90),
                stn5uDC(95),
                stn5uAC(96),
                stn5uDCAC(97),
                dci1ua(100),
                mibmax(101),
                stn3uDC(120),
                stn3uAC(121),
                stn3uDCAC(122)
                }
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The shelf type"
            DEFVAL { unknown }
            ::= { shelfEntry 2 }

        
        shelfSN OBJECT-TYPE
            SYNTAX DisplayString (SIZE (0..50))
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "serial number of shelf"
            DEFVAL { "" }
            ::= { shelfEntry 3 }

        
        shelfPartNumber OBJECT-TYPE
            SYNTAX DisplayString (SIZE (0..50))
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "part number of shelf"
            DEFVAL { "" }
            ::= { shelfEntry 4 }

        
        shelfCLEI OBJECT-TYPE
            SYNTAX DisplayString (SIZE (0..20))
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "Common Language Interface of shelf"
            DEFVAL { "" }
            ::= { shelfEntry 5 }

        
        shelfHwVersion OBJECT-TYPE
            SYNTAX DisplayString (SIZE (0..20))
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "hardware's version of shelf"
            DEFVAL { "" }
            ::= { shelfEntry 6 }

        
        shelfMacAddressMgmt1 OBJECT-TYPE
            SYNTAX DisplayString (SIZE (0..20))
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "the mac address of managment port 1"
            ::= { shelfEntry 7 }

        
        shelfMacAddressMgmt2 OBJECT-TYPE
            SYNTAX DisplayString (SIZE (0..20))
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "the mac address of managment port 2"
            ::= { shelfEntry 8 }

        
        shelfMacAddressMgmt3 OBJECT-TYPE
            SYNTAX DisplayString (SIZE (0..20))
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "the mac address of managment port 3"
            ::= { shelfEntry 9 }

        
        shelfAssignShelfId OBJECT-TYPE
            SYNTAX Unsigned32 (1..128)
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                "Used to match unassigned shelf to required shelf."
            ::= { shelfEntry 10 }

        
        shelfSwVersion OBJECT-TYPE
            SYNTAX DisplayString (SIZE (0..30))
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "software version of shelf"
            DEFVAL { "" }
            ::= { shelfEntry 11 }

        
        shelfLocation OBJECT-TYPE
            SYNTAX DisplayString (SIZE (0..240))
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                "Physical location of a particular shelf within NE arrangement."
            DEFVAL { "" }
            ::= { shelfEntry 12 }

        
        shelfTemperature OBJECT-TYPE
            SYNTAX Integer32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "temperature of shelf"
            ::= { shelfEntry 13 }

        
        shelfMDI1Enable OBJECT-TYPE
            SYNTAX INTEGER
                {
                disable(0),
                enable(1)
                }
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                "enable/disable MDI1 detection, default is disable (no alarm reported), when it��s enabled, MDI alarm shall be raised if no input detected"
            DEFVAL { disable }
            ::= { shelfEntry 14 }

        
        shelfMDI2Enable OBJECT-TYPE
            SYNTAX INTEGER
                {
                disable(0),
                enable(1)
                }
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                "enable/disable MDI2 detection, default is disabled(no alarm reported), when it��s enabled, MDI alarm shall be raised if no input detected"
            DEFVAL { disable }
            ::= { shelfEntry 15 }

        
        shelfMDO1Condition OBJECT-TYPE
            SYNTAX INTEGER
                {
                none(0),
                critical(1),
                criticalAndMajor(2),
                criticalAndMajorAndMinor(3),
                userDefined(4)
                }
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                "If the value is critical, any critical alarm will set MDO, if value is critical&major&minor, any critical or major or minor alarm will set MDO, if value is userDefined, the MDO is always set"
            DEFVAL { none }
            ::= { shelfEntry 16 }

        
        shelfMDO2Condition OBJECT-TYPE
            SYNTAX INTEGER
                {
                none(0),
                critical(1),
                criticalAndMajor(2),
                criticalAndMajorAndMinor(3),
                userDefined(4)
                }
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                "If the value is critical, any critical alarm will set MDO, if value is critical&major&minor, any critical or major or minor alarm will set MDO, if value is userDefined, the MDO is always set"
            DEFVAL { none }
            ::= { shelfEntry 17 }

        
        shelfLampTest OBJECT-TYPE
            SYNTAX INTEGER
                {
                false(0),
                true(1)
                }
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                "trigger the lamp test on shelf"
            DEFVAL { false }
            ::= { shelfEntry 18 }

        
        shelfAlarmCutOperation OBJECT-TYPE
            SYNTAX INTEGER
                {
                false(0),
                true(1)
                }
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                "trigger to cut off the alarm on shelf"
            DEFVAL { false }
            ::= { shelfEntry 19 }

        
        shelfRowStatus OBJECT-TYPE
            SYNTAX RowStatus
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "Used in order to create a shelf. The following values can be used with a semantics according to RFC 2579:
                - active: returned if subsystem managing this shelf is available
                - notReady: returned if subsystem managing this shelf is not available
                - createAndGo: set by the manager in order to add a present shelf
                    (not for locked ONN and OLR)
                - destroy: set by the manager in order to remove a present shelf
                    (not for locked ONN and OLR)"
            REFERENCE
                "NE-FSpec CM - Shelf.sysAvailable"
            ::= { shelfEntry 20 }

        
        shelfFanSpeedPwm OBJECT-TYPE
            SYNTAX Integer32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "Current FAN speed, in duty ratio"
            ::= { shelfEntry 21 }

        
        shelfFanTopSpeed OBJECT-TYPE
            SYNTAX INTEGER
                {
                false(0),
                true(1)
                }
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "trigger FAN top speed by Hardware"
            DEFVAL { false }
            ::= { shelfEntry 22 }
        
        shelfFanSpeedAutoRegulating OBJECT-TYPE
            SYNTAX INTEGER
                {
                disable(0),
                enable(1)
                }
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                "the way of fan speed auto-regulating"
            DEFVAL { enable }
            ::= { shelfEntry 23 }
        
        shelfOneIpRole OBJECT-TYPE
            SYNTAX INTEGER
                {
                normal(0),
                gwne(1),
                subne1(2),
                subne2(3),
                subne3(4),
        subne4(5),
                subne5(6)
                }
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                "NE role for One IP feature"
            DEFVAL { normal }
            ::= { shelfEntry 24 }

        
--          ----------------------------------------------------------------------
-- Slots and Cards
-- ----------------------------------------------------------------------
        slotTable OBJECT-TYPE
            SYNTAX SEQUENCE OF SlotEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "Table with common management information related to all slots in required and overequipped shelves of the NE.
                The table contains always one row for each available slot (according to the NE type) of a provisioned or overequipped shelf. Card creation and deletion in slots of a provisioned shelf are depicted through changes of cardRequiredCardType, card insertion and removal in slots of a provisioned or overequipped shelf are depicted through changes of cardActualCardType for the corresponding cardEntry."
            ::= { configMIB 2 }

        
        slotEntry OBJECT-TYPE
            SYNTAX SlotEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "Represents a particular slot within a required or overequipped shelf of the NE.
                An empty slot, i.e. a slot that is intended to be empty and has no physical card equipped, is depicted through a card with card type 'empty'. Both images of card type (cardRequiredCardType and cardActualCardType) are set to 'empty'.
                An empty slot that is prepared to accept cards which are not required but can be recognized by the NE is indicated by cardRequiredCardType 'emptyAuto' and cardActualCardType 'empty'. Once a known card is plugged and accepted by the NE, the required and actual card type will match the card type of the plugged card."
            INDEX { shelfId, slotNo, subSlotNo }
            ::= { slotTable 1 }

        
        SlotEntry ::=
            SEQUENCE { 
                slotNo
                    Unsigned32,
                subSlotNo
                    Unsigned32,
                slotRequiredCardType
                    CardType,
                slotActualCardType
                    CardType,
                slotAdminState
                    INTEGER,
                slotOperationState
                    INTEGER,
                slotRowStatus
                    RowStatus,
                slotDesc
                    DisplayString
             }

        slotNo OBJECT-TYPE
            SYNTAX Unsigned32 (1..64)
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "The numerical slot position for this card."
            ::= { slotEntry 1 }

        
        subSlotNo OBJECT-TYPE
            SYNTAX Unsigned32 (0..8)
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "some slot have sub slot"
            ::= { slotEntry 2 }

        
        slotRequiredCardType OBJECT-TYPE
            SYNTAX CardType
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                "Provisioning card type"
            DEFVAL { empty }
            ::= { slotEntry 3 }

        
        slotActualCardType OBJECT-TYPE
            SYNTAX CardType
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                "actual card type"
            DEFVAL { empty }
            ::= { slotEntry 4 }

        
        slotAdminState OBJECT-TYPE
            SYNTAX INTEGER
                {
                disabled(0),
                enabled(1)
                }
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                " "
            DEFVAL { enabled }
            ::= { slotEntry 5 }

        
        slotOperationState OBJECT-TYPE
            SYNTAX INTEGER
                {
                unknown(0),
                available(1),
                absent(2),
                mismatch(3),
                operational(4),
                failure(5),
                disabled(6),
                initializing(7)
                }
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                "
                AVAIL = 1,        // Available
                ABSENT,           // Absent
                MISMATCH,         // Mismatch with logical card
                OPERATIONAL,    // Operation
                FAILURE,          // hardware failure
                DISABLED,         // Slot is disabled
                INITIALIZING,    // Initializing
                "
            DEFVAL { available }
            ::= { slotEntry 6 }

        
        slotRowStatus OBJECT-TYPE
            SYNTAX RowStatus
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "Column Description"
            ::= { slotEntry 7 }

        slotDesc OBJECT-TYPE
            SYNTAX DisplayString (SIZE (0..256))
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                "Description."
            DEFVAL { "" }
            ::= { slotEntry 8 }
        

        
        
        portTable OBJECT-TYPE
            SYNTAX SEQUENCE OF PortEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "Port Table"
            ::= { configMIB 3 }

        
        portEntry OBJECT-TYPE
            SYNTAX PortEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                " "
            INDEX { shelfId, slotNo, subSlotNo, portNo, subPortNo
                 }
            ::= { portTable 1 }

        
        PortEntry ::=
            SEQUENCE { 
                portNo
                    Unsigned32,
                subPortNo
                    Unsigned32,
                portAdminState
                    INTEGER,
                portOperationalState
                    INTEGER,
                portMode
                    StPortMode,
                portRowStatus
                    RowStatus,
                portAvailabilityState
                    StAvailabilityState,
                portDesc
                    DisplayString
             }

        portNo OBJECT-TYPE
            SYNTAX Unsigned32 (1..255)
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "Identification of port within card supporting this object.The value is zero for shelf and card objects"
            ::= { portEntry 1 }

        
        subPortNo OBJECT-TYPE
            SYNTAX Unsigned32 (0..255)
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "Identification of port within card supporting this object.The value is zero for shelf and card objects"
            ::= { portEntry 2 }

        
        portAdminState OBJECT-TYPE
            SYNTAX INTEGER
                {
                disabled(0),
                enabled(1)
                }
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                "Specifies the admin state of the entity.
                As to different object, the consequent aciton may be different, e.g. it may shutdown the port, or insert maintenance signal."
            DEFVAL { disabled }
            ::= { portEntry 3 }

        
        portOperationalState OBJECT-TYPE
            SYNTAX INTEGER
                {
                down(0),
                up(1)
                }
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The operational state of the Port."
            ::= { portEntry 4 }

        
        portMode OBJECT-TYPE
            SYNTAX StPortMode
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                "
                - ge          (GE client signal, GFP-T)
                - gettt       (GE client signal, GFP-T)
                - xgeOpu2e    (10GBASE-R client signal, GFP-F G.Sup43 Section 7.1)
                - xgeGfpOpu2  (10GBASE-R client signal, GFP-F G.Sup43 Section 6.2(payload only))
                - xgeGfpOpu2e (10GBASE-R client signal, GFP-F G.Sup43 Section 7.3)
                - ge100       (100GE client signal)
                            - feCbr       (FE client signal, GFP-T)
                            - stm1        (STM1 client signal)
                            - oc3         (OC3 client signal)
                            - stm4        (STM4 client signal)
                            - oc12        (OC12 client signal)
                            - stm16       (STM16 client signal)
                            - oc48        (OC48 client signal)
                - stm64       (STM64 client signal)
                - oc192       (OC192 client signal)
                            - fc1         (1G Fibre Channel client signal)
                            - fc2         (2G Fibre Channel client signal)
                            - fc4         (4G Fibre Channel client signal)
                - fc8         (8G Fibre Channel client signal)
                - fc10        (10G Fibre Channel client signal)
                            - otu1        (OTU1 client signal)
                - ochOSOtu1   (OTU1 line signal)
                            - otu2        (OTU2 client signal)
                - ochOSOtu2   (OTU2 line signal)
                            - otu2e       (OTU2e client signal)
                - ochOSOtu2e  (OTU2e line signal)
                - otu3        (OTU3 client signal)
                - ochOSOtu3   (OTU3 line signal)
                - otu4        (OTU4 client signal)
                            - ochOSOtu4   (OTU4 line signal)
                - otu0ll      (OTU0LL signal)"
            ::= { portEntry 5 }

        
        portRowStatus OBJECT-TYPE
            SYNTAX RowStatus
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "sPortTable RowStatus"
            ::= { portEntry 6 }

        
        portAvailabilityState OBJECT-TYPE
            SYNTAX StAvailabilityState
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "Avalaibility Status is to qualify the operational, usage and/or administrative state attributes"
            DEFVAL { normal }
            ::= { portEntry 7 }

        portDesc OBJECT-TYPE
            SYNTAX DisplayString (SIZE (0..256))
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                "Description."
            DEFVAL { "" }
            ::= { portEntry 8 }
            
        
        configEventsMIB OBJECT IDENTIFIER ::= { stCommon 8 }

        
--         ----------------------------------------------------------------------
-- Configuration Events MIB
-- ----------------------------------------------------------------------
        configurationChangedTrap NOTIFICATION-TYPE
            OBJECTS { eventTimeStamp, typeOfChange, eventDetailIdentifier, eventDetailIntegerValue, eventDetailStringValue, 
                lastChangeFlag }
            STATUS current
            DESCRIPTION 
                "Notification of configuration changes including
                - detection or removal of a gateway's target NE
                - changes of alarm masks and alarm severity assignments
                - configuration of performance monitoring points and performance thresholds.
                trapCounterValue is the current notification counter according to the event category.
                typeOfChange indicates whether new table entries have been added or existing entries have been modified or removed.
                eventTimeStamp indicates when the configuration change happened.
                eventDetailIdentifier may identify
                - the index of a table entry which has been added or removed
                - a particular columnar object within an added or removed table entry
                - the columnar object within a particular table entry which has been modified
                - the index of a table entry where multiple columnar objects have been modified
                - a scalar object which has been modified
                In case that this trap notifies the change of a single INTEGER (or some derived syntax) value eventDetailIntegerValue indicates this value.
                In case that this trap notifies the change of a single OCTET STRING (or some derived syntax) value eventDetailStringValue indicates this value.
                Otherwise eventDetailIntegerValue and eventDetailStringValue are reported with unspecific values and no further details are notified with this trap. The SNMP manager may request the corresponding common and object-specific table entries in order to find out details.
                If the configuration change was triggered by an SNMP SET request
                - eventUserName indicates the usmUserName of that request.
                - sessionID indicates the related session.
                - relatedRequestId indicates the request-id of that request.
                - lastChangeFlag indicates whether further traps will be sent."
            ::= { configEventsMIB 1 }

        
        securityMIB OBJECT IDENTIFIER ::= { stCommon 9 }

                    
        securityEventsMIB OBJECT IDENTIFIER ::= { stCommon 10 }

        
        dcnMIB OBJECT IDENTIFIER ::= { stCommon 11 }

        
        licenseMIB OBJECT IDENTIFIER ::= { stCommon 13 }

        
        inventoryMIB OBJECT IDENTIFIER ::= { stCommon 15 }

        
        cardInventoryTable OBJECT-TYPE
            SYNTAX SEQUENCE OF CardInventoryEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "Description."
            ::= { inventoryMIB 1 }

        
        cardInventoryEntry OBJECT-TYPE
            SYNTAX CardInventoryEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "Description."
            INDEX { shelfId, slotNo, subSlotNo }
            ::= { cardInventoryTable 1 }

        
        CardInventoryEntry ::=
            SEQUENCE { 
                cardType
                    CardType,
                cardSN
                    DisplayString,
                cardPartN
                    DisplayString,
                cardHwVersion
                    DisplayString,
                cardCLEI
                    DisplayString,
                cardSwVersion
                    DisplayString,
                cardFpgaId
                    DisplayString,
                cardFpgaVersion
                    DisplayString,
                cardFpagVersionState
                    Integer32,
                cardTemperature
                    Integer32,
                cardRowStatus
                    RowStatus,
                cardTemperature2
                    Integer32,
                cardCardTypeSrc
                    INTEGER,
                cardCardTypeSet
                    CardType,
                cardFpgaLoadVersion
                    DisplayString,
                cardSdPresent
                    INTEGER,
                cardCpldVersion
                    DisplayString,
                cardTemperature3
                    Integer32,
                cardUbootVersion
                    DisplayString,
                cardRootFsVersion
                    DisplayString,
                cardSwVersionInLoad
                    DisplayString,
                cardKernelVersion
                    DisplayString,
                cardRtcPresent
                    INTEGER
             }

        cardType OBJECT-TYPE
            SYNTAX CardType
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "Description."
            DEFVAL { empty }
            ::= { cardInventoryEntry 1 }

        
        cardSN OBJECT-TYPE
            SYNTAX DisplayString (SIZE (0..50))
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "serial number"
            DEFVAL { "" }
            ::= { cardInventoryEntry 2 }

        
        cardPartN OBJECT-TYPE
            SYNTAX DisplayString (SIZE (0..50))
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "partnumber"
            DEFVAL { "" }
            ::= { cardInventoryEntry 3 }

        
        cardHwVersion OBJECT-TYPE
            SYNTAX DisplayString (SIZE (0..20))
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                "hardware version"
            DEFVAL { "" }
            ::= { cardInventoryEntry 4 }

        
        cardCLEI OBJECT-TYPE
            SYNTAX DisplayString (SIZE (0..20))
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "Description."
            ::= { cardInventoryEntry 5 }

        
        cardSwVersion OBJECT-TYPE
            SYNTAX DisplayString (SIZE (0..30))
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "SW Version"
            DEFVAL { "" }
            ::= { cardInventoryEntry 6 }

        
        cardFpgaId OBJECT-TYPE
            SYNTAX DisplayString (SIZE (0..20))
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "NMS/CLI will do translation between Id and displayed name"
            DEFVAL { "" }
            ::= { cardInventoryEntry 7 }

        
        cardFpgaVersion OBJECT-TYPE
            SYNTAX DisplayString (SIZE (0..20))
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "Description."
            DEFVAL { "" }
            ::= { cardInventoryEntry 8 }

        
        cardFpagVersionState OBJECT-TYPE
            SYNTAX Integer32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "to indicate whether the current FPGA version is same as version in SW load"
            ::= { cardInventoryEntry 9 }

        
        cardTemperature OBJECT-TYPE
            SYNTAX Integer32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "Temperature near CPU."
            ::= { cardInventoryEntry 10 }

        
        cardRowStatus OBJECT-TYPE
            SYNTAX RowStatus
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "Description."
            ::= { cardInventoryEntry 11 }

        
        cardTemperature2 OBJECT-TYPE
            SYNTAX Integer32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "Temperature at air outlet."
            ::= { cardInventoryEntry 12 }

        
        cardCardTypeSrc OBJECT-TYPE
            SYNTAX INTEGER
                {
                unsupport(0),
                hardware(1),
                software(2)
                }
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                "Define the source where to get the cardType"
            DEFVAL { unsupport }
            ::= { cardInventoryEntry 13 }

        
        cardCardTypeSet OBJECT-TYPE
            SYNTAX CardType
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                "Description."
            DEFVAL { empty }
            ::= { cardInventoryEntry 14 }

        
        cardFpgaLoadVersion OBJECT-TYPE
            SYNTAX DisplayString (SIZE (0..20))
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "Description."
            DEFVAL { "" }
            ::= { cardInventoryEntry 15 }

        
        cardSdPresent OBJECT-TYPE
            SYNTAX INTEGER
                {
                absent(0),
                present(1)
                }
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "Define the state for SD card"
            DEFVAL { absent }
            ::= { cardInventoryEntry 16 }

        
        cardCpldVersion OBJECT-TYPE
            SYNTAX DisplayString (SIZE (0..20))
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "Description."
            DEFVAL { "" }
            ::= { cardInventoryEntry 17 }

        
        cardTemperature3 OBJECT-TYPE
            SYNTAX Integer32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "Temperature at air intake."
            ::= { cardInventoryEntry 18 }

        
        cardUbootVersion OBJECT-TYPE
            SYNTAX DisplayString (SIZE (0..64))
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "Description."
            DEFVAL { "" }
            ::= { cardInventoryEntry 19 }

        
        cardRootFsVersion OBJECT-TYPE
            SYNTAX DisplayString (SIZE (0..64))
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "Description."
            DEFVAL { "" }
            ::= { cardInventoryEntry 20 }

        
        cardSwVersionInLoad OBJECT-TYPE
            SYNTAX DisplayString (SIZE (0..20))
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "Description."
            DEFVAL { "" }
            ::= { cardInventoryEntry 21 }

        
        cardKernelVersion OBJECT-TYPE
            SYNTAX DisplayString (SIZE (0..64))
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "Description."
            DEFVAL { "" }
            ::= { cardInventoryEntry 22 }

        
        cardRtcPresent OBJECT-TYPE
            SYNTAX INTEGER
                {
                absent(0),
                present(1)
                }
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "Define the state for RTC"
            DEFVAL { absent }
            ::= { cardInventoryEntry 23 }

        
        pluggableTable OBJECT-TYPE
            SYNTAX SEQUENCE OF PluggableEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "Description."
            ::= { inventoryMIB 2 }

        
        pluggableEntry OBJECT-TYPE
            SYNTAX PluggableEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "Description."
            INDEX { shelfId, slotNo, subSlotNo, portNo, subPortNo
                 }
            ::= { pluggableTable 1 }

        
        PluggableEntry ::=
            SEQUENCE { 
                plType
                    INTEGER,
                plPartNumber
                    DisplayString,
                plVendorSN
                    DisplayString,
                plVendorPN
                    DisplayString,
                plVendorName
                    DisplayString,
                plVendorOUI
                    DisplayString,
                plApplicationCode
                    Unsigned32,
                plCLEI
                    DisplayString,
                plLaneNum
                    Unsigned32,
                pl10GWdmType
                    Integer32,
                pl100GWdmType
                    Integer32,
                plWaveLength
                    Integer32,
                plTunable
                    INTEGER,
                plFirmware
                    DisplayString,
                plPresenceState
                    INTEGER,
                plRowStatus
                    RowStatus,
                plDateCode
                    DisplayString,
                plLength
                    Integer32,
                plNominalMBps
                    Integer32
             }

        plType OBJECT-TYPE
            SYNTAX INTEGER
                {
                unknown(0),
                gbic(1),
                module-connector(2),
                sfp(3),
                xsbi(4),
                xenpak(5),
                xfp(6),
                xff(7),
                xfpe(8),
                xpak(9),
                x2(10),
                dwdmsfp(11),
                qsfp(17),
                qsfpplus(13),
                cfp(14),
                cfp2(16),
                cfp4(18),
                cfp2aco(20),
                cfp2dco(25),
                voaD(192),
                voaB(193)
                }
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "00h: Unknown or unspecified,
                01h: GBIC, *
                02h: Module/connector soldered to motherboard,*
                03h: SFP,*
                04h: 300 pin XSBI, *
                05h: XENPAK,*
                06h: XFP,*
                07h: XFF,*
                08h: XFP-E,*
                09h: XPAK,*
                0Ah: X2,*
                0Bh: DWDM-SFP,*
                0Ch: QSFP, *
                0Dh: QSFP+,*
                0Eh: CFP,
                0Fh: Reserved, (changed from CXP)
                10h: 168-pin 5��x7�� MSA-100GLH,
                11h: CFP2,
                12h: CFP4,
                13h: 168-pin 4��x5�� MSA-100GLH,
                14h: CFP2-ACO
                19h: CFP2-DCO
                c0h: VOA dark type
                c1h: VOA bright type
                15h ~ FFh : Reserved."
            ::= { pluggableEntry 1 }

        
        plPartNumber OBJECT-TYPE
            SYNTAX DisplayString (SIZE (20))
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "Description."
            ::= { pluggableEntry 2 }

        
        plVendorSN OBJECT-TYPE
            SYNTAX DisplayString (SIZE (20))
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "Vendor (manufacturer) serial number in any
                combination of letters and/or digits in ASCII code, left
                aligned and padded on the right with ASCII spaces
                (20h). All zero means unfined."
            ::= { pluggableEntry 3 }

        
        plVendorPN OBJECT-TYPE
            SYNTAX DisplayString (SIZE (20))
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "Vendor (manufacturer) part number in any
                combination of letters and/or digits in ASCII code, left
                aligned and padded on the right with ASCII spaces
                (20h). All zero value means undefined. Vendor is the
                CFP module vendor."
            ::= { pluggableEntry 4 }

        
        plVendorName OBJECT-TYPE
            SYNTAX DisplayString (SIZE (20))
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "Vendor (manufacturer) name in any combination of
                letters and/or digits in ASCII code, left aligned and
                padded on the right with ASCII spaces (20h). The
                vendor name shall be the full name of the corporation,
                a commonly accepted abbreviation of the name or the
                stock exchange code for the corporation. "
            ::= { pluggableEntry 5 }

        
        plVendorOUI OBJECT-TYPE
            SYNTAX DisplayString (SIZE (20))
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "Description.
                The vendor organizationally unique identifier field
                (vendor OUI) is a 3-byte field that contains the IEEE
                Company Identifier for the vendor. The OUI format is
                defined by IEEE 802, section 9.1, which specifies ��a
                string of three octets, using the hexadecimal
                representation��, which lists the OUI bytes in forward
                order.
                data format: xx-xx-xx"
            ::= { pluggableEntry 6 }

        
        plApplicationCode OBJECT-TYPE
            SYNTAX Unsigned32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "(NE SW will report code, NMS/CLI will do translation) : 
                - CFP: e.g. 4I1-9D1F, etc.
                - SFPP_SFP: e.g. I-64.1, etc."
            ::= { pluggableEntry 7 }

        
        plCLEI OBJECT-TYPE
            SYNTAX DisplayString (SIZE (20))
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "CLEI Code in any combination of letters and/or digits
                in ASCII code.(size 10)"
            ::= { pluggableEntry 8 }

        
        plLaneNum OBJECT-TYPE
            SYNTAX Unsigned32 (1..12)
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "For 10G SFP/SFPP/XFP, it��s always 1, not displayed to operator
                For 100G CFP: LR4 is 4, SR10 is 10, coherent CFP is ?"
            DEFVAL { 1 }
            ::= { pluggableEntry 9 }

        
        pl10GWdmType OBJECT-TYPE
            SYNTAX Integer32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "Description."
            ::= { pluggableEntry 10 }

        
        pl100GWdmType OBJECT-TYPE
            SYNTAX Integer32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "A 3-bit field identifying any optical grid spacing used by
                CFP module000b: Non-WDM,
                001b: CWDM,
                010b: LANWDM,
                011b: DWDM on 200G-grid,
                100b: DWDM on 100G-grid,
                101b: DWDM on 50G-grid,
                110b: DWDM on 25G-grid,
                111b: Other type WDM..
                "
            ::= { pluggableEntry 11 }

        
        plWaveLength OBJECT-TYPE
            SYNTAX Integer32
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                "applied to DWDM and CWDM optics only, can be written when attribute ��Tunable�� is true"
            ::= { pluggableEntry 12 }

        
        plTunable OBJECT-TYPE
            SYNTAX INTEGER
                {
                false(0),
                true(1)
                }
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "- For 100G WDM optics: to check whether use ��Tunability�� or ��Wavelength control�� to identify
                - For 10G WDM optics: to check which attribute will be used"
            DEFVAL { false }
            ::= { pluggableEntry 13 }

        
        plFirmware OBJECT-TYPE
            SYNTAX DisplayString (SIZE (20))
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "A two-register number in the format of x.y with x at
                lower address and y at higher address. All zero value
                indicates undefined."
            ::= { pluggableEntry 14 }

        
        plPresenceState OBJECT-TYPE
            SYNTAX INTEGER
                {
                unknown(1),
                work(2),
                alarm(3),
                absent(4)
                }
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "Description."
            DEFVAL { unknown }
            ::= { pluggableEntry 15 }

        
        plRowStatus OBJECT-TYPE
            SYNTAX RowStatus
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "Description."
            ::= { pluggableEntry 16 }

            
        plDateCode OBJECT-TYPE
            SYNTAX DisplayString (SIZE (16))
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "Vendor's manufacturing date code. "
            ::= { pluggableEntry 17 }
            
        plLength OBJECT-TYPE
            SYNTAX Integer32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "Link length supported for single mode fiber,units of km. "
            ::= { pluggableEntry 18 }
            
        plNominalMBps OBJECT-TYPE
            SYNTAX Integer32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "Nominal signalling rate,units of 100MBd. "
            ::= { pluggableEntry 19 }    
        
        pluggabelOpticsParameterTable OBJECT-TYPE
            SYNTAX SEQUENCE OF PluggabelOpticsParameterEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "Description."
            ::= { inventoryMIB 3 }

        
        pluggabelOpticsParameterEntry OBJECT-TYPE
            SYNTAX PluggabelOpticsParameterEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "Description."
            INDEX { shelfId, slotNo, subSlotNo, portNo, subPortNo, 
                popLaneId }
            ::= { pluggabelOpticsParameterTable 1 }

        
        PluggabelOpticsParameterEntry ::=
            SEQUENCE { 
                popLaneId
                    Unsigned32,
                popLaneTxPower
                    Integer32,
                popLaneRxPower
                    Integer32,
                pmpLaneLaserTemperature
                    Integer32,
                popLaneLaserBias
                    Integer32,
                popLaneLaserVcc
                    Integer32,
                popRowStatus
                    RowStatus
             }

        popLaneId OBJECT-TYPE
            SYNTAX Unsigned32 (1..12)
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "Description."
            ::= { pluggabelOpticsParameterEntry 1 }

        
        popLaneTxPower OBJECT-TYPE
            SYNTAX Integer32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "transmission power of lane n (n<=12, applicable for multiple lane, e.g. SR10 CFP)"
            DEFVAL { -10000 }
            ::= { pluggabelOpticsParameterEntry 2 }

        
        popLaneRxPower OBJECT-TYPE
            SYNTAX Integer32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "receiving power of lane n (n<=12, applicable for multiple lane, e.g. SR10 CFP)"
            DEFVAL { -10000 }
            ::= { pluggabelOpticsParameterEntry 3 }

        
        pmpLaneLaserTemperature OBJECT-TYPE
            SYNTAX Integer32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "laser temperature (note: there is too high and too low alarm for each lane)"
            ::= { pluggabelOpticsParameterEntry 4 }

        
        popLaneLaserBias OBJECT-TYPE
            SYNTAX Integer32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "laser bias current (note: there is too high and too low alarm for each lane)"
            ::= { pluggabelOpticsParameterEntry 5 }

        
        popLaneLaserVcc OBJECT-TYPE
            SYNTAX Integer32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "laser voltage (not defined in CFP?) (note: there is too high and too low alarm for each lane)"
            ::= { pluggabelOpticsParameterEntry 6 }

        
        popRowStatus OBJECT-TYPE
            SYNTAX RowStatus
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "Description."
            ::= { pluggabelOpticsParameterEntry 7 }

        
        cardLedTable OBJECT-TYPE
            SYNTAX SEQUENCE OF CardLedEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "Description."
            ::= { inventoryMIB 4 }

        
        cardLedEntry OBJECT-TYPE
            SYNTAX CardLedEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "Description."
            INDEX { shelfId, slotNo, subSlotNo }
            ::= { cardLedTable 1 }

        
        CardLedEntry ::=
            SEQUENCE { 
                cardLedStatus
                    OCTET STRING,
                cardLedRowStatus
                    RowStatus
             }

        cardLedStatus OBJECT-TYPE
            SYNTAX OCTET STRING (SIZE (32))
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The definition of led status.
                One byte for each led, define the index as below:
                fault,run,critical,major,minor,active,reserve,reserve,port1~port24
                For each led, define its status as below:
                0: unknown,
                1: off 
                2: green slow blink
                3: green fast blink
                4: green always
                5: red slow blink
                6: red fast blink
                7: red always "
            ::= { cardLedEntry 1 }

        
        cardLedRowStatus OBJECT-TYPE
            SYNTAX RowStatus
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "Description."
            ::= { cardLedEntry 2 }

            
    END

--
-- ST-COMMON-MIB.mib
--

