import React, {useEffect, useRef, useState} from "react";
import Icon, {MenuUnfoldOutlined, MenuFoldOutlined} from "@ant-design/icons";
import {Layout, Menu, Button, theme, Breadcrumb, Space, Dropdown, Modal, Divider, message} from "antd";
import {Outlet, useLocation, useNavigate} from "react-router-dom";
import {useDispatch, useSelector} from "react-redux";
import {fetchLogout} from "@/store/modules/common/user_slice";
import {useRequest, useWebSocket} from "ahooks";
import {useForm} from "antd/es/form/Form";
import {
    alarm1Svg,
    alarm2Svg,
    alarm3Svg,
    alarm4Svg,
    collapsedLogoSvg,
    downSvg,
    helpSvg,
    ampconTSideSvg,
    upSvg,
    userSvg
} from "@/utils/common/iconSvg";
import {removeCssStyleByCssSelector} from "@/modules-ampcon/utils/util";
import {setNeNameMap} from "@/store/modules/otn/neNameSlice";
import {setOTNLanguage} from "@/store/modules/otn/languageOTNSlice";
import {
    deleteAlarm,
    deleteTNMSAlarm,
    newAlarm,
    newEvent,
    newTNMSAlarm,
    updateAlarm,
    updateDataChanged,
    updateEvent,
    updateNEAlarms,
    updateSwitchAlarm,
    updateTNMSAlarm,
    updateUpgrade
} from "@/store/modules/otn/notificationSlice";
import {UserEditModalForm} from "@/modules-ampcon/pages/System/user_modal";
import {getSwitchAlarm} from "@/modules-ampcon/apis/monitor_api";
import {sidebarItems} from "@/custom_modules";
import {objectGet, objectGetByTime} from "@/modules-otn/apis/api";
import {ALARM_COLOR} from "@/modules-otn/utils/util";
import {getOTNDeviceList} from "@/modules-ampcon/apis/otn";
import styles from "./layout.module.scss";

let upgradePath;
if (import.meta.env.VITE_APP_EXPORT_MODULE === "AmpCon-T") {
    upgradePath = {};
} else {
    upgradePath = {upgrade_management: "otn"};
}

const {Header, Sider, Content} = Layout;

const FSOTNLayout = () => {
    const currentUser = useSelector(state => state.user.userInfo);
    const {alarms, switchAlarms} = useSelector(state => state.notification);
    const [alarmCount, setAlarmCount] = useState({});
    const userType = currentUser?.type;
    const items = sidebarItems[userType] ? sidebarItems[userType] : sidebarItems.readonly;

    const alarmIconMap = {
        CRITICAL: alarm1Svg,
        MAJOR: alarm2Svg,
        MINOR: alarm3Svg,
        WARNING: alarm4Svg
    };

    const {
        token: {colorPrimary}
    } = theme.useToken();

    const navigate = useNavigate();
    const location = useLocation();
    const dispatch = useDispatch();

    // otn
    const webSocketKeepAliveTimerRef = useRef(null);
    const GetNeName = useRequest(
        async () => {
            return await objectGet("config:ne", {});
        },
        {manual: true}
    );
    const {protocol, host} = window.location;

    const {connect, disconnect, sendMessage} = useWebSocket(
        `ws${protocol === "http:" ? "" : "s"}://${host}/otn/api/ws`,
        {
            manual: true,
            reconnectLimit: 999999,
            onOpen: (event, socket) => {
                try {
                    // eslint-disable-next-line no-console
                    console.log("Websocket connected", socket.readyState);
                    if (socket.readyState !== 1) {
                        return;
                    }

                    if (webSocketKeepAliveTimerRef.current) {
                        clearInterval(webSocketKeepAliveTimerRef.current);
                        webSocketKeepAliveTimerRef.current = null;
                    }
                    webSocketKeepAliveTimerRef.current = setInterval(() => {
                        sendMessage("keepalive");
                    }, 60000);

                    objectGet("ne:alarm", {}).then(res => {
                        dispatch(updateAlarm(res.documents));
                    });

                    objectGet("nms:alarm", {}).then(res => {
                        const r = res.documents?.filter?.(item => item.value.data["time-cleared"] === "0");
                        dispatch(
                            updateTNMSAlarm(
                                r?.map?.(item => {
                                    return {
                                        ...item.value.data,
                                        ne_id: item.value.ne_id
                                    };
                                })
                            )
                        );
                    });

                    objectGetByTime(
                        "nms:event",
                        {},
                        {key: "time-created", start: sessionStorage.getItem("loginTime"), equals: true}
                    ).then(res => {
                        dispatch(
                            updateEvent(
                                res.map?.(item => {
                                    return {
                                        ...item.value.data,
                                        ne_id: item.value.ne_id
                                    };
                                })
                            )
                        );
                    });
                } catch (e) {
                    // console.log(e);
                }
            },
            onMessage: event => {
                const {type, source, data} = JSON.parse(event.data);
                switch (type) {
                    case "NOTICE":
                        message[!data.type ? "info" : data.type](data.message, 15).then();
                        break;
                    case "LICENSE":
                        logoutConfirm();
                        break;
                    case "NE_SESSION":
                        dispatch(updateUpgrade({source, data}));
                        break;
                    case "ALARM_NEW":
                        dispatch(newAlarm({source, data}));
                        break;
                    case "ALARM_DELETE":
                        dispatch(deleteAlarm({source, data}));
                        break;
                    case "EVENT":
                        dispatch(newEvent({source, data}));
                        break;
                    case "TNMS_ALARM_NEW":
                        dispatch(newTNMSAlarm({source, data}));
                        break;
                    case "TNMS_ALARM_DELETE":
                        dispatch(deleteTNMSAlarm({source, data}));
                        break;
                    case "DATA_CHANGED":
                        if (data.type === "NEW_NE_ADD") {
                            GetNeName.runAsync().then(r => {
                                dispatch(setNeNameMap(r.documents));
                            });
                        }
                        dispatch(updateDataChanged({data}));
                        break;
                    case "UPDATE_NE_ALARMS":
                        dispatch(updateNEAlarms({source, data}));
                        break;
                    default:
                        break;
                }
            },
            onClose: (event, socket) => {
                // eslint-disable-next-line no-console
                console.log("Websocket closed", socket.readyState);
                if (webSocketKeepAliveTimerRef.current) {
                    clearInterval(webSocketKeepAliveTimerRef.current);
                    webSocketKeepAliveTimerRef.current = null;
                }
            },
            onError: (event, socket) => {
                // eslint-disable-next-line no-console
                console.log("Websocket error", socket.readyState);
            }
        }
    );

    useEffect(() => {
        setAlarmCount(
            [...alarms, ...switchAlarms].reduce(
                (prev, cur) => {
                    prev[cur.severity]++;
                    return prev;
                },
                {CRITICAL: 0, MAJOR: 0, MINOR: 0, WARNING: 0}
            )
        );
    }, [alarms, switchAlarms]);

    useEffect(() => {
        const fetchData = async () => {
            try {
                // const currentTime = dayjs().format("YYYY-MM-DD HH:mm:ss");
                // const lastFetchTimeStored = localStorage.getItem("lastFetchTime");
                // const lastFetchTime = lastFetchTimeStored || currentTime;
                await getSwitchAlarm().then(res => {
                    if (res.status === 200) {
                        dispatch(updateSwitchAlarm(res.data));
                    } else {
                        // eslint-disable-next-line no-console
                        console.log("Error fetching data:", res.msg);
                    }
                });
                // localStorage.setItem("lastFetchTime", currentTime);
            } catch (error) {
                // eslint-disable-next-line no-console
                console.error("Error fetching data:", error);
            }
        };
        fetchData();
        const timer = setInterval(fetchData, 60000);
        return () => clearInterval(timer);
    }, []);

    const [openKeys, setOpenKeys] = useState([]);

    const fourthLevelDefaultTAB = {
        ansible_jobs_list: "job_view",
        config_template: userType === "readonly" ? "template_verify" : "new_template",
        ...upgradePath,
        l0_config: "optical-power-management",
        device_license_management: "license_audit",
        e2e_service_config: "create-business",
        performance: "current",
        performance_subscription: "sensor_group",
        time_management: "time_management",
        link_measure: "otdr",
        alarm: "current_alarm"
    };

    const onOpenChange = newOpenKeys => {
        // If only one menu item is allowed to expand, only the key of the last menu item is reserved
        const lastKey = newOpenKeys[newOpenKeys.length - 1];
        if (lastKey === undefined) {
            setOpenKeys(undefined);
        } else {
            const first_part_regex = `/${lastKey.match(/^\/(.+?)(?:\/|$)/)[1]}`;
            if (first_part_regex === newOpenKeys[0]) {
                setOpenKeys(newOpenKeys);
            } else if (first_part_regex !== lastKey) {
                setOpenKeys(undefined);
            } else {
                setOpenKeys([lastKey]);
            }
        }
    };

    const onMenuClick = item => {
        const path = fourthLevelDefaultTAB[item.key.split("/").pop()];
        if (path) {
            navigate(`${item.key}/${path}`);
        } else {
            navigate(item.key);
        }
        if (item.keyPath.length === 1) {
            setOpenKeys([item.key]);
        }
    };
    const currentVersion = useSelector(state => state.version.currentVersionInfo);
    const [collapsed, setCollapsed] = useState(false);
    const [isVersionOpen, setIsVersionOpen] = useState(false);

    // findOpenKeys =>  get parent path all list
    // findSelectedKey => get 4th parent level path single list
    const findOpenKeys = currentPath => {
        const keys = [];
        const pathArray = currentPath.split("/").filter(Boolean);
        let currentKey = "";
        for (let i = 0; i < pathArray.length - 1; i++) {
            currentKey += `/${pathArray[i]}`;
            keys.push(currentKey);
        }
        return keys;
    };

    const findSelectedKey = () => {
        const pathName = location.pathname;
        if (Object.keys(fourthLevelDefaultTAB).some(key => pathName.split("/").includes(key))) {
            return findOpenKeys(pathName).slice(-1);
        }
        return pathName;
    };

    useEffect(() => {
        // if the sidebar is collapsed, open the parent path, otherwise will do nothing
        if (!collapsed) {
            setOpenKeys(findOpenKeys(location.pathname));
        }
    }, [location.pathname, colorPrimary]);

    useEffect(() => {
        if (!localStorage.language) {
            localStorage.language = "en";
        }
        dispatch(setOTNLanguage(localStorage.language));
        GetNeName.runAsync().then(r => {
            dispatch(setNeNameMap(r.documents));
            // eslint-disable-next-line no-console
            console.log("websocket.connect()");
            connect();
        });

        getOTNDeviceList().then(rs => {
            if (rs.errorCode === 0) {
                const mappingData = rs.data.map(item => ({
                    value: {ne_id: item.ip, name: item.name}
                }));
                dispatch(setNeNameMap(mappingData));
            }
        });

        return () => {
            // eslint-disable-next-line no-console
            console.log("websocket.disconnect()");
            disconnect();
        };
    }, []);

    const getBreadcrumb = () => {
        const uppercaseWords = ["otn", "e2e", "wss", "tff", "oeo", "pmp", "otdr", "ocm", "ntp", "cli"];

        const pathItems = location.pathname
            .split("/")
            .filter(item => item !== "")
            .map(item => {
                let title = item.replace(/[_-]/g, " ");
                title = title.replace(/\b\w/g, c => c.toUpperCase());

                uppercaseWords.forEach(word => {
                    if (title.toLowerCase().includes(word)) {
                        title = title.replace(new RegExp(word, "gi"), word.toUpperCase());
                    }
                });

                return {title};
            });
        if (pathItems.length === 0) {
            pathItems.push({title: "Dashboard"});
        }
        return pathItems;
    };

    const logoutConfirm = () => {
        dispatch(fetchLogout());
    };

    const toHome = () => {
        navigate("/");
    };

    const userItems = [
        {
            label: <a>User Management</a>,
            key: "item-2",
            onClick: () => {
                setIsModalOpen(true);
                form.setFieldValue("username", currentUser.username);
            }
        },
        {
            label: <a>Version</a>,
            key: "item-1",
            onClick: () => {
                setIsVersionOpen(true);
            }
        },
        {
            label: <a>Logout</a>,
            key: "item-3",
            onClick: logoutConfirm
        }
    ];

    const [form] = useForm();
    const [isModalOpen, setIsModalOpen] = useState(false);
    const [hoverStatus, setHoverStatus] = useState(false);

    return (
        <Layout className={styles.layoutStyle}>
            <Modal title={currentVersion} open={isVersionOpen} onCancel={() => setIsVersionOpen(false)} footer={null}>
                <>
                    <Divider />
                    <ul>
                        <li>
                            <span>Release notes for AmpCon-T</span>
                        </li>
                    </ul>
                </>
            </Modal>

            <UserEditModalForm
                title="User Account Settings"
                isModalOpen={isModalOpen}
                setIsModalOpen={setIsModalOpen}
                formInstance={form}
                layoutProps={{
                    labelCol: {
                        span: 8
                    },
                    wrapperCol: {
                        span: 19
                    }
                }}
            />
            <Sider collapsed={collapsed} className={styles.themeBackground}>
                <div className={styles.logoContainer}>
                    {collapsed ? (
                        <Icon component={collapsedLogoSvg} />
                    ) : (
                        <Icon component={ampconTSideSvg} onClick={() => toHome()} />
                    )}
                </div>
                <Menu
                    mode="inline"
                    items={items}
                    className={styles.themeBackground}
                    onClick={onMenuClick}
                    openKeys={openKeys}
                    onOpenChange={onOpenChange}
                    selectedKeys={findSelectedKey()}
                />
            </Sider>
            <Layout>
                <Header className={styles.layoutHeader}>
                    <div className={styles.breadCrumb}>
                        <Button
                            type="text"
                            icon={collapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}
                            onClick={() => {
                                if (collapsed) {
                                    // set sidebar tooltip to visible
                                    const stylesheet = document.styleSheets[0];
                                    removeCssStyleByCssSelector(".ant-tooltip:has(.fixed-tooltip)");
                                    stylesheet.insertRule(
                                        ".ant-tooltip:has(.fixed-tooltip) { position: fixed; left: 210px !important; }",
                                        stylesheet.cssRules.length
                                    );
                                } else {
                                    // set sidebar tooltip to hidden
                                    const stylesheet = document.styleSheets[0];
                                    removeCssStyleByCssSelector(".ant-tooltip:has(.fixed-tooltip)");
                                    stylesheet.insertRule(
                                        ".ant-tooltip:has(.fixed-tooltip) { display: none; }",
                                        stylesheet.cssRules.length
                                    );
                                }
                                setCollapsed(!collapsed);
                            }}
                            className={styles.collapsedButton}
                        />
                        <Breadcrumb separator=">" className={styles.breadCrumbInternal} items={getBreadcrumb()} />
                        <div className={styles.iconList}>
                            <Space size="large" style={{columnGap: 16}}>
                                <>
                                    {Object.entries(ALARM_COLOR).map(([k]) => (
                                        <div
                                            className={styles.iconList_iconDiv}
                                            key={`alarm_${k}`}
                                            onClick={() => {
                                                navigate("/monitor/alarm/current_alarm", {
                                                    state: {
                                                        severity: [
                                                            `${k.charAt(0).toUpperCase() + k.slice(1).toLowerCase()}`
                                                        ]
                                                    }
                                                });
                                            }}
                                        >
                                            <div className={styles.iconList_icon}>
                                                <Icon component={alarmIconMap[k]} />
                                            </div>
                                            <div className={styles.iconList_label}>{alarmCount[k]}</div>
                                        </div>
                                    ))}
                                </>
                                <Dropdown
                                    menu={{items: userItems}}
                                    trigger={["hover"]}
                                    onOpenChange={val => setHoverStatus(val)}
                                >
                                    <div className={styles.iconList_iconUserDiv} style={{marginLeft: "8px"}}>
                                        <div className={styles.iconList_userLabel}>
                                            <Icon component={userSvg} />
                                            <span style={{marginLeft: "4px", marginRight: "4px"}}>
                                                {currentUser.username}
                                            </span>
                                        </div>
                                        <div className={styles.iconList_icon} style={{marginRight: "8px"}}>
                                            <Icon component={hoverStatus ? upSvg : downSvg} />
                                        </div>
                                    </div>
                                </Dropdown>
                                <div
                                    className={styles.helpSvg}
                                    onClick={() => {
                                        window.open(
                                            "https://resource.fs.com/mall/resource/ampcon-t-configuration-guide.pdf",
                                            "_blank"
                                        );
                                    }}
                                >
                                    <Icon component={helpSvg} />
                                </div>
                            </Space>
                        </div>
                    </div>
                </Header>
                <Content className={styles.contentContainer}>
                    <Outlet />
                </Content>
            </Layout>
        </Layout>
    );
};
export default FSOTNLayout;
