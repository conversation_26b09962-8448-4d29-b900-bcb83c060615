import configparser
from munch import Munch

CONF = Munch()
CONF.database = Munch()
CONF.mail = Munch()
CONF.map = Munch()

# init config
config = configparser.ConfigParser()
config.read("/usr/share/automation/server/automation.ini", encoding="utf-8")

default_config = dict((x, y) for x, y in config.items("DEFAULT"))
CONF.default_log_level = default_config.get("default_log_level", 20)
CONF.bind = default_config.get("bind", "0.0.0.0")
CONF.port = int(default_config.get("port", 5000))
CONF.workers = int(default_config.get("workers", 30))
CONF.license_fresh_interval = int(default_config.get("license_fresh_interval", 3600 * 24))
proxy_tmp = default_config.get("license_portal_proxy", "")

try:
    tmp_dict = {}
    if proxy_tmp:
        for i in proxy_tmp.split(","):
            tmp_dict[i.split(":", 1)[0]] = i.split(":", 1)[1]
    CONF.license_portal_proxy = tmp_dict
except:
    CONF.license_portal_proxy = {}
CONF.rma_interval = int(default_config.get("rma_interval", 3600 * 24))
CONF.verizon_feature = eval(default_config.get("verizon_feature", "False"))
CONF.ssl_private_key_path = default_config.get("ssl_private_key_path", None)
CONF.ssl_crt_path = default_config.get("ssl_crt_path", None)
CONF.no_auth_urls = [i.strip().replace("\n", "") for i in default_config.get("no_auth_urls", "").split(",")]
CONF.vpn_server_key = default_config.get("vpn_server_key", None)
CONF.vpn_server_crt = default_config.get("vpn_server_crt", None)
CONF.license_portal_cert_verify = eval(default_config.get("license_portal_cert_verify", "False"))
CONF.global_ip = default_config.get("global_ip", "")
CONF.vpn_keepalive = eval(default_config.get("vpn_keepalive", "True"))
CONF.vpn_enable = eval(default_config.get("vpn_enable", "True"))
CONF.display_log_cycle = int(default_config.get("display_log_cycle", 2))
CONF.supports_models = [i.strip().replace("\n", "") for i in
                        default_config.get("supports_models", "").split(",")]
CONF.mac_vlan_limit = int(default_config.get("mac_vlan_limit", 2000))
CONF.rabbitmq_pwd = default_config.get("rabbitmq_pwd", "admin")
CONF.prometheus_url = default_config.get("prometheus_url", "prometheus:9090")
CONF.prometheus_rules_path = default_config.get("prometheus_rules_path", "/usr/share/automation/server/monitor/rules")
CONF.prometheus_default_values = default_config.get("prometheus_default_values", "/usr/share/automation/server/monitor/prometheus_default_values.yml")


mail_config = dict((x, y) for x, y in config.items("mail"))
CONF.mail.server = mail_config.get("server", None)
CONF.mail.port = int(mail_config.get("port", 587))
CONF.mail.user = mail_config.get("user", None)
CONF.mail.password = mail_config.get("password", None)

map_config = dict((x, y) for x, y in config.items("map"))
CONF.map.geocoding_api = map_config.get("geocoding_api", 'osm')
CONF.map.nominatim_server_url = map_config.get("nominatim_server_url", "")
CONF.map.maxZoom = int(map_config.get("maxZoom", 12))
CONF.map.tileLayer = map_config.get("tileLayer", 'http://{s}.basemaps.cartocdn.com/light_all/{z}/{x}/{y}.png')
CONF.map.map_central_latlng = map_config.get("map_central_latlng", '37.431809,-122.103149').split(",")

database_config = dict((x, y) for x, y in config.items("database"))
CONF.database.connection = database_config.get("connection", '')
CONF.database.idle_timeout = int(database_config.get("idle_timeout", 3600))
CONF.database.min_pool_size = int(database_config.get("min_pool_size", 1))
CONF.database.max_pool_size = int(database_config.get("max_pool_size", 20))
CONF.database.max_overflow = int(database_config.get("max_overflow", 20))
CONF.database.connection_debug = int(database_config.get("connection_debug", 0))
CONF.database.connection_trace = eval(database_config.get("connection_trace", "False"))
CONF.database.pool_timeout = int(database_config.get("pool_timeout", 0))
