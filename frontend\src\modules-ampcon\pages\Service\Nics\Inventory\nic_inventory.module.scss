.nicInventoryTable {
    min-height: 100%;
    :global {
        .ant-table-row-expand-icon {
            border: 1px solid #ddd;
            color: #ddd !important;
            transform: none;
            &:hover {
                color: #14c9bb;
            }
        }

        .ant-table-row-expand-icon.ant-table-row-expand-icon-expanded {
            border-radius: 2px;
        }

        .ant-input-affix-wrapper .ant-input-clear-icon:hover {
            color: #14c9bb;
        }

        .ant-table-wrapper .ant-table-tbody .ant-table-row.ant-table-row-selected > .ant-table-cell {
            background: #fff;
        }
    }
}
