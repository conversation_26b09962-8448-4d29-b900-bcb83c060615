import {Graph} from "@antv/x6";
import {useEffect, useRef, useState, useMemo} from "react";
import {calculateVertices, unitTopoLayout} from "@/utils/topo_layout_utils";
// import FabricNode from "@/modules-ampcon/pages/Topo/fabric_node";
import {register} from "@antv/x6-react-shape?react";
import LeafNode from "./resource/leaf_node.svg?react";
import LeafEmpty from "./resource/leaf_empty.svg?react";
import AccessNode from "./resource/access_node.svg?react";
import AccessEmpty from "./resource/access_empty.svg?react";

const FabricTopo = ({unitNodes, podNodes, type}) => {
    const containerRef = useRef(null);
    const graphRef = useRef(null);

    // eslint-disable-next-line react/no-unstable-nested-components
    const FabricNode = ({node}) => {
        const [isEmpty, setIsEmpty] = useState(node?.store?.data?.isempty);
        const {type = "unknown", name = "Unnamed Node"} = node?.store?.data || {};

        const getNodeIcon = useMemo(() => {
            if (type === "leaf") {
                return isEmpty ? <LeafEmpty /> : <LeafNode />;
            }
            if (type === "access") {
                return isEmpty ? <AccessEmpty /> : <AccessNode />;
            }
            return null;
        }, [type, isEmpty]);

        const handleClick = () => {
            setIsEmpty(prevState => !prevState);
            node.store.data.isempty = !isEmpty;
        };

        return (
            <div
                onClick={handleClick}
                style={{
                    display: "flex",
                    justifyContent: "center",
                    alignItems: "flex-start",
                    height: "100%",
                    width: "100%"
                }}
            >
                <div
                    style={{
                        display: "flex",
                        flexDirection: "column",
                        justifyContent: "center",
                        alignItems: "center",
                        textAlign: "center"
                    }}
                >
                    {getNodeIcon}
                    <div style={{fontFamily: "Lato", fontSize: "14px"}}>{name}</div>
                </div>
            </div>
        );
    };

    const handleRestricted = ({graph, cell}) => {
        const splitter1 = graph.getCellById("splitter1");

        if (!splitter1) {
            return {x: -9000, y: -9000, width: 18000, height: 18000};
        }

        const splitter1Point = splitter1.getSourcePoint?.();

        if (!splitter1Point) {
            return {x: -9000, y: -9000, width: 18000, height: 18000};
        }

        if (cell.store.data.type === "leaf") {
            return {
                x: -9000,
                y: -9000,
                width: 9000 + 9000,
                height: splitter1Point.y + 9000
            };
        }

        if (cell.store.data.type === "access") {
            return {
                x: -9000,
                y: splitter1Point.y,
                width: 18000,
                height: 9000
            };
        }
    };

    const updateAllEdgesVertices = () => {
        graphRef.current.getEdges().forEach(edge => {
            if (edge.id === "splitter1") return;
            const sourceNode = graphRef.current.getCellById(edge.store.data.source.cell);
            const targetNode = graphRef.current.getCellById(edge.store.data.target.cell);

            const sourceNodeBBox = sourceNode.getBBox();
            const targetNodeBBox = targetNode.getBBox();
            edge.setVertices(
                calculateVertices(
                    {
                        x: sourceNodeBBox.x + sourceNodeBBox.width / 2 - 20,
                        y: sourceNodeBBox.y + sourceNodeBBox.height / 2
                    },
                    {
                        x: targetNodeBBox.x + targetNodeBBox.width / 2 - 20,
                        y: targetNodeBBox.y + targetNodeBBox.height / 2
                    }
                )
            );
        });
    };

    const registerCustomNode = () => {
        register({
            shape: "leaf-node",
            height: 60,
            width: 60,
            // eslint-disable-next-line react/no-unstable-nested-components
            component: props => (
                <FabricNode
                    {...props}
                    node={{
                        store: {
                            data: {
                                type: "leaf",
                                isempty: true,
                                name: props.node?.store?.data?.name || "Leaf Node"
                            }
                        }
                    }}
                />
            )
        });

        register({
            shape: "access-node",
            height: 60,
            width: 60,
            // eslint-disable-next-line react/no-unstable-nested-components
            component: props => (
                <FabricNode
                    {...props}
                    node={{
                        store: {
                            data: {
                                type: "access",
                                isempty: true,
                                name: props.node?.store?.data?.name || "Access Node"
                            }
                        }
                    }}
                />
            )
        });
    };

    useEffect(() => {
        graphRef.current = new Graph({
            container: containerRef.current,
            width: containerRef.current.clientWidth,
            height: containerRef.current.clientHeight,

            grid: true,
            translating: {
                restrict: handleRestricted
            },
            rotating: {
                enabled: true
            },
            panning: {
                enabled: true,
                eventTypes: ["leftMouseDown", "mouseWheel"]
            },
            interacting: {
                nodeMovable: false,
                edgeMovable: false
            },
            connecting: {
                connector: {
                    name: "smooth"
                }
            },
            mousewheel: {
                enabled: true,
                modifiers: "ctrl"
            }
        });

        let x = 100;
        const y = 100;
        let dottedFrameId = 1;
        let dottedFrameX = 70;
        const dottedFrameY = 70;
        let width;
        const height = 280;
        registerCustomNode();

        if (type === "5-stage") {
            if (podNodes?.length > 0) {
                podNodes.forEach(podNode => {
                    podNode.unit.forEach(unitGroup => {
                        const leafNodes = unitGroup.unit_info?.leaf;
                        const accessNodes = unitGroup.unit_info?.access;
                        const unitInfo = {leafNodes, accessNodes};
                        const point = {x, y};
                        const isRenderDottedFrame = unitTopoLayout(graphRef.current, point, unitInfo);

                        if (isRenderDottedFrame) {
                            if (leafNodes === undefined && accessNodes === undefined) {
                                return;
                            }
                            let realAccessNodeNum = 0;
                            accessNodes.forEach(node => {
                                if (node?.name !== "") {
                                    realAccessNodeNum++;
                                }
                            });
                            let realLeafNodeNum = 0;
                            leafNodes.forEach(node => {
                                if (node?.name !== "") {
                                    realLeafNodeNum++;
                                }
                            });
                            const realNodeNumMax = Math.max(realAccessNodeNum, realLeafNodeNum);

                            width = 60 + realNodeNumMax * 120 + (realNodeNumMax - 1) * 50;

                            graphRef.current.addNode({
                                id: `group-${dottedFrameId++}`,
                                shape: "rect",
                                label: unitGroup.name,
                                x: dottedFrameX,
                                y: dottedFrameY,
                                width,
                                height,
                                attrs: {
                                    body: {
                                        borderRadius: 4,
                                        fill: "none",
                                        stroke: "grey",
                                        strokeWidth: 2,
                                        strokeDasharray: "4,5"
                                    }
                                },
                                zIndex: 1
                            });
                            x += width + 80;
                            dottedFrameX = x - 30;
                        }
                    });
                });
            }
        }

        if (type === "3-stage") {
            if (unitNodes?.length > 0) {
                unitNodes.forEach(unitGroup => {
                    const leafNodes = unitGroup.unit_info?.leaf;
                    const accessNodes = unitGroup.unit_info?.access;
                    const unitInfo = {leafNodes, accessNodes};
                    const point = {x, y};
                    const isRenderDottedFrame = unitTopoLayout(graphRef.current, point, unitInfo);

                    if (isRenderDottedFrame) {
                        if (leafNodes === undefined && accessNodes === undefined) {
                            return;
                        }
                        let realAccessNodeNum = 0;
                        accessNodes.forEach(node => {
                            if (node?.name !== "") {
                                realAccessNodeNum++;
                            }
                        });
                        let realLeafNodeNum = 0;
                        leafNodes.forEach(node => {
                            if (node?.name !== "") {
                                realLeafNodeNum++;
                            }
                        });
                        const realNodeNumMax = Math.max(realAccessNodeNum, realLeafNodeNum);

                        width = 60 + realNodeNumMax * 120 + (realNodeNumMax - 1) * 50;

                        graphRef.current.addNode({
                            id: `group-${dottedFrameId++}`,
                            shape: "rect",
                            label: unitGroup.name,
                            x: dottedFrameX,
                            y: dottedFrameY,
                            width,
                            height,
                            attrs: {
                                body: {
                                    borderRadius: 4,
                                    fill: "none",
                                    stroke: "grey",
                                    strokeWidth: 2,
                                    strokeDasharray: "4,5"
                                }
                            },
                            zIndex: 1
                        });
                        x += width + 80;
                        dottedFrameX = x - 30;
                    }
                });
            }
        }

        graphRef.current.addEdges([
            {
                id: "splitter1",
                sourcePoint: {x: -9000, y: 200}, // 分割线1的起始点
                targetPoint: {x: 9000, y: 200}, // 分割线1的结束点
                attrs: {
                    line: {
                        stroke: "#B4BECD", // 分割线1的颜色
                        strokeWidth: 1, // 分割线1的宽度
                        strokeDasharray: "10, 5" // 分割线1的样式
                    }
                }
            }
        ]);

        graphRef.current.getNodes().forEach(node => {
            node.setParent(null);
            node.setChildren([]);

            const {type, isempty, name} = node.store.data;

            node.store.data.shape = type === "leaf" ? "leaf-node" : "access-node";

            node.store.data.isempty = isempty;
            node.store.data.name = name;
        });

        updateAllEdgesVertices();

        graphRef.current.on("node:change:position", () => {
            updateAllEdgesVertices();
        });
        graphRef.current.on("node:click", ({node}) => {
            if (node && node.store?.data) {
                node.store.data.isempty = false;
                node.setData(node.store.data);
            }
        });

        return () => {
            graphRef.current.dispose();
        };
    }, [unitNodes, podNodes, type]);

    return (
        <div
            ref={containerRef}
            style={{
                flex: "1",
                width: "100%",
                height: "100%"
            }}
        />
    );
};

export default FabricTopo;
