import React, {useEffect, useState} from "react";
import {Tabs} from "antd";
import {useLocation, useNavigate} from "react-router-dom";
import TemplateList from "@/modules-ampcon/pages/Service/Switch/ConfigTemplate/TemplateList/template_list";
import TemplateVerify from "@/modules-ampcon/pages/Service/Switch/ConfigTemplate/TemplateVerify/template_verify";
import PushConfig from "@/modules-ampcon/pages/Service/Switch/ConfigTemplate/PushConfig/push_config";
import {useSelector} from "react-redux";
import {isRouteForbidden} from "@/modules-ampcon/utils/util";
import ForbiddenPage from "@/modules-ampcon/pages/ForbiddenPage";
import ConfigSnapshotDiff from "./ConfigSnapshotDiff/confg_snapshot_diff";
import ConfigBackup from "./ConfigBackup/config_backup";
import NewTemplate from "../NewTemplate/new_template";

const ConfigTemplateIndex = () => {
    const currentUser = useSelector(state => state.user.userInfo);
    const userType = currentUser?.type;

    let items = [
        {
            key: "new_template",
            label: "New Template",
            children: <NewTemplate />
        },
        {
            key: "template_list",
            label: "Template List",
            children: <TemplateList />
        },
        {
            key: "push_config",
            label: "Push Config",
            children: <PushConfig />
        },
        {
            key: "template_verify",
            label: "Template Verify",
            children: <TemplateVerify />
        },
        {
            key: "config_snapshot_diff",
            label: "Config Snapshot Diff",
            children: <ConfigSnapshotDiff />
        },
        {
            key: "config_backup",
            label: "Config Backup",
            children: <ConfigBackup />
        }
    ];
    items =
        currentUser.type === "readonly"
            ? items.filter(
                  item =>
                      item.key !== "new_template" &&
                      item.key !== "template_list" &&
                      item.key !== "push_config" &&
                      item.key !== "config_backup"
              )
            : items;

    const navigate = useNavigate();
    const location = useLocation();
    const [currentActiveKey, setCurrentActiveKey] = useState();
    const pathReg =
        userType === "readonly"
            ? /(template_verify|config_snapshot_diff)$/
            : /(new_template|template_list|push_config|template_verify|config_snapshot_diff|config_backup)$/;

    useEffect(() => {
        const currentPath = location.pathname;
        if (pathReg.test(currentPath)) {
            setCurrentActiveKey(currentPath.match(pathReg)[0]);
        }
    }, [location.pathname]);

    const onChange = key => {
        const currentPath = location.pathname;
        if (pathReg.test(currentPath)) {
            const matchLength = currentPath.match(pathReg)[0].length;
            const parentPath = currentPath.slice(0, -matchLength);
            navigate(parentPath + key);
        }
    };

    if (isRouteForbidden(location.pathname, userType)) {
        return <ForbiddenPage />;
    }

    return (
        <div style={{display: "flex", flex: 1}}>
            <Tabs
                activeKey={currentActiveKey}
                items={items}
                onChange={onChange}
                style={{flex: 1}}
                destroyInactiveTabPane
            />
        </div>
    );
};

export default ConfigTemplateIndex;
