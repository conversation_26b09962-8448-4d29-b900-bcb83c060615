import Device from "@/modules-otn/pages/otn/device/device";
import Performance from "@/modules-otn/pages/otn/pm/performance";
import Subscription from "@/modules-otn/pages/otn/pm_subscription/subscription_view";
import LinkMeasurement from "@/modules-otn/pages/otn/maintenance/link_measurement";
import Database from "@/modules-otn/pages/otn/device/database";
import Logs from "@/modules-otn/pages/otn/device/logs";
import Business from "@/modules-otn/pages/otn/service/business";
import AlarmTabs from "@/modules-otn/pages/otn/alarm/alarm_tabs";
import ServiceLayer0 from "@/modules-otn/pages/otn/service/service_layer0";
import ServiceLayer1 from "@/modules-otn/pages/otn/service/service_layer1";
import TimeManagement from "@/modules-otn/pages/otn/system/time_manage/time_management";
import OpenConfig from "@/modules-otn/pages/otn/maintenance/open_config";
import SoftwareLisence from "@/modules-ampcon/pages/System/SoftwareLicense/software_license";

let otnConfig;
if (import.meta.env.VITE_APP_EXPORT_MODULE === "AmpCon-T") {
    otnConfig = [
        {
            path: "service/l0_config/:type",
            element: <ServiceLayer0 />
        },
        {
            path: "service/l1_config",
            element: <ServiceLayer1 />
        },
        {
            path: "service/e2e_service_config/:type",
            element: <Business />
        },
        {
            path: "maintain/database_manager",
            element: <Database />
        },
        {
            path: "maintain/log_manager",
            element: <Logs />
        },
        {
            path: "maintain/link_measure/:type",
            element: <LinkMeasurement />
        },
        {
            path: "maintain/openconfig_model",
            element: <OpenConfig />
        }
    ];
} else {
    otnConfig = [
        {
            path: "service/otn/l0_config/:type",
            element: <ServiceLayer0 />
        },
        {
            path: "service/otn/l1_config",
            element: <ServiceLayer1 />
        },
        {
            path: "service/otn/e2e_service_config/:type",
            element: <Business />
        },
        {
            path: "maintain/transport_config/database_manager",
            element: <Database />
        },
        {
            path: "maintain/transport_config/log_manager",
            element: <Logs />
        },
        {
            path: "maintain/transport_config/link_measure/:type",
            element: <LinkMeasurement />
        },
        {
            path: "maintain/transport_config/openconfig_model",
            element: <OpenConfig />
        }
    ];
}

export const routeOTN = [
    ...otnConfig,
    {
        path: "resource/device_view",
        element: <Device />
    },
    {
        path: "monitor/alarm/:type",
        element: <AlarmTabs />
    },
    {
        path: "monitor/performance/:type",
        element: <Performance />
    },
    {
        path: "monitor/performance_subscription/:type",
        element: <Subscription />
    },
    {
        path: "system/time_management/:type",
        element: <TimeManagement />
    },
    {
        path: "system/software_license",
        element: <SoftwareLisence />
    }
];
