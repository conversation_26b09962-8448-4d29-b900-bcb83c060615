import {ampconDCSideSvg, ampconTSideSvg, bgRightSvgDC, bgRightSvgT, ampconCAMPUSSideSvg} from "@/utils/common/iconSvg";
import {Navigate} from "react-router-dom";
import UpgradeManagement from "@/pages/Resource/UpgradeManagement/upgrade_management";
import ProtectedRoute from "@/modules-ampcon/utils/util";
import UserManagement from "@/modules-ampcon/pages/System/user_management";
import CLIConfiguration from "@/modules-ampcon/pages/Maintain/CliConfig/cli_config";
import AmpconGlobalView from "@/modules-ampcon/pages/Dashboard/GlobalView/global_view";
import GlobalView from "@/modules-otn/pages/Dashboard/global_view";
import OtnView from "@/modules-otn/pages/Dashboard/otn_view";

import sideBarEnable from "@/modules-otn/pages/Layout/sidebar_items";
import sideBarDisable from "@/modules-otn/pages/Layout/sidebar_items_disable_ampcon";
import sideBarNew from "@/modules-ampcon/pages/Layout/sidebar_items";

import {ampConRoute, ampConDCRoute} from "@/modules-ampcon/router/route_ampcon";
import {routeOTN} from "@/modules-otn/router/route_otn";

const getModules = () => {
    let sidebarItems;
    let sideSvg;
    let bgRightSvg;
    let customTitle;
    let customRoute = [];
    let defaultRoute = [
        {
            path: "/",
            element: <Navigate to="/dashboard/global_view" replace />
        },
        {
            path: "/index",
            element: <Navigate to="/dashboard/global_view" replace />
        }
    ];

    let extraRoute = [];

    switch (import.meta.env.VITE_APP_EXPORT_MODULE) {
        case "AmpCon-SUPER":
            customTitle = "AmpCon-T";
            sideSvg = ampconTSideSvg;
            bgRightSvg = bgRightSvgT;

            extraRoute = [
                {
                    path: "dashboard/global_view",
                    element: <GlobalView />
                },
                {
                    path: "dashboard/otn_view",
                    element: <OtnView />
                },
                {
                    path: "resource/upgrade_management/:type",
                    element: <UpgradeManagement />
                }
            ];

            sidebarItems = sideBarEnable;
            customRoute = [...extraRoute, ...ampConRoute, ...routeOTN];
            break;
        case "AmpCon-T":
            customTitle = "AmpCon-T";
            sideSvg = ampconTSideSvg;
            bgRightSvg = bgRightSvgT;

            defaultRoute = [
                {
                    path: "/",
                    element: <Navigate to="/dashboard" replace />
                },
                {
                    path: "/index",
                    element: <Navigate to="/dashboard" replace />
                }
            ];

            extraRoute = [
                {
                    path: "system/user_management",
                    element: <ProtectedRoute component={UserManagement} />
                },
                {
                    path: "maintain/cli_configuration",
                    element: <ProtectedRoute component={CLIConfiguration} />
                },
                {
                    path: "dashboard",
                    element: <ProtectedRoute component={OtnView} />
                },
                {
                    path: "resource/upgrade_management",
                    element: <UpgradeManagement />
                }
            ];

            customRoute = [...extraRoute, ...routeOTN];
            sidebarItems = sideBarDisable;
            break;
        case "AmpCon-DC":
            customTitle = "AmpCon-DC";
            sideSvg = ampconDCSideSvg;
            bgRightSvg = bgRightSvgDC;

            extraRoute = [
                {
                    path: "dashboard/global_view",
                    element: <ProtectedRoute component={AmpconGlobalView} />
                },
                {
                    path: "resource/upgrade_management",
                    element: <ProtectedRoute component={UpgradeManagement} />
                }
            ];

            sidebarItems = sideBarNew;
            customRoute = [...extraRoute, ...ampConDCRoute];
            break;
        case "AmpCon-CAMPUS":
            customTitle = "AmpCon-Campus";
            sideSvg = ampconCAMPUSSideSvg;
            bgRightSvg = bgRightSvgDC;

            extraRoute = [
                {
                    path: "dashboard/global_view",
                    element: <ProtectedRoute component={AmpconGlobalView} />
                },
                {
                    path: "resource/upgrade_management",
                    element: <ProtectedRoute component={UpgradeManagement} />
                }
            ];

            sidebarItems = sideBarNew;
            customRoute = [...extraRoute, ...ampConDCRoute];
            break;
        default:
            break;
    }

    return {
        sidebarItems,
        sideSvg,
        bgRightSvg,
        customRoute,
        defaultRoute,
        customTitle
    };
};

export const {sidebarItems, sideSvg, bgRightSvg, customRoute, defaultRoute, customTitle} = getModules();
