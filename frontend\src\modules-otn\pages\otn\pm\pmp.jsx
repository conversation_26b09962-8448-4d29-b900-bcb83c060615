import {useState, useEffect, useRef, useMemo} from "react";
import {useSelector} from "react-redux";
import {message, Switch, Form} from "antd";
import {netconfGetByXML, objectGet, netconfByXML} from "@/modules-otn/apis/api";
import {sortArr} from "@/modules-otn/utils/util";
import useUserRight from "@/modules-otn/hooks/useUserRight";
import {CustomTable} from "@/modules-otn/components/common/custom_table";
import {useRequest} from "ahooks";
import SelectLoading from "@/modules-otn/components/common/select_loading";
import styles from "./pmp.module.scss";
import MutilColumnForm from "../common/mutil_column_form";
import {getOTNDeviceIP} from "@/modules-ampcon/apis/otn";

const Pmp = () => {
    const {labelList} = useSelector(state => state.languageOTN);
    const [selectNe, setSelectNe] = useState();
    const [commonData, setCommonData] = useState({allData: {}});
    const [dataSource, setDataSource] = useState([]);

    const [form] = Form.useForm();
    const formRef = useRef();
    const [filter, setFilter] = useState({ne: undefined, slot: undefined, port: undefined});
    const filterDataSource = useMemo(() => {
        if (!filter.slot && !filter.port) return dataSource;
        return dataSource.filter(item => {
            let matchSlot = true,
                matchPort = true;
            const [, itemChassis, itemSlot, itemPort] = item["pm-point"].split("-");
            if (filter.slot) {
                const [, chassis, slot] = filter.slot.split("-");
                matchSlot = itemChassis === chassis && itemSlot === slot;
            }
            if (filter.port) {
                const [, chassis, slot, port] = filter.port.split("-");
                matchSlot = itemChassis === chassis && itemSlot === slot && itemPort === port;
            }
            return matchSlot && matchPort;
        });
    }, [filter, dataSource]);

    const userRight = useUserRight();

    const tableType = "pmp";

    const {run, refresh, loading} = useRequest(
        ne_id => {
            return netconfGetByXML({
                ne_id,
                msg: true,
                xml: {
                    performance: {
                        $: {
                            xmlns: "http://openconfig.net/yang/performance"
                        },
                        pmps: {}
                    }
                }
            });
        },
        {
            manual: true,
            onSuccess: (rs, params) => {
                const [ne_id] = params;
                if (Array.isArray(rs?.performance?.pmps?.pmp)) {
                    const pmpData = rs.performance.pmps.pmp.map(d => ({...d, key: `${ne_id}-${d["pm-point"]}`}));
                    setDataSource(pmpData);
                } else {
                    setDataSource([]);
                }
            }
        }
    );

    const PmpFormat = {
        // eslint-disable-next-line react/no-unstable-nested-components
        "pm-point-enable": (val, record) => {
            return (
                <LoadingSwitch
                    key={`${record["pm-point"]}pmp`}
                    type={tableType}
                    value={val}
                    record={record}
                    refresh={refresh}
                    commonData={commonData}
                    setCommonData={setCommonData}
                    disabled={
                        (commonData?.[tableType]?.selectedRows?.length > 0 &&
                            !commonData?.[tableType]?.selectedRows?.some(
                                item => item?.["pm-point"] === record["pm-point"]
                            )) ||
                        userRight.disabled
                    }
                    api={newVal => {
                        const pmps = [];
                        if (commonData?.[tableType]?.selectedRows?.length > 0) {
                            commonData?.[tableType]?.selectedRows?.forEach(selectedRow => {
                                pmps.push({
                                    pmp: {
                                        "pm-point": selectedRow["pm-point"],
                                        "pm-point-enable": `${newVal}`
                                    }
                                });
                            });
                        } else {
                            pmps.push({
                                pmp: {
                                    "pm-point": record["pm-point"],
                                    "pm-point-enable": `${newVal}`
                                }
                            });
                        }

                        return netconfByXML({
                            ne_id: filter.ne,
                            xml: {
                                performance: {
                                    $: {
                                        xmlns: "http://openconfig.net/yang/performance"
                                    },
                                    pmps
                                }
                            }
                        });
                    }}
                />
            );
        },
        // eslint-disable-next-line react/no-unstable-nested-components
        "tca-enable": (val, record) => {
            return (
                <LoadingSwitch
                    key={`${record["pm-point"]}tca`}
                    type={tableType}
                    value={val}
                    record={record}
                    refresh={refresh}
                    commonData={commonData}
                    setCommonData={setCommonData}
                    disabled={
                        (commonData?.[tableType]?.selectedRows?.length > 0 &&
                            !commonData?.[tableType]?.selectedRows?.some(
                                item => item?.["pm-point"] === record["pm-point"]
                            )) ||
                        userRight.disabled
                    }
                    api={newVal => {
                        const pmps = [];
                        if (commonData?.[tableType]?.selectedRows?.length > 0) {
                            commonData?.[tableType]?.selectedRows?.forEach(selectedRow => {
                                pmps.push({
                                    pmp: {
                                        "pm-point": selectedRow["pm-point"],
                                        "tca-enable": `${newVal}`
                                    }
                                });
                            });
                        } else {
                            pmps.push({
                                pmp: {
                                    "pm-point": record["pm-point"],
                                    "tca-enable": `${newVal}`
                                }
                            });
                        }

                        return netconfByXML({
                            ne_id: filter.ne,
                            xml: {
                                performance: {
                                    $: {
                                        xmlns: "http://openconfig.net/yang/performance"
                                    },
                                    pmps
                                }
                            }
                        });
                    }}
                />
            );
        }
    };

    return (
        <div style={{width: "100%"}} className={styles.container}>
            <div style={{width: "100%", marginBottom: 8}}>
                <MutilColumnForm
                    ref={formRef}
                    fields={[
                        {
                            name: "ne",
                            label: labelList.ne,
                            rules: [{required: true}],
                            render: (
                                <SelectLoading
                                    placeholder={labelList.select_ne}
                                    style={{width: 280}}
                                    fetchData={async () => {
                                        const [configNeRes, otnRes] = await Promise.all([
                                            objectGet("config:ne", {}),
                                            getOTNDeviceIP("ALL", {layer: "performance"})
                                        ]);

                                        if (configNeRes.apiResult === "fail") {
                                            throw new Error(configNeRes.apiMessage);
                                        }

                                        const configData = (configNeRes.documents || []).map(item => ({
                                            label: item.value.name,
                                            value: item.value.ne_id,
                                            state: item?.value.state,
                                            type: item?.value.type || "default"
                                        }));

                                        const otnData = (otnRes?.data || []).map(item => ({
                                            label: `${item.name}`,
                                            value: item.ip,
                                            state: 1,
                                            type: item.type
                                        }));

                                        const merged = [...configData, ...otnData].map(item => ({
                                            label: item.label,
                                            value: item.value,
                                            type: item.type,
                                            disabled: !(item?.state === 1 || item?.state < 0)
                                        }));

                                        return sortArr(merged, ["label"]);
                                    }}
                                    // fetchData={() => {
                                    //     return objectGet("config:ne", {}).then(rs => {
                                    //         const {apiResult, documents, apiMessage} = rs;
                                    //         if (apiResult === "fail") {
                                    //             throw new Error(apiMessage);
                                    //         }
                                    //         return sortArr(documents, ["value", "name"]).map(item => ({
                                    //             label: item.value.name,
                                    //             value: item.value.ne_id,
                                    //             disabled: !(item?.value.state === 1 || item?.value.state < 0)
                                    //         }));
                                    //     });
                                    // }}
                                    onChange={ne => {
                                        const newFilter = {ne, slot: undefined, port: undefined};
                                        setFilter(newFilter);
                                        formRef.current.form.setFieldsValue(newFilter);
                                        run(ne);
                                    }}
                                />
                            )
                        },
                        {
                            name: "slot",
                            label: labelList.slot,
                            render: (
                                <SelectLoading
                                    allowClear
                                    placeholder={labelList.please_select}
                                    dependence={dataSource}
                                    style={{width: 280}}
                                    fetchData={() => {
                                        const options = dataSource
                                            .reduce((pre, item) => {
                                                const [, chassis, slot] = item["pm-point"].split("-");
                                                const card = chassis && slot ? `SLOT-${chassis}-${slot}` : null;
                                                if (card && !pre.some(f => f.value === card)) {
                                                    pre.push({label: card, value: card});
                                                }
                                                return pre;
                                            }, [])
                                            .sort((a, b) => a.label.localeCompare(b.label, "ZH-CN", {numeric: true}));
                                        return options;
                                    }}
                                    onChange={slot => {
                                        const {form} = formRef.current;
                                        const newFilter = {...filter, slot, port: undefined};
                                        form.setFieldsValue(newFilter);
                                        setFilter(newFilter);
                                    }}
                                />
                            )
                        },
                        {
                            name: "port",
                            label: labelList.port,
                            render: (
                                <SelectLoading
                                    allowClear
                                    style={{width: 280}}
                                    placeholder={labelList.please_select}
                                    dependence={[filter.slot, dataSource]}
                                    fetchData={() => {
                                        const {form} = formRef.current;
                                        const options = filterCard(dataSource, form.getFieldValue("slot"))
                                            .reduce((pre, item) => {
                                                // const [, chassis, slot, port, subPort] = item["pm-point"]?.split("-");
                                                const pmPoint = item["pm-point"];
                                                const [, chassis, slot, port, subPort] = pmPoint
                                                    ? pmPoint.split("-")
                                                    : [];
                                                const portName =
                                                    chassis && slot && port && !subPort
                                                        ? ["PORT", chassis, slot, port].join("-")
                                                        : null;
                                                if (portName && !pre.find(f => f.value === portName)) {
                                                    pre.push({label: portName, value: portName});
                                                }
                                                return pre;
                                            }, [])
                                            .sort((a, b) => a.label.localeCompare(b.label, "ZH-CN", {numeric: true}));
                                        return options;
                                    }}
                                    onChange={port => {
                                        setFilter({...filter, port});
                                    }}
                                />
                            )
                        }
                    ]}
                />
            </div>
            <CustomTable
                initHead
                type={tableType}
                scroll={false}
                initDataSource={filterDataSource}
                loading={loading}
                commonData={commonData}
                setCommonData={setCommonData}
                columnFormat={PmpFormat}
                refreshParent={refresh}
                refreshDisabled={!filter.ne}
            />
        </div>
    );
};

export default Pmp;

const LoadingSwitch = ({api, value, record, refresh, commonData, setCommonData, type, ...rest}) => {
    const {labelList} = useSelector(state => state.languageOTN);
    const [val, setVal] = useState(value === "true");
    const {run, loading} = useRequest(api, {
        manual: true,
        onSuccess(response, params) {
            if (response.result) {
                message.success(labelList.save_success);
                if (commonData?.[type]?.selectedRows?.length > 0) {
                    refresh();
                } else {
                    setVal(params[0]);
                }
            } else {
                message
                    .error({
                        content: response.apiMessage,
                        key: "error2",
                        duration: 0,
                        onClick: () => message.destroy("error2")
                    })
                    .then();
                refresh();
            }
            setCommonData({});
        }
    });

    const onChange = newVal => {
        run(newVal);
    };

    useEffect(() => {
        setVal(value === "true");
    }, [value, record]);

    return <Switch loading={loading} checked={val} onChange={onChange} {...rest} />;
};

const filterCard = (data, card) => {
    if (!card) return data;
    return data.filter(item => {
        const [, chassis, slot] = item["pm-point"].split("-");
        const [, cardChassis, cardSlot] = card.split("-");
        return (!slot && item["pm-point"] === card) || (chassis === cardChassis && slot === cardSlot);
    });
};
