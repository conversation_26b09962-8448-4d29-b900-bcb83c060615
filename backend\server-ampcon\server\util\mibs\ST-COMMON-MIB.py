# SNMP MIB module (ST-COMMON-MIB) expressed in pysnmp data model.
#
# This Python module is designed to be imported and executed by the
# pysnmp library.
#
# See https://www.pysnmp.com/pysnmp for further information.
#
# Notes
# -----
# ASN.1 source file://./ST-COMMON-MIB.mib
# Produced by pysmi-1.5.11 at Tue Apr 22 03:03:06 2025
# On host pica8 platform Linux version 5.15.0-122-generic by user root
# Using Python version 3.11.10 (main, Sep  7 2024, 18:35:41) [GCC 11.4.0]

if 'mibBuilder' not in globals():
    import sys

    sys.stderr.write(__doc__)
    sys.exit(1)

# Import base ASN.1 objects even if this MIB does not use it

(Integer,
 OctetString,
 ObjectIdentifier) = mibBuilder.importSymbols(
    "ASN1",
    "Integer",
    "OctetString",
    "ObjectIdentifier")

(NamedValues,) = mibBuilder.importSymbols(
    "ASN1-E<PERSON>MERATION",
    "NamedValues")
(ConstraintsIntersection,
 ConstraintsUnion,
 SingleValueConstraint,
 ValueRangeConstraint,
 ValueSizeConstraint) = mibBuilder.importSymbols(
    "ASN1-REFINEMENT",
    "ConstraintsIntersection",
    "ConstraintsUnion",
    "SingleValueConstraint",
    "ValueRangeConstraint",
    "ValueSizeConstraint")

# Import SMI symbols from the MIBs this MIB depends on

(usmUserName,) = mibBuilder.importSymbols(
    "SNMP-USER-BASED-SM-MIB",
    "usmUserName")

(ModuleCompliance,
 NotificationGroup) = mibBuilder.importSymbols(
    "SNMPv2-CONF",
    "ModuleCompliance",
    "NotificationGroup")

(Bits,
 Counter32,
 Counter64,
 Gauge32,
 Integer32,
 IpAddress,
 ModuleIdentity,
 MibIdentifier,
 NotificationType,
 ObjectIdentity,
 MibScalar,
 MibTable,
 MibTableRow,
 MibTableColumn,
 TimeTicks,
 Unsigned32,
 iso) = mibBuilder.importSymbols(
    "SNMPv2-SMI",
    "Bits",
    "Counter32",
    "Counter64",
    "Gauge32",
    "Integer32",
    "IpAddress",
    "ModuleIdentity",
    "MibIdentifier",
    "NotificationType",
    "ObjectIdentity",
    "MibScalar",
    "MibTable",
    "MibTableRow",
    "MibTableColumn",
    "TimeTicks",
    "Unsigned32",
    "iso")

(DateAndTime,
 DisplayString,
 MacAddress,
 PhysAddress,
 RowStatus,
 TAddress,
 TextualConvention,
 TruthValue,
 VariablePointer) = mibBuilder.importSymbols(
    "SNMPv2-TC",
    "DateAndTime",
    "DisplayString",
    "MacAddress",
    "PhysAddress",
    "RowStatus",
    "TAddress",
    "TextualConvention",
    "TruthValue",
    "VariablePointer")

(enterpriseProducts,) = mibBuilder.importSymbols(
    "ST-ROOT-MIB",
    "enterpriseProducts")


# MODULE-IDENTITY

stCommon = ModuleIdentity(
    (1, 3, 6, 1, 4, 1, 52642, 1, 10)
)
if mibBuilder.loadTexts:
    stCommon.setRevisions(
        ("2018-01-02 10:48",)
    )
if mibBuilder.loadTexts:
    stCommon.setLastUpdated("201707101130Z")
if mibBuilder.loadTexts:
    stCommon.setDescription("ISSUE 12: - add pumpTempAbnormal(1903) alarm")


# Types definitions


# TEXTUAL-CONVENTIONS



class EventCategory(TextualConvention, Integer32):
    status = "current"
    subtypeSpec = Integer32.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        SingleValueConstraint(
            *(0,
              1,
              2,
              3,
              4,
              5,
              6,
              7,
              8,
              9,
              10,
              11,
              12,
              13,
              14,
              15,
              16,
              17,
              18,
              19)
        )
    )
    namedValues = NamedValues(
        *(("default", 0),
          ("currentAlarms", 1),
          ("fileService", 2),
          ("performance", 3),
          ("configurationOthers", 4),
          ("dcn", 5),
          ("configurationNEAndShelf", 6),
          ("configurationCards", 7),
          ("configurationPorts", 8),
          ("configurationConnections", 9),
          ("configurationTransponderLines", 10),
          ("configurationTransponderClients", 11),
          ("configurationOptical", 12),
          ("configurationChannelMonitoring", 13),
          ("security", 14),
          ("layer2Switch", 15),
          ("securityEvents", 16),
          ("gmpls", 17),
          ("mplstp", 18),
          ("configurationCardProtection", 19))
    )

    if mibBuilder.loadTexts:
        description = "Category of event used for trap counters."


class TypeOfChange(TextualConvention, Integer32):
    status = "current"
    subtypeSpec = Integer32.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        SingleValueConstraint(
            *(1,
              2,
              3)
        )
    )
    namedValues = NamedValues(
        *(("entryAdded", 1),
          ("entryModified", 2),
          ("entryRemoved", 3))
    )

    if mibBuilder.loadTexts:
        description = "Indicates the type of an event which notifies a configuration change."


class AlarmCode(TextualConvention, Integer32):
    status = "current"
    subtypeSpec = Integer32.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        SingleValueConstraint(
            *(101,
              102,
              201,
              202,
              203,
              204,
              205,
              206,
              207,
              208,
              209,
              210,
              211,
              212,
              213,
              214,
              215,
              216,
              217,
              251,
              252,
              253,
              254,
              255,
              256,
              257,
              301,
              302,
              303,
              304,
              305,
              306,
              307,
              308,
              309,
              310,
              311,
              312,
              313,
              314,
              315,
              316,
              317,
              318,
              319,
              320,
              351,
              352,
              353,
              1000,
              1001,
              1002,
              1003,
              1004,
              1005,
              1050,
              1051,
              1052,
              1053,
              1054,
              1055,
              1056,
              1057,
              1058,
              1059,
              1060,
              1061,
              1062,
              1063,
              1100,
              1101,
              1102,
              1103,
              1104,
              1105,
              1106,
              1107,
              1108,
              1109,
              1110,
              1111,
              1112,
              1115,
              1150,
              1200,
              1201,
              1202,
              1203,
              1204,
              1205,
              1206,
              1207,
              1208,
              1209,
              1210,
              1250,
              1251,
              1252,
              1253,
              1254,
              1255,
              1256,
              1257,
              1258,
              1259,
              1260,
              1261,
              1262,
              1270,
              1271,
              1272,
              1273,
              1274,
              1275,
              1276,
              1277,
              1278,
              1279,
              1280,
              1281,
              1282,
              1283,
              1284,
              1285,
              1288,
              1289,
              1290,
              1291,
              1292,
              1293,
              1300,
              1301,
              1302,
              1303,
              1304,
              1305,
              1306,
              1307,
              1308,
              1309,
              1310,
              1311,
              1312,
              1313,
              1350,
              1351,
              1352,
              1353,
              1354,
              1355,
              1356,
              1357,
              1358,
              1359,
              1360,
              1361,
              1362,
              1363,
              1374,
              1375,
              1376,
              1377,
              1380,
              1381,
              1390,
              1400,
              1401,
              1450,
              1451,
              1452,
              1453,
              1454,
              1500,
              1501,
              1600,
              1601,
              1602,
              1603,
              1604,
              1605,
              1700,
              1701,
              1702,
              1703,
              1704,
              1705,
              1706,
              1707,
              1708,
              1800,
              1801,
              1802,
              1803,
              1804,
              1805,
              1806,
              1807,
              1900,
              1901,
              1902,
              1903,
              1904,
              1905,
              1906,
              1907,
              2000,
              2200,
              2201,
              2202,
              2203,
              2204,
              2205,
              2206,
              2207,
              2208,
              2209,
              2210,
              2211,
              2212,
              2213,
              2228,
              2229,
              2230,
              2231)
        )
    )
    namedValues = NamedValues(
        *(("olt-pon-los", 101),
          ("olt-eth-los", 102),
          ("onu-off", 201),
          ("onu-equip", 202),
          ("onu-power", 203),
          ("onu-bat-missing", 204),
          ("onu-fail", 205),
          ("onu-bat-low", 206),
          ("onu-physical-instusion", 207),
          ("onu-selftest-fail", 208),
          ("onu-dying-gasp", 209),
          ("onu-temp-yellow", 210),
          ("onu-temp-red", 211),
          ("onu-volt-yellow", 212),
          ("onu-volt-red", 213),
          ("onu-menual-poweroff", 214),
          ("onu-inv-image", 215),
          ("onu-pse-overload-yellow", 216),
          ("onu-pse-overload-red", 217),
          ("onu-pon-low-recv-optpower", 251),
          ("onu-pon-high-recv-optpower", 252),
          ("onu-pon-sf", 253),
          ("onu-pon-sd", 254),
          ("onu-pon-low-trans-optpower", 255),
          ("onu-pon-high-trans-optpower", 256),
          ("onu-pon-laser-bias-current", 257),
          ("onu-uni-lan-los", 301),
          ("onu-uni-fcs-err", 302),
          ("onu-uni-excessive-collision-count", 303),
          ("onu-uni-late-collision-count", 304),
          ("onu-uni-frame-toolong", 305),
          ("onu-uni-recvbuf-overflow", 306),
          ("onu-uni-transbuf-overflow", 307),
          ("onu-uni-single-collision-frame-count", 308),
          ("onu-uni-multi-collision-frame-count", 309),
          ("onu-uni-sqe-count", 310),
          ("onu-uni-defer-trans-count", 311),
          ("onu-uni-internal-mac-transerr-count", 312),
          ("onu-uni-carrier-sense-err-count", 313),
          ("onu-uni-alignment-err-count", 314),
          ("onu-uni-internal-mac-recvserr-count", 315),
          ("onu-uni-pppoe-filter-frame-count", 316),
          ("onu-uni-dropevent", 317),
          ("onu-uni-undersize-pack", 318),
          ("onu-uni-fragment", 319),
          ("onu-uni-jabber", 320),
          ("onu-subport-lost-pack", 351),
          ("onu-subport-misinsert-pack", 352),
          ("onu-subport-impaired-block", 353),
          ("fcLos", 1000),
          ("fcLosSync", 1001),
          ("fcCsfOpu", 1002),
          ("fcProtna", 1003),
          ("fcLpbkNe", 1004),
          ("fcLpbkFe", 1005),
          ("etynLos", 1050),
          ("etynLosSync", 1051),
          ("etynLf", 1052),
          ("etynRf", 1053),
          ("etynCsfLosGfp", 1054),
          ("etynCsfLosSyncGfp", 1055),
          ("etynCsfFdiGfp", 1056),
          ("etynCsfRdiGfp", 1057),
          ("etynLofdGfp", 1058),
          ("etynPlmGfp", 1059),
          ("etynCsfOpu", 1060),
          ("etynProtNa", 1061),
          ("etynLpbkNe", 1062),
          ("etynLpbkFe", 1063),
          ("ocnstmnLos", 1100),
          ("ocnstmnGenAisIngress", 1101),
          ("ocnstmnLofIngress", 1102),
          ("ocnstmnAislMsaisIngress", 1103),
          ("ocnstmnRfilMsrfi", 1104),
          ("ocnstmnTimlRstim", 1105),
          ("ocnstmnCsfOpu", 1106),
          ("ocnstmnGenAisEgress", 1107),
          ("ocnstmnLofEgress", 1108),
          ("ocnstmnAislMsaisEgress", 1109),
          ("ocnstmnProtna", 1110),
          ("ocnstmnLpbkNe", 1111),
          ("ocnstmnLpbkFe", 1112),
          ("prbsLockLos", 1115),
          ("ochLos", 1150),
          ("otuLos", 1200),
          ("otuLof", 1201),
          ("otuLom", 1202),
          ("otuTim", 1203),
          ("otuBdi", 1204),
          ("otuDeg", 1205),
          ("otuAis", 1206),
          ("otuProtna", 1207),
          ("otuLpbkNe", 1208),
          ("otuLpbkFe", 1209),
          ("otuLol", 1210),
          ("oduDeg", 1250),
          ("oduLck", 1251),
          ("oduOci", 1252),
          ("oduAis", 1253),
          ("oduBdi", 1254),
          ("oduTim", 1255),
          ("oduPlm", 1256),
          ("oduLoomfi", 1257),
          ("oduMsim", 1258),
          ("oduLofLom", 1259),
          ("oduLtc", 1260),
          ("otussf", 1261),
          ("odussf", 1262),
          ("tcaBBE", 1270),
          ("tcaES", 1271),
          ("tcaSES", 1272),
          ("tcaUAS", 1273),
          ("tcaPreFEC", 1274),
          ("tcaRxOPowerHigh", 1275),
          ("tcaRxOPowerLow", 1276),
          ("tcaChkSeqErr", 1277),
          ("tcaAlignErr", 1278),
          ("tcaJabber", 1279),
          ("tcaFragment", 1280),
          ("tcaDropEvts", 1281),
          ("tcaOverSize", 1282),
          ("tcaUnderSize", 1283),
          ("tcaCollision", 1284),
          ("tcapostfec", 1285),
          ("tcaRxErrBytes", 1288),
          ("tcaTxErrBytes", 1289),
          ("tcaTxOPowerHigh", 1290),
          ("tcaTxOPowerLow", 1291),
          ("tcaTempLow", 1292),
          ("tcaTempHigh", 1293),
          ("eqptMissng", 1300),
          ("eqptFail", 1301),
          ("eqptDeg", 1302),
          ("eqptMismatch", 1303),
          ("eqptPowerSupplyIssue", 1304),
          ("eqptTempMajor", 1305),
          ("eqptTempCritical", 1306),
          ("eqptCommFail", 1307),
          ("eqptLatchOpen", 1308),
          ("eqptFanMajor", 1309),
          ("eqptFanCritical", 1310),
          ("eqptLowTempMajor", 1311),
          ("eqptLowTempCritical", 1312),
          ("eqptSdCardMountFail", 1313),
          ("pluggableMissing", 1350),
          ("pluggableFail", 1351),
          ("pluggableMismatch", 1352),
          ("pluggableTxFail", 1353),
          ("pluggableRxPowerTooHigh", 1354),
          ("pluggableRxPowerTooLow", 1355),
          ("pluggableTxPowerTooHigh", 1356),
          ("pluggableTxPowerTooLow", 1357),
          ("pluggableBiasCurrentTooHigh", 1358),
          ("pluggableBiasCurrentTooLow", 1359),
          ("pluggableTempTooHigh", 1360),
          ("pluggableTempTooLow", 1361),
          ("pluggableVccTooHigh", 1362),
          ("pluggableVccTooLow", 1363),
          ("pluggableRx1PowerTooHigh", 1374),
          ("pluggableRx1PowerTooLow", 1375),
          ("pluggableRx2PowerTooHigh", 1376),
          ("pluggableRx2PowerTooLow", 1377),
          ("ponLos", 1380),
          ("ponDsErr", 1381),
          ("remoteDeviceDyingGasp", 1390),
          ("fopNr", 1400),
          ("fopPm", 1401),
          ("swMismatch", 1450),
          ("swDownloadFail", 1451),
          ("swMibMismatch", 1452),
          ("swMibFail", 1453),
          ("swStorgeFull", 1454),
          ("secPasswordExpired", 1500),
          ("secLoginExceeded", 1501),
          ("shelfMDI1", 1600),
          ("shelfMDI2", 1601),
          ("shelfMDI3", 1602),
          ("shelfMDI4", 1603),
          ("shlefTempMajor", 1604),
          ("shelfTempCritical", 1605),
          ("thirdPartyModuleAbsent", 1700),
          ("thirdPartyModuleFail", 1701),
          ("thirdParthModuleTemp1TooHigh", 1702),
          ("thirdParthModuleTemp1TooLow", 1703),
          ("thirdParthModuleTemp2TooHigh", 1704),
          ("thirdParthModuleTemp2TooLow", 1705),
          ("thirdParthModuleTemp3TooHigh", 1706),
          ("thirdParthModuleTemp3TooLow", 1707),
          ("thirdParthModuleCommFail", 1708),
          ("opticalLos", 1800),
          ("opticalBelowThreshold", 1801),
          ("opticalSwitch", 1802),
          ("remoteloss", 1803),
          ("snr", 1804),
          ("heartLoss", 1805),
          ("opticalRx1Los", 1806),
          ("opticalRx2Los", 1807),
          ("edfaRxLOS", 1900),
          ("edfaTXLOS", 1901),
          ("edfaCurrentTooHigh", 1902),
          ("pumpTempAbnormal", 1903),
          ("ponRemoteLos", 1904),
          ("ramanRFLTooHigh", 1905),
          ("ramanRxLOS", 1906),
          ("ramanCurrentTooHigh", 1907),
          ("linkFailure", 2000),
          ("pllInclkLos", 2200),
          ("pllXoLos", 2201),
          ("pllLol", 2202),
          ("pllInclkOof", 2203),
          ("ponPllLol", 2204),
          ("phyQpllLol", 2205),
          ("phyAbnormal", 2206),
          ("opu1FifoEmpty", 2207),
          ("opu1FifoUnderflow", 2208),
          ("opu1FifoFull", 2209),
          ("opu1FifoOverflow", 2210),
          ("onuOffline", 2211),
          ("powerDiffBad", 2212),
          ("powerOverThr", 2213),
          ("localLOS", 2228),
          ("localAIS", 2229),
          ("remoteLOS", 2230),
          ("remoteAIS", 2231))
    )

    if mibBuilder.loadTexts:
        description = "alarm code definition."


class AlarmPathType(TextualConvention, Integer32):
    status = "current"
    subtypeSpec = Integer32.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        SingleValueConstraint(
            *(1,
              2,
              3,
              4,
              5,
              6,
              7,
              8,
              9,
              10,
              11,
              12,
              13,
              14,
              15,
              16,
              17,
              18,
              19,
              21)
        )
    )
    namedValues = NamedValues(
        *(("ne", 1),
          ("slot", 2),
          ("port", 3),
          ("tp", 4),
          ("pon", 5),
          ("onu", 6),
          ("eqpt", 7),
          ("etyn", 8),
          ("stmn", 9),
          ("och", 10),
          ("otuk", 11),
          ("oduk", 12),
          ("pluggable", 13),
          ("lane", 14),
          ("vps", 15),
          ("olp", 16),
          ("oa", 17),
          ("qos", 18),
          ("opm", 19),
          ("stat", 21))
    )

    if mibBuilder.loadTexts:
        description = "Alarm Path Type"


class AlarmState(TextualConvention, Integer32):
    status = "current"
    subtypeSpec = Integer32.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        SingleValueConstraint(
            *(1,
              2)
        )
    )
    namedValues = NamedValues(
        *(("set", 1),
          ("clear", 2))
    )

    if mibBuilder.loadTexts:
        description = "Common alarm state."


class CardType(TextualConvention, Integer32):
    status = "current"
    subtypeSpec = Integer32.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        SingleValueConstraint(
            *(0,
              1,
              2,
              3,
              4,
              6,
              7,
              8,
              10,
              13,
              14,
              15,
              16,
              20,
              21,
              22,
              23,
              24,
              26,
              27,
              28,
              29,
              30,
              41,
              42,
              43,
              44,
              45,
              46,
              47,
              50,
              51,
              52,
              53,
              54,
              55,
              60,
              61,
              62,
              70,
              71,
              72,
              73,
              74,
              75,
              76,
              77,
              78,
              79,
              80,
              81,
              82,
              83,
              84,
              85,
              86,
              87,
              88,
              89,
              90,
              91,
              92,
              93,
              94,
              95,
              96,
              97,
              101,
              102,
              103,
              104,
              105,
              106,
              107,
              111,
              112,
              113,
              115,
              116,
              117,
              118,
              120,
              121,
              122,
              123,
              124,
              125,
              126,
              127,
              129,
              130,
              131,
              132,
              133,
              134,
              135,
              136,
              137,
              138,
              140,
              141,
              142,
              143,
              144,
              145,
              146,
              147,
              148,
              149,
              150,
              201,
              202,
              203,
              206,
              207,
              208,
              210,
              214,
              215,
              216,
              300,
              301,
              302,
              304,
              306,
              307,
              308,
              309,
              500,
              501,
              502,
              503,
              504,
              505,
              510,
              511,
              512,
              517,
              518,
              519,
              520,
              521,
              522,
              523,
              530,
              535,
              536,
              537,
              540,
              541,
              545,
              546,
              552,
              553,
              554,
              555,
              556,
              557,
              558,
              559,
              560,
              561,
              565,
              567,
              568,
              569,
              570,
              571,
              572,
              573,
              574,
              631)
        )
    )
    namedValues = NamedValues(
        *(("empty", 0),
          ("m1H101U", 1),
          ("t20X1U", 2),
          ("m2X161U", 3),
          ("t10X1U", 4),
          ("M6500-M2X16", 6),
          ("m2X16A", 7),
          ("t10X", 8),
          ("M6500-T20", 10),
          ("m1H21U", 13),
          ("t2H1U", 14),
          ("m1H5", 15),
          ("m1H51U", 16),
          ("g161U", 20),
          ("g081U", 21),
          ("g16e1U", 22),
          ("g08e1U", 23),
          ("e161U", 24),
          ("e081U", 26),
          ("g16", 27),
          ("g08", 28),
          ("e16", 29),
          ("e08", 30),
          ("pae", 41),
          ("paeGponOlt", 42),
          ("paeGponOnu", 43),
          ("paeEpon1Olt", 44),
          ("paeEpon1Onu", 45),
          ("paeEpon2Olt", 46),
          ("paeEpon2Onu", 47),
          ("pae1uGponOlt", 50),
          ("pae1uGponOnu", 51),
          ("pae1uEpon1Olt", 52),
          ("pae1uEpon1Onu", 53),
          ("pae1uEpon2Olt", 54),
          ("pae1uEpon2Onu", 55),
          ("paeRpc6480GponOlt", 60),
          ("paeRpc6480Epon1Olt", 61),
          ("paeRpc6480Epon2Olt", 62),
          ("omu2", 70),
          ("M6200-RB", 71),
          ("omu48", 72),
          ("dcm1", 73),
          ("M6200-DCM40", 74),
          ("ssa", 75),
          ("ssm", 76),
          ("sr2", 77),
          ("oa1", 78),
          ("oa21", 79),
          ("otu10", 80),
          ("oa22", 81),
          ("sr22", 82),
          ("ssa2", 83),
          ("olp1to1", 84),
          ("M6200-OLP2", 85),
          ("otu10AM2", 86),
          ("M6200-OEO10G", 87),
          ("soas", 88),
          ("oa4", 89),
          ("omd40", 90),
          ("omd80", 91),
          ("omd40e", 92),
          ("omd80e", 93),
          ("M6500-EDFA", 94),
          ("oa26800", 95),
          ("M6500-OLP2", 96),
          ("omd6800", 97),
          ("M6500-5UNMU", 101),
          ("sxc4", 102),
          ("upl4", 103),
          ("M6200-NMU", 104),
          ("otnSi", 105),
          ("stnBSc", 106),
          ("M6500-NMU", 107),
          ("m2h20-100g1u", 111),
          ("m2h20-20x10g1u", 112),
          ("m2h20-100g-10x10g1u", 113),
          ("m2h20-4x40g-4x10g1u", 115),
          ("t4h-4x25gdwdm", 116),
          ("t4h-dco", 117),
          ("t4h-greycfp", 118),
          ("sax", 120),
          ("otu4h", 121),
          ("ssa2AM2", 122),
          ("M6200-OEO100G", 123),
          ("otu6FA", 124),
          ("otu10A", 125),
          ("M6500-TMXP5", 126),
          ("m2h20-100g-2x40g1u", 127),
          ("m2h20-2x40g-12x10g1u", 129),
          ("M6200-D2160M", 130),
          ("osc", 131),
          ("otu6h", 132),
          ("ssah", 133),
          ("ba-16t", 134),
          ("ba-64t", 135),
          ("M6200-RA", 136),
          ("bypassGbp", 137),
          ("otu4x", 138),
          ("M6800-TSP16", 140),
          ("soa", 141),
          ("soa6", 142),
          ("sah", 143),
          ("oa26", 144),
          ("edfa", 145),
          ("M6200-OLP1Z", 146),
          ("M6200-OLP1", 147),
          ("opm", 148),
          ("osw", 149),
          ("stnSc5u", 150),
          ("otnPwrAc1U", 201),
          ("otnPwrDc1U", 202),
          ("pwrDc10U", 203),
          ("otnpwrDce1U", 206),
          ("stnBDc", 207),
          ("stnBAc", 208),
          ("M6500-2UPSM", 210),
          ("M6500-5UPSM", 214),
          ("M6800-1UPSM", 215),
          ("dciDC1U", 216),
          ("otnFan1U", 300),
          ("fan10U", 301),
          ("fanOlt10U", 302),
          ("M6500-2UFAN", 304),
          ("stnIAFan1U", 306),
          ("M6500-5UFAN", 307),
          ("M6800-1UFAN", 308),
          ("stnFan3U", 309),
          ("M6200-EDFA", 500),
          ("M6200-SOA", 501),
          ("oa23n", 502),
          ("oa24n", 503),
          ("oa25n", 504),
          ("oa26n", 505),
          ("soan", 510),
          ("soasn", 511),
          ("soas2n", 512),
          ("pmu04", 517),
          ("pmu08", 518),
          ("oeo-64t", 519),
          ("sah1", 520),
          ("sah2", 521),
          ("sax1", 522),
          ("sax2", 523),
          ("otu2ha", 530),
          ("omp06", 535),
          ("omp12", 536),
          ("omp18", 537),
          ("omu96", 540),
          ("odu96", 541),
          ("otu6", 545),
          ("e1", 546),
          ("otdr", 552),
          ("pmu4", 553),
          ("pmu8", 554),
          ("pmu16", 555),
          ("osu8", 556),
          ("osu16", 557),
          ("osu32", 558),
          ("olpTbp", 559),
          ("sr2-LC", 560),
          ("opm-osw", 561),
          ("otu2h8", 565),
          ("otu2dh", 567),
          ("otu1h4", 568),
          ("omp06-I", 569),
          ("omp06-II", 570),
          ("omp12-I", 571),
          ("omp12-II", 572),
          ("omp18-I", 573),
          ("omp18-II", 574),
          ("otu10ce", 631))
    )

    if mibBuilder.loadTexts:
        description = "Specifies the type of card provisioned for use in this slot. Empty specifies no rovisioning."


class StPortMode(TextualConvention, Integer32):
    status = "current"
    subtypeSpec = Integer32.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        SingleValueConstraint(
            *(0,
              1,
              2,
              3,
              4,
              5,
              6,
              7,
              8,
              9,
              10,
              11,
              12,
              13,
              14,
              15,
              16,
              17,
              18,
              19,
              20,
              21,
              22,
              23,
              24,
              25,
              26,
              27,
              28,
              29,
              30,
              31,
              32,
              33,
              34,
              35,
              36,
              54,
              55,
              56,
              57,
              58,
              59,
              60,
              61,
              100,
              101,
              102,
              110,
              111,
              112,
              113,
              114,
              115,
              130,
              131,
              132,
              133,
              134,
              135,
              136,
              137,
              138,
              139,
              140,
              141,
              150,
              151,
              152,
              153,
              166,
              201)
        )
    )
    namedValues = NamedValues(
        *(("none", 0),
          ("ge", 1),
          ("gettt", 2),
          ("xgeBMP", 3),
          ("xgeGFPF", 4),
          ("xgeGFPFextp", 5),
          ("ge100", 6),
          ("feCbr", 7),
          ("stm1GMP", 8),
          ("oc3GMP", 9),
          ("stm4GMP", 10),
          ("oc12GMP", 11),
          ("stm16AMP", 12),
          ("oc48AMP", 13),
          ("stm64AMP", 14),
          ("oc192AMP", 15),
          ("fc1", 16),
          ("fc2", 17),
          ("fc4", 18),
          ("fc8", 19),
          ("fc10", 20),
          ("otu1", 21),
          ("ochOSOtu1", 22),
          ("otu2", 23),
          ("ochOSOtu2", 24),
          ("otu2e", 25),
          ("ochOSOtu2e", 26),
          ("otu3", 27),
          ("ochOSOtu3", 28),
          ("otu4", 29),
          ("ochOSOtu4", 30),
          ("otu0ll", 31),
          ("otuc2", 32),
          ("ochOSOtuc2", 33),
          ("otuc4", 34),
          ("ochOSOtuc4", 35),
          ("och4", 36),
          ("stm16BMP", 54),
          ("oc48BMP", 55),
          ("stm64BMP", 56),
          ("oc192BMP", 57),
          ("ge40GMP", 58),
          ("ge40GFPF", 59),
          ("ge100GMP", 60),
          ("ge100GFPF", 61),
          ("oltge", 100),
          ("xge", 101),
          ("xgeWan", 102),
          ("gpon", 110),
          ("epon", 111),
          ("gponOlt", 112),
          ("gponOnu", 113),
          ("eponOlt", 114),
          ("eponOnu", 115),
          ("ssamp", 130),
          ("ssama", 131),
          ("vps", 132),
          ("olp", 133),
          ("oa", 134),
          ("oap", 135),
          ("f10G2G51G25", 136),
          ("f100G40G", 137),
          ("f40G", 138),
          ("f100Gdwdm", 139),
          ("f100G", 140),
          ("f25G", 141),
          ("mgmto", 150),
          ("mgmte", 151),
          ("bpLanE", 152),
          ("bpWanE", 153),
          ("osw", 166),
          ("opm", 201))
    )

    if mibBuilder.loadTexts:
        description = " "


class StAvailabilityState(TextualConvention, Integer32):
    status = "current"
    subtypeSpec = Integer32.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        SingleValueConstraint(
            *(0,
              1,
              2,
              3,
              4,
              5,
              6,
              7,
              8,
              9,
              10,
              11,
              12)
        )
    )
    namedValues = NamedValues(
        *(("normal", 0),
          ("failed", 1),
          ("degraded", 2),
          ("notInstalled", 3),
          ("mismatch", 4),
          ("loopback", 5),
          ("remotefailed", 6),
          ("latchopen", 7),
          ("intest", 8),
          ("empty", 9),
          ("notconnected", 10),
          ("reserved", 11),
          ("unsync", 12))
    )

    if mibBuilder.loadTexts:
        description = " "


class AlarmSeverity(TextualConvention, Integer32):
    status = "current"
    subtypeSpec = Integer32.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        SingleValueConstraint(
            *(0,
              1,
              2,
              3,
              4,
              5)
        )
    )
    namedValues = NamedValues(
        *(("notInAlarm", 0),
          ("warning", 1),
          ("minor", 2),
          ("major", 3),
          ("critical", 4),
          ("indeterminate", 5))
    )

    if mibBuilder.loadTexts:
        description = "Common alarm severity definition."


class AlarmType(TextualConvention, Integer32):
    status = "current"
    subtypeSpec = Integer32.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        SingleValueConstraint(
            *(1,
              2,
              3,
              4,
              5,
              6,
              7)
        )
    )
    namedValues = NamedValues(
        *(("communication", 1),
          ("qos", 2),
          ("equipment", 3),
          ("processerror", 4),
          ("environment", 5),
          ("security", 6),
          ("control-plane", 7))
    )

    if mibBuilder.loadTexts:
        description = "Common alarm category definition."


class AlarmSummary(TextualConvention, Integer32):
    status = "current"
    subtypeSpec = Integer32.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        SingleValueConstraint(
            *(0,
              1,
              2,
              3)
        )
    )
    namedValues = NamedValues(
        *(("none", 0),
          ("critical", 1),
          ("major", 2),
          ("minor", 3))
    )

    if mibBuilder.loadTexts:
        description = "Set of bits, each indicating presence of any alarm of a particular severity."


class PresenceState(TextualConvention, Integer32):
    status = "current"
    subtypeSpec = Integer32.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        SingleValueConstraint(
            *(1,
              2,
              3)
        )
    )
    namedValues = NamedValues(
        *(("required", 1),
          ("planned", 2),
          ("autoInService", 3))
    )

    if mibBuilder.loadTexts:
        description = "Presence state."


class EquipmentState(TextualConvention, Integer32):
    status = "current"
    subtypeSpec = Integer32.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        SingleValueConstraint(
            *(0,
              1,
              2,
              3,
              4,
              5,
              6,
              7,
              8,
              9,
              10)
        )
    )
    namedValues = NamedValues(
        *(("undefined", 0),
          ("spare", 1),
          ("reserved", 2),
          ("inService", 3),
          ("outOfService", 4),
          ("oOSMaintenance", 5),
          ("planned", 6),
          ("pending", 7),
          ("pendingForMaintenance", 8),
          ("pendingReserved", 9),
          ("spareForMaintenance", 10))
    )

    if mibBuilder.loadTexts:
        description = "TransConnect equipment state."


class LinkDirection(TextualConvention, Unsigned32):
    status = "current"
    if mibBuilder.loadTexts:
        description = "Direction of DWDM line interface."


class CommissioningStatusShelf(TextualConvention, Integer32):
    status = "current"
    subtypeSpec = Integer32.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        SingleValueConstraint(
            *(0,
              1)
        )
    )
    namedValues = NamedValues(
        *(("notCommissioned", 0),
          ("commissioned", 1))
    )

    if mibBuilder.loadTexts:
        description = "Indicates status of shelf with respect to match with required equipping."


class ShelfNumberWithinBay(TextualConvention, Unsigned32):
    status = "current"
    subtypeSpec = Unsigned32.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        ValueRangeConstraint(0, 8),
    )

    if mibBuilder.loadTexts:
        description = "Number of shelf within bay."


class EquipmentProvisioningMode(TextualConvention, Integer32):
    status = "current"
    subtypeSpec = Integer32.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        SingleValueConstraint(
            *(0,
              1)
        )
    )
    namedValues = NamedValues(
        *(("locked", 0),
          ("free", 1))
    )

    if mibBuilder.loadTexts:
        description = "Indicates whether shelf or slot can be provisioned using SNMP."


class GenericOperation(TextualConvention, Integer32):
    status = "current"
    subtypeSpec = Integer32.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        SingleValueConstraint(
            *(0,
              1,
              2)
        )
    )
    namedValues = NamedValues(
        *(("idle", 0),
          ("invoked", 1),
          ("failed", 2))
    )

    if mibBuilder.loadTexts:
        description = "Used by the SNMP manager in order to invoke a specific operation. Setting the value to 'invoked' means to trigger the operation. After successful execution of the operation the value will be 'idle'. A failure of the operation is either indicated by an immediate error response on the attempt to set the value to 'invoked' or (in case that the agent must respond before knowing about success or failure) by the value 'failed' after having determined that the operation has failed. It depends on the kind of operation and the timing constraints of the internal procedures in order to execute the operation whether the value 'failed' or an immediate error response is used. In case of an immediate error response the value 'idle' is immediately taken after execution of the operation."


class FilterMaintenanceInterval(TextualConvention, Integer32):
    status = "current"
    subtypeSpec = Integer32.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        SingleValueConstraint(
            *(0,
              1,
              2,
              3)
        )
    )
    namedValues = NamedValues(
        *(("interval12Months", 0),
          ("interval10Months", 1),
          ("interval8Months", 2),
          ("interval6Months", 3))
    )

    if mibBuilder.loadTexts:
        description = "Indicates conditions for filter endurance."


class EnableSwitch(TextualConvention, Integer32):
    status = "current"
    subtypeSpec = Integer32.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        SingleValueConstraint(
            *(0,
              1)
        )
    )
    namedValues = NamedValues(
        *(("disabled", 0),
          ("enabled", 1))
    )

    if mibBuilder.loadTexts:
        description = "Parameter to disable or enable a certain functionality or to indicate availability of a certain functionality."


class OperationalState(TextualConvention, Integer32):
    status = "current"
    subtypeSpec = Integer32.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        SingleValueConstraint(
            *(0,
              1)
        )
    )
    namedValues = NamedValues(
        *(("enabled", 0),
          ("disabled", 1))
    )

    if mibBuilder.loadTexts:
        description = "Indicates whether a resource is able to provide service."


class AdministrativeState(TextualConvention, Integer32):
    status = "current"
    subtypeSpec = Integer32.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        SingleValueConstraint(
            *(0,
              2)
        )
    )
    namedValues = NamedValues(
        *(("unlocked", 0),
          ("locked", 2))
    )

    if mibBuilder.loadTexts:
        description = "The administrative state represents permission to use or prohibition against using a resource, imposed through the management services."


class CardMode(TextualConvention, Integer32):
    status = "current"
    subtypeSpec = Integer32.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        SingleValueConstraint(
            *(0,
              1,
              2,
              3,
              4,
              5,
              6,
              7,
              8,
              9,
              10,
              11,
              12,
              13,
              14)
        )
    )
    namedValues = NamedValues(
        *(("notApplicable", 0),
          ("transponder", 1),
          ("regenerator", 2),
          ("mux", 3),
          ("demux", 4),
          ("dsTributary", 5),
          ("tributary", 6),
          ("ds5", 7),
          ("ds", 8),
          ("add", 9),
          ("drop", 10),
          ("aggregator", 11),
          ("notAssigned", 12),
          ("forward", 13),
          ("backward", 14))
    )

    if mibBuilder.loadTexts:
        description = "Indicates working mode of transponder, filter or WSS card"


class ResetOperation(TextualConvention, Integer32):
    status = "current"
    subtypeSpec = Integer32.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        SingleValueConstraint(
            *(0,
              1,
              2)
        )
    )
    namedValues = NamedValues(
        *(("idle", 0),
          ("cold", 1),
          ("warm", 2))
    )

    if mibBuilder.loadTexts:
        description = "Used by the SNMP manager in order to trigger a particular reset operation. Once the operation is started the value will be 'idle'."


class CardUploadOperation(TextualConvention, Integer32):
    status = "current"
    subtypeSpec = Integer32.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        SingleValueConstraint(
            *(0,
              1,
              2,
              3,
              4)
        )
    )
    namedValues = NamedValues(
        *(("idle", 0),
          ("pmHistory15min", 1),
          ("pmHistory24h", 2),
          ("diagnosticData", 3),
          ("pmHistoryData", 4))
    )

    if mibBuilder.loadTexts:
        description = "Used by the SNMP manager in order to invoke a card related upload operation. Setting the value to the type of file to upload means to trigger the upload procedure for this file. The 'idle' value is used for responses to retrieval operations."


class PMNumberOfRecords(TextualConvention, Unsigned32):
    status = "current"
    subtypeSpec = Unsigned32.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        ValueRangeConstraint(0, 32),
    )

    if mibBuilder.loadTexts:
        description = "Number of performance records."


class PMParameterName(TextualConvention, Integer32):
    status = "current"
    subtypeSpec = Integer32.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        SingleValueConstraint(
            *(0,
              1,
              2,
              3,
              4,
              5,
              6,
              7,
              8,
              9,
              10,
              11,
              12,
              13,
              14,
              15,
              16,
              17,
              18,
              19,
              20,
              21,
              22,
              23,
              24,
              25,
              26,
              27,
              28,
              29,
              30,
              31,
              32,
              33,
              34,
              35,
              36,
              37,
              38,
              39,
              40,
              41,
              42,
              44,
              45,
              47,
              48,
              49,
              50,
              51,
              52,
              53,
              54,
              55,
              56,
              57,
              58,
              59,
              60,
              61,
              62,
              63,
              64,
              65,
              66,
              67,
              68,
              69,
              70,
              71,
              72,
              73,
              74,
              75,
              76,
              77,
              78,
              79,
              80,
              81,
              82,
              83,
              85,
              86,
              87,
              88,
              89,
              90,
              91,
              92,
              93)
        )
    )
    namedValues = NamedValues(
        *(("notApplicable", 0),
          ("maximumBitErrorRate", 1),
          ("averageBitErrorRate", 2),
          ("codingViolations", 3),
          ("backgroundBlockErrors", 4),
          ("erroredSeconds", 5),
          ("severelyErroredSeconds", 6),
          ("severelyErroredFramingSeconds", 7),
          ("unavailableSeconds", 8),
          ("codingViolationsOrDisparityErrors", 9),
          ("framesReceivedOK", 10),
          ("octetsReceivedOK", 11),
          ("erroredFramesReceived", 12),
          ("framesTransmittedOK", 13),
          ("octetsTransmittedOK", 14),
          ("erroredFramesTransmitted", 15),
          ("gfpErroredSuperblocks", 16),
          ("gfpDiscardedFrames", 17),
          ("inputPowerMin", 18),
          ("inputPowerMax", 19),
          ("inputPowerAverage", 20),
          ("powerScans", 21),
          ("lossOfSignalCount", 22),
          ("lossOfSynchronizationCount", 23),
          ("linkFailureCount", 24),
          ("gfpFCSErrors", 25),
          ("outputPowerMin", 26),
          ("outputPowerMax", 27),
          ("outputPowerAverage", 28),
          ("degreeOfPolarizationMin", 29),
          ("degreeOfPolarizationMax", 30),
          ("degreeOfPolarizationAverage", 31),
          ("pcsErroredBlockCounter", 32),
          ("pcsBIPErroredBlockCounter", 33),
          ("transcodingErrors", 34),
          ("droppedFrames", 35),
          ("dispersionCompensationMin", 36),
          ("dispersionCompensationMax", 37),
          ("dispersionCompensationAverage", 38),
          ("inputPowerMinTimestamp", 39),
          ("inputPowerMaxTimestamp", 40),
          ("outputPowerMinTimestamp", 41),
          ("outputPowerMaxTimestamp", 42),
          ("delayMeasuredMaxTimestamp", 44),
          ("delayMeasuredMax", 45),
          ("delayMeasuredAverage", 47),
          ("delayMeasuredNumberOfSnapshots", 48),
          ("inputSpanLossMin", 49),
          ("inputSpanLossMinTimestamp", 50),
          ("inputSpanLossMax", 51),
          ("inputSpanLossMaxTimestamp", 52),
          ("inputSpanLossAverage", 53),
          ("outputTiltMin", 54),
          ("outputTiltMinTimestamp", 55),
          ("outputTiltMax", 56),
          ("outputTiltMaxTimestamp", 57),
          ("outputTiltAverage", 58),
          ("channelCountMin", 59),
          ("channelCountMinTimestamp", 60),
          ("channelCountMax", 61),
          ("channelCountMaxTimestamp", 62),
          ("chromaticDispersionMin", 63),
          ("chromaticDispersionMax", 64),
          ("chromaticDispersionMaxTimestamp", 65),
          ("chromaticDispersionAverage", 66),
          ("broadcastFramesReceived", 67),
          ("multicastFramesReceived", 68),
          ("giantFramesReceived", 69),
          ("runtFramesReceived", 70),
          ("differentialGroupDelayMin", 71),
          ("differentialGroupDelayMax", 72),
          ("differentialGroupDelayMaxTimeStamp", 73),
          ("differentialGroupDelayAverage", 74),
          ("electricalSignalNoiseRatioMin", 75),
          ("electricalSignalNoiseRatioMax", 76),
          ("electricalSignalNoiseRatioMaxTimestamp", 77),
          ("electricalSignalNoiseRatioAverage", 78),
          ("polarizationDependentLossMin", 79),
          ("polarizationDependentLossMax", 80),
          ("polarizationDependentLossMaxTimestamp", 81),
          ("polarizationDependentLossAverage", 82),
          ("pcsBERCount", 83),
          ("shortFramesReceived", 85),
          ("jabberFramesReceived", 86),
          ("broadcastFramesTransmitted", 87),
          ("multicastFramesTransmitted", 88),
          ("giantFramesTransmitted", 89),
          ("runtFramesTransmitted", 90),
          ("shortFramesTransmitted", 91),
          ("jabberFramesTransmitted", 92),
          ("truncatedFrames", 93))
    )

    if mibBuilder.loadTexts:
        description = "Indicates performance parameter."


class PMLogType(TextualConvention, Integer32):
    status = "current"
    subtypeSpec = Integer32.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        SingleValueConstraint(
            *(0,
              1,
              2,
              3,
              4,
              5,
              6)
        )
    )
    namedValues = NamedValues(
        *(("otn", 0),
          ("ethernetWAN", 1),
          ("rmon", 2),
          ("mplsTpAll", 3),
          ("mplsTpAC", 4),
          ("mplsTpLSP", 5),
          ("mplsTpPW", 6))
    )

    if mibBuilder.loadTexts:
        description = "Kind of performance log."


class FileTransferTypeOfProtocol(TextualConvention, Integer32):
    status = "current"
    subtypeSpec = Integer32.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        SingleValueConstraint(
            *(0,
              1,
              2)
        )
    )
    namedValues = NamedValues(
        *(("ftps", 0),
          ("ftp", 1),
          ("sftp", 2))
    )

    if mibBuilder.loadTexts:
        description = "Protocol to use for file transfer."


class UsageState(TextualConvention, Integer32):
    status = "current"
    subtypeSpec = Integer32.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        SingleValueConstraint(
            *(0,
              1)
        )
    )
    namedValues = NamedValues(
        *(("busy", 0),
          ("idle", 1))
    )

    if mibBuilder.loadTexts:
        description = "Indicates whether resource is in use."


class ResetState(TextualConvention, Integer32):
    status = "current"
    subtypeSpec = Integer32.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        SingleValueConstraint(
            *(0,
              1)
        )
    )
    namedValues = NamedValues(
        *(("idle", 0),
          ("resetting", 1))
    )

    if mibBuilder.loadTexts:
        description = "Indicates whether the card is currently executing a reset operation."


class PMStorageControl(TextualConvention, Integer32):
    status = "current"
    subtypeSpec = Integer32.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        SingleValueConstraint(
            *(0,
              1,
              2,
              3,
              4,
              5)
        )
    )
    namedValues = NamedValues(
        *(("notApplicable", 0),
          ("clearHistory15min", 1),
          ("clearHistory24h", 2),
          ("clearCurrentAndPreviousData15min", 3),
          ("clearAllPMData", 4),
          ("clearCurrentAndPreviousData24h", 5))
    )

    if mibBuilder.loadTexts:
        description = "Used to trigger operations which control storage of PM records. The value is only set by the SNMP manager in an SNMP set operation in order to execute the operation. For SNMP retrieval operations the value is always 'notApplicable'."


class HibernationMode(TextualConvention, Integer32):
    status = "current"
    subtypeSpec = Integer32.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        SingleValueConstraint(
            *(0,
              1)
        )
    )
    namedValues = NamedValues(
        *(("alwaysRunning", 0),
          ("auto", 1))
    )

    if mibBuilder.loadTexts:
        description = "Configuration of hibernation."


class HibernationState(TextualConvention, Integer32):
    status = "current"
    subtypeSpec = Integer32.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        SingleValueConstraint(
            *(0,
              1,
              2)
        )
    )
    namedValues = NamedValues(
        *(("running", 0),
          ("hibernating", 1),
          ("runningForTest", 2))
    )

    if mibBuilder.loadTexts:
        description = "Status of hibernation."


# MIB Managed Objects in the order of their OIDs

_CommonMIB_ObjectIdentity = ObjectIdentity
commonMIB = _CommonMIB_ObjectIdentity(
    (1, 3, 6, 1, 4, 1, 52642, 1, 10, 1)
)
_System_ObjectIdentity = ObjectIdentity
system = _System_ObjectIdentity(
    (1, 3, 6, 1, 4, 1, 52642, 1, 10, 1, 1)
)
_SysInfoScalars_ObjectIdentity = ObjectIdentity
sysInfoScalars = _SysInfoScalars_ObjectIdentity(
    (1, 3, 6, 1, 4, 1, 52642, 1, 10, 1, 1, 1)
)
_SysHostName_Type = DisplayString
_SysHostName_Object = MibScalar
sysHostName = _SysHostName_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 10, 1, 1, 1, 1),
    _SysHostName_Type()
)
sysHostName.setMaxAccess("read-write")
if mibBuilder.loadTexts:
    sysHostName.setStatus("current")
if mibBuilder.loadTexts:
    sysHostName.setDescription("Host Name used in CLI prompt. Must be a-z A-Z or 0-9.")
_SysHostAliasName_Type = DisplayString
_SysHostAliasName_Object = MibScalar
sysHostAliasName = _SysHostAliasName_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 10, 1, 1, 1, 2),
    _SysHostAliasName_Type()
)
sysHostAliasName.setMaxAccess("read-write")
if mibBuilder.loadTexts:
    sysHostAliasName.setStatus("current")
if mibBuilder.loadTexts:
    sysHostAliasName.setDescription("Host Alias Name used in Web managment.")
_SysDescription_Type = DisplayString
_SysDescription_Object = MibScalar
sysDescription = _SysDescription_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 10, 1, 1, 1, 3),
    _SysDescription_Type()
)
sysDescription.setMaxAccess("read-write")
if mibBuilder.loadTexts:
    sysDescription.setStatus("current")
if mibBuilder.loadTexts:
    sysDescription.setDescription("Description.")
_SysSwVersion_Type = DisplayString
_SysSwVersion_Object = MibScalar
sysSwVersion = _SysSwVersion_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 10, 1, 1, 1, 4),
    _SysSwVersion_Type()
)
sysSwVersion.setMaxAccess("read-only")
if mibBuilder.loadTexts:
    sysSwVersion.setStatus("current")
if mibBuilder.loadTexts:
    sysSwVersion.setDescription("Description.")
_SysHwVersion_Type = DisplayString
_SysHwVersion_Object = MibScalar
sysHwVersion = _SysHwVersion_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 10, 1, 1, 1, 5),
    _SysHwVersion_Type()
)
sysHwVersion.setMaxAccess("read-only")
if mibBuilder.loadTexts:
    sysHwVersion.setStatus("current")
if mibBuilder.loadTexts:
    sysHwVersion.setDescription("Description.")
_SysSerialNum_Type = DisplayString
_SysSerialNum_Object = MibScalar
sysSerialNum = _SysSerialNum_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 10, 1, 1, 1, 6),
    _SysSerialNum_Type()
)
sysSerialNum.setMaxAccess("read-only")
if mibBuilder.loadTexts:
    sysSerialNum.setStatus("current")
if mibBuilder.loadTexts:
    sysSerialNum.setDescription("Description.")
_SysMacPoolInitAddress_Type = MacAddress
_SysMacPoolInitAddress_Object = MibScalar
sysMacPoolInitAddress = _SysMacPoolInitAddress_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 10, 1, 1, 1, 7),
    _SysMacPoolInitAddress_Type()
)
sysMacPoolInitAddress.setMaxAccess("read-only")
if mibBuilder.loadTexts:
    sysMacPoolInitAddress.setStatus("current")
if mibBuilder.loadTexts:
    sysMacPoolInitAddress.setDescription("The first Mac-address in the system MAC-Address pool")
_SysMacPoolSize_Type = Integer32
_SysMacPoolSize_Object = MibScalar
sysMacPoolSize = _SysMacPoolSize_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 10, 1, 1, 1, 8),
    _SysMacPoolSize_Type()
)
sysMacPoolSize.setMaxAccess("read-only")
if mibBuilder.loadTexts:
    sysMacPoolSize.setStatus("current")
if mibBuilder.loadTexts:
    sysMacPoolSize.setDescription("The number of Mac-Address in the mac-address pool, together with sysMacPoolInitAddress defined the mac-address can be used in the system.")
_SysTemperatureTable_Object = MibTable
sysTemperatureTable = _SysTemperatureTable_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 10, 1, 1, 2)
)
if mibBuilder.loadTexts:
    sysTemperatureTable.setStatus("current")
if mibBuilder.loadTexts:
    sysTemperatureTable.setDescription("SNTP Server Table")
_SysTemperatureEntry_Object = MibTableRow
sysTemperatureEntry = _SysTemperatureEntry_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 10, 1, 1, 2, 1)
)
sysTemperatureEntry.setIndexNames(
    (0, "ST-COMMON-MIB", "sysTempMeterIndex"),
)
if mibBuilder.loadTexts:
    sysTemperatureEntry.setStatus("current")
if mibBuilder.loadTexts:
    sysTemperatureEntry.setDescription(" ")


class _SysTempMeterIndex_Type(Unsigned32):
    """Custom type sysTempMeterIndex based on Unsigned32"""
    subtypeSpec = Unsigned32.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        ValueRangeConstraint(1, 4294967295),
    )


_SysTempMeterIndex_Type.__name__ = "Unsigned32"
_SysTempMeterIndex_Object = MibTableColumn
sysTempMeterIndex = _SysTempMeterIndex_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 10, 1, 1, 2, 1, 1),
    _SysTempMeterIndex_Type()
)
sysTempMeterIndex.setMaxAccess("not-accessible")
if mibBuilder.loadTexts:
    sysTempMeterIndex.setStatus("current")
if mibBuilder.loadTexts:
    sysTempMeterIndex.setDescription("index of temperature meter")
_SysTempMeterDesc_Type = DisplayString
_SysTempMeterDesc_Object = MibTableColumn
sysTempMeterDesc = _SysTempMeterDesc_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 10, 1, 1, 2, 1, 2),
    _SysTempMeterDesc_Type()
)
sysTempMeterDesc.setMaxAccess("read-create")
if mibBuilder.loadTexts:
    sysTempMeterDesc.setStatus("current")
if mibBuilder.loadTexts:
    sysTempMeterDesc.setDescription("The name of temerature meter")


class _SysTempMeterAlarmEnable_Type(Integer32):
    """Custom type sysTempMeterAlarmEnable based on Integer32"""
    subtypeSpec = Integer32.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        SingleValueConstraint(
            *(0,
              1)
        )
    )
    namedValues = NamedValues(
        *(("disable", 0),
          ("enable", 1))
    )


_SysTempMeterAlarmEnable_Type.__name__ = "Integer32"
_SysTempMeterAlarmEnable_Object = MibTableColumn
sysTempMeterAlarmEnable = _SysTempMeterAlarmEnable_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 10, 1, 1, 2, 1, 3),
    _SysTempMeterAlarmEnable_Type()
)
sysTempMeterAlarmEnable.setMaxAccess("read-create")
if mibBuilder.loadTexts:
    sysTempMeterAlarmEnable.setStatus("current")
if mibBuilder.loadTexts:
    sysTempMeterAlarmEnable.setDescription("Temperature Alarm enable/disable")
_SysTempMeterThreshold_Type = Integer32
_SysTempMeterThreshold_Object = MibTableColumn
sysTempMeterThreshold = _SysTempMeterThreshold_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 10, 1, 1, 2, 1, 4),
    _SysTempMeterThreshold_Type()
)
sysTempMeterThreshold.setMaxAccess("read-create")
if mibBuilder.loadTexts:
    sysTempMeterThreshold.setStatus("current")
if mibBuilder.loadTexts:
    sysTempMeterThreshold.setDescription("Temperature exceeds threshold will triger alarm when sysTempMeterAlarmEnable is set enable")
_SysTempTableRowStatus_Type = RowStatus
_SysTempTableRowStatus_Object = MibTableColumn
sysTempTableRowStatus = _SysTempTableRowStatus_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 10, 1, 1, 2, 1, 5),
    _SysTempTableRowStatus_Type()
)
sysTempTableRowStatus.setMaxAccess("read-create")
if mibBuilder.loadTexts:
    sysTempTableRowStatus.setStatus("current")
if mibBuilder.loadTexts:
    sysTempTableRowStatus.setDescription("RowStatus")
_SysMangement_ObjectIdentity = ObjectIdentity
sysMangement = _SysMangement_ObjectIdentity(
    (1, 3, 6, 1, 4, 1, 52642, 1, 10, 1, 2)
)
_SysMangementScalars_ObjectIdentity = ObjectIdentity
sysMangementScalars = _SysMangementScalars_ObjectIdentity(
    (1, 3, 6, 1, 4, 1, 52642, 1, 10, 1, 2, 1)
)
_MaxMgmtEtherPortNum_Type = Integer32
_MaxMgmtEtherPortNum_Object = MibScalar
maxMgmtEtherPortNum = _MaxMgmtEtherPortNum_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 10, 1, 2, 1, 1),
    _MaxMgmtEtherPortNum_Type()
)
maxMgmtEtherPortNum.setMaxAccess("read-only")
if mibBuilder.loadTexts:
    maxMgmtEtherPortNum.setStatus("current")
if mibBuilder.loadTexts:
    maxMgmtEtherPortNum.setDescription("Maximum Ethernet Management interfaces on the controller")
_MaxMgmtVlanIfNum_Type = Integer32
_MaxMgmtVlanIfNum_Object = MibScalar
maxMgmtVlanIfNum = _MaxMgmtVlanIfNum_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 10, 1, 2, 1, 2),
    _MaxMgmtVlanIfNum_Type()
)
maxMgmtVlanIfNum.setMaxAccess("read-only")
if mibBuilder.loadTexts:
    maxMgmtVlanIfNum.setStatus("current")
if mibBuilder.loadTexts:
    maxMgmtVlanIfNum.setDescription("Maximum in-band Management Vlan interfaces ")
_MgmtDefaultRouteGateway_Type = IpAddress
_MgmtDefaultRouteGateway_Object = MibScalar
mgmtDefaultRouteGateway = _MgmtDefaultRouteGateway_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 10, 1, 2, 1, 3),
    _MgmtDefaultRouteGateway_Type()
)
mgmtDefaultRouteGateway.setMaxAccess("read-write")
if mibBuilder.loadTexts:
    mgmtDefaultRouteGateway.setStatus("current")
if mibBuilder.loadTexts:
    mgmtDefaultRouteGateway.setDescription("Default Route for managment network ")
_SysCurrentTime_Type = DisplayString
_SysCurrentTime_Object = MibScalar
sysCurrentTime = _SysCurrentTime_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 10, 1, 2, 1, 4),
    _SysCurrentTime_Type()
)
sysCurrentTime.setMaxAccess("read-write")
if mibBuilder.loadTexts:
    sysCurrentTime.setStatus("current")
if mibBuilder.loadTexts:
    sysCurrentTime.setDescription("Current time of system. using gmt time format with format 'YYYY-MM-DD HH:MM:SS'")
_SysTimeZone_Type = DisplayString
_SysTimeZone_Object = MibScalar
sysTimeZone = _SysTimeZone_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 10, 1, 2, 1, 5),
    _SysTimeZone_Type()
)
sysTimeZone.setMaxAccess("read-write")
if mibBuilder.loadTexts:
    sysTimeZone.setStatus("current")
if mibBuilder.loadTexts:
    sysTimeZone.setDescription("GMT Time zone . format '+ HH:MM' or '- HH:MM'")
_NodeIP_Type = IpAddress
_NodeIP_Object = MibScalar
nodeIP = _NodeIP_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 10, 1, 2, 1, 6),
    _NodeIP_Type()
)
nodeIP.setMaxAccess("read-write")
if mibBuilder.loadTexts:
    nodeIP.setStatus("current")
if mibBuilder.loadTexts:
    nodeIP.setDescription(" Node IP of the Network Element ")
_DefaultRtRedist_Type = Integer32
_DefaultRtRedist_Object = MibScalar
defaultRtRedist = _DefaultRtRedist_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 10, 1, 2, 1, 7),
    _DefaultRtRedist_Type()
)
defaultRtRedist.setMaxAccess("read-write")
if mibBuilder.loadTexts:
    defaultRtRedist.setStatus("current")
if mibBuilder.loadTexts:
    defaultRtRedist.setDescription("option of default route redistribution to ospf")
_MgmtEthIfTable_Object = MibTable
mgmtEthIfTable = _MgmtEthIfTable_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 10, 1, 2, 2)
)
if mibBuilder.loadTexts:
    mgmtEthIfTable.setStatus("current")
if mibBuilder.loadTexts:
    mgmtEthIfTable.setDescription("Management Ethetnet Interface Table")
_MgmtEthIfEntry_Object = MibTableRow
mgmtEthIfEntry = _MgmtEthIfEntry_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 10, 1, 2, 2, 1)
)
mgmtEthIfEntry.setIndexNames(
    (0, "ST-COMMON-MIB", "mgmtEthIfId"),
)
if mibBuilder.loadTexts:
    mgmtEthIfEntry.setStatus("current")
if mibBuilder.loadTexts:
    mgmtEthIfEntry.setDescription(" ")


class _MgmtEthIfId_Type(Unsigned32):
    """Custom type mgmtEthIfId based on Unsigned32"""
    subtypeSpec = Unsigned32.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        ValueRangeConstraint(1, 65535),
    )


_MgmtEthIfId_Type.__name__ = "Unsigned32"
_MgmtEthIfId_Object = MibTableColumn
mgmtEthIfId = _MgmtEthIfId_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 10, 1, 2, 2, 1, 1),
    _MgmtEthIfId_Type()
)
mgmtEthIfId.setMaxAccess("not-accessible")
if mibBuilder.loadTexts:
    mgmtEthIfId.setStatus("current")
if mibBuilder.loadTexts:
    mgmtEthIfId.setDescription("Name of management interface")


class _MgmtEthIfName_Type(DisplayString):
    """Custom type mgmtEthIfName based on DisplayString"""
    subtypeSpec = DisplayString.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        ValueSizeConstraint(0, 20),
    )


_MgmtEthIfName_Type.__name__ = "DisplayString"
_MgmtEthIfName_Object = MibTableColumn
mgmtEthIfName = _MgmtEthIfName_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 10, 1, 2, 2, 1, 2),
    _MgmtEthIfName_Type()
)
mgmtEthIfName.setMaxAccess("read-create")
if mibBuilder.loadTexts:
    mgmtEthIfName.setStatus("current")
if mibBuilder.loadTexts:
    mgmtEthIfName.setDescription("Name of management interface")


class _MgmtEthIfIpMode_Type(Integer32):
    """Custom type mgmtEthIfIpMode based on Integer32"""
    subtypeSpec = Integer32.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        SingleValueConstraint(
            *(0,
              1,
              2)
        )
    )
    namedValues = NamedValues(
        *(("disable", 0),
          ("dhcp", 1),
          ("manual", 2))
    )


_MgmtEthIfIpMode_Type.__name__ = "Integer32"
_MgmtEthIfIpMode_Object = MibTableColumn
mgmtEthIfIpMode = _MgmtEthIfIpMode_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 10, 1, 2, 2, 1, 3),
    _MgmtEthIfIpMode_Type()
)
mgmtEthIfIpMode.setMaxAccess("read-create")
if mibBuilder.loadTexts:
    mgmtEthIfIpMode.setStatus("current")
if mibBuilder.loadTexts:
    mgmtEthIfIpMode.setDescription("The method of system to get managment ip address")
_MgmtEthIfIpAddr_Type = IpAddress
_MgmtEthIfIpAddr_Object = MibTableColumn
mgmtEthIfIpAddr = _MgmtEthIfIpAddr_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 10, 1, 2, 2, 1, 4),
    _MgmtEthIfIpAddr_Type()
)
mgmtEthIfIpAddr.setMaxAccess("read-create")
if mibBuilder.loadTexts:
    mgmtEthIfIpAddr.setStatus("current")
if mibBuilder.loadTexts:
    mgmtEthIfIpAddr.setDescription("Ipv4 address of managment ethernet interface")
_MgmtEthIfIpAddrMask_Type = IpAddress
_MgmtEthIfIpAddrMask_Object = MibTableColumn
mgmtEthIfIpAddrMask = _MgmtEthIfIpAddrMask_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 10, 1, 2, 2, 1, 5),
    _MgmtEthIfIpAddrMask_Type()
)
mgmtEthIfIpAddrMask.setMaxAccess("read-create")
if mibBuilder.loadTexts:
    mgmtEthIfIpAddrMask.setStatus("current")
if mibBuilder.loadTexts:
    mgmtEthIfIpAddrMask.setDescription("Ipv4 address mask ")
_MgmtEthIfTableRowStatus_Type = RowStatus
_MgmtEthIfTableRowStatus_Object = MibTableColumn
mgmtEthIfTableRowStatus = _MgmtEthIfTableRowStatus_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 10, 1, 2, 2, 1, 6),
    _MgmtEthIfTableRowStatus_Type()
)
mgmtEthIfTableRowStatus.setMaxAccess("read-create")
if mibBuilder.loadTexts:
    mgmtEthIfTableRowStatus.setStatus("current")
if mibBuilder.loadTexts:
    mgmtEthIfTableRowStatus.setDescription("RowStatus")


class _MgmtEthOspfEnable_Type(Integer32):
    """Custom type mgmtEthOspfEnable based on Integer32"""
    subtypeSpec = Integer32.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        SingleValueConstraint(
            *(0,
              1)
        )
    )
    namedValues = NamedValues(
        *(("disabled", 0),
          ("enabled", 1))
    )


_MgmtEthOspfEnable_Type.__name__ = "Integer32"
_MgmtEthOspfEnable_Object = MibTableColumn
mgmtEthOspfEnable = _MgmtEthOspfEnable_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 10, 1, 2, 2, 1, 7),
    _MgmtEthOspfEnable_Type()
)
mgmtEthOspfEnable.setMaxAccess("read-create")
if mibBuilder.loadTexts:
    mgmtEthOspfEnable.setStatus("current")
if mibBuilder.loadTexts:
    mgmtEthOspfEnable.setDescription("MGMT Ethernet port ospf enable")
_MgmtEthOspfAreaId_Type = IpAddress
_MgmtEthOspfAreaId_Object = MibTableColumn
mgmtEthOspfAreaId = _MgmtEthOspfAreaId_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 10, 1, 2, 2, 1, 8),
    _MgmtEthOspfAreaId_Type()
)
mgmtEthOspfAreaId.setMaxAccess("read-create")
if mibBuilder.loadTexts:
    mgmtEthOspfAreaId.setStatus("current")
if mibBuilder.loadTexts:
    mgmtEthOspfAreaId.setDescription("OSPF Area Id ")
_MgmtVlanTable_Object = MibTable
mgmtVlanTable = _MgmtVlanTable_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 10, 1, 2, 3)
)
if mibBuilder.loadTexts:
    mgmtVlanTable.setStatus("current")
if mibBuilder.loadTexts:
    mgmtVlanTable.setDescription("Management Ethetnet Interface Table")
_MgmtVlanEntry_Object = MibTableRow
mgmtVlanEntry = _MgmtVlanEntry_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 10, 1, 2, 3, 1)
)
mgmtVlanEntry.setIndexNames(
    (0, "ST-COMMON-MIB", "mgmtVlanId"),
)
if mibBuilder.loadTexts:
    mgmtVlanEntry.setStatus("current")
if mibBuilder.loadTexts:
    mgmtVlanEntry.setDescription(" ")


class _MgmtVlanId_Type(Unsigned32):
    """Custom type mgmtVlanId based on Unsigned32"""
    subtypeSpec = Unsigned32.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        ValueRangeConstraint(1, 4294967295),
    )


_MgmtVlanId_Type.__name__ = "Unsigned32"
_MgmtVlanId_Object = MibTableColumn
mgmtVlanId = _MgmtVlanId_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 10, 1, 2, 3, 1, 1),
    _MgmtVlanId_Type()
)
mgmtVlanId.setMaxAccess("not-accessible")
if mibBuilder.loadTexts:
    mgmtVlanId.setStatus("current")
if mibBuilder.loadTexts:
    mgmtVlanId.setDescription("vlan id that enabled in-band managment")
_MgmtVlanIpAddr_Type = IpAddress
_MgmtVlanIpAddr_Object = MibTableColumn
mgmtVlanIpAddr = _MgmtVlanIpAddr_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 10, 1, 2, 3, 1, 2),
    _MgmtVlanIpAddr_Type()
)
mgmtVlanIpAddr.setMaxAccess("read-create")
if mibBuilder.loadTexts:
    mgmtVlanIpAddr.setStatus("current")
if mibBuilder.loadTexts:
    mgmtVlanIpAddr.setDescription("Ipv4 address of managment ethernet interface")
_MgmtVlanIpAddrMask_Type = IpAddress
_MgmtVlanIpAddrMask_Object = MibTableColumn
mgmtVlanIpAddrMask = _MgmtVlanIpAddrMask_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 10, 1, 2, 3, 1, 3),
    _MgmtVlanIpAddrMask_Type()
)
mgmtVlanIpAddrMask.setMaxAccess("read-create")
if mibBuilder.loadTexts:
    mgmtVlanIpAddrMask.setStatus("current")
if mibBuilder.loadTexts:
    mgmtVlanIpAddrMask.setDescription("Ipv4 address mask ")
_MgmtVlanTableRowStatus_Type = RowStatus
_MgmtVlanTableRowStatus_Object = MibTableColumn
mgmtVlanTableRowStatus = _MgmtVlanTableRowStatus_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 10, 1, 2, 3, 1, 4),
    _MgmtVlanTableRowStatus_Type()
)
mgmtVlanTableRowStatus.setMaxAccess("read-create")
if mibBuilder.loadTexts:
    mgmtVlanTableRowStatus.setStatus("current")
if mibBuilder.loadTexts:
    mgmtVlanTableRowStatus.setDescription("RowStatus")
_SSNTP_ObjectIdentity = ObjectIdentity
sSNTP = _SSNTP_ObjectIdentity(
    (1, 3, 6, 1, 4, 1, 52642, 1, 10, 1, 5)
)
_SntpScalars_ObjectIdentity = ObjectIdentity
sntpScalars = _SntpScalars_ObjectIdentity(
    (1, 3, 6, 1, 4, 1, 52642, 1, 10, 1, 5, 1)
)


class _SntpEnable_Type(Integer32):
    """Custom type sntpEnable based on Integer32"""
    subtypeSpec = Integer32.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        SingleValueConstraint(
            *(0,
              1)
        )
    )
    namedValues = NamedValues(
        *(("disable", 0),
          ("enable", 1))
    )


_SntpEnable_Type.__name__ = "Integer32"
_SntpEnable_Object = MibScalar
sntpEnable = _SntpEnable_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 10, 1, 5, 1, 1),
    _SntpEnable_Type()
)
sntpEnable.setMaxAccess("read-write")
if mibBuilder.loadTexts:
    sntpEnable.setStatus("current")
if mibBuilder.loadTexts:
    sntpEnable.setDescription("Enable/Disable sntp server")


class _SntpSyncInterval_Type(Integer32):
    """Custom type sntpSyncInterval based on Integer32"""
    subtypeSpec = Integer32.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        ValueRangeConstraint(5, 600),
    )


_SntpSyncInterval_Type.__name__ = "Integer32"
_SntpSyncInterval_Object = MibScalar
sntpSyncInterval = _SntpSyncInterval_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 10, 1, 5, 1, 2),
    _SntpSyncInterval_Type()
)
sntpSyncInterval.setMaxAccess("read-write")
if mibBuilder.loadTexts:
    sntpSyncInterval.setStatus("current")
if mibBuilder.loadTexts:
    sntpSyncInterval.setDescription("Inteval to sync with NTP server, Unit is seconde")


class _SntpSyncStatus_Type(Integer32):
    """Custom type sntpSyncStatus based on Integer32"""
    subtypeSpec = Integer32.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        SingleValueConstraint(
            *(0,
              1)
        )
    )
    namedValues = NamedValues(
        *(("notsync", 0),
          ("sync", 1))
    )


_SntpSyncStatus_Type.__name__ = "Integer32"
_SntpSyncStatus_Object = MibScalar
sntpSyncStatus = _SntpSyncStatus_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 10, 1, 5, 1, 3),
    _SntpSyncStatus_Type()
)
sntpSyncStatus.setMaxAccess("read-only")
if mibBuilder.loadTexts:
    sntpSyncStatus.setStatus("current")
if mibBuilder.loadTexts:
    sntpSyncStatus.setDescription("If system synced with NTP")
_SntpLastSyncTime_Type = DisplayString
_SntpLastSyncTime_Object = MibScalar
sntpLastSyncTime = _SntpLastSyncTime_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 10, 1, 5, 1, 4),
    _SntpLastSyncTime_Type()
)
sntpLastSyncTime.setMaxAccess("read-only")
if mibBuilder.loadTexts:
    sntpLastSyncTime.setStatus("current")
if mibBuilder.loadTexts:
    sntpLastSyncTime.setDescription("The most recent time synced with NTP server")
_SntpSyncedTime_Type = DisplayString
_SntpSyncedTime_Object = MibScalar
sntpSyncedTime = _SntpSyncedTime_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 10, 1, 5, 1, 5),
    _SntpSyncedTime_Type()
)
sntpSyncedTime.setMaxAccess("read-only")
if mibBuilder.loadTexts:
    sntpSyncedTime.setStatus("current")
if mibBuilder.loadTexts:
    sntpSyncedTime.setDescription("Current synced time")


class _SntpServerCount_Type(Unsigned32):
    """Custom type sntpServerCount based on Unsigned32"""
    subtypeSpec = Unsigned32.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        ValueRangeConstraint(0, 4294967295),
    )


_SntpServerCount_Type.__name__ = "Unsigned32"
_SntpServerCount_Object = MibScalar
sntpServerCount = _SntpServerCount_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 10, 1, 5, 1, 6),
    _SntpServerCount_Type()
)
sntpServerCount.setMaxAccess("read-only")
if mibBuilder.loadTexts:
    sntpServerCount.setStatus("current")
if mibBuilder.loadTexts:
    sntpServerCount.setDescription("The number of NTP server configured in the system.")
_SntpServerTable_Object = MibTable
sntpServerTable = _SntpServerTable_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 10, 1, 5, 2)
)
if mibBuilder.loadTexts:
    sntpServerTable.setStatus("current")
if mibBuilder.loadTexts:
    sntpServerTable.setDescription("SNTP Server Table")
_SntpServerEntry_Object = MibTableRow
sntpServerEntry = _SntpServerEntry_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 10, 1, 5, 2, 1)
)
sntpServerEntry.setIndexNames(
    (0, "ST-COMMON-MIB", "sntpServerIp"),
)
if mibBuilder.loadTexts:
    sntpServerEntry.setStatus("current")
if mibBuilder.loadTexts:
    sntpServerEntry.setDescription(" ")
_SntpServerIp_Type = IpAddress
_SntpServerIp_Object = MibTableColumn
sntpServerIp = _SntpServerIp_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 10, 1, 5, 2, 1, 1),
    _SntpServerIp_Type()
)
sntpServerIp.setMaxAccess("not-accessible")
if mibBuilder.loadTexts:
    sntpServerIp.setStatus("current")
if mibBuilder.loadTexts:
    sntpServerIp.setDescription("Ipv4 unicast address of NTP server")


class _SntpServerStatus_Type(Integer32):
    """Custom type sntpServerStatus based on Integer32"""
    subtypeSpec = Integer32.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        SingleValueConstraint(
            *(0,
              1,
              2,
              3)
        )
    )
    namedValues = NamedValues(
        *(("unknown", 0),
          ("unreachable", 1),
          ("unsupport", 2),
          ("support", 3))
    )


_SntpServerStatus_Type.__name__ = "Integer32"
_SntpServerStatus_Object = MibTableColumn
sntpServerStatus = _SntpServerStatus_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 10, 1, 5, 2, 1, 2),
    _SntpServerStatus_Type()
)
sntpServerStatus.setMaxAccess("read-only")
if mibBuilder.loadTexts:
    sntpServerStatus.setStatus("current")
if mibBuilder.loadTexts:
    sntpServerStatus.setDescription("status of NTP server")
_SntpServerTableRowStatus_Type = RowStatus
_SntpServerTableRowStatus_Object = MibTableColumn
sntpServerTableRowStatus = _SntpServerTableRowStatus_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 10, 1, 5, 2, 1, 3),
    _SntpServerTableRowStatus_Type()
)
sntpServerTableRowStatus.setMaxAccess("read-create")
if mibBuilder.loadTexts:
    sntpServerTableRowStatus.setStatus("current")
if mibBuilder.loadTexts:
    sntpServerTableRowStatus.setDescription("RowStatus")
_CommonEventsMIB_ObjectIdentity = ObjectIdentity
commonEventsMIB = _CommonEventsMIB_ObjectIdentity(
    (1, 3, 6, 1, 4, 1, 52642, 1, 10, 2)
)
_TrapCategory_Type = EventCategory
_TrapCategory_Object = MibScalar
trapCategory = _TrapCategory_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 10, 2, 1),
    _TrapCategory_Type()
)
trapCategory.setMaxAccess("accessible-for-notify")
if mibBuilder.loadTexts:
    trapCategory.setStatus("current")
if mibBuilder.loadTexts:
    trapCategory.setDescription("Identifies the event category for which changes on the corresponding part of the MIB are summarized in a particular counter.")
_TrapCounterValue_Type = Counter32
_TrapCounterValue_Object = MibScalar
trapCounterValue = _TrapCounterValue_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 10, 2, 2),
    _TrapCounterValue_Type()
)
trapCounterValue.setMaxAccess("read-only")
if mibBuilder.loadTexts:
    trapCounterValue.setStatus("current")
if mibBuilder.loadTexts:
    trapCounterValue.setDescription("Counter for all events of a certain category which are notified. Each time a currentAlarmTrap, a configurationChangedTrap or a protectionSwitchTrap for this manager and event category is issued to the set of currently registered managers this counter value is incremented by one.")
_TypeOfChange_Type = TypeOfChange
_TypeOfChange_Object = MibScalar
typeOfChange = _TypeOfChange_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 10, 2, 3),
    _TypeOfChange_Type()
)
typeOfChange.setMaxAccess("accessible-for-notify")
if mibBuilder.loadTexts:
    typeOfChange.setStatus("current")
if mibBuilder.loadTexts:
    typeOfChange.setDescription("Indicates type of change event.")
_EventTimeStamp_Type = TimeTicks
_EventTimeStamp_Object = MibScalar
eventTimeStamp = _EventTimeStamp_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 10, 2, 4),
    _EventTimeStamp_Type()
)
eventTimeStamp.setMaxAccess("accessible-for-notify")
if mibBuilder.loadTexts:
    eventTimeStamp.setStatus("current")
if mibBuilder.loadTexts:
    eventTimeStamp.setDescription("Time stamp for notification of events occurring in the NE.")
_EventDetailIdentifier_Type = VariablePointer
_EventDetailIdentifier_Object = MibScalar
eventDetailIdentifier = _EventDetailIdentifier_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 10, 2, 5),
    _EventDetailIdentifier_Type()
)
eventDetailIdentifier.setMaxAccess("accessible-for-notify")
if mibBuilder.loadTexts:
    eventDetailIdentifier.setStatus("current")
if mibBuilder.loadTexts:
    eventDetailIdentifier.setDescription("Indicates identifier of scalar object, table entry or object within table entry.")
_EventDetailIntegerValue_Type = Integer32
_EventDetailIntegerValue_Object = MibScalar
eventDetailIntegerValue = _EventDetailIntegerValue_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 10, 2, 6),
    _EventDetailIntegerValue_Type()
)
eventDetailIntegerValue.setMaxAccess("accessible-for-notify")
if mibBuilder.loadTexts:
    eventDetailIntegerValue.setStatus("current")
if mibBuilder.loadTexts:
    eventDetailIntegerValue.setDescription("Indicates changed value of object if eventDetailIdentifier has a syntax derived from INTEGER.")
_EventDetailStringValue_Type = OctetString
_EventDetailStringValue_Object = MibScalar
eventDetailStringValue = _EventDetailStringValue_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 10, 2, 7),
    _EventDetailStringValue_Type()
)
eventDetailStringValue.setMaxAccess("accessible-for-notify")
if mibBuilder.loadTexts:
    eventDetailStringValue.setStatus("current")
if mibBuilder.loadTexts:
    eventDetailStringValue.setDescription("Indicates changed value of object if eventDetailIdentifier has a syntax derived from OCTET STRING.")
_LastChangeFlag_Type = TruthValue
_LastChangeFlag_Object = MibScalar
lastChangeFlag = _LastChangeFlag_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 10, 2, 8),
    _LastChangeFlag_Type()
)
lastChangeFlag.setMaxAccess("accessible-for-notify")
if mibBuilder.loadTexts:
    lastChangeFlag.setStatus("current")
if mibBuilder.loadTexts:
    lastChangeFlag.setDescription("False (true) if further traps related to the originating request will (will not) be sent.")
_AlarmMIB_ObjectIdentity = ObjectIdentity
alarmMIB = _AlarmMIB_ObjectIdentity(
    (1, 3, 6, 1, 4, 1, 52642, 1, 10, 3)
)
_SCurrentAlarmTable_Object = MibTable
sCurrentAlarmTable = _SCurrentAlarmTable_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 10, 3, 1)
)
if mibBuilder.loadTexts:
    sCurrentAlarmTable.setStatus("current")
if mibBuilder.loadTexts:
    sCurrentAlarmTable.setDescription("Active Alarm Table")
_SCurrentAlarmEntry_Object = MibTableRow
sCurrentAlarmEntry = _SCurrentAlarmEntry_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 10, 3, 1, 1)
)
sCurrentAlarmEntry.setIndexNames(
    (0, "ST-COMMON-MIB", "alarmSourceH"),
    (0, "ST-COMMON-MIB", "alarmSourceL"),
    (0, "ST-COMMON-MIB", "alarmCode"),
)
if mibBuilder.loadTexts:
    sCurrentAlarmEntry.setStatus("current")
if mibBuilder.loadTexts:
    sCurrentAlarmEntry.setDescription(" ")


class _AlarmSourceH_Type(Unsigned32):
    """Custom type alarmSourceH based on Unsigned32"""
    subtypeSpec = Unsigned32.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        ValueRangeConstraint(0, 4294967295),
    )


_AlarmSourceH_Type.__name__ = "Unsigned32"
_AlarmSourceH_Object = MibTableColumn
alarmSourceH = _AlarmSourceH_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 10, 3, 1, 1, 1),
    _AlarmSourceH_Type()
)
alarmSourceH.setMaxAccess("not-accessible")
if mibBuilder.loadTexts:
    alarmSourceH.setStatus("current")
if mibBuilder.loadTexts:
    alarmSourceH.setDescription("Gives the high path of the alarm: IfIndex")


class _AlarmSourceL_Type(Unsigned32):
    """Custom type alarmSourceL based on Unsigned32"""
    subtypeSpec = Unsigned32.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        ValueRangeConstraint(0, 4294967295),
    )


_AlarmSourceL_Type.__name__ = "Unsigned32"
_AlarmSourceL_Object = MibTableColumn
alarmSourceL = _AlarmSourceL_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 10, 3, 1, 1, 2),
    _AlarmSourceL_Type()
)
alarmSourceL.setMaxAccess("not-accessible")
if mibBuilder.loadTexts:
    alarmSourceL.setStatus("current")
if mibBuilder.loadTexts:
    alarmSourceL.setDescription("Gives the low path of the alarm: IfIndex")
_AlarmCode_Type = AlarmCode
_AlarmCode_Object = MibTableColumn
alarmCode = _AlarmCode_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 10, 3, 1, 1, 3),
    _AlarmCode_Type()
)
alarmCode.setMaxAccess("not-accessible")
if mibBuilder.loadTexts:
    alarmCode.setStatus("current")
if mibBuilder.loadTexts:
    alarmCode.setDescription("Unique ID identifying the type of alarm.")
_AlarmSourceType_Type = AlarmPathType
_AlarmSourceType_Object = MibTableColumn
alarmSourceType = _AlarmSourceType_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 10, 3, 1, 1, 4),
    _AlarmSourceType_Type()
)
alarmSourceType.setMaxAccess("read-create")
if mibBuilder.loadTexts:
    alarmSourceType.setStatus("current")
if mibBuilder.loadTexts:
    alarmSourceType.setDescription("Gives the path type of the alarm:PON, ONU etc.")


class _AlarmSystemType_Type(DisplayString):
    """Custom type alarmSystemType based on DisplayString"""
    subtypeSpec = DisplayString.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        ValueSizeConstraint(0, 32),
    )


_AlarmSystemType_Type.__name__ = "DisplayString"
_AlarmSystemType_Object = MibTableColumn
alarmSystemType = _AlarmSystemType_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 10, 3, 1, 1, 5),
    _AlarmSystemType_Type()
)
alarmSystemType.setMaxAccess("read-create")
if mibBuilder.loadTexts:
    alarmSystemType.setStatus("current")
if mibBuilder.loadTexts:
    alarmSystemType.setDescription("System Type: SAN3100-G16 etc.")


class _AlarmSeverity_Type(Integer32):
    """Custom type alarmSeverity based on Integer32"""
    subtypeSpec = Integer32.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        SingleValueConstraint(
            *(0,
              1,
              2,
              3,
              4,
              5,
              6)
        )
    )
    namedValues = NamedValues(
        *(("notInAlarm", 0),
          ("warning", 1),
          ("minor", 2),
          ("major", 3),
          ("critical", 4),
          ("indeterminate", 5),
          ("notreport", 6))
    )


_AlarmSeverity_Type.__name__ = "Integer32"
_AlarmSeverity_Object = MibTableColumn
alarmSeverity = _AlarmSeverity_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 10, 3, 1, 1, 6),
    _AlarmSeverity_Type()
)
alarmSeverity.setMaxAccess("read-create")
if mibBuilder.loadTexts:
    alarmSeverity.setStatus("current")
if mibBuilder.loadTexts:
    alarmSeverity.setDescription("The severity of the alarm.")


class _AlarmType_Type(Unsigned32):
    """Custom type alarmType based on Unsigned32"""
    defaultValue = 0


_AlarmType_Type.__name__ = "Unsigned32"
_AlarmType_Object = MibTableColumn
alarmType = _AlarmType_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 10, 3, 1, 1, 7),
    _AlarmType_Type()
)
alarmType.setMaxAccess("read-create")
if mibBuilder.loadTexts:
    alarmType.setStatus("current")
if mibBuilder.loadTexts:
    alarmType.setDescription("Made up of 4 bytes, CardType(higher 2 bytes)+AlarmType(lower 2 bytes)")


class _AlarmProbableCause_Type(DisplayString):
    """Custom type alarmProbableCause based on DisplayString"""
    subtypeSpec = DisplayString.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        ValueSizeConstraint(0, 32),
    )


_AlarmProbableCause_Type.__name__ = "DisplayString"
_AlarmProbableCause_Object = MibTableColumn
alarmProbableCause = _AlarmProbableCause_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 10, 3, 1, 1, 8),
    _AlarmProbableCause_Type()
)
alarmProbableCause.setMaxAccess("read-create")
if mibBuilder.loadTexts:
    alarmProbableCause.setStatus("current")
if mibBuilder.loadTexts:
    alarmProbableCause.setDescription("Short description of the alarm according to event code")


class _AlarmState_Type(AlarmState):
    """Custom type alarmState based on AlarmState"""
    subtypeSpec = AlarmState.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        SingleValueConstraint(
            *(1,
              2)
        )
    )
    namedValues = NamedValues(
        *(("set", 1),
          ("clear", 2))
    )


_AlarmState_Type.__name__ = "AlarmState"
_AlarmState_Object = MibTableColumn
alarmState = _AlarmState_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 10, 3, 1, 1, 9),
    _AlarmState_Type()
)
alarmState.setMaxAccess("read-create")
if mibBuilder.loadTexts:
    alarmState.setStatus("current")
if mibBuilder.loadTexts:
    alarmState.setDescription("Alarm state")
_AlarmDTS_Type = Unsigned32
_AlarmDTS_Object = MibTableColumn
alarmDTS = _AlarmDTS_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 10, 3, 1, 1, 10),
    _AlarmDTS_Type()
)
alarmDTS.setMaxAccess("read-create")
if mibBuilder.loadTexts:
    alarmDTS.setStatus("current")
if mibBuilder.loadTexts:
    alarmDTS.setDescription("Indicates the time at which the alarm state changes.")
_AlarmTimeStamp_Type = DateAndTime
_AlarmTimeStamp_Object = MibTableColumn
alarmTimeStamp = _AlarmTimeStamp_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 10, 3, 1, 1, 11),
    _AlarmTimeStamp_Type()
)
alarmTimeStamp.setMaxAccess("read-only")
if mibBuilder.loadTexts:
    alarmTimeStamp.setStatus("current")
if mibBuilder.loadTexts:
    alarmTimeStamp.setDescription("As a member of CurrentAlarmEntry it indicates when this alarm was raised. As part of a currentAlarmTrap it indicates when the alarm was raised resp. cleared.")


class _AlarmRowStatus_Type(RowStatus):
    """Custom type alarmRowStatus based on RowStatus"""
    subtypeSpec = RowStatus.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        SingleValueConstraint(
            *(1,
              2,
              3,
              4,
              5,
              6)
        )
    )
    namedValues = NamedValues(
        *(("active", 1),
          ("notInService", 2),
          ("notReady", 3),
          ("createAndGo", 4),
          ("createAndWait", 5),
          ("destroy", 6))
    )


_AlarmRowStatus_Type.__name__ = "RowStatus"
_AlarmRowStatus_Object = MibTableColumn
alarmRowStatus = _AlarmRowStatus_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 10, 3, 1, 1, 12),
    _AlarmRowStatus_Type()
)
alarmRowStatus.setMaxAccess("read-create")
if mibBuilder.loadTexts:
    alarmRowStatus.setStatus("current")
if mibBuilder.loadTexts:
    alarmRowStatus.setDescription("Row Status of active alarm table.")
_AlarmEventsMIB_ObjectIdentity = ObjectIdentity
alarmEventsMIB = _AlarmEventsMIB_ObjectIdentity(
    (1, 3, 6, 1, 4, 1, 52642, 1, 10, 4)
)
_ConfigMIB_ObjectIdentity = ObjectIdentity
configMIB = _ConfigMIB_ObjectIdentity(
    (1, 3, 6, 1, 4, 1, 52642, 1, 10, 7)
)
_ShelfTable_Object = MibTable
shelfTable = _ShelfTable_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 10, 7, 1)
)
if mibBuilder.loadTexts:
    shelfTable.setStatus("current")
if mibBuilder.loadTexts:
    shelfTable.setDescription("Table of present shelves within this NE.")
_ShelfEntry_Object = MibTableRow
shelfEntry = _ShelfEntry_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 10, 7, 1, 1)
)
shelfEntry.setIndexNames(
    (0, "ST-COMMON-MIB", "shelfId"),
)
if mibBuilder.loadTexts:
    shelfEntry.setStatus("current")
if mibBuilder.loadTexts:
    shelfEntry.setDescription("Additional parameters for present shelf.")


class _ShelfId_Type(Unsigned32):
    """Custom type shelfId based on Unsigned32"""
    subtypeSpec = Unsigned32.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        ValueRangeConstraint(1, 128),
    )


_ShelfId_Type.__name__ = "Unsigned32"
_ShelfId_Object = MibTableColumn
shelfId = _ShelfId_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 10, 7, 1, 1, 1),
    _ShelfId_Type()
)
shelfId.setMaxAccess("not-accessible")
if mibBuilder.loadTexts:
    shelfId.setStatus("current")
if mibBuilder.loadTexts:
    shelfId.setDescription("Shelf identifier of equipment supporting the object.")


class _ShelfType_Type(Integer32):
    """Custom type shelfType based on Integer32"""
    defaultValue = 0

    subtypeSpec = Integer32.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        SingleValueConstraint(
            *(0,
              20,
              21,
              22,
              23,
              24,
              25,
              26,
              27,
              28,
              29,
              30,
              31,
              32,
              40,
              41,
              51,
              52,
              53,
              60,
              61,
              62,
              63,
              64,
              70,
              71,
              72,
              73,
              74,
              75,
              76,
              77,
              78,
              79,
              80,
              81,
              82,
              90,
              95,
              96,
              97,
              100,
              101,
              120,
              121,
              122)
        )
    )
    namedValues = NamedValues(
        *(("unknown", 0),
          ("otnSingleCard", 20),
          ("otn1uAC", 21),
          ("otn1uDC", 22),
          ("otn2uDC", 23),
          ("otn10uDC", 24),
          ("otn1uA", 25),
          ("stn2uAC", 26),
          ("stn2uDC", 27),
          ("stn1u", 28),
          ("stnRpc6480", 29),
          ("otn6250DC", 30),
          ("otn6250AC", 31),
          ("otn6250DCAC", 32),
          ("stnB1U", 40),
          ("stnB2U", 41),
          ("olt1uA", 51),
          ("olt1uB", 52),
          ("olt10uDC", 53),
          ("otn10uDC4halfslot", 60),
          ("otn10uDC6halfslot", 61),
          ("otn10uDC8halfslot", 62),
          ("otn10uDC10halfslot", 63),
          ("otn10uDC12halfslot", 64),
          ("otn5uDC7F", 70),
          ("otn5uDC6F", 71),
          ("otn5uDC4F", 72),
          ("otn5uDC2F", 73),
          ("otn5uDC0F", 74),
          ("otn5uAC6F", 75),
          ("otn5uAC4F", 76),
          ("otn5uAC2F", 77),
          ("otn5uAC0F", 78),
          ("otn5uDCAC6F", 79),
          ("otn5uDCAC4F", 80),
          ("otn5uDCAC2F", 81),
          ("otn5uDCAC0F", 82),
          ("stnIA1u", 90),
          ("stn5uDC", 95),
          ("stn5uAC", 96),
          ("stn5uDCAC", 97),
          ("dci1ua", 100),
          ("mibmax", 101),
          ("stn3uDC", 120),
          ("stn3uAC", 121),
          ("stn3uDCAC", 122))
    )


_ShelfType_Type.__name__ = "Integer32"
_ShelfType_Object = MibTableColumn
shelfType = _ShelfType_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 10, 7, 1, 1, 2),
    _ShelfType_Type()
)
shelfType.setMaxAccess("read-only")
if mibBuilder.loadTexts:
    shelfType.setStatus("current")
if mibBuilder.loadTexts:
    shelfType.setDescription("The shelf type")


class _ShelfSN_Type(DisplayString):
    """Custom type shelfSN based on DisplayString"""
    defaultValue = OctetString("")

    subtypeSpec = DisplayString.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        ValueSizeConstraint(0, 50),
    )


_ShelfSN_Type.__name__ = "DisplayString"
_ShelfSN_Object = MibTableColumn
shelfSN = _ShelfSN_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 10, 7, 1, 1, 3),
    _ShelfSN_Type()
)
shelfSN.setMaxAccess("read-only")
if mibBuilder.loadTexts:
    shelfSN.setStatus("current")
if mibBuilder.loadTexts:
    shelfSN.setDescription("serial number of shelf")


class _ShelfPartNumber_Type(DisplayString):
    """Custom type shelfPartNumber based on DisplayString"""
    defaultValue = OctetString("")

    subtypeSpec = DisplayString.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        ValueSizeConstraint(0, 50),
    )


_ShelfPartNumber_Type.__name__ = "DisplayString"
_ShelfPartNumber_Object = MibTableColumn
shelfPartNumber = _ShelfPartNumber_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 10, 7, 1, 1, 4),
    _ShelfPartNumber_Type()
)
shelfPartNumber.setMaxAccess("read-only")
if mibBuilder.loadTexts:
    shelfPartNumber.setStatus("current")
if mibBuilder.loadTexts:
    shelfPartNumber.setDescription("part number of shelf")


class _ShelfCLEI_Type(DisplayString):
    """Custom type shelfCLEI based on DisplayString"""
    defaultValue = OctetString("")

    subtypeSpec = DisplayString.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        ValueSizeConstraint(0, 20),
    )


_ShelfCLEI_Type.__name__ = "DisplayString"
_ShelfCLEI_Object = MibTableColumn
shelfCLEI = _ShelfCLEI_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 10, 7, 1, 1, 5),
    _ShelfCLEI_Type()
)
shelfCLEI.setMaxAccess("read-only")
if mibBuilder.loadTexts:
    shelfCLEI.setStatus("current")
if mibBuilder.loadTexts:
    shelfCLEI.setDescription("Common Language Interface of shelf")


class _ShelfHwVersion_Type(DisplayString):
    """Custom type shelfHwVersion based on DisplayString"""
    defaultValue = OctetString("")

    subtypeSpec = DisplayString.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        ValueSizeConstraint(0, 20),
    )


_ShelfHwVersion_Type.__name__ = "DisplayString"
_ShelfHwVersion_Object = MibTableColumn
shelfHwVersion = _ShelfHwVersion_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 10, 7, 1, 1, 6),
    _ShelfHwVersion_Type()
)
shelfHwVersion.setMaxAccess("read-only")
if mibBuilder.loadTexts:
    shelfHwVersion.setStatus("current")
if mibBuilder.loadTexts:
    shelfHwVersion.setDescription("hardware's version of shelf")


class _ShelfMacAddressMgmt1_Type(DisplayString):
    """Custom type shelfMacAddressMgmt1 based on DisplayString"""
    subtypeSpec = DisplayString.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        ValueSizeConstraint(0, 20),
    )


_ShelfMacAddressMgmt1_Type.__name__ = "DisplayString"
_ShelfMacAddressMgmt1_Object = MibTableColumn
shelfMacAddressMgmt1 = _ShelfMacAddressMgmt1_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 10, 7, 1, 1, 7),
    _ShelfMacAddressMgmt1_Type()
)
shelfMacAddressMgmt1.setMaxAccess("read-only")
if mibBuilder.loadTexts:
    shelfMacAddressMgmt1.setStatus("current")
if mibBuilder.loadTexts:
    shelfMacAddressMgmt1.setDescription("the mac address of managment port 1")


class _ShelfMacAddressMgmt2_Type(DisplayString):
    """Custom type shelfMacAddressMgmt2 based on DisplayString"""
    subtypeSpec = DisplayString.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        ValueSizeConstraint(0, 20),
    )


_ShelfMacAddressMgmt2_Type.__name__ = "DisplayString"
_ShelfMacAddressMgmt2_Object = MibTableColumn
shelfMacAddressMgmt2 = _ShelfMacAddressMgmt2_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 10, 7, 1, 1, 8),
    _ShelfMacAddressMgmt2_Type()
)
shelfMacAddressMgmt2.setMaxAccess("read-only")
if mibBuilder.loadTexts:
    shelfMacAddressMgmt2.setStatus("current")
if mibBuilder.loadTexts:
    shelfMacAddressMgmt2.setDescription("the mac address of managment port 2")


class _ShelfMacAddressMgmt3_Type(DisplayString):
    """Custom type shelfMacAddressMgmt3 based on DisplayString"""
    subtypeSpec = DisplayString.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        ValueSizeConstraint(0, 20),
    )


_ShelfMacAddressMgmt3_Type.__name__ = "DisplayString"
_ShelfMacAddressMgmt3_Object = MibTableColumn
shelfMacAddressMgmt3 = _ShelfMacAddressMgmt3_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 10, 7, 1, 1, 9),
    _ShelfMacAddressMgmt3_Type()
)
shelfMacAddressMgmt3.setMaxAccess("read-only")
if mibBuilder.loadTexts:
    shelfMacAddressMgmt3.setStatus("current")
if mibBuilder.loadTexts:
    shelfMacAddressMgmt3.setDescription("the mac address of managment port 3")


class _ShelfAssignShelfId_Type(Unsigned32):
    """Custom type shelfAssignShelfId based on Unsigned32"""
    subtypeSpec = Unsigned32.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        ValueRangeConstraint(1, 128),
    )


_ShelfAssignShelfId_Type.__name__ = "Unsigned32"
_ShelfAssignShelfId_Object = MibTableColumn
shelfAssignShelfId = _ShelfAssignShelfId_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 10, 7, 1, 1, 10),
    _ShelfAssignShelfId_Type()
)
shelfAssignShelfId.setMaxAccess("read-write")
if mibBuilder.loadTexts:
    shelfAssignShelfId.setStatus("current")
if mibBuilder.loadTexts:
    shelfAssignShelfId.setDescription("Used to match unassigned shelf to required shelf.")


class _ShelfSwVersion_Type(DisplayString):
    """Custom type shelfSwVersion based on DisplayString"""
    defaultValue = OctetString("")

    subtypeSpec = DisplayString.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        ValueSizeConstraint(0, 30),
    )


_ShelfSwVersion_Type.__name__ = "DisplayString"
_ShelfSwVersion_Object = MibTableColumn
shelfSwVersion = _ShelfSwVersion_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 10, 7, 1, 1, 11),
    _ShelfSwVersion_Type()
)
shelfSwVersion.setMaxAccess("read-only")
if mibBuilder.loadTexts:
    shelfSwVersion.setStatus("current")
if mibBuilder.loadTexts:
    shelfSwVersion.setDescription("software version of shelf")


class _ShelfLocation_Type(DisplayString):
    """Custom type shelfLocation based on DisplayString"""
    defaultValue = OctetString("")

    subtypeSpec = DisplayString.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        ValueSizeConstraint(0, 240),
    )


_ShelfLocation_Type.__name__ = "DisplayString"
_ShelfLocation_Object = MibTableColumn
shelfLocation = _ShelfLocation_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 10, 7, 1, 1, 12),
    _ShelfLocation_Type()
)
shelfLocation.setMaxAccess("read-write")
if mibBuilder.loadTexts:
    shelfLocation.setStatus("current")
if mibBuilder.loadTexts:
    shelfLocation.setDescription("Physical location of a particular shelf within NE arrangement.")
_ShelfTemperature_Type = Integer32
_ShelfTemperature_Object = MibTableColumn
shelfTemperature = _ShelfTemperature_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 10, 7, 1, 1, 13),
    _ShelfTemperature_Type()
)
shelfTemperature.setMaxAccess("read-only")
if mibBuilder.loadTexts:
    shelfTemperature.setStatus("current")
if mibBuilder.loadTexts:
    shelfTemperature.setDescription("temperature of shelf")


class _ShelfMDI1Enable_Type(Integer32):
    """Custom type shelfMDI1Enable based on Integer32"""
    defaultValue = 0

    subtypeSpec = Integer32.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        SingleValueConstraint(
            *(0,
              1)
        )
    )
    namedValues = NamedValues(
        *(("disable", 0),
          ("enable", 1))
    )


_ShelfMDI1Enable_Type.__name__ = "Integer32"
_ShelfMDI1Enable_Object = MibTableColumn
shelfMDI1Enable = _ShelfMDI1Enable_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 10, 7, 1, 1, 14),
    _ShelfMDI1Enable_Type()
)
shelfMDI1Enable.setMaxAccess("read-write")
if mibBuilder.loadTexts:
    shelfMDI1Enable.setStatus("current")
if mibBuilder.loadTexts:
    shelfMDI1Enable.setDescription("enable/disable MDI1 detection, default is disable (no alarm reported), when it��s enabled, MDI alarm shall be raised if no input detected")


class _ShelfMDI2Enable_Type(Integer32):
    """Custom type shelfMDI2Enable based on Integer32"""
    defaultValue = 0

    subtypeSpec = Integer32.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        SingleValueConstraint(
            *(0,
              1)
        )
    )
    namedValues = NamedValues(
        *(("disable", 0),
          ("enable", 1))
    )


_ShelfMDI2Enable_Type.__name__ = "Integer32"
_ShelfMDI2Enable_Object = MibTableColumn
shelfMDI2Enable = _ShelfMDI2Enable_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 10, 7, 1, 1, 15),
    _ShelfMDI2Enable_Type()
)
shelfMDI2Enable.setMaxAccess("read-write")
if mibBuilder.loadTexts:
    shelfMDI2Enable.setStatus("current")
if mibBuilder.loadTexts:
    shelfMDI2Enable.setDescription("enable/disable MDI2 detection, default is disabled(no alarm reported), when it��s enabled, MDI alarm shall be raised if no input detected")


class _ShelfMDO1Condition_Type(Integer32):
    """Custom type shelfMDO1Condition based on Integer32"""
    defaultValue = 0

    subtypeSpec = Integer32.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        SingleValueConstraint(
            *(0,
              1,
              2,
              3,
              4)
        )
    )
    namedValues = NamedValues(
        *(("none", 0),
          ("critical", 1),
          ("criticalAndMajor", 2),
          ("criticalAndMajorAndMinor", 3),
          ("userDefined", 4))
    )


_ShelfMDO1Condition_Type.__name__ = "Integer32"
_ShelfMDO1Condition_Object = MibTableColumn
shelfMDO1Condition = _ShelfMDO1Condition_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 10, 7, 1, 1, 16),
    _ShelfMDO1Condition_Type()
)
shelfMDO1Condition.setMaxAccess("read-write")
if mibBuilder.loadTexts:
    shelfMDO1Condition.setStatus("current")
if mibBuilder.loadTexts:
    shelfMDO1Condition.setDescription("If the value is critical, any critical alarm will set MDO, if value is critical&major&minor, any critical or major or minor alarm will set MDO, if value is userDefined, the MDO is always set")


class _ShelfMDO2Condition_Type(Integer32):
    """Custom type shelfMDO2Condition based on Integer32"""
    defaultValue = 0

    subtypeSpec = Integer32.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        SingleValueConstraint(
            *(0,
              1,
              2,
              3,
              4)
        )
    )
    namedValues = NamedValues(
        *(("none", 0),
          ("critical", 1),
          ("criticalAndMajor", 2),
          ("criticalAndMajorAndMinor", 3),
          ("userDefined", 4))
    )


_ShelfMDO2Condition_Type.__name__ = "Integer32"
_ShelfMDO2Condition_Object = MibTableColumn
shelfMDO2Condition = _ShelfMDO2Condition_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 10, 7, 1, 1, 17),
    _ShelfMDO2Condition_Type()
)
shelfMDO2Condition.setMaxAccess("read-write")
if mibBuilder.loadTexts:
    shelfMDO2Condition.setStatus("current")
if mibBuilder.loadTexts:
    shelfMDO2Condition.setDescription("If the value is critical, any critical alarm will set MDO, if value is critical&major&minor, any critical or major or minor alarm will set MDO, if value is userDefined, the MDO is always set")


class _ShelfLampTest_Type(Integer32):
    """Custom type shelfLampTest based on Integer32"""
    defaultValue = 0

    subtypeSpec = Integer32.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        SingleValueConstraint(
            *(0,
              1)
        )
    )
    namedValues = NamedValues(
        *(("false", 0),
          ("true", 1))
    )


_ShelfLampTest_Type.__name__ = "Integer32"
_ShelfLampTest_Object = MibTableColumn
shelfLampTest = _ShelfLampTest_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 10, 7, 1, 1, 18),
    _ShelfLampTest_Type()
)
shelfLampTest.setMaxAccess("read-write")
if mibBuilder.loadTexts:
    shelfLampTest.setStatus("current")
if mibBuilder.loadTexts:
    shelfLampTest.setDescription("trigger the lamp test on shelf")


class _ShelfAlarmCutOperation_Type(Integer32):
    """Custom type shelfAlarmCutOperation based on Integer32"""
    defaultValue = 0

    subtypeSpec = Integer32.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        SingleValueConstraint(
            *(0,
              1)
        )
    )
    namedValues = NamedValues(
        *(("false", 0),
          ("true", 1))
    )


_ShelfAlarmCutOperation_Type.__name__ = "Integer32"
_ShelfAlarmCutOperation_Object = MibTableColumn
shelfAlarmCutOperation = _ShelfAlarmCutOperation_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 10, 7, 1, 1, 19),
    _ShelfAlarmCutOperation_Type()
)
shelfAlarmCutOperation.setMaxAccess("read-write")
if mibBuilder.loadTexts:
    shelfAlarmCutOperation.setStatus("current")
if mibBuilder.loadTexts:
    shelfAlarmCutOperation.setDescription("trigger to cut off the alarm on shelf")
_ShelfRowStatus_Type = RowStatus
_ShelfRowStatus_Object = MibTableColumn
shelfRowStatus = _ShelfRowStatus_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 10, 7, 1, 1, 20),
    _ShelfRowStatus_Type()
)
shelfRowStatus.setMaxAccess("read-only")
if mibBuilder.loadTexts:
    shelfRowStatus.setStatus("current")
if mibBuilder.loadTexts:
    shelfRowStatus.setReference("NE-FSpec CM - Shelf.sysAvailable")
if mibBuilder.loadTexts:
    shelfRowStatus.setDescription("Used in order to create a shelf. The following values can be used with a semantics according to RFC 2579: - active: returned if subsystem managing this shelf is available - notReady: returned if subsystem managing this shelf is not available - createAndGo: set by the manager in order to add a present shelf (not for locked ONN and OLR) - destroy: set by the manager in order to remove a present shelf (not for locked ONN and OLR)")
_ShelfFanSpeedPwm_Type = Integer32
_ShelfFanSpeedPwm_Object = MibTableColumn
shelfFanSpeedPwm = _ShelfFanSpeedPwm_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 10, 7, 1, 1, 21),
    _ShelfFanSpeedPwm_Type()
)
shelfFanSpeedPwm.setMaxAccess("read-only")
if mibBuilder.loadTexts:
    shelfFanSpeedPwm.setStatus("current")
if mibBuilder.loadTexts:
    shelfFanSpeedPwm.setDescription("Current FAN speed, in duty ratio")


class _ShelfFanTopSpeed_Type(Integer32):
    """Custom type shelfFanTopSpeed based on Integer32"""
    defaultValue = 0

    subtypeSpec = Integer32.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        SingleValueConstraint(
            *(0,
              1)
        )
    )
    namedValues = NamedValues(
        *(("false", 0),
          ("true", 1))
    )


_ShelfFanTopSpeed_Type.__name__ = "Integer32"
_ShelfFanTopSpeed_Object = MibTableColumn
shelfFanTopSpeed = _ShelfFanTopSpeed_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 10, 7, 1, 1, 22),
    _ShelfFanTopSpeed_Type()
)
shelfFanTopSpeed.setMaxAccess("read-only")
if mibBuilder.loadTexts:
    shelfFanTopSpeed.setStatus("current")
if mibBuilder.loadTexts:
    shelfFanTopSpeed.setDescription("trigger FAN top speed by Hardware")


class _ShelfFanSpeedAutoRegulating_Type(Integer32):
    """Custom type shelfFanSpeedAutoRegulating based on Integer32"""
    defaultValue = 1

    subtypeSpec = Integer32.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        SingleValueConstraint(
            *(0,
              1)
        )
    )
    namedValues = NamedValues(
        *(("disable", 0),
          ("enable", 1))
    )


_ShelfFanSpeedAutoRegulating_Type.__name__ = "Integer32"
_ShelfFanSpeedAutoRegulating_Object = MibTableColumn
shelfFanSpeedAutoRegulating = _ShelfFanSpeedAutoRegulating_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 10, 7, 1, 1, 23),
    _ShelfFanSpeedAutoRegulating_Type()
)
shelfFanSpeedAutoRegulating.setMaxAccess("read-write")
if mibBuilder.loadTexts:
    shelfFanSpeedAutoRegulating.setStatus("current")
if mibBuilder.loadTexts:
    shelfFanSpeedAutoRegulating.setDescription("the way of fan speed auto-regulating")


class _ShelfOneIpRole_Type(Integer32):
    """Custom type shelfOneIpRole based on Integer32"""
    defaultValue = 0

    subtypeSpec = Integer32.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        SingleValueConstraint(
            *(0,
              1,
              2,
              3,
              4,
              5,
              6)
        )
    )
    namedValues = NamedValues(
        *(("normal", 0),
          ("gwne", 1),
          ("subne1", 2),
          ("subne2", 3),
          ("subne3", 4),
          ("subne4", 5),
          ("subne5", 6))
    )


_ShelfOneIpRole_Type.__name__ = "Integer32"
_ShelfOneIpRole_Object = MibTableColumn
shelfOneIpRole = _ShelfOneIpRole_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 10, 7, 1, 1, 24),
    _ShelfOneIpRole_Type()
)
shelfOneIpRole.setMaxAccess("read-write")
if mibBuilder.loadTexts:
    shelfOneIpRole.setStatus("current")
if mibBuilder.loadTexts:
    shelfOneIpRole.setDescription("NE role for One IP feature")
_SlotTable_Object = MibTable
slotTable = _SlotTable_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 10, 7, 2)
)
if mibBuilder.loadTexts:
    slotTable.setStatus("current")
if mibBuilder.loadTexts:
    slotTable.setDescription("Table with common management information related to all slots in required and overequipped shelves of the NE. The table contains always one row for each available slot (according to the NE type) of a provisioned or overequipped shelf. Card creation and deletion in slots of a provisioned shelf are depicted through changes of cardRequiredCardType, card insertion and removal in slots of a provisioned or overequipped shelf are depicted through changes of cardActualCardType for the corresponding cardEntry.")
_SlotEntry_Object = MibTableRow
slotEntry = _SlotEntry_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 10, 7, 2, 1)
)
slotEntry.setIndexNames(
    (0, "ST-COMMON-MIB", "shelfId"),
    (0, "ST-COMMON-MIB", "slotNo"),
    (0, "ST-COMMON-MIB", "subSlotNo"),
)
if mibBuilder.loadTexts:
    slotEntry.setStatus("current")
if mibBuilder.loadTexts:
    slotEntry.setDescription("Represents a particular slot within a required or overequipped shelf of the NE. An empty slot, i.e. a slot that is intended to be empty and has no physical card equipped, is depicted through a card with card type 'empty'. Both images of card type (cardRequiredCardType and cardActualCardType) are set to 'empty'. An empty slot that is prepared to accept cards which are not required but can be recognized by the NE is indicated by cardRequiredCardType 'emptyAuto' and cardActualCardType 'empty'. Once a known card is plugged and accepted by the NE, the required and actual card type will match the card type of the plugged card.")


class _SlotNo_Type(Unsigned32):
    """Custom type slotNo based on Unsigned32"""
    subtypeSpec = Unsigned32.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        ValueRangeConstraint(1, 64),
    )


_SlotNo_Type.__name__ = "Unsigned32"
_SlotNo_Object = MibTableColumn
slotNo = _SlotNo_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 10, 7, 2, 1, 1),
    _SlotNo_Type()
)
slotNo.setMaxAccess("not-accessible")
if mibBuilder.loadTexts:
    slotNo.setStatus("current")
if mibBuilder.loadTexts:
    slotNo.setDescription("The numerical slot position for this card.")


class _SubSlotNo_Type(Unsigned32):
    """Custom type subSlotNo based on Unsigned32"""
    subtypeSpec = Unsigned32.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        ValueRangeConstraint(0, 8),
    )


_SubSlotNo_Type.__name__ = "Unsigned32"
_SubSlotNo_Object = MibTableColumn
subSlotNo = _SubSlotNo_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 10, 7, 2, 1, 2),
    _SubSlotNo_Type()
)
subSlotNo.setMaxAccess("not-accessible")
if mibBuilder.loadTexts:
    subSlotNo.setStatus("current")
if mibBuilder.loadTexts:
    subSlotNo.setDescription("some slot have sub slot")


class _SlotRequiredCardType_Type(CardType):
    """Custom type slotRequiredCardType based on CardType"""
    defaultValue = 0


_SlotRequiredCardType_Type.__name__ = "CardType"
_SlotRequiredCardType_Object = MibTableColumn
slotRequiredCardType = _SlotRequiredCardType_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 10, 7, 2, 1, 3),
    _SlotRequiredCardType_Type()
)
slotRequiredCardType.setMaxAccess("read-write")
if mibBuilder.loadTexts:
    slotRequiredCardType.setStatus("current")
if mibBuilder.loadTexts:
    slotRequiredCardType.setDescription("Provisioning card type")


class _SlotActualCardType_Type(CardType):
    """Custom type slotActualCardType based on CardType"""
    defaultValue = 0


_SlotActualCardType_Type.__name__ = "CardType"
_SlotActualCardType_Object = MibTableColumn
slotActualCardType = _SlotActualCardType_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 10, 7, 2, 1, 4),
    _SlotActualCardType_Type()
)
slotActualCardType.setMaxAccess("read-write")
if mibBuilder.loadTexts:
    slotActualCardType.setStatus("current")
if mibBuilder.loadTexts:
    slotActualCardType.setDescription("actual card type")


class _SlotAdminState_Type(Integer32):
    """Custom type slotAdminState based on Integer32"""
    defaultValue = 1

    subtypeSpec = Integer32.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        SingleValueConstraint(
            *(0,
              1)
        )
    )
    namedValues = NamedValues(
        *(("disabled", 0),
          ("enabled", 1))
    )


_SlotAdminState_Type.__name__ = "Integer32"
_SlotAdminState_Object = MibTableColumn
slotAdminState = _SlotAdminState_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 10, 7, 2, 1, 5),
    _SlotAdminState_Type()
)
slotAdminState.setMaxAccess("read-write")
if mibBuilder.loadTexts:
    slotAdminState.setStatus("current")
if mibBuilder.loadTexts:
    slotAdminState.setDescription(" ")


class _SlotOperationState_Type(Integer32):
    """Custom type slotOperationState based on Integer32"""
    defaultValue = 1

    subtypeSpec = Integer32.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        SingleValueConstraint(
            *(0,
              1,
              2,
              3,
              4,
              5,
              6,
              7)
        )
    )
    namedValues = NamedValues(
        *(("unknown", 0),
          ("available", 1),
          ("absent", 2),
          ("mismatch", 3),
          ("operational", 4),
          ("failure", 5),
          ("disabled", 6),
          ("initializing", 7))
    )


_SlotOperationState_Type.__name__ = "Integer32"
_SlotOperationState_Object = MibTableColumn
slotOperationState = _SlotOperationState_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 10, 7, 2, 1, 6),
    _SlotOperationState_Type()
)
slotOperationState.setMaxAccess("read-write")
if mibBuilder.loadTexts:
    slotOperationState.setStatus("current")
if mibBuilder.loadTexts:
    slotOperationState.setDescription(" AVAIL = 1, // Available ABSENT, // Absent MISMATCH, // Mismatch with logical card OPERATIONAL, // Operation FAILURE, // hardware failure DISABLED, // Slot is disabled INITIALIZING, // Initializing ")
_SlotRowStatus_Type = RowStatus
_SlotRowStatus_Object = MibTableColumn
slotRowStatus = _SlotRowStatus_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 10, 7, 2, 1, 7),
    _SlotRowStatus_Type()
)
slotRowStatus.setMaxAccess("read-only")
if mibBuilder.loadTexts:
    slotRowStatus.setStatus("current")
if mibBuilder.loadTexts:
    slotRowStatus.setDescription("Column Description")


class _SlotDesc_Type(DisplayString):
    """Custom type slotDesc based on DisplayString"""
    defaultValue = OctetString("")

    subtypeSpec = DisplayString.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        ValueSizeConstraint(0, 256),
    )


_SlotDesc_Type.__name__ = "DisplayString"
_SlotDesc_Object = MibTableColumn
slotDesc = _SlotDesc_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 10, 7, 2, 1, 8),
    _SlotDesc_Type()
)
slotDesc.setMaxAccess("read-write")
if mibBuilder.loadTexts:
    slotDesc.setStatus("current")
if mibBuilder.loadTexts:
    slotDesc.setDescription("Description.")
_PortTable_Object = MibTable
portTable = _PortTable_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 10, 7, 3)
)
if mibBuilder.loadTexts:
    portTable.setStatus("current")
if mibBuilder.loadTexts:
    portTable.setDescription("Port Table")
_PortEntry_Object = MibTableRow
portEntry = _PortEntry_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 10, 7, 3, 1)
)
portEntry.setIndexNames(
    (0, "ST-COMMON-MIB", "shelfId"),
    (0, "ST-COMMON-MIB", "slotNo"),
    (0, "ST-COMMON-MIB", "subSlotNo"),
    (0, "ST-COMMON-MIB", "portNo"),
    (0, "ST-COMMON-MIB", "subPortNo"),
)
if mibBuilder.loadTexts:
    portEntry.setStatus("current")
if mibBuilder.loadTexts:
    portEntry.setDescription(" ")


class _PortNo_Type(Unsigned32):
    """Custom type portNo based on Unsigned32"""
    subtypeSpec = Unsigned32.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        ValueRangeConstraint(1, 255),
    )


_PortNo_Type.__name__ = "Unsigned32"
_PortNo_Object = MibTableColumn
portNo = _PortNo_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 10, 7, 3, 1, 1),
    _PortNo_Type()
)
portNo.setMaxAccess("not-accessible")
if mibBuilder.loadTexts:
    portNo.setStatus("current")
if mibBuilder.loadTexts:
    portNo.setDescription("Identification of port within card supporting this object.The value is zero for shelf and card objects")


class _SubPortNo_Type(Unsigned32):
    """Custom type subPortNo based on Unsigned32"""
    subtypeSpec = Unsigned32.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        ValueRangeConstraint(0, 255),
    )


_SubPortNo_Type.__name__ = "Unsigned32"
_SubPortNo_Object = MibTableColumn
subPortNo = _SubPortNo_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 10, 7, 3, 1, 2),
    _SubPortNo_Type()
)
subPortNo.setMaxAccess("not-accessible")
if mibBuilder.loadTexts:
    subPortNo.setStatus("current")
if mibBuilder.loadTexts:
    subPortNo.setDescription("Identification of port within card supporting this object.The value is zero for shelf and card objects")


class _PortAdminState_Type(Integer32):
    """Custom type portAdminState based on Integer32"""
    defaultValue = 0

    subtypeSpec = Integer32.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        SingleValueConstraint(
            *(0,
              1)
        )
    )
    namedValues = NamedValues(
        *(("disabled", 0),
          ("enabled", 1))
    )


_PortAdminState_Type.__name__ = "Integer32"
_PortAdminState_Object = MibTableColumn
portAdminState = _PortAdminState_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 10, 7, 3, 1, 3),
    _PortAdminState_Type()
)
portAdminState.setMaxAccess("read-write")
if mibBuilder.loadTexts:
    portAdminState.setStatus("current")
if mibBuilder.loadTexts:
    portAdminState.setDescription("Specifies the admin state of the entity. As to different object, the consequent aciton may be different, e.g. it may shutdown the port, or insert maintenance signal.")


class _PortOperationalState_Type(Integer32):
    """Custom type portOperationalState based on Integer32"""
    subtypeSpec = Integer32.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        SingleValueConstraint(
            *(0,
              1)
        )
    )
    namedValues = NamedValues(
        *(("down", 0),
          ("up", 1))
    )


_PortOperationalState_Type.__name__ = "Integer32"
_PortOperationalState_Object = MibTableColumn
portOperationalState = _PortOperationalState_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 10, 7, 3, 1, 4),
    _PortOperationalState_Type()
)
portOperationalState.setMaxAccess("read-only")
if mibBuilder.loadTexts:
    portOperationalState.setStatus("current")
if mibBuilder.loadTexts:
    portOperationalState.setDescription("The operational state of the Port.")
_PortMode_Type = StPortMode
_PortMode_Object = MibTableColumn
portMode = _PortMode_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 10, 7, 3, 1, 5),
    _PortMode_Type()
)
portMode.setMaxAccess("read-write")
if mibBuilder.loadTexts:
    portMode.setStatus("current")
if mibBuilder.loadTexts:
    portMode.setDescription(" - ge (GE client signal, GFP-T) - gettt (GE client signal, GFP-T) - xgeOpu2e (10GBASE-R client signal, GFP-F G.Sup43 Section 7.1) - xgeGfpOpu2 (10GBASE-R client signal, GFP-F G.Sup43 Section 6.2(payload only)) - xgeGfpOpu2e (10GBASE-R client signal, GFP-F G.Sup43 Section 7.3) - ge100 (100GE client signal) - feCbr (FE client signal, GFP-T) - stm1 (STM1 client signal) - oc3 (OC3 client signal) - stm4 (STM4 client signal) - oc12 (OC12 client signal) - stm16 (STM16 client signal) - oc48 (OC48 client signal) - stm64 (STM64 client signal) - oc192 (OC192 client signal) - fc1 (1G Fibre Channel client signal) - fc2 (2G Fibre Channel client signal) - fc4 (4G Fibre Channel client signal) - fc8 (8G Fibre Channel client signal) - fc10 (10G Fibre Channel client signal) - otu1 (OTU1 client signal) - ochOSOtu1 (OTU1 line signal) - otu2 (OTU2 client signal) - ochOSOtu2 (OTU2 line signal) - otu2e (OTU2e client signal) - ochOSOtu2e (OTU2e line signal) - otu3 (OTU3 client signal) - ochOSOtu3 (OTU3 line signal) - otu4 (OTU4 client signal) - ochOSOtu4 (OTU4 line signal) - otu0ll (OTU0LL signal)")
_PortRowStatus_Type = RowStatus
_PortRowStatus_Object = MibTableColumn
portRowStatus = _PortRowStatus_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 10, 7, 3, 1, 6),
    _PortRowStatus_Type()
)
portRowStatus.setMaxAccess("read-only")
if mibBuilder.loadTexts:
    portRowStatus.setStatus("current")
if mibBuilder.loadTexts:
    portRowStatus.setDescription("sPortTable RowStatus")


class _PortAvailabilityState_Type(StAvailabilityState):
    """Custom type portAvailabilityState based on StAvailabilityState"""
    defaultValue = 0


_PortAvailabilityState_Type.__name__ = "StAvailabilityState"
_PortAvailabilityState_Object = MibTableColumn
portAvailabilityState = _PortAvailabilityState_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 10, 7, 3, 1, 7),
    _PortAvailabilityState_Type()
)
portAvailabilityState.setMaxAccess("read-only")
if mibBuilder.loadTexts:
    portAvailabilityState.setStatus("current")
if mibBuilder.loadTexts:
    portAvailabilityState.setDescription("Avalaibility Status is to qualify the operational, usage and/or administrative state attributes")


class _PortDesc_Type(DisplayString):
    """Custom type portDesc based on DisplayString"""
    defaultValue = OctetString("")

    subtypeSpec = DisplayString.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        ValueSizeConstraint(0, 256),
    )


_PortDesc_Type.__name__ = "DisplayString"
_PortDesc_Object = MibTableColumn
portDesc = _PortDesc_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 10, 7, 3, 1, 8),
    _PortDesc_Type()
)
portDesc.setMaxAccess("read-write")
if mibBuilder.loadTexts:
    portDesc.setStatus("current")
if mibBuilder.loadTexts:
    portDesc.setDescription("Description.")
_ConfigEventsMIB_ObjectIdentity = ObjectIdentity
configEventsMIB = _ConfigEventsMIB_ObjectIdentity(
    (1, 3, 6, 1, 4, 1, 52642, 1, 10, 8)
)
_SecurityMIB_ObjectIdentity = ObjectIdentity
securityMIB = _SecurityMIB_ObjectIdentity(
    (1, 3, 6, 1, 4, 1, 52642, 1, 10, 9)
)
_SecurityEventsMIB_ObjectIdentity = ObjectIdentity
securityEventsMIB = _SecurityEventsMIB_ObjectIdentity(
    (1, 3, 6, 1, 4, 1, 52642, 1, 10, 10)
)
_DcnMIB_ObjectIdentity = ObjectIdentity
dcnMIB = _DcnMIB_ObjectIdentity(
    (1, 3, 6, 1, 4, 1, 52642, 1, 10, 11)
)
_LicenseMIB_ObjectIdentity = ObjectIdentity
licenseMIB = _LicenseMIB_ObjectIdentity(
    (1, 3, 6, 1, 4, 1, 52642, 1, 10, 13)
)
_InventoryMIB_ObjectIdentity = ObjectIdentity
inventoryMIB = _InventoryMIB_ObjectIdentity(
    (1, 3, 6, 1, 4, 1, 52642, 1, 10, 15)
)
_CardInventoryTable_Object = MibTable
cardInventoryTable = _CardInventoryTable_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 10, 15, 1)
)
if mibBuilder.loadTexts:
    cardInventoryTable.setStatus("current")
if mibBuilder.loadTexts:
    cardInventoryTable.setDescription("Description.")
_CardInventoryEntry_Object = MibTableRow
cardInventoryEntry = _CardInventoryEntry_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 10, 15, 1, 1)
)
cardInventoryEntry.setIndexNames(
    (0, "ST-COMMON-MIB", "shelfId"),
    (0, "ST-COMMON-MIB", "slotNo"),
    (0, "ST-COMMON-MIB", "subSlotNo"),
)
if mibBuilder.loadTexts:
    cardInventoryEntry.setStatus("current")
if mibBuilder.loadTexts:
    cardInventoryEntry.setDescription("Description.")


class _CardType_Type(CardType):
    """Custom type cardType based on CardType"""
    defaultValue = 0


_CardType_Type.__name__ = "CardType"
_CardType_Object = MibTableColumn
cardType = _CardType_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 10, 15, 1, 1, 1),
    _CardType_Type()
)
cardType.setMaxAccess("read-only")
if mibBuilder.loadTexts:
    cardType.setStatus("current")
if mibBuilder.loadTexts:
    cardType.setDescription("Description.")


class _CardSN_Type(DisplayString):
    """Custom type cardSN based on DisplayString"""
    defaultValue = OctetString("")

    subtypeSpec = DisplayString.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        ValueSizeConstraint(0, 50),
    )


_CardSN_Type.__name__ = "DisplayString"
_CardSN_Object = MibTableColumn
cardSN = _CardSN_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 10, 15, 1, 1, 2),
    _CardSN_Type()
)
cardSN.setMaxAccess("read-only")
if mibBuilder.loadTexts:
    cardSN.setStatus("current")
if mibBuilder.loadTexts:
    cardSN.setDescription("serial number")


class _CardPartN_Type(DisplayString):
    """Custom type cardPartN based on DisplayString"""
    defaultValue = OctetString("")

    subtypeSpec = DisplayString.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        ValueSizeConstraint(0, 50),
    )


_CardPartN_Type.__name__ = "DisplayString"
_CardPartN_Object = MibTableColumn
cardPartN = _CardPartN_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 10, 15, 1, 1, 3),
    _CardPartN_Type()
)
cardPartN.setMaxAccess("read-only")
if mibBuilder.loadTexts:
    cardPartN.setStatus("current")
if mibBuilder.loadTexts:
    cardPartN.setDescription("partnumber")


class _CardHwVersion_Type(DisplayString):
    """Custom type cardHwVersion based on DisplayString"""
    defaultValue = OctetString("")

    subtypeSpec = DisplayString.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        ValueSizeConstraint(0, 20),
    )


_CardHwVersion_Type.__name__ = "DisplayString"
_CardHwVersion_Object = MibTableColumn
cardHwVersion = _CardHwVersion_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 10, 15, 1, 1, 4),
    _CardHwVersion_Type()
)
cardHwVersion.setMaxAccess("read-write")
if mibBuilder.loadTexts:
    cardHwVersion.setStatus("current")
if mibBuilder.loadTexts:
    cardHwVersion.setDescription("hardware version")


class _CardCLEI_Type(DisplayString):
    """Custom type cardCLEI based on DisplayString"""
    subtypeSpec = DisplayString.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        ValueSizeConstraint(0, 20),
    )


_CardCLEI_Type.__name__ = "DisplayString"
_CardCLEI_Object = MibTableColumn
cardCLEI = _CardCLEI_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 10, 15, 1, 1, 5),
    _CardCLEI_Type()
)
cardCLEI.setMaxAccess("read-only")
if mibBuilder.loadTexts:
    cardCLEI.setStatus("current")
if mibBuilder.loadTexts:
    cardCLEI.setDescription("Description.")


class _CardSwVersion_Type(DisplayString):
    """Custom type cardSwVersion based on DisplayString"""
    defaultValue = OctetString("")

    subtypeSpec = DisplayString.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        ValueSizeConstraint(0, 30),
    )


_CardSwVersion_Type.__name__ = "DisplayString"
_CardSwVersion_Object = MibTableColumn
cardSwVersion = _CardSwVersion_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 10, 15, 1, 1, 6),
    _CardSwVersion_Type()
)
cardSwVersion.setMaxAccess("read-only")
if mibBuilder.loadTexts:
    cardSwVersion.setStatus("current")
if mibBuilder.loadTexts:
    cardSwVersion.setDescription("SW Version")


class _CardFpgaId_Type(DisplayString):
    """Custom type cardFpgaId based on DisplayString"""
    defaultValue = OctetString("")

    subtypeSpec = DisplayString.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        ValueSizeConstraint(0, 20),
    )


_CardFpgaId_Type.__name__ = "DisplayString"
_CardFpgaId_Object = MibTableColumn
cardFpgaId = _CardFpgaId_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 10, 15, 1, 1, 7),
    _CardFpgaId_Type()
)
cardFpgaId.setMaxAccess("read-only")
if mibBuilder.loadTexts:
    cardFpgaId.setStatus("current")
if mibBuilder.loadTexts:
    cardFpgaId.setDescription("NMS/CLI will do translation between Id and displayed name")


class _CardFpgaVersion_Type(DisplayString):
    """Custom type cardFpgaVersion based on DisplayString"""
    defaultValue = OctetString("")

    subtypeSpec = DisplayString.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        ValueSizeConstraint(0, 20),
    )


_CardFpgaVersion_Type.__name__ = "DisplayString"
_CardFpgaVersion_Object = MibTableColumn
cardFpgaVersion = _CardFpgaVersion_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 10, 15, 1, 1, 8),
    _CardFpgaVersion_Type()
)
cardFpgaVersion.setMaxAccess("read-only")
if mibBuilder.loadTexts:
    cardFpgaVersion.setStatus("current")
if mibBuilder.loadTexts:
    cardFpgaVersion.setDescription("Description.")
_CardFpagVersionState_Type = Integer32
_CardFpagVersionState_Object = MibTableColumn
cardFpagVersionState = _CardFpagVersionState_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 10, 15, 1, 1, 9),
    _CardFpagVersionState_Type()
)
cardFpagVersionState.setMaxAccess("read-only")
if mibBuilder.loadTexts:
    cardFpagVersionState.setStatus("current")
if mibBuilder.loadTexts:
    cardFpagVersionState.setDescription("to indicate whether the current FPGA version is same as version in SW load")
_CardTemperature_Type = Integer32
_CardTemperature_Object = MibTableColumn
cardTemperature = _CardTemperature_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 10, 15, 1, 1, 10),
    _CardTemperature_Type()
)
cardTemperature.setMaxAccess("read-only")
if mibBuilder.loadTexts:
    cardTemperature.setStatus("current")
if mibBuilder.loadTexts:
    cardTemperature.setDescription("Temperature near CPU.")
_CardRowStatus_Type = RowStatus
_CardRowStatus_Object = MibTableColumn
cardRowStatus = _CardRowStatus_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 10, 15, 1, 1, 11),
    _CardRowStatus_Type()
)
cardRowStatus.setMaxAccess("read-only")
if mibBuilder.loadTexts:
    cardRowStatus.setStatus("current")
if mibBuilder.loadTexts:
    cardRowStatus.setDescription("Description.")
_CardTemperature2_Type = Integer32
_CardTemperature2_Object = MibTableColumn
cardTemperature2 = _CardTemperature2_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 10, 15, 1, 1, 12),
    _CardTemperature2_Type()
)
cardTemperature2.setMaxAccess("read-only")
if mibBuilder.loadTexts:
    cardTemperature2.setStatus("current")
if mibBuilder.loadTexts:
    cardTemperature2.setDescription("Temperature at air outlet.")


class _CardCardTypeSrc_Type(Integer32):
    """Custom type cardCardTypeSrc based on Integer32"""
    defaultValue = 0

    subtypeSpec = Integer32.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        SingleValueConstraint(
            *(0,
              1,
              2)
        )
    )
    namedValues = NamedValues(
        *(("unsupport", 0),
          ("hardware", 1),
          ("software", 2))
    )


_CardCardTypeSrc_Type.__name__ = "Integer32"
_CardCardTypeSrc_Object = MibTableColumn
cardCardTypeSrc = _CardCardTypeSrc_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 10, 15, 1, 1, 13),
    _CardCardTypeSrc_Type()
)
cardCardTypeSrc.setMaxAccess("read-write")
if mibBuilder.loadTexts:
    cardCardTypeSrc.setStatus("current")
if mibBuilder.loadTexts:
    cardCardTypeSrc.setDescription("Define the source where to get the cardType")


class _CardCardTypeSet_Type(CardType):
    """Custom type cardCardTypeSet based on CardType"""
    defaultValue = 0


_CardCardTypeSet_Type.__name__ = "CardType"
_CardCardTypeSet_Object = MibTableColumn
cardCardTypeSet = _CardCardTypeSet_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 10, 15, 1, 1, 14),
    _CardCardTypeSet_Type()
)
cardCardTypeSet.setMaxAccess("read-write")
if mibBuilder.loadTexts:
    cardCardTypeSet.setStatus("current")
if mibBuilder.loadTexts:
    cardCardTypeSet.setDescription("Description.")


class _CardFpgaLoadVersion_Type(DisplayString):
    """Custom type cardFpgaLoadVersion based on DisplayString"""
    defaultValue = OctetString("")

    subtypeSpec = DisplayString.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        ValueSizeConstraint(0, 20),
    )


_CardFpgaLoadVersion_Type.__name__ = "DisplayString"
_CardFpgaLoadVersion_Object = MibTableColumn
cardFpgaLoadVersion = _CardFpgaLoadVersion_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 10, 15, 1, 1, 15),
    _CardFpgaLoadVersion_Type()
)
cardFpgaLoadVersion.setMaxAccess("read-only")
if mibBuilder.loadTexts:
    cardFpgaLoadVersion.setStatus("current")
if mibBuilder.loadTexts:
    cardFpgaLoadVersion.setDescription("Description.")


class _CardSdPresent_Type(Integer32):
    """Custom type cardSdPresent based on Integer32"""
    defaultValue = 0

    subtypeSpec = Integer32.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        SingleValueConstraint(
            *(0,
              1)
        )
    )
    namedValues = NamedValues(
        *(("absent", 0),
          ("present", 1))
    )


_CardSdPresent_Type.__name__ = "Integer32"
_CardSdPresent_Object = MibTableColumn
cardSdPresent = _CardSdPresent_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 10, 15, 1, 1, 16),
    _CardSdPresent_Type()
)
cardSdPresent.setMaxAccess("read-only")
if mibBuilder.loadTexts:
    cardSdPresent.setStatus("current")
if mibBuilder.loadTexts:
    cardSdPresent.setDescription("Define the state for SD card")


class _CardCpldVersion_Type(DisplayString):
    """Custom type cardCpldVersion based on DisplayString"""
    defaultValue = OctetString("")

    subtypeSpec = DisplayString.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        ValueSizeConstraint(0, 20),
    )


_CardCpldVersion_Type.__name__ = "DisplayString"
_CardCpldVersion_Object = MibTableColumn
cardCpldVersion = _CardCpldVersion_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 10, 15, 1, 1, 17),
    _CardCpldVersion_Type()
)
cardCpldVersion.setMaxAccess("read-only")
if mibBuilder.loadTexts:
    cardCpldVersion.setStatus("current")
if mibBuilder.loadTexts:
    cardCpldVersion.setDescription("Description.")
_CardTemperature3_Type = Integer32
_CardTemperature3_Object = MibTableColumn
cardTemperature3 = _CardTemperature3_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 10, 15, 1, 1, 18),
    _CardTemperature3_Type()
)
cardTemperature3.setMaxAccess("read-only")
if mibBuilder.loadTexts:
    cardTemperature3.setStatus("current")
if mibBuilder.loadTexts:
    cardTemperature3.setDescription("Temperature at air intake.")


class _CardUbootVersion_Type(DisplayString):
    """Custom type cardUbootVersion based on DisplayString"""
    defaultValue = OctetString("")

    subtypeSpec = DisplayString.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        ValueSizeConstraint(0, 64),
    )


_CardUbootVersion_Type.__name__ = "DisplayString"
_CardUbootVersion_Object = MibTableColumn
cardUbootVersion = _CardUbootVersion_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 10, 15, 1, 1, 19),
    _CardUbootVersion_Type()
)
cardUbootVersion.setMaxAccess("read-only")
if mibBuilder.loadTexts:
    cardUbootVersion.setStatus("current")
if mibBuilder.loadTexts:
    cardUbootVersion.setDescription("Description.")


class _CardRootFsVersion_Type(DisplayString):
    """Custom type cardRootFsVersion based on DisplayString"""
    defaultValue = OctetString("")

    subtypeSpec = DisplayString.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        ValueSizeConstraint(0, 64),
    )


_CardRootFsVersion_Type.__name__ = "DisplayString"
_CardRootFsVersion_Object = MibTableColumn
cardRootFsVersion = _CardRootFsVersion_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 10, 15, 1, 1, 20),
    _CardRootFsVersion_Type()
)
cardRootFsVersion.setMaxAccess("read-only")
if mibBuilder.loadTexts:
    cardRootFsVersion.setStatus("current")
if mibBuilder.loadTexts:
    cardRootFsVersion.setDescription("Description.")


class _CardSwVersionInLoad_Type(DisplayString):
    """Custom type cardSwVersionInLoad based on DisplayString"""
    defaultValue = OctetString("")

    subtypeSpec = DisplayString.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        ValueSizeConstraint(0, 20),
    )


_CardSwVersionInLoad_Type.__name__ = "DisplayString"
_CardSwVersionInLoad_Object = MibTableColumn
cardSwVersionInLoad = _CardSwVersionInLoad_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 10, 15, 1, 1, 21),
    _CardSwVersionInLoad_Type()
)
cardSwVersionInLoad.setMaxAccess("read-only")
if mibBuilder.loadTexts:
    cardSwVersionInLoad.setStatus("current")
if mibBuilder.loadTexts:
    cardSwVersionInLoad.setDescription("Description.")


class _CardKernelVersion_Type(DisplayString):
    """Custom type cardKernelVersion based on DisplayString"""
    defaultValue = OctetString("")

    subtypeSpec = DisplayString.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        ValueSizeConstraint(0, 64),
    )


_CardKernelVersion_Type.__name__ = "DisplayString"
_CardKernelVersion_Object = MibTableColumn
cardKernelVersion = _CardKernelVersion_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 10, 15, 1, 1, 22),
    _CardKernelVersion_Type()
)
cardKernelVersion.setMaxAccess("read-only")
if mibBuilder.loadTexts:
    cardKernelVersion.setStatus("current")
if mibBuilder.loadTexts:
    cardKernelVersion.setDescription("Description.")


class _CardRtcPresent_Type(Integer32):
    """Custom type cardRtcPresent based on Integer32"""
    defaultValue = 0

    subtypeSpec = Integer32.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        SingleValueConstraint(
            *(0,
              1)
        )
    )
    namedValues = NamedValues(
        *(("absent", 0),
          ("present", 1))
    )


_CardRtcPresent_Type.__name__ = "Integer32"
_CardRtcPresent_Object = MibTableColumn
cardRtcPresent = _CardRtcPresent_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 10, 15, 1, 1, 23),
    _CardRtcPresent_Type()
)
cardRtcPresent.setMaxAccess("read-only")
if mibBuilder.loadTexts:
    cardRtcPresent.setStatus("current")
if mibBuilder.loadTexts:
    cardRtcPresent.setDescription("Define the state for RTC")
_PluggableTable_Object = MibTable
pluggableTable = _PluggableTable_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 10, 15, 2)
)
if mibBuilder.loadTexts:
    pluggableTable.setStatus("current")
if mibBuilder.loadTexts:
    pluggableTable.setDescription("Description.")
_PluggableEntry_Object = MibTableRow
pluggableEntry = _PluggableEntry_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 10, 15, 2, 1)
)
pluggableEntry.setIndexNames(
    (0, "ST-COMMON-MIB", "shelfId"),
    (0, "ST-COMMON-MIB", "slotNo"),
    (0, "ST-COMMON-MIB", "subSlotNo"),
    (0, "ST-COMMON-MIB", "portNo"),
    (0, "ST-COMMON-MIB", "subPortNo"),
)
if mibBuilder.loadTexts:
    pluggableEntry.setStatus("current")
if mibBuilder.loadTexts:
    pluggableEntry.setDescription("Description.")


class _PlType_Type(Integer32):
    """Custom type plType based on Integer32"""
    subtypeSpec = Integer32.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        SingleValueConstraint(
            *(0,
              1,
              2,
              3,
              4,
              5,
              6,
              7,
              8,
              9,
              10,
              11,
              13,
              14,
              16,
              17,
              18,
              20,
              25,
              192,
              193)
        )
    )
    namedValues = NamedValues(
        *(("unknown", 0),
          ("gbic", 1),
          ("module-connector", 2),
          ("sfp", 3),
          ("xsbi", 4),
          ("xenpak", 5),
          ("xfp", 6),
          ("xff", 7),
          ("xfpe", 8),
          ("xpak", 9),
          ("x2", 10),
          ("dwdmsfp", 11),
          ("qsfpplus", 13),
          ("cfp", 14),
          ("cfp2", 16),
          ("qsfp", 17),
          ("cfp4", 18),
          ("cfp2aco", 20),
          ("cfp2dco", 25),
          ("voaD", 192),
          ("voaB", 193))
    )


_PlType_Type.__name__ = "Integer32"
_PlType_Object = MibTableColumn
plType = _PlType_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 10, 15, 2, 1, 1),
    _PlType_Type()
)
plType.setMaxAccess("read-only")
if mibBuilder.loadTexts:
    plType.setStatus("current")
if mibBuilder.loadTexts:
    plType.setDescription("00h: Unknown or unspecified, 01h: GBIC, * 02h: Module/connector soldered to motherboard,* 03h: SFP,* 04h: 300 pin XSBI, * 05h: XENPAK,* 06h: XFP,* 07h: XFF,* 08h: XFP-E,* 09h: XPAK,* 0Ah: X2,* 0Bh: DWDM-SFP,* 0Ch: QSFP, * 0Dh: QSFP+,* 0Eh: CFP, 0Fh: Reserved, (changed from CXP) 10h: 168-pin 5��x7�� MSA-100GLH, 11h: CFP2, 12h: CFP4, 13h: 168-pin 4��x5�� MSA-100GLH, 14h: CFP2-ACO 19h: CFP2-DCO c0h: VOA dark type c1h: VOA bright type 15h ~ FFh : Reserved.")


class _PlPartNumber_Type(DisplayString):
    """Custom type plPartNumber based on DisplayString"""
    subtypeSpec = DisplayString.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        ValueSizeConstraint(20, 20),
    )
    fixedLength = 20


_PlPartNumber_Type.__name__ = "DisplayString"
_PlPartNumber_Object = MibTableColumn
plPartNumber = _PlPartNumber_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 10, 15, 2, 1, 2),
    _PlPartNumber_Type()
)
plPartNumber.setMaxAccess("read-only")
if mibBuilder.loadTexts:
    plPartNumber.setStatus("current")
if mibBuilder.loadTexts:
    plPartNumber.setDescription("Description.")


class _PlVendorSN_Type(DisplayString):
    """Custom type plVendorSN based on DisplayString"""
    subtypeSpec = DisplayString.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        ValueSizeConstraint(20, 20),
    )
    fixedLength = 20


_PlVendorSN_Type.__name__ = "DisplayString"
_PlVendorSN_Object = MibTableColumn
plVendorSN = _PlVendorSN_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 10, 15, 2, 1, 3),
    _PlVendorSN_Type()
)
plVendorSN.setMaxAccess("read-only")
if mibBuilder.loadTexts:
    plVendorSN.setStatus("current")
if mibBuilder.loadTexts:
    plVendorSN.setDescription("Vendor (manufacturer) serial number in any combination of letters and/or digits in ASCII code, left aligned and padded on the right with ASCII spaces (20h). All zero means unfined.")


class _PlVendorPN_Type(DisplayString):
    """Custom type plVendorPN based on DisplayString"""
    subtypeSpec = DisplayString.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        ValueSizeConstraint(20, 20),
    )
    fixedLength = 20


_PlVendorPN_Type.__name__ = "DisplayString"
_PlVendorPN_Object = MibTableColumn
plVendorPN = _PlVendorPN_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 10, 15, 2, 1, 4),
    _PlVendorPN_Type()
)
plVendorPN.setMaxAccess("read-only")
if mibBuilder.loadTexts:
    plVendorPN.setStatus("current")
if mibBuilder.loadTexts:
    plVendorPN.setDescription("Vendor (manufacturer) part number in any combination of letters and/or digits in ASCII code, left aligned and padded on the right with ASCII spaces (20h). All zero value means undefined. Vendor is the CFP module vendor.")


class _PlVendorName_Type(DisplayString):
    """Custom type plVendorName based on DisplayString"""
    subtypeSpec = DisplayString.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        ValueSizeConstraint(20, 20),
    )
    fixedLength = 20


_PlVendorName_Type.__name__ = "DisplayString"
_PlVendorName_Object = MibTableColumn
plVendorName = _PlVendorName_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 10, 15, 2, 1, 5),
    _PlVendorName_Type()
)
plVendorName.setMaxAccess("read-only")
if mibBuilder.loadTexts:
    plVendorName.setStatus("current")
if mibBuilder.loadTexts:
    plVendorName.setDescription("Vendor (manufacturer) name in any combination of letters and/or digits in ASCII code, left aligned and padded on the right with ASCII spaces (20h). The vendor name shall be the full name of the corporation, a commonly accepted abbreviation of the name or the stock exchange code for the corporation. ")


class _PlVendorOUI_Type(DisplayString):
    """Custom type plVendorOUI based on DisplayString"""
    subtypeSpec = DisplayString.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        ValueSizeConstraint(20, 20),
    )
    fixedLength = 20


_PlVendorOUI_Type.__name__ = "DisplayString"
_PlVendorOUI_Object = MibTableColumn
plVendorOUI = _PlVendorOUI_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 10, 15, 2, 1, 6),
    _PlVendorOUI_Type()
)
plVendorOUI.setMaxAccess("read-only")
if mibBuilder.loadTexts:
    plVendorOUI.setStatus("current")
if mibBuilder.loadTexts:
    plVendorOUI.setDescription("Description. The vendor organizationally unique identifier field (vendor OUI) is a 3-byte field that contains the IEEE Company Identifier for the vendor. The OUI format is defined by IEEE 802, section 9.1, which specifies ��a string of three octets, using the hexadecimal representation��, which lists the OUI bytes in forward order. data format: xx-xx-xx")
_PlApplicationCode_Type = Unsigned32
_PlApplicationCode_Object = MibTableColumn
plApplicationCode = _PlApplicationCode_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 10, 15, 2, 1, 7),
    _PlApplicationCode_Type()
)
plApplicationCode.setMaxAccess("read-only")
if mibBuilder.loadTexts:
    plApplicationCode.setStatus("current")
if mibBuilder.loadTexts:
    plApplicationCode.setDescription("(NE SW will report code, NMS/CLI will do translation) : - CFP: e.g. 4I1-9D1F, etc. - SFPP_SFP: e.g. I-64.1, etc.")


class _PlCLEI_Type(DisplayString):
    """Custom type plCLEI based on DisplayString"""
    subtypeSpec = DisplayString.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        ValueSizeConstraint(20, 20),
    )
    fixedLength = 20


_PlCLEI_Type.__name__ = "DisplayString"
_PlCLEI_Object = MibTableColumn
plCLEI = _PlCLEI_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 10, 15, 2, 1, 8),
    _PlCLEI_Type()
)
plCLEI.setMaxAccess("read-only")
if mibBuilder.loadTexts:
    plCLEI.setStatus("current")
if mibBuilder.loadTexts:
    plCLEI.setDescription("CLEI Code in any combination of letters and/or digits in ASCII code.(size 10)")


class _PlLaneNum_Type(Unsigned32):
    """Custom type plLaneNum based on Unsigned32"""
    defaultValue = 1

    subtypeSpec = Unsigned32.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        ValueRangeConstraint(1, 12),
    )


_PlLaneNum_Type.__name__ = "Unsigned32"
_PlLaneNum_Object = MibTableColumn
plLaneNum = _PlLaneNum_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 10, 15, 2, 1, 9),
    _PlLaneNum_Type()
)
plLaneNum.setMaxAccess("read-only")
if mibBuilder.loadTexts:
    plLaneNum.setStatus("current")
if mibBuilder.loadTexts:
    plLaneNum.setDescription("For 10G SFP/SFPP/XFP, it��s always 1, not displayed to operator For 100G CFP: LR4 is 4, SR10 is 10, coherent CFP is ?")
_Pl10GWdmType_Type = Integer32
_Pl10GWdmType_Object = MibTableColumn
pl10GWdmType = _Pl10GWdmType_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 10, 15, 2, 1, 10),
    _Pl10GWdmType_Type()
)
pl10GWdmType.setMaxAccess("read-only")
if mibBuilder.loadTexts:
    pl10GWdmType.setStatus("current")
if mibBuilder.loadTexts:
    pl10GWdmType.setDescription("Description.")
_Pl100GWdmType_Type = Integer32
_Pl100GWdmType_Object = MibTableColumn
pl100GWdmType = _Pl100GWdmType_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 10, 15, 2, 1, 11),
    _Pl100GWdmType_Type()
)
pl100GWdmType.setMaxAccess("read-only")
if mibBuilder.loadTexts:
    pl100GWdmType.setStatus("current")
if mibBuilder.loadTexts:
    pl100GWdmType.setDescription("A 3-bit field identifying any optical grid spacing used by CFP module000b: Non-WDM, 001b: CWDM, 010b: LANWDM, 011b: DWDM on 200G-grid, 100b: DWDM on 100G-grid, 101b: DWDM on 50G-grid, 110b: DWDM on 25G-grid, 111b: Other type WDM.. ")
_PlWaveLength_Type = Integer32
_PlWaveLength_Object = MibTableColumn
plWaveLength = _PlWaveLength_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 10, 15, 2, 1, 12),
    _PlWaveLength_Type()
)
plWaveLength.setMaxAccess("read-write")
if mibBuilder.loadTexts:
    plWaveLength.setStatus("current")
if mibBuilder.loadTexts:
    plWaveLength.setDescription("applied to DWDM and CWDM optics only, can be written when attribute ��Tunable�� is true")


class _PlTunable_Type(Integer32):
    """Custom type plTunable based on Integer32"""
    defaultValue = 0

    subtypeSpec = Integer32.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        SingleValueConstraint(
            *(0,
              1)
        )
    )
    namedValues = NamedValues(
        *(("false", 0),
          ("true", 1))
    )


_PlTunable_Type.__name__ = "Integer32"
_PlTunable_Object = MibTableColumn
plTunable = _PlTunable_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 10, 15, 2, 1, 13),
    _PlTunable_Type()
)
plTunable.setMaxAccess("read-only")
if mibBuilder.loadTexts:
    plTunable.setStatus("current")
if mibBuilder.loadTexts:
    plTunable.setDescription("- For 100G WDM optics: to check whether use ��Tunability�� or ��Wavelength control�� to identify - For 10G WDM optics: to check which attribute will be used")


class _PlFirmware_Type(DisplayString):
    """Custom type plFirmware based on DisplayString"""
    subtypeSpec = DisplayString.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        ValueSizeConstraint(20, 20),
    )
    fixedLength = 20


_PlFirmware_Type.__name__ = "DisplayString"
_PlFirmware_Object = MibTableColumn
plFirmware = _PlFirmware_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 10, 15, 2, 1, 14),
    _PlFirmware_Type()
)
plFirmware.setMaxAccess("read-only")
if mibBuilder.loadTexts:
    plFirmware.setStatus("current")
if mibBuilder.loadTexts:
    plFirmware.setDescription("A two-register number in the format of x.y with x at lower address and y at higher address. All zero value indicates undefined.")


class _PlPresenceState_Type(Integer32):
    """Custom type plPresenceState based on Integer32"""
    defaultValue = 1

    subtypeSpec = Integer32.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        SingleValueConstraint(
            *(1,
              2,
              3,
              4)
        )
    )
    namedValues = NamedValues(
        *(("unknown", 1),
          ("work", 2),
          ("alarm", 3),
          ("absent", 4))
    )


_PlPresenceState_Type.__name__ = "Integer32"
_PlPresenceState_Object = MibTableColumn
plPresenceState = _PlPresenceState_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 10, 15, 2, 1, 15),
    _PlPresenceState_Type()
)
plPresenceState.setMaxAccess("read-only")
if mibBuilder.loadTexts:
    plPresenceState.setStatus("current")
if mibBuilder.loadTexts:
    plPresenceState.setDescription("Description.")
_PlRowStatus_Type = RowStatus
_PlRowStatus_Object = MibTableColumn
plRowStatus = _PlRowStatus_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 10, 15, 2, 1, 16),
    _PlRowStatus_Type()
)
plRowStatus.setMaxAccess("read-only")
if mibBuilder.loadTexts:
    plRowStatus.setStatus("current")
if mibBuilder.loadTexts:
    plRowStatus.setDescription("Description.")


class _PlDateCode_Type(DisplayString):
    """Custom type plDateCode based on DisplayString"""
    subtypeSpec = DisplayString.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        ValueSizeConstraint(16, 16),
    )
    fixedLength = 16


_PlDateCode_Type.__name__ = "DisplayString"
_PlDateCode_Object = MibTableColumn
plDateCode = _PlDateCode_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 10, 15, 2, 1, 17),
    _PlDateCode_Type()
)
plDateCode.setMaxAccess("read-only")
if mibBuilder.loadTexts:
    plDateCode.setStatus("current")
if mibBuilder.loadTexts:
    plDateCode.setDescription("Vendor's manufacturing date code. ")
_PlLength_Type = Integer32
_PlLength_Object = MibTableColumn
plLength = _PlLength_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 10, 15, 2, 1, 18),
    _PlLength_Type()
)
plLength.setMaxAccess("read-only")
if mibBuilder.loadTexts:
    plLength.setStatus("current")
if mibBuilder.loadTexts:
    plLength.setDescription("Link length supported for single mode fiber,units of km. ")
_PlNominalMBps_Type = Integer32
_PlNominalMBps_Object = MibTableColumn
plNominalMBps = _PlNominalMBps_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 10, 15, 2, 1, 19),
    _PlNominalMBps_Type()
)
plNominalMBps.setMaxAccess("read-only")
if mibBuilder.loadTexts:
    plNominalMBps.setStatus("current")
if mibBuilder.loadTexts:
    plNominalMBps.setDescription("Nominal signalling rate,units of 100MBd. ")
_PluggabelOpticsParameterTable_Object = MibTable
pluggabelOpticsParameterTable = _PluggabelOpticsParameterTable_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 10, 15, 3)
)
if mibBuilder.loadTexts:
    pluggabelOpticsParameterTable.setStatus("current")
if mibBuilder.loadTexts:
    pluggabelOpticsParameterTable.setDescription("Description.")
_PluggabelOpticsParameterEntry_Object = MibTableRow
pluggabelOpticsParameterEntry = _PluggabelOpticsParameterEntry_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 10, 15, 3, 1)
)
pluggabelOpticsParameterEntry.setIndexNames(
    (0, "ST-COMMON-MIB", "shelfId"),
    (0, "ST-COMMON-MIB", "slotNo"),
    (0, "ST-COMMON-MIB", "subSlotNo"),
    (0, "ST-COMMON-MIB", "portNo"),
    (0, "ST-COMMON-MIB", "subPortNo"),
    (0, "ST-COMMON-MIB", "popLaneId"),
)
if mibBuilder.loadTexts:
    pluggabelOpticsParameterEntry.setStatus("current")
if mibBuilder.loadTexts:
    pluggabelOpticsParameterEntry.setDescription("Description.")


class _PopLaneId_Type(Unsigned32):
    """Custom type popLaneId based on Unsigned32"""
    subtypeSpec = Unsigned32.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        ValueRangeConstraint(1, 12),
    )


_PopLaneId_Type.__name__ = "Unsigned32"
_PopLaneId_Object = MibTableColumn
popLaneId = _PopLaneId_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 10, 15, 3, 1, 1),
    _PopLaneId_Type()
)
popLaneId.setMaxAccess("not-accessible")
if mibBuilder.loadTexts:
    popLaneId.setStatus("current")
if mibBuilder.loadTexts:
    popLaneId.setDescription("Description.")


class _PopLaneTxPower_Type(Integer32):
    """Custom type popLaneTxPower based on Integer32"""
    defaultValue = -10000


_PopLaneTxPower_Type.__name__ = "Integer32"
_PopLaneTxPower_Object = MibTableColumn
popLaneTxPower = _PopLaneTxPower_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 10, 15, 3, 1, 2),
    _PopLaneTxPower_Type()
)
popLaneTxPower.setMaxAccess("read-only")
if mibBuilder.loadTexts:
    popLaneTxPower.setStatus("current")
if mibBuilder.loadTexts:
    popLaneTxPower.setDescription("transmission power of lane n (n<=12, applicable for multiple lane, e.g. SR10 CFP)")


class _PopLaneRxPower_Type(Integer32):
    """Custom type popLaneRxPower based on Integer32"""
    defaultValue = -10000


_PopLaneRxPower_Type.__name__ = "Integer32"
_PopLaneRxPower_Object = MibTableColumn
popLaneRxPower = _PopLaneRxPower_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 10, 15, 3, 1, 3),
    _PopLaneRxPower_Type()
)
popLaneRxPower.setMaxAccess("read-only")
if mibBuilder.loadTexts:
    popLaneRxPower.setStatus("current")
if mibBuilder.loadTexts:
    popLaneRxPower.setDescription("receiving power of lane n (n<=12, applicable for multiple lane, e.g. SR10 CFP)")
_PmpLaneLaserTemperature_Type = Integer32
_PmpLaneLaserTemperature_Object = MibTableColumn
pmpLaneLaserTemperature = _PmpLaneLaserTemperature_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 10, 15, 3, 1, 4),
    _PmpLaneLaserTemperature_Type()
)
pmpLaneLaserTemperature.setMaxAccess("read-only")
if mibBuilder.loadTexts:
    pmpLaneLaserTemperature.setStatus("current")
if mibBuilder.loadTexts:
    pmpLaneLaserTemperature.setDescription("laser temperature (note: there is too high and too low alarm for each lane)")
_PopLaneLaserBias_Type = Integer32
_PopLaneLaserBias_Object = MibTableColumn
popLaneLaserBias = _PopLaneLaserBias_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 10, 15, 3, 1, 5),
    _PopLaneLaserBias_Type()
)
popLaneLaserBias.setMaxAccess("read-only")
if mibBuilder.loadTexts:
    popLaneLaserBias.setStatus("current")
if mibBuilder.loadTexts:
    popLaneLaserBias.setDescription("laser bias current (note: there is too high and too low alarm for each lane)")
_PopLaneLaserVcc_Type = Integer32
_PopLaneLaserVcc_Object = MibTableColumn
popLaneLaserVcc = _PopLaneLaserVcc_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 10, 15, 3, 1, 6),
    _PopLaneLaserVcc_Type()
)
popLaneLaserVcc.setMaxAccess("read-only")
if mibBuilder.loadTexts:
    popLaneLaserVcc.setStatus("current")
if mibBuilder.loadTexts:
    popLaneLaserVcc.setDescription("laser voltage (not defined in CFP?) (note: there is too high and too low alarm for each lane)")
_PopRowStatus_Type = RowStatus
_PopRowStatus_Object = MibTableColumn
popRowStatus = _PopRowStatus_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 10, 15, 3, 1, 7),
    _PopRowStatus_Type()
)
popRowStatus.setMaxAccess("read-only")
if mibBuilder.loadTexts:
    popRowStatus.setStatus("current")
if mibBuilder.loadTexts:
    popRowStatus.setDescription("Description.")
_CardLedTable_Object = MibTable
cardLedTable = _CardLedTable_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 10, 15, 4)
)
if mibBuilder.loadTexts:
    cardLedTable.setStatus("current")
if mibBuilder.loadTexts:
    cardLedTable.setDescription("Description.")
_CardLedEntry_Object = MibTableRow
cardLedEntry = _CardLedEntry_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 10, 15, 4, 1)
)
cardLedEntry.setIndexNames(
    (0, "ST-COMMON-MIB", "shelfId"),
    (0, "ST-COMMON-MIB", "slotNo"),
    (0, "ST-COMMON-MIB", "subSlotNo"),
)
if mibBuilder.loadTexts:
    cardLedEntry.setStatus("current")
if mibBuilder.loadTexts:
    cardLedEntry.setDescription("Description.")


class _CardLedStatus_Type(OctetString):
    """Custom type cardLedStatus based on OctetString"""
    subtypeSpec = OctetString.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        ValueSizeConstraint(32, 32),
    )
    fixedLength = 32


_CardLedStatus_Type.__name__ = "OctetString"
_CardLedStatus_Object = MibTableColumn
cardLedStatus = _CardLedStatus_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 10, 15, 4, 1, 1),
    _CardLedStatus_Type()
)
cardLedStatus.setMaxAccess("read-only")
if mibBuilder.loadTexts:
    cardLedStatus.setStatus("current")
if mibBuilder.loadTexts:
    cardLedStatus.setDescription("The definition of led status. One byte for each led, define the index as below: fault,run,critical,major,minor,active,reserve,reserve,port1~port24 For each led, define its status as below: 0: unknown, 1: off 2: green slow blink 3: green fast blink 4: green always 5: red slow blink 6: red fast blink 7: red always ")
_CardLedRowStatus_Type = RowStatus
_CardLedRowStatus_Object = MibTableColumn
cardLedRowStatus = _CardLedRowStatus_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 10, 15, 4, 1, 2),
    _CardLedRowStatus_Type()
)
cardLedRowStatus.setMaxAccess("read-only")
if mibBuilder.loadTexts:
    cardLedRowStatus.setStatus("current")
if mibBuilder.loadTexts:
    cardLedRowStatus.setDescription("Description.")

# Managed Objects groups


# Notification objects

currentAlarmTrap = NotificationType(
    (1, 3, 6, 1, 4, 1, 52642, 1, 10, 4, 2)
)
currentAlarmTrap.setObjects(
      *(("ST-COMMON-MIB", "alarmSourceH"),
        ("ST-COMMON-MIB", "alarmSourceL"),
        ("ST-COMMON-MIB", "alarmCode"),
        ("ST-COMMON-MIB", "alarmSourceType"),
        ("ST-COMMON-MIB", "alarmSystemType"),
        ("ST-COMMON-MIB", "alarmSeverity"),
        ("ST-COMMON-MIB", "alarmType"),
        ("ST-COMMON-MIB", "alarmProbableCause"),
        ("ST-COMMON-MIB", "alarmState"),
        ("ST-COMMON-MIB", "alarmDTS"),
        ("ST-COMMON-MIB", "alarmTimeStamp"),
        ("ST-COMMON-MIB", "alarmRowStatus"),
        ("ST-COMMON-MIB", "alarmShelfTag"))
)
if mibBuilder.loadTexts:
    currentAlarmTrap.setStatus(
        "current"
    )
if mibBuilder.loadTexts:
    currentAlarmTrap.setDescription("This trap is sent to all SNMP managers if an alarm is raised or cleared. trapCounterValue is the current value of the alarm trap counter. alarmSeverity indicates the assigned severity (i.e. anything but cleared) if an alarm is raised. It also indicates if an alarm is cleared (without indicating the assigned severity). alarmTimeStamp indicates the time when the alarm was raised resp. cleared.")

configurationChangedTrap = NotificationType(
    (1, 3, 6, 1, 4, 1, 52642, 1, 10, 8, 1)
)
configurationChangedTrap.setObjects(
      *(("ST-COMMON-MIB", "eventTimeStamp"),
        ("ST-COMMON-MIB", "typeOfChange"),
        ("ST-COMMON-MIB", "eventDetailIdentifier"),
        ("ST-COMMON-MIB", "eventDetailIntegerValue"),
        ("ST-COMMON-MIB", "eventDetailStringValue"),
        ("ST-COMMON-MIB", "lastChangeFlag"))
)
if mibBuilder.loadTexts:
    configurationChangedTrap.setStatus(
        "current"
    )
if mibBuilder.loadTexts:
    configurationChangedTrap.setDescription("Notification of configuration changes including - detection or removal of a gateway's target NE - changes of alarm masks and alarm severity assignments - configuration of performance monitoring points and performance thresholds. trapCounterValue is the current notification counter according to the event category. typeOfChange indicates whether new table entries have been added or existing entries have been modified or removed. eventTimeStamp indicates when the configuration change happened. eventDetailIdentifier may identify - the index of a table entry which has been added or removed - a particular columnar object within an added or removed table entry - the columnar object within a particular table entry which has been modified - the index of a table entry where multiple columnar objects have been modified - a scalar object which has been modified In case that this trap notifies the change of a single INTEGER (or some derived syntax) value eventDetailIntegerValue indicates this value. In case that this trap notifies the change of a single OCTET STRING (or some derived syntax) value eventDetailStringValue indicates this value. Otherwise eventDetailIntegerValue and eventDetailStringValue are reported with unspecific values and no further details are notified with this trap. The SNMP manager may request the corresponding common and object-specific table entries in order to find out details. If the configuration change was triggered by an SNMP SET request - eventUserName indicates the usmUserName of that request. - sessionID indicates the related session. - relatedRequestId indicates the request-id of that request. - lastChangeFlag indicates whether further traps will be sent.")


# Notifications groups


# Agent capabilities


# Module compliance


# Export all MIB objects to the MIB builder

mibBuilder.exportSymbols(
    "ST-COMMON-MIB",
    **{"EventCategory": EventCategory,
       "TypeOfChange": TypeOfChange,
       "AlarmCode": AlarmCode,
       "AlarmPathType": AlarmPathType,
       "AlarmState": AlarmState,
       "CardType": CardType,
       "StPortMode": StPortMode,
       "StAvailabilityState": StAvailabilityState,
       "AlarmSeverity": AlarmSeverity,
       "AlarmType": AlarmType,
       "AlarmSummary": AlarmSummary,
       "PresenceState": PresenceState,
       "EquipmentState": EquipmentState,
       "LinkDirection": LinkDirection,
       "CommissioningStatusShelf": CommissioningStatusShelf,
       "ShelfNumberWithinBay": ShelfNumberWithinBay,
       "EquipmentProvisioningMode": EquipmentProvisioningMode,
       "GenericOperation": GenericOperation,
       "FilterMaintenanceInterval": FilterMaintenanceInterval,
       "EnableSwitch": EnableSwitch,
       "OperationalState": OperationalState,
       "AdministrativeState": AdministrativeState,
       "CardMode": CardMode,
       "ResetOperation": ResetOperation,
       "CardUploadOperation": CardUploadOperation,
       "PMNumberOfRecords": PMNumberOfRecords,
       "PMParameterName": PMParameterName,
       "PMLogType": PMLogType,
       "FileTransferTypeOfProtocol": FileTransferTypeOfProtocol,
       "UsageState": UsageState,
       "ResetState": ResetState,
       "PMStorageControl": PMStorageControl,
       "HibernationMode": HibernationMode,
       "HibernationState": HibernationState,
       "stCommon": stCommon,
       "commonMIB": commonMIB,
       "system": system,
       "sysInfoScalars": sysInfoScalars,
       "sysHostName": sysHostName,
       "sysHostAliasName": sysHostAliasName,
       "sysDescription": sysDescription,
       "sysSwVersion": sysSwVersion,
       "sysHwVersion": sysHwVersion,
       "sysSerialNum": sysSerialNum,
       "sysMacPoolInitAddress": sysMacPoolInitAddress,
       "sysMacPoolSize": sysMacPoolSize,
       "sysTemperatureTable": sysTemperatureTable,
       "sysTemperatureEntry": sysTemperatureEntry,
       "sysTempMeterIndex": sysTempMeterIndex,
       "sysTempMeterDesc": sysTempMeterDesc,
       "sysTempMeterAlarmEnable": sysTempMeterAlarmEnable,
       "sysTempMeterThreshold": sysTempMeterThreshold,
       "sysTempTableRowStatus": sysTempTableRowStatus,
       "sysMangement": sysMangement,
       "sysMangementScalars": sysMangementScalars,
       "maxMgmtEtherPortNum": maxMgmtEtherPortNum,
       "maxMgmtVlanIfNum": maxMgmtVlanIfNum,
       "mgmtDefaultRouteGateway": mgmtDefaultRouteGateway,
       "sysCurrentTime": sysCurrentTime,
       "sysTimeZone": sysTimeZone,
       "nodeIP": nodeIP,
       "defaultRtRedist": defaultRtRedist,
       "mgmtEthIfTable": mgmtEthIfTable,
       "mgmtEthIfEntry": mgmtEthIfEntry,
       "mgmtEthIfId": mgmtEthIfId,
       "mgmtEthIfName": mgmtEthIfName,
       "mgmtEthIfIpMode": mgmtEthIfIpMode,
       "mgmtEthIfIpAddr": mgmtEthIfIpAddr,
       "mgmtEthIfIpAddrMask": mgmtEthIfIpAddrMask,
       "mgmtEthIfTableRowStatus": mgmtEthIfTableRowStatus,
       "mgmtEthOspfEnable": mgmtEthOspfEnable,
       "mgmtEthOspfAreaId": mgmtEthOspfAreaId,
       "mgmtVlanTable": mgmtVlanTable,
       "mgmtVlanEntry": mgmtVlanEntry,
       "mgmtVlanId": mgmtVlanId,
       "mgmtVlanIpAddr": mgmtVlanIpAddr,
       "mgmtVlanIpAddrMask": mgmtVlanIpAddrMask,
       "mgmtVlanTableRowStatus": mgmtVlanTableRowStatus,
       "sSNTP": sSNTP,
       "sntpScalars": sntpScalars,
       "sntpEnable": sntpEnable,
       "sntpSyncInterval": sntpSyncInterval,
       "sntpSyncStatus": sntpSyncStatus,
       "sntpLastSyncTime": sntpLastSyncTime,
       "sntpSyncedTime": sntpSyncedTime,
       "sntpServerCount": sntpServerCount,
       "sntpServerTable": sntpServerTable,
       "sntpServerEntry": sntpServerEntry,
       "sntpServerIp": sntpServerIp,
       "sntpServerStatus": sntpServerStatus,
       "sntpServerTableRowStatus": sntpServerTableRowStatus,
       "commonEventsMIB": commonEventsMIB,
       "trapCategory": trapCategory,
       "trapCounterValue": trapCounterValue,
       "typeOfChange": typeOfChange,
       "eventTimeStamp": eventTimeStamp,
       "eventDetailIdentifier": eventDetailIdentifier,
       "eventDetailIntegerValue": eventDetailIntegerValue,
       "eventDetailStringValue": eventDetailStringValue,
       "lastChangeFlag": lastChangeFlag,
       "alarmMIB": alarmMIB,
       "sCurrentAlarmTable": sCurrentAlarmTable,
       "sCurrentAlarmEntry": sCurrentAlarmEntry,
       "alarmSourceH": alarmSourceH,
       "alarmSourceL": alarmSourceL,
       "alarmCode": alarmCode,
       "alarmSourceType": alarmSourceType,
       "alarmSystemType": alarmSystemType,
       "alarmSeverity": alarmSeverity,
       "alarmType": alarmType,
       "alarmProbableCause": alarmProbableCause,
       "alarmState": alarmState,
       "alarmDTS": alarmDTS,
       "alarmTimeStamp": alarmTimeStamp,
       "alarmRowStatus": alarmRowStatus,
       "alarmEventsMIB": alarmEventsMIB,
       "currentAlarmTrap": currentAlarmTrap,
       "configMIB": configMIB,
       "shelfTable": shelfTable,
       "shelfEntry": shelfEntry,
       "shelfId": shelfId,
       "shelfType": shelfType,
       "shelfSN": shelfSN,
       "shelfPartNumber": shelfPartNumber,
       "shelfCLEI": shelfCLEI,
       "shelfHwVersion": shelfHwVersion,
       "shelfMacAddressMgmt1": shelfMacAddressMgmt1,
       "shelfMacAddressMgmt2": shelfMacAddressMgmt2,
       "shelfMacAddressMgmt3": shelfMacAddressMgmt3,
       "shelfAssignShelfId": shelfAssignShelfId,
       "shelfSwVersion": shelfSwVersion,
       "shelfLocation": shelfLocation,
       "shelfTemperature": shelfTemperature,
       "shelfMDI1Enable": shelfMDI1Enable,
       "shelfMDI2Enable": shelfMDI2Enable,
       "shelfMDO1Condition": shelfMDO1Condition,
       "shelfMDO2Condition": shelfMDO2Condition,
       "shelfLampTest": shelfLampTest,
       "shelfAlarmCutOperation": shelfAlarmCutOperation,
       "shelfRowStatus": shelfRowStatus,
       "shelfFanSpeedPwm": shelfFanSpeedPwm,
       "shelfFanTopSpeed": shelfFanTopSpeed,
       "shelfFanSpeedAutoRegulating": shelfFanSpeedAutoRegulating,
       "shelfOneIpRole": shelfOneIpRole,
       "slotTable": slotTable,
       "slotEntry": slotEntry,
       "slotNo": slotNo,
       "subSlotNo": subSlotNo,
       "slotRequiredCardType": slotRequiredCardType,
       "slotActualCardType": slotActualCardType,
       "slotAdminState": slotAdminState,
       "slotOperationState": slotOperationState,
       "slotRowStatus": slotRowStatus,
       "slotDesc": slotDesc,
       "portTable": portTable,
       "portEntry": portEntry,
       "portNo": portNo,
       "subPortNo": subPortNo,
       "portAdminState": portAdminState,
       "portOperationalState": portOperationalState,
       "portMode": portMode,
       "portRowStatus": portRowStatus,
       "portAvailabilityState": portAvailabilityState,
       "portDesc": portDesc,
       "configEventsMIB": configEventsMIB,
       "configurationChangedTrap": configurationChangedTrap,
       "securityMIB": securityMIB,
       "securityEventsMIB": securityEventsMIB,
       "dcnMIB": dcnMIB,
       "licenseMIB": licenseMIB,
       "inventoryMIB": inventoryMIB,
       "cardInventoryTable": cardInventoryTable,
       "cardInventoryEntry": cardInventoryEntry,
       "cardType": cardType,
       "cardSN": cardSN,
       "cardPartN": cardPartN,
       "cardHwVersion": cardHwVersion,
       "cardCLEI": cardCLEI,
       "cardSwVersion": cardSwVersion,
       "cardFpgaId": cardFpgaId,
       "cardFpgaVersion": cardFpgaVersion,
       "cardFpagVersionState": cardFpagVersionState,
       "cardTemperature": cardTemperature,
       "cardRowStatus": cardRowStatus,
       "cardTemperature2": cardTemperature2,
       "cardCardTypeSrc": cardCardTypeSrc,
       "cardCardTypeSet": cardCardTypeSet,
       "cardFpgaLoadVersion": cardFpgaLoadVersion,
       "cardSdPresent": cardSdPresent,
       "cardCpldVersion": cardCpldVersion,
       "cardTemperature3": cardTemperature3,
       "cardUbootVersion": cardUbootVersion,
       "cardRootFsVersion": cardRootFsVersion,
       "cardSwVersionInLoad": cardSwVersionInLoad,
       "cardKernelVersion": cardKernelVersion,
       "cardRtcPresent": cardRtcPresent,
       "pluggableTable": pluggableTable,
       "pluggableEntry": pluggableEntry,
       "plType": plType,
       "plPartNumber": plPartNumber,
       "plVendorSN": plVendorSN,
       "plVendorPN": plVendorPN,
       "plVendorName": plVendorName,
       "plVendorOUI": plVendorOUI,
       "plApplicationCode": plApplicationCode,
       "plCLEI": plCLEI,
       "plLaneNum": plLaneNum,
       "pl10GWdmType": pl10GWdmType,
       "pl100GWdmType": pl100GWdmType,
       "plWaveLength": plWaveLength,
       "plTunable": plTunable,
       "plFirmware": plFirmware,
       "plPresenceState": plPresenceState,
       "plRowStatus": plRowStatus,
       "plDateCode": plDateCode,
       "plLength": plLength,
       "plNominalMBps": plNominalMBps,
       "pluggabelOpticsParameterTable": pluggabelOpticsParameterTable,
       "pluggabelOpticsParameterEntry": pluggabelOpticsParameterEntry,
       "popLaneId": popLaneId,
       "popLaneTxPower": popLaneTxPower,
       "popLaneRxPower": popLaneRxPower,
       "pmpLaneLaserTemperature": pmpLaneLaserTemperature,
       "popLaneLaserBias": popLaneLaserBias,
       "popLaneLaserVcc": popLaneLaserVcc,
       "popRowStatus": popRowStatus,
       "cardLedTable": cardLedTable,
       "cardLedEntry": cardLedEntry,
       "cardLedStatus": cardLedStatus,
       "cardLedRowStatus": cardLedRowStatus}
)
