import copy
import json
import logging
import paramiko
from flask import Blueprint, jsonify, Response, request
from sqlalchemy import and_, or_

from server.db.models.otn import OtnTempData, OtnDeviceBasic, M6200DeviceCards
from server.util.permission import admin_permission
from server.db.models import inventory
from server.util import m6200_util
from server.util.snmp_util import SNMPClient, convert_asn1_value

inven_db = inventory.inven_db
m6200_module = Blueprint("m6200_module", __name__, template_folder="templates")
LOG = logging.getLogger(__name__)

@m6200_module.route("/info/query", methods=["GET"])
@admin_permission.require(http_exception=403)
def query_info():
    id = request.args.get('id')
    ip = request.args.get('ip')
    if id is None and ip is None:
        return Response(json.dumps({"data": "", "errorCode": 1, "errorMsg": "At least one of IP and ID is required!"}),
                        mimetype="application/json")
    # 下发指令查询设备数据
    data = m6200_util.sync_otn_device_info_single(id=id, ip=ip)
    errorCode = 0
    if data is None:
        errorCode = 1
    result = {"data": data, "errorCode": errorCode, "errorMsg": ""}
    return Response(json.dumps(result), mimetype="application/json")

@m6200_module.route('/info/get', methods=['GET'])
@admin_permission.require(http_exception=403)
def get_m6200_info():
    id = request.args.get('id')
    ip = request.args.get('ip')

    if id is None and ip is None:
        return Response(json.dumps({
            "data": "",
            "errorCode": 1,
            "errorMsg": "At least one of IP and ID is required!"
        }), mimetype="application/json")

    db_session = inven_db.get_session()
    m6200_temp_data = db_session.query(OtnTempData)

    if id is None:
        data = m6200_temp_data.filter(OtnTempData.ip == ip).first()
    elif ip is None:
        data = m6200_temp_data.filter(OtnTempData.id == id).first()
    else:
        data = m6200_temp_data.filter(OtnTempData.id == id, OtnTempData.ip == ip).first()

    if not data:
        return Response(json.dumps({"data": "", "errorCode": 1, "errorMsg": "Data is empty!"}),
                        mimetype="application/json")
    result = {"data": json.loads(data.data), "errorCode": 0, "errorMsg": ""}

    return Response(json.dumps(result), mimetype="application/json")


@m6200_module.route("/config/set_note", methods=["POST"])
@admin_permission.require(http_exception=403)
def modify_port_note():
    data = request.get_json()
    ip = data.get("ip")
    slotIndex = data.get("slotIndex")
    # cardId = data.get('cardId')
    port = data.get("port")
    note = data.get("note")
    if (ip is None) or (slotIndex is None) or (port is None) or (note is None):
        return Response(
            json.dumps({"data": "", "errorCode": 1, "errorMsg": "Ip, slotIndex, port and note is required!"}),
            mimetype="application/json")
    db_session = inven_db.get_session()
    card = db_session.query(M6200DeviceCards).join(OtnDeviceBasic,
                                                 OtnDeviceBasic.id == M6200DeviceCards.device_id).filter(
        and_(OtnDeviceBasic.ip == ip,M6200DeviceCards.slot_index == slotIndex)).first()
    if not card:
        return Response(json.dumps({"data": "", "errorCode": 1, "errorMsg": "Device is not exist!"}),
                        mimetype="application/json")

    if slotIndex == "0":
        innerKey = "slot_note"
    else:
        innerKey = "port_note"

    newPortData, error = m6200_util.set_note(ip, slotIndex, port, innerKey, note, card.ports_data)
    db_session.query(M6200DeviceCards).filter(M6200DeviceCards.card_id == card.card_id).update({
        M6200DeviceCards.ports_data: newPortData})

    result = {"data": newPortData, "errorCode": 0, "errorMsg": ""}
    return Response(json.dumps(result), mimetype="application/json")

@m6200_module.route("/get_m6200_device_card", methods=["POST"])
@admin_permission.require(http_exception=403)
def get_m6200_device_card():
    m6200_cards_ret = {"data": [], "errorMsg": ""}
    try:
        data = request.get_json()
        ip = data["ip"] if isinstance(data["ip"], str) else data["ip"].get("ip")
        filter_card = data["filterCard"]
        with inven_db.get_session() as db_session:
            device_obj = db_session.query(OtnDeviceBasic).filter(OtnDeviceBasic.ip == ip).first()
            if not device_obj:
                raise ValueError("Device is not exist!")

            m6200_device_cards_objs = db_session.query(M6200DeviceCards).filter(
                M6200DeviceCards.device_id == device_obj.id,
                M6200DeviceCards.slot_index != 0,
                M6200DeviceCards.ports_data != "{}")

            # m6200_device_cards_objs = m6200_device_cards_objs.filter(
            #     M6200DeviceCards.slot_index.in_([1, 2, 3, 4])
            # )

        ret = [
            {obj.card_id: f"{obj.type}-1-{obj.slot_index}"}
            for obj in m6200_device_cards_objs
            if m6200_device_cards_objs
        ]
    except Exception as e:
        m6200_cards_ret["errorCode"] = 500
        m6200_cards_ret["errorMsg"] = f"Error: {str(e)}"
    else:
        m6200_cards_ret["errorCode"] = 200
        m6200_cards_ret["data"] = ret
    finally:
        return jsonify(m6200_cards_ret)


@m6200_module.route("/get_m6200_device_port", methods=["POST"])
@admin_permission.require(http_exception=403)
def get_m6200_device_port():
    ports_ret = {"data": {}, "errorMsg": ""}
    try:
        data = request.get_json()
        card_id = data["id"]
        tabType = data["type"]

        with inven_db.get_session() as db_session:
            m6200_device_cards_obj = db_session.query(M6200DeviceCards).filter(M6200DeviceCards.card_id == card_id).first()

        if not m6200_device_cards_obj:
            raise ValueError("Card is not exist!")

        try:
            ports_info = json.loads(m6200_device_cards_obj.ports_data)
        except json.JSONDecodeError:
            raise ValueError("Failed to parse ports data")

        ports_name = [v["name"] for k, v in ports_info.items() if v.get("name") is not None]
        ports_ret["data"]["port_name"] = ports_name
        ports_ret["data"]["info"] = m6200_device_cards_obj.make_dict()
        ports_ret["data"]["ports_info"] = {"ports_data": m6200_device_cards_obj.ports_data}

    except Exception as e:
        ports_ret["errorCode"] = 500
        ports_ret["errorMsg"] = f"Error: {str(e)}"
    else:
        ports_ret["errorCode"] = 200
    finally:
        return jsonify(ports_ret)

@m6200_module.route("/config/query", methods=["GET"])
@admin_permission.require(http_exception=403)
def get_dcs_config():
    ip = request.args.get('ip')
    slotIndex = request.args.get('slotIndex')
    cardId = request.args.get('cardId')
    if ip is None or (slotIndex is None and cardId is None):
        return Response(json.dumps({"data": "", "errorCode": 1,
                                    "errorMsg": "Ip and at least one of slotIndex and cardId is required!"}),
                        mimetype="application/json")
    if slotIndex is None:
        slotIndex = -1
    else:
        slotIndex = int(slotIndex)
    if cardId is None:
        cardId = ""
    db_session = inven_db.get_session()
    card = db_session.query(M6200DeviceCards).join(OtnDeviceBasic,
                                                 OtnDeviceBasic.id == M6200DeviceCards.device_id).filter(
        and_(OtnDeviceBasic.ip == ip,
             or_(M6200DeviceCards.slot_index == slotIndex, M6200DeviceCards.card_id == cardId))).first()
    if not card:
        return Response(json.dumps({"data": "", "errorCode": 1, "errorMsg": "Device is not exist!"}),
                        mimetype="application/json")
    slotIndex = card.slot_index
    data, errorCode, errorMsg = m6200_util.get_config(ip, slotIndex)
    result = {"data": data, "errorCode": errorCode, "errorMsg": errorMsg}
    return Response(json.dumps(result), mimetype="application/json")

@m6200_module.route("/get_m6200_time_management", methods=["GET"])
@admin_permission.require(http_exception=403)
def get_m6200_time_management():
    try:
        with inven_db.get_session() as db_session:
            m6200_devices = db_session.query(OtnDeviceBasic).filter_by(model="M6200").all()

        if not m6200_devices:
            LOG.warning("No M6200 devices found in the database.")
            return jsonify({"status": "success", "data": {}}), 200

        all_devices_data = {}

        for device in m6200_devices:
            device_ip = device.ip
            device_name = device.name  # 从数据库中获取设备名称
            LOG.info(f"Fetching SNTP data for device IP: {device_ip}")

            # Create SNMP client instance
            snmp_client = SNMPClient(
                host=device_ip,
                community_read="private",
                community_write="private",
                timeout=10,
                retries=5
            )

            # Perform SNMP walk on sntpScalars
            sntp_walk_results = snmp_client.process('walk', "ST-COMMON-MIB::sntpScalars", ())

            # Process walk results
            device_sntp_data = {}
            if sntp_walk_results:
                for oid_obj, value_obj in sntp_walk_results:
                    try:
                        # Get MIB symbol info
                        mib_module, mib_object, indices = oid_obj.get_mib_symbol()
                        LOG.debug(f"Processing OID: {oid_obj}, MIB Object: {mib_object}, Indices: {indices}")
                        value = convert_asn1_value(value_obj)
                        device_sntp_data[mib_object] = value
                    except Exception as e:
                        LOG.warning(f"Error processing sntpScalars walk result {oid_obj} for IP {device_ip}: {e}")

                LOG.info(f"SNTP data fetched for {device_ip}: {device_sntp_data}")
            else:
                LOG.error(f"SNMP WALK failed for sntpScalars on IP {device_ip}.")
                device_sntp_data = None

            # 在设备数据中加入名称和型号
            all_devices_data[device_ip] = {
                "name": device_name,
                "model": "M6200",
                "sntp_data": device_sntp_data
            }

        return jsonify({
            "status": "success",
            "data": all_devices_data
        }), 200

    except Exception as e:
        LOG.error(f"Error in get_m6200_time_management: {e}")
        return jsonify({
            "status": "error",
            "message": str(e)
        }), 500