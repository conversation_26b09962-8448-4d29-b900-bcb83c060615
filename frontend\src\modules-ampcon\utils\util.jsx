import {layoutColumnProps, buttonProps} from "@/modules-ampcon/utils/modal";
import {useSelector} from "react-redux";
import ForbiddenPage from "@/modules-ampcon/pages/ForbiddenPage";
import {useEffect, useState} from "react";
import {useLocation} from "react-router-dom";

export {layoutColumnProps, buttonProps};

//  --------------------------------------------------------------------------------------------
//   Form Check Start
//  --------------------------------------------------------------------------------------------

// eslint-disable-next-line unicorn/better-regex
const IPv4_REGEXP =
    /^(25[0-5]|2[0-4]\d|[01]?\d{1,2})\.(25[0-5]|2[0-4]\d|[01]?\d{1,2})\.(25[0-5]|2[0-4]\d|[01]?\d{1,2})\.(25[0-5]|2[0-4]\d|[01]?\d{1,2})$/;
// eslint-disable-next-line unicorn/better-regex
const IPv6_REGEXP =
    /^(([\dA-Fa-f]{1,4}:){7}([\dA-Fa-f]{1,4}|:)|([\dA-Fa-f]{1,4}:){6}(:[\dA-Fa-f]{1,4}|((25[0-5]|2[0-4]\d|[01]?\d{1,2})\.){3}(25[0-5]|2[0-4]\d|[01]?\d{1,2})|:)|([\dA-Fa-f]{1,4}:){5}((:[\dA-Fa-f]{1,4}){1,2}|:((25[0-5]|2[0-4]\d|[01]?\d{1,2})\.){3}(25[0-5]|2[0-4]\d|[01]?\d{1,2})|:)|([\dA-Fa-f]{1,4}:){4}((:[\dA-Fa-f]{1,4}){1,3}|(:[\dA-Fa-f]{1,4})?:((25[0-5]|2[0-4]\d|[01]?\d{1,2})\.){3}(25[0-5]|2[0-4]\d|[01]?\d{1,2})|:)|([\dA-Fa-f]{1,4}:){3}((:[\dA-Fa-f]{1,4}){1,4}|(:[\dA-Fa-f]{1,4}){0,2}:((25[0-5]|2[0-4]\d|[01]?\d{1,2})\.){3}(25[0-5]|2[0-4]\d|[01]?\d{1,2})|:)|([\dA-Fa-f]{1,4}:){2}((:[\dA-Fa-f]{1,4}){1,5}|(:[\dA-Fa-f]{1,4}){0,3}:((25[0-5]|2[0-4]\d|[01]?\d{1,2})\.){3}(25[0-5]|2[0-4]\d|[01]?\d{1,2})|:)|([\dA-Fa-f]{1,4}:)((:[\dA-Fa-f]{1,4}){1,6}|(:[\dA-Fa-f]{1,4}){0,4}:((25[0-5]|2[0-4]\d|[01]?\d{1,2})\.){3}(25[0-5]|2[0-4]\d|[01]?\d{1,2})|:)|:(((:[\dA-Fa-f]{1,4}){1,7}|(:[\dA-Fa-f]{1,4}){0,5}:((25[0-5]|2[0-4]\d|[01]?\d{1,2})\.){3}(25[0-5]|2[0-4]\d|[01]?\d{1,2})|:)))$/;

const MAC_REGEXP = /^([\dA-Fa-f]{2}:){5}([\dA-Fa-f]{2})$/;
const NUM_REGEXP = /^-?\d+$/;
const INT_REGEXP = /^\d+$/;

export const formValidateRules = {
    required: {},
    int: (msg = "input should be a number") => {
        return {pattern: INT_REGEXP, message: msg};
    },
    macaddr: (msg = "This value is not in MAC address format. i.e: 22:11:11:11:11:11") => {
        return {pattern: MAC_REGEXP, message: msg};
    },
    ipv4: (msg = "input should be a IPv4 address") => {
        return {pattern: IPv4_REGEXP, message: msg};
    },
    ipv4net: (msg = "input should be a IPv4 NET address") => {
        return {validator: checkValidator(msg, _checkIPv4, ["WITH"])};
    },
    ipv4range: (msg = "input should be a IPv4 range") => {
        return {validator: checkValidator(msg, _checkIPRange, ["IPv4"])};
    },
    ipv6: (msg = "input should be a IPv6 address") => {
        return {pattern: IPv6_REGEXP, message: msg};
    },
    ipv6net: (msg = "input should be a IPv6 NET address") => {
        return {validator: checkValidator(msg, _checkIPv6, ["WITH"])};
    },
    ipv6range: (msg = "input should be a IPv6 range") => {
        return {validator: checkValidator(msg, _checkIPRange, ["IPv6"])};
    },
    ipv4ipv6: (msg = "input should be a IPv4 / IPv6 address") => {
        return {validator: checkValidator(msg, _checkIPv4v6)};
    },
    uintrange: (msg = "input should be a number range format") => {
        return {validator: checkValidator(msg, _checkUintRange)};
    }
};

export const ALARM_COLOR = {
    error: {
        color: "#F53F3F",
        backgroundColor: "rgba(245,63,63,0.1)",
        borderColor: "#F53F3F"
    },
    warn: {
        color: "#FFBB00",
        backgroundColor: "rgba(255,187,0,0.1)",
        borderColor: "#FFBB00"
    },
    info: {
        color: "#00B7FF",
        backgroundColor: "rgba(0,183,255,0.1)",
        borderColor: "#00B7FF"
    }
};

const checkValidator = (msg, checkMethod, checkMethodParams = []) => {
    return (_, value) => {
        if (!value || checkMethod(value, ...checkMethodParams)) {
            return Promise.resolve();
        }
        return Promise.reject(new Error(msg));
    };
};

const _numberRange = (value, min, max) => {
    // tslint:disable-next-line: radix
    const numValue = parseInt(value);
    if (isNaN(numValue)) {
        return false;
    }
    return numValue >= min && numValue <= max;
};

const _checkIPv4 = (value, enableMask) => {
    // enableMask ['WITH', 'WITHOUT', 'BOTH_OK']
    let ipValue;
    let maskValue;
    if (enableMask === "WITH") {
        [ipValue, maskValue] = value.split("/");
        return IPv4_REGEXP.test(ipValue) && _numberRange(maskValue, 0, 32);
    }
    if (enableMask === "WITHOUT") {
        ipValue = value;
        return IPv4_REGEXP.test(ipValue);
    }
    if (enableMask === "BOTH_OK") {
        [ipValue, maskValue] = value.split("/");
        return typeof maskValue === "undefined"
            ? IPv4_REGEXP.test(ipValue)
            : IPv4_REGEXP.test(ipValue) && _numberRange(maskValue, 0, 32);
    }
    return false;
};

const _checkIPv6 = (value, enableMask) => {
    // enableMask ['WITH', 'WITHOUT', 'BOTH_OK']
    let ipValue;
    let maskValue;
    if (enableMask === "WITH") {
        [ipValue, maskValue] = value.split("/");
        return IPv6_REGEXP.test(ipValue) && _numberRange(maskValue, 0, 128);
    }
    if (enableMask === "WITHOUT") {
        ipValue = value;
        return IPv6_REGEXP.test(ipValue);
    }
    if (enableMask === "BOTH_OK") {
        [ipValue, maskValue] = value.split("/");
        return typeof maskValue === "undefined"
            ? IPv6_REGEXP.test(ipValue)
            : IPv6_REGEXP.test(ipValue) && _numberRange(maskValue, 0, 128);
    }
    return false;
};

const _checkIPv4v6 = value => {
    return _checkIPv4(value, "WITHOUT") || _checkIPv6(value, "WITHOUT");
};

const _checkIPRange = (value, checkType) => {
    // type ['IPv4', 'IPv6']
    const [ipValueFirst, ipValueSec] = value.split("..");
    const ipCheckMethod = checkType === "IPv6" ? _checkIPv6 : _checkIPv4;

    if (ipValueSec) {
        // WITH RANGE ".."
        return ipCheckMethod(value, "WITHOUT");
    }
    return ipCheckMethod(ipValueFirst) && _checkIPv4(ipValueSec);
};

const _checkUintRange = value => {
    // type ['IPv4', 'IPv6']

    const [valueFirst, valueSec] = value.split("..");
    return NUM_REGEXP.test(valueFirst) && NUM_REGEXP.test(valueSec) && parseInt(valueFirst) <= parseInt(valueSec);
};

// const _checkMAC = (value, enableMask) => {
//     // enableMask ['WITH', 'WITHOUT', 'BOTH_OK']
//     let macValue;
//     let maskValue;
//     if (enableMask === "WITH") {
//         [macValue, maskValue] = value.split("/");
//         return MAC_REGEXP.test(macValue) && MAC_REGEXP.test(maskValue);
//     }
//     if (enableMask === "WITHOUT") {
//         macValue = value;
//         return MAC_REGEXP.test(macValue);
//     }
//     if (enableMask === "BOTH_OK") {
//         [macValue, maskValue] = value.split("/");
//         return typeof maskValue === "undefined"
//             ? MAC_REGEXP.test(macValue)
//             : MAC_REGEXP.test(macValue) && MAC_REGEXP.test(maskValue);
//     }
//     return false;
// };

//  --------------------------------------------------------------------------------------------
//   Form Check END
//  --------------------------------------------------------------------------------------------

export const URLRegExp = new RegExp(
    "^(https?:\\/\\/)?" + // protocol
        "((([a-z\\d]([a-z\\d-]*[a-z\\d])*)\\.)+[a-z]{2,}|" + // domain name and extension
        "((\\d{1,3}\\.){3}\\d{1,3}))" + // OR ip (v4) address
        "(\\:\\d+)?" + // port
        "(\\/[-a-z\\d%@_.~+&:]*)*" + // path
        "(\\?[;&a-z\\d%@_.,~+&:=-]*)?" + // query string
        "(\\#[-a-z\\d_]*)?$",
    "i"
);

export const convertConfigToStr = config_str => {
    const resultOutput = [];
    const config_list = config_str.split("\n");
    const treePath = [];

    config_list.forEach(rowString => {
        const trimmedRow = rowString.trim();

        if (trimmedRow.includes("/*") || !trimmedRow) {
            return;
        }
        if (trimmedRow.includes("{")) {
            const node_str = trimmedRow.split("{")[0].trim();
            treePath.push(node_str);
        } else if (trimmedRow.includes("}")) {
            if (config_list[config_list.indexOf(rowString) - 1].includes("{")) {
                resultOutput.push(`set ${treePath.join(" ")}`);
            }
            treePath.pop();
        } else if (trimmedRow.includes(": ")) {
            const [key, value] = trimmedRow.split(":").map(str => str.trim());
            resultOutput.push(`set ${treePath.join(" ")} ${key} ${value}`);
        } else {
            resultOutput.push(`set ${treePath.join(" ")} ${trimmedRow}`);
        }
    });

    return resultOutput.join("\n");
};

const image_name_regex_mapping = {
    white_box_stable_release:
        /^onie-installer-PicOS-(?<version>[\d.A-Za-z]+)-(?<revision>[\dA-Za-z]+)-(?<platform>x86h|as4610|x86)\.bin$/,
    black_box_stable_release: /^(?<platform>[\dA-Za-z]+)-PicOS-(?<version>[\d.A-Za-z]+)-(?<revision>[\dA-Za-z]+).bin$/,
    white_box_stable_x86h_release:
        /^PicOS-(?<version>[\d.A-Za-z]+)-(?<revision>[\dA-Za-z]+)-fs-(?<platform>x86h)\.bin$/,
    black_box_x86_stable_release: /^PicOS-(?<version>[\d.A-Za-z]+)-(?<revision>[\dA-Za-z]+)-fs-(?<platform>x86)\.bin$/,
    old_white_box:
        /^onie-installer-picos-(?<version>[\d.]+)-(?<revision>[\dA-Za-z]+)-(?<platform>x86|as4610|n3100|n3000)\.bin$/,
    new_white_box_stable_release: /^onie-installer-picos-(?<version>[\d.]+)-(?<platform>[\dA-Za-z]+)\.bin$/,
    new_white_box_transition_release: /^onie-installer-PICOS-(?<version>[\d.]+)-fs-(?<platform>[\dA-Za-z]+)\.bin$/,
    new_white_box_transition_research:
        /^onie-installer-PICOS-(?<version>[\d.]+)-(?<revision>[\dA-Za-z]+)-fs-(?<platform>[\dA-Za-z]+)\.bin$/,
    new_black_box_data_center_research:
        /^picos-(?<version>[\d.]+)-(?<revision>[\dA-Za-z]+)-fs-(?<platform>[\dA-Za-z]+)\.bin$/,
    new_black_box_campus_research:
        /^(?<platform>[\dA-Za-z]+)_picos-(?<version>[\d.]+)-(?<revision>[\dA-Za-z]+)-fs.bin$/,
    new_black_box_data_center_release: /^picos-(?<version>[\d.]+)-fs-(?<platform>[\dA-Za-z]+)\.bin$/,
    new_black_box_campus_release: /^(?<platform>[\dA-Za-z]+)_picos-(?<version>[\d.]+)-fs.bin$/,
    new_s3410_busy_box_release: /^(?<platform>[\dA-Za-z]+)_picos-(?<version>[\d.]+)-(?<revision>[\dA-Za-z]+).bin$/
};

const getImageType = imageName => {
    for (const [imageType, regex] of Object.entries(image_name_regex_mapping)) {
        const match = imageName.match(regex);
        if (match) {
            return imageType;
        }
    }
    return null;
};

export const getImageInfo = imageName => {
    const imageType = getImageType(imageName);
    if (!imageType) {
        return null;
    }
    const match = imageName.match(image_name_regex_mapping[imageType]);
    if (match) {
        const temp = match.groups;
        if (temp.platform === "x86h") {
            temp.platform = "x86";
        }
        return {
            version: temp.version,
            revision: temp.revision || "",
            platform:
                imageType === "new_black_box_campus_research" ||
                imageType === "new_black_box_campus_release" ||
                imageType === "new_s3410_busy_box_release" ||
                imageType === "black_box_stable_release"
                    ? temp.platform.toLowerCase()
                    : temp.platform
        };
    }
    return null;
};

export const normalizeNumber = (input, digit = 2) => {
    if (typeof input === "string" && !isNaN(input)) {
        return isNaN(parseFloat(input)) ? "" : parseFloat(input).toFixed(digit);
    }
    if (typeof input === "number") {
        return input.toFixed(digit);
    }
    return input;
};

const adminURLExcludeKeys = [
    "/resource/auth_management/group_management",
    "/service/system_config",
    "/system/user_management",
    "/service/switch_model"
];

const operatorURLExcludeKeys = [
    "/resource/auth_management/group_management",
    "/service/system_config",
    "/system",
    "/service/switch_model"
];

const readonlyURLExcludeKeys = [
    "/maintain/automation/playbooks",
    "/maintain/automation/other_devices",
    "/maintain/automation/ansible_jobs_list/job_view",
    "/maintain/automation/ansible_jobs_list/switch_view",
    "/maintain/automation/schedule",
    "/maintain/system_backup",
    "/system/user_management",
    "/resource/auth_management/device_license_management/license_audit",
    "/resource/auth_management/device_license_management/license_action",
    "/resource/auth_management/device_license_management/license_local",
    "/resource/auth_management/group_management",
    "/resource/auth_management/fabric_management",
    "/resource/auth_management/site_management",
    "/service/global_configuration",
    "/service/switch_configuration",
    "/service/switch_model",
    "/service/system_config",
    "/resource/upgrade_management/switch",
    "/service/config_template/new_template",
    "/service/config_template/template_list",
    "/service/config_template/push_config",
    "/service/config_template/config_backup"
];

const roleMapping = {
    readonly: "readonly",
    admin: "operator",
    superadmin: "superadmin",
    superuser: "superuser"
};

export const isRouteForbidden = (currentPath, userType) => {
    let forbiddenURL = [];
    if (roleMapping[userType] === "superadmin") {
        forbiddenURL = adminURLExcludeKeys;
    } else if (roleMapping[userType] === "operator") {
        forbiddenURL = operatorURLExcludeKeys;
    } else if (roleMapping[userType] === "readonly") {
        forbiddenURL = readonlyURLExcludeKeys;
    }
    return forbiddenURL.includes(currentPath);
};

export const removeCssStyleByCssSelector = cssSelector => {
    const stylesheet = document.styleSheets[0];
    for (let i = 0; i < stylesheet.cssRules.length; i++) {
        if (stylesheet.cssRules[i].selectorText === cssSelector) {
            stylesheet.deleteRule(i);
        }
    }
};

const ProtectedRoute = ({component: Component, ...rest}) => {
    const currentUser = useSelector(state => state.user.userInfo);
    const currentPath = useLocation().pathname;
    const [isDataLoaded, setIsDataLoaded] = useState(false);
    const [isForbidden, setIsForbidden] = useState(false);

    useEffect(() => {
        if (Object.keys(currentUser).length !== 0) {
            setIsDataLoaded(true);
        }
        const userType = currentUser?.type;
        if (!currentPath) {
            setIsForbidden(true);
            return;
        }
        let forbiddenURL = [];
        if (roleMapping[userType] === "superadmin") {
            forbiddenURL = adminURLExcludeKeys;
        } else if (roleMapping[userType] === "operator") {
            forbiddenURL = operatorURLExcludeKeys;
        } else if (roleMapping[userType] === "readonly") {
            forbiddenURL = readonlyURLExcludeKeys;
        }
        if (forbiddenURL.includes(currentPath)) {
            setIsForbidden(true);
        } else {
            setIsForbidden(false);
        }
    }, [Component]);

    if (!isDataLoaded) {
        return null;
    }

    return isForbidden ? <ForbiddenPage /> : <Component {...rest} />;
};
export default ProtectedRoute;
