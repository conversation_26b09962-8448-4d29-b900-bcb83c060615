import {request} from "@/utils/common/request";

const baseUrl = "/ampcon/dc_blueprint/fabric_unit";

export function fetchUnitListInfo(page, pageSize, filterFields = [], sortFields = [], searchFields = {}) {
    return request({
        url: `${baseUrl}/list`,
        method: "POST",
        data: {
            filterFields,
            sortFields,
            searchFields,
            page,
            pageSize
        }
    });
}

export function saveUnitInfo(data) {
    return request({
        url: `${baseUrl}/save`,
        method: "POST",
        data
    });
}

export function fetchUnitDetailedInfo(data) {
    return request({
        url: `${baseUrl}/view`,
        method: "POST",
        data
    });
}

export function cloneUnitInfo(data) {
    return request({
        url: `${baseUrl}/clone`,
        method: "POST",
        data
    });
}

export function delUnitInfo(unit_id) {
    return request({
        url: `${baseUrl}/delete`,
        method: "POST",
        data: {
            unit_id
        }
    });
}
