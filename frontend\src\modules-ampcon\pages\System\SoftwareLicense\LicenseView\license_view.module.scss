.licenseView{
    display: grid;
    height: 100%;
    width: 100%;
    gap: 18px 24px;
    grid-template-columns: repeat(7, 1rf);
    grid-template-rows: 1fr 2fr;
    > div {
        &:nth-child(1) {
            grid-column: span 4;
        }
        &:nth-child(2) {
            grid-column: span 3;
        }
        &:nth-child(3) {
            grid-column: 1 / 8;
        }}
} 

.card_title {
    font-size: 18px;
}

.expiredTag {
    background-color: rgba(245, 63, 63, 0.1);
    color: rgb(245, 63, 63);
    border-color: rgb(245, 63, 63);
    font-size: 14px;
    // line-height: 22px;
    // width: 80px;
    text-align: center;
}

.avaliableTag {
    background-color: rgba(84, 196, 28, 0.1);
    color: #2BC174;
    border-color: #2BC174;
    font-size: 14px;
    text-align: center;
}

.invalidTag {
    background-color: rgba(253, 186, 0, 0.1);
    color: rgb(253, 186, 0);
    border-color: rgb(253, 186, 0);
    font-size: 14px;
    text-align: center;
}
// .pieStyle > .ant-card-body > div > div {
//     display: flex;
//     justify-content: center;
// }
