import React, {useEffect, useState} from "react";
import {Card} from "antd";
import {getDashboardData, getOTNRecentAlarmCount} from "@/modules-otn/apis/api";
import {useSelector} from "react-redux";
import Icon from "@ant-design/icons";
import {criticalIcon, majorIcon, minorIcon, warningIcon} from "@/modules-otn/utils/iconSvg";
import {fetchStaticHistory, getRecentAlarmCount} from "@/modules-ampcon/apis/dashboard_api";
import {getOTNDeviceList} from "@/modules-ampcon/apis/otn";
import {Doughnutchart, PlusDoughnutchart, BarEcharts, MultiLineChart} from "./echarts_common";
import styles from "./otn_view.module.scss";
import Alarm from "../otn/alarm/alarm";
import {getAmpconOTNCardList} from "@/modules-ampcon/apis/otn";

const OtnView = () => {
    const [statisticsData, setStatisticsData] = useState({
        serverCpuData: [],
        serverMemData: [],
        serverDiskData: [],
        loading: true
    });
    const {alarms, switchAlarms} = useSelector(state => state.notification);
    const [otnViewData, setOtnViewData] = useState();
    const [cardSeriesData, setCardSeriesData] = useState();
    const [neSeriesData, setNeSeriesData] = useState();
    const [alarmStatisticData, setAlarmStatisticData] = useState([]);
    const alarmRightImg = [criticalIcon, majorIcon, minorIcon, warningIcon];
    const alarmRightText = ["Critical", "Major", "Minor", "Warning"];
    const [alarmRightData, setAlarmRightData] = useState();
    useEffect(() => {
        const fetchHistoryData = async () => {
            await fetchStaticHistory().then(res => {
                if (res.status === 200) {
                    const cpu_count = parseFloat(parseFloat(res.data[0].cpu).toFixed(2));
                    const cpu_count_free = parseFloat(parseFloat(100 - cpu_count).toFixed(2));
                    const mem_count = parseFloat(parseFloat(res.data[0].mem).toFixed(2));
                    const mem_count_free = parseFloat(parseFloat(100 - mem_count).toFixed(2));
                    const disk_count = parseFloat(parseFloat(res.data[0].disk).toFixed(2));
                    const disk_count_free = parseFloat(parseFloat(100 - disk_count).toFixed(2));

                    setStatisticsData({
                        serverCpuData: [
                            {value: cpu_count, name: `Usage ${cpu_count}`},
                            {value: cpu_count_free, name: `Free ${cpu_count_free}`}
                        ],
                        serverMemData: [
                            {value: mem_count, name: `Usage ${mem_count}`},
                            {value: mem_count_free, name: `Free ${mem_count_free}`}
                        ],
                        serverDiskData: [
                            {value: disk_count, name: `Usage ${disk_count}`},
                            {value: disk_count_free, name: `Free ${disk_count_free}`}
                        ],
                        loading: false
                    });
                }
            });
        };
        fetchHistoryData();
    }, []);
    useEffect(() => {
        const countProperty = (obj, property) => {
            return obj.reduce((count, item) => (item.severity === property ? count + 1 : count), 0);
        };
        const result = ["CRITICAL", "MAJOR", "MINOR", "WARNING"].map(property =>
            countProperty([...alarms, ...switchAlarms], property)
        );
        setAlarmRightData(result);
    }, [alarms, switchAlarms]);
    useEffect(() => {
        setCardSeriesData(
            otnViewData?.card
                ? Object.entries(otnViewData?.card).map(item => {
                      return {
                          name: item[0],
                          value: item[1]
                      };
                  })
                : []
        );
        const seriesData = otnViewData?.ne
            ? Object.entries(otnViewData?.ne).map(item => {
                  if (item && item[0] !== "offLineStatistic") {
                      return {
                          name: item[0].charAt(0).toUpperCase() + item[0].slice(1).toLowerCase(),
                          value:
                              item[0] === "onLine"
                                  ? {
                                        value: item[1],
                                        itemStyle: {
                                            color: "#78D047"
                                        }
                                    }
                                  : {
                                        value: item[1],
                                        itemStyle: {
                                            color: "#C5CACD"
                                        }
                                    }
                      };
                  }
              })
            : [];
        setNeSeriesData(seriesData.filter(item => item !== undefined));
    }, [otnViewData]);
    useEffect(() => {
        const fetchAlarmData = async () => {
            const end_time = new Date(new Date().getTime()).toISOString().replace("T", " ").substring(0, 19);
            const start_time = new Date(new Date().getTime() - 24 * 60 * 60 * 1000)
                .toISOString()
                .replace("T", " ")
                .substring(0, 19);
            try {
                const recentAlarmCount = (await getRecentAlarmCount(start_time, end_time)).data;
                const otnRecentAlarmCount = await getOTNRecentAlarmCount({start_time, end_time});

                const mergedData = (otnRecentAlarmCount || []).map((otnItem, index) => {
                    const item = recentAlarmCount[index] || {};

                    return {
                        hour: otnItem.hour,
                        levels: {
                            Critical: (otnItem?.levels?.Critical || 0) + (item?.levels?.error || 0), // error -> Critical
                            Major: (otnItem?.levels?.Major || 0) + (item?.levels?.warn || 0), // warn -> Major
                            Warning: (otnItem.levels.Warning || 0) + (item?.levels?.info || 0), // info -> Warning
                            Minor: otnItem?.levels?.Minor || 0
                        }
                    };
                });

                setAlarmStatisticData(mergedData);
            } catch (error) {
                console.error("Error fetching alarm data", error);
            }
        };
        fetchAlarmData();
    }, []);
    useEffect(() => {
        const mergeStatus = (otnData, ampconOTNData) => {
            let onLineCount = otnData?.ne?.onLine;
            let offLineCount = otnData?.ne?.offLine;

            ampconOTNData?.neInfo?.forEach(item => {
                const runState = item?.value?.runState;
                if (runState === 1) {
                    onLineCount += 1;
                } else {
                    offLineCount += 1;
                }
            });

            return {
                ...otnData?.ne,
                onLine: onLineCount,
                offLine: offLineCount
            };
        };
        const mergeCard = (otnData, ampconOTNData) => {
            const otnCard = otnData?.card;

            const mergedDict = {};
            if (otnCard && Object.keys(otnCard).length > 0) {
                for (const [key, value] of Object.entries(otnCard)) {
                    mergedDict[key] = (mergedDict[key] || 0) + value;
                }
            }
            if (ampconOTNData?.data && Object.keys(ampconOTNData?.data).length > 0) {
                for (const [key, value] of Object.entries(ampconOTNData?.data)) {
                    if (key === "EDFA" || key === "VOA") {
                        mergedDict.OA = (mergedDict.OA || 0) + value;
                    } else if (key === "PSU") {
                        mergedDict.POWER_SUPPLY = (mergedDict.POWER_SUPPLY || 0) + value;
                    } else if (key === "NMU") {
                        /* empty */
                    } else {
                        mergedDict[key] = (mergedDict[key] || 0) + value;
                    }
                }
            }
            return mergedDict;
        };
        const fetchData = async () => {
            const [dashboardData, ampconOTNData, ampconOTNCards] = await Promise.all([
                getDashboardData(),
                getOTNDeviceList(),
                getAmpconOTNCardList()
            ]);
            const mergedData = {...dashboardData};
            mergedData.ne = mergeStatus(dashboardData, ampconOTNData);
            mergedData.card = mergeCard(dashboardData, ampconOTNCards);
            setOtnViewData(mergedData);
        };
        fetchData();
        const intervalId = setInterval(fetchData, 10000);
        return () => {
            clearInterval(intervalId);
        };
    }, []);
    return (
        <div className={styles.otnView}>
            <Card title={<div className={styles.otnView_custom_title}>Alarms</div>} bordered={false}>
                <div className={styles.otnView_header_cardBody}>
                    {alarmRightData?.map((item, index) => (
                        // eslint-disable-next-line react/no-array-index-key
                        <div key={index} className={styles.otnView_alarms_value}>
                            <Icon component={alarmRightImg[index]} />
                            <div className={styles.otnView_alarmText}>
                                <span className={styles.otnView_alarms_number}>{item}</span>
                                <div className={styles.otnView_header_title}>{alarmRightText[index]}</div>
                            </div>
                        </div>
                    ))}
                </div>
            </Card>
            <Card title={<div className={styles.otnView_custom_title}>CPU</div>} loading={statisticsData.loading}>
                <Doughnutchart chartData={statisticsData.serverCpuData} Echartsname="CPU" color="#14C9BB" />
            </Card>

            <Card title={<div className={styles.otnView_custom_title}>MEM</div>} loading={statisticsData.loading}>
                <Doughnutchart chartData={statisticsData.serverMemData} Echartsname="Mem" color="#FFBB00" />
            </Card>

            <Card title={<div className={styles.otnView_custom_title}>DISK</div>} loading={statisticsData.loading}>
                <Doughnutchart chartData={statisticsData.serverDiskData} Echartsname="Disk" color="#14C9BB" />
            </Card>
            <Card title={<div className={styles.otnView_custom_title}>Card</div>} bordered={false}>
                <BarEcharts colorList={["#708DFD"]} width="24px" seriesData={cardSeriesData} seriesName="Card" />
            </Card>
            <Card
                title={<div className={styles.otnView_custom_title}>Alarm Statistics For The Past 24H</div>}
                bordered={false}
            >
                <MultiLineChart chartData={alarmStatisticData} />
            </Card>
            <Card
                title={<div className={styles.otnView_custom_title}>NE Statistics</div>}
                loading={statisticsData.loading}
            >
                <div>
                    <PlusDoughnutchart seriesData={neSeriesData} Echartsname="NE" color="#14C9BB" />
                    <div className={styles.otnView_ne}>
                        <div className={styles.otnView_item}>
                            <span className={styles.otnView_sizeText}>{otnViewData?.ne?.onLine}</span>
                            <div className={styles.otnView_nebottom}>
                                <div className={styles.otnView_neOnlineCricle} />
                                <span className={styles.otnView_onlineText}>Online</span>
                            </div>
                        </div>
                        <div className={styles.otnView_item}>
                            <span className={styles.otnView_sizeText}>{otnViewData?.ne?.offLine}</span>
                            <div className={styles.otnView_nebottom}>
                                <div className={styles.otnView_neOfflineCricle} />
                                <span className={styles.otnView_onlineText}>Offline</span>
                            </div>
                        </div>
                    </div>
                </div>
            </Card>
            <Card title={<div className={styles.otnView_custom_title}>Alarm</div>} bordered={false}>
                <div className={styles.otnView_alarmCard_alarmBody} style={{minHeight: "40vh"}}>
                    <Alarm alarmType="currentAlarmStatistics" />
                </div>
            </Card>
        </div>
    );
};

export default OtnView;
