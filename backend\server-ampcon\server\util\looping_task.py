import time
import threading
import logging

LOG = logging.getLogger(__name__)


class LoopingTask:

    def __init__(self, func, interval, delay, args, kwargs):
        self.func = func
        self.args = args
        self.kwargs = kwargs
        self.delay = delay
        self.interval = interval
        self.exe_time = time.time() + delay

    def run(self):
        try:
            self.func(*self.args, **self.kwargs)
        except Exception as e:
            LOG.error('task %s run error [%s]', self.func.__name__, str(e))


class LoopingTaskManage(threading.Thread):

    def __init__(self):
        super(LoopingTaskManage, self).__init__()
        self.heap = list()
        self.event = threading.Event()
        self.name = 'looping-tasks-manage'

    def _sift_up(self, n):
        if n <= 0:
            return
        p = (n - 2 + n%2)/2
        if self.heap[p].exe_time > self.heap[n].exe_time:
            swap(self.heap, p, n)
            self._sift_up(p)

    def _sift_down(self, n):
        l = len(self.heap)
        if l < 2*n + 2:
            return
        left_c = 2*n + 1
        right_c = None
        if l > 2*n + 2:
            right_c = left_c + 1

        min_c = left_c if not right_c or self.heap[left_c].exe_time <= self.heap[right_c].exe_time else right_c
        if self.heap[min_c].exe_time < self.heap[n].exe_time:
            swap(self.heap, min_c, n)
            self._sift_down(min_c)

    def start_task(self, func, interval, delay=0, args=(), kwargs={}):
        task = LoopingTask(func, interval, delay, args, kwargs)

        l = len(self.heap)
        if l > 0:
            last_exe_time = self.heap[0].exe_time
        self.heap.append(task)
        self._sift_up(l)
        if l > 0 and task.exe_time < last_exe_time:
            self.event.set()

    def run(self):
        while True:
            l = len(self.heap)
            if l > 0 and self.heap[0].exe_time <= time.time():
                last_task = self.heap[0]

                # start schedule last task
                try:
                    last_task.run()
                except Exception as e:
                    LOG.exception(e)
                finally:
                    last_task.exe_time = time.time() + last_task.interval
                    swap(self.heap, 0, l - 1)
                    self._sift_down(0)
                    self._sift_up(l-1)

            idle_time = self.heap[0].exe_time - time.time() if len(self.heap) > 0 else 5
            self.event.wait(idle_time)
            self.event.clear()


looping_task_manage = LoopingTaskManage()


def swap(ls, m, n):
    tmp = ls[m]
    ls[m] = ls[n]
    ls[n] = tmp


def test1(a):
    print('%s>>>>>>>>>>>>>>>>>>>test1 %s' % (time.time(), a))


def test2(b='bb'):
    print('%s>>>>>>>>>>>>>>>>>>>test2 %s' % (time.time(), b))


def test3(c, d='d'):
    print('%s>>>>>>>>>>>>>>>>>>>test3 %s %s' % (time.time(), c, d))


def test4():
    print('%s>>>>>>>>>>>>>>>>>>>test4' % time.time())


def test5():
    print('%s>>>>>>>>>>>>>>>>>>>test5' % time.time())

if __name__ == '__main__':
    looping_task_manage = LoopingTaskManage()
    threading.Thread(target=looping_task_manage.run, name='loop-test').start()
    looping_task_manage.start_task(test1, 5, delay=1, args=['aa'])
    looping_task_manage.start_task(test2, 10, delay=1, kwargs={'b': 'bbb'})
    looping_task_manage.start_task(test3, 15, delay=1, args=['cc'], kwargs={'d': 'dddd'})
    looping_task_manage.start_task(test4, 60, delay=1)
    looping_task_manage.start_task(test5, 120, delay=1)
    input('please wait')
