import React, {useRef, useEffect, useState} from "react";
import * as echarts from "echarts";
import {chunk, debounce} from "lodash";
import styles from "./old_otn_view.module.scss";
import {CaretLeftOutlined, CaretRightOutlined} from "@ant-design/icons";
import {Color} from "@antv/x6";

export const BarEcharts = ({seriesData = [], colorList = [], width = "", onClicked = null}) => {
    const pageSize = 5;
    const [currentPage, setCurrentPage] = useState(0);
    const getPageData = page => {
        const start = page * pageSize;
        const end = start + pageSize;
        return {
            categories: seriesData.slice(start, end).map(item => item.name),
            counts: seriesData.slice(start, end).map(item => item.value)
        };
    };
    const {categories: currentCategories, counts: currentCounts} = getPageData(currentPage);
    const totalPages = Math.ceil(seriesData.length / pageSize);

    const handlePageChange = direction => {
        if (direction === "next" && currentPage < totalPages - 1) {
            setCurrentPage(currentPage + 1);
        } else if (direction === "prev" && currentPage > 0) {
            setCurrentPage(currentPage - 1);
        }
    };
    const allCounts = seriesData.map(item => item.value);
    const maxValue = Math.max(...allCounts);
    const chartRef = useRef();
    useEffect(() => {
        const myChart = echarts.init(chartRef.current);
        const option = {
            tooltip: {
                trigger: "axis",
                formatter(params) {
                    const {name} = params[0];
                    const {value} = params[0];
                    return `${name}: ${value}`;
                }
            },
            xAxis: {
                data: currentCategories,
                axisLabel: {
                    interval: 0
                },
                axisTick: {
                    alignWithLabel: true
                }
            },
            yAxis: {
                scale: false,
                min: 0,
                max: maxValue + 1
            },
            grid: {
                left: "4%",
                right: "2%",
                top: "10%",
                bottom: "16%"
            },
            series: [
                {
                    type: "bar",
                    barWidth: width || "40%",
                    data: currentCounts
                }
            ]
        };
        if (colorList.length !== 0) {
            option.color = colorList;
        }
        myChart.setOption(option);
        myChart.on("click", params => {
            if (onClicked) {
                onClicked(params);
            }
        });

        const handleResize = () => {
            myChart.resize();
        };

        window.addEventListener("resize", handleResize);

        return () => {
            window.removeEventListener("resize", handleResize);
        };
    }, [currentCounts]);
    return (
        <>
            <div style={{height: "290px", width: "100%"}} ref={chartRef} />
            <div
                style={{
                    position: "absolute",
                    bottom: "1vh",
                    justifyContent: "right",
                    right: "2.8vh"
                }}
            >
                <span
                    onClick={() => handlePageChange("prev")}
                    disabled={currentPage === 0}
                    style={{
                        cursor: currentPage === 0 ? "not-allowed" : "pointer",
                        color: currentPage === 0 ? "gray" : "black"
                    }}
                >
                    <CaretLeftOutlined />
                </span>
                <span>
                    {currentPage + 1} / {totalPages}
                </span>
                <span
                    onClick={() => handlePageChange("next")}
                    disabled={currentPage === totalPages - 1}
                    style={{
                        cursor: currentPage === totalPages - 1 ? "not-allowed" : "pointer",
                        color: currentPage === totalPages - 1 ? "gray" : "black"
                    }}
                >
                    <CaretRightOutlined />
                </span>
            </div>
        </>
    );
};
export const Doughnutchart = ({chartData, Echartsname}) => {
    const DoughnutchartRef = useRef();
    useEffect(() => {
        const Doughnutchart = echarts.init(DoughnutchartRef.current);
        const pieColors =
            Echartsname === "Devices" ? ["#D8DCDC", "rgb(19, 202, 188)"] : ["rgb(19, 202, 188)", "#D8DCDC"];
        const DoughnutchartOption = {
            color: pieColors,
            tooltip: {
                trigger: "item",
                backgroundColor: "rgba(0, 0, 0, 0.6)",
                textStyle: {
                    color: "#fff"
                },
                formatter(params) {
                    console.log(params);
                    let tooltipContent = "";
                    tooltipContent += `${params.seriesName}<br/>`;
                    tooltipContent += `${params.name}%<br/>`;
                    return tooltipContent;
                }
            },
            graphic: [
                {
                    type: "text",
                    left: "center",
                    top: "80%",
                    style: {
                        text: "Usage",
                        fill: "#929A9E",
                        fontSize: "16px"
                    }
                }
            ],
            series: [
                {
                    name: Echartsname,
                    type: "pie",
                    radius: ["105%", "130%"],
                    center: ["50%", "75%"],
                    startAngle: 180,
                    endAngle: 360,
                    avoidLabelOverlap: false,
                    label: {
                        show: false
                    },
                    labelLine: {
                        show: false,
                        lineStyle: {
                            width: 3
                        }
                    },
                    data: chartData,
                    clockwise: Echartsname !== "Devices"
                },
                {
                    name: Echartsname,
                    type: "pie",
                    color: "#E0F9F6",
                    radius: "false",
                    center: ["50%", "35%"],
                    top: "40%",
                    label: {
                        position: "center",
                        formatter: "{data|{c}}{percent|%}",
                        rich: {
                            data: {
                                fontWeight: 600,
                                fontSize: 16,
                                color: `#212519`
                            },
                            percent: {
                                fontWeight: 600,
                                fontSize: 20,
                                color: `#212519`
                            }
                        }
                    },
                    labelLine: {
                        show: false
                    },
                    data: [{value: chartData[0]?.value, name: chartData[0]?.name}]
                }
            ]
        };
        Doughnutchart.setOption(DoughnutchartOption);
        const handleResize = () => {
            Doughnutchart.resize();
        };
        window.addEventListener("resize", handleResize);
        return () => {
            window.removeEventListener("resize", handleResize);
        };
    }, []);
    return (
        <div className={styles.echartsContainer}>
            <div className={styles.echartsContainer_echart} ref={DoughnutchartRef} style={{height: "110px"}} />
        </div>
    );
};
export const PlusDoughnutchart = ({seriesData, Echartsname}) => {
    const DoughnutchartRef = useRef();
    useEffect(() => {
        const Doughnutchart = echarts.init(DoughnutchartRef.current);
        const pieColors =
            Echartsname === "Devices" ? ["#D8DCDC", "rgb(19, 202, 188)"] : ["rgb(19, 202, 188)", "#D8DCDC"];
        const totalValue = seriesData.reduce((prev, cur) => prev + (cur.value.value || 0), 0);
        const DoughnutchartOption = {
            color: pieColors,
            tooltip: {
                trigger: "item",
                backgroundColor: "rgba(0, 0, 0, 0.6)",
                textStyle: {
                    color: "#fff"
                }
            },
            formatter: params => {
                if (params.name === "Total") {
                    return "";
                }
                return `${Echartsname}<br/>${params.name}: ${params.value}`;
            },
            title: {
                text: "Total",
                left: "center",
                top: "88%",
                textStyle: {
                    color: "#929A9E",
                    fontSize: 14,
                    fontWeight: "normal"
                }
            },
            series: [
                {
                    name: Echartsname,
                    type: "pie",
                    radius: ["62%", "80%"],
                    center: ["50%", "56%"],
                    startAngle: 230,
                    endAngle: 310,
                    avoidLabelOverlap: false,
                    label: {
                        show: false
                    },
                    labelLine: {
                        show: false,
                        lineStyle: {
                            width: 3
                        }
                    },
                    data: seriesData
                        ? seriesData.map(item => ({
                              value: item.value.value,
                              name: item.name
                          }))
                        : [],
                    clockwise: Echartsname !== "Devices"
                },
                {
                    name: Echartsname,
                    type: "pie",
                    color: "#E0F9F6",
                    radius: ["46%", "0%"],
                    center: ["50%", "56%"],
                    label: {
                        position: "center",
                        formatter: `{total|${totalValue}}`,
                        rich: {
                            total: {
                                fontWeight: 600,
                                fontSize: 30,
                                color: `#14C9BB`,
                                lineHeight: 40,
                                padding: [5, 0, 0, 0]
                            }
                        }
                    },
                    labelLine: {
                        show: false
                    },
                    data: [{value: totalValue, name: "Total"}]
                }
            ]
        };
        Doughnutchart.setOption(DoughnutchartOption);
        const handleResize = () => {
            Doughnutchart.resize();
        };
        window.addEventListener("resize", handleResize);
        return () => {
            window.removeEventListener("resize", handleResize);
        };
    }, [seriesData]);
    return (
        <div className={styles.echartsContainer}>
            <div className={styles.echartsContainer_echart} ref={DoughnutchartRef} style={{height: "220px"}} />
        </div>
    );
};
export const PieEcharts = ({seriesData, seriesName}) => {
    const chartRef = useRef();
    const chartObjRef = useRef();

    const getLayoutOption = el => {
        try {
            const boxWidth = el.clientWidth;
            const boxHeight = el.clientHeight;

            let type = "plain";
            const pieRadius = boxHeight * 0.6;
            const legendWidthList = chunk(seriesData?.length ? seriesData : [{name: seriesName}], 9).map(item => {
                const textLength = item.reduce((max, item) => {
                    return item.name.length > max ? item.name.length : max;
                }, 0);
                return textLength;
            });
            let legendWidth = legendWidthList.reduce((count, length) => {
                const itemWidth = length * 10 + 35;
                return count + itemWidth;
            }, 0);
            let needWidth = legendWidth + pieRadius;

            if (boxWidth < needWidth) {
                type = "scroll";
                legendWidth = legendWidthList[0] * 10;
                needWidth = legendWidth + pieRadius;
            }
            const paddingWidth = boxWidth - needWidth;
            const pieWidth = paddingWidth / 24;
            const centerLeft = pieWidth * 9 + pieRadius * 0.5;
            const legendLeft = pieWidth * 16 + pieRadius;
            return {type, centerLeft, legendLeft};
        } catch (e) {
            return {type: "plain", centerLeft: "76%", legendLeft: 0};
        }
    };

    useEffect(() => {
        const myChart = echarts.init(chartRef.current);
        const {type, centerLeft, legendLeft} = getLayoutOption(chartRef.current);
        const option = {
            tooltip: {
                trigger: "item",
                backgroundColor: "rgba(0, 0, 0, 0.6)",
                textStyle: {
                    color: "#fff"
                },
                formatter(params) {
                    let tooltipContent = "";
                    tooltipContent += `${params.name}<br/>`;
                    tooltipContent += `${params.seriesName}: ${params.value}<br/>`;
                    return tooltipContent;
                }
            },
            legend: {
                type,
                orient: "vertical",
                top: "center",
                left: legendLeft,
                itemGap: 15,
                itemWidth: 12,
                itemHeight: 12
            },
            series: [
                {
                    name: seriesName,
                    type: "pie",
                    center: [centerLeft, "50%"],
                    radius: "60%",
                    label: {
                        show: false
                    },
                    data: seriesData,
                    emphasis: {
                        itemStyle: {
                            shadowBlur: 10,
                            shadowOffsetX: 0,
                            shadowColor: "rgba(0, 0, 0, 0.5)"
                        }
                    }
                }
            ]
        };
        myChart.setOption(option);
        chartObjRef.current = myChart;

        const observer = new ResizeObserver(
            debounce(() => {
                const chart = chartObjRef.current;
                const currentOption = chart.getOption();
                const {type, centerLeft, legendLeft} = getLayoutOption(chartRef.current);

                currentOption.legend[0].type = type;
                currentOption.legend[0].left = legendLeft;
                currentOption.series[0].center = [centerLeft, "50%"];
                chart.setOption(currentOption);
                chart.resize();
            }, 100)
        );
        observer.observe(document.querySelector("body"));
        return () => {
            observer.disconnect();
        };
    }, [seriesData]);
    return (
        <div className={styles.echartsContainer}>
            <div className={styles.echartsContainer_echart} ref={chartRef} />
        </div>
    );
};
export const Histogramchart = ({chartData, LicenseXAxisData, LicenseSeriesColor, color}) => {
    const histogramRef = useRef();
    useEffect(() => {
        const myzhChart = echarts.init(histogramRef.current);
        const histogramOption = {
            color,
            grid: {
                show: false
            },
            xAxis: {
                type: "category",
                data: LicenseXAxisData,
                axisLabel: {
                    show: false
                },
                axisLine: {
                    show: false
                },
                axisTick: {
                    show: false
                }
            },
            yAxis: {
                type: "value",
                splitLine: {
                    show: false
                },
                axisLabel: {
                    show: false
                },
                axisLine: {
                    show: false
                }
            },
            series: [
                {
                    data: chartData,
                    type: "bar",
                    itemStyle: {
                        color: LicenseSeriesColor
                    },
                    barWidth: "40%"
                }
            ]
        };
        myzhChart.setOption(histogramOption);
        return () => {
            myzhChart.dispose();
        };
    }, []);
    return (
        <div>
            <div className={styles.echartsContainer_histogramchart} ref={histogramRef} />
        </div>
    );
};
export const Omschart = () => {
    const omsChartRef = useRef();
    useEffect(() => {
        const omsChart = echarts.init(omsChartRef.current);
        const omsOption = {
            xAxis: [
                {
                    type: "category",
                    boundaryGap: false,
                    data: ["Mon", "Tue", "Wed", "Thu", "Fri", "Sat", "Sun"],
                    axisLabel: {
                        show: false
                    },
                    axisLine: {
                        show: false
                    },
                    axisTick: {
                        show: false
                    }
                }
            ],
            yAxis: [
                {
                    type: "value",
                    splitLine: {
                        show: false
                    },
                    axisLabel: {
                        show: false
                    }
                }
            ],
            series: [
                {
                    name: "Email",
                    type: "line",
                    stack: "Total",
                    areaStyle: {
                        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                            {offset: 0, color: "#04aff3"},
                            {offset: 1, color: "#dbeaf5"}
                        ])
                    },
                    lineStyle: {
                        width: 2,
                        type: "solid",
                        color: "#00B7FF"
                    },
                    emphasis: {
                        focus: "series"
                    },
                    data: [6, 5, 14, 8, 10, 6],
                    showSymbol: false
                }
            ]
        };
        omsChart.setOption(omsOption);
    }, []);
    return (
        <div>
            <div className={styles.echartsContainer_histogramchart} ref={omsChartRef} />
        </div>
    );
};
export const NeStatisticsEchart = ({seriesData}) => {
    const neChartRef = useRef();
    useEffect(() => {
        const neChart = echarts.init(neChartRef.current);
        const zhOption = {
            tooltip: {
                trigger: "axis",
                backgroundColor: "rgba(0, 0, 0, 0.6)",
                textStyle: {
                    color: "#fff"
                }
            },
            legend: {
                bottom: "bottom",
                itemWidth: 10,
                itemHeight: 10
            },
            grid: {
                left: "3%",
                right: "4%",
                bottom: "16%",
                containLabel: true
            },
            xAxis: {
                type: "value",
                boundaryGap: [0, 0.1],
                axisLine: {show: true},
                axisTick: {show: true},
                minInterval: 1
            },
            yAxis: {
                type: "category",
                axisTick: false,
                inverse: true,
                animationDuration: 10000,
                animationDurationUpdate: 10000,
                data: seriesData ? seriesData?.map(item => item?.name) : []
            },
            series: [
                {
                    type: "bar",
                    data: seriesData ? seriesData?.map(item => item?.value) : [],
                    barWidth: "30%",
                    label: {
                        show: true,
                        position: "right",
                        valueAnimation: true
                    }
                }
            ],
            animationDuration: 0,
            animationDurationUpdate: 10000,
            animationEasing: "linear",
            animationEasingUpdate: "linear"
        };
        neChart.setOption(zhOption);
        window.onresize = () => {
            neChart.resize();
        };
        return () => {
            neChart.dispose();
        };
    }, [seriesData]);
    return (
        <div className={styles.echartsContainer}>
            <div className={styles.echartsContainer_echart} ref={neChartRef} />
        </div>
    );
};
export const HollowPiechart = ({chartData, Echartsname}) => {
    const HollowPiechartRef = useRef();
    useEffect(() => {
        const HollowPiechart = echarts.init(HollowPiechartRef.current);
        const pieColors =
            Echartsname === "Devices" ? ["rgb(19, 202, 188)", "#FF9F30"] : ["#FF9F30", "rgb(19, 202, 188)"];
        const HollowPieOption = {
            color: pieColors,
            tooltip: {
                trigger: "item",
                backgroundColor: "rgba(0, 0, 0, 0.6)",
                textStyle: {
                    color: "#fff"
                },
                formatter(params) {
                    let tooltipContent = "";
                    tooltipContent += `${params.seriesName}<br/>`;
                    tooltipContent += `${params.name}<br/>`;
                    return tooltipContent;
                }
            },
            legend: {
                orient: "vertical",
                top: "center",
                left: "10px",
                itemGap: 15,
                itemWidth: 10,
                itemHeight: 10,
                textStyle: {
                    rich: {
                        labelName: {
                            color: "#929A9E",
                            fontSize: 14
                        },
                        labelMark: {
                            fontWeight: 700,
                            fontSize: "16px",
                            align: "right",
                            color: "#323333"
                        }
                    }
                },
                formatter(params) {
                    const legend = params.split(" ");
                    return `{labelName|${legend[0]}}    {labelMark|${legend[1]}}`;
                }
            },
            series: [
                {
                    name: "Server Source",
                    type: "pie",
                    center: ["70%", "50%"],
                    radius: ["55%", "75%"],
                    avoidLabelOverlap: false,
                    label: {
                        show: false
                    },
                    labelLine: {
                        show: false,
                        lineStyle: {
                            width: 3
                        }
                    },
                    data: chartData,
                    clockwise: Echartsname !== "Devices"
                },
                {
                    name: Echartsname,
                    type: "pie",
                    radius: ["0%", "0%"],
                    center: ["70%", "50%"],
                    label: {
                        position: "inside",
                        formatter: `{data|{c}}\n{serie|{a}}`,
                        rich: {
                            data: {
                                fontWeight: 700,
                                fontSize: 16,
                                color: `#323333`
                            },
                            serie: {
                                fontSize: 14,
                                color: "#929A9E",
                                lineHeight: 28
                            }
                        }
                    },
                    labelLine: {
                        show: true
                    },
                    data: [chartData.reduce((prev, cur) => prev + cur.value, 0)]
                }
            ]
        };
        HollowPiechart.setOption(HollowPieOption);
        const handleResize = () => {
            HollowPiechart.resize();
        };
        window.addEventListener("resize", handleResize);
        return () => {
            window.removeEventListener("resize", handleResize);
        };
    }, []);
    return (
        <div className={styles.echartsContainer}>
            <div className={styles.echartsContainer_echart} ref={HollowPiechartRef} style={{height: "200px"}} />
        </div>
    );
};

export const MultiLineChart = ({chartData}) => {
    const chartRef = useRef(null);
    const [yAxisConfig, setYAxisConfig] = useState({max: 5, interval: 1});

    useEffect(() => {
        const calculateYAxisConfig = data => {
            if (data.length === 0) return {max: 5, interval: 1};

            const maxValue = Math.max(
                ...data.flatMap(entry => [
                    entry.levels.Critical,
                    entry.levels.Major,
                    entry.levels.Minor,
                    entry.levels.Warning
                ])
            );

            // eslint-disable-next-line no-nested-ternary
            const interval = maxValue <= 7 ? 1 : maxValue <= 20 ? 3 : maxValue <= 40 ? 5 : 8;
            const max = maxValue <= 7 ? 7 : Math.ceil(maxValue / interval) * interval;

            return {max, interval};
        };
        setYAxisConfig(calculateYAxisConfig(chartData));
    }, [chartData]);

    const parseData = data => {
        const dataMap = new Map();
        const timeLabels = [];
        const criticalData = [];
        const majorData = [];
        const minorData = [];
        const warningData = [];
        const userTimeZone = Intl.DateTimeFormat().resolvedOptions().timeZone;

        if (Array.isArray(data)) {
            data.forEach(entry => {
                const formattedTime = new Date(`${entry.hour} UTC`)
                    .toLocaleTimeString("en-GB", {timeZone: userTimeZone, hour12: false})
                    .slice(0, 5);
                timeLabels.push(formattedTime);
                criticalData.push(entry.levels.Critical);
                majorData.push(entry.levels.Major);
                minorData.push(entry.levels.Minor);
                warningData.push(entry.levels.Warning);
            });
        } else {
            console.error("data is not an array:", data);
        }

        dataMap.set("Critical", criticalData);
        dataMap.set("Major", majorData);
        dataMap.set("Minor", minorData);
        dataMap.set("Warning", warningData);

        return {dataMap, timeLabels};
    };

    // 为每个系列指定颜色
    const lineColors = {
        Critical: "#F67B7B",
        Major: "#F9A07A",
        Minor: "#F9D779",
        Warning: "#77D2F6"
    };
    const generateSeriesList = dataMap => {
        const seriesList = [];
        dataMap.forEach((data, name) => {
            const lineColor = lineColors[name]; // 获取每条线的颜色
            const series = {
                name,
                type: "line",
                smooth: true,
                symbol: "circle",
                symbolSize: 10,
                showSymbol: false,
                emphasis: {
                    itemStyle: {
                        color: lineColor,
                        borderWidth: 2,
                        borderColor: "#FFFFFF",
                        shadowBlur: 10,
                        shadowColor: lineColor
                    }
                },
                lineStyle: {
                    width: 2,
                    color: lineColor
                },
                itemStyle: {
                    color: lineColor
                },
                data
            };
            seriesList.push(series);
        });
        return seriesList;
    };
    useEffect(() => {
        const myChart = echarts.init(chartRef.current);
        const {dataMap, timeLabels} = parseData(chartData);

        const option = {
            tooltip: {
                trigger: "axis"
            },
            legend: {
                orient: "horizontal",
                // bottom: "2%",
                top: "94%",
                data: ["Critical", "Major", "Minor", "Warning"],
                itemWidth: 10,
                itemHeight: 10,
                icon: "rect",
                itemGap: 35
            },
            grid: {
                left: "2%",
                right: "2%",
                top: "10%",
                bottom: "10%",
                containLabel: true
            },
            xAxis: {
                type: "category",
                boundaryGap: false,
                data: timeLabels
            },
            yAxis: {
                type: "value",
                axisLabel: {
                    formatter: "{value}"
                },
                min: 0,
                max: yAxisConfig.max,
                interval: yAxisConfig.interval,
                splitLine: {
                    show: true
                }
            },
            series: generateSeriesList(dataMap)
        };

        // 渲染图表
        myChart.setOption(option);

        // 窗口尺寸变化时调整图表
        const handleResize = () => {
            myChart.resize();
        };

        window.addEventListener("resize", handleResize);

        // 清理工作：销毁图表实例
        return () => {
            window.removeEventListener("resize", handleResize);
            myChart.dispose();
        };
    }, [chartData, yAxisConfig]);

    return (
        <div style={{width: "100%", height: "100%"}}>
            <div ref={chartRef} style={{height: "290px"}} />
        </div>
    );
};

export const Linechart = ({title, chartData, chartXAxis}) => {
    const LinechartRef = useRef();
    useEffect(() => {
        const myzhChart = echarts.init(LinechartRef.current);
        const LineOption = {
            tooltip: {
                trigger: "axis",
                backgroundColor: "rgba(0, 0, 0, 0.6)",
                textStyle: {
                    color: "#fff"
                }
            },
            xAxis: {
                type: "category",
                data: chartData
            },
            yAxis: {
                type: "value",
                min: 0,
                max: 100,
                axisLabel: {
                    formatter: "{value} %"
                }
            },
            grid: {
                left: "7%",
                right: "7%",
                top: "5%",
                bottom: "10%"
            },
            series: [
                {
                    name: `${title}`,
                    type: "line",
                    data: chartXAxis,
                    symbol: "none",
                    itemStyle: {
                        color: "#14C9BB"
                    }
                }
            ]
        };
        myzhChart.setOption(LineOption);
        const handleResize = () => {
            myzhChart.resize();
        };

        window.addEventListener("resize", handleResize);

        return () => {
            window.removeEventListener("resize", handleResize);
        };
    }, [chartData, chartXAxis]);
    return (
        <div className={styles.echartsContainer}>
            <div className={styles.echartsContainer_echart} style={{width: "100%"}} ref={LinechartRef} />
        </div>
    );
};
export const Memorychart = ({chartData, memoryXAxis}) => {
    const LinechartRef = useRef();
    useEffect(() => {
        const myzhChart = echarts.init(LinechartRef.current);
        const LineOption = {
            tooltip: {
                trigger: "axis",
                backgroundColor: "rgba(0, 0, 0, 0.6)",
                textStyle: {
                    color: "#fff"
                }
            },
            xAxis: {
                type: "category",
                data: chartData
            },
            yAxis: {
                type: "value"
            },
            series: [
                {
                    name: "Memory Utilization",
                    data: memoryXAxis,
                    type: "bar",
                    itemStyle: {
                        color: "#14C9BB"
                    }
                }
            ]
        };
        myzhChart.setOption(LineOption);
        window.onresize = () => {
            myzhChart.resize();
        };
    }, [chartData, memoryXAxis]);
    return (
        <div className={styles.echartsContainer}>
            <div className={styles.echartsContainer_echart} ref={LinechartRef} />
        </div>
    );
};
