import {createColumnConfig, AmpConCustomTable, TableFilterDropdown} from "@/modules-ampcon/components/custom_table";
import {fetchJobInfo, fetchTaskOutputInfo, terminateJob} from "@/modules-ampcon/apis/automation_api";
import {Space, message} from "antd";
import {JobResultView} from "@/modules-ampcon/pages/Maintain/NetworkConfiguration/Automation/job_result";
import {useRef, useState} from "react";
import {confirmModalAction} from "@/modules-ampcon/components/custom_modal";

const JobView = () => {
    const tableRef = useRef(null);
    const matchFieldsList = [
        {name: "name", matchMode: "fuzzy"},
        {name: "playbook_name", matchMode: "fuzzy"},
        {name: "schedule_type", matchMode: "fuzzy"},
        {name: "create_time", matchMode: "fuzzy"}
    ];

    const searchFieldsList = ["name", "playbook_name", "schedule_type", "create_time"];

    const [isModalOpen, setIsModalOpen] = useState(false);
    const [isModalOpenItems, setIsModalOpenItems] = useState(false);
    const [curJob, setCurJob] = useState("");
    const [textAreaValue, setTextAreaValue] = useState("");

    const taskResults = record => {
        setIsModalOpen(true);
        setCurJob(record.name);
        fetchTaskOutputInfo({job_name: record.name}).then(res => {
            if (res.status === 200) {
                setTextAreaValue(res.info);
            } else {
                message.error("Failed to fetch task output info");
            }
        });
    };

    const delTask = record => {
        confirmModalAction("Are you sure want to delete?", () => {
            terminateJob(record.name).then(res => {
                if (res.status === 200) {
                    message.success(res.info);
                    tableRef.current.refreshTable();
                } else {
                    message.error("Failed to delete job");
                }
            });
        });
    };

    const columns = [
        {
            ...createColumnConfig("Job Name", null, TableFilterDropdown, "", "20%"),
            render: (_, record) => {
                return <div className="force-wrap-text">{record.name}</div>;
            }
        },
        {
            ...createColumnConfig("Playbook Name", null, TableFilterDropdown, "", "20%"),
            render: (_, record) => {
                return <div className="force-wrap-text">{record.playbook_name}</div>;
            }
        },
        createColumnConfig("Schedule Type", "schedule_type", TableFilterDropdown, "", "10%"),
        createColumnConfig("Job Creation Time", "create_time", TableFilterDropdown, "", "15%", "descend"),
        createColumnConfig("Create By(user)", "create_user", TableFilterDropdown, "", "10%"),
        createColumnConfig("Status", "status", TableFilterDropdown, "", "10%"),
        {
            title: "Operation",
            render: (_, record) => {
                return (
                    <div>
                        <Space size="large" className="actionLink">
                            <a onClick={() => taskResults(record)}>Task Results</a>
                            <a onClick={() => delTask(record)}>Remove</a>
                        </Space>
                    </div>
                );
            }
        }
    ];

    return (
        <>
            <JobResultView
                useJob
                extraParams={curJob}
                textAreaValue={textAreaValue}
                isModalOpen={isModalOpen}
                setIsModalOpen={setIsModalOpen}
                isModalOpenItems={isModalOpenItems}
                setIsModalOpenItems={setIsModalOpenItems}
            />
            <AmpConCustomTable
                ref={tableRef}
                columns={columns}
                searchFieldsList={searchFieldsList}
                matchFieldsList={matchFieldsList}
                fetchAPIInfo={fetchJobInfo}
            />
        </>
    );
};

export default JobView;
