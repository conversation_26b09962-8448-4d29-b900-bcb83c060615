package config

import (
	"log"
	"os"
	"path/filepath"

	"gopkg.in/yaml.v2"
)

type Config struct {
	GlobalConfig GlobalConfig `yaml:"global,omitempty"`
	ScrapeConfig ScrapeConfig `yaml:"scrape,omitempty"`
}

type GlobalConfig struct {
	Interval      int    `yaml:"update_target_interval,omitempty"`
	AmpconAddress string `yaml:"ampcon_address,omitempty"`
}

type ScrapeConfig struct {
	Interval int            `yaml:"scrape_interval,omitempty"`
	Target   []TargetConfig `yaml:"target,omitempty"`
}

type TargetConfig struct {
	TargetName string `yaml:"target_name,omitempty"`
	Address    string `yaml:"address,omitempty"`
	Username   string `yaml:"username,omitempty"`
	Password   string `yaml:"password,omitempty"`
	Enable     int    `yaml:"enable,omitempty"`
}

func LoadFile(paths []string) (*Config, error) {
	cfg := &Config{}
	for _, p := range paths {
		files, err := filepath.Glob(p)
		if err != nil {
			log.Println(err)
			return nil, err
		}
		for _, f := range files {
			content, err := os.ReadFile(f)
			if err != nil {
				log.Println(err)
				return nil, err
			}
			err = yaml.UnmarshalStrict(content, cfg)
			if err != nil {
				log.Println(err)
				return nil, err
			}
		}
	}
	return cfg, nil
}
