import {But<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, Modal} from "antd";
import <PERSON><PERSON> from "antd/es/upload/Dragger";
import {InboxOutlined} from "@ant-design/icons";

const UploadSingleFileModal = ({
    uploadByJsonModalTitle,
    isShowUploadJsonModal,
    preForm,
    fileList,
    uploadButtonCallback,
    beforeUploadCallback,
    onRemoveCallback,
    cancelCallback
}) => {
    return (
        <Modal
            className="ampcon-middle-modal"
            title={
                <div>
                    {uploadByJsonModalTitle}
                    <Divider style={{marginTop: 8, marginBottom: 0}} />
                </div>
            }
            open={isShowUploadJsonModal}
            onCancel={cancelCallback}
            footer={
                <Flex vertical>
                    <Divider style={{marginTop: 0, marginBottom: 20}} />
                    <Flex justify="flex-end">
                        <Button key="export" type="primary" onClick={uploadButtonCallback}>
                            Upload
                        </Button>
                    </Flex>
                </Flex>
            }
        >
            <div style={{minHeight: "268px"}}>
                {preForm || null}
                <Dragger
                    name="file"
                    beforeUpload={file => {
                        return beforeUploadCallback([file]);
                    }}
                    fileList={fileList}
                    multiple={false}
                    style={{marginBottom: "20px"}}
                    onRemove={onRemoveCallback}
                >
                    <p className="ant-upload-drag-icon">
                        <InboxOutlined />
                    </p>
                    <p className="ant-upload-text">Click or drag file to this area to upload</p>
                    <p className="ant-upload-hint">Support for a single upload.</p>
                </Dragger>
            </div>
        </Modal>
    );
};

export default UploadSingleFileModal;
