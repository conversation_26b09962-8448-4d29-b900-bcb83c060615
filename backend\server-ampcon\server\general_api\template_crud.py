#!/usr/bin/python3
# -*- coding: utf-8 -*-
"""
@author: <PERSON><PERSON>
@project: Pica8AmpCon
@file: template_crud.py
@function:
@time: 2022/1/4 16:59
"""
import ast
import difflib
import re
import zlib

import jinja2

from server.collect.rma_collect import convert_configstr_to_setcommand
from server.db.models.general import general_db
from server.db.models.inventory import Switch, SwitchAutoConfig, SwitchConfigSnapshot
from server.db.session import get_session
from server.south_api import ssh_api
from server.util import str_helper, utils
from .__init__ import traceback, request, jsonify, general_model, json, general, inven_db


def template_to_dict(model):
    return dict(
        name=model.name,
        description=model.description,
        content=model.content,
        j2_template=model.j2_template,
        params=model.params
    )


def template_to_dict_with_tag(model):
    return dict(
        name=model.name,
        description=model.description,
        content=model.content,
        j2_template=model.j2_template,
        params=model.params,
        tag=model.tag
    )


@general_model.route('/templates', methods=['GET'])
def get_templates_list():
    """
    :return: list[dict]
            [
              {
                "content": xxx,
                "description": "3024et",
                "j2_template": "set xovs enable true\n\n",
                "name": "3024et",
                "params": "{}\n",
                "platform": "N3024ET-ON",
                "tag": "tag"
              }
            ]
    """
    tag = request.args.get('tag')
    db_session = general.general_db.get_session()
    if not tag:
        template_info = db_session.query(general.GeneralTemplateWithTag).all()
    else:
        template_info = []
        for template in db_session.query(general.GeneralTemplateWithTag).all():
            if template.tag and tag in template.tag.split(','):
                template_info.append(template)
    return jsonify(list(map(template_to_dict_with_tag, template_info)))


@general_model.route('/templates/<template_name>', methods=['GET'])
def get_templates(template_name):
    """
    :param template_name: template
    :return: list[dict]
            [
              {
                "content": xxx,
                "description": "3024et",
                "j2_template": "set xovs enable true\n\n",
                "name": "3024et",
                "params": "{}\n",
                "platform": "N3024ET-ON"
              }
            ]
    """
    db_session = general.general_db.get_session()
    return jsonify(
        list(map(template_to_dict_with_tag, db_session.query(general.GeneralTemplateWithTag).filter(general.GeneralTemplateWithTag.name == template_name))))


@general_model.route('/templates/add', methods=['POST'])
def add_templates():
    """
    :return: code 200 success, 500 error
    """
    status, msg = 200, "success"
    try:
        params = json.loads(request.get_data(as_text=True))
        if "name" not in params or not params["name"]:
            raise ValueError("template name is required")
        name = params['name'].strip()
        description = params.get('description')
        s_platform = params.get('platform')
        if '(' in name or ')' in name or ':' in name or '\\' in name:
            return json.dumps({'status': '500', 'msg': 'Template name is invalid'})
        if not utils.is_valid_switch_model_name(s_platform):
            raise ValueError("s_platform name is invalid")
        content = params.get('content')
        action = params.get('action').lower()

        templ = general.general_db.get_model(general.GeneralTemplate, filters={'name': [name]})
        if templ:
            raise ValueError('Template {0} exists'.format(name))
        template_str, params = str_helper.generate_jinja2_template(content, line_seg='X', action=action)
        session = get_session()
        with session.begin(subtransactions=True):
            template = general.GeneralTemplate(name=name, platform=s_platform, description=description, content=content,
                                               j2_template=template_str, params=json.dumps(params))
            session.add(template)
    except ValueError as v:
        status, msg = 500, "ERROR:[%s]" % v
    except Exception as e:
        status, msg = 502, "ERROR:[%s]" % e
    finally:
        return jsonify({"status_code": status, "msg": msg})


@general_model.route('/templates/update', methods=['POST'])
def update_templates():
    """
    :return: code 200 success, 500 error
    """
    status, msg = 200, "success"
    try:
        params = json.loads(request.get_data(as_text=True))
        if "file_name" not in params or not params["file_name"]:
            raise ValueError("template name is required")
        name = params['file_name']
        file_type = params.get('type')
        content = params.get('content') if file_type == 'j2_template' else json.dumps(params.get('content'))
        hit_number = general.general_db.get_session().query(general.GeneralTemplate).filter(general.GeneralTemplate.name == name).count()
        if not hit_number:
            raise ValueError('template_name[%s] does not exist' % name)
        else:
            general.general_db.update_template_by_name(name, content, file_type)
    except ValueError as v:
        status, msg = 500, "ERROR:[%s]" % v
    except Exception as e:
        status, msg = 502, "ERROR:[%s]" % e
    finally:
        return jsonify({"status_code": status, "msg": msg})


@general_model.route('/templates/delete', methods=['POST'])
def del_templates():
    """
    :return: code 200 success, 500 error
    """
    status, msg = 200, "success"
    try:
        params = json.loads(request.get_data(as_text=True))
        if "name" not in params or not params["name"]:
            raise ValueError("template_name is required")
        name = params['name'].strip()
        hit_number = general.general_db.get_session().query(general.GeneralTemplate).filter(general.GeneralTemplate.name == name).count()
        if not hit_number:
            raise ValueError('template_name[%s] does not exist' % params['name'])
        general.general_db.delete_collection(general.GeneralConfigParams, filters={'template_name': [name]})
        general.general_db.delete_collection(general.GeneralTemplate, filters={'name': [name]})
    except ValueError as v:
        status, msg = 500, "ERROR:[%s]" % v
    except Exception as e:
        status, msg = 502, "ERROR:[%s]" % e
    finally:
        return jsonify({"status_code": status, "msg": msg})


def get_verify_config_str(sn, ip, config):
    if config == 'running-config':
        # get running config str
        host = ip
        config_str = ssh_api.get_switch_running_config(sn=sn, host=host, format='all_set')['running_config']
    else:
        # get snapshot config str
        snapshot = inven_db.get_collection(SwitchConfigSnapshot,
                                           filters={'sn': [sn], 'snapshot_time': [config]})
        if not snapshot:
            raise ValueError('Can\'t found {} {} snapshot'.format(sn, config))
        config_str = convert_configstr_to_setcommand(snapshot[0].archive_config.decode())
    return '\n'.join(sorted(list(map(str.strip, config_str.replace('"', '').strip().split('\n'))))).strip().replace('\r', '')


def hide_config_sensitive_info(s):
    key_reg = re.compile('key\s+([^\s]+)')
    password_reg = re.compile('password\s+([^\s]+)')
    s = key_reg.sub('key ***\n', s)
    s = password_reg.sub('password ***\n', s)
    return s


@general_model.route('/templates/template_verify', methods=['POST'])
def template_verify():
    """
    :return: code 200 success, 500 error
    """
    status, msg = 200, "success"
    # data = {}
    diff_str = ''
    try:
        params = json.loads(request.get_data(as_text=True))
        if "switch" not in params or not params["switch"]:
            raise ValueError("switch sn is required")
        elif "global_template" not in params or not params["global_template"]:
            raise ValueError("global_template is required")
        elif "site_template" not in params or not params["site_template"]:
            raise ValueError("site_template is required")
        elif "compare_config" not in params or not params["compare_config"]:
            raise ValueError("compare_config is required")
        if type(params["site_template"]) != list:
            raise ValueError('site_template is not list')

        sn = params['switch']
        global_config_name = params['global_template']
        switches = inven_db.get_collection(Switch, filters={'sn': [sn]})

        if not switches:
            raise ValueError('Can\'t found switch')

        # get deployed template str
        global_config = inven_db.get_model(SwitchAutoConfig, filters={'name': [global_config_name]})
        if global_config:
            global_config_line = global_config.config
        else:
            global_config_line = ''
        full_config = global_config_line
        template_params_str = inven_db.get_model(Switch, filters={'sn': [sn]}).config_parameters

        if not template_params_str:
            raise ValueError('template param string is empty!')

        template_params = ast.literal_eval(template_params_str)

        # for old type
        if not template_params.get('multiple_param'):
            template = general_db.get_template_by_name(params['site_template'][0])
            if not template:
                raise ValueError('Can\'t found site template')
            template = jinja2.Template(template.j2_template)
            full_config += '\n' + template.render(template_params)
        # for new multiple template param
        else:
            for template_name in params['site_template']:
                template = general_db.get_template_by_name(template_name)
                if not template:
                    raise ValueError('Can\'t found site template')
                template = jinja2.Template(template.j2_template)
                full_config += '\n' + template.render(template_params.get(template_name))
        config_lines_list = full_config.split("\n")
        revise_config_lines_list = []
        for config_line in config_lines_list:
            if re.search(r'[0-9a-zA-Z]', config_line):
                revise_config_lines_list.append(" ".join(config_line.split()))
        final_full_config = "\n".join(sorted(revise_config_lines_list))

        # get config str
        config_str = get_verify_config_str(sn, switches[0].mgt_ip, params["compare_config"])

        # data['final_full_config'] = hide_config_sensitive_info(final_full_config).replace('"', '').strip()
        # data['config_str'] = hide_config_sensitive_info(config_str)
        diff_list = list(map(str.strip, list(difflib.Differ().compare(final_full_config.replace('"', '').strip().split('\n'), config_str.split('\n')))))
        # data['diff_full_context'] = hide_config_sensitive_info("\n".join(diff_list))
        # data['diff_only'] = hide_config_sensitive_info("\n".join(filter(lambda x: True if x and x[0] in ['+', '-', '?'] else False, diff_list)))
        diff_str = hide_config_sensitive_info("\n".join(filter(lambda x: True if x and x[0] in ['+', '-', '?'] else False, diff_list)))
    except ValueError as v:
        status, msg = 500, "ERROR:[%s]" % v
    except Exception as e:
        status, msg = 502, "ERROR:[%s]" % e
    finally:
        return jsonify({"status_code": status, "msg": msg, 'diff_str': diff_str})


@general_model.route('/compare_config', methods=['POST'])
def compare_config():
    """
    :return: code 200 success, 500 error
    """
    status, msg = 200, "success"
    # data = {}
    diff_str = ''
    try:
        params = json.loads(request.get_data(as_text=True))
        if "switch1" not in params or not params["switch1"]:
            raise ValueError("switch1 sn is required")
        elif "switch1_config" not in params or not params["switch1_config"]:
            raise ValueError("switch1_config is required")
        elif "switch2" not in params or not params["switch2"]:
            raise ValueError("switch2 is required")
        elif "switch2_config" not in params or not params["switch2_config"]:
            raise ValueError("switch2_config is required")
        sn1 = params['switch1']
        sn2 = params['switch2']
        switch1 = inven_db.get_collection(Switch, filters={'sn': [sn1]})
        switch2 = inven_db.get_collection(Switch, filters={'sn': [sn2]})
        if not switch1 and switch2:
            raise ValueError('Can\'t found {}, {} switch'.format(sn1, sn2))
        elif not switch1:
            raise ValueError('Can\'t found {} switch'.format(sn1))
        elif not switch2:
            raise ValueError('Can\'t found {} switch'.format(sn2))
        switch1_config_str = get_verify_config_str(sn1, switch1[0].mgt_ip, params["switch1_config"])
        switch2_config_str = get_verify_config_str(sn2, switch2[0].mgt_ip, params["switch2_config"])
        # data['switch1_config_str'] = hide_config_sensitive_info(switch1_config_str)
        # data['switch2_config_str'] = hide_config_sensitive_info(switch2_config_str)
        diff_list = list(map(str.strip, list(difflib.Differ().compare(switch1_config_str.split('\n'), switch2_config_str.split('\n')))))
        # data['diff_full_context'] = hide_config_sensitive_info("\n".join(diff_list))
        # data['diff_only'] = hide_config_sensitive_info("\n".join(filter(lambda x: True if x and x[0] in ['+', '-', '?'] else False, diff_list)))
        diff_str = hide_config_sensitive_info("\n".join(filter(lambda x: True if x and x[0] in ['+', '-', '?'] else False, diff_list)))
    except ValueError as v:
        status, msg = 500, "ERROR:[%s]" % v
    except Exception as e:
        status, msg = 502, "ERROR:[%s]" % e
    finally:
        return jsonify({"status_code": status, "msg": msg, 'diff_str': diff_str})
