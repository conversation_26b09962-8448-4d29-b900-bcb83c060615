import logging
import re
import socket
import time
from contextlib import contextmanager

import paramiko

from server.constants import PICOS_V_IP, PICOS_V_USERNAME, PICOS_V_PASSWORD
from server import constants as C
from server.util import utils
from server.util import ssh_helper
from server import cfg

LOG = logging.getLogger(__name__)

LINUX_PROMPT = '.*@.*:.*\$'
PICOS_PROMPT = '.*@.*> '
PICOS_CLI_PROMPT = '.*@.*# '
linux_regex = re.compile(LINUX_PROMPT, re.I)
picos_regex = re.compile(PICOS_PROMPT, re.I)
picos_cli_regex = re.compile(PICOS_CLI_PROMPT, re.I)
replace_re = re.compile('.*@.*#')
special_re = re.compile('\s+\^\r\r\n')
more_re_list = [re.compile('--More-- \x1b\[J\x08\x08\x08\x08\x08\x08\x08\x08\x08\x08\x1b\[J'), re.compile('--More-- \x1b\[J\r'), re.compile('--More-- \r'), re.compile('--More--'), re.compile('\x1b\[J')]
end_re_list = [re.compile('\(END\) \x08\x08\x08\x08\x08\x08\x08\x08\x08\x08\x08\x08\x08\x08\x08\x08\r'), re.compile('\(END\) \r')]


bash_enable = False


def interactive_get_route_vrf_with_conn(ssh_session, address):
    cmd = 'ip route get %s oif mgmt-vrf' % address
    res, status_code = interactive_shell_linux_with_conn(ssh_session, cmd)
    if status_code == C.RMA_ACTIVE and address in res:
        return 'mgmt-vrf'

    cmd = 'ip route get %s' % address
    res, status_code = interactive_shell_linux_with_conn(ssh_session, cmd)
    if status_code == C.RMA_ACTIVE and address in res:
        return 'default'

    return 'default'


def interactive_push_file(dest_host, user, password, local_file, remote_file, retry_time=0, retry_interval=15, **kwargs):
    LOG.info('push file %s to remote host %s', local_file, remote_file)
    ssh = None
    try:
        invoke_shell, status, ssh = get_interactive_session(dest_host, username=user, password=password, **kwargs)

        # local_ip, local_port = invoke_shell.get_transport().sock.getsockname()
        local_ip = cfg.CONF.global_ip
        vrf = interactive_get_route_vrf_with_conn(invoke_shell, local_ip)
        cmd_prefix = 'sudo'
        if vrf != 'default':
            cmd_prefix = 'sudo /sbin/ip vrf exec %s' % vrf

        cmd = "%s curl -o %s -k -v https://%s/rma/file/%s" % (cmd_prefix, remote_file, local_ip, local_file)

        return interactive_shell_linux_with_conn(invoke_shell, cmd)
    finally:
        if ssh:
            ssh.close()


def interactive_push_file_with_conn(ssh_session, local_file, remote_file, retry_time=0, retry_interval=15,
                                    continue_at=False):
    LOG.info('push file %s to remote host %s', local_file, remote_file)
    local_ip = cfg.CONF.global_ip

    vrf = interactive_get_route_vrf_with_conn(ssh_session, local_ip)
    cmd_prefix = 'sudo'
    if vrf != 'default':
        cmd_prefix = 'sudo /sbin/ip vrf exec %s' % vrf

    continue_at_str = '-C -' if continue_at else ''
    cmd = "%s curl %s -o %s -k -v https://%s/rma/file/%s" % (cmd_prefix, continue_at_str, remote_file, local_ip, local_file)

    return interactive_shell_linux_with_conn(ssh_session, cmd)


def interactive_shell_linux_with_conn(ssh_session, cmd, error_fn=None):
    LOG.info('execute cmd %s', cmd)
    if bash_enable:
        ssh_session.sendall(('bash "%s"\n' % cmd).encode())
        res, status_code = _recv(ssh_session, prompt=PICOS_PROMPT, error_fn=error_fn)
    else:
        ssh_session.sendall(('%s\n' % cmd).encode())
        res, status_code = _recv(ssh_session, prompt=LINUX_PROMPT, error_fn=error_fn)
    if 'NOTICE TO USERS' in res:
        time.sleep(3)
        ssh_session.sendall('\n\n'.encode())
        res, status_code = _recv(ssh_session, prompt=PICOS_PROMPT, error_fn=error_fn)
    return _format_command_result(res), status_code


def execute_cli_with_conn_bak(ssh_session, cmd, action=''):
    LOG.info('execute cmd %s', cmd)
    cmd = cmd.replace('\n', ';')
    ssh_session.sendall(('/pica/bin/pica_sh -c "configure;%s"\n' % cmd).encode())
    res, status_code = _recv(ssh_session, prompt=LINUX_PROMPT)
    LOG.info('execute cmd %s, stdout %s', cmd, res)
    stdout = res
    re_mats = re.search('.*@.*#.*\r\n.*\r\n', res)
    if re_mats:
        _, end = re_mats.span()
        stdout = res[end:]

    stdout1 = ''
    status_code1 = status_code
    if action != '':
        ssh_session.sendall(('/pica/bin/pica_sh -c "configure;%s"\n' % action).encode())
        res1, status_code1 = _recv(ssh_session, prompt=LINUX_PROMPT)
        LOG.info('execute cmd %s, stdout %s', action, res1)
        stdout1 = res1
        re_mats1 = re.search('.*@.*#.*\r\n.*\r\n', res1)
        if re_mats1:
            _, end = re_mats1.span()
            stdout1 = res1[end:]
    return stdout + stdout1, status_code1


def interactive_shell_configure_with_conn(ssh_session, cmd, action='', error_fn=None):
    LOG.info('execute cmd %s', cmd)

    # execute command
    cmd_lines = cmd.split('\n')
    res = ''
    status_code = 0
    for cmd_line in cmd_lines:
        ssh_session.sendall(('%s\n' % cmd_line).encode())
        line_result, status_code = _recv(ssh_session, prompt=PICOS_CLI_PROMPT, error_fn=error_fn)
        if status_code == C.RMA_FAILED:
            return _format_cli_result(line_result), status_code
        res += line_result

    if action != '':
        ssh_session.sendall(('%s\n' % action).encode())
        action_result, status_code = _recv(ssh_session, prompt=PICOS_CLI_PROMPT, error_fn=error_fn)
        res += action_result
        if action == 'commit' and 'Commit OK' not in action_result:
            status_code = C.RMA_FAILED
            res = _format_cli_result(action_result)

    LOG.info('execute cmd %s, stdout %s', cmd, res)

    return res, status_code


def interactive_shell_cli_with_conn(ssh_session, cmd, error_fn=None):
    LOG.info('execute cmd %s', cmd)

    ssh_session.sendall(('%s\n' % cmd).encode())
    res, status_code = _recv(ssh_session, prompt=PICOS_PROMPT, error_fn=error_fn)
    if 'NOTICE TO USERS' in res:
        time.sleep(3)
        ssh_session.sendall('\n\n'.encode())
        res, status_code = _recv(ssh_session, prompt=PICOS_PROMPT, error_fn=error_fn)
    return _format_command_result(res), status_code


def _format_cli_result(result):
    return replace_re.sub('', special_re.sub(';', result)).strip().replace('\r\r\n', ';')


def get_interactive_session(*args, **kwargs):
    ssh = paramiko.SSHClient()
    ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())
    kwargs.setdefault('timeout', 60)
    kwargs.setdefault('allow_agent', False)
    kwargs.setdefault('look_for_keys', False)
    kwargs.setdefault('auth_timeout', 60)
    mode = kwargs.pop('mode', C.SSH_MODE.LINUX)
    try:
        ssh.connect(*args, **kwargs)
    except paramiko.ssh_exception.AuthenticationException as e:
        sw_host = args[0] if args else kwargs['hostname']
        LOG.error('can not connect to host %s, auth error %s', sw_host, str(e))
        ssh.close()
        return 'auth error', C.RMA_UN_REACHABLE, ssh
    except Exception as e:
        sw_host = args[0] if args else kwargs['hostname']
        LOG.error('can not connect to host %s, auth error %s', sw_host, str(e))
        ssh.close()
        return 'connect failed', C.RMA_UN_REACHABLE, ssh
    ssh.get_transport().set_keepalive(3)
    invoke_shell = ssh.invoke_shell()
    invoke_shell.settimeout(kwargs['timeout'])
    # _recv(invoke_shell, prompt=LINUX_PROMPT)
    res, status = _adapt_ssh_mode(invoke_shell, mode)
    return invoke_shell, status, ssh


def interactive_multi_shell_linux(cmds, *args, **kwargs):
    ssh = None
    try:
        invoke_shell, status, ssh = get_interactive_session(*args, **kwargs)
        if status != C.RMA_ACTIVE:
            return invoke_shell, status
        res = []
        for cmd in cmds:

            one_re, status_code = interactive_shell_linux_with_conn(invoke_shell, cmd)
            if status_code != C.RMA_ACTIVE:
                res.append(one_re)
                return res, status_code

            res.append(one_re)

        return res, C.RMA_ACTIVE
    finally:
        if ssh:
            ssh.close()


def interactive_shell_linux(cmd, *args, **kwargs):
    sw_host = args[0] if args else kwargs['hostname']
    LOG.info('%s execute cmd %s', sw_host, cmd)
    ssh = None
    error_fn = kwargs.pop('error_fn', None)
    try:
        invoke_shell, status, ssh = get_interactive_session(*args, **kwargs)
        if status != C.RMA_ACTIVE:
            return invoke_shell, status
        return interactive_shell_linux_with_conn(invoke_shell, cmd, error_fn=error_fn)
    finally:
        if ssh:
            ssh.close()


def interactive_shell_cli(cmd, *args, **kwargs):
    sw_host = args[0] if args else kwargs['hostname']
    LOG.info('%s execute cmd %s', sw_host, cmd)
    ssh = None
    error_fn = kwargs.pop('error_fn', None)
    try:
        invoke_shell, status, ssh = get_interactive_session(mode=C.SSH_MODE.CLI, *args, **kwargs)
        if status != C.RMA_ACTIVE:
            return invoke_shell, status
        return interactive_shell_cli_with_conn(invoke_shell, cmd, error_fn=error_fn)
    finally:
        if ssh:
            ssh.close()


def interactive_shell_configure(cmd, *args, **kwargs):
    sw_host = args[0] if args else kwargs['hostname']
    LOG.info('%s execute cmd %s', sw_host, cmd)
    action = kwargs.pop('action', '')
    ssh = None
    error_fn = kwargs.pop('error_fn', None)
    try:
        invoke_shell, status, ssh = get_interactive_session(mode=C.SSH_MODE.CONFIGURE, *args, **kwargs)
        if status != C.RMA_ACTIVE:
            return invoke_shell, status
        return interactive_shell_configure_with_conn(invoke_shell, cmd, action=action, error_fn=error_fn)
    finally:
        if ssh:
            ssh.close()


@contextmanager
def open_interactive_connection(*args, **kwargs):
    """
        implements with
        example: with open_interactive_connection('************', username='test', password='12345678') as client:
                        print client.execute(cmd)
                        print client.execute(cmd1)
                        print client.execute(cmd2)
                        print client.execute(cmd3)
    :param args: ('************')
    :param kwargs: {'username': a, 'password': pw}
    :return:
    """
    invoke_shell, status, ssh = get_interactive_session(*args, **kwargs)
    if status != C.RMA_ACTIVE:
        raise Exception(invoke_shell)

    class Client:
        def execute(self, cmd):
            return interactive_shell_linux_with_conn(invoke_shell, cmd)

    ssh_client = Client()

    try:
        yield ssh_client
    finally:
        if ssh:
            ssh.close()


@contextmanager
def open_interactive_configure_connection(*args, **kwargs):
    """
        implements with
        example: with open_interactive_connection('************', username='test', password='12345678') as client:
                        print client.execute(cmd)
                        print client.execute(cmd1)
                        print client.execute(cmd2)
                        print client.execute(cmd3)
    :param args: ('************')
    :param kwargs: {'username': a, 'password': pw}
    :return:
    """
    invoke_shell, status, ssh = get_interactive_session(mode=C.SSH_MODE.CONFIGURE, *args, **kwargs)
    if status != C.RMA_ACTIVE:
        raise Exception(invoke_shell)

    class Client:
        def execute(self, cmd, action='', error_fn=None):
            return interactive_shell_configure_with_conn(invoke_shell, cmd, action, error_fn)

    ssh_client = Client()

    try:
        yield ssh_client
    finally:
        if ssh:
            ssh.close()


def interactive_upgrade_with_conn(ssh_session, cmd):
    LOG.info('execute cmd %s', cmd)
    ssh_session.sendall(('%s\n' % cmd).encode())
    error_prompts = ['ERROR', 'Error', 'error']

    recv = ''
    flag = C.RMA_ACTIVE
    head_regex = re.compile('^' + LINUX_PROMPT, re.I)

    while True:
        try:
            if ssh_session.closed:
                break
            data = ssh_session.recv(256)
            print(data)
            recv += data.decode(errors="ignore")

            if head_regex and head_regex.search(recv[:120]):
                recv = head_regex.sub('', recv)
                head_regex = None
            if linux_regex and linux_regex.search(recv[-1200:]):
                break

        except socket.timeout:
            LOG.warn('upgrade timeout for msg')
            break
        except Exception as e:
            LOG.exception(e)
            flag = C.RMA_FAILED
            recv += str(e)
            break
    if any([error_prompt in recv for error_prompt in error_prompts]):
        flag = C.RMA_FAILED

    return _format_command_result(recv), flag


def _adapt_ssh_mode(ssh_shell, mode):
    recv = ''
    while True:
        try:
            if ssh_shell.closed:
                return recv,C.RMA_FAILED
            data = ssh_shell.recv(1024)
            recv += data.decode(errors="ignore")
            if mode == C.SSH_MODE.LINUX:
                if picos_regex.search(recv[-1200:]):
                    # switch is cli version, change to linux
                    ssh_shell.sendall('start shell sh\n'.encode())
                    res, status = _recv(ssh_shell, LINUX_PROMPT)
                    global bash_enable
                    if status == C.RMA_FAILED and 'timeout' in res:
                        # switch disable start shell sh
                        bash_enable = True
                        return res, C.RMA_ACTIVE
                    else:
                        bash_enable = False
                        return res, status
                if linux_regex.search(recv[-1200:]):
                    return recv, C.RMA_ACTIVE
            elif mode == C.SSH_MODE.CLI:
                if linux_regex.search(recv[-1200:]):
                    ssh_shell.sendall('cli\n'.encode())
                    return _recv(ssh_shell, PICOS_PROMPT)
                if picos_regex.search(recv[-1200:]):
                    return recv, C.RMA_ACTIVE
            elif mode == C.SSH_MODE.CONFIGURE:
                if linux_regex.search(recv[-1200:]):
                    ssh_shell.sendall('cli\n'.encode())
                    res, status = _recv(ssh_shell, PICOS_PROMPT)
                    ssh_shell.sendall('configure\n'.encode())
                    return _recv(ssh_shell, PICOS_CLI_PROMPT)
                if picos_regex.search(recv[-1200:]):
                    ssh_shell.sendall('configure\n'.encode())
                    return _recv(ssh_shell, PICOS_CLI_PROMPT)
        except Exception as e:
            LOG.exception(e)
            recv += str(e)
            return recv, C.RMA_FAILED


def _recv(ssh_shell, prompt=None, error_fn=None):
    recv = ''
    flag = C.RMA_ACTIVE
    if prompt:
        regex = re.compile(prompt, re.I)
        head_regex = re.compile('^' + prompt, re.I)
    while True:
        try:
            if ssh_shell.closed:
                LOG.error('SSH session broken')
                flag = C.RMA_FAILED
                return recv, flag
            data = ssh_shell.recv(1024)
            recv += data.decode(errors="ignore")
            if head_regex and head_regex.search(recv[:120]):
                recv = head_regex.sub('', recv)
                head_regex = None
            if regex and regex.search(recv[-1200:]):
                break
            if '--More--' in data.decode(errors="ignore"):
                ssh_shell.send(' '.encode())
        except Exception as e:
            LOG.exception(e)
            flag = C.RMA_FAILED
            recv += str(e)
            break
    for more_re in more_re_list:
        recv = more_re.sub('', recv)
    for end_re in end_re_list:
        recv = end_re.sub('', recv)
    recv = recv.replace("\x1b[?2004l\r", "")
    if not error_fn:
        if not ssh_helper.error_judge(recv):
            flag = C.RMA_FAILED
    else:
        if not error_fn(recv):
            flag = C.RMA_FAILED

    LOG.debug('execute cmd result %s, flag %s', recv, flag)
    return recv, flag


def _format_command_result(res):
    result_lines = remove_color_and_mess(res).split('\r\n')
    if len(result_lines) > 2:
        return '\n'.join(result_lines[1:-1])
    else:
        res = res.strip()
        if res.endswith('$') or res.endswith('>') or res.endswith('#'):
            return ''
        else:
            try:
                return re.split('\$|>|#', res)[1].strip()
            except:
                LOG.error('can not format result %s', res)
                return res


def close_session(channel):
    try:
        if channel:
            channel.close()
            transport = channel.transport
            if transport:
                transport.close()
    except:
        pass


def switch_picos_v_model(model):
    ssh = None
    try:
        invoke_shell, status, ssh = get_interactive_session(PICOS_V_IP, username=PICOS_V_USERNAME, password=PICOS_V_PASSWORD)
        if status != C.RMA_ACTIVE:
            return invoke_shell, status
        if interactive_shell_linux_with_conn(invoke_shell, 'sudo rm -f /pica/config/*;sudo rm -f /backup/pica/config/*;sudo /etc/reset-product.sh -p {}'.format(model), error_fn=None)[1] != C.RMA_ACTIVE:
            return 'Failed'
        interactive_shell_linux_with_conn(invoke_shell, 'sudo reboot', error_fn=None)
        max_retry = 50
        retry = 0
        time.sleep(20)
        while True:
            try:
                retry += 1
                invoke_shell, status, ssh = get_interactive_session(PICOS_V_IP, username=PICOS_V_USERNAME, password=PICOS_V_PASSWORD)
                if status != C.RMA_UN_REACHABLE:
                    break
                else:
                    time.sleep(3)
            except:
                if retry > max_retry:
                    return 'TimeOut'
                time.sleep(3)
        version = interactive_shell_linux_with_conn(invoke_shell, 'version', error_fn=None)[0]
        if model.upper() in version.upper():
            return 'OK'
        else:
            return 'Failed'
    except:
        return 'Failed'
    finally:
        if ssh:
            ssh.close()


# check PICOS-V flag skip add license step
def is_picos_v_need_to_skip_add_license(ip, user=None, pw=None, auth_timeout=60, timeout=60):
    if not user or not pw:
        user, pw = utils.get_switch_default_user()
    cmdline_info, cmdline_code = interactive_shell_linux('cat /proc/cmdline | grep platform=x86v', ip, username=user, password=pw, auth_timeout=auth_timeout, timeout=timeout)
    return True if cmdline_info and cmdline_code == C.RMA_ACTIVE else False


def is_black_box_model_need_to_skip_add_license(ip, user, pw):
    cmdline_info, cmdline_code = interactive_shell_linux('cat /sys/class/swmon/ctrl/product_id', ip, username=user, password=pw)
    return True if cmdline_info in C.BLACK_BOX_MODEL_LIST and cmdline_code == C.RMA_ACTIVE else False


def remove_color_and_mess(input_string):
    clean_string = re.sub(r'\x1b\[[0-9;]*[mK]', '', input_string)
    return clean_string


if __name__ == '__main__':
    host = '***********'
    name = 'admin'
    password = 'pica8'
    # with open_interactive_connection('************', username='admin', password='pica8') as client:
    #     print client.execute('/pica/bin/system/pica_switch_mac')

    # configs = "/pica/bin/system/fan_status -s"
    # res, status = execute('ls', host, username=name, password=password)
    # print (res, status)
    # and head_regex and not head_regex.search(recv[:100])
    # res, status = interactive_shell_linux('cd /home/<USER>', host, username=name, password=password)
    # print res
    # print status

    ssh_session, status, _ = get_interactive_session(host, username=name, password=password, timeout=60)
    # res, status = interactive_upgrade_with_conn(ssh_session,
    #                                                         'sudo upgrade2 /cftmp/%s' % 'picos-*********-n3048-2fae88a-automation.tar.gz')
    # res, status = interactive_upgrade_with_conn(ssh_session,
    #                                             'sudo upgrade2 /cftmp/%s' % 'picos-*********-n3048-57e62d6-automation.tar.gz')

    # print(interactive_shell_linux_with_conn(ssh_session, '''sudo find /home -maxdepth 2 -type f | grep -E '*.bin$|*.tar.gz$' '''))
    print(interactive_shell_linux_with_conn(ssh_session, 'ls -al'))
    # print interactive_shell_linux_with_conn(ssh_session, 'df -lh | awk \'/\/$/ {print $4 }\' | head -n 1')
