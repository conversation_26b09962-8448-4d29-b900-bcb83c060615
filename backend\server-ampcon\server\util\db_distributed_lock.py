import os
import threading
import time
import socket
from server.db.db_common import DBCommon
import traceback
import logging

LOG = logging.getLogger(__name__)


class DistributedLock(DBCommon):

    def __init__(self, lock_key, lock_time=10):
        self.lock_key = lock_key
        self.lock_time = lock_time
        self.is_locked = False
        self.process_id = os.getpid()
        self.thread_id = threading.get_ident()
        self.host_name = socket.gethostname()

    def acquire(self, timeout=3):
        """
                获取锁，可选超时时间
                """
        session = self.get_session()
        end_time = time.time() + timeout
        while time.time() < end_time:
            try:
                LOG.info(str(self.lock_key))
                LOG.info(str(self.process_id))
                LOG.info(str(self.thread_id))
                LOG.info(str(self.host_name))
                # 尝试插入锁记录
                session.execute(
                    "INSERT INTO distributed_lock (lock_key, lock_expiry, process_id, thread_id, host_name"
                    + ", acquired_time) VALUES ('" + str(self.lock_key) + "', NOW() + INTERVAL " + str(self.lock_time)
                    + " SECOND, '" + str(self.process_id) + "', '" + str(self.thread_id) + "', '"
                    + str(self.host_name) + "', NOW())")
                self.is_locked = True
                print("Acquired lock success")
                return True
            except Exception as e:
                # 如果遇到唯一性约束错误，说明锁已被其他客户端持有
                # traceback.print_exc()
                time.sleep(0.5)  # 等待后重试
        print("Failed to acquire lock within the specified timeout.")
        return False

    def release(self):
        """
        释放锁
        """
        if self.is_locked:
            self.get_session().execute("DELETE FROM distributed_lock WHERE lock_key = '" + str(self.lock_key) + "'")
            print("Released lock.")
            self.is_locked = False
        else:
            print("No lock held to release.")

    def release_all(self):
        """
        释放所有过期锁
        """
        self.get_session().execute("DELETE FROM distributed_lock WHERE lock_expiry + INTERVAL 1 SECOND < NOW()")
        print("Released all expired lock.")
