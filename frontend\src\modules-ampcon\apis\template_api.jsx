import {request} from "@/utils/common/request";

const baseUrl = "/ampcon/template";

export function queryAllGlobalConfigs() {
    return request({
        url: `${baseUrl}/platform/global/config`,
        method: "GET"
    });
}

export function queryAllSiteTemplates() {
    return request({
        url: `${baseUrl}/platform/site/templates`,
        method: "GET"
    });
}

export function fetchSwitchInfo(page, pageSize, filterFields = [], sortFields = [], searchFields = {}) {
    return request({
        url: `${baseUrl}/get_avaliable_switch`,
        method: "POST",
        data: {
            filterFields,
            sortFields,
            searchFields,
            page,
            pageSize
        }
    });
}

export function fetchSnapshotListAPI(sn) {
    return request({
        url: `${baseUrl}/get_snapshot_list`,
        method: "POST",
        data: {sn}
    });
}

export function fetchSnapshotConfigAPI(sn, snapshotTime) {
    return request({
        url: `${baseUrl}/get_snapshot_config`,
        method: "POST",
        data: {sn, snapshotTime}
    });
}

export function fetchRunningConfigAPI(sn, format) {
    return request({
        url: `${baseUrl}/get_running_config`,
        method: "POST",
        data: {sn, format}
    });
}

export function querySiteConfigParams(siteTemplateNameList) {
    return request({
        url: `${baseUrl}/config/site_template_params`,
        method: "POST",
        data: {
            siteTemplateNameList
        }
    });
}

export function querySiteConfigSaveList(file) {
    const formData = new FormData();
    formData.append("file", file);
    return request({
        url: `${baseUrl}/config/site/save_list`,
        method: "POST",
        data: formData,
        headers: {
            "Content-Type": "multipart/form-data"
        }
    });
}

export function queryGenerateMultipleConfig(data) {
    return request({
        url: `${baseUrl}/generate_multiple_config`,
        data,
        method: "POST"
    });
}

export function saveSiteConfig(data) {
    return request({
        url: `${baseUrl}/config/site/save`,
        data,
        method: "POST"
    });
}

export function getTemplateInfo(name) {
    return request({
        url: `${baseUrl}/config/edit_template/${name}`,
        method: "GET"
    });
}

export function saveEntireTemplateInfo(name, templateContent, variableContent) {
    return request({
        url: `${baseUrl}/config/update_template`,
        data: {name, templateContent, variableContent},
        method: "POST"
    });
}

export function fetchTemplateListData(
    isShowPreBuiltTemplate,
    page,
    pageSize,
    filterFields = [],
    sortFields = [],
    searchFields = {}
) {
    sortFields =
        sortFields.length === 0
            ? [
                  {
                      field: "create_time",
                      order: "asc"
                  }
              ]
            : sortFields;
    return request({
        url: `${baseUrl}/template/list`,
        method: "POST",
        data: {
            isShowPreBuiltTemplate,
            filterFields,
            sortFields,
            searchFields,
            page,
            pageSize
        }
    });
}

export function uploadTemplateBySingleFile(templateName, templateDescription, templateFile) {
    const formData = new FormData();
    formData.append("templateName", templateName);
    formData.append("templateDescription", templateDescription);
    formData.append("templateFile", templateFile);
    return request({
        url: `${baseUrl}/upload`,
        method: "POST",
        data: formData,
        headers: {
            "Content-Type": "multipart/form-data"
        }
    });
}

export function setGoldenSnapshot(sn, snapshotTime) {
    return request({
        url: `${baseUrl}/set_golden_snapshot`,
        method: "POST",
        data: {
            sn,
            snapshotTime
        }
    });
}

export function updateSnapshotDescription(sn, snapshotTime, description, tag) {
    return request({
        url: `${baseUrl}/update_snapshot_desc_tag`,
        method: "POST",
        data: {
            sn,
            snapshotTime,
            description,
            tag
        }
    });
}

export function delSnapshot(sn, snapshotTime) {
    return request({
        url: `${baseUrl}/del_snapshot`,
        method: "POST",
        data: {
            sn,
            snapshotTime
        }
    });
}

export function updateTag(record_id, record_type, tag_content) {
    return request({
        url: `${baseUrl}/update_tag`,
        method: "POST",
        data: {
            record_id,
            record_type,
            tag_content
        }
    });
}

export function getPlatformModel() {
    return request({
        url: `${baseUrl}/platform_model`,
        method: "GET"
    });
}

export function getPlatformVersion() {
    return request({
        url: `${baseUrl}/cli/platform_version`,
        method: "GET"
    });
}

export function getCliTrees(model, version) {
    return request({
        url: `${baseUrl}/platform/${model}/${version}/cli/data`,
        method: "GET"
    });
}

export function createTemplate(name, description, content, action) {
    return request({
        url: `${baseUrl}/create_template`,
        method: "POST",
        data: {
            name,
            description,
            content,
            action
        }
    });
}

export function updateCliTree() {
    return request({
        url: `${baseUrl}/cli/update`,
        method: "POST",
        data: {}
    });
}

export function exportTemplate(name) {
    window.location.href = `${baseUrl}/export/${name}`;
}

export function updateInternalTemplate() {
    return request({
        url: `${baseUrl}/update_internal_template`,
        method: "POST"
    });
}

export function removeTemplate(templateName) {
    return request({
        url: `${baseUrl}/template/remove`,
        method: "POST",
        data: {templateName}
    });
}

export function copyTemplate(originTemplateName, newTemplateName, newTemplateDescription) {
    return request({
        url: `${baseUrl}/copy`,
        method: "POST",
        data: {originTemplateName, newTemplateName, newTemplateDescription}
    });
}

export function generateConfigTemplateVerify(sn, globalConfigName, siteTemplateName, content) {
    return request({
        url: `${baseUrl}/generate_config_template_verify`,
        method: "POST",
        data: {
            sn,
            globalConfigName,
            siteTemplateName,
            content
        }
    });
}
