import {
    createColumnConfig,
    AmpConCustomTable,
    TableFilterDropdown,
    createColumnWithoutFilter,
    AmpConCustomModalForm
} from "@/modules-ampcon/components/custom_table";
import {Button, Checkbox, Divider, Space, Flex, Tree, Tabs, Modal, Form, Input, message} from "antd";
import Typography from "antd/es/typography";
import {useState, useEffect, memo, useRef, forwardRef, useImperativeHandle} from "react";
import {useForm} from "antd/es/form/Form";
import {
    fetchGroups,
    fetchGroupInfo,
    loadGroup,
    createGroup,
    deleteGroup,
    editGroup
} from "@/modules-ampcon/apis/lifecycle_api";
import {fetchUserGroupInfo} from "@/modules-ampcon/apis/user_api";
import Icon from "@ant-design/icons/lib/components/Icon";
import {plusAddSvg, plusAddDisableSvg, deleteSvg, onlineSvg, offlineSvg, exclamationSvg} from "@/utils/common/iconSvg";
import {confirmModalAction} from "@/modules-ampcon/components/custom_modal";
import groupManagerStyle from "./group_manager.module.scss";

const {TextArea} = Input;

const roleMapping = {
    readonly: "Readonly",
    admin: "Operator",
    superadmin: "Admin",
    superuser: "SuperAdmin"
};

const GroupButton = ({refreshGroup, deleteGroup}) => {
    const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
    return (
        <div>
            <Space size="middle">
                <Button
                    type="primary"
                    block
                    icon={<Icon component={plusAddSvg} style={{height: 20}} />}
                    onClick={() => {
                        setIsCreateModalOpen(true);
                    }}
                >
                    Create
                </Button>
                <Button
                    htmlType="button"
                    block
                    icon={<Icon component={deleteSvg} style={{height: 20}} />}
                    onClick={() => confirmModalAction("Are you sure want to delete?", () => deleteGroup())}
                >
                    Delete
                </Button>
            </Space>
            <CreateGroupModal
                isModalOpen={isCreateModalOpen}
                onCancel={() => {
                    setIsCreateModalOpen(false);
                    refreshGroup();
                }}
            />
        </div>
    );
};

const GroupManagement = () => {
    const [selectedGroup, setSelectedGroup] = useState("All");

    return (
        <div style={{display: "flex", flex: 1}}>
            <Flex
                gap="large"
                vertical
                style={{minWidth: "280px", width: "18%", marginRight: "15px"}}
                className={groupManagerStyle.tile}
            >
                <GroupList onSelectGroup={setSelectedGroup} />
            </Flex>
            <GroupManageTabs groupName={selectedGroup} destroyInactiveTabPane />
        </div>
    );
};

const GroupList = ({onSelectGroup}) => {
    const [selectedGroup, setSelectedGroup] = useState("");
    const [selectedKeys, setSelectedKeys] = useState([0]);
    const [treeData, setTreeData] = useState([
        {
            title: "All",
            key: 0
        }
    ]);

    const listGroupTree = async () => {
        const response = await fetchGroups();
        const treeChild = response.data.map((item, index) => {
            const updatedNode = {title: item, key: index + 1};
            return updatedNode;
        });
        const treeTempData = [
            {
                title: "All",
                key: 0,
                children: treeChild
            }
        ];
        setTreeData(treeTempData);
    };

    const handleDeleteGroup = async () => {
        if (selectedGroup && selectedGroup !== "All") {
            const response = await deleteGroup(selectedGroup);
            if (response.status !== 200) {
                message.error(response.info);
            } else {
                message.success(response.info);
                listGroupTree().then();
            }
        } else {
            message.error("Please select a group");
        }
        setSelectedGroup("All");
        onSelectGroup("All");
        setSelectedKeys([0]);
    };

    useEffect(() => {
        listGroupTree().then();
    }, []);

    return (
        <Flex vertical>
            <Flex gap="middle" style={{marginBottom: "20px"}}>
                <GroupButton refreshGroup={listGroupTree} deleteGroup={handleDeleteGroup} />
            </Flex>
            <Tree
                showLine
                treeData={treeData}
                onSelect={(selectedKeys, info) => {
                    onSelectGroup(info.node.title);
                    setSelectedGroup(info.node.title);
                    setSelectedKeys(selectedKeys);
                }}
                defaultExpandedKeys={[0]}
                selectedKeys={selectedKeys}
                rootStyle={{fontSize: "16px"}}
            />
        </Flex>
    );
};

const GroupManageTabs = ({groupName}) => {
    const isInitialMount = useRef(true);
    const [groupInfo, setGroupInfo] = useState({});
    const items = [
        {
            key: "switchView",
            label: "Switch View",
            children: (
                <GroupManagementTable
                    groupName={groupName}
                    groupInfo={groupInfo}
                    refreshGroupInfo={() => {
                        fetchData().then(() => {});
                    }}
                />
            )
        },
        {
            key: "userView",
            label: "User View",
            children: <GroupManagementUserTable groupName={groupName} groupInfo={groupInfo} />
        }
    ];

    const fetchData = async () => {
        if (isInitialMount.current) {
            isInitialMount.current = false;
            return;
        }
        if (groupName !== "All") {
            const response = await fetchGroupInfo(groupName);
            setGroupInfo({
                action: response.data.action,
                audit: response.data.audit,
                retrieve_config: response.data.retrieve_config,
                upgrading: response.data.upgrading,
                selectAll:
                    response.data.action &&
                    response.data.audit &&
                    response.data.retrieve_config &&
                    response.data.upgrading
            });
        } else {
            setGroupInfo({});
        }
    };

    useEffect(() => {
        fetchData().then(() => {});
    }, [groupName]);

    return <Tabs className="group-management-tabs" items={items} />;
};

const GroupManagementTable = memo(({groupName, groupInfo, refreshGroupInfo}) => {
    const tableRef = useRef(null);
    const modalRef = useRef(null);
    const [isAddModalOpen, setIsAddModalOpen] = useState(false);

    const delSwitch = async record => {
        const response = await editGroup(groupInfo, groupName, [record.sn], []);
        if (response.status !== 200) {
            message.error(response.info);
        } else {
            message.success(response.info);
            tableRef.current.refreshTable();
            modalRef.current.refreshSelectedRows();
        }
    };

    const moreColumn = [
        {
            title: "Operation",
            fixed: "right",
            render: (_, record) => {
                return (
                    <div>
                        <Space size="large" className="actionLink">
                            <a
                                onClick={() =>
                                    confirmModalAction("Are you sure want to delete it?", () => delSwitch(record))
                                }
                            >
                                Delete
                            </a>
                        </Space>
                    </div>
                );
            }
        }
    ];

    const [matchFieldsList, searchFieldsList, columns] = InitSwitchTableParams({
        moreColumn: groupName && groupName !== "All" ? moreColumn : []
    });

    return (
        <Flex gap={16} vertical>
            <Space>
                <Typography aria-level={2}>Group Class:</Typography>
                <Checkbox checked={groupInfo.audit} disabled={!groupInfo.audit} />
                <p>License Audit</p>
                <Checkbox checked={groupInfo.action} disabled={!groupInfo.action} />
                <p>License Action</p>
                <Checkbox checked={groupInfo.upgrading} disabled={!groupInfo.upgrading} />
                <p>Upgrading</p>
                <Checkbox checked={groupInfo.retrieve_config} disabled={!groupInfo.retrieve_config} />
                <p>Retrieve Config</p>
            </Space>
            <AmpConCustomTable
                ref={tableRef}
                columns={columns}
                searchFieldsList={searchFieldsList}
                matchFieldsList={matchFieldsList}
                fetchAPIInfo={loadGroup}
                fetchAPIParams={[groupName]}
                extraButton={
                    <div>
                        <Button
                            type="primary"
                            icon={
                                groupName && groupName !== "All" ? (
                                    <Icon component={plusAddSvg} style={{height: 20}} />
                                ) : (
                                    <Icon component={plusAddDisableSvg} style={{height: 20}} />
                                )
                            }
                            onClick={() => {
                                setIsAddModalOpen(true);
                            }}
                            disabled={!(groupName && groupName !== "All")}
                        >
                            Edit Group
                        </Button>
                        <AddSwitchModal
                            ref={modalRef}
                            isModalOpen={isAddModalOpen}
                            groupName={groupName}
                            groupInfo={groupInfo}
                            onCancel={() => {
                                setIsAddModalOpen(false);
                                tableRef.current.refreshTable();
                                modalRef.current.refreshSelectedRows();
                                refreshGroupInfo();
                            }}
                        />
                    </div>
                }
            />
        </Flex>
    );
});

const GroupManagementUserTable = memo(({groupName, groupInfo}) => {
    const matchFieldsList = [
        {name: "name", matchMode: "fuzzy"},
        {name: "type", matchMode: "fuzzy"}
    ];

    const searchFieldsList = ["name"];

    const columns = [
        createColumnConfig("Group Username", "name", TableFilterDropdown),
        {
            ...createColumnWithoutFilter("Role", "type"),
            render: (text, record) => <Space>{roleMapping[record.type]}</Space>
        },
        {title: "Added On", dataIndex: "add_on"},
        {title: "GroupName", dataIndex: "group_name"}
    ];

    return (
        <Flex gap={16} vertical>
            <Space>
                <Typography aria-level={2}>Group Class:</Typography>
                <Checkbox checked={groupInfo.audit} disabled={!groupInfo.audit} />
                <p>License Audit</p>
                <Checkbox checked={groupInfo.action} disabled={!groupInfo.action} />
                <p>License Action</p>
                <Checkbox checked={groupInfo.upgrading} disabled={!groupInfo.upgrading} />
                <p>Upgrading</p>
                <Checkbox checked={groupInfo.retrieve_config} disabled={!groupInfo.retrieve_config} />
                <p>Retrieve Config</p>
            </Space>
            <AmpConCustomTable
                columns={columns}
                searchFieldsList={searchFieldsList}
                matchFieldsList={matchFieldsList}
                fetchAPIInfo={fetchUserGroupInfo}
                fetchAPIParams={[groupName]}
            />
        </Flex>
    );
});

const CreateGroupModal = ({isModalOpen, onCancel}) => {
    const [form] = useForm();

    const formItems = () => {
        return (
            <>
                <Form.Item
                    name="groupName"
                    label="Group Name"
                    rules={[
                        {required: true, message: "Please input your group name!"},
                        {max: 32, message: "Enter a maximum of 32 characters"},
                        {
                            validator: (_, value) => {
                                if (value === "All") {
                                    return Promise.reject(new Error("Please input a valid group name!"));
                                }
                                if (value.trim() !== value) {
                                    return Promise.reject(
                                        new Error("Group name should not have leading or trailing spaces.")
                                    );
                                }
                                return Promise.resolve();
                            }
                        }
                    ]}
                >
                    <Input placeholder="Group Name" style={{width: "280px"}} />
                </Form.Item>
                <Form.Item
                    name="groupClass"
                    label="Group Class"
                    rules={[{required: true, message: "Please choose group class!"}]}
                >
                    <GroupClassCheckbox />
                </Form.Item>
                <Form.Item name="description" label="Description">
                    <TextArea rows={5} style={{width: "280px"}} />
                </Form.Item>
            </>
        );
    };

    const onSubmit = async values => {
        const response = await createGroup(values.groupClass, values.groupName, values.description);
        if (response.status !== 200) {
            message.error(response.info);
        } else {
            message.success(response.info);
            onCancelModal();
        }
    };

    const onCancelModal = () => {
        form.resetFields();
        onCancel();
    };

    return (
        <AmpConCustomModalForm
            title="Create Group"
            isModalOpen={isModalOpen}
            formInstance={form}
            layoutProps={{
                labelCol: {
                    span: 6
                }
            }}
            CustomFormItems={formItems}
            onCancel={onCancelModal}
            onSubmit={onSubmit}
            modalClass="ampcon-middle-modal"
        />
    );
};

const AddSwitchModal = forwardRef(({isModalOpen, groupName, groupInfo, onCancel, onChange}, ref) => {
    const tableRef = useRef(null);
    const isInitialMount = useRef(true);
    const [selectedRowKeys, selectSelectedRowKeys] = useState([]);
    const [selectedRows, selectSelectedRows] = useState([]);
    const [matchFieldsList, searchFieldsList, columns] = InitSwitchTableParams({});
    const rowSelection = {
        selectedRowKeys,
        selectedRows,
        fixed: true
    };

    const fetchData = async () => {
        if (isInitialMount.current) {
            isInitialMount.current = false;
            return;
        }
        const response = await loadGroup(groupName, 1, 10);
        const keys = response.data.map(item => {
            return item.id;
        });
        selectSelectedRowKeys(keys);
        selectSelectedRows(response.data);
    };

    function compareArrays(base, delta) {
        const added = delta.filter(item => !base.includes(item));
        const removed = base.filter(item => !delta.includes(item));
        return {added, removed};
    }

    useImperativeHandle(ref, () => ({
        refreshSelectedRows() {
            fetchData().then();
        }
    }));

    useEffect(() => {
        fetchData().then();
    }, [groupName]);

    const handleOk = async () => {
        if (!checkedValues.length) {
            message.error("Please select at least one group class");
            // onCancel();
            return;
        }
        const actionList = checkedValues;
        const compare = compareArrays(selectedRows, tableRef.current.getSelectedRow().tableSelectedRows);
        const delSwitch = compare.removed.map(item => item.sn);
        const addSwitch = compare.added.map(item => item.sn);
        const response = await editGroup(actionList, groupName, delSwitch, addSwitch);
        if (response.status !== 200) {
            message.error(response.info);
        } else {
            message.success(response.info);
            tableRef.current.refreshTable();
            onCancel();
        }
    };

    const checkboxOptions = [
        {label: <span style={{lineHeight: "32px"}}>Select All</span>, value: "selectAll"},
        {label: <span style={{lineHeight: "32px"}}>License Audit</span>, value: "audit"},
        {label: <span style={{lineHeight: "32px"}}>License Action</span>, value: "action"},
        {label: <span style={{lineHeight: "32px"}}>Upgrading</span>, value: "upgrading"},
        {label: <span style={{lineHeight: "32px"}}>Retrieve Config</span>, value: "retrieve_config"}
    ];
    const [checkedValues, setCheckedValues] = useState([]);

    const handleCheckboxChange = value => {
        let tempCheckValues = [];
        if (value.includes("selectAll") && !checkedValues.includes("selectAll")) {
            // 勾选select all
            tempCheckValues = checkboxOptions.map(option => option.value);
        } else if (!value.includes("selectAll") && checkedValues.includes("selectAll")) {
            // 取消select all
            tempCheckValues = [];
        } else if (!value.includes("selectAll") && value.length === 4) {
            tempCheckValues = checkboxOptions.map(option => option.value);
        } else {
            tempCheckValues = value.filter(value => value !== "selectAll");
        }
        setCheckedValues(tempCheckValues);
        if (typeof onChange === "function") {
            onChange(tempCheckValues);
        }
    };

    useEffect(() => {
        if (groupInfo) {
            const initialValues = checkboxOptions.filter(option => groupInfo[option.value]).map(option => option.value);
            setCheckedValues(initialValues);
        }
    }, [groupInfo]);

    return (
        <Modal
            title={
                <div>
                    Edit Group
                    <Divider style={{marginTop: 8, marginBottom: 0}} />
                </div>
            }
            open={isModalOpen}
            onOk={handleOk}
            onCancel={onCancel}
            destroyOnClose
            className="ampcon-max-modal"
            footer={[
                <Divider style={{marginTop: 0, marginBottom: 20}} />,
                <Button key="cancel" onClick={onCancel}>
                    Cancel
                </Button>,
                <Button key="ok" type="primary" onClick={handleOk}>
                    ok
                </Button>
            ]}
        >
            <AmpConCustomTable
                rowSelection={rowSelection}
                extraButton={
                    <Checkbox.Group options={checkboxOptions} value={checkedValues} onChange={handleCheckboxChange} />
                }
                ref={tableRef}
                columns={columns}
                searchFieldsList={searchFieldsList}
                matchFieldsList={matchFieldsList}
                fetchAPIInfo={loadGroup}
                fetchAPIParams={["All"]}
            />
        </Modal>
    );
});

const GroupClassCheckbox = forwardRef(({onChange, groupInfo}, ref) => {
    const checkboxOptions = [
        {label: <span style={{lineHeight: "32px"}}>Select All</span>, value: "selectAll"},
        {label: <span style={{lineHeight: "32px"}}>License Audit</span>, value: "audit"},
        {label: <span style={{lineHeight: "32px"}}>License Action</span>, value: "action"},
        {label: <span style={{lineHeight: "32px"}}>Upgrading</span>, value: "upgrading"},
        {label: <span style={{lineHeight: "32px"}}>Retrieve Config</span>, value: "retrieve_config"}
    ];
    const [checkedValues, setCheckedValues] = useState([]);

    useImperativeHandle(ref, () => ({
        getCheckedValues: () => {
            return checkedValues;
        }
    }));

    const handleCheckboxChange = value => {
        let tempCheckValues = [];
        if (value.includes("selectAll") && !checkedValues.includes("selectAll")) {
            // 勾选select all
            tempCheckValues = checkboxOptions.map(option => option.value);
        } else if (!value.includes("selectAll") && checkedValues.includes("selectAll")) {
            // 取消select all
            tempCheckValues = [];
        } else if (!value.includes("selectAll") && value.length === 4) {
            tempCheckValues = checkboxOptions.map(option => option.value);
        } else {
            tempCheckValues = value.filter(value => value !== "selectAll");
        }
        setCheckedValues(tempCheckValues);
        if (typeof onChange === "function") {
            onChange(tempCheckValues);
        }
    };

    useEffect(() => {
        if (groupInfo) {
            const initialValues = checkboxOptions.filter(option => groupInfo[option.value]).map(option => option.value);
            setCheckedValues(initialValues);
        }
    }, [groupInfo]);

    const renderCheckbox = option => (
        <label
            key={option.value}
            htmlFor={`checkbox-${option.value}`}
            className="group-checkbox-label"
            style={{display: "block", textAlign: "left"}}
        >
            <Checkbox
                id={`checkbox-${option.value}`}
                value={option.value}
                checked={checkedValues.includes(option.value)}
                onChange={e =>
                    handleCheckboxChange(
                        e.target.checked
                            ? [...checkedValues, option.value]
                            : checkedValues.filter(v => v !== option.value)
                    )
                }
                className="group-checkbox"
            >
                {option.label}
            </Checkbox>
        </label>
    );

    return (
        <div
            className="group-checkbox-container"
            style={{
                display: "flex",
                flexDirection: "column",
                alignItems: "flex-start"
            }}
        >
            <div
                className="group-checkbox-row"
                style={{display: "flex", alignItems: "flex-start", marginBottom: "5px"}}
            >
                {renderCheckbox(
                    checkboxOptions.find(o => o.value === "selectAll"),
                    0
                )}
            </div>
            <div
                className="group-checkbox-row"
                style={{display: "flex", flexDirection: "row", alignItems: "flex-start", marginBottom: "5px"}}
            >
                {checkboxOptions.slice(1, 3).map((option, index) => (
                    <div key={option.value} className="group-checkbox-item">
                        {" "}
                        {renderCheckbox(option, index + 1)}
                    </div>
                ))}
            </div>
            <div
                className="group-checkbox-row"
                style={{display: "flex", flexDirection: "row", alignItems: "flex-start"}}
            >
                {checkboxOptions.slice(3).map((option, index) => (
                    <div
                        key={option.value}
                        className="group-checkbox-item"
                        style={{marginRight: index === checkboxOptions.length - 1 ? 0 : "18px"}}
                    >
                        {renderCheckbox(option, index + 3)}
                    </div>
                ))}
            </div>
        </div>
    );
});

const InitSwitchTableParams = ({moreColumn = []}) => {
    const matchFieldsList = [
        {name: "host_name", matchMode: "fuzzy"},
        {name: "mgt_ip", matchMode: "fuzzy"},
        {name: "platform_model", matchMode: "fuzzy"},
        {name: "address", matchMode: "fuzzy"},
        {name: "version", matchMode: "fuzzy"},
        {name: "license_status", matchMode: "fuzzy"}
    ];

    const searchFieldsList = ["sn", "host_name", "mgt_ip", "platform_model"];

    const columns = [
        {...createColumnWithoutFilter("Switch SN", "sn")},
        {
            ...createColumnConfig("IP address", "mgt_ip", TableFilterDropdown),
            render: (_, record) => {
                let iconComponent;
                if (record.reachable_status === 0) {
                    iconComponent = onlineSvg;
                } else if (record.reachable_status === 1) {
                    iconComponent = offlineSvg;
                } else {
                    iconComponent = exclamationSvg;
                }

                return (
                    <Space>
                        <Icon component={iconComponent} />
                        {record.mgt_ip}
                    </Space>
                );
            }
        },
        createColumnConfig("Switch Name", "host_name", TableFilterDropdown),
        createColumnConfig("Deployed Location", "address", TableFilterDropdown),
        createColumnConfig("Version/Revision", "version", TableFilterDropdown),
        createColumnWithoutFilter("License Expiry", "license_expired"),
        createColumnConfig("License Status", "license_status", TableFilterDropdown),
        createColumnConfig("Platform Model", "platform_model", TableFilterDropdown),
        ...(moreColumn || [])
    ];

    const switchTableParams = [matchFieldsList, searchFieldsList, columns];

    return switchTableParams;
};

export default GroupManagement;
