export const rebootIcon = () => (
    <svg xmlns="http://www.w3.org/2000/svg" fill="none" version="1.1" width="14" height="14" viewBox="0 0 15 15">
        <g>
            <g />
            <g clipPath="url(#master_svg0_1_4509)">
                <g>
                    <path
                        d="M8.8458984375,1.8462890625C8.8458984375,1.4873040625,8.5548884375,1.1962890625,8.1958984375,1.1962890625C7.8369134375,1.1962890625,7.545898854733,1.4873040625,7.5458984375,1.8462890625L7.5458984375,7.5462890625C7.545898854733,7.9052690625,7.8369134375,8.1962890625,8.1958984375,8.1962890625C8.5548884375,8.1962890625,8.8458984375,7.9052690625,8.8458984375,7.5462890625L8.8458984375,1.8462890625Z"
                        fill="#212519"
                        fillOpacity="1"
                    />
                    <path
                        d="M8.7262284375,8.0766190625Q8.9458984375,7.8569490625,8.9458984375,7.5462890625L8.9458984375,1.8462890625Q8.9458984375,1.5356290625,8.7262284375,1.3159590625Q8.5065594375,1.0962890625,8.1958984375,1.0962890625Q7.8852384375,1.0962890625,7.6655684375,1.3159590625Q7.4458988375,1.5356290625,7.4458985375,1.8462890625L7.4458984375,7.5462890625Q7.4458988375,7.8569490625,7.6655684375,8.0766190625Q7.8852384375,8.2962890625,8.1958984375,8.2962890625Q8.5065594375,8.2962890625,8.7262284375,8.0766190625ZM8.7458984375,1.8462890625L8.7458984375,7.5462890625Q8.7458984375,7.7741090625,8.5848084375,7.9351990625Q8.4237154375,8.0962890625,8.1958984375,8.0962890625Q7.9680814375,8.0962890625,7.8069904375,7.9351990625Q7.6458984375,7.7741090625,7.6458984375,7.5462890625L7.6458984375,1.8462890625Q7.6458984375,1.6184720625,7.8069904375,1.4573800625Q7.9680814375,1.2962890625,8.1958984375,1.2962889625Q8.4237164375,1.2962889625,8.5848084375,1.4573800625Q8.7458984375,1.6184710625,8.7458984375,1.8462890625Z"
                        fillRule="evenodd"
                        fill="#212519"
                        fillOpacity="1"
                    />
                </g>
                <g>
                    <path
                        d="M3.2538453125,6.148789375C3.7040553125,5.060939375,4.5005453125,4.151979375,5.5198453125,3.562789375C5.8307753125,3.383299375,5.9373353125,2.985726375,5.7578453125,2.674790375C5.5783453125,2.363853875,5.1807753125,2.257297775,4.8698453125,2.436790175C1.2891793125,4.5040893749999995,0.44298531250000006,9.303109375,3.1006453125,12.470409375C5.7583153125,15.637709375,10.6313753125,15.637709375,13.2890453125,12.470409375C15.9467453125,9.303109375,15.1005453125,4.5040893749999995,11.5198453125,2.436790175C11.2089053125,2.2572976750000002,10.8113353125,2.363853975,10.6318453125,2.674790375C10.4523453125,2.9857273749999997,10.5589053125,3.383299375,10.8698453125,3.562789375C13.3504453125,4.994489375000001,14.2634453125,8.125809375,12.9411453125,10.666389375C11.6188453125,13.207009375,8.5303153125,14.255609375,5.9345453125,13.045209375C3.3387753125,11.834789375,2.1566633125,8.794819375,3.2528453125,6.148789375L3.2538453125,6.148789375Z"
                        fill="#212519"
                        fillOpacity="1"
                    />
                    <path
                        d="M3.3200553125,6.248789374999999Q2.5505653125,8.165619375,3.3024153125,10.099109375000001Q4.0663653125,12.063719375,5.9768053125,12.954509375Q7.8872353125,13.845409375,9.8832753125,13.167709375Q11.8793453125,12.490009375,12.8524453125,10.620229375Q13.8256453125,8.750389375000001,13.2356453125,6.726749375Q12.6455453125,4.703099375,10.8198453125,3.649399375Q10.5507753125,3.494069375,10.4703453125,3.193970375Q10.3899053125,2.893871375,10.5452353125,2.624796375Q10.7005653125,2.355720395,11.0006653125,2.275288575Q11.3007653125,2.1948573749999998,11.5698453125,2.350189424Q12.9035453125,3.120204375,13.7631453125,4.374759375Q14.5873453125,5.577779375,14.8422453125,7.023729375Q15.0972453125,8.469709375,14.7341453125,9.882059375Q14.3555453125,11.354949375,13.3656453125,12.534709375Q12.3757453125,13.714409375,10.9909753125,14.343009375Q9.6631153125,14.945909375,8.1948453125,14.945809375Q6.7265653125,14.945809375,5.3987153125,14.343009375Q4.0139653125,13.714409375,3.0240453125,12.534709375Q2.0341263124999998,11.354939375,1.6554993125,9.882059375Q1.2924293125,8.469689375,1.5473909625,7.023729375Q1.8023523125,5.577779375,2.6265853125,4.374759375Q3.4861253125,3.120208375,4.8198453125,2.350184528Q5.0889253125,2.194856375,5.389025312499999,2.275288475Q5.6891253125,2.355720395,5.8444453125,2.624795375Q5.9997753125,2.893871375,5.9193453125,3.193969375Q5.8389153125,3.494069375,5.5698853125,3.649369375Q4.027505312500001,4.540919375,3.3462453125,6.187029375L3.3206853125,6.248789374999999L3.3200553125,6.248789374999999ZM3.1874053125,6.048789375Q3.8977053125,4.384939375,5.4697953125000005,3.476209375Q5.6671753125,3.362269375,5.7261653125,3.1421933749999997Q5.7851453125,2.922113375,5.6712353125,2.724784375Q5.5573253125,2.527455375,5.3372453125,2.468470375Q5.1171653125,2.409485075,4.9198353125,2.523396375Q3.6254553125,3.270709375,2.7915753125,4.487799375Q1.9918003125000001,5.655119375,1.7443523125,7.058469375Q1.4969055125,8.461809375,1.8492023125000001,9.832269375Q2.2165233125,11.261169375,3.1772553125,12.406109375Q4.1379853125,13.551109375,5.4813953125,14.160909375Q6.7698453125,14.745909375,8.1948453125,14.745809375Q9.6198453125,14.745909375,10.9082953125,14.160909375Q12.2516453125,13.551109375,13.2124453125,12.406109375Q14.1731453125,11.261179375,14.5404453125,9.832259375Q14.8927453125,8.461829375,14.6453453125,7.058459375Q14.3978453125,5.655129375,13.5981453125,4.487799375Q12.7642453125,3.270705375,11.4698453125,2.523394375Q11.2725153125,2.409484875,11.0524353125,2.468470375Q10.8323553125,2.527455375,10.7184453125,2.724784375Q10.6045353125,2.922113375,10.6635253125,3.142194375Q10.7225053125,3.362269375,10.9198353125,3.476179375Q12.8150453125,4.570019375,13.4276453125,6.670769375Q14.0401453125,8.771489375,13.0298453125,10.712559375Q12.0196453125,12.653609375,9.9475653125,13.357109375Q7.8755053125,14.060609375,5.8922853125,13.135809375Q3.9090653125,12.211049375,3.1160153125,10.171589375Q2.3229573125,8.132109374999999,3.1604553125,6.110519375L3.1860253125,6.048789375L3.1874053125,6.048789375Z"
                        fillRule="evenodd"
                        fill="#212519"
                        fillOpacity="1"
                    />
                </g>
            </g>
        </g>
    </svg>
);

export const resetIcon = () => (
    <svg xmlns="http://www.w3.org/2000/svg" fill="none" version="1.1" width="14" height="14" viewBox="0 0 15 15">
        <g>
            <g />
            <g clipPath="url(#master_svg0_289_54895)">
                <g>
                    <path
                        d="M2.927981,7.53846C3.1212299999999997,5.41866,4.61632,3.6444799999999997,6.6727,3.0947199999999997C8.72908,2.544955,10.91006,3.33636,12.1354,5.076919999999999L10.13783,5.076919999999999C9.88292,5.076919999999999,9.67629,5.28356,9.67629,5.538460000000001C9.67629,5.79336,9.88292,6,10.13783,6L13.3686,6C13.5385,6,13.6763,5.86224,13.6763,5.69231L13.6763,2.461541C13.6763,2.20664,13.4696,2.00000242086,13.2147,2.00000242086C12.9598,2.00000242086,12.7532,2.20664,12.7532,2.461541L12.7532,4.35877C11.61904,2.870886,9.85484,1.99834655,7.98398,2.00000242086C4.82583,2.00000242086,2.236905,4.44062,2.00121241,7.53846C1.9821354,7.79262,2.190751,8,2.44552,8C2.700289,8,2.904597,7.79262,2.927981,7.53846ZM13.1938,8.46154C13.0006,10.58134,11.50548,12.3555,9.4491,12.9053C7.39273,13.455,5.21174,12.6636,3.98644,10.92308L5.98398,10.92308C6.23888,10.92308,6.44552,10.71644,6.44552,10.46154C6.44552,10.20664,6.23888,10,5.98398,10L2.753212,10C2.583278,10,2.44552,10.13776,2.44552,10.30769L2.44552,13.5385C2.44552,13.7934,2.652158,14,2.907058,14C3.16196,14,3.3686,13.7934,3.3686,13.5385L3.3686,11.64123C4.50276,13.1291,6.26696,14.0017,8.13782,14C11.29659,14,13.8849,11.55938,14.1206,8.46154C14.1397,8.20738,13.9311,8,13.6763,8C13.4215,8,13.2172,8.20738,13.1938,8.46154Z"
                        fill="#212529"
                        fillOpacity="1"
                    />
                    <path
                        d="M1.9417706,8.039159999999999Q2.147564,8.25,2.44552,8.25Q3.11357,8.25,3.17693,7.56137Q3.31475,6.04959,4.29284,4.888920000000001Q5.27093,3.72825,6.73727,3.33623Q8.203610000000001,2.944216,9.630410000000001,3.46195Q10.81923,3.8933400000000002,11.62408,4.826919999999999L10.13783,4.826919999999999Q9.8431,4.826919999999999,9.634689999999999,5.03533Q9.42629,5.243729999999999,9.42629,5.538460000000001Q9.42629,5.83319,9.634689999999999,6.0416Q9.8431,6.25,10.13783,6.25L13.3686,6.25Q13.9263,6.25,13.9263,5.69231L13.9263,2.461541Q13.9263,2.166813,13.7179,1.9584077Q13.5095,1.750002,13.2147,1.750002Q12.92,1.750002,12.7116,1.9584067Q12.5032,2.166813,12.5032,2.461541L12.5032,3.67245Q10.72008,1.7475800000000001,7.98376,1.750002Q5.525259999999999,1.750002,3.72634,3.42455Q1.9367447,5.09042,1.751933,7.5195Q1.729268,7.82145,1.9417706,8.039159999999999ZM2.679033,7.51556Q2.6574619999999998,7.75,2.44552,7.75Q2.358225,7.75,2.299577,7.68991Q2.244763,7.63376,2.250492,7.55743Q2.420476,5.323230000000001,4.06701,3.79053Q5.72195,2.250002,7.9842,2.250002Q10.8295,2.247485,12.5544,4.51033L13.0032,5.099130000000001L13.0032,2.461541Q13.0032,2.250002,13.2147,2.250002Q13.4263,2.250002,13.4263,2.461541L13.4263,5.69231Q13.4263,5.75,13.3686,5.75L10.13783,5.75Q9.92629,5.75,9.92629,5.538460000000001Q9.92629,5.326919999999999,10.13783,5.326919999999999L12.6171,5.326919999999999L12.3398,4.9330099999999995Q11.37556,3.56331,9.80096,2.9919409999999997Q8.22636,2.4205740000000002,6.60813,2.8531969999999998Q4.9899000000000004,3.28582,3.9105,4.56672Q2.831089,5.84762,2.679033,7.51556ZM14.18,7.96084Q13.9742,7.75,13.6763,7.75Q13.0082,7.75,12.9449,8.43863Q12.8071,9.9504,11.82897,11.11108Q10.85088,12.2718,9.384540000000001,12.6638Q7.9182,13.0558,6.49139,12.538Q5.30258,12.1067,4.49772,11.17308L5.98398,11.17308Q6.27871,11.17308,6.48711,10.96467Q6.69552,10.75627,6.69552,10.46154Q6.69552,10.16681,6.48711,9.958400000000001Q6.27871,9.75,5.98398,9.75L2.753212,9.75Q2.19552,9.75,2.19552,10.30769L2.19552,13.5385Q2.19552,13.8332,2.403925,14.0416Q2.612329,14.25,2.907058,14.25Q3.20179,14.25,3.41019,14.0416Q3.6186,13.8332,3.6186,13.5385L3.6186,12.3276Q5.40172,14.2524,8.13805,14.25Q10.59691,14.25,12.3957,12.5754Q14.1851,10.90968,14.3699,8.4805Q14.3925,8.178550000000001,14.18,7.96084ZM13.4428,8.48444Q13.4643,8.25,13.6763,8.25Q13.7636,8.25,13.8222,8.310089999999999Q13.877,8.366240000000001,13.8713,8.44257Q13.7013,10.67688,12.055,12.2095Q10.40021,13.75,8.137599999999999,13.75Q5.2923100000000005,13.7525,3.5674200000000003,11.48967L3.1186,10.90087L3.1186,13.5385Q3.1186,13.75,2.907058,13.75Q2.69552,13.75,2.69552,13.5385L2.69552,10.30769Q2.69552,10.2838,2.712417,10.2669Q2.729316,10.25,2.753212,10.25L5.98398,10.25Q6.19552,10.25,6.19552,10.46154Q6.19552,10.67308,5.98398,10.67308L3.50472,10.67308L3.78202,11.06698Q4.74624,12.4367,6.32084,13.0081Q7.89544,13.5794,9.513670000000001,13.1468Q11.13191,12.7142,12.2113,11.43328Q13.2907,10.15237,13.4428,8.48444Z"
                        fillRule="evenodd"
                        fill="#212529"
                        fillOpacity="1"
                    />
                </g>
            </g>
        </g>
    </svg>
);
