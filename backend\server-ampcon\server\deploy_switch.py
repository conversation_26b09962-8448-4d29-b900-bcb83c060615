import abc
import logging
import os
import re
import time

import six

from ansible_lib import pica_lic
from server.db.models import inventory
from server import constants
from server.util import utils
from server.util.utils import time_now

LOG = logging.getLogger(__name__)
db = inventory.inven_db


class DeployError(Exception):

    def __init__(self, sn, step, msg):
        self.sn = sn
        self.step = step
        self.msg = msg
        self.message = 'deploy switch %s error in [%s] msg [%s]' % (sn, step, msg)


@six.add_metaclass(abc.ABCMeta)
class RegSwitchApi(object):

    running = False

    def __init__(self, info):
        self.sn = info['sn']
        self.ip = info['ip']
        self.model = info['model']
        self.hwid = info['hwid']
        self.uplink_type = info['uplink_type']

        self.user = constants.DEFAULT_USER
        self.pw = constants.DEFAULT_PASSWORD

        self.current_step = 0
        self.mgt_sys_config = db.get_system_config_by_sn(self.sn)
        self.global_config = db.get_global_system_config()
        self.switch_info = None
        self.model_info = None
        # self.check_register()

    def check_register(self):
        """must be implemented in subclass"""
        switch_info = db.get_switch_info_by_sn(self.sn)
        if switch_info:

            db.update_model(inventory.Switch, filters={'sn': [self.sn]},
                            updates={inventory.Switch.hwid: self.hwid})

            if switch_info.status == 'Provisioning Success':
                log_deployed = "::::::::The switch with SN '%s' is already deployed" % self.sn
                msg = {'msg': log_deployed}
                return msg

            log_register = ":::::Register Start::::::register the switch begin"
            self.switch_info = switch_info
            db.add_switch_log(self.sn, log_register, level='info')
            # find current how many steps finished
            self.current_step = switch_info.step
            if not switch_info.platform_model:
                db.update_model(inventory.Switch, filters={'sn': [self.sn]},
                                updates={inventory.Switch.platform_model: self.model})
            self.model_info = db.get_model(inventory.SwitchSystemInfo,
                                           filters={'model': [self.model]})

            # check if had set security config
            deployed_security = db.get_model(inventory.DeployedSecuritySwitch, filters={'sn': [self.sn]})
            if self.current_step >= 2 or deployed_security:
                self.user = self.mgt_sys_config.switch_op_user
                self.pw = self.mgt_sys_config.switch_op_password
            else:
                self.user = constants.DEFAULT_USER
                self.pw = constants.DEFAULT_PASSWORD

            return True
        else:
            return False

    @abc.abstractmethod
    def start_push_security_config(self):
        """must be implemented in subclass"""

    @abc.abstractmethod
    def start_push_full_config(self):
        """must be implemented in subclass"""


class RegSwitch(RegSwitchApi):

    def __init__(self, info):
        super(RegSwitch, self).__init__(info)

    # step 1: apply switch license
    def step1(self):
        log_step1 = "%s::::STEP 1::::::Reg switch info SN: '%s' IP: '%s' model: '%s and write to Databased' \n " % (
            time_now(), self.sn, self.ip, self.model)
        db.add_switch_log(self.sn, log_step1)
        # session.query(Users).filter(Users.id > 0).update({Users.name: Users.name + "099"}, synchronize_session=False)
        log_license_apply = "%s:::::Apply a license in portal:::::: \n" % (time_now())
        db.add_switch_log(self.sn, log_license_apply)
        lic = license_apply_portal(self.sn, self.hwid, self.switch_info.switch_model)
        if lic:
            log_license_apply = "%s:::::Apply successfully :::::: " % (time_now())
            db.add_switch_log(self.sn, log_license_apply)
            # db.update_magt_ip(self.sn, self.ip)
            # db.update_hwid(self.sn, self.hwid)
            log_update_info = "%s:::::Update other infomation of switch in DB:::::: " % (time_now())
            db.add_switch_log(self.sn, log_update_info)
            db.update_step(self.sn, 1)

        # Todo update license count

    # install image
    def step2(self):
        log_step2 = "%s:::::STEP 2::::::check system version upgrade or not" % (time_now())
        db.add_switch_log(self.sn, log_step2)
        # rt = api_ans.upgrade_image_switch(self.sn)
        is_new, hardware_model = conform_switch_version(self.sn, self.switch_info, self.mgt_sys_config)
        if is_new:
            log_conform = '%s:::::STEP 2::::::switch %s version is new, no need to upgrade' % (time_now(), self.sn)
            db.add_switch_log(self.sn, log_conform)
            LOG.info(log_conform)
            return

        # upgrade switch version
        upgrade_image(self.sn, self.switch_info, self.mgt_sys_config, hardware_model)
        db.update_step(self.sn, 2)

    # push security config to switch
    def step3(self):
        log_step3_start = "%s::::::::::STEP 3::::::upload the configuration of security:::::::::::" % (time_now())
        db.add_switch_log(self.sn, log_step3_start)

        # get the security config from DB and generate the file to put remote switch
        # mgt_sys_config = db.get_mgt_system_config()
        sec_file_path = self.mgt_sys_config.security_config
        push_config_tool(self.sn, self.user, self.pw, self.switch_info.tmp_ip, sec_file_path)
        # start to deploy the license file to switch
        log_step3_end = "%s:::::STEP 3:::::: Done" % (time_now())
        db.add_switch_log(self.sn, log_step3_end)
        LOG.info("%s:::::STEP 3:::::: Done", self.sn)
        db.update_step(self.sn, 3)

        # update switch user
        with open(sec_file_path, 'r') as f:
            for line in f:
                finds = re.findall('user\s+([^\s]+).*plain-text-password\s+(.*$)', line)
                if len(finds) == 2:
                    user = finds[0]
                    password = finds[1]
                    self.user = user
                    self.pw = password
                    db.update_model(inventory.Switch, {'sn': [self.sn]}, {inventory.Switch.current_user: user,
                                                                          inventory.Switch.current_password: password})
                    break

    # upload license file
    def step4(self):
        log_step4_start = "%s:::::STEP 4::::::upload the license file to switch and reboot switch:::::::::::" % (
            time_now())
        db.add_switch_log(self.sn, log_step4_start)

        switch_info = db.get_switch_info_by_sn(self.sn)
        lic_string = switch_info.license
        lic_file_path = "license/" + self.sn + ".lic"
        with open(lic_file_path, 'w') as lic_file:
            lic_file.write(lic_string)
        lic_file.close()

        # get the system User and password, push the license
        # mgt_sys_config = db.get_mgt_system_config()
        # username = self.mgt_sys_config.switch_default_user
        # password = self.mgt_sys_config.system_default_passwd

        if push_lic_tool(self.sn, self.user, self.pw, self.ip, lic_file_path):
            log_step4_end = "%s:::::STEP 4:::::: Done\n" % (time_now())
            db.add_switch_log(self.sn, log_step4_end)
            db.update_step(self.sn, 4)

    def step5(self):
        log_step5_start = "%s:::::STEP 5::::::update some information of switch in database:::::::::::" % (
            time_now())
        db.add_switch_log(self.sn, log_step5_start)
        # mgt_sys_config = db.get_mgt_system_config()
        username = self.user
        password = self.pw
        switch_info = dict()

        o, e, e_s = utils.ssh_execute_with_host(self.ip, username, password, 'license -s')
        switch_info['license'] = o
        switch_info['support_end_date'] = \
            re.findall('"Support End Date":.*"([0-9]+\-[0-9]+\-[0-9]+)"', o)[0]
        o, e, e_s = utils.ssh_execute_with_host(self.ip, username, password, 'version')
        switch_info['version'] = o
        current_version = list(map(lambda x: x[0] if x[0] else x[1], re.findall("L2/L3 Version/Revision.*:.*([0-9]\.[0-9]+\.[0-9]+)|PICOS Release/Commit.*:.*([0-9]\.[0-9]+\.[0-9]+)", o)))[0]
        db.update_version(self.sn, current_version)
        db.add_switch_log(self.sn, "%s:::::update the version and license date" % (time_now()))
        db.update_lic_date(self.sn, switch_info['support_end_date'])
        log_step5_end = "%s:::::STEP 5:::::: Done\n" % (time_now())
        db.add_switch_log(self.sn, log_step5_end)
        db.update_step(self.sn, 5)

    def step6(self):
        log_step6_start = "%s:::::STEP 6::::::upload the Full configuration:::::::::::" % (time_now())
        db.add_switch_log(self.sn, log_step6_start)
        # mgt_sys_config = db.get_mgt_system_config()
        username = self.user
        password = self.pw

        # push system banner and ssh_config file to system
        log_step6 = "%s:::::Start to replace the switch system file" % (time_now())
        db.add_switch_log(self.sn, log_step6)

        db_model = db.get_model(inventory.SwitchSystemInfo, {'model': [self.model]})
        banner_file_path = self.mgt_sys_config.banner
        # ssh_config_file = self.mgt_sys_config.ssh_config

        remote_dir = '/home/<USER>'
        banner_remote_path = remote_dir + '/' + os.path.basename(banner_file_path)
        # ssh_remote_path = "/etc/ssh/ssh_config"

        status = utils.push_file(self.ip, username, password, banner_file_path, banner_remote_path)

        if not status:
            raise DeployError(self.sn, 'push banner config', '%s push banner file failed %s %s' %
                              (self.sn, banner_file_path, banner_remote_path))

        cmd = 'tar -xvf ' + banner_remote_path + ' -C ' + remote_dir
        o, e, e_s = utils.ssh_execute_with_host(self.ip, username, password, cmd)

        if e or e_s != 0:
            db.add_switch_log(self.sn, e, level='error')
            raise DeployError(self.sn, 'push banner config', '%s push banner file failed %s %s' %
                              (self.sn, banner_file_path, banner_remote_path))

        chmod_cmd = 'chmod u+x ' + remote_dir + '/' + 'install.sh'
        o, e, e_s = utils.ssh_execute_with_host(self.ip, username, password, chmod_cmd)

        if e or e_s > 0:
            db.add_switch_log(self.sn, e, level='error')
            raise DeployError(self.sn, 'push banner config', '%s chmod install.sh failed' %
                              self.sn)

        execute_install = '.' + remote_dir + '/' + 'install.sh'
        o, e, e_s = utils.ssh_execute_with_host(self.ip, username, password, execute_install)

        if e or e_s > 0:
            db.add_switch_log(self.sn, e, level='error')
            raise DeployError(self.sn, 'push banner config', '%s execute install.sh failed' %
                              self.sn)

        # if not push_system_file_tool(self.sn, username, password, self.ip, banner_file_path, banner_remote_path):
        #     raise DeployError(self.sn, 'push banner config', '%s push banner file failed %s %s' %
        #                       (self.sn, banner_file_path, banner_remote_path))
        # if not push_system_file_tool(self.sn, username, password, self.ip, ssh_config_file, ssh_remote_path):
        #     raise DeployError(self.sn, 'push ssh config', '%s push banner file failed %s %s' %
        #                       (self.sn, ssh_config_file, ssh_remote_path))

        db.update_step(self.sn, 6)

        o, e, e_s = utils.ssh_execute_with_host(self.ip, username, password,
                                                'echo "deployed" > /home/<USER>/auto_flag', retry_times=5)
        if e or e_s > 0:
            raise DeployError(self.sn, 'push config',
                              'modify switch %s /home/<USER>/auto_flag failed error:%s' % (self.sn, e))

        config_str = ''
        switch_configs = self.switch_info.configs
        for config in switch_configs:
            config_str += config.config

        config_file_path = "config_gen/_output/" + self.sn + ".config"
        with open(config_file_path, 'w') as config_file:
            for line in config_str.split("\n"):
                config_file.write(line)
                config_file.write('\n')

        push_config_tool(self.sn, username, password, self.ip, config_file_path)

        LOG.info("::::Waiting to load the full configuration!")
        utils.ssh_execute_with_host(self.ip, username, password, 'sudo reboot &')

        log_step6_end = "%s:::::Step6 END::::::register the switch successful and reboot the switch\n" % (time_now())
        db.add_switch_log(self.sn, log_step6_end)
        db.update_step(self.sn, 6)

    def finish_deploy(self):
        # need do someting in the end, e,g,, call API
        db.update_status(self.sn, 'success')
        sub = 'Switch Regist Info'
        body = 'switch regist is success'
        recipients = []
        utils.send_email(sub, body, recipients)

    def error_callback(self, e):
        db.update_status(self.sn, 'failed')
        if isinstance(e, DeployError):
            db.add_switch_log(self.sn, str(e))
            LOG.error('deploy switch failed in %s with error msg %s', e.step, str(e))
            db.add_event(self.sn, 'error', str(e))
            utils.send_email('Switch %s Regist Error' % self.sn,
                             'deploy switch failed in %s with error msg %s' % (e.step, str(e)))
        else:
            db.add_switch_log(self.sn, 'error in deploy switch')
            LOG.exception('%s -- %s', self.sn, e)
            db.add_event(self.sn, 'error', str(e))
            utils.send_email('Switch %s Regist Error' % self.sn,
                             'deploy switch failed with error msg %s' % str(e))

    def start(self):
        if not self.register():
            # message = ''
            # subject = ''
            # send_email()
            return

        db.update_status(self.sn, 'pending')

        try:
            if self.current_step < 1:
                # ensure license
                self.step1()
            if self.current_step < 2:
                # ensure image is new
                self.step2()
            if self.current_step < 3:
                # push security config to switch
                self.step3()
            if self.current_step < 4:
                # upload license file
                self.step4()
            if self.current_step < 5:
                # update switch db info
                self.step5()
            if self.current_step < 6:
                # push all config to switch
                self.step6()

            self.finish_deploy()
        except DeployError as e:
            self.error_callback(e)
        except Exception as e1:
            self.error_callback(e1)

    def start_push_security_config(self):
        """must be implemented in subclass"""
        pass

    def start_push_full_config(self):
        """must be implemented in subclass"""
        pass


def conform_switch_version(sn, switch_info, mgt_sys_config):
    username = mgt_sys_config.switch_op_user
    password = mgt_sys_config.switch_op_password

    version = switch_info.switch_model.up_to_date_version
    ip = switch_info.tmp_ip

    o, e, e_s = utils.ssh_execute_with_host(ip, username, password, 'version')

    if e:
        error_log = "%s::::%s push image fail execute version with error %s:::: \n" % (time_now(), sn, e)
        raise DeployError(sn, 'push image', error_log)

    if e_s > 0:
        error_log = "%s::::%s push image fail in execute version with error exit code %s:::: \n" \
                    % (time_now(), sn, str(e_s))
        raise DeployError(sn, error_log)

    hardware_model = re.findall("Hardware Model.*: (.*)\n", o)
    current_version = list(map(lambda x: x[0] if x[0] else x[1], re.findall("L2/L3 Version/Revision.*:(.*?)\n|PICOS Release/Commit.*:(.*?)\n", o)))

    if not current_version:
        raise DeployError(sn, 'push image', '%s::::%s error in find version from stdout %s' % (time_now(), sn, o))

    current_version = current_version[0].strip()
    db.update_version(sn, current_version)

    return current_version >= version, hardware_model


def upgrade_image(sn, switch_info, mgt_sys_config, hardware_model):
    username = mgt_sys_config.switch_op_user
    password = mgt_sys_config.switch_op_password

    ip = switch_info.tmp_ip

    # sftp.get('/tmp/learning.py', 'F:\learning.py')
    # image_path = './img/tftpboot/verizon_as4610/2.11.7/pronto3290/picos-2.11.7-P3290-1a7cd1b.tar.gz'
    image_path = switch_info.switch_model.up_to_date_image_path
    image_name = re.findall(".*/(.*)$", image_path)[0]
    image_path_md5 = image_path + '.md5'
    image_path_md5_name = image_name + '.md5'
    image_model = re.findall(".*/picos-.*-(.*)?-", image_path)

    if image_model and hardware_model:
        if image_model[0] != hardware_model[0]:
            log_upgrade_info = "%s:::: switch %s system_model config error [switch model:{%s}, " \
                               "config model:{%s}] ::::" % (time_now(), sn, hardware_model[0], image_model[0])
            raise DeployError(sn, 'upgrade image', log_upgrade_info)

        LOG.info('push switch %s image md5', sn)

        if not utils.push_file(ip, username, password, image_path_md5, '/home/<USER>/%s' % image_path_md5_name,
                               retry_time=3):
            raise DeployError(sn, 'upgrade image', '%s::::%s error in push %s file to switch' %
                              (time_now(), sn, image_path_md5))

        if not utils.push_file(ip, username, password, image_path, '/home/<USER>/%s' % image_name, retry_time=3):
            raise DeployError(sn, 'upgrade image', '%s::::%s error in push %s file to switch' %
                              (time_now(), sn, image_path))

        utils.ssh_execute_with_host(ip, username, password, 'sync')

        # o, e, e_s = utils.ssh_execute_with_host(ip, username, password,
        #                                                     'md5sum -c /home/<USER>/%s ' % image_path_md5_name)
        #
        # md5_check = re.findall(".*: (.*)?", o)[0]
        # if md5_check == 'OK':

        utils.ssh_execute_with_host(ip, username, password,
                                                            'sudo mv /home/<USER>/%s /cftmp/rootfs.tar.gz' % image_name)
        # 4610
        # stdin, stdout, stderr = utils.ssh_execute_with_host(ip, username, password,
        #                                                     'sudo upgrade2 /cftmp/rootfs.tar.gz no-md5-check')
        # 3290
        utils.ssh_execute_with_host(ip, username, password, 'sudo reboot')

        # else:
        #     log_upgrade_info = "%s:::: image doesn't match switch version, Please check:::: \n" % (time_now())
        #     db.add_switch_log(sn, log_upgrade_info)
        #     return False

        # maybe switch will reboot,we should ensure switch is ready before execute next step
        time.sleep(600)
        ensure_switch_ready(ip, username, password)

    else:
        log_upgrade_info = "%s:::: failed find image_model  %s or hardware_model %s ::::" % (time_now(), image_model,
                                                                                             hardware_model)
        raise DeployError(sn, 'upgrade image', log_upgrade_info)


def push_config_tool(sn, username, password, host, config_file):
    log_push_config = "%s::::start to push config to switch '%s' in /home/<USER>/auto.config" % (time_now(), host)
    db.add_switch_log(sn, log_push_config)
    LOG.info("::::%s start to push config", sn)
    utils.push_file(host, username, password, config_file, '/home/<USER>/auto.config')

    db.add_switch_log(sn, "%s:::::Finish push config files:::::" % time_now())
    LOG.info(":::::%s Finish push config files:::::", sn)
    db.add_switch_log(sn, "%s:::::start to load and config the switch %s " % (time_now(), host))
    LOG.info(":::::%s start to load and config", sn)
    stdout, stderr, exit_status = utils.ssh_execute_with_host(host, username, password,
                                        '/pica/bin/pica_sh -c "configure;execute /home/<USER>/auto.config;commit"')

    if stderr:
        raise DeployError(sn, 'push config', stderr)

    if exit_status > 0:
        log_error = "%s::::Loading config files failed with exit_status %s" % (time_now(), exit_status)
        raise DeployError(sn, 'push config', log_error)

    db.add_switch_log(sn, "%s:::::Loading the config files done:::::" % time_now())
    LOG.info(":::::%s Loading the config files done:::::", sn)


def push_system_file_tool(sn, username, password, host, local_file, remote_path):

    log_push_config = "%s::::start to push system file to switch %s in %s" % (time_now(), host, remote_path)
    LOG.debug(log_push_config)
    db.add_switch_log(sn, log_push_config)
    tmp_path = "/home/<USER>/tmp.config"

    status = utils.push_file(host, username, password, local_file, tmp_path)
    if not status:
        LOG.error('push system file failed sn:%s,host:%s,local_file:%s,remote_path:%s', sn, host, local_file, remote_path)
        db.add_switch_log(sn, "%s::::push system file to switch %s in %s failed" % (time_now(), host, remote_path),
                          level='error')
        return False
    cmd = 'sudo mv /home/<USER>/tmp.config ' + remote_path
    o, e, e_s = utils.ssh_execute_with_host(host, username, password, cmd)

    if e:
        db.add_switch_log(sn, e, level='error')
        return False

    if e_s != 0:
        db.add_switch_log(sn, o, level='error')
        return False

    db.add_switch_log(sn, "%s::::system file to switch %s in %s successful" % (time_now(), host, remote_path))
    return True


def push_lic_tool(sn, username, password, host, local_file):

    db.add_switch_log(sn, "%s:::::start to push license to switch %s in /home/<USER>/" % (time_now(), host))
    remote_file = '/home/<USER>/switch.lic'
    status = utils.push_file(host, username, password, local_file, remote_file)
    if not status:
        raise DeployError(sn, 'push license', 'push license file failed sn:%s,host:%s,local_file:%s,remote_path:%s' %
                          (sn, host, local_file, remote_file))

    o, e, e_s = utils.ssh_execute_with_host(host, username, password, 'sudo license -i /home/<USER>/switch.lic')

    if e or e_s:
        log_line = "%s:::: %s " % (time_now(), e)
        raise DeployError(sn, 'push license', log_line)

    log_line = "%s:::: %s " % (time_now(), o)
    db.add_switch_log(sn, log_line)
    db.add_switch_log(sn, "%s:::::Loading the config files done:::::" % time_now())
    db.add_switch_log(sn, "%s:::::Start to restart the PicOS:::::" % time_now())

    o, e, e_s = utils.ssh_execute_with_host(host, username, password, 'sudo service picos restart')
    LOG.info('switch install license stdout %s, stderr %s, exit_code %s', o, e, e_s)
    return not e and e_s <= 0


def ensure_switch_ready(host, username, password):
    # wait L2/L3 stop restart
    return utils.ssh_execute_retry_until_success(host, username, password, '/pica/bin/pica_sh -c "show vlans"', retry_interval=300)


def img_status(size, filsize):
    print('size:%s  filsize:%s' % (size, filsize))


def license_apply_portal(sn, hwid, system_model):
    lic = pica_lic.pica8_license(sn=sn)
    tuple_lics = pica_lic.portal_licenseinfo(lic, hwid=hwid, speed=system_model.speed_for_license,
                                             features=system_model.feature)
    if not tuple_lics:
        error_log = 'portal_licenseinfo is None \n'
        LOG.error(error_log)
        db.add_switch_log(sn, error_log, level='error')
        raise DeployError(sn, 'get license', 'get license inventory error')

    if tuple_lics[1].status:
        db.update_lic(sn, tuple_lics[1])
        return tuple_lics[1].key

    # Create license at portal only none exists
    if not tuple_lics[1].status and tuple_lics[0] > 0:
        _, license_key = lic.license_create(speed=pica_lic.LIC_TYPE[system_model.speed_for_license],
                                         feature=pica_lic.LIC_TYPE[system_model.feature],
                                         hwid=hwid, name="PicOS" + '_' + sn, expiry_date_flag='true',
                                         purge_flag='false')
        LOG.warn(' License_create : key len=%d\n%s\n', len(license_key), license_key)
        tuple_lics[1].key = license_key
        db.update_lic(sn, tuple_lics[1])
        return license_key

    raise DeployError(sn, 'get license', 'no available license')

if __name__ == '__main__':
    o, e, e_s = utils.ssh_execute_with_host('************', 'admin', 'pica8', '/pica/bin/pica_sh -c "show vlans"')
    print(o)
    # o, e, e_s = utils.ssh_execute_with_host('************', 'odl', 'pica8', 'ls')
    # print o
    # print e
    # print e_s
