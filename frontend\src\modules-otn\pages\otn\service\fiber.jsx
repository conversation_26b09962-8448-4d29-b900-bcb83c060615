import {message, Tag} from "antd";
import {useSelector, useDispatch} from "react-redux";
import {CustomTable} from "@/modules-otn/components/common/custom_table";
import {openDBModalCreate} from "@/modules-otn/components/form/create_form_db";
import {createFiber, netconfChange} from "@/modules-otn/apis/api";
import {setSelectedFiberConnectionInfo} from "@/store/modules/otn/mapSlice";
import useUserRight from "@/modules-otn/hooks/useUserRight";
import {useState} from "react";

const Fiber = ({loadData}) => {
    const {labelList} = useSelector(state => state.languageOTN);
    const {neNameMap} = useSelector(state => state.neName);
    const {tableFilter, connections} = useSelector(state => state.map);
    const [commonData, setCommonData] = useState({});
    const dispatch = useDispatch();

    const readyOnlyRight = useUserRight();
    // 表数据
    const connList = structuredClone(connections);

    let initDataSource;
    try {
        if (["ots", "oms", "och", "client"].includes(tableFilter?.type)) {
            initDataSource = [];
            connList.map((item, index) => {
                item.index = index;
            });
            const filterFiberIndex = [];
            tableFilter.servicePortList.map(item => {
                item.ports.map(port => {
                    const fList = connList.filter(
                        f =>
                            (f.sourceIP === item.ne_id && f.sourcePort.split("/").includes(port)) ||
                            (f.destIP === item.ne_id && f.destPort.split("/").includes(port))
                    );
                    fList.map(_fiber => {
                        if (!filterFiberIndex.includes(_fiber.index)) {
                            filterFiberIndex.push(_fiber.index);
                            initDataSource.push(_fiber);
                        }
                    });
                });
            });
        } else {
            initDataSource = connList.filter(item => {
                const {type} = tableFilter;
                const {sourceIP, destIP} = item;
                if (type === "NODE_NE") {
                    // 单网元点击过滤
                    const {id} = tableFilter;
                    return sourceIP.includes(id) || destIP.includes(id);
                }
                // 单区域点击过滤
                if (type === "NODE_GROUP") {
                    const {idList} = tableFilter;
                    return idList.includes(sourceIP) && idList.includes(destIP);
                }

                // 连接线过滤
                if (type === "CELL_FILTER") {
                    const {sourceList, targetList} = tableFilter;
                    const concatPortStr = String(sourceIP + destIP);
                    return (
                        sourceList.some(item => concatPortStr.includes(item)) &&
                        targetList.some(item => concatPortStr.includes(item))
                    );
                }
                return true;
            });
        }
        initDataSource.map(item => {
            item.sourceNE = neNameMap[item.sourceIP];
            item.destNE = neNameMap[item.destIP];
            item.description = item.originValue.map(item => {
                let s = item.source;
                if (s.indexOf("|") > -1) {
                    const temp = s.split("|");
                    s = `${neNameMap[temp[0]]}|${temp[1]}`;
                }
                let d = item.dest;
                if (d.indexOf("|") > -1) {
                    const temp = d.split("|");
                    d = `${neNameMap[temp[0]]}|${temp[1]}`;
                }
                return (
                    <Tag
                        key={`${neNameMap[item.ne_id]} : ${s} -> ${d}`}
                        style={{
                            margin: "0 8px 8px 0",
                            padding: "0 7px",
                            background: "#14C9BB1A",
                            color: "#14C9BB",
                            borderColor: "#14C9BB"
                        }}
                    >{`${neNameMap[item.ne_id]} : ${s} -> ${d}`}</Tag>
                );
            });
        });
    } catch (e) {
        initDataSource = [];
    }

    return (
        <CustomTable
            type="ne:5:connection"
            initTitle="Fiber Connection"
            initDataSource={initDataSource}
            commonData={commonData}
            scroll
            setCommonData={setCommonData}
            refreshParent={() => {
                loadData();
            }}
            initRowOperation={[
                {
                    disabled: () => {
                        return readyOnlyRight.disabled;
                    },
                    label: labelList.del,
                    confirm: {
                        title: labelList.delete_confirm
                    },
                    async onClick() {
                        let fiberList = [];
                        if (commonData["ne:5:connection"]?.selectedRows) {
                            commonData["ne:5:connection"].selectedRows.map(i => {
                                fiberList = fiberList.concat(i.originValue);
                            });
                        } else {
                            const {originValue} = this;
                            fiberList = [...originValue];
                        }
                        let success = true;
                        while (fiberList.length > 0) {
                            const item = fiberList.shift();
                            try {
                                await netconfChange({
                                    ne_id: item.ne_id,
                                    operation: "delete",
                                    entity: "connection",
                                    keys: [item.index],
                                    values: {},
                                    msg: false
                                    // eslint-disable-next-line no-loop-func
                                }).then(rs => {
                                    if (rs.apiResult === "fail") {
                                        success = false;
                                        message.error(labelList.delete_failed);
                                    }
                                });
                            } catch (e) {
                                // eslint-disable-next-line no-console
                                console.log(e);
                            }
                            if (success && fiberList.length === 0) {
                                setTimeout(() => {
                                    message.success(labelList.delete_success);
                                    loadData();
                                }, 500);
                            }
                        }
                    }
                }
            ]}
            buttons={[
                {
                    label: labelList.create,
                    onClick: () => {
                        openDBModalCreate({
                            type: "connection",
                            title: "Create Fiber Connection",
                            submit: async (values, defaultValue, diffValue, cancel, fail) => {
                                const rs = await createFiber(values);
                                if (rs.apiResult === "complete") {
                                    message.success(labelList.create_success).then();
                                    cancel?.(rs);
                                    loadData();
                                } else {
                                    for (let i = 0; i < rs.fiberList.length; i++) {
                                        const fiber = rs.fiberList[i];
                                        try {
                                            await netconfChange({
                                                ne_id: fiber.ne,
                                                operation: "delete",
                                                entity: "connection",
                                                keys: [fiber.index.toString()],
                                                values: {},
                                                msg: false
                                            }).then();
                                        } catch (e) {
                                            // eslint-disable-next-line no-console
                                            console.log(e);
                                        }
                                    }
                                    message.error(labelList.create_failed).then();
                                    // eslint-disable-next-line no-console
                                    console.log(fail);
                                    fail?.(rs);
                                }
                            }
                        });
                    }
                }
            ]}
            initref
            expandable={{
                // eslint-disable-next-line react/no-unstable-nested-components
                expandedRowRender: record => (
                    <p
                        style={{
                            margin: 0
                        }}
                    >
                        {record.description}
                    </p>
                )
            }}
            rowClick={(_, record) => {
                const {description, ...connectionInfo} = record;
                dispatch(setSelectedFiberConnectionInfo(connectionInfo));
            }}
        />
    );
};

export default Fiber;
