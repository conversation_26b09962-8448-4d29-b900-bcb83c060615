import React, {useEffect, useState} from "react";
import {Modal, Form, Input, Select, Button, InputNumber, Switch, Divider, Typography, message, Row, Space} from "antd";
import {MinusOutlined, PlusOutlined} from "@ant-design/icons";
import styles from "@/modules-ampcon/pages/System/user_management.module.scss";
import {useCheckPwd} from "@/modules-ampcon/hooks/useModalTable";
import {updateUserInfo} from "@/modules-ampcon/apis/user_api";
import {fetchLogout} from "@/store/modules/common/user_slice";
import {useDispatch} from "react-redux";

const {Title} = Typography;

const {Option} = Select;

const OldPwdFormItem = () => (
    <Form.Item
        labelCol={{style: {width: 164}}}
        name="oldPassword"
        label="Old Password"
        rules={[{required: true, message: "Please input your old password!"}]}
    >
        <Input.Password placeholder="Old Password" className={styles.formWidth} />
    </Form.Item>
);

const UserModal = ({
    title,
    okText,
    isModalOpen,
    onSubmit,
    onCancel,
    formInstance,
    layoutProps,
    oldPwdFormItemTag,
    setOldPwdFormItemTag,
    groupsData,
    groupSelects,
    setGroupSelects,
    modalClass = ""
}) => {
    useEffect(() => {
        if (formInstance) {
            const userType = formInstance.getFieldValue("userType");
            const group = formInstance.getFieldValue("group");
            const updateField = {};
            if (oldPwdFormItemTag && userType === "group") {
                if (group && group.split(",").length > 0) {
                    group.split(",").map((item, index) => {
                        setGroupSelects(prevState => [...prevState, index]);
                        updateField[`groupSelect${index}`] = parseInt(item);
                    });
                    formInstance.setFieldsValue(updateField);
                } else {
                    setGroupSelects(prevState => [...prevState, Date.now()]);
                }
            }
        }
    }, [oldPwdFormItemTag]);

    const [pwdFocus, setPwdFocus] = useState(false);

    const handleUserTypeChange = value => {
        if (value === "group" && groupSelects.length === 0) {
            setGroupSelects([Date.now()]);
        } else if (value === "global") {
            setGroupSelects([]);
        }
    };

    const handleAddGroupSelect = () => {
        setGroupSelects(prevState => [...prevState, Date.now()]);
    };

    const handleRemoveGroupSelect = id => {
        setGroupSelects(prevState => prevState.filter(item => item !== id));
    };

    const [pwdCheckStatus, setPwdCheckStatus, checkPwdTips, setCheckPwdTips, handlePasswordBlur] =
        useCheckPwd(formInstance);

    return (
        <Modal
            className={modalClass || ""}
            title={
                <div>
                    {title}
                    <Divider style={{marginTop: 8, marginBottom: 0}} />
                </div>
            }
            open={isModalOpen}
            onOk={formInstance.submit}
            okText={okText}
            onCancel={() => {
                setGroupSelects([]);
                setOldPwdFormItemTag(false);
                setPwdFocus(false);
                setPwdCheckStatus("");
                setCheckPwdTips(null);
                onCancel();
            }}
            destroyOnClose
            footer={[
                <Divider style={{marginTop: 0, marginBottom: 20}} />,
                <Button
                    key="cancel"
                    onClick={() => {
                        setGroupSelects([]);
                        setOldPwdFormItemTag(false);
                        setPwdFocus(false);
                        setPwdCheckStatus("");
                        setCheckPwdTips(null);
                        onCancel();
                    }}
                >
                    Cancel
                </Button>,
                <Button key="ok" type="primary" onClick={formInstance.submit}>
                    OK
                </Button>
            ]}
        >
            <Form
                layout="horizontal"
                form={formInstance}
                onFinish={onSubmit}
                {...layoutProps}
                validateTrigger="onBlur"
                labelAlign="left"
            >
                <Form.Item
                    labelCol={{style: {width: 164}}}
                    name="username"
                    label="User Name"
                    rules={[
                        {required: true, message: "Please input your username!"},
                        {
                            validator: (_, value) => {
                                if (!value || !/\s/.test(value)) {
                                    return Promise.resolve();
                                }
                                return Promise.reject(new Error("New User name cannot contain spaces"));
                            }
                        }
                    ]}
                >
                    <Input placeholder="User Name" className={styles.formWidth} disabled={oldPwdFormItemTag} />
                </Form.Item>

                {oldPwdFormItemTag && !pwdFocus ? (
                    <Form.Item
                        labelCol={{style: {width: 164}}}
                        name="noChangePwd"
                        label="Password"
                        initialValue="********"
                        rules={[{required: true, message: "Please input your username!"}]}
                    >
                        <Input.Password
                            className={styles.formWidth}
                            onFocus={() => {
                                setPwdFocus(true);
                            }}
                        />
                    </Form.Item>
                ) : (
                    <>
                        {oldPwdFormItemTag && <OldPwdFormItem />}
                        <Form.Item
                            labelCol={{style: {width: 164}}}
                            name="password"
                            label={oldPwdFormItemTag ? "First Password" : "User Password"}
                            validateStatus={pwdCheckStatus}
                            help={checkPwdTips}
                            rules={[{required: true, message: "Please input your password!"}]}
                        >
                            <Input.Password
                                placeholder="User Password"
                                className={styles.formWidth}
                                onBlur={handlePasswordBlur}
                            />
                        </Form.Item>
                        <Form.Item
                            labelCol={{style: {width: 164}}}
                            name="confirmPassword"
                            label="Confirm Password"
                            rules={[
                                {
                                    required: true,
                                    message: "Please confirm your password!"
                                },
                                ({getFieldValue}) => ({
                                    validator(_, value) {
                                        if (!value || getFieldValue("password") === value) {
                                            return Promise.resolve();
                                        }
                                        return Promise.reject(
                                            new Error("The new password that you entered do not match!")
                                        );
                                    }
                                })
                            ]}
                        >
                            <Input.Password placeholder="Confirm Password" className={styles.formWidth} />
                        </Form.Item>
                    </>
                )}

                <Form.Item
                    labelCol={{style: {width: 164}}}
                    name="userRole"
                    label="User Role"
                    rules={[{required: true, message: "Please select your user role!"}]}
                >
                    <Select placeholder="User Role" style={{width: "280px"}}>
                        <Option key="readonly" value="readonly">
                            Readonly
                        </Option>
                        <Option key="admin" value="admin">
                            Operator
                        </Option>
                        <Option key="superadmin" value="superadmin">
                            Admin
                        </Option>
                        <Option key="superuser" value="superuser">
                            SuperAdmin
                        </Option>
                    </Select>
                </Form.Item>
                <Form.Item
                    labelCol={{style: {width: 164}}}
                    name="userType"
                    label="User Type"
                    rules={[{required: true, message: "Please select your user type!"}]}
                >
                    <Select placeholder="User Type" onChange={handleUserTypeChange} style={{width: "280px"}}>
                        <Option key="global" value="global">
                            Global
                        </Option>
                        <Option key="group" value="group">
                            Group
                        </Option>
                    </Select>
                </Form.Item>

                {groupSelects.map((id, index) => (
                    <div key={id} style={{display: "flex"}}>
                        <Form.Item
                            labelCol={{style: {width: 164}}}
                            name={`groupSelect${id}`}
                            label="Group Name"
                            key={`groupSelect${id}`}
                            rules={[
                                ({getFieldValue}) => ({
                                    validator(_, value) {
                                        const selectedGroups = groupSelects.map(id =>
                                            getFieldValue(`groupSelect${id}`)
                                        );
                                        if (selectedGroups.filter(group => group === value).length > 1) {
                                            return Promise.reject(new Error("The group has been selected !"));
                                        }
                                        return Promise.resolve();
                                    }
                                })
                            ]}
                            style={{flex: 1}}
                            validateTrigger={["onBlur", "onChange"]}
                        >
                            <Select placeholder="Select Group" style={{width: "280px"}}>
                                {groupsData.map(group => (
                                    <Option key={group.id} value={group.id}>
                                        {group.name}
                                    </Option>
                                ))}
                            </Select>
                        </Form.Item>
                        {index === 0 ? (
                            <Button
                                type="primary"
                                icon={<PlusOutlined />}
                                onClick={handleAddGroupSelect}
                                style={{marginRight: "128px"}}
                            />
                        ) : (
                            <Button
                                type="primary"
                                icon={<MinusOutlined />}
                                onClick={() => handleRemoveGroupSelect(id)}
                                style={{marginRight: "128px"}}
                            />
                        )}
                    </div>
                ))}

                <Form.Item
                    labelCol={{style: {width: 164}}}
                    name="email"
                    label="Email"
                    rules={[
                        {required: true, message: "Please input your email!"},
                        {
                            type: "email",
                            message: "Please enter a valid email address!"
                        }
                    ]}
                >
                    <Input placeholder="Email" className={styles.formWidth} />
                </Form.Item>
            </Form>
        </Modal>
    );
};

const TACACSUserModal = ({
    title,
    okText,
    isModalOpen,
    onSubmit,
    onCancel,
    formInstance,
    layoutProps,
    modalClass = ""
}) => {
    return (
        <Modal
            title={
                <div>
                    {title}
                    <Divider style={{marginTop: 8, marginBottom: 0}} />
                </div>
            }
            open={isModalOpen}
            onOk={formInstance.submit}
            okText={okText}
            onCancel={onCancel}
            destroyOnClose
            className={modalClass || ""}
            footer={
                <>
                    <Divider style={{marginBottom: "20px"}} />
                    <Row justify="end">
                        <Space size={16}>
                            <Button onClick={onCancel}>Cancel</Button>
                            <Button type="primary" onClick={formInstance.submit}>
                                OK
                            </Button>
                        </Space>
                    </Row>
                </>
            }
        >
            <Form form={formInstance} onFinish={onSubmit} {...layoutProps} labelAlign="left" className="formSpacewidth">
                <Form.Item
                    name="enable"
                    valuePropName="checked"
                    label="Enable"
                    initialValue={false}
                    labelCol={{style: {width: 156}}}
                >
                    <Switch />
                </Form.Item>

                <Form.Item
                    labelCol={{style: {width: 156}}}
                    name="serverHost"
                    label="Primary Server IP"
                    rules={[
                        {required: true},
                        {pattern: /^(?:\d{1,3}\.){3}\d{1,3}$/, message: "Please enter a valid IP address!"}
                    ]}
                >
                    <Input placeholder="xx.xx.xx.xx" style={{width: 280}} />
                </Form.Item>
                <Form.Item
                    labelCol={{style: {width: 156}}}
                    name="serverHostII"
                    label="Secondary Server IP"
                    rules={[{pattern: /^(?:\d{1,3}\.){3}\d{1,3}$/, message: "Please enter a valid IP address!"}]}
                >
                    <Input placeholder="xx.xx.xx.xx" style={{width: 280}} />
                </Form.Item>
                <Form.Item
                    name="serverSecret"
                    label="Server Key"
                    rules={[{required: true}]}
                    labelCol={{style: {width: 156}}}
                >
                    <Input placeholder="server secret" style={{width: 280}} />
                </Form.Item>
                <Form.Item
                    name="sessionTimeout"
                    label="Session Timeout"
                    rules={[{required: true}]}
                    labelCol={{style: {width: 156}}}
                >
                    <Input placeholder="session timeout seconds" style={{width: 280}} />
                </Form.Item>
                <Form.Item
                    name="authProtocol"
                    label="Auth Protocol"
                    rules={[{required: true}]}
                    labelCol={{style: {width: 156}}}
                >
                    <Select placeholder="protocol" style={{width: 280}}>
                        <Option value={1}>ASCII</Option>
                        <Option value={2}>PAP</Option>
                        <Option value={3}>CHAP</Option>
                    </Select>
                </Form.Item>

                <Divider style={{marginLeft: "0px", width: "616px"}} />
                <Title level={5}>TACACS+ user level mapping</Title>
                <Divider style={{marginLeft: "0px", width: "616px"}} />
                <Form.Item label="readonly" style={{marginBottom: 0}} labelCol={{style: {width: 156}}}>
                    <Form.Item
                        name="readonlyMin"
                        rules={[
                            {required: true},
                            ({getFieldValue}) => ({
                                validator(_, value) {
                                    const readonlyMaxValue = getFieldValue("readonlyMax");
                                    if (!readonlyMaxValue || value <= readonlyMaxValue) {
                                        return Promise.resolve();
                                    }
                                    return Promise.reject(new Error("Min value should be less than max value!"));
                                }
                            })
                        ]}
                        style={{
                            display: "inline-block",
                            width: "calc(28%)"
                        }}
                    >
                        <InputNumber placeholder="Input min value" min={0} max={15} style={{width: "120px"}} />
                    </Form.Item>
                    <Form.Item
                        style={{
                            display: "inline-block",
                            width: "calc(10%)",
                            textAlign: "center"
                        }}
                    >
                        -
                    </Form.Item>

                    <Form.Item
                        name="readonlyMax"
                        rules={[{required: true}]}
                        style={{
                            display: "inline-block",
                            width: "calc(28%)"
                        }}
                    >
                        <InputNumber placeholder="Input max value" min={0} max={15} style={{width: "120px"}} />
                    </Form.Item>
                </Form.Item>

                <Form.Item label="admin" style={{marginBottom: 0}} labelCol={{style: {width: 156}}}>
                    <Form.Item
                        name="adminMin"
                        rules={[
                            {required: true},
                            ({getFieldValue}) => ({
                                validator(_, value) {
                                    const readonlyMaxValue = getFieldValue("adminMax");
                                    if (!readonlyMaxValue || value <= readonlyMaxValue) {
                                        return Promise.resolve();
                                    }
                                    return Promise.reject(new Error("Min value should be less than max value!"));
                                }
                            })
                        ]}
                        style={{
                            display: "inline-block",
                            width: "calc(28%)"
                        }}
                    >
                        <InputNumber placeholder="Input min value" min={0} max={15} style={{width: "120px"}} />
                    </Form.Item>
                    <Form.Item
                        style={{
                            display: "inline-block",
                            width: "calc(10%)",
                            textAlign: "center"
                        }}
                    >
                        -
                    </Form.Item>

                    <Form.Item
                        name="adminMax"
                        rules={[{required: true}]}
                        style={{
                            display: "inline-block",
                            width: "calc(28%)"
                        }}
                    >
                        <InputNumber placeholder="Input max value" min={0} max={15} style={{width: "120px"}} />
                    </Form.Item>
                </Form.Item>

                <Form.Item
                    label="superadmin"
                    style={{marginBottom: 0}}
                    className="userModalflex"
                    labelCol={{style: {width: 156}}}
                >
                    <Form.Item
                        name="superadminMin"
                        rules={[
                            {required: true},
                            ({getFieldValue}) => ({
                                validator(_, value) {
                                    const readonlyMaxValue = getFieldValue("superadminMax");
                                    if (!readonlyMaxValue || value <= readonlyMaxValue) {
                                        return Promise.resolve();
                                    }
                                    return Promise.reject(new Error("Min value should be less than max value!"));
                                }
                            })
                        ]}
                        style={{
                            display: "inline-block",
                            width: "calc(28%)"
                        }}
                    >
                        <InputNumber placeholder="Input min value" min={0} max={15} style={{width: "120px"}} />
                    </Form.Item>
                    <Form.Item
                        style={{
                            display: "inline-block",
                            width: "calc(10%)",
                            textAlign: "center"
                        }}
                    >
                        -
                    </Form.Item>

                    <Form.Item
                        name="superadminMax"
                        rules={[{required: true}]}
                        style={{
                            display: "inline—block",
                            width: "calc(28%)"
                        }}
                    >
                        <InputNumber placeholder="Input max value" min={0} max={15} style={{width: "120px"}} />
                    </Form.Item>
                </Form.Item>

                <Form.Item
                    label="superuser"
                    style={{marginBottom: 0}}
                    className="userModalflex"
                    labelCol={{style: {width: 156}}}
                >
                    <Form.Item
                        name="superuserMin"
                        rules={[
                            {required: true},
                            ({getFieldValue}) => ({
                                validator(_, value) {
                                    const readonlyMaxValue = getFieldValue("superuserMax");
                                    if (!readonlyMaxValue || value <= readonlyMaxValue) {
                                        return Promise.resolve();
                                    }
                                    return Promise.reject(new Error("Min value should be less than max value!"));
                                }
                            })
                        ]}
                        style={{
                            display: "inline—block",
                            width: "calc(28%)"
                        }}
                    >
                        <InputNumber placeholder="Input min value" min={0} max={15} style={{width: "120px"}} />
                    </Form.Item>
                    <Form.Item
                        style={{
                            display: "inline-block",
                            width: "calc(10%)",
                            textAlign: "center"
                        }}
                    >
                        -
                    </Form.Item>

                    <Form.Item
                        name="superuserMax"
                        rules={[{required: true}]}
                        style={{
                            display: "inline-block",
                            width: "calc(28%)"
                        }}
                    >
                        <InputNumber placeholder="Input max value" min={0} max={15} style={{width: "120px"}} />
                    </Form.Item>
                </Form.Item>
            </Form>
        </Modal>
    );
};

const UserEditModalForm = ({title, isModalOpen, setIsModalOpen, formInstance, layoutProps}) => {
    const [pwdCheckStatus, setPwdCheckStatus, checkPwdTips, setCheckPwdTips, handlePasswordBlur] =
        useCheckPwd(formInstance);

    const dispatch = useDispatch();

    const onSubmit = async values => {
        updateUserInfo(values).then(res => {
            if (res.status === 200) {
                setIsModalOpen(false);
                message.success({
                    content: `${res.info}, 3 seconds later, you will be logged out!`,
                    duration: 3,
                    onClose: () => {
                        dispatch(fetchLogout());
                    }
                });
            } else {
                message.error(res.info);
            }
        });
    };

    return (
        <Modal
            title={
                <div>
                    {title}
                    <Divider style={{marginTop: 8, marginBottom: 0}} />
                </div>
            }
            open={isModalOpen}
            onText="Apply"
            onCancel={() => {
                setPwdCheckStatus("");
                setCheckPwdTips(null);
                formInstance.resetFields();
                setIsModalOpen(false);
            }}
            // onOk={formInstance.submit}
            destroyOnClose
            className="ampcon-middle-modal"
            footer={
                <>
                    <Divider style={{marginBottom: "20px", marginTop: "0px"}} />
                    <Row justify="end">
                        <Space>
                            <Button
                                onClick={() => {
                                    setPwdCheckStatus("");
                                    setCheckPwdTips(null);
                                    formInstance.resetFields();
                                    setIsModalOpen(false);
                                }}
                            >
                                Cancel
                            </Button>
                            <Button type="primary" onClick={formInstance.submit}>
                                OK
                            </Button>
                        </Space>
                    </Row>
                </>
            }
        >
            <Form
                layout="horizontal"
                form={formInstance}
                onFinish={onSubmit}
                {...layoutProps}
                validateTrigger="onBlur"
                labelAlign="left"
            >
                <Form.Item
                    name="username"
                    label="User Name"
                    rules={[{required: true, message: "Please input your username!"}]}
                >
                    <Input placeholder="User Name" className={styles.formWidth} disabled />
                </Form.Item>
                <Form.Item
                    name="oldPassword"
                    label="Old Password"
                    rules={[{required: true, message: "Please input your old password!"}]}
                >
                    <Input.Password placeholder="Old Password" className={styles.formWidth} />
                </Form.Item>
                <Form.Item
                    name="password"
                    label="New Password"
                    validateStatus={pwdCheckStatus}
                    help={checkPwdTips}
                    rules={[{required: true, message: "Please input your password!"}]}
                >
                    <Input.Password
                        placeholder="User Password"
                        className={styles.formWidth}
                        onBlur={handlePasswordBlur}
                    />
                </Form.Item>
                <Form.Item
                    name="confirmPassword"
                    label="Confirm Password"
                    rules={[
                        {
                            required: true,
                            message: "Please confirm your password!"
                        },
                        ({getFieldValue}) => ({
                            validator(_, value) {
                                if (!value || getFieldValue("password") === value) {
                                    return Promise.resolve();
                                }
                                return Promise.reject(new Error("The new password that you entered do not match!"));
                            }
                        })
                    ]}
                >
                    <Input.Password placeholder="Confirm Password" className={styles.formWidth} />
                </Form.Item>
                <Form.Item
                    name="email"
                    label="Email"
                    rules={[
                        {required: true, message: "Please input your email!"},
                        {
                            type: "email",
                            message: "Please enter a valid email address!"
                        }
                    ]}
                >
                    <Input placeholder="Email" className={styles.formWidth} />
                </Form.Item>
            </Form>
        </Modal>
    );
};

export {UserModal, TACACSUserModal, UserEditModalForm};
