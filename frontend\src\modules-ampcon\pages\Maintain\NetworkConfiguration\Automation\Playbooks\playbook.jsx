import {
    createColumnConfig,
    AmpConCustomTable,
    TableFilterDropdown,
    AmpConCustomModal
} from "@/modules-ampcon/components/custom_table";
import {
    exportPlaybookAPI,
    fetchInternalPlaybookInfo,
    fetchPlaybookFilelist,
    fetchPlaybookListInfo,
    importPlaybookAPI,
    removePlaybookAPI,
    saveAsPlaybookAPI,
    checkPlaybookAPI,
    updatePlaybookTag
} from "@/modules-ampcon/apis/automation_api";
import Icon from "@ant-design/icons";
import {Space, Button, Form, Input, Row, Switch, message, Divider, Flex, Tag, Card, Tour} from "antd";
import {useRef, useState, useEffect} from "react";
import CustomStep from "@/modules-ampcon/pages/Maintain/NetworkConfiguration/Automation/Playbooks/playbook_step";
import PlaybookEditor from "@/modules-ampcon/pages/Maintain/NetworkConfiguration/Automation/Playbooks/playbook_edit";
import {addSvg, importSvg, updatePlayBookSvg, errorSvg} from "@/utils/common/iconSvg";
import {confirmModalAction} from "@/modules-ampcon/components/custom_modal";

function convertToTreeData(data) {
    return data.map(item => {
        const newNode = {
            title: item.name,
            key: item.dirname
        };
        if (item.children) {
            newNode.children = convertToTreeData(item.children);
        } else {
            newNode.isLeaf = true;
        }
        return newNode;
    });
}

const getDate = () => {
    const timestamp = Date.now();
    const date = new Date(timestamp);
    const year = date.getFullYear();
    const month = date.getMonth() + 1;
    const day = date.getDate();
    const hours = date.getHours();
    const minutes = date.getMinutes();
    const seconds = date.getSeconds();

    return `${year}-${month}-${day}_${hours}:${minutes}:${seconds}`;
};

const PlaybookButton = ({
    setShowPrebuiltTag,
    setIsModalOpenImport,
    setIsModalOpenAdd,
    tableRef,
    playBookRef,
    importRef,
    updateRef,
    switchRef
}) => {
    return (
        <Space size="middle" className="playbook-button">
            <Button
                type="primary"
                onClick={() => {
                    setIsModalOpenAdd(true);
                }}
                style={{height: 36, width: 114}}
                className="playbook"
                ref={playBookRef}
            >
                <Icon component={addSvg} />
                Playbook
            </Button>
            <Button
                htmlType="button"
                onClick={() => {
                    setIsModalOpenImport(true);
                }}
                style={{height: 36, width: 98}}
                className="import"
                ref={importRef}
            >
                <Icon component={importSvg} />
                Import
            </Button>
            <Button
                htmlType="button"
                onClick={() => {
                    tableRef.current.setTableLoading(true);
                    fetchInternalPlaybookInfo()
                        .then(res => {
                            if (res.status === 200) {
                                message.success(res.info);
                            } else {
                                message.error(res.info);
                            }
                            tableRef.current.setTableLoading(false);
                        })
                        .catch(() => {
                            tableRef.current.setTableLoading(false);
                            message.error("Failed to update pre-built playbooks");
                        });
                }}
                style={{display: "flex", alignItems: "center", height: 36}}
                className="update"
                ref={updateRef}
            >
                <Icon component={updatePlayBookSvg} />
                Update Pre-built Playbooks
            </Button>
            <div ref={switchRef}>
                Show Pre-built Playbooks
                <Switch
                    style={{marginLeft: "16px"}}
                    onChange={checked => {
                        setShowPrebuiltTag(checked);
                    }}
                />
            </div>
        </Space>
    );
};

const ImportPlaybookItems = ({setIsModalOpenImport}) => {
    const [playbookZipFileList, setPlaybookZipFileList] = useState([]);

    const handleFileChange = e => {
        const file = e.target.files[0];
        setPlaybookZipFileList([file]);
    };

    return (
        <Form
            layout="horizontal"
            onFinish={value => {
                importPlaybookAPI(value).then(res => {
                    if (res.status === 200) {
                        message.success(res.info);
                        setIsModalOpenImport(false);
                    } else {
                        message.error(res.info);
                    }
                });
            }}
            // validateTrigger="onBlur"
            labelAlign="left"
            labelCol={{span: 6}}
            // wrapperCol={{span: 16}}
            style={{height: "100%"}}
        >
            <Form.Item
                name="playbookName"
                label="Playbook Name"
                rules={[{required: true, message: "Please input playbook name!"}]}
            >
                <Input placeholder="Playbook Name" style={{width: "280px"}} />
            </Form.Item>
            <Form.Item
                name="playbookDesc"
                label="Description"
                rules={[{required: true, message: "Please input playbook description!"}]}
            >
                <Input placeholder="Playbook Description" style={{width: "280px"}} />
            </Form.Item>
            <Form.Item
                name="playbookFile"
                label="Zip File"
                rules={[
                    {
                        required: true,
                        validator: () => {
                            const file = playbookZipFileList[0];
                            if (!file.name.endsWith(".zip")) {
                                return Promise.reject(new Error("File type must be .zip"));
                            }
                            if (file.size > 512 * 1024 * 1024) {
                                return Promise.reject(new Error("File size cannot exceed 512MB"));
                            }
                            return Promise.resolve();
                        }
                    }
                ]}
                valuePropName="file"
            >
                <Input type="file" aria-required="true" style={{width: "280px"}} onChange={handleFileChange} />
            </Form.Item>

            <Divider style={{marginTop: "138px", marginLeft: "-32px"}} />
            <Row justify="end">
                <Button type="primary" htmlType="submit">
                    Import
                </Button>
            </Row>
        </Form>
    );
};

const SavePlaybookItems = ({srcPlaybookName, newPlaybookName, setIsModalOpenSave}) => {
    return (
        <Form
            layout="horizontal"
            onFinish={values => {
                saveAsPlaybookAPI(values).then(res => {
                    if (res.status === 200) {
                        message.success(res.info);
                        setIsModalOpenSave(false);
                    } else {
                        message.error(res.info);
                    }
                });
            }}
            validateTrigger="onBlur"
            labelAlign="left"
            labelCol={{span: 7}}
            // wrapperCol={{span: 16}}
        >
            <Form.Item
                name="srcPlaybookName"
                label="Src Playbook Name"
                rules={[{required: true, message: "Please input src playbook name!"}]}
                initialValue={srcPlaybookName}
            >
                <Input placeholder="Src Playbook Name" style={{width: "280px"}} />
            </Form.Item>
            <Form.Item
                name="playbookName"
                label="Playbook Name"
                rules={[{required: true, message: "Please input new playbook name!"}]}
                initialValue={newPlaybookName}
            >
                <Input placeholder="Playbook Name" style={{width: "280px"}} />
            </Form.Item>

            <Divider style={{marginTop: "199px", marginLeft: "-32px"}} />
            <Row justify="end">
                <Button type="primary" htmlType="submit">
                    Save
                </Button>
            </Row>
        </Form>
    );
};

const TagPlaybookItems = ({srcPlaybookName, playBookTags, setPlayBookTags}) => {
    const [tmpTagContent, setTmpTagContent] = useState("");
    const NAME_MATCH_REGEX = "/^[sw:-]+$/";
    const handleTagClose = tagToRemove => {
        const updatedTags = playBookTags
            .split(",")
            .filter(tag => tag !== tagToRemove)
            .join(",");
        setPlayBookTags(updatedTags);
    };

    return (
        <Form
            layout="horizontal"
            validateTrigger="onBlur"
            labelAlign="left"
            labelCol={{flex: "147px"}}
            wrapperCol={{flex: "280px"}}
            style={{minHeight: "278px"}}
        >
            <Form.Item
                name="playbookName"
                label="Playbook Name"
                rules={[
                    {required: true, message: "Please input src playbook name!"},
                    {pattern: NAME_MATCH_REGEX, message: "Invalid playbook name!"}
                ]}
                initialValue={srcPlaybookName}
            >
                <Input placeholder="Src Playbook Name" disabled />
            </Form.Item>
            <Form.Item label="Playbook Tag" rules={[{required: true, message: "Please input new playbook name!"}]}>
                <div style={{border: "1px solid #ccc", minHeight: "50px"}}>
                    <Space style={{flexWrap: "wrap", padding: 10}}>
                        {playBookTags &&
                            playBookTags.split(",").map(tag => (
                                <Tag
                                    style={{
                                        color: "#14C9BB",
                                        backgroundColor: "rgba(20, 201, 187, 0.1)",
                                        border: "1px solid #14C9BB"
                                    }}
                                    key={tag}
                                    closable
                                    onClose={() => handleTagClose(tag)}
                                >
                                    {tag}
                                </Tag>
                            ))}
                    </Space>
                </div>
            </Form.Item>

            <Form.Item label="Tag Name">
                <Flex>
                    <Input
                        placeholder="Input Tag Name"
                        style={{width: "calc(100% - 34px)", marginRight: "16px"}}
                        onChange={e => setTmpTagContent(e.target.value)}
                        value={tmpTagContent}
                    />
                    <Button
                        type="primary"
                        onClick={() => {
                            if (tmpTagContent.trim() === "") {
                                message.error("New tag is not allow empty");
                                return;
                            }
                            if (playBookTags && playBookTags.split(",").includes(tmpTagContent.trim())) {
                                message.error("New tag is duplicate");
                                return;
                            }
                            if (tmpTagContent.length > 24) {
                                message.error("Tag Name over 24 character limit.");
                                return;
                            }
                            if (!playBookTags) {
                                setPlayBookTags(tmpTagContent);
                            } else {
                                setPlayBookTags(`${playBookTags},${tmpTagContent.trim()}`);
                            }
                            setTmpTagContent("");
                        }}
                    >
                        Add
                    </Button>
                </Flex>
            </Form.Item>
        </Form>
    );
};

const Playbook = () => {
    const tableRef = useRef(null);
    const playBookRef = useRef(null);
    const importRef = useRef(null);
    const updateRef = useRef(null);
    const switchRef = useRef(null);
    const operationRef = useRef(null);
    const cardRef = useRef(null);

    const [open, setOpen] = useState(false);
    const [steps, setSteps] = useState([
        {
            title: "Welcome to the playbook page",
            description: (
                <>
                    <Divider />
                    <p>
                        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Welcome to the playbook page, you can use this page to
                        manage your playbooks. This feature requires you to have some knowledge of Ansible, if you are
                        not familiar with Ansible, please learn the relevant content first.
                    </p>
                    <Divider />
                </>
            ),
            placement: "center",
            target: () => cardRef.current
        },
        {
            title: "Add a playbook",
            description: (
                <>
                    <Divider />
                    <p>
                        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Click here you can customize the content of the playbook to
                        achieve the features you want.
                    </p>
                    <Divider />
                </>
            ),
            placement: "bottomRight",
            target: () => playBookRef.current
        },
        {
            title: "Import a playbook",
            description: (
                <>
                    <Divider />
                    <p>
                        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;You can also click on the import playbook file here to
                        implement the functions you want.
                    </p>
                    <Divider />
                </>
            ),
            placement: "bottomRight",
            target: () => importRef.current
        },
        {
            title: "Update Pre-built Playbooks",
            description: (
                <>
                    <Divider />
                    <p>
                        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;By clicking on this button, you can update the content of
                        the pre-built playbook.
                    </p>
                    <Divider />
                </>
            ),
            placement: "bottomRight",
            target: () => updateRef.current
        },
        {
            title: "Switch the pre-built playbooks",
            description: (
                <>
                    <Divider />
                    <p>
                        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;There is a toggle to get a sample of the already built
                        playbook.
                    </p>
                    <Divider />
                </>
            ),
            placement: "bottomRight",
            target: () => switchRef.current
        },
        {
            title: "Take some operations",
            description: (
                <>
                    <Divider />
                    <p>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Click the action button to achieve the relevant action.</p>
                    <Divider />
                </>
            ),
            placement: "topLeft",
            target: () => operationRef.current
        }
    ]);

    useEffect(() => {
        const fetchData = async () => {
            const res = await fetchPlaybookListInfo();
            if (res.total === 0) {
                setSteps([
                    {
                        title: "Welcome to the playbook page",
                        description: (
                            <>
                                <Divider />
                                <p>
                                    &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Welcome to the playbook page, you can use this
                                    page to manage your playbooks. This feature requires you to have some knowledge of
                                    Ansible, if you are not familiar with Ansible, please learn the relevant content
                                    first.
                                </p>
                                <Divider />
                            </>
                        ),
                        placement: "center",
                        target: () => cardRef.current
                    },
                    {
                        title: "Add a playbook",
                        description: (
                            <>
                                <Divider />
                                <p>
                                    &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Click here you can customize the content of the
                                    playbook to achieve the features you want.
                                </p>
                                <Divider />
                            </>
                        ),
                        placement: "bottomRight",
                        target: () => playBookRef.current
                    },
                    {
                        title: "Import a playbook",
                        description: (
                            <>
                                <Divider />
                                <p>
                                    &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;You can also click on the import playbook file
                                    here to implement the functions you want.
                                </p>
                                <Divider />
                            </>
                        ),
                        placement: "bottomRight",
                        target: () => importRef.current
                    },
                    {
                        title: "Update Pre-built Playbooks",
                        description: (
                            <>
                                <Divider />
                                <p>
                                    &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;By clicking on this button, you can update the
                                    content of the pre-built playbook.
                                </p>
                                <Divider />
                            </>
                        ),
                        placement: "bottomRight",
                        target: () => updateRef.current
                    },
                    {
                        title: "Switch the pre-built playbooks",
                        description: (
                            <>
                                <Divider />
                                <p>
                                    &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;There is a toggle to get a sample of the already
                                    built playbook.
                                </p>
                                <Divider />
                            </>
                        ),
                        placement: "bottomRight",
                        target: () => switchRef.current
                    }
                ]);
            }
        };
        fetchData();
        const isFirstVisit = localStorage.getItem("Visited") === null;
        if (isFirstVisit) {
            localStorage.setItem("Visited", "true");
            setOpen(true);
        }
    }, []);

    const savePlaybook = record => {
        setIsModalOpenSave(true);
        setSrcPlayBookName(record.name);
        setNewPlayBookName(`${record.name}:::${getDate()}`);
    };

    const runPlaybook = async record => {
        await fetchPlaybookFilelist(record.name)
            .then(res => {
                if (res.status === 200) {
                    setTreeData(convertToTreeData(res.info));
                } else {
                    message.error(res.info);
                }
            })
            .catch(() => {
                message.error("Failed to fetch playbook tree");
            });
        setPlaybookName(record.name);
        setPlaybookDesc(record.description);
        setIsModalOpenRun(true);
    };

    const removePlaybook = record => {
        confirmModalAction("Are you sure want to delete?", () => {
            removePlaybookAPI(record.name).then(res => {
                if (res.status === 200) {
                    message.success(res.info);
                    tableRef.current.refreshTable();
                } else {
                    message.error(res.info);
                }
            });
        });
    };

    const exportPlaybook = record => {
        exportPlaybookAPI(record.name);
    };

    const tagPlaybook = record => {
        setIsModalOpenTag(true);
        setSrcPlayBookName(record.name);
        setPlaybookTags(record.tag);
        setPlaybookId(record.id);
    };

    const checkPlaybook = record => {
        tableRef.current.setTableLoading(true);
        checkPlaybookAPI(record.name)
            .then(res => {
                if (res.status === 200) {
                    message.success(res.info);
                } else {
                    const content = (
                        <pre
                            style={{
                                whiteSpace: "pre-wrap",
                                width: 760,
                                height: 200,
                                overflowY: "auto",
                                margin: 0
                            }}
                        >
                            <Icon component={errorSvg} style={{marginRight: 8}} />
                            {res.info}
                        </pre>
                    );

                    message.open({
                        content,
                        className: "custom-error-message"
                    });
                }
                tableRef.current.refreshTable(false);
            })
            .catch(() => {
                tableRef.current.refreshTable(false);
                message.error("Failed to check playbook");
            });
    };

    const [isModalOpenAdd, setIsModalOpenAdd] = useState(false);
    const [isModalOpenEdit, setIsModalOpenEdit] = useState(false);
    const [isModalOpenImport, setIsModalOpenImport] = useState(false);
    const [isModalOpenSave, setIsModalOpenSave] = useState(false);
    const [isModalOpenTag, setIsModalOpenTag] = useState(false);
    const [isModalOpenRun, setIsModalOpenRun] = useState(false);

    const [showPrebuiltTag, setShowPrebuiltTag] = useState(false);

    const [treeData, setTreeData] = useState([]);
    const [checkStatus, setCheckStatus] = useState();
    const [checkHelp, setCheckHelp] = useState(null);

    const [playbookName, setPlaybookName] = useState("");
    const [playbookDesc, setPlaybookDesc] = useState("");
    const [isSaveAllDisabled, setIsSaveAllDisabled] = useState(false);

    const [srcPlayBookName, setSrcPlayBookName] = useState("");
    const [newPlayBookName, setNewPlayBookName] = useState("");
    const [playBookTags, setPlaybookTags] = useState("");
    const [playbookId, setPlaybookId] = useState("");

    const matchFieldsList = [
        {name: "name", matchMode: "fuzzy"},
        {name: "description", matchMode: "fuzzy"},
        {name: "create_user", matchMode: "fuzzy"},
        {name: "tag", matchMode: "fuzzy"},
        {name: "modified_time", matchMode: "fuzzy"}
    ];

    const searchFieldsList = ["name", "description", "create_user", "tag"];

    const columns = [
        {
            ...createColumnConfig("Name", "name", TableFilterDropdown, "", "15%"),
            fixed: "left",
            render: (_, record) => {
                return (
                    <div>
                        {record.internal ? (
                            <Space>
                                {record.name}
                                <Tag
                                    style={{
                                        color: "#14C9BB",
                                        backgroundColor: "rgba(20, 201, 187, 0.1)",
                                        border: "1px solid #14C9BB"
                                    }}
                                >
                                    Pre-built
                                </Tag>
                            </Space>
                        ) : (
                            record.name
                        )}
                    </div>
                );
            }
        },
        createColumnConfig("Description", "description", TableFilterDropdown, "", "12%"),
        createColumnConfig("Created By", "create_user", TableFilterDropdown, "", "10%"),
        createColumnConfig("Last Modified", "modified_time", TableFilterDropdown, "", "13%", "descend"),
        {
            ...createColumnConfig("Tag", "tag", TableFilterDropdown, "", "20%"),
            render: (_, record) => {
                const items = record.tag ? record.tag.split(",") : [];
                return (
                    <Flex style={{flexWrap: "wrap", rowGap: "5px", marginTop: "5px", marginBottom: "5px"}}>
                        {items.map(item => (
                            <Tag
                                htmlType="button"
                                key={item}
                                size="small"
                                style={{
                                    color: "#14C9BB",
                                    backgroundColor: "rgba(20, 201, 187, 0.1)",
                                    border: "1px solid #14C9BB"
                                }}
                            >
                                {item}
                            </Tag>
                        ))}
                    </Flex>
                );
            }
        },
        {
            title: "Operation",
            fixed: "right",
            render: (_, record) => {
                return (
                    <div ref={operationRef}>
                        <Space size="large" className="actionLink">
                            <a
                                onClick={async () => {
                                    if (record.internal) {
                                        setIsSaveAllDisabled(true);
                                    } else {
                                        setIsSaveAllDisabled(false);
                                    }
                                    await fetchPlaybookFilelist(record.name)
                                        .then(res => {
                                            if (res.status === 200) {
                                                setTreeData(convertToTreeData(res.info));
                                            } else {
                                                message.error(res.info);
                                            }
                                        })
                                        .catch(() => {
                                            message.error("Failed to fetch playbook file list");
                                        });
                                    setIsModalOpenEdit(true);
                                    setPlaybookName(record.name);
                                    setPlaybookDesc(record.description);
                                }}
                            >
                                {record.internal ? "View" : "Edit"}
                            </a>
                            <a onClick={() => savePlaybook(record)}> Save AS</a>
                            {!record.internal && <a onClick={() => runPlaybook(record)}>Run</a>}
                            <a
                                onClick={() => {
                                    exportPlaybook(record);
                                }}
                            >
                                Export
                            </a>

                            {!record.internal && (
                                <>
                                    <a onClick={() => removePlaybook(record)}>Remove</a>
                                    <a onClick={() => tagPlaybook(record)}>Tag Management</a>
                                    <a onClick={() => checkPlaybook(record)}>Check</a>
                                </>
                            )}
                        </Space>
                    </div>
                );
            }
        }
    ];

    return (
        <Card ref={cardRef} style={{flex: 1}} className={open ? "no-interaction" : ""}>
            <Tour steps={steps} open={open} onClose={() => setOpen(false)} />
            <AmpConCustomModal
                modalClass="ampcon-max-modal"
                title="Ansible Playbook"
                onCancel={() => {
                    setIsModalOpenAdd(false);
                    setCheckStatus("");
                    setCheckHelp(null);
                    setTreeData([]);
                    setPlaybookName("");
                    setPlaybookDesc("");
                }}
                childItems={
                    <PlaybookEditor
                        setIsModalOpen={setIsModalOpenAdd}
                        treeData={treeData}
                        setTreeData={setTreeData}
                        isSaveAllDisabled={isSaveAllDisabled}
                        playbookName={playbookName}
                        setPlaybookName={setPlaybookName}
                        playbookDesc={playbookDesc}
                        setPlaybookDesc={setPlaybookDesc}
                        isModalOpenEdit={isModalOpenEdit}
                        checkStatus={checkStatus}
                        setCheckStatus={setCheckStatus}
                        checkHelp={checkHelp}
                        setCheckHelp={setCheckHelp}
                    />
                }
                isModalOpen={isModalOpenAdd}
            />

            <AmpConCustomModal
                modalClass="ampcon-max-modal"
                title="Ansible Playbook"
                onCancel={() => {
                    setIsModalOpenEdit(false);
                    setCheckStatus("");
                    setCheckHelp(null);
                    setTreeData([]);
                    setPlaybookName("");
                    setPlaybookDesc("");
                    setIsSaveAllDisabled(false);
                }}
                childItems={
                    <PlaybookEditor
                        setIsModalOpen={setIsModalOpenEdit}
                        treeData={treeData}
                        setTreeData={setTreeData}
                        isSaveAllDisabled={isSaveAllDisabled}
                        playbookName={playbookName}
                        setPlaybookName={setPlaybookName}
                        playbookDesc={playbookDesc}
                        setPlaybookDesc={setPlaybookDesc}
                        isModalOpenEdit={isModalOpenEdit}
                        checkStatus={checkStatus}
                        setCheckStatus={setCheckStatus}
                        checkHelp={checkHelp}
                        setCheckHelp={setCheckHelp}
                    />
                }
                isModalOpen={isModalOpenEdit}
            />

            <AmpConCustomModal
                modalClass="ampcon-custom-modal-style"
                title="Import Playbook"
                onCancel={() => {
                    setIsModalOpenImport(false);
                }}
                childItems={<ImportPlaybookItems setIsModalOpenImport={setIsModalOpenImport} />}
                isModalOpen={isModalOpenImport}
            />

            <AmpConCustomModal
                title="Save As Playbook"
                onCancel={() => {
                    setIsModalOpenSave(false);
                }}
                childItems={
                    <SavePlaybookItems
                        srcPlaybookName={srcPlayBookName}
                        newPlaybookName={newPlayBookName}
                        setIsModalOpenSave={setIsModalOpenSave}
                    />
                }
                isModalOpen={isModalOpenSave}
                modalClass="ampcon-custom-modal-style"
            />

            <AmpConCustomModal
                modalClass="ampcon-max-modal"
                title="Run Playbook"
                onCancel={() => {
                    setIsModalOpenRun(false);
                    setIsModalOpenAdd(false);
                    setCheckStatus("");
                    setCheckHelp(null);
                    setTreeData([]);
                    setPlaybookName("");
                    setPlaybookDesc("");
                }}
                childItems={
                    <CustomStep
                        treeData={treeData}
                        playbookName={playbookName}
                        playbookDesc={playbookDesc}
                        setIsModalOpenRun={setIsModalOpenRun}
                    />
                }
                isModalOpen={isModalOpenRun}
            />

            <AmpConCustomModal
                title="Tag Management"
                onCancel={() => {
                    setIsModalOpenTag(false);
                }}
                footer={
                    <Flex vertical>
                        <Divider style={{marginTop: 0, marginBottom: 20}} />
                        <Flex justify="flex-end">
                            <Button
                                type="primary"
                                onClick={() => {
                                    updatePlaybookTag({
                                        recordId: playbookId,
                                        recordType: "playbook",
                                        tagContent: playBookTags
                                    }).then(res => {
                                        if (res.status === 200) {
                                            setIsModalOpenTag(false);
                                            message.success(res.info);
                                        } else {
                                            message.error(res.info);
                                        }
                                    });
                                }}
                            >
                                Save
                            </Button>
                        </Flex>
                    </Flex>
                }
                childItems={
                    <TagPlaybookItems
                        srcPlaybookName={srcPlayBookName}
                        playBookTags={playBookTags}
                        setPlayBookTags={setPlaybookTags}
                        playbookId={playbookId}
                        setIsModalOpenTag={setIsModalOpenTag}
                    />
                }
                isModalOpen={isModalOpenTag}
                modalClass="ampcon-middle-modal"
            />

            <h2 style={{margin: "8px 0 20px"}}>Playbooks</h2>
            <AmpConCustomTable
                ref={tableRef}
                columns={columns}
                searchFieldsList={searchFieldsList}
                matchFieldsList={matchFieldsList}
                extraButton={
                    <PlaybookButton
                        setShowPrebuiltTag={setShowPrebuiltTag}
                        tableRef={tableRef}
                        setIsModalOpenAdd={setIsModalOpenAdd}
                        setIsModalOpenImport={setIsModalOpenImport}
                        playBookRef={playBookRef}
                        importRef={importRef}
                        updateRef={updateRef}
                        switchRef={switchRef}
                    />
                }
                fetchAPIInfo={fetchPlaybookListInfo}
                fetchAPIParams={[showPrebuiltTag]}
                // helpDraw={
                //     <HelpDraw
                //         title="Playbooks"
                //         content={
                //             <ul>
                //                 <li>use playbook by step 1</li>
                //                 <li>use Playbook by step 2</li>
                //                 <li>use Playbook by step 3</li>
                //                 <li>....</li>
                //             </ul>
                //         }
                //     />
                // }
            />
        </Card>
    );
};

export default Playbook;
