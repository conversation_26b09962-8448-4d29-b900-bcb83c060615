import {Form, message, Select} from "antd";
import {useEffect, useState} from "react";
import {LoadingOutlined} from "@ant-design/icons";
import merge from "lodash/merge";
import {LeafRefs} from "@/modules-otn/config/leafref";
import {apiEditRpc, apiGetCategory, apiGetYang, netconfGetByXML} from "@/modules-otn/apis/api";
import {gLabelList} from "@/store/modules/otn/languageOTNSlice";
import {
    addNS,
    getText,
    getValueByJPath,
    getYangByPath,
    removeNSForObj,
    needAscii16FormatKeys,
    needAscii16FormatWithFlagKeys
} from "@/modules-otn/utils/util";
import YANG_CONFIG from "@/modules-otn/config/yang_config";
import AsciiHexAndPlainTextConverter from "@/modules-otn/components/input/asciihex_and_plaintext_converter";
import DisableInput from "./disable_input";
import styles from "./edit_form.module.scss";
import EditInput from "./edit_input";
import {MIN_FREQ_PARAM} from "../common/frequency";
import {bigModal, middleModal} from "../modal/custom_modal";

const EditForm = ({
    setForm,
    afterFinish,
    Yang,
    category,
    labelCol,
    wrapperCol,
    title,
    refresh,
    initData,
    keys,
    ne_id,
    type,
    categoryName,
    readOnly,
    columnConfig,
    columnNum
}) => {
    const [form] = Form.useForm();
    setForm?.(form);
    const [requestConfig, setRequestConfig] = useState({});
    const [initialValues, setInitialValues] = useState(null);
    const [options, setOptions] = useState([]);

    const {rootPath, configRootPath, tabs} = category;
    const UpperDef = configRootPath != null;

    const [datas, setDatas] = useState({});
    const useDefine = YANG_CONFIG[categoryName];

    const parseData = _v => {
        const _data = _v;
        setInitialValues(_data);
        form.setFieldsValue(_data);

        const req = {};
        let _keys = [...keys];
        // eslint-disable-next-line no-return-assign
        const newRequest = (p, c) => [
            (p[0][c] = Object.fromEntries(
                p[1][c].definition.key ? p[1][c].definition.key.split(" ").map(k => [k, _keys.shift()]) : []
            )),
            p[1][c]
        ];
        rootPath.reduce(newRequest, [req, Yang]);
        if (UpperDef) {
            _keys = [...keys];
            const req1 = {};
            configRootPath?.reduce(newRequest, [req1, Yang]);
            setRequestConfig(req1);
        } else {
            setRequestConfig(req);
        }
    };

    const fetchData = () => {
        if (initData) {
            parseData(initData);
        } else {
            let req = {};
            let _keys = [...keys];
            // eslint-disable-next-line no-return-assign
            const newRequest = (p, c) => [
                (p[0][c] = Object.fromEntries(
                    p[1][c].definition.key ? p[1][c].definition.key.split(" ").map(k => [k, _keys.shift()]) : []
                )),
                p[1][c]
            ];
            rootPath.reduce(newRequest, [req, Yang]);
            if (UpperDef) {
                _keys = [...keys];
                const req1 = {};
                configRootPath?.reduce(newRequest, [req1, Yang]);
                req = merge(req, req1);
            }
            netconfGetByXML({ne_id, msg: true, xml: addNS(req, Yang)})
                .then(rs => {
                    parseData(removeNSForObj(rs));
                })
                .catch(e => {
                    // eslint-disable-next-line no-console
                    console.log(e.message);
                    afterFinish?.(true);
                });
        }
    };

    useEffect(fetchData, []);

    const formatCommitValue = o =>
        Object.fromEntries(
            Object.entries(o).map(([k, v]) => {
                if (k === "used-service-port-type-preconf") {
                    return [k, `PT_${v}`];
                }
                if (v?.constructor === Object) {
                    return [k, formatCommitValue(v)];
                }
                return [k, v];
            })
        );

    const onFinish = async () => {
        let changedValues = form.getFieldsValue(true, meta => {
            return (
                useDefine?.[meta.name?.at(-1)]?.commit ||
                (meta.touched && getValueByJPath(initialValues, meta.name) !== form.getFieldValue(meta.name))
            );
        });
        changedValues = formatCommitValue(changedValues);
        if (Object.keys(changedValues).length === 0) {
            message.info("No Change");
            afterFinish?.(false);
        } else {
            await apiEditRpc({
                ne_id,
                params: merge(changedValues, requestConfig),
                success: () => {
                    afterFinish?.(true);
                },
                fail: () => {
                    afterFinish?.(false);
                }
            });
        }
    };

    const createTabItems = (field, statePath, configPath, mode, key, y, showColumns) => {
        const allItems = Object.entries(field)
            .filter(
                ([k]) =>
                    (!showColumns || showColumns.find(i => i.dataIndex === k)) &&
                    (useDefine?.[k]?.needDisplay ||
                        getValueByJPath(initialValues, [...statePath, k]) !== undefined ||
                        getValueByJPath(initialValues, [...configPath, k]) !== undefined)
            )
            .map(([k, v]) => {
                if (useDefine?.[k]?.when) {
                    if (!useDefine?.[k].when(form.getFieldsValue(), initialValues)) {
                        return;
                    }
                }
                const temp = getValueByJPath(LeafRefs, [...configPath]);
                const leafRef = temp?.config?.[k] ?? temp?.[k];
                return ((type === "5" && mode === 0 && v.configMode === 2) || UpperDef) &&
                    !key.includes(k) &&
                    !readOnly &&
                    (useDefine?.[k]?.needDisplay ||
                        getValueByJPath(initialValues, [...configPath, k]) !== undefined) ? (
                    <Form.Item
                        key={k}
                        label={getText(showColumns?.find?.(i => i.dataIndex === k)?.title ?? k)}
                        tooltip={
                            (useDefine?.[k]?.description ?? y[k].description) ? (
                                <span style={{whiteSpace: "pre-line"}}>
                                    {useDefine?.[k]?.description ?? y[k].description}
                                </span>
                            ) : (
                                false
                            )
                        }
                        style={{marginBottom: 0}}
                    >
                        <div className={styles.multiColumnForm}>
                            <Form.Item
                                key="config"
                                name={[...configPath, k]}
                                style={{
                                    display: "inline-block",
                                    width: type === "5" ? "50%" : "100%"
                                }}
                            >
                                {leafRef ? (
                                    <Select
                                        notFoundContent={gLabelList.loading}
                                        onDropdownVisibleChange={async open => {
                                            if (open) {
                                                if (!leafRef.empty?.(form)) {
                                                    const _values = await leafRef.getList?.({
                                                        form,
                                                        keys,
                                                        ne_id
                                                    });
                                                    setOptions(_values);
                                                }
                                            } else {
                                                setOptions([]);
                                            }
                                        }}
                                        options={options.map(op => {
                                            if (typeof op === "string") {
                                                return {
                                                    label: op,
                                                    value: op
                                                };
                                            }
                                            return {label: op.label, value: op.value};
                                        })}
                                    />
                                ) : (
                                    EditInput({
                                        config: y[k],
                                        useDefine: useDefine?.[k],
                                        form,
                                        datas,
                                        setDatas,
                                        initVals: initialValues,
                                        key: k,
                                        path: [...configPath, k]
                                    })
                                )}
                            </Form.Item>
                            {type === "5" && (
                                <Form.Item
                                    key="state"
                                    name={[...statePath, k]}
                                    style={{
                                        display: "inline-block",
                                        width: k === MIN_FREQ_PARAM ? "40%" : "50%"
                                    }}
                                >
                                    {[...needAscii16FormatKeys, ...needAscii16FormatWithFlagKeys].includes(k) ? (
                                        <AsciiHexAndPlainTextConverter
                                            disabled
                                            defaultValue={getValueByJPath(initialValues, [...statePath, k])}
                                            isContainFlag={needAscii16FormatWithFlagKeys.includes(k)}
                                        />
                                    ) : (
                                        <DisableInput config={y[k]} />
                                    )}
                                </Form.Item>
                            )}
                        </div>
                    </Form.Item>
                ) : (
                    <Form.Item
                        key={k}
                        name={[...statePath, k]}
                        label={getText(k)}
                        tooltip={y[k].description ? y[k].description : false}
                    >
                        {[...needAscii16FormatKeys, ...needAscii16FormatWithFlagKeys].includes(k) ? (
                            <AsciiHexAndPlainTextConverter
                                disabled
                                defaultValue={getValueByJPath(initialValues, [...statePath, k])}
                                isContainFlag={needAscii16FormatWithFlagKeys.includes(k)}
                            />
                        ) : (
                            <DisableInput config={y[k]} />
                        )}
                    </Form.Item>
                );
            });
        if (columnNum === 1) {
            return allItems;
        }
        const oneElementComponents = [];
        const twoElementComponents = [];
        allItems.forEach(i => {
            if (i.props?.children?.length > 1) {
                twoElementComponents.push(i);
            } else {
                oneElementComponents.push(i);
            }
        });
        const leftElementComponents = [];
        const rightElementComponents = [];
        for (let i = 0; i < allItems.length; i++) {
            if (i % 2 === 0) {
                leftElementComponents.push(
                    oneElementComponents.length > 0
                        ? oneElementComponents.splice(0, 1)
                        : twoElementComponents.splice(0, 1)
                );
            } else {
                rightElementComponents.push(
                    twoElementComponents.length > 0
                        ? twoElementComponents.splice(0, 1)
                        : oneElementComponents.splice(0, 1)
                );
            }
        }
        return (
            <div style={{display: "flex", flex: 1}}>
                <div style={{width: "50%", marginRight: 25}}>{leftElementComponents}</div>
                <div style={{width: "50%", marginLeft: 25}}>{rightElementComponents}</div>
            </div>
        );
    };
    const {path, mode, field} = tabs[Object.keys(columnConfig)[0]] ?? tabs[categoryName] ?? tabs[Object.keys(tabs)[0]];
    const statePath = UpperDef || mode === 1 ? path : [...path, "state"];
    // eslint-disable-next-line no-nested-ternary
    const configPath = UpperDef ? configRootPath : mode === 0 ? [...path, "config"] : statePath;
    let y, key;
    if (UpperDef || getValueByJPath(initialValues, statePath) || getValueByJPath(initialValues, configPath)) {
        y = getYangByPath(Yang, UpperDef ? statePath : path);
        key = y.definition.key ? y.definition.key.split(" ") : [];
        if (!UpperDef && mode === 0) y = y.state;
    }
    return initialValues ? (
        <>
            {title || refresh ? (
                <div className={styles.edit_form_header}>
                    <span className={styles.edit_form_header_title}>{title}</span>
                    <div className={styles.edit_form_header_operation}>
                        {refresh ? (
                            <a key="refresh" onClick={fetchData}>
                                {gLabelList.refresh}
                            </a>
                        ) : (
                            ""
                        )}
                    </div>
                </div>
            ) : null}
            <div className={styles.edit_form_content}>
                <Form
                    labelCol={{span: labelCol ?? 10}}
                    wrapperCol={{span: wrapperCol ?? 13}}
                    form={form}
                    onFinish={onFinish}
                    className={styles.edit_form}
                    labelAlign="left"
                >
                    {createTabItems(field, statePath, configPath, mode, key, y, Object.values(columnConfig)[0])}
                </Form>
            </div>
        </>
    ) : (
        <div style={{width: "100%", textAlign: "center"}}>
            <a>
                <LoadingOutlined style={{fontSize: 32, fill: "#14C9BB", color: "#14C9BB"}} />
            </a>
        </div>
    );
};

const openEditComponent = (
    categoryName,
    keys,
    type,
    ne_id,
    initData,
    onUpdate,
    readOnly,
    title,
    columnConfig,
    columnNum = 1
) => {
    if (!categoryName) return;
    apiGetCategory(categoryName, type).then(categoryRs => {
        apiGetYang(type).then(yang => {
            let form;
            // eslint-disable-next-line no-return-assign
            const handle = f => (form = f);
            let modal;
            const afterFinish = success => {
                if (success) {
                    modal.destroy();
                    onUpdate?.();
                } else {
                    modal.update({okButtonProps: {loading: false}});
                }
            };
            modal = (columnNum === 1 ? middleModal : bigModal)?.({
                title: title ?? (Array.isArray(keys) ? keys[keys.length - 1] : keys),
                okText: gLabelList.ok,
                cancelText: gLabelList.cancel,
                // eslint-disable-next-line no-unused-vars
                onOk: _ => {
                    modal.update({okButtonProps: {loading: true}});
                    form.submit();
                },
                okButtonProps: {
                    disabled: readOnly
                },
                content: (
                    <EditForm
                        categoryName={categoryName}
                        setForm={handle}
                        afterFinish={afterFinish}
                        Yang={yang}
                        category={categoryRs}
                        ne_id={ne_id}
                        keys={typeof keys === "string" ? [keys] : keys}
                        initData={initData}
                        type={type}
                        readOnly={readOnly}
                        columnConfig={columnConfig}
                        columnNum={columnNum}
                    />
                )
            });
        });
    });
};

export {openEditComponent};
