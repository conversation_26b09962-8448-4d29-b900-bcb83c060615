import sqlalchemy as sa
from sqlalchemy import func
from sqlalchemy import String, Text, BLOB, DateTime
from sqlalchemy.ext.declarative import declarative_base


time_format = "%Y-%m-%d %H:%M:%S"


class BaseMixin(object):

    create_time = sa.Column(sa.DateTime, default=func.now())
    modified_time = sa.Column(sa.DateTime, default=func.now(), onupdate=func.now())

    def __repr__(self):
        """sqlalchemy based automatic __repr__ method."""
        items = ['%s=%r' % (col.name, getattr(self, col.name))
                 for col in self.__table__.columns]
        return "<%s.%s[object at %x] {%s}>" % (self.__class__.__module__,
                                               self.__class__.__name__,
                                               id(self), ', '.join(items))

    def make_dict(self, ignore_none=True, html_escape=True):
        res_dict = {}
        for col in self.__table__.columns:
            if ignore_none:
                if getattr(self, col.name) is not None:
                    res_dict[col.name] = getattr(self, col.name)
                    if res_dict[col.name] and isinstance(col.type, BLOB):
                        res_dict[col.name] = res_dict[col.name].decode()
                    elif res_dict[col.name] and isinstance(col.type, DateTime):
                        res_dict[col.name] = res_dict[col.name].strftime(time_format)
                    elif html_escape and res_dict[col.name] and isinstance(col.type, String) and not isinstance(col.type, Text):
                        res_dict[col.name] = str(res_dict[col.name])
                else:
                    pass
            else:
                res_dict[col.name] = getattr(self, col.name)
                if res_dict[col.name] and isinstance(col.type, BLOB):
                    res_dict[col.name] = res_dict[col.name].decode()
                elif html_escape and res_dict[col.name] and isinstance(col.type, String) and not isinstance(col.type, Text):
                    res_dict[col.name] = str(res_dict[col.name])
        return res_dict


Base = declarative_base(cls=BaseMixin)
