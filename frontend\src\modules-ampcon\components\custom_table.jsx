import {Button, Divider, Empty, Flex, Form, Input, Modal, Select, Spin, Table, Tabs, Checkbox, Row, Col} from "antd";
import SearchOutlined from "@ant-design/icons/SearchOutlined";
import {useTableInitialElement} from "@/modules-ampcon/hooks/useModalTable";
import React, {forwardRef, useEffect, useImperativeHandle, useMemo, useRef, useState} from "react";
import {useForm} from "antd/es/form/Form";
import Icon from "@ant-design/icons";
import {searchSvg} from "@/utils/common/iconSvg";
import {useDispatch} from "react-redux";
import {updateAlarmSearch, updateAlarmSearchStatus} from "@/store/modules/common/alarm_slice";
import settingGreenSvg from "../pages/Topo/resource/site_green.svg?react";

const handleSearch = (selectedKeys, confirm) => {
    confirm();
};

// this is search style component.
export const TableFilterDropdown = ({
    setSelectedKeys,
    selectedKeys,
    confirm,
    clearFilters,
    defaultValue,
    searchCount
}) => {
    const dispatch = useDispatch();
    useEffect(() => {
        if (defaultValue) {
            setSelectedKeys([defaultValue]);
            handleSearch([defaultValue], confirm);
        } else {
            clearFilters();
            handleSearch(selectedKeys, confirm);
        }
    }, [searchCount + defaultValue]);
    return (
        <div style={{padding: 8}}>
            <Input
                placeholder="Search name"
                value={selectedKeys[0]}
                onChange={e => {
                    dispatch(updateAlarmSearchStatus(false));
                    setSelectedKeys(e.target.value ? [e.target.value] : []);
                }}
                onPressEnter={() => {
                    dispatch(updateAlarmSearchStatus(false));
                    handleSearch(selectedKeys, confirm);
                }}
                style={{width: 188, marginBottom: 8, display: "block"}}
            />
            <Button
                type="primary"
                onClick={() => {
                    dispatch(updateAlarmSearchStatus(false));
                    handleSearch(selectedKeys, confirm);
                }}
                icon={<SearchOutlined />}
                size="small"
                style={{width: 90, marginRight: 8}}
            >
                Search
            </Button>
            <Button
                onClick={() => {
                    clearFilters();
                    dispatch(updateAlarmSearch(""));
                    handleSearch(selectedKeys, confirm);
                }}
                size="small"
                style={{width: 90}}
            >
                Reset
            </Button>
        </div>
    );
};

export const TableSelectFilterDropdown = ({
    setSelectedKeys,
    selectedKeys,
    confirm,
    clearFilters,
    filterOptions,
    filterDefaultValue
}) => {
    useEffect(() => {
        if (filterDefaultValue) {
            setSelectedKeys([filterDefaultValue]);
            handleSearch([filterDefaultValue], confirm);
        } else {
            clearFilters();
            handleSearch(selectedKeys, confirm);
        }
    }, [filterDefaultValue]);
    return (
        <div style={{padding: 8}}>
            <Select
                showSearch
                size="small"
                style={{width: 130, marginRight: 8}}
                onChange={value => {
                    setSelectedKeys(value ? [value] : []);
                    handleSearch(selectedKeys, confirm);
                }}
                // getPopupContainer={trigger => trigger.parentNode}
                // options={[
                //     {
                //         label: "Manager",
                //         options: [
                //             {
                //                 label: "Jack",
                //                 value: "jack"
                //             },
                //             {
                //                 label: "tom3",
                //                 value: "tom3"
                //             }
                //         ]
                //     },
                //     {
                //         label: "Engineer",
                //         options: [
                //             {
                //                 label: "tom6",
                //                 value: "tom6"
                //             }
                //         ]
                //     }
                // ]}
                options={filterOptions}
                value={selectedKeys[0]}
            />
            <Button
                onClick={() => {
                    clearFilters();
                    handleSearch(selectedKeys, confirm);
                }}
                type="primary"
                size="small"
                style={{width: 90}}
            >
                Reset
            </Button>
        </div>
    );
};

// add other style component.

export const createMatchMode = fields => {
    const matchModes = {};
    fields.forEach(field => {
        matchModes[field.name] = field.matchMode;
    });
    return matchModes;
};

export const createFilterFields = (filters, matchModes) => {
    return Object.keys(filters).map(field => {
        const matchMode = matchModes[field] || "exact";
        const fieldFilters = filters[field] || [];
        return {
            field,
            filters: fieldFilters.map(value => ({value, matchMode}))
        };
    });
};
// table change component

export const handleTableChange = async (
    pagination,
    filters,
    sorter,
    setPagination,
    searchFields,
    fetchDataAPI,
    fetchAPIParams,
    setData,
    matchModes,
    setLoading,
    tableSelectedRowKey = null,
    tableSelectedRows = null,
    setTableSelectedRowKey = null,
    setTableSelectedRows = null,
    tableRemovedRowKey = null
) => {
    try {
        setLoading(true);
        const filterFields = createFilterFields(filters, matchModes);
        const sortFields = [];
        if (sorter.field && sorter.order) {
            sortFields.push({
                field: sorter.field,
                order: sorter.order === "ascend" ? "asc" : "desc"
            });
        }

        const response = await fetchDataAPI(
            ...(fetchAPIParams
                ? [...fetchAPIParams, pagination.current, pagination.pageSize, filterFields, sortFields, searchFields]
                : [pagination.current, pagination.pageSize, filterFields, sortFields, searchFields])
        );

        setData(response.data);
        if (tableSelectedRowKey !== null && tableSelectedRows !== null) {
            response.data.forEach(item => {
                if (tableRemovedRowKey.indexOf(item.id) === -1 && tableSelectedRowKey.indexOf(item.id) > -1) {
                    item.selected = true;
                }
            });
            if (response.data.every(item => "selected" in item)) {
                const backendSelectedRowKeys = response.data.filter(item => item.selected).map(item => item.id);
                const frontendSelectedRowKeys = response.data
                    .filter(item => tableSelectedRowKey.indexOf(item.id) > -1)
                    .map(item => item.id);
                const removedRowKeys = response.data
                    .filter(item => tableRemovedRowKey.indexOf(item.id) > -1)
                    .map(item => item.id);
                const selectedRowKeys = Array.from(
                    new Set([...tableSelectedRowKey, ...backendSelectedRowKeys, ...frontendSelectedRowKeys])
                ).filter(itemId => {
                    return removedRowKeys.indexOf(itemId) === -1;
                });
                setTableSelectedRowKey(selectedRowKeys);
                setTableSelectedRows(response.data.filter(item => item.selected));
            }
        }
        setPagination(prev => ({
            ...prev,
            total: response.total,
            current: response.page,
            pageSize: response.pageSize
        }));
    } catch (error) {
        // error
    } finally {
        setLoading(false);
    }
};

export const createColumnWithoutFilter = (title, dataIndex) => ({
    title,
    dataIndex,
    onFilter: () => true,
    sorter: true
});

export const createColumnConfig = (
    title,
    dataIndex,
    filterDropdownComponent,
    defaultValue = "",
    width = null,
    defaultSortOrder = null
) =>
    width === null
        ? {
              title,
              dataIndex,
              filterDropdown: filterDropdownComponent
                  ? props => filterDropdownComponent({...props, defaultValue})
                  : null,
              onFilter: () => true,
              sorter: true,
              defaultSortOrder: defaultSortOrder || null
          }
        : {
              title,
              dataIndex,
              filterDropdown: filterDropdownComponent
                  ? props => filterDropdownComponent({...props, defaultValue})
                  : null,
              onFilter: () => true,
              sorter: true,
              width,
              defaultSortOrder: defaultSortOrder || null
          };

export const createColumnConfigMultipleParams = ({
    title,
    dataIndex,
    enableFilter = true,
    enableSorter = true,
    filterDropdownComponent,
    defaultSortOrder = null,
    ...otherProps
}) =>
    enableFilter
        ? {
              title,
              dataIndex,
              filterDropdown: props => filterDropdownComponent(props),
              onFilter: () => enableFilter,
              sorter: enableSorter,
              defaultSortOrder: defaultSortOrder || null,
              ...otherProps
          }
        : {
              title,
              dataIndex,
              onFilter: () => enableFilter,
              sorter: enableSorter,
              defaultSortOrder: defaultSortOrder || null,
              ...otherProps
          };

export const GlobalSearchInput = ({onChange}) => (
    <Input
        placeholder="Search"
        prefix={<Icon component={searchSvg} />}
        allowClear
        onChange={onChange}
        style={{width: 240, height: "32px", float: "right", borderRadius: "2px"}}
    />
);

export const AmpConCustomTable = forwardRef(
    (
        {
            columns,
            rowSelection,
            matchFieldsList,
            searchFieldsList,
            extraButton,
            helpDraw,
            fetchAPIInfo,
            fetchAPIParams,
            isShowPagination,
            ...props
        },
        ref
    ) => {
        // eslint-disable-next-line no-unused-vars
        const [_, __, searchFields, setSearchFields, data, setData, loading, setLoading, pagination, setPagination] =
            useTableInitialElement(searchFieldsList, false);

        const matchModes = createMatchMode(matchFieldsList || []);

        const [tableSelectedRowKey, setTableSelectedRowKey] = useState(
            rowSelection ? rowSelection.selectedRowKeys : []
        );
        const [tableSelectedRows, setTableSelectedRows] = useState(rowSelection ? rowSelection.selectedRows : []);
        const [tableRemovedRowKey, setTableRemovedRowKey] = useState([]);
        const [tableRemovedRows, setTableRemovedRows] = useState([]);
        const [operations, setOperations] = useState({});
        const dispatch = useDispatch();

        const [sorter, setSorter] = useState({});
        const checkSortedColumn = columns => {
            for (const columnKey in columns) {
                if (Object.prototype.hasOwnProperty.call(columns, columnKey)) {
                    const columnConfig = columns[columnKey];
                    // check each column has defaultSortOrder of descend
                    if (columnConfig.defaultSortOrder === "descend") {
                        return columnConfig.dataIndex;
                    }
                }
            }
            return undefined;
        };
        const [filters, setFilters] = useState({});

        const handleSelect = (record, selected) => {
            const keys = selected
                ? tableSelectedRowKey.concat([record.id])
                : tableSelectedRowKey.filter(item => item !== record.id);

            if (!selected) {
                setTableRemovedRowKey([...tableRemovedRowKey, record.id]);
                setTableRemovedRows([...tableRemovedRows, record]);
            } else {
                setTableRemovedRowKey(tableRemovedRowKey.filter(item => item !== record.id));
                setTableRemovedRows(tableRemovedRows.filter(item => item.id !== record.id));
            }

            const rows = selected
                ? [...tableSelectedRows, record]
                : tableSelectedRows.filter(item => item.id !== record.id);

            if (Object.prototype.hasOwnProperty.call(record, "selected")) {
                const operationsTemp = operations;
                if (record.selected === false && selected) {
                    operationsTemp[record.id] = "add";
                } else if (record.selected === false && !selected) {
                    delete operationsTemp[record.id];
                } else if (record.selected === true && selected) {
                    delete operationsTemp[record.id];
                } else if (record.selected === true && !selected) {
                    operationsTemp[record.id] = "remove";
                }
                setOperations(operationsTemp);
            }

            rows.forEach(row => {
                if (row.children) {
                    const isInChildren = row.children.some(child => child.id === record.id);
                    if (isInChildren) {
                        const rowIndex = rows.findIndex(r => r.id === row.id);
                        if (rowIndex > -1) {
                            rows.splice(rowIndex, 1);
                        }

                        const keyIndex = keys.findIndex(k => k === row.id);
                        if (keyIndex > -1) {
                            keys.splice(keyIndex, 1);
                        }
                    }
                }
            });

            if (Object.prototype.hasOwnProperty.call(record, "children")) {
                if (selected) {
                    record.children.forEach(child => {
                        if (!keys.includes(child.id)) {
                            keys.push(child.id);
                        }
                        if (!rows.some(row => row.id === child.id)) {
                            rows.push(child);
                        }
                    });
                } else {
                    const needRemoveRows = record.children.map(child => child.id);
                    needRemoveRows.forEach(id => {
                        const rowIndex = rows.findIndex(r => r.id === id);
                        rows.splice(rowIndex, 1);
                        const keyIndex = keys.findIndex(k => k === id);
                        keys.splice(keyIndex, 1);
                    });
                }
            }
            setTableSelectedRowKey(keys);
            setTableSelectedRows(rows);

            if (rowSelection && rowSelection.onChange) {
                rowSelection.onChange(keys, rows);
            }
        };

        const handleSelectAll = (selected, selectedRows, changeRows) => {
            const ids = changeRows.map(item => item.id);
            const keys = selected
                ? tableSelectedRowKey.concat(ids)
                : tableSelectedRowKey.filter(item => !ids.includes(item));
            setTableSelectedRowKey(keys);

            if (!selected) {
                setTableRemovedRowKey([...tableRemovedRowKey, ...ids]);
                setTableRemovedRows([...tableRemovedRows, ...changeRows]);
            } else {
                setTableRemovedRowKey(tableRemovedRowKey.filter(item => !ids.includes(item)));
                setTableRemovedRows(tableRemovedRows.filter(item => !ids.includes(item.id)));
            }
            const rows = selected
                ? [...tableSelectedRows, ...changeRows]
                : tableSelectedRows.filter(item => !ids.includes(item.id));
            setTableSelectedRows(rows);

            // for default selected rows
            if (Object.prototype.hasOwnProperty.call(changeRows[0], "selected")) {
                changeRows.map(record => {
                    const operationsTemp = operations;
                    if (record.selected === false && selected) {
                        operationsTemp[record.id] = "add";
                    } else if (record.selected === false && !selected) {
                        delete operationsTemp[record.id];
                    } else if (record.selected === true && selected) {
                        delete operationsTemp[record.id];
                    } else if (record.selected === true && !selected) {
                        operationsTemp[record.id] = "remove";
                    }
                    setOperations(operationsTemp);
                });
            }

            if (rowSelection && rowSelection.onChange) {
                rowSelection.onChange(keys, rows);
            }
        };

        const tableRowSelection =
            rowSelection && rowSelection.type === "radio"
                ? rowSelection
                : {
                      selectedRowKeys: tableSelectedRowKey,
                      onSelect: handleSelect,
                      onSelectAll: handleSelectAll,
                      getCheckboxProps: rowSelection?.getCheckboxProps,
                      fixed: rowSelection?.fixed,
                      checkStrictly: rowSelection?.checkStrictly
                  };

        useImperativeHandle(ref, () => ({
            refreshTable() {
                fetchData().then();
            },
            setTableLoading(value) {
                setLoading(value);
            },
            getSelectedRow: () => {
                return {tableSelectedRowKey, tableSelectedRows};
            },
            getRemovedRow: () => {
                return {tableRemovedRowKey, tableRemovedRows};
            },
            clearSelectedRow: () => {
                setTableSelectedRowKey([]);
                setTableSelectedRows([]);
            },
            getOperations: () => {
                return operations;
            },
            getTableData: () => {
                return data;
            }
        }));

        const fetchData = async () => {
            setLoading(true);

            const filterFields = filters ? createFilterFields(filters, matchModes) : [];
            const sortFields = [];
            if (sorter.field && sorter.order) {
                sortFields.push({
                    field: sorter.field,
                    order: sorter.order === "ascend" ? "asc" : "desc"
                });
            }

            try {
                let response = await fetchAPIInfo(
                    ...(fetchAPIParams
                        ? [
                              ...fetchAPIParams,
                              pagination.current,
                              pagination.pageSize,
                              filterFields,
                              sortFields,
                              searchFields
                          ]
                        : [pagination.current, pagination.pageSize, filterFields, sortFields, searchFields])
                );
                if (response.data.length === 0 && response.total !== 0) {
                    response = await fetchAPIInfo(
                        ...(fetchAPIParams
                            ? [
                                  ...fetchAPIParams,
                                  Math.ceil(response.total / response.pageSize),
                                  pagination.pageSize,
                                  [],
                                  [],
                                  searchFields
                              ]
                            : [
                                  Math.ceil(response.total / response.pageSize),
                                  pagination.pageSize,
                                  [],
                                  [],
                                  searchFields
                              ])
                    );
                }
                setData(response.data);
                if (response.data.every(item => "selected" in item)) {
                    const backendSelectedRowKeys = response.data.filter(item => item.selected).map(item => item.id);
                    const frontendSelectedRowKeys = response.data
                        .filter(item => tableSelectedRowKey.indexOf(item.id) > -1)
                        .map(item => item.id);
                    const removedRowKeys = response.data
                        .filter(item => tableRemovedRowKey.indexOf(item.id) > -1)
                        .map(item => item.id);
                    const selectedRowKeys = Array.from(
                        new Set([...tableSelectedRowKey, ...backendSelectedRowKeys, ...frontendSelectedRowKeys])
                    ).filter(itemId => {
                        return removedRowKeys.indexOf(itemId) === -1;
                    });
                    setTableSelectedRowKey(selectedRowKeys);
                    setTableSelectedRows(response.data.filter(item => item.selected));
                }
                setPagination(prev => ({
                    ...prev,
                    total: response.total,
                    current: response.page,
                    pageSize: response.pageSize
                }));
            } catch (error) {
                // error
            } finally {
                setLoading(false);
            }
        };

        useEffect(() => {
            const sortedColumn = checkSortedColumn(columns);
            if (sortedColumn) {
                sorter.field = sortedColumn;
                sorter.order = "descend";
                tableChange("", "", sorter);
            }
        }, []);
        useEffect(() => {
            // if (!props.readTag)
            fetchData().then();
        }, [JSON.stringify(fetchAPIParams), JSON.stringify(searchFields)]);

        const handleSearchChange = e => {
            dispatch(updateAlarmSearchStatus(false));
            dispatch(updateAlarmSearch(""));
            if (tableRowSelection && tableRowSelection.selectedRowKeys) {
                tableRowSelection.selectedRowKeys = [];
            }
            setTableSelectedRows([]);
            setTableSelectedRowKey([]);
            setSearchFields({
                fields: searchFieldsList,
                value: e.target.value
            });
        };

        const tableChange = async (pagination, filters, sorter) => {
            const delay = ms =>
                new Promise(resolve => {
                    setTimeout(resolve, ms);
                });
            await delay(100);
            setSorter(sorter);
            setFilters(filters);
            await handleTableChange(
                pagination,
                filters,
                sorter,
                setPagination,
                searchFields,
                fetchAPIInfo,
                fetchAPIParams,
                setData,
                matchModes,
                setLoading,
                tableSelectedRowKey,
                tableSelectedRows,
                setTableSelectedRowKey,
                setTableSelectedRows,
                tableRemovedRowKey
            );
        };

        return (
            <div>
                <Flex vertical>
                    <Flex gap="middle" style={{marginBottom: "20px"}}>
                        {extraButton}
                        <div style={{flexGrow: 1}} />
                        {searchFieldsList ? <GlobalSearchInput onChange={handleSearchChange} /> : null}
                        {helpDraw}
                    </Flex>
                    <Table
                        rowSelection={rowSelection ? tableRowSelection : null}
                        columns={columns}
                        bordered
                        rowKey={record => record.id}
                        loading={loading}
                        dataSource={data}
                        pagination={searchFieldsList || isShowPagination ? pagination : false}
                        onChange={tableChange}
                        {...props}
                    />
                </Flex>
            </div>
        );
    }
);

export const AmpConCustomModalTable = forwardRef(
    (
        {
            title,
            selectModalOpen,
            onCancel,
            columns,
            matchFieldsList,
            searchFieldsList,
            buttonProps,
            fetchAPIInfo,
            fetchAPIParams,
            rowSelection,
            footer,
            modalClass = ""
        },
        ref
    ) => {
        const tableRef = useRef(null);
        useImperativeHandle(ref, () => ({
            getTableRef() {
                return tableRef;
            }
        }));

        return (
            <Modal
                className={modalClass || ""}
                title={
                    typeof title === "string" ? (
                        <div>
                            {title}
                            <Divider style={{marginTop: 8, marginBottom: 0}} />
                        </div>
                    ) : (
                        title
                    )
                }
                open={selectModalOpen}
                onCancel={onCancel}
                destroyOnClose
                footer={footer || null}
            >
                <AmpConCustomTable
                    extraButton={buttonProps}
                    columns={columns}
                    searchFieldsList={searchFieldsList}
                    matchFieldsList={matchFieldsList}
                    fetchAPIInfo={fetchAPIInfo}
                    fetchAPIParams={fetchAPIParams}
                    rowSelection={rowSelection}
                    ref={tableRef}
                />
            </Modal>
        );
    }
);

export const AmpConCustomModalForm = ({
    title,
    isModalOpen,
    formInstance,
    layoutProps,
    CustomFormItems,
    onCancel,
    onSubmit,
    isShowSpin = false,
    modalClass = ""
}) => (
    <Modal
        title={
            typeof title === "string" ? (
                <div>
                    {title}
                    <Divider style={{marginTop: 8, marginBottom: 0}} />
                </div>
            ) : (
                title
            )
        }
        open={isModalOpen}
        onCancel={onCancel}
        onOk={formInstance.submit}
        destroyOnClose
        className={modalClass || ""}
        footer={[
            <Divider style={{marginTop: 0, marginBottom: 20}} />,
            <Button key="cancel" onClick={onCancel}>
                Cancel
            </Button>,
            <Button key="ok" type="primary" onClick={formInstance.submit}>
                OK
            </Button>
        ]}
    >
        {/* <Divider /> */}
        <Form
            layout="horizontal"
            form={formInstance}
            onFinish={onSubmit}
            {...layoutProps}
            validateTrigger="onBlur"
            labelAlign="left"
            style={{
                minHeight: modalClass === "ampcon-middle-modal" ? "268px" : "auto"
            }}
        >
            {CustomFormItems}
        </Form>
        <Spin spinning={isShowSpin} tip="Loading..." fullscreen />
    </Modal>
);

export const AmpConCustomModalTABTable = ({
    title,
    selectModalOpen,
    onCancel,
    items,
    modalClass = "",
    footer = null
}) => (
    <Modal
        title={
            typeof title === "string" ? (
                <div>
                    {title}
                    <Divider style={{marginTop: 8, marginBottom: 0}} />
                </div>
            ) : (
                title
            )
        }
        open={selectModalOpen}
        onCancel={onCancel}
        destroyOnClose
        footer={footer || null}
        className={modalClass || ""}
    >
        <Tabs className="radioGroupTabs" items={items} />
    </Modal>
);

export const AmpConCustomModal = ({title, childItems, isModalOpen, onCancel, modalClass = "", footer}) => (
    <Modal
        title={
            typeof title === "string" ? (
                <div>
                    {title}
                    <Divider style={{marginTop: 8, marginBottom: 0}} />
                </div>
            ) : (
                title
            )
        }
        open={isModalOpen}
        onCancel={onCancel}
        destroyOnClose
        footer={footer || null}
        className={modalClass || ""}
    >
        {childItems}
    </Modal>
);

export const AmpConCustomStaticTable = forwardRef(
    ({columns, rowSelection, extraButton, helpDraw, data, filterFunc, ...props}, ref) => {
        // eslint-disable-next-line no-unused-vars
        const [_, __, searchFields, setSearchFields, ___, ____, loading, setLoading, pagination, setPagination] =
            useTableInitialElement([], false);

        const [tableData, setTableData] = useState([]);

        const [tableSelectedRowKey, setTableSelectedRowKey] = useState(
            rowSelection ? rowSelection.selectedRowKeys : []
        );
        const [tableSelectedRows, setTableSelectedRows] = useState(rowSelection ? rowSelection.selectedRows : []);
        const [operations, setOperations] = useState({});

        const handleSelect = (record, selected) => {
            const keys = selected
                ? tableSelectedRowKey.concat([record.id])
                : tableSelectedRowKey.filter(item => item !== record.id);
            setTableSelectedRowKey(keys);

            const rows = selected
                ? [...tableSelectedRows, record]
                : tableSelectedRows.filter(item => item.id !== record.id);
            setTableSelectedRows(rows);

            if (Object.prototype.hasOwnProperty.call(record, "selected")) {
                const operationsTemp = operations;
                if (record.selected === false && selected) {
                    operationsTemp[record.id] = "add";
                } else if (record.selected === false && !selected) {
                    delete operationsTemp[record.id];
                } else if (record.selected === true && selected) {
                    delete operationsTemp[record.id];
                } else if (record.selected === true && !selected) {
                    operationsTemp[record.id] = "remove";
                }
                setOperations(operationsTemp);
            }

            if (rowSelection && rowSelection.onChange) {
                rowSelection.onChange(keys, rows);
            }
        };

        const handleSelectAll = (selected, selectedRows, changeRows) => {
            const ids = changeRows.map(item => item.id);
            const keys = selected
                ? tableSelectedRowKey.concat(ids)
                : tableSelectedRowKey.filter(item => !ids.includes(item));
            setTableSelectedRowKey(keys);

            const rows = selected
                ? [...tableSelectedRows, ...changeRows]
                : tableSelectedRows.filter(item => !ids.includes(item.id));
            setTableSelectedRows(rows);

            // for default selected rows
            if (Object.prototype.hasOwnProperty.call(changeRows[0], "selected")) {
                changeRows.map(record => {
                    const operationsTemp = operations;
                    if (record.selected === false && selected) {
                        operationsTemp[record.id] = "add";
                    } else if (record.selected === false && !selected) {
                        delete operationsTemp[record.id];
                    } else if (record.selected === true && selected) {
                        delete operationsTemp[record.id];
                    } else if (record.selected === true && !selected) {
                        operationsTemp[record.id] = "remove";
                    }
                    setOperations(operationsTemp);
                });
            }

            if (rowSelection && rowSelection.onChange) {
                rowSelection.onChange(keys, rows);
            }
        };

        const tableRowSelection =
            rowSelection && rowSelection.type === "radio"
                ? rowSelection
                : {
                      selectedRowKeys: tableSelectedRowKey,
                      onSelect: handleSelect,
                      onSelectAll: handleSelectAll,
                      getCheckboxProps:
                          rowSelection === null || rowSelection === undefined ? null : rowSelection.getCheckboxProps
                  };

        const formatData = async () => {
            setLoading(true);
            try {
                const paginated = data.data.slice(
                    (pagination.current - 1) * pagination.pageSize,
                    pagination.current * pagination.pageSize
                );
                setTableData(paginated);
                setPagination(prev => ({
                    ...prev,
                    total: data.total
                }));
            } catch (error) {
                // error
            } finally {
                setLoading(false);
            }
        };

        useEffect(() => {
            formatData().then();
            // console.log(data);
        }, [data]);

        const handleSearchChange = e => {
            const searchData = data.data.filter(obj => {
                return Object.values(obj).some(value => {
                    if (typeof value === "string") {
                        return value.toLowerCase().includes(e.target.value.toLowerCase());
                    }
                });
            });
            setTableData(searchData);
        };

        const tableChange = async (pagination, filters, sorter) => {
            if (data.data) {
                const sortedData = data.data.sort((a, b) => {
                    if (sorter.column) {
                        if (sorter.order === "ascend") {
                            return a[sorter.column] - b[sorter.column];
                        }
                        return b[sorter.column] - a[sorter.column];
                    }
                    return 0;
                });

                let filterData = sortedData;
                if (filterFunc) {
                    filterData = filterFunc(sortedData, filters);
                    // console.log(filterData);
                }

                setPagination(prev => ({
                    ...prev,
                    current: pagination.current,
                    pageSize: pagination.pageSize
                }));

                const paginated = filterData.slice(
                    (pagination.current - 1) * pagination.pageSize,
                    pagination.current * pagination.pageSize
                );
                setTableData(paginated);
            }
        };

        useImperativeHandle(ref, () => ({
            getSelectedRow: () => {
                return {tableSelectedRowKey, tableSelectedRows};
            },
            clearSelectedRow: () => {
                setTableSelectedRowKey([]);
                setTableSelectedRows([]);
            }
        }));

        return (
            <div>
                <Flex vertical>
                    <Flex gap="middle" style={{marginBottom: "20px"}}>
                        {extraButton}
                        <div style={{flexGrow: 1}} />
                        <GlobalSearchInput onChange={handleSearchChange} />
                        {helpDraw}
                    </Flex>
                    <Table
                        rowSelection={rowSelection ? tableRowSelection : null}
                        columns={columns}
                        bordered
                        rowKey={record => record.id}
                        loading={loading}
                        dataSource={tableData}
                        pagination={pagination}
                        onChange={tableChange}
                        {...props}
                    />
                </Flex>
            </div>
        );
    }
);

export const AmpConCustomTelemteryTable = ({columnsConfig, data, tableWidth, showSetting = true}) => {
    const defaultCheckedList = columnsConfig.map(item => item.dataIndex);
    const [form] = useForm();
    const [isSelectColumnsModalOpen, setSelectColumnsModalOpen] = useState(false);
    const [columnsList, setColumnsList] = useState(defaultCheckedList);
    const [selectedColumns, setSelectedColumns] = useState(defaultCheckedList);

    const handleSelectAllColumns = () => {
        if (selectedColumns.length === columnsConfig.length) {
            setSelectedColumns(
                columnsConfig.filter(column => column.dataIndex === "name").map(column => column.dataIndex)
            );
        } else {
            setSelectedColumns(columnsConfig.map(column => column.dataIndex));
        }
    };

    const handleSelectColumn = value => {
        setSelectedColumns(value);
    };

    const formItems = () => {
        return (
            <Form.Item name="columns" label=" ">
                <Checkbox
                    indeterminate={selectedColumns.length > 0 && selectedColumns.length < columnsConfig.length}
                    checked={selectedColumns.length === columnsConfig.length}
                    onChange={handleSelectAllColumns}
                    style={{marginBottom: 8}}
                >
                    Select All
                </Checkbox>
                <Checkbox.Group value={selectedColumns} onChange={handleSelectColumn}>
                    <Row gutter={[16, 8]}>
                        <>
                            {columnsConfig.map(column => (
                                <Col span={12}>
                                    <Checkbox value={column.dataIndex} disabled={column.dataIndex === "name"}>
                                        {column.title}
                                    </Checkbox>
                                </Col>
                            ))}
                        </>
                    </Row>
                </Checkbox.Group>
            </Form.Item>
        );
    };

    const onChangeColumns = () => {
        setColumnsList(selectedColumns);
        setSelectColumnsModalOpen(false);
    };

    return (
        <div>
            <Flex vertical>
                {showSetting ? (
                    <div style={{display: "flex", justifyContent: "flex-end", marginBottom: "10px"}}>
                        <Button
                            icon={<Icon component={settingGreenSvg} />}
                            onClick={() => setSelectColumnsModalOpen(true)}
                        />
                    </div>
                ) : null}
                <Table
                    columns={columnsConfig.filter(item => columnsList.includes(item.dataIndex))}
                    bordered
                    dataSource={data}
                    scroll={{
                        y: "100%"
                    }}
                    sticky
                    style={{width: tableWidth, marginBottom: "24px"}}
                    pagination={false}
                />
            </Flex>
            <AmpConCustomModalForm
                title="All Columns"
                isModalOpen={isSelectColumnsModalOpen}
                formInstance={form}
                layoutProps={{
                    labelCol: {
                        span: 3
                    }
                }}
                CustomFormItems={formItems}
                onCancel={() => {
                    setSelectedColumns(columnsList);
                    setSelectColumnsModalOpen(false);
                }}
                onSubmit={onChangeColumns}
                modalClass="ampcon-middle-modal"
            />
        </div>
    );
};
