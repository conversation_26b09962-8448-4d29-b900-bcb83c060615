import {<PERSON><PERSON>, <PERSON><PERSON><PERSON>, Flex, message} from "antd";
import {forwardRef, useImperativeHandle, useRef, useState} from "react";
import {
    AmpConCustomModalTable,
    createColumnConfigMultipleParams,
    TableFilterDropdown,
    AmpConCustomModalTABTable,
    AmpConCustomTable
} from "@/modules-ampcon/components/custom_table";
import {fetchAddDeviceModalTableData} from "@/modules-ampcon/apis/monitor_api";
import {objectGet} from "@/modules-otn/apis/api";

async function getNeInfo(otnlist) {
    const filter = {type: 5};
    const response = await objectGet("config:ne", filter);

    const {apiResult, apiMessage, documents, total} = response;
    if (apiResult === "fail") {
        message.error(apiMessage).then();
        return;
    }
    const _list = documents.map(item => {
        return {
            id: item.value.name,
            label: item.value.name,
            value: item.value.ne_id,
            type: item.value.type,
            disabled: item.value.type === "2" && item.value.runState === 0,
            selected: otnlist.includes(item.value.name)
        };
    });
    _list.sort((a, b) => {
        if (a.selected && !b.selected) {
            return -1;
        }
        if (!a.selected && b.selected) {
            return 1;
        }
        return a.label.localeCompare(b.label, "zh-CN", {numeric: true});
    });
    return {
        data: _list,
        total
    };
}

const AddDeviceModal = forwardRef((props, ref) => {
    useImperativeHandle(ref, () => ({
        showAddDeviceModal: (snList, otnList) => {
            setSwitchSNList(snList);
            setOTNList(otnList);
            setIsShowModal(true);
        },
        hideAddDeviceModal: () => {
            setIsShowModal(false);
        }
    }));

    const title = "Add Device";
    const {updateNodesCallback} = props;

    const matchFieldsList = [
        {name: "host_name", matchMode: "fuzzy"},
        {name: "sn", matchMode: "fuzzy"},
        {name: "platform_model", matchMode: "fuzzy"},
        {name: "status", matchMode: "fuzzy"},
        {name: "mgt_ip", matchMode: "fuzzy"}
    ];
    const searchFieldsList = ["host_name", "sn", "platform_model", "status", "mgt_ip"];

    const tableModalRef = useRef(null);

    const switchTableModalRef = useRef(null);
    const otnTableModalRef = useRef(null);

    const [isShowModal, setIsShowModal] = useState(false);

    const [switchSNList, setSwitchSNList] = useState([]);

    const [otnList, setOTNList] = useState([]);

    const switchColumns = [
        createColumnConfigMultipleParams({
            title: "Switch Name",
            dataIndex: "host_name",
            filterDropdownComponent: TableFilterDropdown,
            width: "15%"
        }),
        createColumnConfigMultipleParams({
            title: "SN / Service Tag",
            dataIndex: "sn",
            filterDropdownComponent: TableFilterDropdown,
            width: "15%"
        }),
        createColumnConfigMultipleParams({
            title: "Model",
            dataIndex: "platform_model",
            filterDropdownComponent: TableFilterDropdown,
            width: "15%"
        }),
        createColumnConfigMultipleParams({
            title: "Version",
            width: "15%",
            enableSorter: false,
            enableFilter: false,
            render: (_, record) => {
                if (record.revision === null) {
                    return "";
                }
                if (record.version === null) {
                    return `${record.version}`;
                }
                return `${record.version}/${record.revision}`;
            }
        }),
        createColumnConfigMultipleParams({
            title: "Status",
            dataIndex: "status",
            filterDropdownComponent: TableFilterDropdown,
            width: "15%"
        }),
        createColumnConfigMultipleParams({
            title: "Mgmt",
            dataIndex: "mgt_ip",
            filterDropdownComponent: TableFilterDropdown,
            width: "15%"
        })
    ];

    const otnColumns = [
        createColumnConfigMultipleParams({
            title: "Name",
            dataIndex: "label",
            filterDropdownComponent: TableFilterDropdown
            // width: "15%"
        }),
        createColumnConfigMultipleParams({
            title: "Ip",
            dataIndex: "value",
            filterDropdownComponent: TableFilterDropdown
            // width: "15%"
        })
    ];

    const tabItems = [
        {
            key: "switch_table",
            label: "Switch Table",
            children: (
                <AmpConCustomTable
                    ref={switchTableModalRef}
                    columns={switchColumns}
                    searchFieldsList={searchFieldsList}
                    matchFieldsList={matchFieldsList}
                    fetchAPIInfo={fetchAddDeviceModalTableData}
                    fetchAPIParams={[switchSNList]}
                    rowSelection={{
                        selectedRowKeys: [],
                        selectedRows: [],
                        onChange: () => {}
                    }}
                />
            )
        },
        {
            key: "otn_table",
            label: "OTN Table",
            children: (
                <AmpConCustomTable
                    ref={otnTableModalRef}
                    columns={otnColumns}
                    fetchAPIInfo={getNeInfo}
                    fetchAPIParams={[otnList]}
                    rowSelection={{
                        selectedRowKeys: [],
                        selectedRows: [],
                        onChange: () => {}
                    }}
                    isShowPagination
                />
            )
        }
    ];

    return import.meta.env.VITE_APP_EXPORT_MODULE === "AmpCon-SUPER" ? (
        <AmpConCustomModalTABTable
            title={title}
            selectModalOpen={isShowModal}
            onCancel={() => {
                setIsShowModal(false);
            }}
            items={tabItems}
            modalClass="ampcon-max-modal"
            footer={
                <Flex vertical>
                    <Divider />
                    <Flex justify="flex-end">
                        <Button
                            htmlType="button"
                            onClick={() => {
                                setIsShowModal(false);
                            }}
                        >
                            Cancel
                        </Button>
                        <Button
                            type="primary"
                            onClick={() => {
                                setIsShowModal(false);
                                updateNodesCallback(
                                    switchTableModalRef.current.getOperations(),
                                    otnTableModalRef.current ? otnTableModalRef.current.getOperations() : []
                                );
                            }}
                        >
                            Save
                        </Button>
                    </Flex>
                </Flex>
            }
        />
    ) : (
        <AmpConCustomModalTable
            modalClass="ampcon-max-modal"
            ref={tableModalRef}
            title={title}
            rowSelection={{
                selectedRowKeys: [],
                selectedRows: [],
                onChange: () => {}
            }}
            selectModalOpen={isShowModal}
            onCancel={() => {
                setIsShowModal(false);
            }}
            columns={switchColumns}
            matchFieldsList={matchFieldsList}
            searchFieldsList={searchFieldsList}
            buttonProps={[]}
            fetchAPIInfo={fetchAddDeviceModalTableData}
            fetchAPIParams={[switchSNList]}
            footer={
                <Flex vertical>
                    <Divider />
                    <Flex justify="flex-end">
                        <Button
                            htmlType="button"
                            onClick={() => {
                                setIsShowModal(false);
                            }}
                        >
                            Cancel
                        </Button>
                        <Button
                            type="primary"
                            onClick={() => {
                                setIsShowModal(false);
                                updateNodesCallback(tableModalRef.current.getTableRef().current.getOperations(), []);
                            }}
                        >
                            Save
                        </Button>
                    </Flex>
                </Flex>
            }
        />
    );
});

export default AddDeviceModal;
