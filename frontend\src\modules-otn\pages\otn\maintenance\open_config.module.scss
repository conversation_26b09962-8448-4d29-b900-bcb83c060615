.tree {
    background: #fff;
    border-radius: 5px;
    width: 20%;
}

.container {
    display: flex;
    width: 80%;
    background: #fff;
    border-radius: 5px;
    padding: 24px;
    // overflow: auto;
    min-width: 100px;

    .content {
        display: flex;
        flex-direction: column;
        flex: 1;
        min-width: 0;

        :global {
            .ant-tabs-tab {
                border: none !important;
                padding: 0 !important;
                margin-right: 32px !important;
                font-weight: 600 !important;
                font-size: 18px !important;
                color: #929A9E !important;
                padding-bottom: 8px !important;
                border-bottom: 3px solid #FFFFFF !important;
            }

            .ant-tabs-tab:hover {
                color: #14C9BB !important;
            }

            .ant-tabs-tab-active {
                background: #FFFFFF !important;
                color: #14C9BB !important;
                border-bottom: 3px solid #14C9BB !important;
            }

            div[class*="edit_form_edit_form_content"] {
                // margin: 0 4px;
                overflow: hidden;
                margin-top: 24px;
                height: 100%;
                border-radius: 5px;

                & > form {
                    height: 100% !important;

                    .ant-tabs-nav {
                        margin: 0 24px 24px 24px !important;
                    }

                    .ant-tabs-content {
                        padding: 0 24px;
                        overflow: auto;

                        & > div {
                            overflow: unset;
                        }
                    }
                }
            }

            .ant-tabs-nav {
                margin-bottom: 24px;

                .ant-tabs-nav-operations .ant-tabs-nav-more .anticon-ellipsis {
                    font-size: 20px;
                }
            }

            #formPanel-panel-component,
            #formPanel-panel-breakout-mode,
            #formPanel-panel-supported-layer-protocol-names,
            #formPanel-panel-port {
                padding: 0;
            }


        }
    }
}
