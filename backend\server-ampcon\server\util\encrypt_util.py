import base64
from Crypto.Cipher import AES
from Crypto import Random
import os

from sqlalchemy import <PERSON><PERSON><PERSON>, Integer, String

from server.db.db_common import DBCommon
from server.db.models.base import Base


class EncryptKey(Base):
    __tablename__ = 'encrypt_key'
    id = Column(Integer, primary_key=True, autoincrement=True)
    encrypt_key = Column(String(32), nullable=False)
    used = Column(Integer)


class AESCipher:
    def __init__(self):
        """
        CBC
        """
        self.session = None
        self.is_locked = False
        self.unlock()
        self.key = self._check_key(self.get_key())
        self.BS = 16
        # CBC mode, iv 16-bit bytes
        self.mode = AES.MODE_CBC
        # Padding function to fill up encrypt str
        self._add_padding = lambda s: s + (self.BS - len(s.encode()) % self.BS) * chr(
            self.BS - len(s.encode()) % self.BS)
        # Drop padding function Remove filled data
        self._drop_padding = lambda s: s[:-ord(s[len(s) - 1:])]

    def get_key(self):
        self.session = DBCommon.get_session()
        return self.session.query(EncryptKey).first().encrypt_key

    def lock(self):
        self.session = DBCommon.get_session()
        self.session.query(EncryptKey).update({EncryptKey.used: 1}, synchronize_session=False)
        self.is_locked = True

    def unlock(self):
        self.session = DBCommon.get_session()
        self.session.query(EncryptKey).update({EncryptKey.used: 0}, synchronize_session=False)
        self.is_locked = False

    def update_key(self, key):
        self.session = DBCommon.get_session()
        self.session.query(EncryptKey).update({EncryptKey.encrypt_key: key}, synchronize_session=False)
        self._refresh_key()

    def _refresh_key(self):
        self.key = self._check_key(self.session.query(EncryptKey).first().encrypt_key)

    @staticmethod
    def _check_key(key):
        try:
            if isinstance(key, bytes):
                assert len(key) in [32]
                return key
            elif isinstance(key, str):
                assert len(key.encode()) in [32]
                return key.encode()
            else:
                raise Exception(f'The key must be str or bytes, not {type(key)}')
        except AssertionError:
            print('The length of the key should be 32 bits')

    @staticmethod
    def _check_data(data):
        """
        Detect encrypted data types
        """
        if isinstance(data, int):
            data = str(data)
        elif isinstance(data, bytes):
            data = data.decode()
        elif isinstance(data, str):
            pass
        else:
            raise Exception(f'The key must be str or bytes, not {type(data)}')
        return data

    def encrypt(self, raw):
        self._refresh_key()
        if not raw:
            return ''
        if self.is_locked:
            raise Exception('Changing encrypt key, cannot encrypt!')
        raw = self._check_data(raw)
        raw = self._add_padding(raw).encode()
        # get iv randomly
        iv = Random.new().read(AES.block_size)
        cipher = AES.new(self.key, self.mode, iv)
        # Here is the base64 decryption of the ciphertext and iv, which can be decrypted according to this iv
        return base64.b64encode(iv + cipher.encrypt(raw)).decode()

    def decrypt(self, enc):
        self._refresh_key()
        if not enc:
            return ''
        if self.is_locked:
            raise Exception('Changing encrypt key, cannot decrypt!')
        # First base64 decode the ciphertext
        if len(enc) % 4:
            # not a multiple of 4, add padding:
            enc += '=' * (4 - len(enc) % 4)
        enc = base64.b64decode(enc)
        # get iv
        iv = enc[:self.BS]
        cipher = AES.new(self.key, self.mode, iv)
        # Returns data in utf-8
        return self._drop_padding(cipher.decrypt(enc[self.BS:])).decode()

    @staticmethod
    def remove_temp_file(path):
        if os.path.exists(path):
            os.remove(path)


def encrypt_with_static_key(raw):
    BS = 16
    add_padding = lambda s: s + (BS - len(s.encode()) % BS) * chr(
            BS - len(s.encode()) % BS)
    mode = AES.MODE_CBC
    key = "pica8pica8                      ".encode()
    if not raw:
        return ''
    # raw = self._check_data(raw)
    raw = add_padding(raw).encode()
    # get iv randomly
    iv = Random.new().read(AES.block_size)
    cipher = AES.new(key, mode, iv)
    # Here is the base64 decryption of the ciphertext and iv, which can be decrypted according to this iv
    return base64.b64encode(iv + cipher.encrypt(raw)).decode()

aes_cipher = AESCipher()


if __name__ == '__main__':
    r = aes_cipher.encrypt('pica8\n' * 500)
    # aes_cipher.update_key(('pica8'*32)[:32])
    # aes_cipher.lock()
    # aes_cipher.unlock()
    r = aes_cipher.decrypt(r)
    print(r)
