.switchView {
    display: grid;
    height: 100%;
    width: 100%;
    gap: 18px 24px;
    grid-template-columns: repeat(12, 1fr);
    grid-template-rows: 0.75fr 1fr 1fr 1fr;
    > div {

        &:nth-child(1) {
            grid-column: 1 / 10;
        }
        &:nth-child(2) {
            grid-column: span 3;
        
        }
        &:nth-child(3) {
            grid-column: 1 / 13;
        }
        &:nth-child(4),
        &:nth-child(5),
        &:nth-child(6),
        &:nth-child(7) {
            grid-column: span 4;
        }

        &:nth-child(8) {
            grid-column: 5 / 13;
        }

    }
}

.card_title {
    font-size: 18px;
}

// .configTemplateItem {
//     color: #212519 ;
//     font-weight:bold;
  
//     &:hover {
//       color: #34dccf;
//     }
//   }
