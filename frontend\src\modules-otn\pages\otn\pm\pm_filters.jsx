import {useState, forwardRef, useImperative<PERSON><PERSON>le, useRef, useMemo, useLayoutEffect} from "react";
import {Select, DatePicker, InputNumber, Form} from "antd";
import {useSelector} from "react-redux";
import dayjs from "dayjs";

import {max} from "lodash";
import {getPmFilterInfo, apiGetSafeNeList} from "@/modules-otn/apis/api";
import {sortArr} from "@/modules-otn/utils/util";
import SelectLoading from "@/modules-otn/components/common/select_loading";
import {getOTNDeviceIP} from "@/modules-ampcon/apis/otn";

const PmFilters = forwardRef((props, ref) => {
    const {pmType, buttons} = props;
    const {labelList} = useSelector(state => state.languageOTN);
    const [labelMaxWidth, setLabelMaxWidth] = useState(0);
    const [selectedNeType, setSelectedNeType] = useState();
    const ptpTypeListRef = useRef([]);
    const initialValues = useMemo(() => {
        const values = {
            "pm-granularity": "24H",
            "value-scope": "INSTANT-AVG-MIN-MAX"
        };
        if (pmType === "HISTORY") {
            values["history-data-type"] = "RECORDS";
            values["number-of-records"] = 10;
        }
        return values;
    }, [pmType]);

    const [form] = Form.useForm();
    const ne_id = Form.useWatch("ne_id", form);
    const historyDataType = Form.useWatch("history-data-type", form);
    const pmPointType = Form.useWatch("pm-point-type", form);
    const pmPoint = Form.useWatch("pm-point", form);

    useImperativeHandle(ref, () => ({
        form,
        selectedNeType,
        ptpTypeList: ptpTypeListRef.current
    }));

    const renderSearchFilters = () => {
        let items = [
            {
                name: "ne_id",
                label: labelList.ne,
                rules: [{required: true, message: labelList.ne_id_input_require_message}],
                render: (
                    <SelectLoading
                        placeholder={labelList.select_ne}
                        style={{width: 280}}
                        fetchData={async () => {
                            const [safeRes, otnRes] = await Promise.all([
                                apiGetSafeNeList({}),
                                getOTNDeviceIP("ALL", {layer: "performance"})
                            ]);

                            const safeData = safeRes?.data || [];
                            const otnData = otnRes?.data || [];

                            const merged = [...safeData, ...otnData]
                                .filter(item => item?.name)
                                .map(item => ({
                                    label: item.name,
                                    value: item.ne_id || item.ip,
                                    type: item.type,
                                    disabled: !(item?.state === 1 || item?.state < 0)
                                }));

                            return sortArr(merged, ["label"]);
                        }}
                        onChange={(ne_id, option) => {
                            setSelectedNeType(option.type);
                            form.setFieldsValue({
                                "pm-point-type": undefined,
                                "pm-point": undefined,
                                "pm-parameter": undefined
                            });
                        }}
                    />
                )
            },
            {
                name: "pm-point-type",
                label: labelList.pm_point_type,
                rules: [{required: true, message: labelList.pm_point_type_input_require_message}],
                render: (
                    <SelectLoading
                        showSearch
                        mode="multiple"
                        placeholder={labelList.please_select}
                        style={{width: 280}}
                        maxTagCount="responsive"
                        fetchData={() => {
                            return getPmFilterInfo({action: "getPmPointType"}).then(rs => {
                                const {apiResult, data, apiMessage} = rs;
                                if (apiResult === "fail") {
                                    throw new Error(apiMessage);
                                }
                                return getSelectOptions(data);
                            });
                        }}
                        onChange={value => {
                            if (value?.includes("ALL")) {
                                form.setFieldValue("pm-point-type", "ALL");
                            }
                        }}
                    />
                )
            },
            {
                name: "pm-point",
                label: labelList.pm_point,
                rules: [{required: true, message: labelList.pm_point_input_require_message}],
                render: (
                    <SelectLoading
                        showSearch
                        mode="multiple"
                        placeholder={labelList.please_select}
                        style={{width: 280}}
                        maxTagCount="responsive"
                        dependence={[pmPointType, ne_id]}
                        fetchData={async () => {
                            try {
                                await form.validateFields(["pm-point-type"]);
                            } catch (e) {
                                // console.log("e", e);
                            }
                            const {ne_id, "pm-point-type": pmPointType} = form.getFieldValue();
                            return getPmFilterInfo({
                                action: "getPmPoint",
                                ne_id,
                                "pm-point-type": pmPointType
                            }).then(rs => {
                                const {apiResult, data, apiMessage} = rs;
                                if (apiResult === "fail") {
                                    throw new Error(apiMessage);
                                }
                                return getSelectOptions(data, {
                                    defaultOption: [{label: "ALL", value: "ALL"}]
                                });
                            });
                        }}
                        onChange={value => {
                            if (value?.includes("ALL")) {
                                form.setFieldValue("pm-point", "ALL");
                            }
                        }}
                    />
                )
            },
            {
                name: "pm-parameter",
                label: labelList.pm_parameter,
                rules: [{required: true, message: labelList.pm_parameter_input_require_message}],
                render: (
                    <SelectLoading
                        showSearch
                        mode="multiple"
                        maxTagCount="responsive"
                        placeholder={labelList.please_select}
                        style={{width: 280}}
                        dependence={[pmPoint, pmPointType, ne_id]}
                        fetchData={async () => {
                            try {
                                await form.validateFields(["pm-point"]);
                            } catch (e) {
                                // console.log("e", e);
                            }
                            const {ne_id, "pm-point": pmPoint} = form.getFieldValue();

                            return getPmFilterInfo({
                                action: "getPmParamter",
                                ne_id,
                                "pm-point": pmPoint
                            }).then(rs => {
                                const {apiResult, data, apiMessage} = rs;
                                if (apiResult === "fail") {
                                    throw new Error(apiMessage);
                                }
                                return getSelectOptions(data, {
                                    defaultOption: [{label: "ALL", value: "ALL"}]
                                });
                            });
                        }}
                        onChange={value => {
                            if (value?.includes("ALL")) {
                                form.setFieldValue("pm-parameter", "ALL");
                            }
                        }}
                    />
                )
            },
            {
                name: "pm-granularity",
                label: labelList.pm_granularity,
                rules: [{required: true, message: labelList.pm_granularity_input_require_message}],
                render: (
                    <Select options={["15MIN", "24H"].map(item => ({value: item, label: item}))} style={{width: 280}} />
                )
            },
            {
                name: "value-scope",
                label: labelList.value_scope,
                rules: [{required: true, message: labelList.value_scope_input_require_message}],
                render: (
                    <Select
                        options={["INSTANT-AVG-MIN-MAX", "INSTANT"]?.map(item => ({value: item, label: item}))}
                        style={{width: 280}}
                    />
                )
            }
        ];

        if (pmType === "HISTORY") {
            items = items.concat([
                {
                    name: "history-data-type",
                    label: labelList.history_data_type,
                    rules: [{required: true, message: labelList.history_data_type_input_require_message}],
                    render: (
                        <Select
                            style={{width: 280}}
                            options={[
                                {value: "RECORDS", label: "RECORDS"},
                                {value: "TIME", label: "TIME"}
                            ]}
                            onChange={value => {
                                if (value === "RECORDS") {
                                    form.setFieldsValue({
                                        "number-of-records": 10
                                    });
                                }
                            }}
                        />
                    )
                }
            ]);
        }
        if (historyDataType === "RECORDS") {
            items = items.concat([
                {
                    name: "number-of-records",
                    label: labelList.number_of_records,
                    rules: [{required: true, message: labelList.number_of_records_input_require_message}],
                    render: <InputNumber style={{width: 280}} step={1} min={1} />
                }
            ]);
        }
        if (historyDataType === "TIME") {
            items = items.concat([
                {
                    name: "start-time",
                    label: labelList.start_time,
                    rules: [
                        {
                            validator: async (_, value) => {
                                const {"history-data-type": historyDataType, "end-time": endTime} =
                                    form.getFieldsValue();
                                if (historyDataType === "TIME" && !endTime && !value)
                                    throw new Error(labelList.start_time_input_require_message);
                            },
                            required: true
                        }
                    ],
                    render: (
                        <DatePicker
                            format={time => dayjs(time.$d).format("YYYY-MM-DD HH:mm:ss")}
                            style={{width: 280}}
                            showTime
                        />
                    )
                },
                {
                    name: "end-time",
                    label: labelList.end_time,
                    rules: [
                        {
                            validator: async (_, value) => {
                                const {"history-data-type": historyDataType, "start-time": startTime} =
                                    form.getFieldsValue();
                                if (historyDataType === "TIME" && !startTime && !value)
                                    throw new Error(labelList.end_time_input_require_message);
                            },
                            required: true
                        }
                    ],
                    render: (
                        <DatePicker
                            format={time => dayjs(time.$d).format("YYYY-MM-DD HH:mm:ss")}
                            style={{width: 280}}
                            showTime
                        />
                    )
                }
            ]);
        }

        return items;
    };

    useLayoutEffect(() => {
        const maxWidth =
            max(
                Array.from(document.querySelectorAll(".ant-form-item-label > label")).map(item => {
                    return parseInt(item.offsetWidth);
                })
            ) + 32;
        setLabelMaxWidth(maxWidth);
    }, [pmType]);

    return (
        <Form
            form={form}
            labelAlign="left"
            initialValues={initialValues}
            style={{display: "flex", flexWrap: "wrap", columnGap: 80}}
        >
            {renderSearchFilters().map(item => {
                const {render, ...rest} = item;
                return (
                    <Form.Item
                        {...rest}
                        labelCol={{style: {...(labelMaxWidth ? {width: labelMaxWidth} : {})}}}
                        wrapperCol={{flex: "0 0 280px"}}
                        style={{width: "auto"}}
                    >
                        {render}
                    </Form.Item>
                );
            })}
            {buttons && (
                <Form.Item
                    label={buttons.label}
                    labelCol={{style: {...(labelMaxWidth ? {width: labelMaxWidth, visibility: "hidden"} : {})}}}
                    wrapperCol={{flex: "0 0 280px"}}
                    style={{width: "100%"}}
                >
                    {buttons.render}
                </Form.Item>
            )}
        </Form>
    );
});

export default PmFilters;

const getSelectOptions = (keyArr, config = {}) => {
    const {upperCase = true, defaultOption = []} = config;
    if (!Array.isArray(keyArr) || keyArr.length === 0) return [];
    const sortedOptions = sortArr(
        [...defaultOption, ...keyArr.map(item => ({value: item, label: upperCase ? item?.toUpperCase() : item}))],
        ["label"]
    );
    const ALLOption = sortedOptions?.find(item => item.label === "ALL");
    const options = ALLOption ? [ALLOption, ...sortedOptions.filter(item => item.label !== "ALL")] : sortedOptions;
    return options;
};
