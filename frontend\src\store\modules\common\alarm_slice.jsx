import {createSlice} from "@reduxjs/toolkit";
import {getUnreadAlarmList} from "@/modules-ampcon/apis/monitor_api";
import {message} from "antd";

const alarmStore = createSlice({
    name: "alarm",
    initialState: {
        alarmInfo: [0, 0, 0],
        alarmSearch: "",
        alarmStatus: false,
        alarmClickCount: 0
    },
    reducers: {
        updateAlarm: (state, action) => {
            state.alarmInfo = action.payload;
        },
        updateAlarmType: (state, action) => {
            state.alarmSearch = action.payload;
            state.alarmClickCount += 1;
        },
        updateAlarmStatus: (state, action) => {
            state.alarmStatus = action.payload;
        }
    }
});

const {updateAlarm, updateAlarmType, updateAlarmStatus} = alarmStore.actions;

const getAlarmCount = () => {
    return async dispatch => {
        const res = await getUnreadAlarmList();
        if (res.status === 200) {
            const unreadData = res.unread_data;
            if (unreadData.length === 0) {
                dispatch(updateAlarm([0, 0, 0]));
            } else {
                const criticalCount = unreadData.filter(item => item.type === "error");
                const warningCount = unreadData.filter(item => item.type === "warn");
                const infoCount = unreadData.filter(item => item.type === "info");
                const criticalCountlength = criticalCount.length;
                const warningCountlength = warningCount.length;
                const infoCountLength = infoCount.length;
                dispatch(updateAlarm([criticalCountlength, infoCountLength, warningCountlength]));
            }
        } else {
            dispatch(updateAlarm([0, 0, 0]));
            message.error("Get alarm message failed, please check network");
        }
    };
};

const updateAlarmSearch = type => {
    return async dispatch => {
        dispatch(updateAlarmType(type));
    };
};

const updateAlarmSearchStatus = type => {
    return async dispatch => {
        dispatch(updateAlarmStatus(type));
    };
};

const alarmReducer = alarmStore.reducer;

export {getAlarmCount, updateAlarmSearch, updateAlarmSearchStatus};

export default alarmReducer;
