#!/usr/bin/python3
# -*- coding: utf-8 -*-
"""
@author: <PERSON><PERSON>
@project: Pica8AmpCon
@file: configuration_crud.py
@function:
@time: 2022/1/6 16:25
"""
import logging
import re

from .__init__ import traceback, request, jsonify, general_model, inven_db, inventory, json, general
from server.general_api.common.configuration_utils import save_config, delete_config, add_site_config, get_snapshot_backup_config, backup_config, rollback_backup_config, push_config_files
from server.util import utils
from server.util.encrypt_util import aes_cipher

LOG = logging.getLogger(__name__)


def switch_auto_config_to_dict(model):
    """
        name = Column(String(64), unique=True, nullable=False)
        config = Column(Text(1048576))
        type = Column(Enum('global', 'regional', 'site', 'switch', 'mgt_ip'))
        parent_id = Column(Integer)
        system_model = Column(String(32), Foreign<PERSON>ey('switch_systeminfo.model', ondelete='CASCADE'))
        default = Column(Boolean, default=False)
        switchYamlConfigs = relationship("SwitchYamlConfig", backref='switch_autoConfig', lazy='joined',
                                         passive_deletes=True)
    """
    return dict(
        name=model.name,
        config=model.config,
        type=model.type,
        parent_id=model.parent_id,
        system_model=model.system_model,
        default=model.default,
        # switchYamlConfigs=model.switchYamlConfigs,
    )


@general_model.route('/global_config', methods=['GET'])
def api_global_config():
    """
        Get global configuration list
    :return:
    """
    return jsonify(list(map(switch_auto_config_to_dict,
                            inven_db.get_collection(inventory.SwitchAutoConfig, filters={'type': ['global']}))))


@general_model.route('/global_config/<string:global_config_name>', methods=['GET'])
def api_get_global_config(global_config_name):
    return jsonify(list(map(switch_auto_config_to_dict,
                            inven_db.get_collection(inventory.SwitchAutoConfig,
                                                    filters={'name': [global_config_name], 'type': ['global']}))))


@general_model.route('/global_config/add', methods=['POST'])
def api_add_global_config():
    """
        :return: code 200 success, 500 error
        """
    status, msg = 200, "success"
    try:
        params = json.loads(request.get_data(as_text=True))
        if "name" not in params or not params["name"]:
            raise ValueError("global_config_name is required")
        name = params.get('name', '')
        config = params.get('config', '')
        model_name = params.get('model_name', '')
        if not utils.is_valid_switch_model_name(model_name):
            raise ValueError("model_name is invalid")
        msg = save_config(name, config, model_name, 'global', is_create=True)
        if msg != 'success':
            raise ValueError(msg)
    except ValueError as v:
        status, msg = 500, "ERROR:[%s]" % v
    except Exception as e:
        status, msg = 502, "ERROR:[%s]" % e
    finally:
        return jsonify({"status_code": status, "msg": msg})


@general_model.route('/global_config/update', methods=['POST'])
def api_update_global_config():
    """
    :return: code 200 success, 500 error
    """
    status, msg = 200, "success"
    try:
        params = json.loads(request.get_data(as_text=True))
        config = params.get('config', '')
        if "name" not in params or not params["name"]:
            raise ValueError("global_config_name is required")
        name = params.get('name', '')
        model_name = params.get('model_name', '')
        if not utils.is_valid_switch_model_name(model_name):
            raise ValueError("model_name is invalid")
        msg = save_config(name, config, model_name, 'global')
        if msg != 'success':
            raise ValueError(msg)
    except ValueError as v:
        status, msg = 500, "ERROR:[%s]" % v
    except Exception as e:
        status, msg = 502, "ERROR:[%s]" % e
    finally:
        return jsonify({"status_code": status, "msg": msg})


@general_model.route('/global_config/delete', methods=['POST'])
def api_del_global_config():
    """
    :return: code 200 success, 500 error
    """
    status, msg = 200, "success"
    try:
        params = json.loads(request.get_data(as_text=True))
        config_name = params.get('name')
        if not config_name:
            raise ValueError('Config name is invalid!')
        msg = delete_config(config_name, 'global')
        if msg != 'success':
            raise ValueError(msg)
    except ValueError as v:
        status, msg = 500, "ERROR:[%s]" % v
    except Exception as e:
        status, msg = 502, "ERROR:[%s]" % e
    finally:
        return jsonify({"status_code": status, "msg": msg})


@general_model.route('/switch_config', methods=['GET'])
def api_switch_config():
    """
        Get global configuration list
    :return:
    """
    return jsonify(list(
        map(switch_auto_config_to_dict, inven_db.get_collection(inventory.SwitchAutoConfig, filters={'type': ['site']}))))


@general_model.route('/switch_config/<string:switch_config_name>', methods=['GET'])
def api_get_switch_config(switch_config_name):
    return jsonify(list(map(switch_auto_config_to_dict,
                            inven_db.get_collection(inventory.SwitchAutoConfig,
                                                    filters={'name': [switch_config_name], 'type': ['site']}))))


@general_model.route('/switch_config/add', methods=['POST'])
def api_add_switch_config():
    """
    :return: code 200 success, 500 error
    """
    status, msg = 200, "success"
    try:
        params = json.loads(request.get_data(as_text=True))
        if not params.get('template_info') or not params.get('template_info').get('no_generate_name'):
            raise ValueError("template_info is required")
        msg = add_site_config(params.get('template_info'), params.get('agent_info'), params.get('param'))
        if msg != 'success':
            raise ValueError(msg)
    except ValueError as v:
        status, msg = 500, "ERROR:[%s]" % v
    except Exception as e:
        status, msg = 502, "ERROR:[%s]" % e
    finally:
        return jsonify({"status_code": status, "msg": msg})


@general_model.route('/switch_config/update', methods=['POST'])
def api_update_switch_config():
    """
    :return: code 200 success, 500 error
    """
    status, msg = 200, "success"
    try:
        params = json.loads(request.get_data(as_text=True))
        config_str = params.get('config')
        if "name" not in params or not params["name"]:
            raise ValueError("switch_config_name is required")
        name = params.get('name')
        if not inven_db.get_model(inventory.SwitchAutoConfig, filters={'name': [name]}):
            raise ValueError('Site config does not exist, please check again!')
        general.general_db.update_model(inventory.SwitchAutoConfig,
                                        {'name': [name]}, {inventory.SwitchAutoConfig.config: config_str})
    except ValueError as v:
        status, msg = 500, "ERROR:[%s]" % v
    except Exception as e:
        status, msg = 502, "ERROR:[%s]" % e
    finally:
        return jsonify({"status_code": status, "msg": msg})


@general_model.route('/switch_config/delete', methods=['POST'])
def api_del_switch_config():
    """
    :return: code 200 success, 500 error
    """
    status, msg = 200, "success"
    try:
        params = json.loads(request.get_data(as_text=True))
        config_name = params.get('name')
        if not config_name:
            raise ValueError('Config name is invalid!')
        msg = delete_config(config_name, 'site')
        if msg != 'success':
            raise ValueError(msg)
    except ValueError as v:
        status, msg = 500, "ERROR:[%s]" % v
    except Exception as e:
        status, msg = 502, "ERROR:[%s]" % e
    finally:
        return jsonify({"status_code": status, "msg": msg})


def backup_switch_config_to_dict(model):
    """
    ip = Column(String(64), index=True, unique=True, nullable=False)
    sn = Column(String(64), index=True)
    config = Column(sa.BLOB(1048576))
    # 0:unknown 1:auto 2:manual
    back_up_type = Column(SmallInteger, default=0)
    """
    return dict(
        sn=model.sn,
        config=model.config,
        type=model.type,
        back_up_type=model.back_up_type,
        system_model=model.system_model,
        default=model.default,
        # switchYamlConfigs=model.switchYamlConfigs,
    )


@general_model.route('/backup_config/<string:switch_sn>/<string:backup_date>', methods=['GET'])
def api_get_backup_config_sn_date(switch_sn, backup_date):
    status, result = 200, ""
    try:
        result = get_snapshot_backup_config(switch_sn, backup_date)
    except ValueError as v:
        status, result = 500, "ERROR:[%s]" % ("time data '{}' does not match format '%Y-%m-%d' or '%a, %d %b %Y' ".format(backup_date) if re.match('time data.*?does not match format.*', str(v)) else v)
    except Exception as e:
        status, result = 502, "ERROR:[%s]" % (e)
    finally:
        return result


@general_model.route('/backup_config/<string:switch_sn>', methods=['POST', 'GET'])
def api_get_backup_config_sn(switch_sn):
    status, result = 200, ""
    if request.method == "GET":
        try:
            result = get_snapshot_backup_config(switch_sn)
        except ValueError as v:
            status, result = 500, "ERROR:[%s]" % v
        except Exception as e:
            status, result = 502, "ERROR:[%s]" % (e)
        finally:
            return result
    elif request.method == 'POST':
        try:
            result = backup_config(switch_sn)
            if result != 'success':
                raise ValueError(result)
        except ValueError as v:
            status, result = 500, "ERROR:[%s]" % v
            LOG.exception(v)
            inven_db.add_switch_log(switch_sn, "Retrieve config failed", level='warn')
        except Exception as e:
            LOG.exception(e)
            status, result = 502, "ERROR:[%s]" % (e)
            inven_db.add_switch_log(switch_sn, "Retrieve config failed", level='warn')
        finally:
            return jsonify({"status_code": status, "msg": result})


@general_model.route('/backup_config/rollback', methods=['POST'])
def api_rollback_backup_config():
    """
    :return: code 200 success, 500 error
    """
    status, msg = 200, "success"
    try:
        params = json.loads(request.get_data(as_text=True))
        sn = params['sn']
        snapshot_id = params['snapshot_id']
        commit_wait_time = int(params['commit_wait_time'])
        is_success, msg = rollback_backup_config(sn, snapshot_id, commit_wait_time)
        if not is_success:
            raise ValueError(msg)
    except ValueError as v:
        status, msg = 500, "ERROR:[%s]" % v
    except Exception as e:
        status, msg = 502, "ERROR:[%s]" % e
    finally:
        return jsonify({"status_code": status, "msg": msg})


def config_files_to_dict(model):
    """
    name = Column(String(32), index=True, unique=True, nullable=False)
    description = Column(String(255), default='')
    pid = Column(Integer, ForeignKey('general_config.id'))
    platform = Column(String(32), ForeignKey('switch_systeminfo.model'))
    level = Column(Integer)
    content = Column(Text)
    children = relationship("GeneralConfig", backref=backref('parent', remote_side=[id]))
    attach_switches = relationship('Switch', secondary=general_config_attach, backref='general_configs')
    """
    return dict(
        name=model.name,
        description=model.description,
        pid=model.pid,
        level=model.level,
        content=model.content,
        # attach_switches=model.attach_switches
    )


@general_model.route('/config_files', methods=['GET'])
def api_config_files():
    return jsonify(list(map(config_files_to_dict, inven_db.get_collection(general.GeneralConfig))))


@general_model.route('/config_files/<config_file_name>', methods=['GET'])
def api_get_config_files(config_file_name):
    return jsonify(list(map(config_files_to_dict,
                            inven_db.get_collection(general.GeneralConfig, filters={'name': [config_file_name]}))))


@general_model.route('/config_files/update', methods=['POST'])
def api_update_config_files():
    """
        :return: code 200 success, 500 error
        """
    status, msg = 200, "success"
    try:
        params = json.loads(request.get_data(as_text=True))
        if "name" not in params or not params["name"]:
            raise ValueError("config_file name is required")
        name = params['name'].strip()
        config_str = params['config']
        if general.general_db.update_model(general.GeneralConfig, {'name': [name]}, {'content_encrypted': aes_cipher.encrypt(config_str)}):
            return 'ok'
        else:
            raise ValueError('config name {}, does not exist'.format(name))
    except ValueError as v:
        status, msg = 500, "ERROR:[%s]" % v
    except Exception as e:
        status, msg = 502, "ERROR:[%s]" % e
    finally:
        return jsonify({"status_code": status, "msg": msg})


@general_model.route('/config_files/delete', methods=['POST'])
def api_delete_config_files():
    """
    :return: code 200 success, 500 error
    """
    status, msg = 200, "success"
    try:
        params = json.loads(request.get_data(as_text=True))
        if "config_name" not in params or not params["config_name"]:
            raise ValueError("config_file name is required")
        name = params['config_name']
        if general.general_db.delete_collection(general.GeneralConfig, {'name': [name]}):
            return 'ok'
        else:
            raise ValueError('config name {}, does not exist'.format(name))
    except ValueError as v:
        status, msg = 500, "ERROR:[%s]" % v
    except Exception as e:
        status, msg = 502, "ERROR:[%s]" % e
    finally:
        return jsonify({"status_code": status, "msg": msg})


@general_model.route('/config_files/push', methods=['POST'])
def api_push_config_files():
    """
        :return: code 200 success, 500 error
        """
    status, msg = 200, "success"
    try:
        params = json.loads(request.get_data(as_text=True))
        config_name = params['config_name']
        general_config = inven_db.get_collection(general.GeneralConfig, filters={'name': [config_name]})
        if not general_config:
            raise ValueError('config file named {} not exist'.format(config_name))
        config = general_config[0].content
        switch_info = params.get('switch_checked')
        group_info = params.get('group_checked')
        is_success, msg = push_config_files(config, switch_info, group_info)
        if not is_success:
            raise ValueError(msg)
    except ValueError as v:
        status, msg = 500, "ERROR:[%s]" % v
    except Exception as e:
        status, msg = 502, "ERROR:[%s]" % e
    finally:
        return jsonify({"status_code": status, "msg": msg})
