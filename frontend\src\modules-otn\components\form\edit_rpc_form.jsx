import {Divider, Form, Input, message, Radio, Tabs} from "antd";
import {getText, removeJsonNS} from "@/modules-otn/utils/util";
import {gLabelList} from "@/store/modules/otn/languageOTNSlice";
import YANG_CONFIG from "@/modules-otn/config/yang_config";
import {useState} from "react";
import styles from "@/modules-otn/components/form/edit_form.module.scss";
import {apiRpc, getYangRpc} from "@/modules-otn/apis/api";
import EditInput from "./edit_input";
import {middleModal, smallModal} from "../modal/custom_modal";

/**
 * rpcName: RPC name
 * input: RPC input
 * initVals: Initial Values like {name: "1-1", type: "upgrade"}
 * readOnly: Array of readOnly field name like ["name"]
 * setForm: Callback for passing form instance to parent
 * afterFinish: Callback after Finish
 */
const FormRpc = ({rpcName, afterFinish, input, initVals, readOnly, setForm, ne_id, objectName}) => {
    const [form] = Form.useForm();
    setForm(form);
    const [datas, setDatas] = useState({});
    const useDefine = YANG_CONFIG[rpcName];

    const onFinish = values => {
        const requestObj = {};
        let request_neID = ne_id;
        Object.entries(values).map(([key, val]) => {
            if (val) {
                if (useDefine?.[key]?.getData) {
                    val = useDefine[key].getData(val);
                }
                const temp = key.split("&");
                if (key === "ne_id" && !request_neID) {
                    request_neID = val;
                } else if (temp.length === 0) {
                    requestObj[key] = val;
                } else if (temp.length === 2 && temp[0] === "parameter") {
                    requestObj[temp[1]] = val;
                } else {
                    let _obj = requestObj;
                    temp.map((i, index) => {
                        if (!_obj[i]) {
                            _obj[i] = index === temp.length - 1 ? val : {};
                        }
                        _obj = _obj[i];
                    });
                }
            }
        });
        if (!request_neID) {
            message.error("Please select NE.").then();
            return;
        }
        apiRpc({ne_id: request_neID, rpcName, rpcConfig: requestObj}).then(
            ({result, message: msg, data, apiResult}) => {
                let success = false;
                if (apiResult === "fail") {
                    message.error(gLabelList.operation_fail).then();
                } else if (!result) {
                    message
                        .error({
                            content: gLabelList.operation_fail,
                            key: "error1",
                            duration: 0,
                            onClick: () => message.destroy("error1")
                        })
                        .then();
                } else {
                    message
                        .success(
                            <span>
                                <div>{gLabelList.operation_success}</div>
                                <div>{removeJsonNS(data)}</div>
                            </span>
                        )
                        .then();
                    success = true;
                }
                if (typeof afterFinish === "function") {
                    afterFinish(success);
                }
            }
        );
    };

    const parseWhen = (when, parentKey, type) => {
        if (!when) {
            return true;
        }

        let eqStr = "!=";
        if (when.includes(" = ")) {
            eqStr = "=";
        }

        const temp = when.split(eqStr);
        let pathLength = temp[0].match(/..\//g).length;
        if (type === "item") {
            pathLength--;
        }
        const parentKeyTemp = parentKey.split("&");
        while (pathLength > 0) {
            pathLength--;
            parentKeyTemp.pop();
        }
        if (parentKeyTemp.length === 0) {
            parentKeyTemp.push("parameter");
        }
        const key = temp[0].trim().replace(/..\//g, "");
        const value = temp[1].trim().replace(/'/g, "");
        const checkValue = form.getFieldValue(`${parentKeyTemp.join("&")}&${key}`);
        return (eqStr === "!=" && checkValue !== value) || (eqStr === "=" && checkValue === value);
    };

    const parseItem = (_yang, parentKey, disabled) => {
        return Object.entries(_yang).map(([key, val]) => {
            const _type = val.yangType || val.type;
            if (_type && _type !== "container" && _type !== "list") {
                const _key = parentKey ? `${parentKey}&${key}` : key;
                if (!parseWhen(val.when, parentKey, "item")) {
                    return;
                }
                return (
                    <Form.Item
                        key={_key}
                        name={_key}
                        label={getText(key)}
                        labelCol={{span: 6}}
                        tooltip={val.description}
                        initialValue={initVals[key] ?? val.default}
                    >
                        {disabled || readOnly.includes(key) ? (
                            <Input disabled addonAfter={val.units} key={_key} />
                        ) : (
                            EditInput({
                                config: val,
                                key: _key,
                                form,
                                useDefine: useDefine?.[_key],
                                parameter: {ne_id, objectName},
                                datas,
                                setDatas,
                                initVals
                            })
                        )}
                    </Form.Item>
                );
            }
        });
    };

    // 解析单参数
    const parseParameter = (_yang, parentKey) => {
        // console.log("parentKey", parentKey);
        return (
            <Tabs.TabPane
                key={`${parentKey}tabpanel`}
                tab={getText(parentKey.split("&").pop()).toUpperCase()}
                className={styles.edit_tab_pane}
            >
                {_yang.choice ? parseChoice(_yang, parentKey) : parseItem(_yang, parentKey)}
            </Tabs.TabPane>
        );
    };

    const parseChoice = (_yang, parentKey) => {
        return (
            <Radio.Group
                style={{width: "100%"}}
                key={_yang._key}
                onChange={e => {
                    setDatas({...datas, [_yang._key]: e.target.value});
                }}
                value={datas[_yang._key] || useDefine?.[_yang._key]?.defaultChoose}
            >
                {Object.entries(Object.values(_yang.choice)[0].case).map(([key, val]) => {
                    return (
                        <>
                            <Divider key={`${parentKey + key}divider`} orientation="left">
                                <Radio key={`${parentKey + key}radio`} value={key}>
                                    {getText(key)}
                                </Radio>
                            </Divider>
                            {parseItem(
                                val.leaf ? val.leaf : val,
                                parentKey,
                                (datas[_yang._key] ?? useDefine?.[_yang._key]?.defaultChoose) !== key
                            )}
                        </>
                    );
                })}
            </Radio.Group>
        );
    };

    const parseComponent = yang => {
        const tabs = [];
        tabs.push(parseParameter(yang, "parameter"));
        const containerList = [];
        parseYang(input, null, containerList);
        containerList.map(item => {
            if (parseWhen(item.when, item._key, "container")) {
                tabs.push(parseParameter(item, item._key));
            }
        });

        return tabs;
    };

    const parseYang = (yang, parentKey, list) => {
        Object.entries(yang).map(([key, val]) => {
            if (val?.definition?.yangType === "container") {
                const _key = parentKey ? `${parentKey}&${key}` : key;
                list.push({...val, _key});
                parseYang(val, _key, list);
            }
        });
    };

    return (
        <Form form={form} onFinish={onFinish} wrapperCol={{span: 16}} labelAlign="left" className={styles.edit_form}>
            <Tabs className={styles.edit_tab}>{parseComponent(input)}</Tabs>
        </Form>
    );
};

const openModalRpcForm = (
    ne_id,
    rpcName,
    type,
    title = getText(rpcName),
    initVals = {},
    readOnly = [],
    callbackFunc = null,
    warningConfig = null
) => {
    getYangRpc(rpcName, type).then(rs => {
        let form;
        // eslint-disable-next-line no-return-assign
        const handle = f => (form = f);
        let commitValues;
        const afterFinish = success => {
            if (success) {
                callbackFunc?.(commitValues);
                modal.destroy();
            } else {
                modal.update({okButtonProps: {loading: false}});
            }
        };
        const commit = () => {
            modal.update({okButtonProps: {loading: true}});
            form.submit();
        };
        const modal = middleModal({
            title,
            // eslint-disable-next-line no-unused-vars
            onOk: _ => {
                commitValues = form.getFieldsValue();
                if (warningConfig) {
                    const warning = warningConfig(commitValues);
                    if (warning) {
                        const modal2 = smallModal({
                            // eslint-disable-next-line no-unused-vars
                            onOk: _ => {
                                modal2.destroy();
                                commit();
                            },
                            okText: gLabelList.ok,
                            cancelText: gLabelList.cancel,
                            content: warning
                        });
                        return;
                    }
                }
                commit();
            },
            okText: gLabelList.ok,
            cancelText: gLabelList.cancel,
            closable: true,
            content: (
                <FormRpc
                    setForm={handle}
                    afterFinish={afterFinish}
                    rpcName={rpcName}
                    initVals={initVals}
                    input={rs.input}
                    readOnly={readOnly}
                    ne_id={ne_id}
                    objectName={title}
                />
            )
        });
    });
};

export {openModalRpcForm};
