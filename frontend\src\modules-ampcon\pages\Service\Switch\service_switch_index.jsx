import React, {useEffect, useState} from "react";
import {Tabs} from "antd";
import {useLocation, useNavigate} from "react-router-dom";
import GlobalConfiguration from "@/modules-ampcon/pages/Service/Switch/GlobalConfiguration/global_configuration";
import SwitchConfiguration from "@/modules-ampcon/pages/Service/Switch/SwitchConfiguration/switch_configuration";
import Switch from "@/modules-ampcon/pages/Service/Switch/switch";
import ConfigFileView from "@/modules-ampcon/pages/Service/Switch/ConfigFileView/configfile_view";
import {useSelector} from "react-redux";
import AmpConSystemConfig from "@/modules-ampcon/pages/Service/Switch/SystemConfig/system_config";
import SwitchModel from "@/modules-ampcon/pages/Service/Switch/SwitchModel/switch_model";

let items = [
    {
        key: "switch",
        label: "Switch",
        children: <Switch />
    },
    {
        key: "global_configuration",
        label: "Global Configuration",
        children: <GlobalConfiguration />
    },
    {
        key: "switch_configuration",
        label: "Switch Configuration",
        children: <SwitchConfiguration />
    },
    {
        key: "config_files_view",
        label: "Config Files View",
        children: <ConfigFileView />
    },
    {
        key: "switch_model",
        label: "Switch Model",
        children: <SwitchModel />
    },
    {
        key: "system_config",
        label: "System Config",
        children: <AmpConSystemConfig />
    }
];

const ServiceSwitchIndex = () => {
    const currentUser = useSelector(state => state.user.userInfo);
    items =
        currentUser.type === "readonly"
            ? items.filter(
                  item =>
                      item.key !== "global_configuration" &&
                      item.key !== "switch_configuration" &&
                      item.key !== "switch_model"
              )
            : items;

    const navigate = useNavigate();
    const location = useLocation();
    const [currentActiveKey, setCurrentActiveKey] = useState();
    const pathReg = /(switch|global_configuration|switch_configuration|config_files_view|switch_model)$/;

    useEffect(() => {
        const currentPath = location.pathname;
        if (pathReg.test(currentPath)) {
            setCurrentActiveKey(currentPath.match(pathReg)[0]);
        }
    }, [location.pathname]);

    const onChange = key => {
        const currentPath = location.pathname;
        if (pathReg.test(currentPath)) {
            const matchLength = currentPath.match(pathReg)[0].length;
            const parentPath = currentPath.slice(0, -matchLength);
            navigate(parentPath + key);
        }
    };

    return (
        // <Flex style={{flex: 1}} layout="horizontal">
        //     <Tabs activeKey={currentActiveKey} items={items} onChange={onChange} style={{flex: 1}} />
        // </Flex>
        <div style={{display: "flex", flex: 1}}>
            <Tabs
                activeKey={currentActiveKey}
                items={items}
                onChange={onChange}
                style={{flex: 1}}
                destroyInactiveTabPane
            />
        </div>
    );
};

export default ServiceSwitchIndex;
