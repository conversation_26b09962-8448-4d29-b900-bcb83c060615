import datetime
import logging
import os
import platform
import re
import shutil
import sys
import time
import configparser
import urllib

import flask_login
import jinja2
from flask import Blueprint, render_template, request, jsonify, flash, Response
from sqlalchemy import or_, and_

from server import constants as C
from server.ansible_lib.pica_lic import pica8_license
from server.south_api import ssh_api
from server.util import switch_paramiko_util, http_client, osutil, utils, str_helper
from server.util.utils import get_latlong, un_tar
from server import cfg

from server.db.models import inventory, monitor
from server.db.models import general


if platform.system() != 'Windows':
    from server.collect.rma_collect import collect_backup_config_single, upload_rollback_config_paramiko


inven_db = inventory.inven_db
monitor_db = monitor.monitor_db
SUCCESS = 'success'


class StringLoader(jinja2.BaseLoader):
    def get_source(self, environment, template):

        db_temp = general.general_db.get_model(general.GeneralTemplate, filters={"name": [template]})

        if not db_temp or db_temp == '':
            raise jinja2.TemplateNotFound(template)

        def uptodate():
            try:
                return False
            except OSError:
                return False

        return db_temp.j2_template, None, uptodate


env = jinja2.Environment(loader=StringLoader(),
                         trim_blocks=True,
                         lstrip_blocks=True)

file_env = jinja2.Environment(loader=jinja2.FileSystemLoader('config_gen/'),
                              trim_blocks=True,
                              lstrip_blocks=True)


def save_config(name, config_str, model_name, model_type, is_create=False):
    if config_str:
        session = inven_db.get_session()
        switch_auto_config = inventory.SwitchAutoConfig()
        if model_name:
            switch_auto_config.system_model = model_name
        switch_auto_config.name = name
        switch_auto_config.type = model_type
        switch_auto_config.config = config_str
        db_config = inven_db.get_model(inventory.SwitchAutoConfig, filters={'name': [name]}, session=session)

        if is_create and db_config:
            return 'Create failed, the config is already existed!'
        elif not is_create and not db_config:
            return 'Action failed, the config does not exist!'

        # SwitchYamlConfig
        yaml_config = inventory.SwitchYamlConfig()
        if model_type == 'global':
            yaml_pushconfig_dict = {'Global Config Name': name}
            yaml_config.pushconfig = str(yaml_pushconfig_dict)
        elif model_type == 'regional':
            yaml_pushconfig_dict = {'Regional Config Name': name}
            yaml_config.pushconfig = str(yaml_pushconfig_dict)

        if db_config:
            switch_auto_config.id = db_config.id
            inven_db.merge(switch_auto_config, session=session)

            # SwitchYamlConfig Setting
            yaml_config.autoconfig_id = db_config.id
            db_yaml_config = inven_db.get_model(inventory.SwitchAutoConfig, filters={'autoconfig_id': [db_config.id]},
                                          session=session)
            yaml_config.id = db_yaml_config.id
            inven_db.merge(db_yaml_config, session=session)
        else:
            # global_config.config = global_config_str
            inven_db.insert(switch_auto_config, session=session)
            db_global_config = inven_db.get_model(inventory.SwitchAutoConfig, filters={'name': [name]}, session=session)

            # SwitchYamlConfig Setting
            yaml_config.autoconfig_id = db_global_config.id
            inven_db.insert(yaml_config, session=session)

        return SUCCESS
    else:
        return 'The config is null'


def delete_config(name, config_type):
    config = inven_db.get_model(inventory.SwitchAutoConfig, filters={'name': [name], 'type': [config_type]})
    if not config:
        return 'config does not exist!'
    attaches = inven_db.get_collection(inventory.SwitchSwitchAutoConfig,
                                       filters={'switchAutoConfig_id': [config.id], 'type': [config_type]})
    if len(attaches) > 0:
        return 'config is in use!'
    inven_db.delete_collection(inventory.SwitchAutoConfig, {'parent_id': [config.id]})
    inven_db.delete_collection(inventory.SwitchAutoConfig, {'id': [config.id]})
    return SUCCESS


def add_site_config(template_info, agent_info, param):
    name = template_info['no_generate_name']
    description = template_info['no_generate_description']
    template_name_list = template_info['no_generate_template_name']
    config_platform = template_info['no_generate_platform']
    session = general.general_db.get_session()
    switch_sn = template_info.get('no_generate_switch_sn')
    sw_platform = template_info.get('no_generate_platform')
    location = template_info.get('no_generate_location', None)
    vpn_option = template_info.get('no_vpn_config', None)
    retrieve_config = template_info.get('no_retrieve_config', None)
    switch = inven_db.get_model(inventory.Switch, filters={'sn': [switch_sn]})
    if switch:
        return 'Switch %s already exist' % switch_sn
    if type(template_name_list) != list:
        raise ValueError('no_generate_template_name should be list')

    params = {
        'multiple_param': True
    }
    # if db error should roll back
    with session.begin(subtransactions=True):
        config_content = ''
        for template_name in template_name_list:
            try:
                template_env = env.get_template(template_name)
            except:
                return 'template {} not exist, please check again.'.format(template_name)
            params[template_name] = str_helper.parse_form_params(param)
            config_content += template_env.render(param) + '\n'

        config = inventory.SwitchAutoConfig()
        config.name = name
        config.description = description
        config.config = config_content
        config.system_model = config_platform
        config.type = 'site'

        option_post_deployed = {'vpn_option': str(vpn_option), 'retrieve_config': str(retrieve_config)}
        switch = inventory.Switch(sn=switch_sn, platform_model=sw_platform,
                        address=location, status=C.SwitchStatus.CONFIGURED, config_parameters=str(params),
                        import_type=C.ImportType.DEPLOY, post_deployed_config=str(option_post_deployed))

        # attach global config to switch
        if template_info['no_generate_global_config']:
            global_config_content = inven_db.get_model(inventory.SwitchAutoConfig,
                                                       filters={'name': [template_info['no_generate_global_config']]})
            switch.configs.append(global_config_content)

        switch.configs.append(config)
        session.add(switch)

        switch_latlong = inventory.SwitchGis()
        g = get_latlong(location)
        switch_latlong.sn = switch_sn
        # need lat change to lng
        if g.lng and g.lat:
            switch_latlong.latitude = g.lng
            switch_latlong.longitude = g.lat
        else:
            switch_latlong.latitude = 0
            switch_latlong.longitude = 0
        db_switch_gis = inven_db.get_model(inventory.SwitchGis, filters={'sn': [switch_sn]}, session=session)
        if db_switch_gis:
            switch_latlong.id = db_switch_gis.id
            inven_db.merge(switch_latlong, session=session)
        else:
            inven_db.insert(switch_latlong, session=session)

        inven_db.delete_collection(inventory.SwitchParking, filters={'sn': [switch_sn]}, session=session)

    hostname_prefix = agent_info.get('no_hostname_prefix', 'ac')
    server_domain = agent_info.get('no_server_domain', 'pica8.com')
    uplink_ports = agent_info.get('no_uplink_ports', 'te-1/1/49,te-1/1/50')
    speed = agent_info.get('no_speed', '1000')
    vpn_host = agent_info.get('no_vpn_host', 'vpn.pica8.com')
    vlan = agent_info.get('no_vlan', '4094')
    native_vlan = agent_info.get('no_native_vlan', '4094')
    vpn_enable = agent_info.get('no_vpn_enable', 'True')
    agent_enable = agent_info.get('no_enable', 'True')
    lacp_enable = agent_info.get('no_lacp', 'False')
    new_dict = {'hostname_prefix': hostname_prefix,
                'uplink': uplink_ports,
                'uplink_speed': speed,
                'vpn_host': vpn_host,
                'vlan': vlan,
                'native_vlan': native_vlan,
                'vpn_enable': vpn_enable,
                'server_domain': server_domain,
                'enable': agent_enable,
                'platform': sw_platform,
                'lacp': lacp_enable
                }

    agent_conf_temp = file_env.get_template('auto-deploy.j2')
    conf_str = agent_conf_temp.render(new_dict)
    inven_db.insert_or_update_agent_conf(switch_sn, conf_str)
    return SUCCESS


def get_snapshot_backup_config(switch_sn, snapshot_time_str=None):
    filters = [inventory.SwitchConfigSnapshotWithTag.sn == switch_sn]
    if snapshot_time_str:
        if '-' in snapshot_time_str:
            snapshot_start_time = datetime.datetime.strptime(snapshot_time_str, '%Y-%m-%d')
            snapshot_end_time = datetime.datetime.strptime(snapshot_time_str + ' 23:59:59', '%Y-%m-%d %H:%M:%S')
        elif ',' in snapshot_time_str:
            # 'Sat, 22 Feb 2020
            snapshot_start_time = datetime.datetime.strptime(snapshot_time_str, '%a, %d %b %Y')
            # 'Sat, 22 Feb 2020 23:59:59 GMT
            snapshot_end_time = datetime.datetime.strptime(snapshot_time_str + ' 23:59:59 GMT', '%a, %d %b %Y %H:%M:%S GMT')
        else:
            return 'snapshot_time_str error'
        filters.extend([and_(inventory.SwitchConfigSnapshotWithTag.snapshot_time < snapshot_end_time, inventory.SwitchConfigSnapshotWithTag.snapshot_time >= snapshot_start_time)])
    session = inven_db.get_session()
    snapshot_entry = session.query(inventory.SwitchConfigSnapshotWithTag)
    # snapshot_entry = [result.make_dict() for result in snapshot_entry.filter(*filters).all()]
    snapshot_list = list()
    for i in snapshot_entry.filter(*filters).all():
        tmp = i.make_dict()
        tmp['archive_config'] = tmp['archive_config'].decode()
        tmp['tag'] = tmp['tag'] if tmp['tag'] else ''
        snapshot_list.append(tmp)
    result = jsonify(snapshot_list)
    return result


def backup_config(sn):
    # execute ansible to collect switch config
    # need to know the switch is import or is registed
    try:
        filter_rules = [inventory.Switch.status.in_([C.SwitchStatus.PROVISIONING_SUCCESS,
                                      C.SwitchStatus.IMPORTED]), inventory.Switch.sn != C.PICOS_V_SN, inventory.Switch.sn == sn]
        rule = and_(*filter_rules)
        session = inven_db.get_session()
        switch = session.query(inventory.Switch).filter(rule).all()
        if not switch:
            return 'back-up failed, switch not exist.'
        ip = switch[0].mgt_ip
        if collect_backup_config_single(ip, sn) == C.RMA_ACTIVE:
            inven_db.add_switch_log(sn, "Retrieve config success", level='info')
            return 'success'
        else:
            inven_db.add_switch_log(sn, "Retrieve config failed", level='warn')
            return 'back-up failed'
    except Exception:
        return 'back-up failed'


def rollback_backup_config(sn, snapshot_id, commit_wait_time):
    snapshot_entry = inven_db.get_model(inventory.SwitchConfigSnapshot, filters={'sn': [sn], 'id': [snapshot_id]})
    if not snapshot_entry:
        raise ValueError("The switch sn or snapshot_id invalid")
    snapshot_content = snapshot_entry.archive_config.decode()

    switch_entry = inven_db.get_model(inventory.Switch, filters={'sn': [sn]})
    host_ip = switch_entry.mgt_ip

    user, pw = utils.get_switch_default_user(sn=sn)
    return upload_rollback_config_paramiko(host_ip, user, pw, snapshot_content, 240, commit_wait_time)


def push_config_files(config, switch_info=None, group_info=None):
    session = inven_db.get_session()
    if switch_info:
        check_all = switch_info['checkall']
        if check_all:
            nodes = switch_info['uncheckedNodes']
            switch_sns = [node['sn'] for node in nodes]
            switches = session.query(inventory.Switch).filter(inventory.Switch.sn.notin_(switch_sns)).all()
        else:
            nodes = switch_info['checkedNodes']
            switch_sns = [node['sn'] for node in nodes]
            switches = inven_db.get_collection(inventory.Switch, filters={'sn': switch_sns}, session=session)
    elif group_info:
        check_all = bool(group_info['checkall'])
        switches = []
        if check_all:
            nodes = group_info['uncheckedNodes']
            group_names = [node['name'] for node in nodes]
            groups = session.query(inventory.Group).filter(inventory.Group.group_name.notin_(group_names)).all()
        else:
            nodes = group_info['checkedNodes']
            group_names = [node['group_name'] for node in nodes]
            groups = inven_db.get_collection(inventory.Group, filters={'group_name': group_names}, session=session)

        filter_ip = set()
        for group in groups:
            group_switches = inven_db.get_group_switchs_new(group.group_name, session=session)
            for switch in group_switches:
                if switch.mgt_ip in filter_ip:
                    continue
                filter_ip.add(switch.mgt_ip)
                switches.append(switch)
    else:
        raise ValueError('group_info and group_info is empty.')
    if not check_all:
        error = len(nodes) - len(switches)
    else:
        error = 0
    success = failed = 0
    for switch in switches:
        try:
            user, password = (None, None) if switch.sn != C.PICOS_V_SN else (C.PICOS_V_USERNAME, C.PICOS_V_PASSWORD)
            res, status = ssh_api.apply_configs(switch.mgt_ip, config, sn=switch.sn, user=user, password=password)
            if status != C.RMA_ACTIVE:
                reason = res[-1200:] if len(res) > 1200 else res
                failed += 1
                monitor_db.add_event(switch.sn, 'error',
                                     'failed apply config to switch %s, reason %s, Config: %s ....' % (switch.sn, reason, config[0:150]),
                                     session=session)
            else:
                success += 1
                monitor_db.add_event(switch.sn, 'info', 'apply config to switch %s success, Config: %s ....' % (switch.sn, config[0:150]),
                                     session=session)
        except Exception as e:
            failed += 1
            monitor_db.add_event(switch.sn, 'error',
                                 'failed apply config to switch %s, reason %s, Config: %s ....' % (switch.sn, str(e), config[0:150]),
                                 session=session)
    return failed == 0 and error == 0, 'apply configs, success:[{}],failed:[{}]{} get detail from alarm page'.format(success, failed, '' if error == 0 else ',error:[{}]'.format(error))

