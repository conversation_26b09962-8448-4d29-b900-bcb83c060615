import {But<PERSON>, Col, Flex, Form, Input, Row, Select, Switch} from "antd";
import {useState} from "react";

const CLIConfiguration = () => {
    const [webSSHURL, setWebSSHURL] = useState("");
    const [refreshKey, setRefresh<PERSON><PERSON>] = useState(0);

    const onConnect = ({host, port, username, password, new_tab}) => {
        const encodeString = btoa(`${host};${port};${username};${password}`);
        if (new_tab) {
            window.open(`/ssh/?${encodeString}`, "_blank", "noopener,noreferrer");
        } else {
            setWebSSHURL(`/ssh/?${encodeString}`);
            setRefreshKey(prevKey => prevKey + 1);
        }
    };

    return (
        <Flex
            vertical
            style={{
                flex: 1,
                background: "#FFFFFF",
                padding: "13px 20px 20px 20px",
                borderRadius: "5px",
                minWidth: "1180px"
            }}
        >
            <h2 style={{margin: "8px 0 20px"}}>CLI Configuration</h2>
            <Form style={{marginBottom: 18, flexDirection: "column"}} layout="inline" onFinish={onConnect}>
                <Row style={{height: "58px", minWidth: "1160px", width: "100%"}} gutter={[16, 16]}>
                    <Col style={{marginRight: "5vw"}}>
                        <Form.Item
                            name="host"
                            rules={[
                                {required: true, message: "This field is required."},
                                {
                                    validator: (_, value) => {
                                        const ipv4Pattern =
                                            /^(25[0-5]|2[0-4]\d|[01]?\d{1,2})\.(25[0-5]|2[0-4]\d|[01]?\d{1,2})\.(25[0-5]|2[0-4]\d|[01]?\d{1,2})\.(25[0-5]|2[0-4]\d|[01]?\d{1,2})$/;
                                        const ipv6Pattern =
                                            /^((([\dA-Fa-f]{1,4}:){7}([\dA-Fa-f]{1,4}|:))|(([\dA-Fa-f]{1,4}:){6}(:[\dA-Fa-f]{1,4}|((\d{1,3}\.){3}\d{1,3})|:))|(([\dA-Fa-f]{1,4}:){5}(((:[\dA-Fa-f]{1,4}){1,2})|:((\d{1,3}\.){3}\d{1,3})|:))|(([\dA-Fa-f]{1,4}:){4}(((:[\dA-Fa-f]{1,4}){1,3})|((:[\dA-Fa-f]{1,4})?:((\d{1,3}\.){3}\d{1,3}))|:))|(([\dA-Fa-f]{1,4}:){3}(((:[\dA-Fa-f]{1,4}){1,4})|((:[\dA-Fa-f]{1,4}){0,2}:((\d{1,3}\.){3}\d{1,3}))|:))|(([\dA-Fa-f]{1,4}:){2}(((:[\dA-Fa-f]{1,4}){1,5})|((:[\dA-Fa-f]{1,4}){0,3}:((\d{1,3}\.){3}\d{1,3}))|:))|(([\dA-Fa-f]{1,4}:)(((:[\dA-Fa-f]{1,4}){1,6})|((:[\dA-Fa-f]{1,4}){0,4}:((\d{1,3}\.){3}\d{1,3}))|:))|((:(((:[\dA-Fa-f]{1,4}){1,7})|((:[\dA-Fa-f]{1,4}){0,5}:((\d{1,3}\.){3}\d{1,3})))|:)))(%.+)?$/;
                                        if (ipv4Pattern.test(value) || ipv6Pattern.test(value)) {
                                            return Promise.resolve();
                                        }
                                        return Promise.reject(new Error("Invalid IP address."));
                                    }
                                }
                            ]}
                            label="Host"
                        >
                            <Input
                                placeholder="Host"
                                style={{
                                    width: "15vw"
                                }}
                            />
                        </Form.Item>
                    </Col>
                    <Col style={{marginRight: "5vw"}}>
                        <Form.Item
                            name="username"
                            label="User Name"
                            rules={[{required: true, message: "This field is required."}]}
                        >
                            <Input
                                placeholder="Username"
                                style={{
                                    width: "15vw"
                                }}
                            />
                        </Form.Item>
                    </Col>
                    <Col>
                        <Form.Item
                            label="Password"
                            name="password"
                            rules={[{required: true, message: "This field is required."}]}
                        >
                            <Input.Password
                                placeholder="Password"
                                style={{
                                    width: "15vw"
                                }}
                            />
                        </Form.Item>
                    </Col>
                </Row>
                <Row style={{minWidth: "1160px"}} gutter={[16, 16]}>
                    <Col style={{marginLeft: "2px", marginRight: "5vw"}}>
                        <Form.Item
                            label="Port"
                            name="port"
                            rules={[
                                {required: true, message: "This field is required."},
                                {
                                    validator: (_, value) => {
                                        if (value < 1 || value > 65535) {
                                            return Promise.reject(new Error("Port must be between 1 and 65535."));
                                        }
                                        return Promise.resolve();
                                    }
                                }
                            ]}
                        >
                            <Input
                                placeholder="Port"
                                style={{
                                    width: "15vw"
                                }}
                            />
                        </Form.Item>
                    </Col>
                    <Col style={{marginLeft: "25px", marginRight: "5vw"}}>
                        <Form.Item
                            label="Session"
                            name="session"
                            rules={[{required: true, message: "This field is required."}]}
                        >
                            <Select
                                options={[{value: "SSH", label: "SSH"}]}
                                style={{
                                    width: "15vw"
                                }}
                            />
                        </Form.Item>
                    </Col>
                    <Col>
                        <Form.Item name="new_tab" label="New Tab" labelCol={{span: 15}} wrapperCol={{span: 9}}>
                            <Switch />
                        </Form.Item>
                    </Col>
                    <Col>
                        <Form.Item>
                            <Button type="primary" htmlType="submit">
                                Submit
                            </Button>
                        </Form.Item>
                    </Col>
                </Row>
            </Form>

            <Flex
                style={{
                    height: "100%",
                    border: "1px solid black",
                    boxSizing: "border-box",
                    padding: "1px"
                }}
            >
                {(() => {
                    if (refreshKey) {
                        return (
                            <iframe
                                src={webSSHURL}
                                title="Dynamic Iframe"
                                width="100%"
                                style={{flexGrow: 1, border: 0}}
                                key={refreshKey}
                            />
                        );
                    }
                })()}
            </Flex>
        </Flex>
    );
};
export default CLIConfiguration;
