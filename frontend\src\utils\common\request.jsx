import axios from "axios";
import {eventBus} from "@//utils/common/event_bus";
import {message} from "antd";

const request = axios.create({
    baseURL: "/",
    // baseURL: 'http://localhost:5000',
    timeout: 1800000,
    withCredentials: true
});

request.interceptors.request.use(
    config => {
        return config;
    },
    error => {
        return Promise.reject(error);
    }
);

request.interceptors.response.use(
    response => {
        return response.data;
    },
    error => {
        if (error.response.status === 401) {
            eventBus.emit("redirectToLogin");
        }
        if (error.response.status === 302) {
            eventBus.emit("redirectTo", error.response.data.url);
        }
        if (error.response.status === 403) {
            message.error("You don't have permission to access this action");
        }
        if (error.response.status === 413) {
            message.error("Request Entity Too Large");
        }
        return Promise.reject(error);
    }
);

export {request};
