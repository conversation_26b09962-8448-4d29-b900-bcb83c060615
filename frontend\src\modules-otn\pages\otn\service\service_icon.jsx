export const editServiceDisabledIcon = () => (
    <svg xmlns="http://www.w3.org/2000/svg" fill="none" version="1.1" width="24" height="24" viewBox="0 0 24 24">
        <g>
            <g>
                <rect x="0" y="0" width="24" height="24" rx="2" fill="#F4F5F7" fillOpacity="1" />
                <rect
                    x="0.75"
                    y="0.75"
                    width="22.5"
                    height="22.5"
                    rx="1.25"
                    fillOpacity="0"
                    strokeOpacity="1"
                    stroke="#DADCE1"
                    fill="none"
                    strokeWidth="1.5"
                />
            </g>
            <g>
                <path
                    d="M17.591234375,16.9391L6.468119375,16.9391C6.237349375,16.9391,6.052734375,17.1237,6.052734375,17.3545C6.052734375,17.5853,6.237349375,17.7699,6.468119375,17.7699L17.637334375000002,17.7699C17.868134375,17.7699,18.052734375,17.5853,18.052734375,17.3545C18.006534375,17.1699,17.821934374999998,16.9391,17.591234375,16.9391ZM7.282894375,15.69171L9.267614375,15.69171C9.359924375,15.69171,9.498754375,15.64621,9.544904375,15.5539L16.376334375,8.722480000000001C16.560934375000002,8.53787,16.560934375000002,8.30715,16.376334375,8.12253L14.436974375,6.137805C14.252364375,5.9531894,14.021634375,5.9531894,13.837024375,6.137805L12.452254374999999,7.52257L7.005604375,12.96922C6.913296375,13.061530000000001,6.867799375,13.154209999999999,6.867799375,13.24651L6.867799375,15.23124C6.867799375,15.32355,6.913296375,15.4616,7.005604375,15.5539C7.051757375,15.64621,7.190584375,15.69171,7.282894375,15.69171ZM14.114314375,7.0150500000000005L15.499084375,8.39982L14.714264375,9.18463L13.329494375,7.79986L14.114314375,7.0150500000000005ZM7.745044375,13.431370000000001L12.774914375,8.39982L14.159684375,9.78459L9.129814375,14.81614L7.745044375,14.81614L7.745044375,13.431370000000001Z"
                    fill="#C4CDDB"
                    fillOpacity="1"
                />
                <path
                    d="M6.802544375,15.7032Q6.617799375,15.5011,6.617799375,15.23124L6.617799375,13.24651Q6.617799375,13.00348,6.828827375,12.792449999999999L12.275474375,7.3458000000000006L13.660244375000001,5.9610286Q13.871934375,5.749343,14.137004375,5.749343Q14.402074375,5.749343,14.615784375,5.9630839L16.553134375,7.94575Q16.764834375,8.15744,16.764834375,8.422509999999999Q16.764834375,8.68758,16.553134375,8.89926L9.746144375,15.70622Q9.602824375,15.94171,9.267614375,15.94171L7.282894375,15.94171Q6.9455483749999996,15.94171,6.802544375,15.7032ZM7.220854375,15.4254Q7.223794375,15.42691,7.227724375,15.42864Q7.257404375,15.44171,7.282894375,15.44171L9.267614375,15.44171Q9.293114375,15.44171,9.322794375,15.42864L9.321304375,15.4421L9.339444375,15.40581L16.199534375,8.54571Q16.264834375,8.48047,16.264834375,8.422509999999999Q16.264834375,8.36455,16.199534375,8.29931L14.258174375,6.312526Q14.194964375,6.249343,14.137004375,6.249343Q14.079044375,6.249343,14.013804375,6.314581L12.629034375,7.69935L7.182384375,13.146Q7.117794375,13.21058,7.117794375,13.24651L7.117794375,15.23124Q7.117794375,15.31255,7.182384375,15.37713L7.211064375,15.40581L7.220854375,15.4254ZM14.114314375,6.661497L13.152724375,7.6230899999999995L12.975944375000001,7.79986L14.714264375,9.53819L15.852634375,8.39982L14.114314375,6.661497ZM13.683054375000001,7.79986L14.714264375,8.83108L15.145534375,8.39982L14.114314375,7.3686L13.683054375000001,7.79986ZM7.495044375,13.32785L7.495044375,15.06614L9.233394375,15.06614L14.513214375,9.784559999999999L12.774884375,8.046240000000001L7.495044375,13.32785ZM7.995044375,14.56614L9.026234375,14.56614L13.806164375,9.78462L12.774944375,8.7534L7.995044375,13.5349L7.995044375,14.56614ZM18.302734375,17.323700000000002L18.295234375,17.2939Q18.240034375,17.0728,18.067934375,16.9008Q17.856334375,16.6891,17.591234375,16.6891L6.468119375,16.6891Q6.191489375,16.6891,5.997111175,16.883499999999998Q5.802734375,17.0779,5.802734375,17.3545Q5.802734375,17.6311,5.997111475,17.825499999999998Q6.191488375,18.0199,6.468119375,18.0199L17.637334375000002,18.0199Q17.913934375,18.0199,18.108334375,17.825499999999998Q18.302734375,17.6311,18.302734375,17.3545L18.302734375,17.323700000000002ZM17.801334375,17.3833Q17.741834375,17.1891,17.591234375,17.1891L6.468119375,17.1891Q6.398595375,17.1891,6.350665375,17.237000000000002Q6.302734375,17.285,6.302734375,17.3545Q6.302734375,17.4372,6.344080375,17.4785Q6.385426375,17.5199,6.468119375,17.5199L17.637334375000002,17.5199Q17.787634375,17.5199,17.801334375,17.3833Z"
                    fillRule="evenodd"
                    fill="#C4CDDB"
                    fillOpacity="1"
                />
            </g>
        </g>
    </svg>
);

export const editServiceEnabledIcon = () => (
    <svg xmlns="http://www.w3.org/2000/svg" fill="none" version="1.1" width="24" height="24" viewBox="0 0 24 24">
        <g>
            <g>
                <rect
                    x="0.75"
                    y="0.75"
                    width="22.5"
                    height="22.5"
                    rx="1.25"
                    fillOpacity="0"
                    strokeOpacity="1"
                    stroke="#DADCE1"
                    fill="none"
                    strokeWidth="1.5"
                />
            </g>
            <g>
                <path
                    d="M17.591234375,16.9391L6.468119375,16.9391C6.237349375,16.9391,6.052734375,17.1237,6.052734375,17.3545C6.052734375,17.5853,6.237349375,17.7699,6.468119375,17.7699L17.637334375000002,17.7699C17.868134375,17.7699,18.052734375,17.5853,18.052734375,17.3545C18.006534375,17.1699,17.821934374999998,16.9391,17.591234375,16.9391ZM7.282894375,15.69171L9.267614375,15.69171C9.359924375,15.69171,9.498754375,15.64621,9.544904375,15.5539L16.376334375,8.722480000000001C16.560934375000002,8.53787,16.560934375000002,8.30715,16.376334375,8.12253L14.436974375,6.137805C14.252364375,5.9531894,14.021634375,5.9531894,13.837024375,6.137805L12.452254374999999,7.52257L7.005604375,12.96922C6.913296375,13.061530000000001,6.867799375,13.154209999999999,6.867799375,13.24651L6.867799375,15.23124C6.867799375,15.32355,6.913296375,15.4616,7.005604375,15.5539C7.051757375,15.64621,7.190584375,15.69171,7.282894375,15.69171ZM14.114314375,7.0150500000000005L15.499084375,8.39982L14.714264375,9.18463L13.329494375,7.79986L14.114314375,7.0150500000000005ZM7.745044375,13.431370000000001L12.774914375,8.39982L14.159684375,9.78459L9.129814375,14.81614L7.745044375,14.81614L7.745044375,13.431370000000001Z"
                    fill="#212519"
                    fillOpacity="1"
                />
                <path
                    d="M6.802544375,15.7032Q6.617799375,15.5011,6.617799375,15.23124L6.617799375,13.24651Q6.617799375,13.00348,6.828827375,12.792449999999999L12.275474375,7.3458000000000006L13.660244375000001,5.9610286Q13.871934375,5.749343,14.137004375,5.749343Q14.402074375,5.749343,14.615784375,5.9630839L16.553134375,7.94575Q16.764834375,8.15744,16.764834375,8.422509999999999Q16.764834375,8.68758,16.553134375,8.89926L9.746144375,15.70622Q9.602824375,15.94171,9.267614375,15.94171L7.282894375,15.94171Q6.9455483749999996,15.94171,6.802544375,15.7032ZM7.220854375,15.4254Q7.223794375,15.42691,7.227724375,15.42864Q7.257404375,15.44171,7.282894375,15.44171L9.267614375,15.44171Q9.293114375,15.44171,9.322794375,15.42864L9.321304375,15.4421L9.339444375,15.40581L16.199534375,8.54571Q16.264834375,8.48047,16.264834375,8.422509999999999Q16.264834375,8.36455,16.199534375,8.29931L14.258174375,6.312526Q14.194964375,6.249343,14.137004375,6.249343Q14.079044375,6.249343,14.013804375,6.314581L12.629034375,7.69935L7.182384375,13.146Q7.117794375,13.21058,7.117794375,13.24651L7.117794375,15.23124Q7.117794375,15.31255,7.182384375,15.37713L7.211064375,15.40581L7.220854375,15.4254ZM14.114314375,6.661497L13.152724375,7.6230899999999995L12.975944375000001,7.79986L14.714264375,9.53819L15.852634375,8.39982L14.114314375,6.661497ZM13.683054375000001,7.79986L14.714264375,8.83108L15.145534375,8.39982L14.114314375,7.3686L13.683054375000001,7.79986ZM7.495044375,13.32785L7.495044375,15.06614L9.233394375,15.06614L14.513214375,9.784559999999999L12.774884375,8.046240000000001L7.495044375,13.32785ZM7.995044375,14.56614L9.026234375,14.56614L13.806164375,9.78462L12.774944375,8.7534L7.995044375,13.5349L7.995044375,14.56614ZM18.302734375,17.323700000000002L18.295234375,17.2939Q18.240034375,17.0728,18.067934375,16.9008Q17.856334375,16.6891,17.591234375,16.6891L6.468119375,16.6891Q6.191489375,16.6891,5.997111175,16.883499999999998Q5.802734375,17.0779,5.802734375,17.3545Q5.802734375,17.6311,5.997111475,17.825499999999998Q6.191488375,18.0199,6.468119375,18.0199L17.637334375000002,18.0199Q17.913934375,18.0199,18.108334375,17.825499999999998Q18.302734375,17.6311,18.302734375,17.3545L18.302734375,17.323700000000002ZM17.801334375,17.3833Q17.741834375,17.1891,17.591234375,17.1891L6.468119375,17.1891Q6.398595375,17.1891,6.350665375,17.237000000000002Q6.302734375,17.285,6.302734375,17.3545Q6.302734375,17.4372,6.344080375,17.4785Q6.385426375,17.5199,6.468119375,17.5199L17.637334375000002,17.5199Q17.787634375,17.5199,17.801334375,17.3833Z"
                    fillRule="evenodd"
                    fill="#212519"
                    fillOpacity="1"
                />
            </g>
        </g>
    </svg>
);

export const editServiceFocusIcon = () => (
    <svg xmlns="http://www.w3.org/2000/svg" fill="none" version="1.1" width="24" height="24" viewBox="0 0 24 24">
        <g>
            <g>
                <rect
                    x="0.75"
                    y="0.75"
                    width="22.5"
                    height="22.5"
                    rx="1.25"
                    fillOpacity="0"
                    strokeOpacity="1"
                    stroke="#14C9BB"
                    fill="none"
                    strokeWidth="1.5"
                />
            </g>
            <g>
                <path
                    d="M17.591234375,16.9391L6.468119375,16.9391C6.237349375,16.9391,6.052734375,17.1237,6.052734375,17.3545C6.052734375,17.5853,6.237349375,17.7699,6.468119375,17.7699L17.637334375000002,17.7699C17.868134375,17.7699,18.052734375,17.5853,18.052734375,17.3545C18.006534375,17.1699,17.821934374999998,16.9391,17.591234375,16.9391ZM7.282894375,15.69171L9.267614375,15.69171C9.359924375,15.69171,9.498754375,15.64621,9.544904375,15.5539L16.376334375,8.722480000000001C16.560934375000002,8.53787,16.560934375000002,8.30715,16.376334375,8.12253L14.436974375,6.137805C14.252364375,5.9531894,14.021634375,5.9531894,13.837024375,6.137805L12.452254374999999,7.52257L7.005604375,12.96922C6.913296375,13.061530000000001,6.867799375,13.154209999999999,6.867799375,13.24651L6.867799375,15.23124C6.867799375,15.32355,6.913296375,15.4616,7.005604375,15.5539C7.051757375,15.64621,7.190584375,15.69171,7.282894375,15.69171ZM14.114314375,7.0150500000000005L15.499084375,8.39982L14.714264375,9.18463L13.329494375,7.79986L14.114314375,7.0150500000000005ZM7.745044375,13.431370000000001L12.774914375,8.39982L14.159684375,9.78459L9.129814375,14.81614L7.745044375,14.81614L7.745044375,13.431370000000001Z"
                    fill="#14C9BB"
                    fillOpacity="1"
                />
                <path
                    d="M6.802544375,15.7032Q6.617799375,15.5011,6.617799375,15.23124L6.617799375,13.24651Q6.617799375,13.00348,6.828827375,12.792449999999999L12.275474375,7.3458000000000006L13.660244375000001,5.9610286Q13.871934375,5.749343,14.137004375,5.749343Q14.402074375,5.749343,14.615784375,5.9630839L16.553134375,7.94575Q16.764834375,8.15744,16.764834375,8.422509999999999Q16.764834375,8.68758,16.553134375,8.89926L9.746144375,15.70622Q9.602824375,15.94171,9.267614375,15.94171L7.282894375,15.94171Q6.9455483749999996,15.94171,6.802544375,15.7032ZM7.220854375,15.4254Q7.223794375,15.42691,7.227724375,15.42864Q7.257404375,15.44171,7.282894375,15.44171L9.267614375,15.44171Q9.293114375,15.44171,9.322794375,15.42864L9.321304375,15.4421L9.339444375,15.40581L16.199534375,8.54571Q16.264834375,8.48047,16.264834375,8.422509999999999Q16.264834375,8.36455,16.199534375,8.29931L14.258174375,6.312526Q14.194964375,6.249343,14.137004375,6.249343Q14.079044375,6.249343,14.013804375,6.314581L12.629034375,7.69935L7.182384375,13.146Q7.117794375,13.21058,7.117794375,13.24651L7.117794375,15.23124Q7.117794375,15.31255,7.182384375,15.37713L7.211064375,15.40581L7.220854375,15.4254ZM14.114314375,6.661497L13.152724375,7.6230899999999995L12.975944375000001,7.79986L14.714264375,9.53819L15.852634375,8.39982L14.114314375,6.661497ZM13.683054375000001,7.79986L14.714264375,8.83108L15.145534375,8.39982L14.114314375,7.3686L13.683054375000001,7.79986ZM7.495044375,13.32785L7.495044375,15.06614L9.233394375,15.06614L14.513214375,9.784559999999999L12.774884375,8.046240000000001L7.495044375,13.32785ZM7.995044375,14.56614L9.026234375,14.56614L13.806164375,9.78462L12.774944375,8.7534L7.995044375,13.5349L7.995044375,14.56614ZM18.302734375,17.323700000000002L18.295234375,17.2939Q18.240034375,17.0728,18.067934375,16.9008Q17.856334375,16.6891,17.591234375,16.6891L6.468119375,16.6891Q6.191489375,16.6891,5.997111175,16.883499999999998Q5.802734375,17.0779,5.802734375,17.3545Q5.802734375,17.6311,5.997111475,17.825499999999998Q6.191488375,18.0199,6.468119375,18.0199L17.637334375000002,18.0199Q17.913934375,18.0199,18.108334375,17.825499999999998Q18.302734375,17.6311,18.302734375,17.3545L18.302734375,17.323700000000002ZM17.801334375,17.3833Q17.741834375,17.1891,17.591234375,17.1891L6.468119375,17.1891Q6.398595375,17.1891,6.350665375,17.237000000000002Q6.302734375,17.285,6.302734375,17.3545Q6.302734375,17.4372,6.344080375,17.4785Q6.385426375,17.5199,6.468119375,17.5199L17.637334375000002,17.5199Q17.787634375,17.5199,17.801334375,17.3833Z"
                    fillRule="evenodd"
                    fill="#14C9BB"
                    fillOpacity="1"
                />
            </g>
        </g>
    </svg>
);

export const deleteDisabledIcon = () => (
    <svg xmlns="http://www.w3.org/2000/svg" fill="none" version="1.1" width="24" height="24" viewBox="0 0 24 24">
        <g>
            <g>
                <rect x="0" y="0" width="24" height="24" rx="2" fill="#F4F5F7" fillOpacity="1" />
                <rect
                    x="0.75"
                    y="0.75"
                    width="22.5"
                    height="22.5"
                    rx="1.25"
                    fillOpacity="0"
                    strokeOpacity="1"
                    stroke="#DADCE1"
                    fill="none"
                    strokeWidth="1.5"
                />
            </g>
            <g>
                <rect x="6" y="11" width="12" height="2" rx="1" fill="#C4CDDB" fillOpacity="1" />
            </g>
        </g>
    </svg>
);

export const deleteEnabledIcon = () => (
    <svg xmlns="http://www.w3.org/2000/svg" fill="none" version="1.1" width="24" height="24" viewBox="0 0 24 24">
        <g>
            <g>
                <rect
                    x="0.75"
                    y="0.75"
                    width="22.5"
                    height="22.5"
                    rx="1.25"
                    fillOpacity="0"
                    strokeOpacity="1"
                    stroke="#C5D0D6"
                    fill="none"
                    strokeWidth="1.5"
                />
            </g>
            <g>
                <rect x="6" y="11" width="12" height="2" rx="1" fill="#212519" fillOpacity="1" />
            </g>
        </g>
    </svg>
);

export const deleteFocusIcon = () => (
    <svg xmlns="http://www.w3.org/2000/svg" fill="none" version="1.1" width="24" height="24" viewBox="0 0 24 24">
        <g>
            <g>
                <rect
                    x="0.75"
                    y="0.75"
                    width="22.5"
                    height="22.5"
                    rx="1.25"
                    fillOpacity="0"
                    strokeOpacity="1"
                    stroke="#14C9BB"
                    fill="none"
                    strokeWidth="1.5"
                />
            </g>
            <g>
                <rect x="6" y="11" width="12" height="2" rx="1" fill="#007D71" fillOpacity="1" />
                <rect
                    x="6.5"
                    y="11.5"
                    width="11"
                    height="1"
                    rx="0.5"
                    fillOpacity="0"
                    strokeOpacity="1"
                    stroke="#14C9BB"
                    fill="none"
                    strokeWidth="1"
                />
            </g>
        </g>
    </svg>
);

export const hexInfoIcon = () => (
    <svg xmlns="http://www.w3.org/2000/svg" fill="none" version="1.1" width="16" height="16" viewBox="0 0 16 16">
        <g>
            <g />
            <g>
                <path
                    d="M13,11C13.3682,11,13.6667,11.29848,13.6667,11.66667C13.6667,12.03486,13.3682,12.33333,13,12.33333L3.666667,12.33333C3.2984780000000002,12.33333,3.000000953674,12.03486,3.000000953674,11.66667C3.000000953674,11.29848,3.298477,11,3.666667,11L13,11ZM3.000000953674,6.37333C2.999690533,6.22889,3.0772789,6.0954999999999995,3.202988,6.02435C3.328697,5.95321,3.482997,5.95537,3.606668,6.029999999999999L5.76133,7.32333C6.02102,7.47857,6.02102,7.85477,5.76133,8.01L3.606001,9.303329999999999C3.482367,9.37761,3.328317,9.37958,3.202825,9.30848C3.0773335,9.237390000000001,2.999836206,9.104230000000001,3.000000953674,8.96L3.000000953674,6.37333ZM13,7C13.3682,7,13.6667,7.29848,13.6667,7.66667C13.6667,8.03486,13.3682,8.33333,13,8.33333L8.33333,8.33333C7.96514,8.33333,7.66667,8.03486,7.66667,7.66667C7.66667,7.29848,7.96514,7,8.33333,7L13,7ZM13,3C13.3682,3,13.6667,3.298477,13.6667,3.666667C13.6667,4.03486,13.3682,4.33333,13,4.33333L3.666667,4.33333C3.2984780000000002,4.33333,3.000000953674,4.03486,3.000000953674,3.666667C3.000000953674,3.298477,3.2984780000000002,3,3.666667,3L13,3Z"
                    fill="#14C9BB"
                    fillOpacity="1"
                />
            </g>
        </g>
    </svg>
);

export const decInfoIcon = () => (
    <svg xmlns="http://www.w3.org/2000/svg" fill="none" version="1.1" width="16" height="16" viewBox="0 0 16 16">
        <g>
            <g />
            <g>
                <path
                    d="M13,11C13.3682,11,13.6667,11.29848,13.6667,11.66667C13.6667,12.03486,13.3682,12.33333,13,12.33333L3.666667,12.33333C3.298477,12.33333,3,12.03486,3,11.66667C3,11.29848,3.298476,11,3.666667,11L13,11ZM13.06,6.029999999999999C13.1837,5.95537,13.338,5.95321,13.4637,6.02436C13.5894,6.0954999999999995,13.667,6.22889,13.6667,6.37333L13.6667,8.96C13.667,9.10444,13.5894,9.237829999999999,13.4637,9.30898C13.338,9.38012,13.1837,9.37796,13.06,9.303329999999999L10.90533,8.01C10.64565,7.85477,10.64565,7.47857,10.90533,7.32333L13.06,6.029999999999999ZM8.33333,7C8.70152,7,9,7.29848,9,7.66667C9,8.03486,8.70152,8.33333,8.33333,8.33333L3.666667,8.33333C3.298477,8.33333,3,8.03486,3,7.66667C3,7.29848,3.298477,7,3.666667,7L8.33333,7ZM13,3C13.3682,3,13.6667,3.298477,13.6667,3.666667C13.6667,4.03486,13.3682,4.33333,13,4.33333L3.666667,4.33333C3.298477,4.33333,3,4.03486,3,3.666667C3,3.298477,3.298477,3,3.666667,3L13,3Z"
                    fill="#14C9BB"
                    fillOpacity="1"
                />
            </g>
        </g>
    </svg>
);
