import {useEffect, useRef, useState} from "react";
import {useSelector} from "react-redux";
import {message, Tag} from "antd";
import Icon, {CheckCircleTwoTone, CloseCircleTwoTone, SyncOutlined} from "@ant-design/icons";
import {debounce} from "lodash";
import dayjs from "dayjs";
import {apiDelFile, apiGetFileList, objectGet, uploadFile} from "@/modules-otn/apis/api";
import {NEStateConfig} from "@/modules-otn/config/state_config";
import useUserRight from "@/modules-otn/hooks/useUserRight";
import {backupLogDisabledIcon, backupLogEnabledIcon} from "@/modules-otn/pages/otn/device/device_icons";
import {CustomTable} from "@/modules-otn/components/common/custom_table";
import {NE_TYPE_CONFIG} from "@/modules-otn/utils/util";
import logStyles from "./logs.module.scss";

const Logs = () => {
    const {labelList} = useSelector(state => state.languageOTN);
    const [commonData, setCommonData] = useState({allData: {}});
    const [upgradeInfo, setUpgradeInfo] = useState();
    const [exeLoading, setExeLoading] = useState(false);
    const [logData, setLogData] = useState([]);
    const userRight = useUserRight();
    const {upgrade} = useSelector(state => state.notification);
    const uploadState = useRef();

    const loadLogs = async () => {
        try {
            apiGetFileList({type: "log", filter: "file"}).then(rs => {
                if (rs?.apiResult === "fail") return;
                rs.forEach(async (item, index) => {
                    if (!item?.key) {
                        item.key = index.toString();
                    }
                    try {
                        await apiGetFileList({
                            type: "log",
                            filter: "file",
                            filePath: [item.name.replace(".tar.gz", ""), "log"]
                        }).then(childRS => {
                            if (!childRS.apiResult) item.childData = childRS;
                        });
                    } catch (e) {
                        // eslint-disable-next-line no-console
                        console.log(e);
                    }
                });
                setLogData(rs);
            });
        } catch (e) {
            // console.log(e);
        }
    };

    const updateUpgradeInfo = neID => {
        objectGet("config:ne", {ne_id: neID}).then(rs => {
            setUpgradeInfo(rs.documents[0]);
        });
    };

    const stateMap = {
        "-1": {
            title: labelList.error,
            icon: <CloseCircleTwoTone twoToneColor="#e22020" />
        },
        0: {
            title: labelList.upload_start,
            icon: <SyncOutlined spin style={{color: "#087bee"}} />
        },
        1: {
            title: labelList.upload_done,
            icon: <CheckCircleTwoTone twoToneColor="#14c9bb" />
        }
    };

    useEffect(() => {
        loadLogs().then();

        const reRender = () => {
            setLogData(logData => [...logData]);
        };
        window.addEventListener("resize", debounce(reRender, 500));

        return () => {
            window.removeEventListener("resize", debounce(reRender, 500));
        };
    }, []);

    useEffect(() => {
        if (upgrade && upgradeInfo && upgrade?.source === upgradeInfo?.value.ne_id) {
            updateUpgradeInfo(upgrade.source);
        }
    }, [upgrade]);

    const _disabled =
        !commonData?.upgrade ||
        userRight.disabled ||
        exeLoading ||
        !commonData?.upgrade?.selectedRows?.some(a => a.state === 1 || a.state < 0);

    return (
        <div
            className={logStyles.container}
            style={{
                overflow: "auto",
                borderRadius: 5,
                ...(commonData?.allData?.upgrade?.length && {paddingBottom: 16})
            }}
        >
            <CustomTable
                type="log"
                commonData={commonData}
                setCommonData={setCommonData}
                rootStyle={!commonData?.allData?.log?.length ? {marginBottom: 24} : {}}
                initDataSource={logData}
                scroll={false}
                refreshParent={() => {
                    loadLogs().then();
                }}
                initRowOperation={[
                    {
                        label: labelList.del,
                        disabled: () => {
                            return userRight.disabled;
                        },
                        confirm: {
                            title: labelList.delete_confirm
                        },
                        onClick() {
                            // eslint-disable-next-line react/no-this-in-sfc
                            const rowName = this.name;
                            const keyList =
                                commonData?.log?.selectedRows?.length > 0
                                    ? commonData.log.selectedRows.map(item => item?.name)
                                    : [rowName];
                            Promise.all(
                                keyList.map(fileName => {
                                    apiDelFile({file: fileName, type: "log"}).then(rs => {
                                        const {apiResult, apiMessage} = rs;
                                        if (apiResult === "fail") throw new Error(apiMessage);
                                    });
                                })
                            )
                                .then(() => {
                                    message.success(labelList.delete_success).then();
                                })
                                .catch(() => {
                                    message.error(labelList.delete_failed).then();
                                })
                                .finally(() => {
                                    loadLogs().then();
                                    setCommonData({});
                                });
                        }
                    },
                    {
                        label: labelList.download_local,
                        disabled: () => userRight.disabled,
                        onClick() {
                            if (userRight.disabled) {
                                return;
                            }
                            if (commonData?.log?.selectedRows?.length > 1) {
                                commonData.log.selectedRows.forEach(file => {
                                    const url = `../../otn/api/file/download?type=log&filename=${file.name}`;
                                    const link = document.createElement("a");
                                    link.href = url;
                                    link.target = "_blank";
                                    link.download = file.filename;
                                    document.body.appendChild(link);
                                    link.click();
                                    document.body.removeChild(link);
                                });
                            } else {
                                // eslint-disable-next-line react/no-this-in-sfc
                                window.open(`../../otn/api/file/download?type=log&filename=${this.name}`, "_self");
                            }
                        }
                    }
                ]}
                expandable={{
                    // eslint-disable-next-line react/no-unstable-nested-components
                    expandedRowRender: record => {
                        return (
                            <div
                                style={{
                                    margin: 0,
                                    maxHeight: "200px",
                                    overflowY: "auto",
                                    overflowX: "auto",
                                    padding: "8px 0"
                                }}
                            >
                                <div
                                    style={{
                                        display: "flex",
                                        flexWrap: "wrap"
                                    }}
                                >
                                    {record?.childData?.map(item => (
                                        <Tag
                                            key={item.name}
                                            style={{
                                                margin: "0 8px 8px 0",
                                                padding: "0 7px",
                                                background: "#14C9BB1A",
                                                color: "#14C9BB",
                                                borderColor: "#14C9BB",
                                                cursor: "pointer"
                                            }}
                                        >
                                            <a
                                                href={
                                                    `../../otn/api/file/download?type=log&filename=${item.name}` +
                                                    "&open=true" +
                                                    `&filePath=${[record.name.replace(".tar.gz", ""), "log"]}`
                                                }
                                                target="_blank"
                                                rel="noreferrer"
                                                style={{color: "#14C9BB"}}
                                            >
                                                {item.name}
                                            </a>
                                        </Tag>
                                    ))}
                                </div>
                            </div>
                        );
                    }
                }}
                columnFormat={{
                    "time-created": val => dayjs(val).format("YYYY-MM-DD HH:mm:ss")
                }}
            />
            <CustomTable
                type="upgrade"
                execRefresh={uploadState.current || upgrade}
                scroll={false}
                buttons={[
                    {
                        label: "upload_logs",
                        title: "upload_logs_desc",
                        type: "primary",
                        confirm: {
                            title: "upload_logs_confirm"
                        },
                        icon: <Icon component={_disabled ? backupLogDisabledIcon : backupLogEnabledIcon} />,
                        loading: exeLoading,
                        disabled: _disabled,
                        onClick() {
                            setExeLoading(true);
                            const neList = [];
                            commonData.upgrade.selectedRows.forEach(item => {
                                neList.push(item.ne_id);
                            });
                            let doneCount = 0;
                            const state = {};
                            for (let i = 0; i < neList.length; i++) {
                                state[neList[i]] = 0;
                                uploadFile({
                                    neList: [neList[i]],
                                    type: "log"
                                }).then(rs => {
                                    doneCount++;
                                    if (rs.length > 0 && rs[0]?.apiResult === "SUCCESS") {
                                        rs.forEach(async (item, index) => {
                                            if (index >= rs.length - 1) {
                                                uploadState.current = {
                                                    ...uploadState.current,
                                                    [neList[i]]: 1
                                                };
                                                loadLogs().then();
                                                if (doneCount >= neList.length) {
                                                    message.success(labelList.backup_success);
                                                    setExeLoading(false);
                                                }
                                            }
                                        });
                                    } else {
                                        uploadState.current = {...uploadState.current, [neList[i]]: -1};
                                        message.error(labelList.download_logfile_failed).then();
                                        if (doneCount >= neList.length) {
                                            setExeLoading(false);
                                        }
                                    }
                                });
                            }
                            uploadState.current = {...uploadState, ...state};
                        }
                    }
                ]}
                checkboxProps={record => ({
                    disabled: [0, 5].includes(record?.state)
                })}
                columnFormat={{
                    type: state => {
                        return NE_TYPE_CONFIG[parseInt(state)];
                    },
                    // eslint-disable-next-line react/no-unstable-nested-components
                    state: (state, data = {}) => {
                        let _state = state < 0 ? "error" : state;
                        if (data.upgradeType !== "logs" && state < 0) {
                            _state = 1;
                        }
                        const _upgradeType = data.upgradeType;
                        if (uploadState.current?.[data.key]) {
                            return (
                                <span>
                                    {stateMap[uploadState.current[data.key]].icon}{" "}
                                    {stateMap[uploadState.current[data.key]].title}
                                </span>
                            );
                        }
                        return (
                            <span>
                                {NEStateConfig[_upgradeType]?.[_state].icon}{" "}
                                {labelList[NEStateConfig[_upgradeType]?.[_state].title2]}
                            </span>
                        );
                    }
                }}
                setCommonData={setCommonData}
                commonData={commonData}
            />
        </div>
    );
};

export default Logs;
