import {Tabs} from "antd";
import Alarm from "@/modules-otn/pages/otn/alarm/alarm";
import {useLocation, useNavigate} from "react-router-dom";
import {useEffect, useState} from "react";
import serviceLayer0Styles from "@/modules-otn/pages/otn/service/service_layer0.module.scss";
import styles from "./alarm.module.scss";

const AlarmTabs = () => {
    const items = [
        {
            key: "current_alarm",
            label: "Current Alarm",
            style: {flex: 1, display: "flex"},
            children: <Alarm head alarmType="currentAlarm" tabSelectionType="check" />
        },
        {
            key: "history_alarm",
            label: "History Alarm",
            style: {flex: 1, display: "flex"},
            children: <Alarm head alarmType="historyAlarm" tabSelectionType="check" />
        }
    ];
    const navigate = useNavigate();
    const location = useLocation();
    const [currentActiveKey, setCurrentActiveKey] = useState();
    const pathReg = /(current_alarm|history_alarm)$/;
    useEffect(() => {
        const currentPath = location.pathname;
        if (pathReg.test(currentPath)) {
            setCurrentActiveKey(currentPath.match(pathReg)[0]);
        }
    }, [location.pathname]);

    const onChange = key => {
        const currentPath = location.pathname;
        if (pathReg.test(currentPath)) {
            const matchLength = currentPath.match(pathReg)[0].length;
            const parentPath = currentPath.slice(0, -matchLength);
            navigate(parentPath + key);
        }
    };

    return (
        <Tabs
            destroyInactiveTabPane
            className={[styles.container, serviceLayer0Styles.tabs]}
            items={items}
            activeKey={currentActiveKey}
            onChange={onChange}
        />
    );
};

export default AlarmTabs;
