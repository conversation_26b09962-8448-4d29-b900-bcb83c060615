import json
import logging
import traceback
import threading
from flask import Blueprint, jsonify, Response, request
from datetime import timedelta, datetime, date


from server.util.permission import admin_permission
from server.db.models.campus_blueprint import campus_blueprint_db, CampusTopologyConfig
from server.db.models.inventory import inven_db
from server.util.prometheus_util import query_lldp_state

campus_blueprint_mold = Blueprint("campus_blueprint", __name__, template_folder="templates")
LOG = logging.getLogger(__name__)


@campus_blueprint_mold.route("/site_topo/preview", methods=["POST"])
@admin_permission.require(http_exception=403)
def site_topo_preview():
    try:
        data = request.get_json()
        if not data:
            return jsonify({'status': 400, 'info': 'Invalid request data'})

        site_config_id = data.get('site_config_id')
        if not site_config_id:
            return jsonify({'status': 400, 'info': 'Site_config_id not found'})

        # 获取配置记录
        config = campus_blueprint_db.get_campus_site_config(site_config_id)
        if not config:
            return jsonify({'status': 404, 'info': 'Site_config not found'})

        # 获取节点记录
        nodes = campus_blueprint_db.get_campus_site_nodes(site_config_id)
        
        # 构建返回结果
        result = {
            'site_id': config.site_id,
            'site_config_id': config.id,
            'topology_name': config.topology_name,
            'topology': config.configuration.get('topology', {}),
            'networks': config.configuration.get('networks', {}),
            'nodes': []
        }

        # 处理节点数据
        nodes_data = []
        topology_view_nodes = []
        topology_view_edges = []

        for node in nodes:
            node_data = {
                'switch_sn': node.switch_sn,
                'type': node.type,
                'router_id': node.node_info.get('router_id'),
                'other_ip_config': node.node_info.get('other_ip_config', []),
                'links': node.node_info.get('links', {})
            }
            nodes_data.append(node_data)

            # 构建topology_view节点数据
            lldp_state = query_lldp_state(node.switch_sn, node.node_info.get('mac_addr', ''))
            view_node = {
                'switch_sn': node.switch_sn,
                'mac_addr': node.node_info.get('mac_addr', ''),
                'router_id': node.node_info.get('router_id'),
                'type': node.type,
                'other_ip_config': node.node_info.get('other_ip_config', []),
                'links': node.node_info.get('links', {})
            }
            view_node.update(lldp_state)
            topology_view_nodes.append(view_node)

            # 构建topology_view边数据
            links = node.node_info.get('links', {})
            for link_type, link_ports in links.items():
                for port_id, target_sn in link_ports.items():
                    edge = {
                        'source_sn': node.switch_sn,
                        'source_label': node.node_info.get('mac_addr', ''),
                        'target_sn': target_sn,
                        'target_label': '',
                        'port_info': [{
                            'source_port': port_id,
                            'target_port': port_id
                        }]
                    }
                    topology_view_edges.append(edge)

        result['nodes'] = nodes_data
        result['topology_view'] = {
            'nodes': topology_view_nodes,
            'edges': topology_view_edges
        }

        return jsonify(result)
        
    except Exception as e:
        LOG.error(f"Error in site_topo_preview: {str(e)}")
        LOG.error(traceback.format_exc())
        return Response(status=500)
    

@campus_blueprint_mold.route("/site_config/save", methods=["POST"])
@admin_permission.require(http_exception=403)
def site_topo_save():
    try:
        data = request.get_json()
        if not data:
            return Response("Invalid request data", status=400)

        site_id = data.get('site_id')
        topology_name = data.get('topology_name')
        nodes = data.get('nodes', [])

        config_data = {
            'topology_name': topology_name,
            'topology': data.get('topology'),
            'networks': data.get('networks')
        }

        config = campus_blueprint_db.add_campus_site_config(site_id, topology_name, config_data)

        for n in nodes:
            node_info = {
                'router_id': n.get('router_id'),
                'links': n.get('links', {})
            }
            campus_blueprint_db.add_campus_site_node(config.id, n.get('switch_sn'), n.get('type'), node_info)

        return jsonify({'status': 200, 'info': 'Save topology successed.'})

    except Exception as e:
        LOG.error(f"Error in site_topo_save: {str(e)}")
        LOG.error(traceback.format_exc())
        return Response(status=500)

@campus_blueprint_mold.route("/site_topo/get", methods=["POST"])
@admin_permission.require(http_exception=403)
def site_topo_get():
    try:
        data = request.get_json()
        if not data:
            return Response("Invalid request data", status=400)

        topology_config_id = data.get('topology_config_id')
        if not topology_config_id:
            return Response("Missing topology_config_id", status=400)

        # 获取配置记录
        config = campus_blueprint_db.get_campus_site_config(topology_config_id)
        if not config:
            return Response("Config not found", status=404)
        
        date = data.get("date", None)
        if date:
            date = datetime.strptime(date, "%Y-%m-%d %H:%M:%S").timestamp()

        # 获取节点记录
        nodes = campus_blueprint_db.get_campus_site_nodes(topology_config_id)
        
        result = {
            'topology_name': config.topology_name,
            'nodes': [],
            'edges': []
        }

        # 构建节点数据
        for node in nodes:
            node_data = {
                'switch_sn': node.switch_sn,
                'type': node.type,
                'router_id': node.node_info.get('router_id'),
                'links': node.node_info.get('links', {}),
                'mac_addr': node.node_info.get('mac_addr', ''),
                'other_ip_config': node.node_info.get('other_ip_config', [])
            }
            result['nodes'].append(node_data)
            lldp_state = query_lldp_state(node.switch_sn, node.node_info.get('mac_addr', ''), date)
            node_data.update(lldp_state)

            # 构建边数据
            links = node.node_info.get('links', {})
            for link_type, link_ports in links.items():
                for port_id, target_sn in link_ports.items():
                    edge = {
                        'source_sn': node.switch_sn,
                        'source_label': node.node_info.get('mac_addr', ''),
                        'target_sn': target_sn,
                        'target_label': '',
                        'port_info': [{
                            'source_port': port_id,
                            'target_port': port_id
                        }]
                    }
                    result['edges'].append(edge)

        return jsonify(result)

    except Exception as e:
        LOG.error(f"Error in site_topo_get: {str(e)}")
        LOG.error(traceback.format_exc())
        return Response(status=500)
    pass

@campus_blueprint_mold.route("/site_topo/list", methods=["GET"])
@admin_permission.require(http_exception=403)
def site_topo_list():
    try:
        session = inven_db.get_session()
        query = session.query(CampusTopologyConfig).all()
        res = [{"id": config.id, "name": config.topology_name} for config in query]
        msg = {'status': 200, "data": res}
        return jsonify(msg)
    except Exception as e:
        LOG.error(traceback.format_exc())
        msg = {'info': 'Topology get fail', 'status': 500}
        return jsonify(msg)

