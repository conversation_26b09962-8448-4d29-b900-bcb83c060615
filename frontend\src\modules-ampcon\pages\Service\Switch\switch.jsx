import {
    AmpConCustomTable,
    createColumnConfig,
    TableFilterDropdown,
    AmpConCustomModalTable
} from "@/modules-ampcon/components/custom_table";
import {Space, message, <PERSON><PERSON>, Card} from "antd";
import {fetchAllSwitch} from "@/modules-ampcon/apis/dashboard_api";
import {fetchParkingSwitch, delParkingSwitch, investigateSwitch} from "@/modules-ampcon/apis/config_api";
import {useRef, useState, useEffect} from "react";
import {useSelector} from "react-redux";
import {useNavigate} from "react-router-dom";
import LifecycleAction from "@/modules-ampcon/pages/Service/Switch/lifecycle_action";
import SSHAction from "@/modules-ampcon/pages/Service/Switch/ssh_action";
import ImportDropdownAction from "@/modules-ampcon/pages/Service/Switch/import_dropdown_action";
import LifecycletDropdownAction from "@/modules-ampcon/pages/Service/Switch/lifecycle_dropdown_action";
import {onlineSvg, offlineSvg, exclamationSvg, exportSvg} from "@/utils/common/iconSvg";
import Icon from "@ant-design/icons/lib/components/Icon";
import dayjs from "dayjs";
import {confirmModalAction} from "@/modules-ampcon/components/custom_modal";
import StageAction from "./stage_action";
import LogAction from "./log_action";
import ConfigAction from "./config_action";
import ConfigurationAction from "./configuration_action";

const Switch = () => {
    const switchRef = useRef(null);
    const fetchIntervalRef = useRef();
    const currentUser = useSelector(state => state.user.userInfo);
    const [isForbidden, setIsForbidden] = useState(false);
    const navigate = useNavigate();

    const switchColumns = [
        {...createColumnConfig("Switch Name", "host_name", TableFilterDropdown)},
        {
            ...createColumnConfig("SN/Service Tag", "sn", TableFilterDropdown),
            render: (_, record) => {
                return (
                    <Space size="large" className="actionLink">
                        <a
                            onClick={() => {
                                navigate(`/service/switch/${record.sn}`);
                            }}
                        >
                            {record.sn}
                        </a>
                    </Space>
                );
            }
        },
        createColumnConfig("Model", "platform_model", TableFilterDropdown),
        createColumnConfig("Version", "version", TableFilterDropdown),
        createColumnConfig("Status", "status", TableFilterDropdown),
        {
            ...createColumnConfig("Mgmt IP", "mgt_ip", TableFilterDropdown),
            render: (_, record) => {
                if (!record.mgt_ip) {
                    return null;
                }

                let iconComponent;
                if (record.reachable_status === 0) {
                    iconComponent = onlineSvg;
                } else if (record.reachable_status === 1) {
                    iconComponent = offlineSvg;
                } else {
                    iconComponent = exclamationSvg;
                }

                return (
                    <Space>
                        <Icon component={iconComponent} />
                        {record.link_ip_addr ? `${record.mgt_ip}/${record.link_ip_addr}` : record.mgt_ip}
                    </Space>
                );
            }
        },
        {
            title: "Operation",
            render: (_, record) => {
                return (
                    <div>
                        <Space size="large" className="actionLink">
                            {isForbidden ? null : <StageAction record={record} tableRef={switchRef} />}
                            <SSHAction record={record} tableRef={switchRef} />
                            <LogAction record={record} tableRef={switchRef} />
                            {isForbidden ? null : <ConfigurationAction record={record} tableRef={switchRef} />}
                            <ConfigAction record={record} isForbidden={isForbidden} tableRef={switchRef} />
                            {isForbidden ? null : <LifecycleAction record={record} tableRef={switchRef} />}
                        </Space>
                    </div>
                );
            }
        }
    ];
    const switchSearchFieldsList = ["sn", "host_name", "mgt_ip", "platform_model"];
    const switchMatchFieldsList = [
        {name: "sn", matchMode: "fuzzy"},
        {name: "host_name", matchMode: "fuzzy"},
        {name: "mgt_ip", matchMode: "fuzzy"},
        {name: "platform_model", matchMode: "fuzzy"},
        {name: "create_time", matchMode: "fuzzy"}
    ];

    useEffect(() => {
        const userType = currentUser?.type;
        if (userType === "readonly") {
            setIsForbidden(true);
        } else {
            setIsForbidden(false);
        }
    }, []);

    useEffect(() => {
        fetchIntervalRef.current = setInterval(() => {
            if (switchRef.current) {
                switchRef.current.refreshTable();
            }
        }, 60000);
        return () => {
            if (fetchIntervalRef.current) {
                clearInterval(fetchIntervalRef.current);
            }
        };
    }, []);

    return (
        <div style={{minHeight: "100%"}}>
            <Card style={{display: "flex", flex: 1, minHeight: "100%"}}>
                <h2 style={{marginTop: "8px", marginBottom: "20px"}}>Switch</h2>
                <AmpConCustomTable
                    columns={switchColumns}
                    searchFieldsList={switchSearchFieldsList}
                    extraButton={<ExtraButton isForbidden={isForbidden} tableRef={switchRef} />}
                    matchFieldsList={switchMatchFieldsList}
                    fetchAPIInfo={fetchAllSwitch}
                    ref={switchRef}
                />
            </Card>
        </div>
    );
};

const ExtraButton = ({isForbidden, tableRef}) => {
    return isForbidden ? null : (
        <Space size="middle">
            <ImportDropdownAction tableRef={tableRef} />
            <LifecycletDropdownAction tableRef={tableRef} />
            <ParkingSwicthButton />
        </Space>
    );
};

const ExportButton = ({tableRef}) => {
    const exportToJson = data => {
        const currentDateTime = dayjs().format("YYYY_MM_DD_HH_mm_ss");
        const filename = `switch_${currentDateTime}.json`;

        const blob = new Blob([data], {type: "text/json"});
        const url = URL.createObjectURL(blob);
        const link = document.createElement("a");
        link.setAttribute("href", url);
        link.setAttribute("download", filename);
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    };
    const exportmsg = () => {
        // console.log(tableRef.current.getTableRef().current.getTableData());
        const {length} = tableRef.current.getTableRef().current.getTableData();
        if (length >= 1) {
            exportToJson(JSON.stringify(tableRef.current.getTableRef().current.getTableData()));
            message.success("Success!");
        } else {
            message.error("There is no data in the table.");
        }
    };
    return (
        <Button onClick={() => exportmsg()} style={{display: "flex", alignItems: "center"}}>
            <Icon component={exportSvg} />
            Export
        </Button>
    );
};
const ParkingSwicthButton = () => {
    const navigate = useNavigate();
    const tableRef = useRef(null);
    const [isModalOpen, setIsModalOpen] = useState(false);
    const searchFieldsList = ["sn", "ip", "hardware_id", "model", "investigate"];
    const matchFieldsList = [
        {name: "sn", matchMode: "fuzzy"},
        {name: "ip", matchMode: "fuzzy"},
        {name: "model", matchMode: "fuzzy"},
        {name: "hardware_id", matchMode: "fuzzy"},
        {name: "register_count", matchMode: "fuzzy"},
        {name: "create_time", matchMode: "fuzzy"},
        {name: "last_register", matchMode: "fuzzy"}
    ];
    const columns = [
        createColumnConfig("SN", "sn", TableFilterDropdown),
        createColumnConfig("Hardware ID", "hardware_id", TableFilterDropdown),
        createColumnConfig("IP Address", "ip", TableFilterDropdown),
        createColumnConfig("Model", "model", TableFilterDropdown),
        createColumnConfig("Register Count", "register_count", TableFilterDropdown),
        createColumnConfig("Time In", "create_time", TableFilterDropdown),
        createColumnConfig("Latest Time", "last_register", TableFilterDropdown),
        {
            ...createColumnConfig("Flag", "investigate", TableFilterDropdown),
            render: investigate => {
                if (investigate) {
                    investigate = "I";
                } else {
                    investigate = "";
                }
                return investigate;
            }
        },
        {
            title: "Operation",
            render: (_, record) => {
                return (
                    <div>
                        <Space size="large" className="actionLink">
                            <a
                                onClick={() => {
                                    navigate("/service/switch_configuration", {
                                        state: {sn: record.sn, model: record.model}
                                    });
                                }}
                            >
                                Create Config
                            </a>
                            <a
                                onClick={() => {
                                    investigateSwitch(record.sn).then(res => {
                                        if (res.status === 200) {
                                            message.success(res.info);
                                            tableRef.current.getTableRef().current.refreshTable();
                                        } else {
                                            message.error("Failed to investigate parking switch");
                                        }
                                    });
                                }}
                            >
                                Investigated
                            </a>
                            <a
                                onClick={() => {
                                    confirmModalAction("Are you sure want to delete?", () => {
                                        delParkingSwitch(record.sn).then(res => {
                                            if (res.status === 200) {
                                                message.success(res.info);
                                                tableRef.current.getTableRef().current.refreshTable();
                                            } else {
                                                message.error("Failed to delete parking switch");
                                            }
                                        });
                                    });
                                }}
                            >
                                Remove
                            </a>
                        </Space>
                    </div>
                );
            }
        }
    ];
    return (
        <div>
            <Button
                htmlType="button"
                onClick={() => {
                    setIsModalOpen(true);
                }}
            >
                Parking Lot
            </Button>
            <AmpConCustomModalTable
                ref={tableRef}
                title="Parking Lot"
                selectModalOpen={isModalOpen}
                onCancel={() => {
                    setIsModalOpen(false);
                }}
                columns={columns}
                matchFieldsList={matchFieldsList}
                searchFieldsList={searchFieldsList}
                buttonProps={[<ExportButton tableRef={tableRef} />]}
                fetchAPIInfo={fetchParkingSwitch}
                modalClass="ampcon-max-modal"
            />
        </div>
    );
};

export default Switch;
