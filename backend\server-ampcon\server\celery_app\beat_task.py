#!/usr/bin/python
# -*- coding: utf-8 -*-
"""
@author: <PERSON><PERSON>
@project: ampcon
@file: beat_task.py
@function:
@time: 2022/8/24 13:39
"""
from server.collect.rma_collect import collect_backup_config_all
from server.util import utils, dcp920_util, fmt_util, m6200_util, check_license_util

import cfg
from celery_app import my_celery_app
from celery_app.automation_task import AmpConBaseTask


@my_celery_app.task(name="beat_update_db_license_count", base=AmpConBaseTask)
def beat_update_db_license_count(**kwargs):
    utils.update_db_license_count()


@my_celery_app.task(name="beat_collect_backup_config_all", base=AmpConBaseTask)
def beat_collect_backup_config_all(**kwargs):
    if cfg.CONF.vpn_keepalive and cfg.CONF.rma_interval > 0:
        collect_backup_config_all()


@my_celery_app.task(name="beat_update_vpn_client_status")
def beat_update_vpn_client_status(**kwargs):
    if cfg.CONF.vpn_enable:
        utils.update_vpn_client_status()


@my_celery_app.task(name="beat_update_cpu_mem_record")
def beat_update_cpu_mem_record(**kwargs):
    utils.update_cpu_mem_record()


@my_celery_app.task(name="beat_update_alarm_logs_read_tag")
def beat_update_alarm_logs_read_tag(**kwargs):
    utils.update_alarm_logs_read_tag()


@my_celery_app.task(name="beat_check_license_expire_time")
def beat_check_license_expire_time(**kwargs):
    check_license_util.beat_check_license_expire_time()


@my_celery_app.task(name="beat_sync_dcp920_device_info")
def beat_sync_dcp920_device_info(**kwargs):
    dcp920_util.beat_sync_dcp920_device_info_all()


@my_celery_app.task(name="beat_sync_fmt_device_info")
def beat_sync_fmt_device_info(**kwargs):
    fmt_util.beat_sync_fmt_device_info_all()

@my_celery_app.task(name="beat_sync_dcs_device_info")
def beat_sync_dcs_device_info(**kwargs):
    fmt_util.beat_sync_dcs_device_info_all()

@my_celery_app.task(name="beat_sync_dcs_lldp_info")
def beat_sync_dcs_lldp_info(**kwargs):
    fmt_util.beat_sync_dcs_lldp_info()

@my_celery_app.task(name="beat_sync_m6200_device_info")
def beat_sync_m6200_device_info(**kwargs):
    m6200_util.beat_sync_m6200_device_info_all()

@my_celery_app.task(name="beat_count_alarms_info")
def beat_count_alarms_info(**kwargs):
    utils.beat_count_alarms_info()
