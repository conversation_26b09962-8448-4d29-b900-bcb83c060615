import {Button, Flex, Form, Input, message, Select} from "antd";
import React, {useEffect, useState} from "react";

import {queryRetrieveConfig, collectBackupConfig} from "@/modules-ampcon/apis/config_backup_api";
import {ConfigBackupTable} from "@/modules-ampcon/pages/Service/Switch/ConfigTemplate/ConfigBackup/config_backup_base";

const {Option} = Select;

const ConfigBackup = () => {
    const [selectedTime, setSelectedTime] = useState(0);
    const [groups, setGroups] = useState(["All"]);
    const [reportTime, setReportTime] = useState([]);
    const [job, setJob] = useState({day: "", hour: ""});
    const hours = Array.from({length: 24}, (_, index) => index.toString().padStart(2, "0"));

    useEffect(() => {
        const fetchData = async () => {
            const retrieveConfig = await queryRetrieveConfig();
            // eslint-disable-next-line no-console
            setGroups(["All", ...retrieveConfig.data.groups]);
            setReportTime(retrieveConfig.data.reportTimeList);
            setJob({
                day: retrieveConfig.data.job.day,
                hour: retrieveConfig.data.job.hour
            });
        };
        fetchData()
            .then(() => {})
            .catch(() => {});
    }, []);

    const handleCollectBackupConfig = async values => {
        // eslint-disable-next-line no-console
        collectBackupConfig(values.days, values.time).then(response => {
            if (response.status !== 200) {
                message.error(response.info);
            } else {
                setJob({
                    day: values.days,
                    hour: values.time
                });
                message.success(response.info);
            }
        });
    };

    return (
        <div>
            <div style={{marginTop: -8}}>
                Current auto backup interval is: {job.day} days at time {job.hour !== "" && ` at time ${job.hour}:00`}
            </div>
            <div style={{marginTop: 24}}>
                <ConfigBackupTable
                    groups={groups}
                    reportTime={reportTime}
                    onChangeBackupInterval={value => {
                        setJob(value);
                    }}
                />
            </div>
        </div>
    );
};

export default ConfigBackup;
