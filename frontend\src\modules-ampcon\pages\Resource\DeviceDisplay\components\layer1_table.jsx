import {Space, Empty, Form, message, Table, Typography, theme, Switch} from "antd";
import React, {useState, useEffect} from "react";
import EmptyPic from "@/assets/images/App/empty.png";
import {queryDCSConfig, modifyDCSConfig, batchModifyDCSConfig} from "@/modules-ampcon/apis/fmt";

import openCustomEditForm, {
    openCustomEditFormForD6000
} from "@/modules-ampcon/pages/Resource/DeviceDisplay/components/custom_edit_form";
import {DebounceButton, NULL_VALUE, SortPortsDefault} from "../utils";

const switchComponent = (value, rowData, disabled, submit) => {
    if (value === NULL_VALUE) {
        return NULL_VALUE;
    }
    return (
        <Switch
            disabled={disabled ?? false}
            defaultChecked={!!value}
            onChange={newVal => {
                if (newVal === value) {
                    return;
                }
                if (submit) {
                    submit(newVal);
                    return;
                }
            }}
        />
    );
};

export const DynamicTable = ({
    label,
    tabType,
    data,
    afterUpdate,
    CardId,
    CardName,
    CardSuffix,
    CardType,
    SlotNo,
    NeIP,
    NeName,
    PortName = ""
}) => {
    const [form] = Form.useForm();

    const {
        token: {colorPrimary}
    } = theme.useToken();

    const [pagination, setPagination] = useState({
        current: 1,
        pageSize: 10,
        total: Object.keys(data).length
    });

    const [frequencyOptions, setFrequencyOptions] = useState([
        {value: "75GHz", label: "75GHz", children: []},
        {value: "100GHz", label: "100GHz", children: []}
    ]);

    useEffect(() => {
        const newOptions = [
            {value: "75GHz", label: "75GHz", children: []},
            {value: "100GHz", label: "100GHz", children: []}
        ];

        for (let i = 0; i < 64; i++) {
            const value = 196.0375 - i * 0.075;
            const wavelengthNm = (2.99792458e8 / (value * 1e12)) * 1e9;
            newOptions[0].children.push({
                label: `CM${i + 1}-${value.toFixed(4)}THz-${wavelengthNm.toFixed(2)}nm`,
                value: `CM${i + 1}`
            });
        }

        for (let i = 0; i < 48; i++) {
            const value = 191.4 + i * 0.1;
            const wavelengthNm = (2.99792458e8 / (value * 1e12)) * 1e9;
            newOptions[1].children.push({
                label: `C${i + 14}-${value.toFixed(2)}THz-${wavelengthNm.toFixed(2)}nm`,
                value: `C${i + 14}`
            });
        }

        setFrequencyOptions(newOptions);
    }, []);

    const [tableData, setTableData] = useState([]);
    useEffect(() => {
        if (data) {
            const sortedPortsArray = SortPortsDefault(Object.values(data));
            const initialData = sortedPortsArray.map((portData, index) => ({
                key: String(index + 1),
                // ...Object.fromEntries(
                //     Object.entries(data).map(([key, value]) => [key, value === "" ? NULL_VALUE : value])
                // ),
                ...Object.fromEntries(
                    Object.entries(portData).map(([key, value]) => [key, value === "" ? NULL_VALUE : value])
                ),
                ne_name: NeName,
                ne_id: NeIP,
                card_name: CardName,
                card_type: CardType,
                mapping_path: NULL_VALUE,
                port_used: NULL_VALUE
            }));
            setTableData(initialData);
        } else {
            setTableData([]);
        }
    }, [data]);

    if (!data) {
        return <Empty image={EmptyPic} description="No Data" imageStyle={{margin: 0}} />;
    }

    function getLoopBackStatus(state, portIndex, CardType) {
        const stateInt = parseInt(state, 10);

        if (CardType === "4ME4C") {
            if (portIndex === -1) {
                if (stateInt === 34) {
                    return "TERMINAL";
                }
                if (stateInt === 68) {
                    return "FACILITY";
                }
                if (stateInt === 0) {
                    return "NONE";
                }
                return "NONE";
            }

            if (stateInt === 34 || stateInt === 68 || stateInt === 0) {
                return "NONE";
            }

            const binaryStr = stateInt.toString(2).padStart(8, "0");
            const high4 = binaryStr.slice(0, 4);
            const low4 = binaryStr.slice(4, 8);

            const isFacility = parseInt(low4, 2) === 0;
            const binaryStrA = isFacility ? high4 : low4;

            const bitPosition = 4 - portIndex;
            const bit = binaryStrA.length >= bitPosition ? binaryStrA.charAt(bitPosition) : NULL_VALUE;

            if (bit === "0") return "NONE";
            if (bit === "1") return isFacility ? "FACILITY" : "TERMINAL";
        }

        if (CardType === "4T4E4C") {
            if (stateInt === 0) {
                return "NONE";
            }

            if (portIndex === -1) {
                if (stateInt === 1) {
                    return "TERMINAL";
                }
                if (stateInt === 2) {
                    return "FACILITY";
                }
                return "NONE";
            }

            if (stateInt === 3) {
                return "TERMINAL";
            }

            if (stateInt === 4) {
                return "FACILITY";
            }
        }
        return "NONE";
    }

    function getTxPowerRange(modulation, CardType) {
        let min = null;
        let max = null;

        if (CardType === "4ME4C") {
            if (modulation === 1) {
                min = -13;
                max = 9;
            } else if ([3, 4, 5, 6, 7].includes(modulation)) {
                min = -6;
                max = 1;
            }
        }

        if (CardType === "4T4E4C") {
            if ([1, 3].includes(modulation)) {
                min = -13;
                max = 9;
            } else if ([5, 6, 7].includes(modulation)) {
                min = -6;
                max = 1;
            }
        }

        return {min, max};
    }

    function getTxPowerPlaceholderText(modulation, CardType) {
        const {min, max} = getTxPowerRange(modulation, CardType);
        if (min !== null && max !== null) {
            return `Range: ${min}dB~${max}dB`;
        }
        return NULL_VALUE;
    }

    function validateTxPowerRange(modulation, CardType, value) {
        const {min, max} = getTxPowerRange(modulation, CardType);
        if (min !== null && max !== null) {
            if (value < min || value > max) {
                return Promise.reject(new Error("Please enter the optical power value within the supported range."));
            }
        }
        return Promise.resolve();
    }

    const TableConfigs = {
        linecard: {
            port: {
                columns: [
                    {
                        dataIndex: "name",
                        title: "Port",
                        fixed: "left",
                        render: text => `PORT${CardSuffix}-${text}`
                    },
                    {
                        dataIndex: "business_mode",
                        title: "Service Type",
                        render: (text, rowData) => {
                            // if (CardType === "4T4E4C" && (text === "" || text === NULL_VALUE)) return NULL_VALUE;

                            const normalizedText = text && text.length === 2 ? parseInt(text) : text;
                            let mappedValue = NULL_VALUE;

                            switch (CardType) {
                                case "4ME4C":
                                    if (["L1"].includes(rowData.name)) {
                                        mappedValue = normalizedText === 1 ? "400GE" : NULL_VALUE;
                                    } else if (["C1", "C2", "C3", "C4"].includes(rowData.name)) {
                                        mappedValue = "100GE";
                                    }
                                    break;
                                case "4T4E4C":
                                    mappedValue = "400GE";
                                    break;
                                default:
                                    mappedValue = NULL_VALUE;
                            }
                            return mappedValue;
                        }
                    },
                    {dataIndex: "mapping_path", title: "Mapping Path"},
                    {
                        dataIndex: "modulation",
                        title: "Modulation",
                        render: (text, rowData) => {
                            if (
                                text === "" ||
                                text === NULL_VALUE ||
                                rowData.module_type === NULL_VALUE ||
                                parseInt(rowData.module_type) === 0
                            )
                                return NULL_VALUE;

                            const parsedValue = parseInt(text, 10);
                            if (isNaN(parsedValue)) return NULL_VALUE;

                            let modulationMap = {};

                            switch (CardType) {
                                case "4T4E4C":
                                    modulationMap = {
                                        1: "1x400G ZR-CFEC-16QAM",
                                        // 2: "1x400G ZR-Single Wavalength",
                                        3: "4x100G ZR-CFEC-16QAM",
                                        // 4: "4x100G ZR-Single Wavalength",
                                        5: "1x400G OFEC-16QAM",
                                        6: "4x100G OFEC-16QAM",
                                        7: "400ZR-CFEC-16QAM"
                                        // 11: "100G",
                                        // 12: "100GBASE-ZR NO-FEC",
                                        // 13: "100GBASE-ZR RS-FEC"
                                    };
                                    break;
                                case "4ME4C":
                                    modulationMap = {
                                        1: "400ZR-CFEC-16QAM",
                                        // 2: "400GZR-Single Wavalength",
                                        3: "400ZR-OFEC-16QAM",
                                        4: "300ZR-OFEC-8QAM",
                                        5: "200ZR-OFEC-QPSK",
                                        6: "100ZR-OFEC-QPSK",
                                        7: "400ZR-CFEC-16QAM"
                                    };
                                    break;
                                default:
                                    return NULL_VALUE;
                            }
                            return modulationMap[parsedValue] || parsedValue;
                        }
                    },
                    {
                        dataIndex: "module_type",
                        title: "Module Type",
                        render: value => {
                            if (parseInt(value) === 0) {
                                return NULL_VALUE;
                            }
                            return value;
                        }
                    },
                    {
                        dataIndex: "wavelength",
                        title: "Wavelength",
                        render: (value, rowData) => {
                            if (rowData.name.startsWith("C")) {
                                const val = parseInt(rowData.module_wavelength);
                                if (val === 0 || val === NULL_VALUE) {
                                    return NULL_VALUE;
                                }
                                return parseFloat(rowData.module_wavelength).toFixed(2);
                            }

                            if (parseInt(value) === 0 || value < 14 || value > 61) {
                                return NULL_VALUE;
                            }

                            const val = parseInt(value);
                            const waveValue = 191.4 + (val - 14) * 0.1;
                            const wavelengthNm = (2.99792458e8 / (waveValue * 1e12)) * 1e9;
                            return `${waveValue.toFixed(2)}THz-${wavelengthNm.toFixed(2)}nm`;
                        }
                    },
                    {
                        dataIndex: "target_output_power",
                        title: "Target Output Power",
                        render: (value, rowData) => {
                            if (rowData.name.startsWith("C")) {
                                return NULL_VALUE;
                            }
                            if (
                                parseInt(value) === -50 ||
                                rowData.module_wavelength === "0000.00" ||
                                rowData.module_wavelength === NULL_VALUE
                            ) {
                                return NULL_VALUE;
                            }
                            const num = Number(value);
                            const displayVal = num >= 0 ? Math.abs(num) : num;

                            return displayVal.toFixed(2);
                        }
                    },
                    {
                        dataIndex: "fec",
                        title: "FEC",
                        render: (state, rowData) => {
                            if (CardType === "4ME4C") {
                                const binaryStr = parseInt(state, 10).toString(2).padStart(4, "0");
                                const bitPosition = 4 - parseInt(rowData.no - 1);
                                const value =
                                    binaryStr.length >= bitPosition ? binaryStr.charAt(bitPosition) : NULL_VALUE;
                                if (value === "0") return "ENABLE";
                                if (value === "1") return "DISABLE";
                            }
                            if (CardType === "4T4E4C" && rowData.name.startsWith("C")) {
                                return "ENABLE";
                            }
                            return NULL_VALUE;
                        }
                    },
                    {dataIndex: "pre_fec", title: "Pre FEC"},
                    {dataIndex: "post_fec", title: "Post FEC"},
                    {
                        dataIndex: "work_mode",
                        title: "Loopback",
                        render: (state, rowData) => {
                            if (rowData.name.startsWith("C")) {
                                return "--";
                            }
                            const enabledM1Values = [
                                // C内回环 M1 = 1~15
                                1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15,
                                // C外回环 M1 = 16~240（按16递增）
                                16, 32, 48, 64, 80, 96, 112, 128, 144, 160, 176, 192, 208, 224, 240
                            ];

                            const stateValue = parseInt(state, 10);

                            if (enabledM1Values.includes(stateValue)) {
                                return "ENABLED";
                            }
                            if (stateValue === 34 || stateValue === 68) {
                                return "ENABLED";
                            }
                            return "DISABLE";
                        }
                    },
                    {
                        dataIndex: "als",
                        title: "ALS",
                        render: state => {
                            const parsedValue = parseInt(state, 10);
                            if (parsedValue === 0) return "DISABLE";
                            if (parsedValue === 1) return "ENABLE";
                            return parsedValue || NULL_VALUE;
                        }
                    },
                    {dataIndex: "port_used", title: "Port Used"},
                    {
                        dataIndex: "laser_switch",
                        title: "Laser Enable",
                        render: (value, rowData, index) =>
                            switchComponent(
                                value === "0",
                                rowData,
                                rowData.laser_switch === NULL_VALUE || rowData.als === "1",
                                newVal => {
                                    modifyDCSConfig({
                                        ip: NeIP,
                                        cardId: CardId,
                                        key: `tx_switch_${rowData.no}`,
                                        value: newVal ? "0" : "1"
                                    })
                                        .then(response => {
                                            if (response.errorCode === 0) {
                                                setTableData(prevData => {
                                                    const newData = [...prevData];
                                                    newData[index].laser_switch = newVal ? "0" : "1";
                                                    return newData;
                                                });
                                                message.success("Modify successfully");
                                            } else {
                                                message.error("Modify failed");
                                            }
                                        })
                                        .catch(() => {});
                                }
                            )
                    },
                    {
                        dataIndex: "operation",
                        title: "Operation",
                        fixed: "right",
                        sorter: false,
                        render: (value, rowData) => {
                            const portNo = rowData.no;
                            const portIndex = portNo - 1;
                            const ServiceType = `business_mode_${portNo}`;
                            const FECType = `fec_switch_1`;
                            const LoopBack = `work_mode_${portNo}`;
                            const ALS = `als_${portNo}`;
                            const LaserEnable = `tx_switch_${portNo}`;
                            const Modulation = `modulation_${portNo}`;
                            const Wavelength = `wavelength_${portNo}`;
                            const TxPower = `tx_power_${portNo}`;

                            const CH1 = `CH1_${portNo}`;
                            const CH2 = `CH2_${portNo}`;
                            const CH3 = `CH3_${portNo}`;
                            const CH4 = `CH4_${portNo}`;

                            const UNI4M = `UNI4M_${portNo}`;
                            const UNI4T = `UNI4T_${portNo}`;
                            const NNI4T = `NNI4T_${portNo}`;

                            return (
                                <DebounceButton
                                    type="link"
                                    title="Modify"
                                    onClick={async () => {
                                        openCustomEditFormForD6000({
                                            title: "Modify",
                                            columnNum: 1,
                                            columns: (() => {
                                                const columns = [
                                                    [
                                                        {
                                                            key: `modulation`,
                                                            dataIndex: Modulation,
                                                            label: "Modulation",
                                                            inputType: "select",
                                                            required: true,
                                                            visible: true,
                                                            data: {
                                                                options: (() => {
                                                                    let options = [];
                                                                    switch (CardType) {
                                                                        case "4ME4C":
                                                                            switch (rowData.modulation) {
                                                                                case "1":
                                                                                    options = [
                                                                                        {
                                                                                            value: "1",
                                                                                            label: "400ZR-CFEC-16QAM"
                                                                                        }
                                                                                    ];
                                                                                    break;
                                                                                case "3":
                                                                                case "4":
                                                                                case "5":
                                                                                case "6":
                                                                                case "7":
                                                                                    options = [
                                                                                        {
                                                                                            value: "3",
                                                                                            label: "400ZR-OFEC-16QAM"
                                                                                        },
                                                                                        {
                                                                                            value: "4",
                                                                                            label: "300ZR-OFEC-8QAM"
                                                                                        },
                                                                                        {
                                                                                            value: "5",
                                                                                            label: "200ZR-OFEC-QPSK"
                                                                                        },
                                                                                        {
                                                                                            value: "6",
                                                                                            label: "100ZR-OFEC-QPSK"
                                                                                        },
                                                                                        {
                                                                                            value: "7",
                                                                                            label: "400ZR-CFEC-16QAM"
                                                                                        }
                                                                                    ];
                                                                                    break;

                                                                                default:
                                                                                    options = [];
                                                                            }
                                                                            break;

                                                                        case "4T4E4C":
                                                                            switch (rowData.modulation) {
                                                                                case "1":
                                                                                case "3":
                                                                                    options = [
                                                                                        {
                                                                                            value: "1",
                                                                                            label: "1x400G ZR-CFEC-16QAM"
                                                                                        },
                                                                                        {
                                                                                            value: "3",
                                                                                            label: "4x100G ZR-CFEC-16QAM"
                                                                                        }
                                                                                    ];
                                                                                    break;
                                                                                case "5":
                                                                                case "6":
                                                                                case "7":
                                                                                    options = [
                                                                                        {
                                                                                            value: "5",
                                                                                            label: "1x400G OFEC-16QAM"
                                                                                        },
                                                                                        {
                                                                                            value: "6",
                                                                                            label: "4x100G OFEC-16QAM"
                                                                                        },
                                                                                        {
                                                                                            value: "7",
                                                                                            label: "400ZR-CFEC-16QAM"
                                                                                        }
                                                                                    ];
                                                                                    break;

                                                                                default:
                                                                                    options = [];
                                                                            }
                                                                            break;

                                                                        default:
                                                                            options = [];
                                                                    }
                                                                    return options;
                                                                })()
                                                            }
                                                        }
                                                    ],
                                                    [
                                                        {
                                                            key: `wavelength`,
                                                            dataIndex: Wavelength,
                                                            label: "Wavelength",
                                                            inputType: "cascader",
                                                            required: true,
                                                            visible: true,
                                                            data: async () => {
                                                                return frequencyOptions;
                                                            },
                                                            transformValue: val => {
                                                                if (Array.isArray(val)) val = val[val.length - 1];
                                                                const match = val.match(/\d+/);
                                                                return match ? Number(match[0]) : null;
                                                            }
                                                        }
                                                    ],
                                                    [
                                                        {
                                                            key: `tx_power`,
                                                            dataIndex: TxPower,
                                                            label: "Target Output Power",
                                                            inputType: "number",
                                                            required: true,
                                                            visible: true,
                                                            step: 0.01,
                                                            placeholder: getTxPowerPlaceholderText(
                                                                parseInt(rowData.modulation),
                                                                CardType
                                                            ),
                                                            validator: (_, value) =>
                                                                validateTxPowerRange(
                                                                    parseInt(rowData.modulation),
                                                                    CardType,
                                                                    parseInt(value)
                                                                )
                                                        }
                                                    ],
                                                    [
                                                        {
                                                            key: `business_mode`,
                                                            dataIndex: ServiceType,
                                                            label: "Service Type",
                                                            inputType: "select",
                                                            required: true,
                                                            visible: true,
                                                            // noSetButton: CardType === "4T4E4C",
                                                            data: {
                                                                options: (() => {
                                                                    switch (CardType) {
                                                                        case "4ME4C":
                                                                            return [{label: "100GE", value: "1"}];
                                                                        // case "1M10":
                                                                        //     return [
                                                                        //         {label: "10GE LAN", value: "10GE LAN"},
                                                                        //         {label: "10GE WAN", value: "10GE WAN"},
                                                                        //         {label: "FC8G", value: "FC8G"},
                                                                        //         {label: "FC16G", value: "FC16G"}
                                                                        //     ];
                                                                        // case "32T5":
                                                                        //     return [
                                                                        //         {label: "GE", value: "GE"},
                                                                        //         {label: "10GE LAN", value: "10GE LAN"},
                                                                        //         {label: "10GE WAN", value: "10GE WAN"},
                                                                        //         {label: "25GE", value: "25GE"},
                                                                        //         {label: "FC8G", value: "FC8G"},
                                                                        //         {label: "FC16G", value: "FC16G"},
                                                                        //         {label: "FC32G", value: "FC32G"}
                                                                        //     ];
                                                                        case "4T4E4C":
                                                                            return [
                                                                                // {label: "100GE", value: "1"},
                                                                                {label: "400GE", value: "4"}
                                                                            ];
                                                                        // case "1T4":
                                                                        //     return [
                                                                        //         {label: "10GE LAN", value: "10GE LAN"},
                                                                        //         {label: "10GE WAN", value: "10GE WAN"},
                                                                        //         {label: "25GE", value: "25GE"},
                                                                        //         {label: "40GE", value: "40GE"},
                                                                        //         {label: "100GE", value: "100GE"}
                                                                        //     ];
                                                                        default:
                                                                            return [];
                                                                    }
                                                                })()
                                                            }
                                                        }
                                                    ],
                                                    [
                                                        {
                                                            key: `fec_switch`,
                                                            dataIndex: FECType,
                                                            label: "FEC Type",
                                                            inputType: "select",
                                                            required: true,
                                                            visible: true,
                                                            // noSetButton: CardType === "4T4E4C",
                                                            data: {
                                                                options:
                                                                    CardType === "4T4E4C"
                                                                        ? [
                                                                              {
                                                                                  label: "ENABLED",
                                                                                  value: "0"
                                                                              }
                                                                          ]
                                                                        : [
                                                                              {
                                                                                  label: "ENABLED",
                                                                                  value: "0"
                                                                              },
                                                                              {
                                                                                  label: "DISABLED",
                                                                                  value: "1"
                                                                              }
                                                                          ]
                                                            },
                                                            transformValue: value => {
                                                                return `${portIndex}${value}`;
                                                            }
                                                        }
                                                    ],
                                                    [
                                                        {
                                                            key: `als`,
                                                            dataIndex: ALS,
                                                            label: "ALS",
                                                            inputType: "select",
                                                            required: true,
                                                            visible: true,
                                                            data: {
                                                                options: [
                                                                    {label: "ENABLED", value: "1"},
                                                                    {label: "DISABLED", value: "0"}
                                                                ]
                                                            }
                                                        }
                                                    ],
                                                    [
                                                        {
                                                            key: `tx_switch`,
                                                            dataIndex: LaserEnable,
                                                            label: "Laser Enable",
                                                            inputType: "select",
                                                            required: true,
                                                            visible: true,
                                                            disabled: rowData.als === "1",
                                                            data: {
                                                                options: [
                                                                    {label: "TRUE", value: "0"},
                                                                    {label: "FALSE", value: "1"}
                                                                ]
                                                            }
                                                        }
                                                    ],

                                                    [
                                                        {
                                                            key: `work_mode`,
                                                            dataIndex: LoopBack,
                                                            label: "Loopback",
                                                            inputType: "select",
                                                            required: true,
                                                            visible: true,
                                                            configurable: false,
                                                            data: {
                                                                options: (() => {
                                                                    if (CardType === "4ME4C") {
                                                                        return [
                                                                            {label: "UNI", value: "UNI4M"},
                                                                            {label: "NNI", value: "NNI4M"}
                                                                        ];
                                                                    }
                                                                    if (CardType === "4T4E4C") {
                                                                        return [
                                                                            {label: "UNI", value: "UNI4T"},
                                                                            {label: "NNI", value: "NNI4T"}
                                                                        ];
                                                                    }
                                                                    return [];
                                                                })()
                                                            }
                                                        }
                                                    ],

                                                    [
                                                        {
                                                            key: `CH1`,
                                                            dataIndex: CH1,
                                                            label: "CH1",
                                                            inputType: "select",
                                                            required: true,
                                                            visible: false,
                                                            data: {
                                                                options: [
                                                                    {label: "NONE", value: "71"},
                                                                    {label: "FACILITY", value: "61"},
                                                                    {label: "TERMINAL", value: "51"}
                                                                ]
                                                            },
                                                            dependOn: {
                                                                dataIndex: LoopBack,
                                                                expected: "NNI4M"
                                                            }
                                                        },
                                                        {
                                                            key: `CH2`,
                                                            dataIndex: CH2,
                                                            label: "CH2",
                                                            inputType: "select",
                                                            required: true,
                                                            visible: false,
                                                            data: {
                                                                options: [
                                                                    {label: "NONE", value: "72"},
                                                                    {label: "FACILITY", value: "62"},
                                                                    {label: "TERMINAL", value: "52"}
                                                                ]
                                                            },
                                                            dependOn: {
                                                                dataIndex: LoopBack,
                                                                expected: "NNI4M"
                                                            }
                                                        },
                                                        {
                                                            key: `CH3`,
                                                            dataIndex: CH3,
                                                            label: "CH3",
                                                            inputType: "select",
                                                            required: true,
                                                            visible: false,
                                                            data: {
                                                                options: [
                                                                    {label: "NONE", value: "73"},
                                                                    {label: "FACILITY", value: "63"},
                                                                    {label: "TERMINAL", value: "53"}
                                                                ]
                                                            },
                                                            dependOn: {
                                                                dataIndex: LoopBack,
                                                                expected: "NNI4M"
                                                            }
                                                        },
                                                        {
                                                            key: `CH4`,
                                                            dataIndex: CH4,
                                                            label: "CH4",
                                                            inputType: "select",
                                                            required: true,
                                                            visible: false,
                                                            data: {
                                                                options: [
                                                                    {label: "NONE", value: "74"},
                                                                    {label: "FACILITY", value: "64"},
                                                                    {label: "TERMINAL", value: "54"}
                                                                ]
                                                            },
                                                            dependOn: {
                                                                dataIndex: LoopBack,
                                                                expected: "NNI4M"
                                                            }
                                                        }
                                                    ],

                                                    [
                                                        {
                                                            key: `UNI4M`,
                                                            dataIndex: UNI4M,
                                                            label: "UNI",
                                                            inputType: "select",
                                                            required: true,
                                                            data: {
                                                                options: [
                                                                    {label: "NONE", value: "7"},
                                                                    {label: "FACILITY", value: "4"},
                                                                    {label: "TERMINAL", value: "3"}
                                                                ]
                                                            },
                                                            dependOn: {
                                                                dataIndex: LoopBack,
                                                                expected: "UNI4M"
                                                            }
                                                        }
                                                    ],

                                                    [
                                                        {
                                                            key: `UNI4T`,
                                                            dataIndex: UNI4T,
                                                            label: "UNI",
                                                            inputType: "select",
                                                            required: true,
                                                            data: {
                                                                options: [
                                                                    {label: "NONE", value: "0"},
                                                                    {label: "FACILITY", value: "2"},
                                                                    {label: "TERMINAL", value: "1"}
                                                                ]
                                                            },
                                                            dependOn: {
                                                                dataIndex: LoopBack,
                                                                expected: "UNI4T"
                                                            }
                                                        }
                                                    ],
                                                    [
                                                        {
                                                            key: `NNI4T`,
                                                            dataIndex: NNI4T,
                                                            label: "NNI",
                                                            inputType: "select",
                                                            required: true,
                                                            data: {
                                                                options: [
                                                                    {label: "NONE", value: "0"},
                                                                    {label: "FACILITY", value: "4"},
                                                                    {label: "TERMINAL", value: "3"}
                                                                ]
                                                            },
                                                            dependOn: {
                                                                dataIndex: LoopBack,
                                                                expected: "NNI4T"
                                                            }
                                                        }
                                                    ]
                                                ];

                                                if (
                                                    CardType === "4ME4C" &&
                                                    ["L1", "L2", "L3", "L4"].includes(rowData.name)
                                                ) {
                                                    const baseKeys = [
                                                        "modulation",
                                                        "wavelength",
                                                        "tx_power",
                                                        "als",
                                                        "tx_switch",
                                                        "work_mode",
                                                        "CH1",
                                                        "CH2",
                                                        "CH3",
                                                        "CH4",
                                                        "UNI4M"
                                                    ];

                                                    return columns.filter(column => baseKeys.includes(column[0].key));
                                                }

                                                if (
                                                    CardType === "4T4E4C" &&
                                                    ["L1", "L2", "L3", "L4"].includes(rowData.name)
                                                ) {
                                                    const baseKeys = [
                                                        "modulation",
                                                        "wavelength",
                                                        "tx_power",
                                                        "als",
                                                        "tx_switch",
                                                        "work_mode",
                                                        "UNI4T",
                                                        "NNI4T"
                                                    ];

                                                    return columns.filter(column => baseKeys.includes(column[0].key));
                                                }

                                                if (["C1", "C2", "C3", "C4"].includes(rowData.name)) {
                                                    return columns.filter(column =>
                                                        ["business_mode", "fec_switch", "als", "tx_switch"].includes(
                                                            column[0].key
                                                        )
                                                    );
                                                }

                                                return columns;
                                            })(),
                                            getData: async () => {
                                                const ip = NeIP;
                                                const response = await queryDCSConfig(ip, null, CardId);
                                                const _data = response.data?.configData;

                                                const initialValues = {
                                                    [Modulation]: _data.modulation_[portIndex],
                                                    [Wavelength]: _data.wavelength_Lvalue_[portIndex],
                                                    [TxPower]: _data.tx_power_[portIndex],
                                                    [ServiceType]: (() => {
                                                        if (CardType === "4ME4C") {
                                                            return _data.business_mode_;
                                                        }
                                                        if (CardType === "4T4E4C") {
                                                            return "4";
                                                        }
                                                    })(),
                                                    [FECType]: (() => {
                                                        if (CardType === "4ME4C") {
                                                            if (portNo === "1") {
                                                                return "";
                                                            }
                                                            const fecSwitchValue = _data.fec_switch_;
                                                            if (fecSwitchValue === "" || fecSwitchValue === NULL_VALUE)
                                                                return "";
                                                            const binaryStr = parseInt(fecSwitchValue, 10)
                                                                .toString(2)
                                                                .padStart(4, "0");
                                                            const bitPosition = 4 - parseInt(portNo) + 1;
                                                            return binaryStr.length >= bitPosition
                                                                ? binaryStr.charAt(bitPosition)
                                                                : "";
                                                        }
                                                        if (CardType === "4T4E4C") {
                                                            if (["1", "2", "3", "4"].includes(portNo)) {
                                                                return "";
                                                            }
                                                            return "0";
                                                        }
                                                    })(),
                                                    [LoopBack]: _data.CH1_[portIndex],
                                                    [ALS]: _data.als_[portIndex],
                                                    [LaserEnable]: _data.tx_switch_[portIndex]
                                                };

                                                const modulationMap = {
                                                    "4ME4C": {
                                                        1: "400ZR-CFEC-16QAM",
                                                        // 2: "400GZR-Single Wavalength",
                                                        3: "400ZR-OFEC-16QAM",
                                                        4: "300ZR-OFEC-8QAM",
                                                        5: "200ZR-OFEC-QPSK",
                                                        6: "100ZR-OFEC-QPSK",
                                                        7: "400ZR-CFEC-16QAM"
                                                    },
                                                    "4T4E4C": {
                                                        1: "1x400G ZR-CFEC-16QAM",
                                                        // 2: "1x400G ZR-Single Wavalength",
                                                        3: "4x100G ZR-CFEC-16QAM",
                                                        // 4: "4x100G ZR-Single Wavalength",
                                                        5: "1x400G OFEC-16QAM",
                                                        6: "4x100G OFEC-16QAM",
                                                        7: "400ZR-CFEC-16QAM"
                                                        // 11: "100G",
                                                        // 12: "100GBASE-ZR NO-FEC",
                                                        // 13: "100GBASE-ZR RS-FEC"
                                                    }
                                                };

                                                const businessModeMap = {
                                                    "4ME4C": {
                                                        L1: {1: "400GE"},
                                                        C1: {1: "100GE"},
                                                        C2: {1: "100GE"},
                                                        C3: {1: "100GE"},
                                                        C4: {1: "100GE"}
                                                    },
                                                    "4T4E4C": {
                                                        1: "100GE",
                                                        4: "400GE"
                                                    }
                                                };

                                                const alsMap = {
                                                    0: "DISABLED",
                                                    1: "ENABLED"
                                                };

                                                const fecMap = {
                                                    0: "ENABLED",
                                                    1: "DISABLED"
                                                };

                                                const waveValue = 191.4 + (initialValues[Wavelength] - 14) * 0.1;
                                                const wavelengthNm = (2.99792458e8 / (waveValue * 1e12)) * 1e9;

                                                const enabledM1Values = [
                                                    1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 32, 48, 64,
                                                    80, 96, 112, 128, 144, 160, 176, 192, 208, 224, 240
                                                ];

                                                const workModeMap = loopbackValue => {
                                                    if (loopbackValue === 0) {
                                                        return "NONE";
                                                    }

                                                    if (CardType === "4ME4C") {
                                                        if (loopbackValue === 34 || loopbackValue === 68) {
                                                            return "UNI4M";
                                                        }
                                                        if (enabledM1Values.includes(loopbackValue)) {
                                                            return "NNI4M";
                                                        }
                                                    }

                                                    if (CardType === "4T4E4C") {
                                                        if (loopbackValue === 1 || loopbackValue === 2) {
                                                            return "UNI4T";
                                                        }
                                                        if (loopbackValue === 3 || loopbackValue === 4) {
                                                            return "NNI4T";
                                                        }
                                                    }
                                                    return NULL_VALUE;
                                                };

                                                const mappedValues = {
                                                    [Modulation]:
                                                        modulationMap[CardType]?.[
                                                            parseInt(initialValues[Modulation])
                                                        ] || "",
                                                    [Wavelength]:
                                                        parseInt(initialValues[Wavelength]) === 0
                                                            ? NULL_VALUE
                                                            : `${waveValue.toFixed(2)}THz-${wavelengthNm.toFixed(2)}nm`,
                                                    [TxPower]: initialValues[TxPower],
                                                    [ServiceType]: (() => {
                                                        const cardMapping = businessModeMap[CardType] || {};
                                                        if (cardMapping[rowData.name]) {
                                                            return (
                                                                cardMapping[rowData.name][
                                                                    parseInt(initialValues[ServiceType])
                                                                ] || "100GE"
                                                            );
                                                        }
                                                        return (
                                                            businessModeMap[CardType][
                                                                parseInt(initialValues[ServiceType])
                                                            ] || ""
                                                        );
                                                    })(),
                                                    [LoopBack]: workModeMap(parseInt(initialValues[LoopBack])),
                                                    [ALS]: alsMap[parseInt(initialValues[ALS])] || "",
                                                    [LaserEnable]:
                                                        initialValues[LaserEnable] === "0" ? "TRUE" : "FALSE",
                                                    [FECType]: fecMap[parseInt(initialValues[FECType])] || ""
                                                };

                                                mappedValues[CH1] = getLoopBackStatus(
                                                    initialValues[LoopBack],
                                                    1,
                                                    CardType
                                                );
                                                mappedValues[CH2] = getLoopBackStatus(
                                                    initialValues[LoopBack],
                                                    2,
                                                    CardType
                                                );
                                                mappedValues[CH3] = getLoopBackStatus(
                                                    initialValues[LoopBack],
                                                    3,
                                                    CardType
                                                );
                                                mappedValues[CH4] = getLoopBackStatus(
                                                    initialValues[LoopBack],
                                                    4,
                                                    CardType
                                                );

                                                mappedValues[UNI4M] = getLoopBackStatus(
                                                    initialValues[LoopBack],
                                                    -1,
                                                    CardType
                                                );

                                                mappedValues[NNI4T] = getLoopBackStatus(
                                                    initialValues[LoopBack],
                                                    portNo,
                                                    CardType
                                                );

                                                mappedValues[UNI4T] = getLoopBackStatus(
                                                    initialValues[LoopBack],
                                                    -1,
                                                    CardType
                                                );
                                                return {
                                                    ...initialValues,
                                                    ...mappedValues
                                                };
                                            },
                                            setDataAPI: () => {
                                                return {
                                                    APIName: batchModifyDCSConfig,
                                                    APIParameter: () => {
                                                        return {
                                                            ip: NeIP,
                                                            cardId: CardId
                                                        };
                                                    }
                                                };
                                            },
                                            afterUpdate
                                        });
                                    }}
                                >
                                    Modify
                                </DebounceButton>
                            );
                        }
                    }
                ]
            },
            power: {
                columns: [
                    {dataIndex: "key", title: "Index"},
                    {dataIndex: "ne_name", title: "NE Name"},
                    {dataIndex: "ne_id", title: "NE ID"},
                    {dataIndex: "card_type", title: "Card Type"},
                    {dataIndex: "card_name", title: "Card Name"},
                    {
                        dataIndex: "no",
                        title: "Slot NO.",
                        fixed: "left",
                        render: () => SlotNo
                    },
                    {
                        dataIndex: "name",
                        title: "Port Name",
                        fixed: "left",
                        render: text => `PORT${CardSuffix}-${text}`
                    },
                    {
                        dataIndex: "input_optical_power",
                        title: "Input Optical Power",
                        unit: "dBm",
                        render: (value, rowData) => {
                            if (
                                parseInt(value) === -50 ||
                                rowData.module_wavelength === "0000.00" ||
                                rowData.module_wavelength === NULL_VALUE
                            ) {
                                return NULL_VALUE;
                            }
                            return value >= 0 ? Math.abs(value) : value;
                        }
                    },
                    {
                        dataIndex: "output_optical_power",
                        title: "Output Optical Power",
                        unit: "dBm",
                        render: (value, rowData) => {
                            if (
                                parseInt(value) === -50 ||
                                rowData.module_wavelength === "0000.00" ||
                                rowData.module_wavelength === NULL_VALUE
                            ) {
                                return NULL_VALUE;
                            }
                            return value >= 0 ? Math.abs(value) : value;
                        }
                    },
                    {
                        dataIndex: "overlow_input_threshold",
                        title: "Overlow Input Threshold",
                        unit: "dBm",
                        render: value => {
                            if (value === NULL_VALUE) {
                                return (-28.0).toFixed(2);
                            }
                            return parseFloat(value).toFixed(2);
                        }
                    },
                    {
                        dataIndex: "overhigh_input_threshold",
                        title: "Overhigh Input Threshold",
                        unit: "dBm",
                        render: (value, rowData) => {
                            if (value === NULL_VALUE) {
                                if (rowData.name.startsWith("C")) {
                                    return (12.0).toFixed(2);
                                }
                                if (rowData.name.startsWith("L")) {
                                    return (13.0).toFixed(2);
                                }
                            }
                            return parseFloat(value).toFixed(2);
                        }
                    },
                    {
                        dataIndex: "overlow_output_threshold",
                        title: "Overlow Output Threshold",
                        unit: "dBm",
                        render: (value, rowData) => {
                            if (value === NULL_VALUE) {
                                if (rowData.name.startsWith("C")) {
                                    return (-10.0).toFixed(2);
                                }
                                if (rowData.name.startsWith("L")) {
                                    return (-15.0).toFixed(2);
                                }
                            }
                            return parseFloat(value).toFixed(2);
                        }
                    },
                    {
                        dataIndex: "overhigh_output_threshold",
                        title: "Overhigh Output Threshold",
                        unit: "dBm",
                        render: (value, rowData) => {
                            if (value === NULL_VALUE) {
                                if (rowData.name.startsWith("C")) {
                                    return (12.0).toFixed(2);
                                }
                                if (rowData.name.startsWith("L")) {
                                    return (3.0).toFixed(2);
                                }
                            }
                            return parseFloat(value).toFixed(2);
                        }
                    },
                    {
                        dataIndex: "operation",
                        title: "Operation",
                        fixed: "right",
                        sorter: false,
                        render: (value, rowData) => {
                            const portNo = rowData.no;
                            const OverlowInputThreshold = `overlow_input_threshold_${portNo}`;
                            const OverhighInputThreshold = `overhigh_input_threshold_${portNo}`;
                            const OverlowOutputThreshold = `overlow_output_threshold_${portNo}`;
                            const OverhighOutputThreshold = `overhigh_output_threshold_${portNo}`;

                            return (
                                <DebounceButton
                                    type="link"
                                    title="Modify"
                                    onClick={() => {
                                        openCustomEditFormForD6000({
                                            title: "Modify",
                                            columnNum: 1,
                                            columns: [
                                                [
                                                    {
                                                        key: "overlow_input_threshold",
                                                        dataIndex: OverlowInputThreshold,
                                                        label: "Overlow Input Threshold",
                                                        inputType: "number",
                                                        required: true,
                                                        visible: true,
                                                        step: 0.01
                                                    },
                                                    {
                                                        key: "overhigh_input_threshold",
                                                        dataIndex: OverhighInputThreshold,
                                                        label: "Overhigh Input Threshold",
                                                        inputType: "number",
                                                        required: true,
                                                        visible: true,
                                                        step: 0.01
                                                    },
                                                    {
                                                        key: "overlow_output_threshold",
                                                        dataIndex: OverlowOutputThreshold,
                                                        label: "Overlow Output Threshold",
                                                        inputType: "number",
                                                        required: true,
                                                        visible: true,
                                                        step: 0.01
                                                    },
                                                    {
                                                        key: "overhigh_output_threshold",
                                                        dataIndex: OverhighOutputThreshold,
                                                        label: "Overhigh Output Threshold",
                                                        inputType: "number",
                                                        required: true,
                                                        visible: true,
                                                        step: 0.01
                                                    }
                                                ]
                                            ],
                                            getData: async () => {
                                                const ip = NeIP;
                                                const response = await queryDCSConfig(ip, null, CardId);
                                                const _data = response.data?.configData;
                                                const portIndex = parseInt(portNo) - 1;

                                                const formatThreshold = val => {
                                                    if (
                                                        val === null ||
                                                        val === undefined ||
                                                        val === "" ||
                                                        val === NULL_VALUE
                                                    )
                                                        return NULL_VALUE;
                                                    const num = parseFloat(val);
                                                    if (isNaN(num) || Math.abs(num) < 1e-6) return NULL_VALUE;
                                                    return num.toFixed(2);
                                                };

                                                const initialValues = {
                                                    [OverlowInputThreshold]: formatThreshold(
                                                        _data.overlow_input_threshold_value_?.[portIndex]
                                                    ),
                                                    [OverhighInputThreshold]: formatThreshold(
                                                        _data.overhigh_input_threshold_value_?.[portIndex]
                                                    ),
                                                    [OverlowOutputThreshold]: formatThreshold(
                                                        _data.overlow_output_threshold_value_?.[portIndex]
                                                    ),
                                                    [OverhighOutputThreshold]: formatThreshold(
                                                        _data.overhigh_output_threshold_value_?.[portIndex]
                                                    )
                                                };

                                                return initialValues;
                                            },
                                            setDataAPI: () => {
                                                return {
                                                    APIName: batchModifyDCSConfig,
                                                    APIParameter: () => {
                                                        return {
                                                            ip: NeIP,
                                                            cardId: CardId
                                                        };
                                                    }
                                                };
                                            },
                                            afterUpdate
                                        });
                                    }}
                                >
                                    Modify
                                </DebounceButton>
                            );
                        }
                    }
                ]
            }
        }
    };
    const getTableConfig = () => {
        const typeMap = {
            power: data.type,
            edfa: "EDFA",
            oeo: "OEO"
        };
        const configType = typeMap[tabType];
        return tabType === "linecard"
            ? TableConfigs[tabType]?.[label]?.columns
            : TableConfigs[tabType]?.[configType]?.columns || [];
    };

    const columns = getTableConfig().map(col => {
        const {dataIndex, title, unit, render, fixed} = col;
        const column = {
            title: title || dataIndex.replace(/_/g, " ").replace(/(^|\s)\S/g, match => match.toUpperCase()),
            dataIndex,
            key: dataIndex,
            fixed,
            sorter: (a, b) => {
                if (typeof a[dataIndex] === "number" && typeof b[dataIndex] === "number") {
                    return a[dataIndex] - b[dataIndex];
                }
                if (typeof a[dataIndex] === "string" && typeof b[dataIndex] === "string") {
                    return a[dataIndex].localeCompare(b[dataIndex]);
                }
                return 0;
            }
        };
        if (unit) {
            column.title += ` (${unit})`;
        }
        if (render) {
            column.render = render;
        }
        return column;
    });

    return (
        <Table
            columns={columns}
            dataSource={tableData}
            bordered
            pagination={{
                current: pagination.current,
                pageSize: pagination.pageSize,
                total: tableData.length,
                onChange: (page, pageSize) => {
                    setPagination({...pagination, current: page, pageSize});
                },
                showSizeChanger: true,
                pageSizeOptions: ["10", "20", "50", "100"],
                showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} items`
            }}
            style={{whiteSpace: "nowrap"}}
        />
    );
};
