/* eslint-disable no-console */
import {Button, Col, Image, Input, message, Row, Select, Space, Switch, Tabs, Tag, theme, Typography} from "antd";
import {
    asciiHexConvertToPlainText,
    classNames,
    convertToArray,
    DebounceButton,
    getAttrValue,
    getDeviceStateValue,
    getText,
    NULL_VALUE,
    OCH_MODE_SUPPORT_MAP,
    OPERATIONAL_MODE_MAP,
    plainTextConvertToAsciiHex,
    PORT_MAPPING_MAP,
    removeNS,
    sortLabel
} from "@/modules-otn/utils/util";
import {apiEditRpc, getStateData, NEGet, NESet, netconfByXML, netconfGetByXML, objectGet} from "@/modules-otn/apis/api";
import {useSelector} from "react-redux";
import React, {useEffect, useRef, useState} from "react";
import Alarm from "@/modules-otn/pages/otn/alarm/alarm";
import {CustomTable} from "@/modules-otn/components/common/custom_table";
import {openModalRpc} from "@/modules-otn/components/form/edit_rpc";
import {openCustomTable} from "@/modules-otn/components/form/edit_table_custom";
import {gLabelList} from "@/store/modules/otn/languageOTNSlice";
import openCustomEditForm from "@/modules-otn/components/form/edit_form_custom";
import useUserRight from "@/modules-otn/hooks/useUserRight";
import Icon from "@ant-design/icons";
import {
    editTableIcon,
    plusDisableIcon,
    plusIcon,
    refreshEnabledIcon
} from "@/modules-otn/pages/otn/device/device_icons";
import {openEditComponent} from "@/modules-otn/components/form/edit_component";
import {openModalCreate} from "@/modules-otn/components/form/create_form";
import {getSignalType, updatePortSignalType} from "@/modules-otn/pages/otn/service/resource_manager";
import {decInfoIcon, hexInfoIcon} from "@/modules-otn/pages/otn/service/service_icon";
import ResourceTree from "@/modules-otn/components/chassis/resource_tree";
import {useRequest} from "ahooks";
import {smallModal} from "@/modules-otn/components/modal/custom_modal";

import OA1825 from "@/assets/L0/OA1825.png";
import OA1835 from "@/assets/L0/OA1835.png";
import OLA2525 from "@/assets/L0/OLA2525.png";
import OLA3535 from "@/assets/L0/OLA3535.png";
import OMD48ECM from "@/assets/L0/OMD48ECM.png";
import ROADM from "@/assets/L0/ROADM-09T.png";
import TFF04 from "@/assets/L0/TFF04.png";

import LINECARD_2MC2 from "@/assets/L1/2MC2.png";
import LINECARD_4MC4 from "@/assets/L1/4MC4.png";
import LINECARD_11MC2 from "@/assets/L1/11MC2.png";
import LINECARD_20MC2 from "@/assets/L1/20MC2.png";
import RAMAN_BOX from "@/assets/L0/RAMAN_BOX.png";
import CORFA from "@/assets/L0/CORFA.png";
import RFA from "@/assets/L0/RFA.png";
import {openDBModalCreate} from "@/modules-otn/components/form/create_form_db";
import MutilColumnForm from "../common/mutil_column_form";
import styles from "./service_common.module.scss";
import {getOTNDeviceIP} from "@/modules-ampcon/apis/otn";
import {
    getFMTDeviceCard,
    getFMTDevicePort,
    getFMTDeviceSinglePort,
    getDCSDeviceCard,
    getDCSDevicePort
} from "@/modules-ampcon/apis/fmt";
import {getM6200DeviceCard, getM6200DevicePort} from "@/modules-ampcon/apis/m6200_api";
import {DynamicTable} from "@/modules-ampcon/pages/Resource/DeviceDisplay/components/layer0_table";
import {Layer0Config} from "@/modules-ampcon/pages/Resource/DeviceDisplay/components/config_layer0";
import {Layer1Config} from "@/modules-ampcon/pages/Resource/DeviceDisplay/components/config_layer1";

const callOTNAPI = (apiType, tabType, args = null) => {
    const apiTypeMapping = {
        OTNDeviceIP: getOTNDeviceIP,
        DeviceCard: type => {
            const deviceTypeMapping = {
                7: getFMTDeviceCard,
                8: getDCSDeviceCard,
                9: getM6200DeviceCard
            };
            return deviceTypeMapping[type] || null;
        }
    };

    const tabTypeMapping = {
        power: "ALL",
        linecard: "ALL",
        edfa: "EDFA",
        oeo: "OEO"
    };

    if (!apiTypeMapping[apiType] || !tabTypeMapping[tabType]) {
        return Promise.reject(new Error("Invalid apiType or tabType"));
    }

    const apiFunction = apiTypeMapping[apiType];
    if (apiType === "DeviceCard" && args && args.deviceType) {
        const {deviceType, ...otherArgs} = args;
        const selectedFunction = apiFunction(deviceType);
        if (!selectedFunction) {
            return Promise.reject(new Error(`No function found for deviceType: ${deviceType}`));
        }

        return selectedFunction(tabTypeMapping[tabType], otherArgs);
    }
    return apiFunction(tabTypeMapping[tabType], args);
};

const DeviceImageMap = {
    "2MC2": LINECARD_2MC2,
    "4MC4": LINECARD_4MC4,
    "11MC2": LINECARD_11MC2,
    "20MC2": LINECARD_20MC2,
    TFF: TFF04,
    ROADM,
    OMD48ECM,
    OLA2525,
    OLA3535,
    OA1825,
    OA1835,
    RAMAN_BOX,
    CORFA,
    RFA
};

const {Paragraph} = Typography;

const HexComponent = ({value}) => {
    const [showType, setShowType] = useState(false);
    const [tipType, setTipType] = useState(false);
    const {labelList} = useSelector(state => state.languageOTN);
    return (
        <Paragraph
            style={{margin: 0, display: "flex", alignItems: "center"}}
            editable={{
                icon: <Icon component={showType ? hexInfoIcon : decInfoIcon} />,
                tooltip: tipType ? labelList.hex : labelList.dec,
                editing: false,
                showType: false,
                onStart: () => {
                    setTipType(!tipType);
                    setTimeout(() => {
                        setShowType(!showType);
                    }, 100);
                }
            }}
        >
            {tipType ? asciiHexConvertToPlainText(value) : value}
        </Paragraph>
    );
};

const ServiceCommon = ({tabType}) => {
    const {dataChanged} = useSelector(state => state.notification);
    const {labelList} = useSelector(state => state.languageOTN);
    const {neNameMap} = useSelector(state => state.neName);
    const {
        token: {colorPrimary}
    } = theme.useToken();
    const readyOnlyRight = useUserRight();
    const [selectNe, setSelectNe] = useState();
    const [activeTab, setActiveTab] = useState();
    const activeTabRef = useRef(activeTab);
    const [neOptions, setNeOptions] = useState([{value: "", label: "No Data", disabled: true}]);
    const [cardOptions, setCardOptions] = useState([{value: "", label: "No Data", disabled: true}]);
    const [portOptions, setPortOptions] = useState([{value: "", label: "No Data", disabled: true}]);
    const selectNeRef = useRef(selectNe);
    const [selectCard, setSelectCard] = useState();
    const selectCardRef = useRef(selectCard);
    const [selectPort, setSelectPort] = useState();
    const selectPortRef = useRef(selectPort);
    const [cardLoading, setCardLoading] = useState(false);
    const [portLoading, setPortLoading] = useState(false);
    const [tableData, setTableData] = useState([]);
    const [loading, setLoading] = useState(false);
    const [drawerTreeData, setDrawerTreeData] = useState([]);
    const [filterTree, setFilterTree] = useState([]);
    const [selectedTreeNode, setSelectedTreeNode] = useState();
    const selectedTreeNodeRef = useRef(selectedTreeNode);
    const [filterTableData, setFilterTableData] = useState(null);
    const [switchLoading, setSwitchLoading] = useState({});
    const [frequencyOptions, setFrequencyOptions] = useState([]);

    const [portData, setPortData] = useState([]);
    const [portFilterData, setPortFilterData] = useState(null);
    const [ttiData, setTtiData] = useState([]);
    const [ttiFilterData, setTtiFilterData] = useState(null);
    const [lldpData, setLldpData] = useState([]);
    const [lldpFilterData, setLldpFilterData] = useState(null);
    const [edfaData, setEdfaData] = useState([]);
    const [oscData, setOscData] = useState([]);

    const [updateSignalType, setUpdateSignalType] = useState(false);

    const [ampconOTNDeviceList, setAmpconOTNDeviceList] = useState([]);
    const [otnDevicePorts, setOTNDevicePorts] = useState();
    const [otnDevicePortOptions, setOTNDevicePortOptions] = useState();
    const [selectNeType, setSelectNeType] = useState();
    const selectNeTypeRef = useRef(selectNeType);

    const DataMapping = {
        port: portFilterData ?? portData,
        "otn-tti": ttiFilterData ?? ttiData,
        lldp: lldpFilterData ?? lldpData,
        edfa: edfaData,
        osc: oscData
    };

    const onSelectNe = (value, option) => {
        if (selectCard?.[tabType] || selectCard?.value) onSelectCard(null);
        if (selectPortRef.current) onSelectPort(null);
        // setOTNDevicePortOptions([]);
        setSelectNe(value);
        selectNeRef.current = value;
        setSelectNeType(option?.type);
        selectNeTypeRef.current = option?.type;
    };

    const updateSelectedTreeNode = value => {
        setSelectedTreeNode(value);
        selectedTreeNodeRef.current = value;
    };

    const updateFilterTableData = value => {
        if (loading) {
            return;
        }
        setFilterTableData(value);
    };

    const onSelectCard = async (value, option) => {
        if (ampconOTNDeviceList.some(device => device.ip === selectNe)) {
            if (value) {
                const type = option?.type;
                // const ret = await getFMTDevicePort(value, type);
                let ret;
                if (tabType === "linecard") {
                    ret = await getDCSDevicePort(value, type);
                } else if (tabType === "oeo") {
                    ret = await getM6200DevicePort(value, type);
                } else {
                    ret = await getFMTDevicePort(value, type);
                }
                setOTNDevicePorts(ret.data.info);
                const port_data = ret.data.port_name.map(port => ({
                    label: port,
                    value: port
                }));
                setOTNDevicePortOptions(port_data);
            } else {
                setSelectCard(null);
                selectCardRef.current = null;
                setOTNDevicePorts([]);
                setOTNDevicePortOptions([]);
            }
            setSelectCard(option);
            selectCardRef.current = option;
            if (selectPortRef.current) onSelectPort(null);
            return;
        }
        if (selectPortRef.current) onSelectPort(null);
        let newVal;
        if (!value) {
            newVal = {...selectCard};
            delete newVal[tabType];
            delete newVal[`${tabType}_cardType`];
        } else {
            newVal = {...selectCard, [tabType]: value, [`${tabType}_cardType`]: option?.label};
        }
        setSelectCard(newVal);
        selectCardRef.current = newVal;
    };

    const onSelectPort = async value => {
        if (ampconOTNDeviceList.some(device => device.ip === selectNe)) {
            if (value) {
                const ret = await getFMTDeviceSinglePort(selectCardRef.current?.value, value);
                setOTNDevicePorts(ret.data);
            } else if (selectCardRef.current) {
                const ret = await getFMTDevicePort(selectCardRef.current?.value);
                setOTNDevicePorts(ret.data.info);
            } else {
                setSelectPort(null);
                selectPortRef.current = null;
                setOTNDevicePorts([]);
            }
        }
        setSelectPort(value);
        selectPortRef.current = value;
    };

    const updateTableData = (subTabType, card, dataList) => {
        if (
            (!activeTabRef.current || subTabType === activeTabRef.current) &&
            (!selectCardRef.current[subTabType] || card === selectCardRef.current[tabType])
        ) {
            setTableData(dataList);
            if (selectedTreeNodeRef.current) {
                execFilterTableData(dataList);
            }
        }
    };
    useEffect(() => {
        const frequencyOptions = [
            {value: "50GHz", label: "50GHz", children: []},
            {value: "75GHz", label: "75GHz", children: []},
            {value: "100GHz", label: "100GHz", children: []}
        ];
        for (let i = 0; i < 96; i++) {
            const value = 191.35 + i * 0.05;
            const frequencyNm = (
                Math.round(((((2.99792458 * 10 ** 8) / value) * 1000000) / 1000000000) * 100) / 100
            ).toFixed(2);
            frequencyOptions[0].children.push({
                label:
                    i < 48
                        ? `C${i + 13}-${value.toFixed(2)}THz-${frequencyNm}nm`
                        : `H${i - 35}-${value.toFixed(2)}THz-${frequencyNm}nm`,
                value:
                    i < 48
                        ? `C${i + 13}-${value.toFixed(2)}THz-${frequencyNm}nm`
                        : `H${i - 35}-${value.toFixed(2)}THz-${frequencyNm}nm`
            });
        }
        for (let i = 0; i < 48; i++) {
            const value = 191.4 + i * 0.1;
            const frequencyTHz = value.toFixed(2);
            const frequencyNm = (
                Math.round(((((2.99792458 * 10 ** 8) / value) * 1000000) / 1000000000) * 100) / 100
            ).toFixed(2);
            const label = `C${i + 14}-${frequencyTHz}THz-${frequencyNm}nm`;
            const optionValue = `C${i + 14}-${frequencyTHz}THz-${frequencyNm}nm`;
            frequencyOptions[2].children.push({
                label,
                value: optionValue
            });
        }
        for (let i = 0; i < 64; i++) {
            const value = 196.0375 - i * 0.075;
            const frequencyTHz = value.toFixed(4);
            const frequencyNm = (
                Math.round(((((2.99792458 * 10 ** 8) / value) * 1000000) / 1000000000) * 100) / 100
            ).toFixed(2);
            const label = `CM${i + 1}-${frequencyTHz}THz-${frequencyNm}nm`;
            const optionValue = `CM${i + 1}-${frequencyTHz}THz-${frequencyNm}nm`;
            frequencyOptions[1].children.push({
                label,
                value: optionValue
            });
            setFrequencyOptions(frequencyOptions);
        }
    }, []);
    const editComponent = cfg => {
        return (
            <Select
                value={cfg.initValue ?? cfg.data[cfg.key]}
                filterSort={sortLabel()}
                // allowClear
                bordered={false}
                // suffixIcon={<EditOutlined style={{pointerEvents: "none", color: colorPrimary}} />}
                style={{width: "100%"}}
                onDropdownVisibleChange={open => {
                    if (open) {
                        // setDataOptions([]);
                        // if (typeof cfg.optionData === "function") {
                        //     cfg.optionData().then(rs => {
                        //         setDataOptions(rs);
                        //     });
                        // } else {
                        //     setDataOptions(
                        //         convertToArray(cfg.optionData).map(i => {
                        //             if (typeof i === "string") {
                        //                 return {label: i, value: i};
                        //             }
                        //             return i;
                        //         })
                        //     );
                        // }
                    }
                }}
                onChange={v => {
                    if (cfg.change) {
                        cfg.change(cfg.data, v).then();
                    } else if (cfg.getEditXML) {
                        apiEditRpc({
                            ne_id: selectNe,
                            params: cfg.getEditXML(v),
                            msg: false,
                            success: () => {
                                setTimeout(() => {
                                    if (cfg.reload) {
                                        reloadData();
                                    } else {
                                        message.success(labelList.update_success).then();
                                    }
                                }, 2000);
                            },
                            fail: () => {
                                message.error(labelList.update_fail).then();
                                setLoading(false);
                            }
                        }).then();
                    } else {
                        // console.log("no request xml");
                    }
                }}
                // options={dataOptions}
            />
        );
    };

    const reloadData = () => {
        loadData(false).then();
    };

    const getEditRander = (state, getXml, legitimate, legitimateMsg, maxLength = 15, reload = false) => {
        try {
            return (
                <Paragraph
                    style={{margin: 0, fontSize: 12, display: "flex", justifyContent: "space-between"}}
                    editable={{
                        maxLength,
                        icon: <Icon component={editTableIcon} />,
                        onChange: newVal => {
                            if (newVal === state) {
                                return;
                            }
                            if (legitimate && !legitimate(newVal)) {
                                message.error(legitimateMsg).then();
                                return;
                            }
                            apiEditRpc({
                                ne_id: selectNe,
                                params: getXml(newVal),
                                msg: false,
                                success: () => {
                                    setTimeout(() => {
                                        if (reload) {
                                            reloadData();
                                        } else {
                                            message.success(labelList.update_success).then();
                                        }
                                    }, 2000);
                                },
                                fail: () => {
                                    message.error(labelList.update_fail).then();
                                    setLoading(false);
                                }
                            }).then();
                        },
                        triggerType: ["icon", "text"]
                    }}
                >
                    {state ?? ""}
                </Paragraph>
            );
        } catch (e) {
            return state;
        }
    };

    const getAsciiHexForSplitTTI = (value, newValue, index) => {
        if (index === "sapi") {
            return `00${plainTextConvertToAsciiHex(newValue ?? "")}`.padEnd(32, "0") + value.substring(32);
        }
        if (index === "dapi") {
            return (
                value.substring(0, 32).padEnd(32, "0") +
                `00${plainTextConvertToAsciiHex(newValue ?? "")}`.padEnd(32, "0") +
                value.substring(64)
            );
        }
        if (index === "oper") {
            return `${value.substring(0, 64).padEnd(64, "0")}${plainTextConvertToAsciiHex(newValue ?? "")}`;
        }
    };

    const switchComponent = (key, value, rowData, disabled, getEditXML) => {
        if (value === NULL_VALUE) {
            return NULL_VALUE;
        }
        return (
            <Switch
                key={key}
                disabled={disabled ?? false}
                // defaultChecked={value === "true"}
                checked={value === "true"}
                loading={switchLoading[key]}
                onChange={newVal => {
                    if (newVal === value) {
                        return;
                    }
                    setSwitchLoading({...switchLoading, [key]: true});
                    apiEditRpc({
                        ne_id: selectNe,
                        params: getEditXML(newVal),
                        success: () => {
                            setTimeout(() => {
                                reloadData();
                                setSwitchLoading({...switchLoading, [key]: false});
                            }, 1000);
                        }
                    }).then();
                }}
            />
        );
    };

    const tableConfig = {
        raman: [
            {
                dataIndex: "name",
                width: 120,
                fixed: "left"
            },
            {
                dataIndex: "ramanChannelSignalPower",
                title: "optical-power",
                unit: "dBm",
                render: value => (value / 10).toFixed(2)
            },
            {
                dataIndex: "ramanChannelConfigGain",
                title: "config-gain",
                unit: "dB",
                render: value => (value / 10).toFixed(1)
            },
            {
                dataIndex: "ramanChannelOutputGain",
                title: "actual-gain",
                unit: "dB",
                render: value => (value / 10).toFixed(1)
            },
            {
                dataIndex: "ramanChannelPumpCtrl",
                title: "pump-state",
                render: state => (
                    <Tag className={state === "0" ? styles.container_tag_green : styles.container_tag_red}>
                        {state === "0" ? "ON" : "OFF"}
                    </Tag>
                )
            },
            {
                dataIndex: labelList.operation,
                sorter: false,
                fixed: "right",
                render: (state, rowData) => {
                    return (
                        <DebounceButton
                            type="link"
                            disabled={readyOnlyRight.disabled}
                            style={{color: readyOnlyRight.disabled ? "" : colorPrimary}}
                            onClick={async () => {
                                let hasChanged = false;
                                const rs = await NEGet({
                                    ne_id: selectNeRef.current,
                                    parameter: {ramanChannelEntry: {}, commonDeviceInfoEntry: {}}
                                });
                                if (
                                    !rs.ramanChannelEntry ||
                                    !rs.commonDeviceInfoEntry ||
                                    !rs.ramanChannelEntry.find(i => i.instance === rowData.instance)
                                ) {
                                    message.error("Data does not exist");
                                    return;
                                }
                                const channel = rs.ramanChannelEntry.find(i => i.instance === rowData.instance);
                                const device = rs.commonDeviceInfoEntry[0];
                                const initData = {
                                    "raman-gain": {value: (channel.ramanChannelConfigGain / 10).toFixed(1)},
                                    "alarm-status": {
                                        value: device.commonDeviceAlarmDetectionControl === "2" ? "ON" : "OFF"
                                    },
                                    "pump-status": {value: channel.ramanChannelPumpCtrl === "0" ? "ON" : "OFF"}
                                };
                                openDBModalCreate({
                                    type: "raman:info",
                                    initData,
                                    submit: async (values, defaultValue, diffValue, cancel, fail) => {
                                        const new_raman_gain = values["raman-gain"].trim();
                                        const channelUpdate = {};
                                        if (new_raman_gain !== initData["raman-gain"].value) {
                                            if (
                                                /\./.test(new_raman_gain) &&
                                                new_raman_gain?.split(".")[1]?.length > 1
                                            ) {
                                                message.error(labelList.one_decimal_warning);
                                                fail?.(false);
                                                return;
                                            }
                                            if (
                                                new_raman_gain < channel.ramanChannelConfigGainMin / 10 ||
                                                new_raman_gain > channel.ramanChannelConfigGainMax / 10
                                            ) {
                                                message.error(
                                                    labelList.edit_range_warning.format(
                                                        (channel.ramanChannelConfigGainMin / 10).toFixed(1),
                                                        (channel.ramanChannelConfigGainMax / 10).toFixed(1)
                                                    )
                                                );
                                                fail?.(false);
                                                return;
                                            }
                                            channelUpdate.ramanChannelConfigGain = new_raman_gain * 10;
                                        }
                                        if (values["pump-status"] !== initData["pump-status"].value) {
                                            channelUpdate.ramanChannelPumpCtrl = values["pump-status"] === "ON" ? 0 : 1;
                                        }
                                        if (Object.keys(channelUpdate).length > 0) {
                                            const saveRs = await NESet({
                                                ne_id: selectNeRef.current,
                                                msg: false,
                                                parameter: {
                                                    ramanChannelEntry: {
                                                        instance: rowData.instance,
                                                        ...channelUpdate
                                                    }
                                                }
                                            });
                                            if (saveRs.message === "fail") {
                                                message.error(gLabelList.save_failed);
                                                fail?.(false);
                                                return;
                                            }
                                            hasChanged = true;
                                        }
                                        if (values["alarm-status"] !== initData["alarm-status"].value) {
                                            const saveRs = await NESet({
                                                ne_id: selectNeRef.current,
                                                msg: false,
                                                parameter: {
                                                    commonDeviceInfoEntry: {
                                                        instance: rowData.instance,
                                                        commonDeviceAlarmDetectionControl:
                                                            values["alarm-status"] === "ON" ? 2 : 1
                                                    }
                                                }
                                            });
                                            if (saveRs.message === "fail") {
                                                message.error(gLabelList.save_failed);
                                                fail?.(false);
                                                return;
                                            }
                                            hasChanged = true;
                                        }
                                        if (hasChanged) {
                                            message.success(gLabelList.save_success);
                                            setTimeout(() => {
                                                getServiceDataForNe2().then();
                                            }, 1500);
                                        }
                                        cancel(true);
                                    }
                                });
                            }}
                        >
                            Modify
                        </DebounceButton>
                    );
                }
            }
        ],
        edfa: [
            {
                dataIndex: "name",
                width: 120,
                fixed: "left"
            },
            {
                dataIndex: "input-power",
                title: "input-optical-power",
                unit: "dBm"
            },
            {
                dataIndex: "output-power",
                title: "output-optical-power",
                unit: "dBm"
            },
            {
                dataIndex: "actual-voa-attenuation",
                title: "voa-attenuation-value",
                unit: "dB"
            },
            {
                dataIndex: "target-voa-attenuation",
                title: "voa-attenuation-expected",
                unit: "dB"
            },
            {
                dataIndex: "actual-gain",
                title: "EDFA-gain-value",
                unit: "dB"
            },
            {
                dataIndex: "target-gain",
                title: "expected-EDFA-gain",
                unit: "dB"
            },
            {
                dataIndex: "target-gain-tilt",
                title: "gain-slope",
                unit: "dB/40nm"
            },
            {
                dataIndex: "enabled",
                title: "laser-switch",
                render: (state, rowData) => {
                    if (readyOnlyRight.disabled || !state) {
                        return state;
                    }
                    return switchComponent(rowData.name, state, rowData, rowData.enabled === NULL_VALUE, newVal => {
                        return {
                            "optical-amplifier": {
                                amplifiers: {
                                    amplifier: {
                                        name: rowData.name,
                                        config: {
                                            enabled: newVal
                                        }
                                    }
                                }
                            }
                        };
                    });
                }
            },
            {
                dataIndex: labelList.operation,
                sorter: false,
                fixed: "right",
                render: (state, rowData) => {
                    return (
                        <DebounceButton
                            type="link"
                            disabled={readyOnlyRight.disabled}
                            style={{color: readyOnlyRight.disabled ? "" : colorPrimary}}
                            onClick={() => {
                                openEditComponent(
                                    "amplifier",
                                    rowData.name,
                                    "5",
                                    selectNe,
                                    null,
                                    () => {
                                        setTimeout(() => {
                                            reloadData();
                                        }, 1500);
                                    },
                                    readyOnlyRight.disabled,
                                    null,
                                    {
                                        amplifier: [
                                            {dataIndex: "target-gain"},
                                            {dataIndex: "target-gain-tilt"},
                                            {dataIndex: "amp-mode"},
                                            {dataIndex: "target-voa-attenuation"},
                                            {dataIndex: "apr-enable"}
                                        ]
                                    }
                                );
                            }}
                        >
                            Modify
                        </DebounceButton>
                    );
                }
            }
        ],
        osc: [
            {
                dataIndex: "name",
                width: "calc(25%)"
            },
            {
                dataIndex: "used-service-port-type-preconf",
                title: "speed",
                width: "calc(25%)",
                render: state => {
                    if (!state) {
                        return state;
                    }
                    return state.substring(state.indexOf("_") > -1 ? state.indexOf("_") + 1 : 0);
                }
            },
            {
                dataIndex: "enabled",
                title: "laser-enable",
                width: "calc(25%)",
                render: (state, rowData) => {
                    if (readyOnlyRight.disabled || !state) {
                        return state;
                    }
                    return switchComponent(rowData.name, state, rowData, rowData.enabled === NULL_VALUE, newVal => {
                        return {
                            components: {
                                component: {
                                    name: rowData.name,
                                    transceiver: {
                                        config: {
                                            enabled: newVal
                                        }
                                    }
                                }
                            }
                        };
                    });
                }
            },
            {
                dataIndex: labelList.operation,
                sorter: false,
                fixed: "right",
                render: (state, rowData) => {
                    return (
                        <DebounceButton
                            type="link"
                            disabled={readyOnlyRight.disabled}
                            style={{color: readyOnlyRight.disabled ? "" : colorPrimary}}
                            onClick={() => {
                                openEditComponent(
                                    "component",
                                    rowData.name,
                                    "5",
                                    selectNe,
                                    null,
                                    () => {
                                        setTimeout(() => {
                                            reloadData();
                                        }, 1500);
                                    },
                                    readyOnlyRight.disabled,
                                    null,
                                    {
                                        transceiver: [
                                            {dataIndex: "used-service-port-type-preconf", title: "speed"},
                                            {dataIndex: "enabled", title: "laser-enable"}
                                        ]
                                    }
                                );
                            }}
                        >
                            Modify
                        </DebounceButton>
                    );
                }
            }
        ],
        tff: [
            {
                dataIndex: "name",
                width: 120
            },
            {
                dataIndex: "target-voa-attenuation",
                unit: "dB"
            },
            {
                dataIndex: "channel-interval",
                unit: "Hz",
                render: state => {
                    if (!state) {
                        return state;
                    }
                    return state.split("_").pop();
                }
            },
            {
                dataIndex: "actual-voa-attenuation",
                unit: "dB"
            },
            {
                dataIndex: labelList.operation,
                sorter: false,
                fixed: "right",
                width: 150,
                render: (state, rowData) => {
                    return (
                        <DebounceButton
                            type="link"
                            disabled={readyOnlyRight.disabled}
                            style={{color: readyOnlyRight.disabled ? "" : colorPrimary}}
                            onClick={() => {
                                const columnsCfg = [
                                    {
                                        dataIndex: "target-voa-attenuation",
                                        unit: "dB",
                                        inputType: "number",
                                        step: 0.01,
                                        required: true
                                    }
                                ];
                                openCustomEditForm({
                                    title: "Modify",
                                    columnNum: 1,
                                    columns: columnsCfg.map(i => [i]),
                                    getData: async () => {
                                        return rowData;
                                    },
                                    saveFun: async (diffValue, value) => {
                                        return await apiEditRpc({
                                            ne_id: selectNe,
                                            msg: true,
                                            params: {
                                                components: {
                                                    component: {
                                                        name: rowData.name,
                                                        tff: {
                                                            config: {
                                                                "target-voa-attenuation":
                                                                    value["target-voa-attenuation"]
                                                            }
                                                        }
                                                    }
                                                }
                                            },
                                            success: () => {
                                                setTimeout(() => {
                                                    reloadData();
                                                }, 1500);
                                            }
                                        });
                                    }
                                });
                            }}
                        >
                            Modify
                        </DebounceButton>
                    );
                }
            }
        ],
        aps: [
            {
                dataIndex: "name",
                width: 120
            },
            {dataIndex: "active-path"},
            {dataIndex: "aps-status"},
            {
                dataIndex: "revertive",
                render: (state, data) => {
                    if (readyOnlyRight.disabled || !state) {
                        return state;
                    }
                    return editComponent({
                        key: "revertive",
                        data,
                        optionData: ["true", "false"],
                        reload: true,
                        getEditXML: newVal => {
                            return {
                                aps: {
                                    "aps-modules": {
                                        "aps-module": {
                                            name: data.name,
                                            config: {
                                                revertive: newVal
                                            }
                                        }
                                    }
                                }
                            };
                        }
                    });
                }
            },
            {
                dataIndex: "wait-to-restore-time",
                unit: "ms",
                render: (state, data) => {
                    if (readyOnlyRight.disabled || !state) {
                        return state;
                    }
                    return getEditRander(
                        state,
                        newVal => {
                            return {
                                aps: {
                                    "aps-modules": {
                                        "aps-module": {
                                            name: data.name,
                                            config: {
                                                "wait-to-restore-time": newVal
                                            }
                                        }
                                    }
                                }
                            };
                        },
                        null,
                        null,
                        null,
                        true
                    );
                }
            },
            {
                dataIndex: "hold-off-time",
                unit: "ms",
                render: (state, data) => {
                    if (readyOnlyRight.disabled || !state) {
                        return state;
                    }
                    return getEditRander(
                        state,
                        newVal => {
                            return {
                                aps: {
                                    "aps-modules": {
                                        "aps-module": {
                                            name: data.name,
                                            config: {
                                                "hold-off-time": newVal
                                            }
                                        }
                                    }
                                }
                            };
                        },
                        null,
                        null,
                        null,
                        true
                    );
                }
            },
            {
                dataIndex: "primary-switch-threshold",
                unit: "dBm",
                render: (state, data) => {
                    if (readyOnlyRight.disabled || !state) {
                        return state;
                    }
                    return getEditRander(
                        state,
                        newVal => {
                            return {
                                aps: {
                                    "aps-modules": {
                                        "aps-module": {
                                            name: data.name,
                                            config: {
                                                "primary-switch-threshold": newVal
                                            }
                                        }
                                    }
                                }
                            };
                        },
                        null,
                        null,
                        null,
                        true
                    );
                }
            },
            {
                dataIndex: "primary-switch-hysteresis",
                unit: "dB",
                render: (state, data) => {
                    if (readyOnlyRight.disabled || !state) {
                        return state;
                    }
                    return getEditRander(
                        state,
                        newVal => {
                            return {
                                aps: {
                                    "aps-modules": {
                                        "aps-module": {
                                            name: data.name,
                                            config: {
                                                "primary-switch-hysteresis": newVal
                                            }
                                        }
                                    }
                                }
                            };
                        },
                        null,
                        null,
                        null,
                        true
                    );
                }
            },
            {
                dataIndex: "secondary-switch-threshold",
                unit: "dBm",
                render: (state, data) => {
                    if (readyOnlyRight.disabled || !state) {
                        return state;
                    }
                    return getEditRander(
                        state,
                        newVal => {
                            return {
                                aps: {
                                    "aps-modules": {
                                        "aps-module": {
                                            name: data.name,
                                            config: {
                                                "secondary-switch-threshold": newVal
                                            }
                                        }
                                    }
                                }
                            };
                        },
                        null,
                        null,
                        null,
                        true
                    );
                }
            },
            {
                dataIndex: "relative-switch-threshold",
                unit: "dB",
                render: (state, data) => {
                    if (readyOnlyRight.disabled || !state) {
                        return state;
                    }
                    return getEditRander(
                        state,
                        newVal => {
                            return {
                                aps: {
                                    "aps-modules": {
                                        "aps-module": {
                                            name: data.name,
                                            config: {
                                                "relative-switch-threshold": newVal
                                            }
                                        }
                                    }
                                }
                            };
                        },
                        null,
                        null,
                        null,
                        true
                    );
                }
            },
            {
                dataIndex: "relative-switch-threshold-offset",
                unit: "dB",
                render: (state, data) => {
                    if (readyOnlyRight.disabled || !state) {
                        return state;
                    }
                    return getEditRander(
                        state,
                        newVal => {
                            return {
                                aps: {
                                    "aps-modules": {
                                        "aps-module": {
                                            name: data.name,
                                            config: {
                                                "relative-switch-threshold-offset": newVal
                                            }
                                        }
                                    }
                                }
                            };
                        },
                        null,
                        null,
                        null,
                        true
                    );
                }
            },
            {
                dataIndex: "force-to-port",
                render: (state, data) => {
                    if (readyOnlyRight.disabled || !state) {
                        return state;
                    }
                    return editComponent({
                        key: "force-to-port",
                        data,
                        optionData: ["NONE", "PRIMARY", "SECONDARY"],
                        reload: true,
                        getEditXML: newVal => {
                            return {
                                aps: {
                                    "aps-modules": {
                                        "aps-module": {
                                            name: data.name,
                                            config: {
                                                "force-to-port": newVal
                                            }
                                        }
                                    }
                                }
                            };
                        }
                    });
                }
            },
            {
                dataIndex: labelList.operation,
                width: 70,
                sorter: false,
                fixed: "right",
                render: (_, r) => (
                    <DebounceButton
                        containerType="a"
                        onClick={() => {
                            openModalRpc(
                                selectNe,
                                "switch-olp",
                                "5",
                                `Switch OLP (${r.name})`,
                                {name: r.name},
                                ["name"],
                                async () => {
                                    reloadData();
                                },
                                null,
                                null,
                                readyOnlyRight.disabled
                            );
                        }}
                    >
                        {labelList.switch_connection}
                    </DebounceButton>
                )
            }
        ],
        ocm: [
            {
                dataIndex: "name",
                width: 120
            },
            {
                dataIndex: "active-local-port",
                render: (state, data) => {
                    if (!state) {
                        return state;
                    }
                    return editComponent({
                        key: "active-local-port",
                        data,
                        initValue: data["active-local-port"],
                        reload: true,
                        optionData: async () => {
                            // const rs = await LeafRefs["channel-monitors"]["channel-monitor"].config[
                            //     "active-local-port"
                            // ].getList({keys: [data.name]});
                            // return rs.map(i => ({label: i, value: i}));
                        },
                        getEditXML: newVal => {
                            return {
                                "channel-monitors": {
                                    "channel-monitor": {
                                        name: data.name,
                                        config: {
                                            "active-local-port": newVal
                                        }
                                    }
                                }
                            };
                        }
                    });
                }
            },
            {
                dataIndex: "channel-interval",
                render: (state, data) => {
                    if (!state) {
                        return state;
                    }
                    return editComponent({
                        key: "channel-interval",
                        data,
                        optionData: ["CHANNEL_50G", "CHANNEL_75G", "CHANNEL_100G"],
                        reload: true,
                        getEditXML: newVal => {
                            return {
                                "channel-monitors": {
                                    "channel-monitor": {
                                        name: data.name,
                                        config: {
                                            "channel-interval": newVal
                                        }
                                    }
                                }
                            };
                        }
                    });
                }
            },
            {
                dataIndex: labelList.operation,
                width: 120,
                sorter: false,
                fixed: "right",
                render: (_, r) => (
                    <DebounceButton
                        containerType="a"
                        onClick={() => {
                            openCustomTable(
                                "ocm-channel",
                                [r.name],
                                "5",
                                r.name,
                                {
                                    initColumns: [
                                        {dataIndex: "channel"},
                                        {dataIndex: "lower-frequency", unit: "MHz"},
                                        {dataIndex: "upper-frequency", unit: "MHz"},
                                        {dataIndex: "power", unit: "dBm"}
                                    ],
                                    formatData: data => {
                                        data.reverse();
                                        return data.map((i, index) => ({...i, channel: index + 1}));
                                    }
                                },
                                selectNe
                            );
                        }}
                    >
                        {labelList.test_result}
                    </DebounceButton>
                )
            }
        ],
        wss: [
            {
                dataIndex: "index",
                fixed: "left"
            },
            {
                dataIndex: "ad-port"
            },
            {
                dataIndex: "line-port"
            },
            {
                dataIndex: "target-add-voa-attenuation"
            },
            {
                dataIndex: "target-drop-voa-attenuation"
            },
            {
                dataIndex: "min-edge-freq",
                unit: "THz",
                render: state => {
                    if (!state) {
                        return state;
                    }
                    return (state / 1000000).toFixed(2);
                }
            },
            {
                dataIndex: "max-edge-freq",
                unit: "THz",
                render: state => {
                    if (!state) {
                        return state;
                    }
                    return (state / 1000000).toFixed(2);
                }
            },
            {
                dataIndex: "actual-add-voa-attenuation"
            },
            {
                dataIndex: "actual-drop-voa-attenuation"
            },
            {
                dataIndex: labelList.operation,
                sorter: false,
                fixed: "right",
                render: (_, record) => {
                    return (
                        <DebounceButton
                            type="link"
                            disabled={readyOnlyRight.disabled}
                            style={{color: readyOnlyRight.disabled ? "" : colorPrimary}}
                            onClick={() => {
                                const onOk = async () => {
                                    await netconfByXML({
                                        ne_id: selectNe,
                                        action: "delete",
                                        msg: true,
                                        xml: {
                                            components: {
                                                $: {
                                                    xmlns: "http://openconfig.net/yang/platform"
                                                },
                                                component: {
                                                    name: selectCardRef.current[tabType],
                                                    wss: {
                                                        config: {
                                                            "frequency-channel": {
                                                                $: {
                                                                    xmlns: "http://openconfig.net/yang/platform/wss",
                                                                    "nc:operation": "delete"
                                                                },
                                                                index: record.index
                                                            }
                                                        }
                                                    }
                                                }
                                            }
                                        },
                                        success: () => {
                                            setTimeout(() => {
                                                reloadData();
                                            }, 2000);
                                        }
                                    });
                                };
                                const modal = smallModal({
                                    content: gLabelList.delete_confirm_msg,
                                    // eslint-disable-next-line no-unused-vars
                                    onOk: _ => {
                                        onOk().then();
                                        modal.destroy();
                                    }
                                });
                            }}
                        >
                            Delete
                        </DebounceButton>
                    );
                }
            }
        ],
        port: [
            {
                dataIndex: "port",
                fixed: "left",
                width: 120
            },
            {
                dataIndex: "signal-type",
                title: "service-type",
                width: 150,
                render: state => {
                    if (state) {
                        return state.substring(state.indexOf("_") + 1);
                    }
                    return state;
                }
            },
            {
                dataIndex: "mapping-path",
                width: 270,
                render: (state, rowData) => {
                    if (rowData["signal-type"] === "NA" || rowData?.port?.indexOf?.("-C") < 0) {
                        return NULL_VALUE;
                    }
                    return PORT_MAPPING_MAP[rowData["actual-vendor-type"]]?.[rowData["signal-type"]] ?? NULL_VALUE;
                }
            },
            {
                dataIndex: "modulation",
                width: 220,
                render: state => {
                    return OPERATIONAL_MODE_MAP[state] ?? NULL_VALUE;
                }
            },
            {
                dataIndex: "module-type",
                width: 150
            },
            {
                dataIndex: "wavelength",
                width: 200
            },
            {
                dataIndex: "target-output-power",
                width: 180
            },
            {
                dataIndex: "fec",
                width: 120,
                render: state => {
                    return state || NULL_VALUE;
                }
            },
            {
                dataIndex: "pre-fec",
                width: 120
            },
            {
                dataIndex: "post-fec",
                width: 120
            },
            {
                dataIndex: "loopback",
                width: 120
            },
            {
                dataIndex: "als",
                width: 150,
                render: state => {
                    return state || NULL_VALUE;
                }
            },
            {
                dataIndex: "port-used",
                width: 120
            },
            {
                dataIndex: "laser-enable",
                width: 150,
                render: (value, rowData) =>
                    switchComponent(
                        rowData.port,
                        value,
                        rowData,
                        readyOnlyRight.disabled || rowData["laser-enable"] === NULL_VALUE,
                        newVal => {
                            return {
                                components: {
                                    component: {
                                        name: rowData.port.replace("PORT", "TRANSCEIVER"),
                                        transceiver: {
                                            config: {
                                                enabled: newVal
                                            }
                                        }
                                    }
                                }
                            };
                        }
                    )
            },
            {
                dataIndex: labelList.operation,
                width: 120,
                fixed: "right",
                sorter: false,
                render: (_, originValue) => {
                    return (
                        <DebounceButton
                            type="link"
                            disabled={readyOnlyRight.disabled}
                            style={{color: readyOnlyRight.disabled ? "" : colorPrimary}}
                            onClick={() => {
                                const columnsCfg = [
                                    {
                                        dataIndex: "signal-type",
                                        title: "service-type",
                                        inputType: "select",
                                        data: async () => {
                                            return await getSignalType(selectNe, originValue.port);
                                        }
                                    },
                                    {
                                        dataIndex: "modulation",
                                        inputType: "select",
                                        // data: {options: OPERATIONAL_MODE_MAP}
                                        data: async () => {
                                            const _options = [];
                                            const supportType = OCH_MODE_SUPPORT_MAP[originValue["actual-vendor-type"]];
                                            Object.entries(OPERATIONAL_MODE_MAP).map(([k, v]) => {
                                                if (!supportType || supportType.includes(parseInt(k))) {
                                                    _options.push({
                                                        label: v,
                                                        value: k
                                                    });
                                                }
                                            });
                                            return _options;
                                        }
                                    },
                                    {
                                        dataIndex: "wavelength",
                                        inputType: "cascader",
                                        showCheckedStrategy: "SHOW_CHILD",
                                        data: async () => {
                                            return frequencyOptions;
                                        }
                                    },
                                    {
                                        dataIndex: "target-output-power",
                                        inputType: "number",
                                        step: 0.01
                                    },
                                    {
                                        dataIndex: "fec",
                                        title: "fec-type",
                                        inputType: "select",
                                        data: {options: ["ENABLED", "DISABLED"]}
                                    },
                                    {
                                        dataIndex: "loopback",
                                        inputType: "select",
                                        data: {options: ["NONE", "FACILITY", "TERMINAL"]}
                                    },
                                    {
                                        dataIndex: "als",
                                        inputType: "select",
                                        data: {options: ["ENABLED", "DISABLED"]}
                                        // data: async () => {
                                        //     return originValue.interface_type === "ethernet"
                                        //         ? [
                                        //               {label: "DISABLED", value: "LASER_SHUTDOWN"},
                                        //               {label: "ENABLED", value: "ETHERNET"}
                                        //           ]
                                        //         : ["ENABLED", "DISABLED"];
                                        // }
                                    },
                                    {
                                        dataIndex: "laser-enable",
                                        inputType: "select",
                                        data: {options: ["true", "false"]}
                                    }
                                ];
                                openCustomEditForm({
                                    title: "Modify",
                                    columnNum: 1,
                                    columns: columnsCfg
                                        .filter(
                                            i =>
                                                (i.dataIndex === "signal-type" &&
                                                    originValue.port.indexOf("C") > -1 &&
                                                    originValue["port-used"] === "False" &&
                                                    (!portData.find(i => i["signal-type"] === "PROT_FC32G") ||
                                                        originValue.port.endsWith("C1"))) ||
                                                (i.dataIndex === "wavelength" &&
                                                    originValue["port-used"] === "False" &&
                                                    originValue[i.dataIndex] &&
                                                    originValue[i.dataIndex] !== NULL_VALUE) ||
                                                (i.dataIndex !== "signal-type" &&
                                                    i.dataIndex !== "wavelength" &&
                                                    originValue[i.dataIndex] &&
                                                    originValue[i.dataIndex] !== NULL_VALUE)
                                        )
                                        .map(i => [i]),
                                    getData: async () => {
                                        const newValue = {
                                            ...originValue,
                                            "signal-type": originValue["signal-type"].replace("PROT_", "")
                                        };

                                        if (originValue.modulation) {
                                            newValue.modulation = OPERATIONAL_MODE_MAP[originValue.modulation];
                                        }
                                        return newValue;
                                    },
                                    saveFun: async diffValue => {
                                        let rs;
                                        if (diffValue["signal-type"]) {
                                            setUpdateSignalType(true);
                                            rs = await updatePortSignalType({
                                                selectNe,
                                                data: originValue,
                                                portType: diffValue["signal-type"],
                                                portManagerList: portData,
                                                reloadData: () => {},
                                                setLoading,
                                                lineCard: selectCardRef.current.linecard,
                                                lineCardType: selectCardRef.current?.linecard_cardType?.split("-")?.[0]
                                            });
                                            setUpdateSignalType(false);
                                            if (!rs || rs.apiResult === "fail") {
                                                // message.error(gLabelList.save_failed);
                                                return rs;
                                            }
                                        }
                                        if (
                                            diffValue.modulation ||
                                            diffValue.wavelength ||
                                            diffValue["target-output-power"] !== undefined
                                        ) {
                                            const update = {};
                                            if (diffValue.modulation) {
                                                update["operational-mode"] = diffValue.modulation;
                                            }
                                            if (diffValue.wavelength) {
                                                update.frequency =
                                                    diffValue.wavelength[1].match(/(\d+(\.\d+)?)/g)[1] * 1000000;
                                            }
                                            if (diffValue["target-output-power"] !== undefined) {
                                                update["target-output-power"] = diffValue["target-output-power"];
                                            }
                                            rs = await apiEditRpc({
                                                ne_id: selectNe,
                                                msg: false,
                                                params: {
                                                    components: {
                                                        component: {
                                                            name: originValue.port.replace("PORT", "OCH"),
                                                            "optical-channel": {
                                                                config: update
                                                            }
                                                        }
                                                    }
                                                }
                                            });
                                            if (rs.apiResult === "fail") {
                                                message.error(gLabelList.save_failed);
                                                return rs;
                                            }
                                        }
                                        if (diffValue.fec || diffValue.als) {
                                            const update = {};
                                            if (diffValue.fec) {
                                                update.ethernet = {config: {"client-fec": diffValue.fec}};
                                            }
                                            if (diffValue.als) {
                                                if (originValue.interface_type === "ethernet") {
                                                    let _als;
                                                    if (diffValue.als === "ENABLED") {
                                                        _als = "LASER_SHUTDOWN";
                                                    } else if (diffValue.als === "DISABLED") {
                                                        _als = "ETHERNET";
                                                    }
                                                    if (_als) {
                                                        if (diffValue.fec) {
                                                            update.ethernet.config["client-als"] = _als;
                                                        } else {
                                                            update.ethernet = {config: {"client-als": _als}};
                                                        }
                                                    }
                                                } else {
                                                    update[originValue.interface_type] = {
                                                        config: {
                                                            "client-als": diffValue.als
                                                        }
                                                    };
                                                }
                                            }
                                            rs = await apiEditRpc({
                                                ne_id: selectNe,
                                                msg: false,
                                                params: {
                                                    interfaces: {
                                                        interface: {
                                                            name: originValue["interface-name"],
                                                            ...update
                                                        }
                                                    }
                                                }
                                            });
                                            if (rs.apiResult === "fail") {
                                                message.error(gLabelList.save_failed);
                                                return rs;
                                            }
                                        }
                                        if (diffValue.loopback) {
                                            if (originValue.loopback !== "NONE" && diffValue.loopback !== "NONE") {
                                                rs = await apiEditRpc({
                                                    ne_id: selectNe,
                                                    msg: false,
                                                    params: {
                                                        "terminal-device": {
                                                            "logical-channels": {
                                                                channel: {
                                                                    index: originValue["channel-index"],
                                                                    config: {
                                                                        "loopback-mode": "NONE"
                                                                    }
                                                                }
                                                            }
                                                        }
                                                    }
                                                });
                                                if (rs.apiResult === "fail") {
                                                    message.error(gLabelList.save_failed);
                                                    return rs;
                                                }
                                            }
                                            rs = await apiEditRpc({
                                                ne_id: selectNe,
                                                msg: false,
                                                params: {
                                                    "terminal-device": {
                                                        "logical-channels": {
                                                            channel: {
                                                                index: originValue["channel-index"],
                                                                config: {
                                                                    "loopback-mode": diffValue.loopback
                                                                }
                                                            }
                                                        }
                                                    }
                                                }
                                            });
                                            if (rs.apiResult === "fail") {
                                                message.error(gLabelList.save_failed);
                                                return rs;
                                            }
                                        }

                                        if (diffValue["laser-enable"]) {
                                            rs = await apiEditRpc({
                                                ne_id: selectNe,
                                                msg: false,
                                                params: {
                                                    components: {
                                                        component: {
                                                            name: originValue.port.replace("PORT", "TRANSCEIVER"),
                                                            transceiver: {
                                                                config: {
                                                                    enabled: diffValue["laser-enable"]
                                                                }
                                                            }
                                                        }
                                                    }
                                                }
                                            });
                                            if (rs.apiResult === "fail") {
                                                message.error(gLabelList.save_failed);
                                                return rs;
                                            }
                                        }
                                        setTimeout(() => {
                                            reloadData();
                                        }, 3000);
                                        message.success(gLabelList.save_success);
                                        return rs;
                                    }
                                });
                            }}
                        >
                            Modify
                        </DebounceButton>
                    );
                }
            }
        ],
        "otn-tti": [
            {
                dataIndex: "port",
                width: 120,
                fixed: "left"
            },
            {
                dataIndex: "sm-tti-msg-transmit-sapi",
                width: 220,
                render: (state, data) => {
                    if (!data.otn_signal && !data["signal-type"]?.startsWith?.("OTU")) {
                        return data["sm-tti-msg-expected-sapi"];
                    }
                    const val = data["sm-tti-msg-expected"] ?? "";
                    try {
                        if (readyOnlyRight.disabled) {
                            return <HexComponent value={val.substring(0, 32)} />;
                        }
                        if (data["signal-type"].startsWith("OTU")) {
                            return getEditRander(
                                asciiHexConvertToPlainText(val.substring(0, 32)),
                                newVal => {
                                    return {
                                        "terminal-device": {
                                            "logical-channels": {
                                                channel: {
                                                    index: data["sm-channel-index"],
                                                    otn: {
                                                        config: {
                                                            "tti-msg-expected": getAsciiHexForSplitTTI(
                                                                val,
                                                                newVal,
                                                                "sapi"
                                                            )
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    };
                                },
                                null,
                                null,
                                15,
                                true
                            );
                        }
                        return <HexComponent value={val.substring(0, 32)} />;
                    } catch (e) {
                        return val;
                    }
                }
            },
            {
                dataIndex: "sm-tti-msg-transmit-dapi",
                width: 220,
                render: (state, data) => {
                    if (!data.otn_signal && !data["signal-type"]?.startsWith?.("OTU")) {
                        return data["sm-tti-msg-transmit-dapi"];
                    }
                    const val = data["sm-tti-msg-transmit"] ?? "";
                    try {
                        if (readyOnlyRight.disabled) {
                            return <HexComponent value={val.substring(32, 64)} />;
                        }
                        if (data["signal-type"].startsWith("OTU")) {
                            return getEditRander(
                                asciiHexConvertToPlainText(val.substring(32, 64)),
                                newVal => {
                                    return {
                                        "terminal-device": {
                                            "logical-channels": {
                                                channel: {
                                                    index: data["sm-channel-index"],
                                                    otn: {
                                                        config: {
                                                            "tti-msg-transmit": getAsciiHexForSplitTTI(
                                                                val,
                                                                newVal,
                                                                "dapi"
                                                            )
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    };
                                },
                                null,
                                null,
                                15,
                                true
                            );
                        }
                        return <HexComponent value={val.substring(32, 64)} />;
                    } catch (e) {
                        return val;
                    }
                }
            },
            {
                dataIndex: "sm-tti-msg-transmit-oper",
                width: 220,
                render: (state, data) => {
                    if (!data.otn_signal && !data["signal-type"]?.startsWith?.("OTU")) {
                        return data["sm-tti-msg-transmit-oper"];
                    }
                    const val = data["sm-tti-msg-transmit"] ?? "";
                    try {
                        if (readyOnlyRight.disabled) {
                            return <HexComponent value={val.substring(64)} />;
                        }
                        if (data["signal-type"].startsWith("OTU")) {
                            return getEditRander(
                                asciiHexConvertToPlainText(val.substring(64)),
                                newVal => {
                                    return {
                                        "terminal-device": {
                                            "logical-channels": {
                                                channel: {
                                                    index: data["sm-channel-index"],
                                                    otn: {
                                                        config: {
                                                            "tti-msg-transmit": getAsciiHexForSplitTTI(
                                                                val,
                                                                newVal,
                                                                "oper"
                                                            )
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    };
                                },
                                null,
                                null,
                                30,
                                true
                            );
                        }
                        return <HexComponent value={val.substring(64)} />;
                    } catch (e) {
                        return val;
                    }
                }
            },
            {
                dataIndex: "sm-tti-msg-expected-sapi",
                width: 220,
                render: (state, data) => {
                    if (!data.otn_signal && !data["signal-type"]?.startsWith?.("OTU")) {
                        return data["sm-tti-msg-expected-sapi"];
                    }
                    const val = data["sm-tti-msg-expected"] ?? "";
                    try {
                        if (readyOnlyRight.disabled) {
                            return <HexComponent value={val.substring(0, 32)} />;
                        }
                        if (data["signal-type"].startsWith("OTU")) {
                            return getEditRander(
                                asciiHexConvertToPlainText(val.substring(0, 32)),
                                newVal => {
                                    return {
                                        "terminal-device": {
                                            "logical-channels": {
                                                channel: {
                                                    index: data["sm-channel-index"],
                                                    otn: {
                                                        config: {
                                                            "tti-msg-expected": getAsciiHexForSplitTTI(
                                                                val,
                                                                newVal,
                                                                "sapi"
                                                            )
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    };
                                },
                                null,
                                null,
                                15,
                                true
                            );
                        }
                        return <HexComponent value={val.substring(0, 32)} />;
                    } catch (e) {
                        return val;
                    }
                }
            },
            {
                dataIndex: "sm-tti-msg-expected-dapi",
                width: 220,
                render: (state, data) => {
                    if (!data.otn_signal && !data["signal-type"]?.startsWith?.("OTU")) {
                        return data["sm-tti-msg-expected-dapi"];
                    }
                    const val = data["sm-tti-msg-expected"] ?? "";
                    try {
                        if (readyOnlyRight.disabled) {
                            return <HexComponent value={val.substring(32, 64)} />;
                        }
                        if (data["signal-type"].startsWith("OTU")) {
                            return getEditRander(
                                asciiHexConvertToPlainText(val.substring(32, 64)),
                                newVal => {
                                    return {
                                        "terminal-device": {
                                            "logical-channels": {
                                                channel: {
                                                    index: data["sm-channel-index"],
                                                    otn: {
                                                        config: {
                                                            "tti-msg-expected": getAsciiHexForSplitTTI(
                                                                val,
                                                                newVal,
                                                                "dapi"
                                                            )
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    };
                                },
                                null,
                                null,
                                15,
                                true
                            );
                        }
                        return <HexComponent value={val.substring(32, 64)} />;
                    } catch (e) {
                        return val;
                    }
                }
            },
            {
                dataIndex: "sm-tti-msg-expected-oper",
                width: 220,
                render: (state, data) => {
                    if (!data.otn_signal && !data["signal-type"]?.startsWith?.("OTU")) {
                        return data["sm-tti-msg-expected-oper"];
                    }
                    const val = data["sm-tti-msg-expected"] ?? "";
                    try {
                        if (readyOnlyRight.disabled) {
                            return <HexComponent value={val.substring(64)} />;
                        }
                        if (data["signal-type"].startsWith("OTU")) {
                            return getEditRander(
                                asciiHexConvertToPlainText(val.substring(64)),
                                newVal => {
                                    return {
                                        "terminal-device": {
                                            "logical-channels": {
                                                channel: {
                                                    index: data["sm-channel-index"],
                                                    otn: {
                                                        config: {
                                                            "tti-msg-expected": getAsciiHexForSplitTTI(
                                                                val,
                                                                newVal,
                                                                "oper"
                                                            )
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    };
                                },
                                null,
                                null,
                                30,
                                true
                            );
                        }
                        return <HexComponent value={val.substring(64)} />;
                    } catch (e) {
                        return val;
                    }
                }
            },
            {
                dataIndex: "sm-tti-msg-recv-sapi",
                width: 220,
                render: (state, data) => {
                    if (!data.otn_signal) {
                        return data["sm-tti-msg-recv-sapi"];
                    }
                    const val = data["sm-tti-msg-recv"];
                    if (val) {
                        return <HexComponent value={val.substring(0, 32)} />;
                    }
                    return state;
                }
            },
            {
                dataIndex: "sm-tti-msg-recv-dapi",
                title: "SM DAPI Receive",
                width: 180,
                render: (state, data) => {
                    if (!data.otn_signal) {
                        return data["sm-tti-msg-recv-dapi"];
                    }
                    const val = data["sm-tti-msg-recv"];
                    if (val) {
                        return <HexComponent value={val.substring(32, 64)} />;
                    }
                    return state;
                }
            },
            {
                dataIndex: "sm-tti-msg-recv-oper",
                width: 180,
                render: (state, data) => {
                    if (!data.otn_signal) {
                        return data["sm-tti-msg-recv-oper"];
                    }
                    const val = data["sm-tti-msg-recv"];
                    if (val) {
                        return <HexComponent value={val.substring(64)} />;
                    }
                    return state;
                }
            },
            {
                dataIndex: "pm-tti-msg-transmit-sapi",
                title: "PM SAPI Transmit",
                width: 180,
                render: (state, data) => {
                    const val = data["pm-tti-msg-transmit"];
                    if (!val) {
                        return state;
                    }
                    try {
                        if (
                            readyOnlyRight.disabled ||
                            (data["signal-type"].startsWith("OTU") && !data["signal-type"].startsWith("OTUC"))
                        ) {
                            return <HexComponent value={val.substring(0, 32)} />;
                            // return asciiHexConvertToPlainText(state);
                        }
                        return getEditRander(
                            asciiHexConvertToPlainText(val.substring(0, 32)),
                            newVal => {
                                return {
                                    "terminal-device": {
                                        "logical-channels": {
                                            channel: {
                                                index: data["pm-channel-index"],
                                                otn: {
                                                    config: {
                                                        "tti-msg-transmit": getAsciiHexForSplitTTI(val, newVal, "sapi")
                                                    }
                                                }
                                            }
                                        }
                                    }
                                };
                            },
                            null,
                            null,
                            15,
                            true
                        );
                    } catch (e) {
                        return val;
                    }
                }
            },
            {
                dataIndex: "pm-tti-msg-transmit-dapi",
                width: 220,
                render: (state, data) => {
                    const val = data["pm-tti-msg-transmit"];
                    if (!val) {
                        return state;
                    }
                    try {
                        if (
                            readyOnlyRight.disabled ||
                            (data["signal-type"].startsWith("OTU") && !data["signal-type"].startsWith("OTUC"))
                        ) {
                            return <HexComponent value={val.substring(32, 64)} />;
                            // return asciiHexConvertToPlainText(state);
                        }
                        return getEditRander(
                            asciiHexConvertToPlainText(val.substring(32, 64)),
                            newVal => {
                                return {
                                    "terminal-device": {
                                        "logical-channels": {
                                            channel: {
                                                index: data["pm-channel-index"],
                                                otn: {
                                                    config: {
                                                        "tti-msg-transmit": getAsciiHexForSplitTTI(val, newVal, "dapi")
                                                    }
                                                }
                                            }
                                        }
                                    }
                                };
                            },
                            null,
                            null,
                            15,
                            true
                        );
                    } catch (e) {
                        return val;
                    }
                }
            },
            {
                dataIndex: "pm-tti-msg-transmit-oper",
                width: 220,
                render: (state, data) => {
                    const val = data["pm-tti-msg-transmit"];
                    if (!val) {
                        return state;
                    }
                    try {
                        if (
                            readyOnlyRight.disabled ||
                            (data["signal-type"].startsWith("OTU") && !data["signal-type"].startsWith("OTUC"))
                        ) {
                            return <HexComponent value={val.substring(64)} />;
                            // return asciiHexConvertToPlainText(state);
                        }
                        return getEditRander(
                            asciiHexConvertToPlainText(val.substring(64)),
                            newVal => {
                                return {
                                    "terminal-device": {
                                        "logical-channels": {
                                            channel: {
                                                index: data["pm-channel-index"],
                                                otn: {
                                                    config: {
                                                        "tti-msg-transmit": getAsciiHexForSplitTTI(val, newVal, "oper")
                                                    }
                                                }
                                            }
                                        }
                                    }
                                };
                            },
                            null,
                            null,
                            30,
                            true
                        );
                    } catch (e) {
                        return val;
                    }
                }
            },
            {
                dataIndex: "pm-tti-msg-expected-sapi",
                width: 220,
                render: (state, data) => {
                    const val = data["pm-tti-msg-expected"] ?? "";
                    try {
                        if (readyOnlyRight.disabled) {
                            return <HexComponent value={val.substring(0, 32)} />;
                        }
                        return getEditRander(
                            asciiHexConvertToPlainText(val.substring(0, 32)),
                            newVal => {
                                return {
                                    "terminal-device": {
                                        "logical-channels": {
                                            channel: {
                                                index: data["pm-channel-index"],
                                                otn: {
                                                    config: {
                                                        "tti-msg-expected": getAsciiHexForSplitTTI(val, newVal, "sapi")
                                                    }
                                                }
                                            }
                                        }
                                    }
                                };
                            },
                            null,
                            null,
                            15,
                            true
                        );
                    } catch (e) {
                        return val;
                    }
                }
            },
            {
                dataIndex: "pm-tti-msg-expected-dapi",
                width: 220,
                render: (state, data) => {
                    const val = data["pm-tti-msg-expected"] ?? "";
                    try {
                        if (readyOnlyRight.disabled) {
                            return <HexComponent value={val.substring(32, 64)} />;
                        }
                        return getEditRander(
                            asciiHexConvertToPlainText(val.substring(32, 64)),
                            newVal => {
                                return {
                                    "terminal-device": {
                                        "logical-channels": {
                                            channel: {
                                                index: data["pm-channel-index"],
                                                otn: {
                                                    config: {
                                                        "tti-msg-expected": getAsciiHexForSplitTTI(val, newVal, "dapi")
                                                    }
                                                }
                                            }
                                        }
                                    }
                                };
                            },
                            null,
                            null,
                            15,
                            true
                        );
                    } catch (e) {
                        return val;
                    }
                }
            },
            {
                dataIndex: "pm-tti-msg-expected-oper",
                width: 220,
                render: (state, data) => {
                    const val = data["pm-tti-msg-expected"] ?? "";
                    try {
                        if (readyOnlyRight.disabled) {
                            return <HexComponent value={val.substring(64)} />;
                        }
                        return getEditRander(
                            asciiHexConvertToPlainText(val.substring(64)),
                            newVal => {
                                return {
                                    "terminal-device": {
                                        "logical-channels": {
                                            channel: {
                                                index: data["pm-channel-index"],
                                                otn: {
                                                    config: {
                                                        "tti-msg-expected": getAsciiHexForSplitTTI(val, newVal, "oper")
                                                    }
                                                }
                                            }
                                        }
                                    }
                                };
                            },
                            null,
                            null,
                            30,
                            true
                        );
                    } catch (e) {
                        return val;
                    }
                }
            },
            {
                dataIndex: "pm-tti-msg-recv-sapi",
                width: 180,
                render: (state, data) => {
                    const val = data["pm-tti-msg-recv"];
                    if (val) {
                        return <HexComponent value={val.substring(0, 32)} />;
                    }
                    return state;
                }
            },
            {
                dataIndex: "pm-tti-msg-recv-dapi",
                width: 180,
                render: (state, data) => {
                    const val = data["pm-tti-msg-recv"];
                    if (val) {
                        return <HexComponent value={val.substring(32, 64)} />;
                    }
                    return state;
                }
            },
            {
                dataIndex: "pm-tti-msg-recv-oper",
                width: 180,
                render: (state, data) => {
                    const val = data["pm-tti-msg-recv"];
                    if (val) {
                        return <HexComponent value={val.substring(64)} />;
                    }
                    return state;
                }
            },
            {
                dataIndex: "signal-type",
                width: 120,
                fixed: "right",
                render: state => {
                    if (state) {
                        return state.substring(state.indexOf("_") + 1);
                    }
                    return state;
                }
            }
        ],
        "lldp-interfaces": [
            {
                dataIndex: "name",
                fixed: "left"
            },
            {
                dataIndex: "system-name"
            },
            {
                dataIndex: "system-description"
            },
            {
                dataIndex: "chassis-id",
                title: "Chassis Mac"
            },
            {
                dataIndex: "age"
            },
            {
                dataIndex: "last-update"
            },
            {
                dataIndex: "ttl"
            },
            {
                dataIndex: "port-id"
            },
            {
                dataIndex: "management-address"
            },
            {
                dataIndex: "enabled",
                fixed: "right",
                render: (state, rowData) => {
                    if (readyOnlyRight.disabled) {
                        return state;
                    }
                    return switchComponent(rowData.name, state, rowData, rowData?.name?.match?.(/-L\d/g), newVal => {
                        return {
                            lldp: {
                                interfaces: {
                                    interface: {
                                        name: rowData.name,
                                        config: {
                                            enabled: newVal
                                        }
                                    }
                                }
                            }
                        };
                    });
                }
            }
        ],
        "lldp-state": [
            [
                {dataIndex: "enabled", inputType: "switch"},
                {
                    dataIndex: "hello-timer",
                    unit: "seconds",
                    inputType: "input",
                    disabled: true
                },
                {
                    dataIndex: "system-name",
                    inputType: "input",
                    disabled: true
                }
            ],
            [
                {
                    dataIndex: "system-description",
                    inputType: "input",
                    disabled: true
                },
                {
                    dataIndex: "chassis-id",
                    title: "Chassis Mac",
                    inputType: "input",
                    disabled: true
                }
            ]
        ]
    };

    const cardSupport = {
        power: ["OA", "OLA", "OLP", "WSS", "TFF", "OCM"],
        edfa: ["OA", "OLA"],
        wss: ["WSS"],
        tff: ["TFF"],
        linecard: ["LINECARD"]
    };

    const supportCardType = [
        "11MC2",
        "20MC2",
        "2MC2",
        "4MC4",
        "OA1825",
        "OA1835",
        "OCM08",
        "OLA2525",
        "OLA3535",
        "OPB2",
        "OPB2-I",
        "OTDR08",
        "ROADM-09T"
    ];

    const getFilterPanel = type => {
        return (
            <div style={{marginTop: "8px", marginBottom: "8px"}}>
                <MutilColumnForm
                    fields={[
                        {
                            label: labelList.ne,
                            render: (
                                <Select
                                    value={selectNe}
                                    placeholder={labelList.please_select}
                                    filterSort={sortLabel()}
                                    style={{
                                        width: 280
                                    }}
                                    disabled={loading}
                                    allowClear
                                    onDropdownVisibleChange={async open => {
                                        let isL1 = false;
                                        if (open) {
                                            const filter = {};
                                            if (type !== "edfa") {
                                                filter.type = "5";
                                            }
                                            try {
                                                if (tabType === "linecard") {
                                                    // tabType = "power";
                                                    isL1 = true;
                                                }
                                                const [deviceResponse, objectResponse] = await Promise.all([
                                                    callOTNAPI("OTNDeviceIP", tabType, {layer: isL1 ? "l1" : "l0"}),
                                                    objectGet("config:ne", filter)
                                                ]);

                                                const {data: device_ip_list, errorCode, errorMsg} = deviceResponse;
                                                if (errorCode !== 200) {
                                                    message.error(errorMsg);
                                                    return;
                                                }

                                                setAmpconOTNDeviceList(device_ip_list);

                                                const {apiResult, apiMessage, documents} = objectResponse;
                                                if (apiResult === "fail") {
                                                    message.error(apiMessage).then();
                                                    return;
                                                }

                                                const _list = documents.map(item => ({
                                                    label: item.value.name,
                                                    value: item.value.ne_id,
                                                    type: item.value.type,
                                                    disabled: item.value.type === "2" && item.value.runState === 0
                                                }));

                                                const _list2 = device_ip_list.map(item => ({
                                                    label: item.name,
                                                    value: item.ip,
                                                    name: item?.name,
                                                    type: item?.type
                                                }));

                                                let combinedList;
                                                if (isL1) {
                                                    combinedList = [..._list, ..._list2];
                                                } else if (tabType === "oeo") {
                                                    combinedList = _list2;
                                                } else {
                                                    combinedList = [..._list, ..._list2];
                                                }

                                                combinedList.sort((a, b) =>
                                                    a.label.localeCompare(b.label, "ZH-CN", {numeric: true})
                                                );
                                                setNeOptions(combinedList);
                                            } catch (error) {
                                                console.error("Error fetching data:", error);
                                                message.error("Get data error");
                                            }

                                            // objectGet("config:ne", filter).then(rs => {
                                            //     const {apiResult, apiMessage, documents} = rs;
                                            //     if (apiResult === "fail") {
                                            //         message.error(apiMessage).then();
                                            //         return;
                                            //     }
                                            //     console.log(documents);
                                            //     const _list = documents.map(item => {
                                            //         return {
                                            //             label: item.value.name,
                                            //             value: item.value.ne_id,
                                            //             type: item.value.type,
                                            //             disabled: item.value.type === "2" && item.value.runState === 0
                                            //         };
                                            //     });
                                            //     _list.sort((a, b) =>
                                            //         a.label.localeCompare(b.label, "ZH-CN", {numeric: true})
                                            //     );
                                            //     setNeOptions(_list);
                                            // });
                                        }
                                    }}
                                    onChange={onSelectNe}
                                    options={neOptions}
                                />
                            )
                        },
                        {
                            label: labelList.card,
                            render: (
                                <Select
                                    value={
                                        ampconOTNDeviceList.some(device => device.ip === selectNe)
                                            ? selectCard?.value
                                            : (selectCard?.[type] ?? null)
                                    }
                                    placeholder={labelList.please_select}
                                    // filterSort={sortLabel()}
                                    loading={cardLoading}
                                    disabled={loading}
                                    allowClear
                                    style={{
                                        width: 280
                                    }}
                                    onDropdownVisibleChange={async open => {
                                        if (open) {
                                            if (!selectNe) {
                                                setCardOptions([]);
                                                return;
                                            }

                                            setCardLoading(true);
                                            if (ampconOTNDeviceList.some(device => device.ip === selectNe)) {
                                                await callOTNAPI("DeviceCard", tabType, {
                                                    ip: selectNe,
                                                    deviceType: selectNeTypeRef.current
                                                }).then(rs => {
                                                    if (rs.errorCode !== 200) {
                                                        message.error(rs.errorMsg);
                                                        return;
                                                    }
                                                    const _data = rs.data
                                                        .map(item => {
                                                            const [key, value] = Object.entries(item)[0];
                                                            return {
                                                                label: value,
                                                                value: key,
                                                                type: tabType
                                                            };
                                                        })
                                                        .filter(option => {
                                                            if (tabType === "oeo") {
                                                                return option.label.includes("OEO");
                                                            }
                                                            if (tabType === "edfa") {
                                                                return !option.label.includes("OEO");
                                                            }
                                                            return true;
                                                        });
                                                    setCardOptions(_data);
                                                    setCardLoading(false);
                                                });
                                            } else {
                                                objectGet("ne:5:component", {
                                                    ne_id: selectNe,
                                                    parent: "CHASSIS-1"
                                                }).then(rs => {
                                                    const _data = [];
                                                    rs.documents.map(i => {
                                                        const cardType = i.value.data.name.split("-")[0];
                                                        const vendorType = i.value.data.config?.["vendor-type-preconf"];
                                                        if (
                                                            cardSupport[type].includes(cardType) &&
                                                            vendorType &&
                                                            (vendorType.startsWith("TFF") ||
                                                                supportCardType.includes(vendorType))
                                                        ) {
                                                            const label = i.value.data.name.replace(
                                                                cardType,
                                                                i.value.data?.state?.["vendor-type-preconf"] ??
                                                                    i.value.data?.config?.["vendor-type-preconf"]
                                                            );
                                                            if (
                                                                (tabType === "oeo" && label.includes("OEO")) ||
                                                                (tabType === "edfa" && !label.includes("OEO")) ||
                                                                !tabType ||
                                                                (tabType !== "oeo" && tabType !== "edfa")
                                                            ) {
                                                                _data.push({
                                                                    label,
                                                                    value: i.value.data.name
                                                                });
                                                            }
                                                        }
                                                    });
                                                    _data.sort((a, b) => {
                                                        const arrA = a.label.split("-");
                                                        const arrB = b.label.split("-");
                                                        return arrA[arrA.length - 1] - arrB[arrB.length - 1];
                                                    });
                                                    setCardOptions(_data);
                                                    setCardLoading(false);
                                                });
                                            }
                                        }
                                    }}
                                    onChange={onSelectCard}
                                    options={cardOptions}
                                />
                            )
                        },
                        ...(type === "power"
                            ? [
                                  {
                                      label: labelList.port,
                                      render: (
                                          <Select
                                              value={selectPort ?? null}
                                              placeholder={labelList.please_select}
                                              // filterSort={sortLabel()}
                                              loading={portLoading}
                                              allowClear
                                              disabled={loading}
                                              style={{
                                                  width: 280
                                              }}
                                              onDropdownVisibleChange={open => {
                                                  if (open) {
                                                      if (
                                                          !selectNe ||
                                                          (!ampconOTNDeviceList.some(
                                                              device => device.ip === selectNe
                                                          ) &&
                                                              !selectCard.power)
                                                      ) {
                                                          setPortOptions([]);
                                                          return;
                                                      }
                                                      setPortLoading(true);

                                                      if (ampconOTNDeviceList.some(device => device.ip === selectNe)) {
                                                          setPortOptions(otnDevicePortOptions);
                                                          setPortLoading(false);
                                                          return;
                                                      }

                                                      objectGet("", {
                                                          DBKey: `ne:5:component:${selectNe}:${selectCard.power}`
                                                      }).then(rs => {
                                                          const _data = [];
                                                          rs.documents?.[0].value.data.subcomponents.subcomponent.map(
                                                              i => {
                                                                  if (!i.name.startsWith("LLDP")) {
                                                                      _data.push({
                                                                          label: i.name,
                                                                          value: i.name
                                                                      });
                                                                  }
                                                              }
                                                          );
                                                          setPortOptions(_data);
                                                          setPortLoading(false);
                                                      });
                                                  }
                                              }}
                                              onChange={onSelectPort}
                                              options={portOptions}
                                          />
                                      )
                                  }
                              ]
                            : [])
                    ]}
                />
            </div>
        );
    };

    const createDynamicTable = type => {
        if (type === "alarm") {
            const filter = {
                ne_id: selectNe,
                ...(selectCardRef?.current?.[tabType] ? {object: selectCardRef?.current?.[tabType]} : {})
            };
            return {
                key: "alarm",
                label: "Alarm",
                style: {flex: 1, display: "flex", flexDirection: "column"},
                children: <Alarm filter={filter} paginationEnable={false} containerName="Service_L0" />
            };
        }
        if (type === "power") {
            return {
                key: "power",
                label: "Optical Power Management",
                style: {flex: 1, display: "flex", flexDirection: "column"},
                children: (
                    <CustomTable
                        type="power_manager_linecard"
                        scroll={false}
                        initDataSource={filterTableData ?? tableData}
                        loading={loading}
                        paginationEnable={false}
                        initRowOperation={operationConfig.power}
                    />
                )
            };
        }
        if (type === "lldp") {
            return {
                key: "lldp",
                label: "LLDP",
                style: {flex: 1, display: "flex", flexDirection: "column"},
                children: (
                    <>
                        <div style={{maxWidth: 1500, marginBottom: 14}}>
                            {tableConfig["lldp-state"].map(i => {
                                return (
                                    <Row>
                                        {i.map((item, itemIndex) => {
                                            return (
                                                <Col
                                                    key={`${item.dataIndex}_col`}
                                                    style={{
                                                        width: "calc((100% - 48px) / 3)",
                                                        maxWidth: 440,
                                                        marginLeft: itemIndex === 0 ? 0 : 24,
                                                        overflow: "hidden",
                                                        display: "flex",
                                                        alignItems: "center",
                                                        marginBottom: 8
                                                    }}
                                                >
                                                    <div style={{width: "calc(100% - 280px)"}}>
                                                        {getText(item.title ?? item.dataIndex)}
                                                    </div>
                                                    <div style={{width: 280}}>
                                                        {item.inputType === "switch" && (
                                                            <Switch
                                                                disabled={readyOnlyRight.disabled}
                                                                checked={lldpData?.[0]?.[item.dataIndex] === "true"}
                                                                onChange={async newVal => {
                                                                    await apiEditRpc({
                                                                        ne_id: selectNe,
                                                                        params: {
                                                                            lldp: {
                                                                                config: {
                                                                                    enabled: newVal
                                                                                }
                                                                            }
                                                                        }
                                                                    });
                                                                    reloadData();
                                                                }}
                                                            />
                                                        )}
                                                        {item.inputType === "input" && (
                                                            <Input
                                                                style={{width: 280}}
                                                                key={`${item.dataIndex}_value`}
                                                                disabled={item.disabled}
                                                                value={lldpData?.[0]?.[item.dataIndex]}
                                                            />
                                                        )}
                                                    </div>
                                                </Col>
                                            );
                                        })}
                                    </Row>
                                );
                            })}
                        </div>
                        <CustomTable
                            type="lldp-interfaces"
                            initColumns={tableConfig["lldp-interfaces"].map(i => ({
                                ...i,
                                title:
                                    (labelList[i.title] ??
                                        getText(i.title) ??
                                        labelList[i.dataIndex] ??
                                        getText(i.dataIndex)) + (i.unit ? ` (${i.unit})` : "")
                            }))}
                            scroll={false}
                            initDataSource={lldpFilterData?.[1] ?? lldpData?.[1] ?? []}
                            loading={loading}
                        />
                    </>
                )
            };
        }
        return {
            key: type,
            style: {flex: 1, display: "flex", flexDirection: "column"},
            label: labelList[`${type}_manager`] ?? getText(type),
            children: (
                <>
                    {type === "wss" && (
                        <div style={{marginBottom: 10}}>
                            <Space>
                                <DebounceButton
                                    type="primary"
                                    icon={<Icon component={readyOnlyRight.disabled ? plusDisableIcon : plusIcon} />}
                                    disabled={readyOnlyRight.disabled}
                                    onClick={() => {
                                        openModalCreate({
                                            categoryName: "frequency-channel",
                                            type: "5",
                                            title: `${gLabelList.create} ${getText("frequency-channel")}`,
                                            keys: [selectCardRef.current[tabType]],
                                            ne_id: selectNe,
                                            callback: () => {
                                                setTimeout(() => {
                                                    reloadData();
                                                }, 2000);
                                            }
                                        });
                                    }}
                                >
                                    {labelList.create}
                                </DebounceButton>
                                <Button
                                    icon={<Icon component={refreshEnabledIcon} />}
                                    onClick={() => {
                                        reloadData();
                                    }}
                                >
                                    {labelList.refresh}
                                </Button>
                            </Space>
                        </div>
                    )}
                    <CustomTable
                        type={`${type}_manager`}
                        initColumns={tableConfig[type].map(i => ({
                            ...i,
                            title: getText(i.title ?? i.dataIndex) + (i.unit ? ` (${i.unit})` : "")
                        }))}
                        scroll={false}
                        initDataSource={DataMapping[type] ?? filterTableData ?? tableData}
                        loading={loading}
                    />
                </>
            )
        };
    };

    const dynamicTableConfig = {
        edfa: ["edfa", "osc", "alarm"],
        wss: ["edfa", "osc", "wss", "alarm"],
        tff: ["tff", "alarm"],
        linecard: ["port", "power", "otn-tti", "lldp", "alarm"],
        RAMAN_BOX: ["raman", "alarm"]
    };

    const getVOA = (cardType, cardName, portName, stateData) => {
        if (cardType === "TFF" && portName.includes("PT")) {
            return getDeviceStateValue(stateData, cardName, "actual-voa-attenuation") ?? NULL_VALUE;
        }
        return NULL_VALUE;
    };

    const getThresholdValue = (thresholdValues, point, parameter, granularity) => {
        return thresholdValues?.find(
            i =>
                i.value.data["pm-point"] === point &&
                i.value.data["pm-parameter"] === parameter &&
                i.value.data["pm-granularity"] === granularity
        );
    };

    const olpStateParameterMap = {
        APSP: "line-primary",
        APSS: "line-secondary",
        APSC: "common"
    };

    const getStateValue = (cardType, stateValues, componentName, parameter) => {
        if (cardType === "OLP") {
            parameter = `${olpStateParameterMap[componentName.split("-").pop()]}-${
                parameter === "input-power" ? "in" : "out"
            }`;
            componentName = `APS${componentName.substring(componentName.indexOf("-"), componentName.lastIndexOf("-"))}`;
        }
        return getDeviceStateValue(stateValues, componentName, parameter) ?? NULL_VALUE;
    };

    const formatThreshold = state => {
        if (state) {
            return parseFloat(state).toFixed(2);
        }
        return state ?? NULL_VALUE;
    };

    const operationConfig = {
        power: [
            {
                label: "Modify",
                disabled: v => {
                    return (
                        readyOnlyRight.disabled ||
                        (v["overhigh-input-threshold-15min"] === NULL_VALUE &&
                            v["overhigh-output-threshold-15min"] === NULL_VALUE &&
                            v["overlow-input-threshold-15min"] === NULL_VALUE &&
                            v["overlow-output-threshold-15min"] === NULL_VALUE)
                    );
                },
                async onClick() {
                    const originValue = {...this};
                    const columnsCfg = [
                        {
                            dataIndex: "overlow-input-threshold-15min",
                            title: "overlow-input-threshold",
                            inputType: "number",
                            step: 0.01,
                            required: true
                        },
                        {
                            dataIndex: "overhigh-input-threshold-15min",
                            title: "overhigh-input-threshold",
                            inputType: "number",
                            step: 0.01,
                            required: true
                        },
                        {
                            dataIndex: "overlow-output-threshold-15min",
                            title: "overlow-output-threshold",
                            inputType: "number",
                            step: 0.01,
                            required: true
                        },
                        {
                            dataIndex: "overhigh-output-threshold-15min",
                            title: "overhigh-output-threshold",
                            inputType: "number",
                            step: 0.01,
                            required: true
                        }
                    ];
                    openCustomEditForm({
                        title: "Modify",
                        columnNum: 1,
                        columns: columnsCfg.filter(i => originValue[i.dataIndex] !== NULL_VALUE).map(i => [i]),
                        getData: async () => {
                            return originValue;
                        },
                        saveFun: async (diffValue, value) => {
                            const changedKey = Object.keys(diffValue);
                            let thresholdNameKey = originValue["port-name"];
                            if (originValue["card-name"].startsWith("LINECARD")) {
                                thresholdNameKey = `TRANSCEIVER${thresholdNameKey.substring(
                                    thresholdNameKey.indexOf("-")
                                )}`;
                            }
                            let rs;
                            if (
                                changedKey.includes("overhigh-input-threshold-15min") ||
                                changedKey.includes("overlow-input-threshold-15min")
                            ) {
                                rs = await apiEditRpc({
                                    ne_id: selectNe,
                                    msg: false,
                                    params: {
                                        performance: {
                                            tcas: {
                                                tca: {
                                                    "pm-granularity": "15MIN",
                                                    "pm-parameter": "INPUT-POWER",
                                                    "pm-point": thresholdNameKey,
                                                    "pm-point-type": originValue["pm-point-type"],
                                                    "threshold-value-high": value["overhigh-input-threshold-15min"],
                                                    "threshold-value-low": value["overlow-input-threshold-15min"]
                                                }
                                            }
                                        }
                                    }
                                });
                                if (rs.apiResult === "fail") {
                                    message.error(gLabelList.save_failed);
                                    return rs;
                                }
                            }
                            if (
                                changedKey.includes("overhigh-output-threshold-15min") ||
                                changedKey.includes("overlow-output-threshold-15min")
                            ) {
                                rs = await apiEditRpc({
                                    ne_id: selectNe,
                                    msg: false,
                                    params: {
                                        performance: {
                                            tcas: {
                                                tca: {
                                                    "pm-granularity": "15MIN",
                                                    "pm-parameter": "OUTPUT-POWER",
                                                    "pm-point": thresholdNameKey,
                                                    "pm-point-type": originValue["pm-point-type"],
                                                    "threshold-value-high": value["overhigh-output-threshold-15min"],
                                                    "threshold-value-low": value["overlow-output-threshold-15min"]
                                                }
                                            }
                                        }
                                    }
                                });
                                if (rs.apiResult === "fail") {
                                    message.error(gLabelList.save_failed);
                                    return rs;
                                }
                            }
                            message.success(gLabelList.save_success);
                            setTimeout(() => {
                                reloadData();
                            }, 1000);
                            return rs;
                        }
                    });
                }
            }
        ]
    };

    const getDataFromAllData = async () => {
        const configRs = await netconfGetByXML({
            ne_id: selectNe,
            type: "get-config",
            msg: true,
            xml: {
                components: {
                    $: {
                        xmlns: "http://openconfig.net/yang/platform"
                    },
                    component: {}
                },
                "terminal-device": {
                    $: {
                        xmlns: "http://openconfig.net/yang/terminal-device"
                    },
                    "logical-channels": {
                        channel: {}
                    }
                },
                interfaces: {
                    $: {
                        xmlns: "http://openconfig.net/yang/interfaces"
                    },
                    interface: {}
                }
            }
        });
        const stateRs = await getStateData({
            ne_id: selectNe
        });
        return {config: configRs, state: stateRs?.data?.value?.data?.["state-data"]};
    };

    const loadOpticalPowerData = async (needLoading, filterCard) => {
        try {
            const NotSupportPortTypeKeys = {
                OA: ["-OTDR", "-MON"],
                WSS: ["-OTDR", "-MON"],
                OLP: ["APS-"],
                LINECARD: ["LLDPINTERFACE-"]
            };
            if (!selectNe) {
                setTableData([]);
                return;
            }
            if (needLoading) {
                setLoading(true);
            }
            const thresholdValue = (await objectGet("ne:5:tca", {ne_id: selectNe})).documents;
            const stateData =
                (
                    await getStateData({
                        ne_id: selectNe
                    })
                )?.data?.value?.data?.["state-data"] ?? {};
            objectGet("ne:5:component", {
                ne_id: selectNe,
                parent: "CHASSIS-1"
            }).then(rs => {
                const _data = [];
                rs.documents.forEach(_card => {
                    const cardType = _card.value.data.name.split("-")[0];
                    const notSupportTypeKeys = NotSupportPortTypeKeys[cardType] ?? [];
                    if (
                        ((filterCard && ["LINECARD"].includes(cardType)) ||
                            (!filterCard && ["OA", "OLA", "WSS", "OLP", "TFF"].includes(cardType))) &&
                        _card.value.data.config?.["vendor-type-preconf"]
                    ) {
                        const cardName = _card.value.data.name;
                        // if (selectCardRef.current?.power) {
                        //     if (selectCardRef.current.power !== cardName) {
                        //         return false;
                        //     }
                        // }
                        if (filterCard && cardName !== filterCard) {
                            return false;
                        }
                        _card.value.data.subcomponents?.subcomponent?.forEach?.(_port => {
                            const portName = _port.name;
                            if (notSupportTypeKeys.find(_type => portName.indexOf(_type) > -1)) {
                                return true;
                            }
                            let thresholdNameKey = portName;
                            if (cardType === "LINECARD") {
                                thresholdNameKey = `TRANSCEIVER${thresholdNameKey.substring(
                                    thresholdNameKey.indexOf("-")
                                )}`;
                            }
                            const thresholdValue_15min_IN =
                                getThresholdValue(thresholdValue, thresholdNameKey, "INPUT-POWER", "15MIN")?.value
                                    ?.data ?? {};
                            const thresholdValue_15min_OUT =
                                getThresholdValue(thresholdValue, thresholdNameKey, "OUTPUT-POWER", "15MIN")?.value
                                    ?.data ?? {};
                            _data.push({
                                key: `${selectNe}_${portName}`,
                                "ne-name": neNameMap[selectNe],
                                ne_id: selectNe,
                                "card-type": _card.value.data?.state?.["actual-vendor-type"] ?? cardType,
                                "card-name": cardName,
                                "slot-no": cardName.split("-")[2],
                                "port-name": portName,
                                "voa-attenuation": getVOA(cardType, cardName, portName, stateData),
                                "input-optical-power": getStateValue(cardType, stateData, portName, "input-power"),
                                "output-optical-power": getStateValue(cardType, stateData, portName, "output-power"),
                                "overlow-input-threshold-15min": formatThreshold(
                                    thresholdValue_15min_IN?.["threshold-value-low"]
                                ),
                                "overhigh-input-threshold-15min": formatThreshold(
                                    thresholdValue_15min_IN?.["threshold-value-high"]
                                ),
                                "overlow-output-threshold-15min": formatThreshold(
                                    thresholdValue_15min_OUT?.["threshold-value-low"]
                                ),
                                "overhigh-output-threshold-15min": formatThreshold(
                                    thresholdValue_15min_OUT?.["threshold-value-high"]
                                ),
                                "pm-point-type":
                                    thresholdValue_15min_IN["pm-point-type"] ??
                                    thresholdValue_15min_OUT["pm-point-type"]
                            });
                        });
                    }
                });
                _data.sort((a, b) => (a["slot-no"] >= b["slot-no"] ? 1 : -1));
                const _tableData = _data.map((i, index) => ({...i, index: index + 1}));
                if (!activeTabRef.current || activeTabRef.current === "power") {
                    setTableData(_tableData);
                    if (selectCardRef.current?.power) {
                        filterPowerManagementData(_tableData);
                    }
                    if (selectedTreeNodeRef.current && selectedTreeNodeRef.current.toString().startsWith("PORT")) {
                        setFilterTableData(_tableData.filter(i => i["port-name"] === selectedTreeNodeRef.current));
                    }
                    setLoading(false);
                }
            });
        } catch (e) {
            setTableData([]);
            setLoading(false);
            console.log(e);
        }
    };

    const loadEDFAData = async () => {
        try {
            const cardMach = selectCardRef.current[tabType];
            const _dataList = [];
            netconfGetByXML({
                ne_id: selectNe,
                msg: false,
                xml: {
                    "optical-amplifier": {
                        $: {
                            xmlns: "http://openconfig.net/yang/optical-amplifier"
                        },
                        amplifiers: {amplifier: {}}
                    }
                }
            }).then(rs => {
                const machKey = cardMach.substring(cardMach.indexOf("-"));
                const amplifiers = rs?.["optical-amplifier"]?.amplifiers?.amplifier ?? [];
                convertToArray(amplifiers).forEach(amplifier => {
                    if (amplifier.name.indexOf(`${machKey}-`) > -1) {
                        _dataList.push({
                            ...amplifier.state,
                            ...amplifier.config,
                            enabled: amplifier.config.enabled,
                            "actual-voa-attenuation":
                                amplifier.state?.["actual-voa-attenuation"] ??
                                amplifier.config?.["actual-voa-attenuation"],
                            "target-voa-attenuation":
                                amplifier.config?.["actual-voa-attenuation"] ??
                                amplifier.state?.["actual-voa-attenuation"],
                            "input-power": amplifier.state?.["input-power"]?.instant,
                            "output-power": amplifier.state?.["output-power"]?.instant,
                            "actual-gain":
                                amplifier.config?.["actual-gain"]?.instant ?? amplifier.state?.["actual-gain"]?.instant,
                            "target-gain": amplifier.config?.["target-gain"] ?? amplifier.state?.["target-gain"],
                            "target-gain-tilt":
                                amplifier.config?.["target-gain-tilt"] ?? amplifier.state?.["target-gain-tilt"]
                        });
                    }
                });
                // updateTableData("edfa", cardMach, _dataList);
                setEdfaData(_dataList);
                setLoading(false);
            });
        } catch (e) {
            setEdfaData([]);
            setLoading(false);
            console.log(e);
        }
    };

    const loadWSSData = async () => {
        try {
            const cardMach = selectCardRef.current[tabType];
            let _dataList = [];
            netconfGetByXML({
                ne_id: selectNe,
                msg: false,
                xml: {
                    components: {
                        $: {
                            xmlns: "http://openconfig.net/yang/platform"
                        },
                        component: {
                            $: {
                                xmlns: "http://openconfig.net/yang/platform"
                            },
                            name: selectCardRef.current.wss,
                            wss: {}
                        }
                    }
                }
            }).then(rs => {
                const data = rs?.components?.component?.wss?.state?.["frequency-channel"];
                if (data) {
                    _dataList = convertToArray(data);
                }
                updateTableData("wss", cardMach, _dataList);
                setLoading(false);
            });
        } catch (e) {
            setTableData([]);
            setLoading(false);
            console.log(e);
        }
    };

    const loadTFFData = async () => {
        try {
            const cardMach = selectCardRef.current.tff;
            getDataFromAllData().then(rs => {
                const components = rs?.config?.components?.component ?? [];
                const _dataList = convertToArray(components)
                    .filter(component => component.name === cardMach)
                    .map(() => ({
                        name: cardMach,
                        "target-voa-attenuation":
                            getDeviceStateValue(rs.state, cardMach, "target-voa-attenuation") ?? "",
                        "channel-interval": getDeviceStateValue(rs.state, cardMach, "channel-interval") ?? "",
                        "actual-voa-attenuation":
                            getDeviceStateValue(rs.state, cardMach, "actual-voa-attenuation") ?? ""
                    }));
                updateTableData("tff", cardMach, _dataList);
                setLoading(false);
            });
        } catch (e) {
            setTableData([]);
            setLoading(false);
            console.log(e);
        }
    };

    const loadPortManagerData = async () => {
        try {
            const cardMach = selectCardRef.current.linecard;
            getDataFromAllData().then(rs => {
                objectGet("nms:provision", {}).then(prRs => {
                    const services = prRs.documents;

                    const _dataList = [];
                    const components = rs?.config?.components?.component ?? [];
                    const channels = rs?.config?.["terminal-device"]?.["logical-channels"]?.channel ?? [];
                    const interfaces = rs?.config?.interfaces?.interface ?? [];
                    components.forEach(component => {
                        if (component.name.startsWith("LINECARD-")) {
                            if (cardMach && component.name !== cardMach) {
                                return true;
                            }
                            const card = {
                                card: component.name,
                                "actual-vendor-type":
                                    component?.config?.["vendor-type-preconf"] ??
                                    getDeviceStateValue(rs.state, component.name, "actual-vendor-type")
                            }; // card
                            const cardNameKeys = `${component.name.replace("LINECARD", "")}-`;
                            const ports = components.filter(cp => cp.name.startsWith(`PORT${cardNameKeys}`));
                            ports?.forEach(portComponent => {
                                // const portIndex = portComponent.name.split("-").pop();
                                const port = {
                                    ...card,
                                    port: portComponent.name,
                                    key: portComponent.name,
                                    // "port-display": `${selectCardRef.current.linecard_cardType.split("-")[0]}-${
                                    //     portIndex.toString().charAt(0) === "C" ? "CLIENT" : "LINE"
                                    // }-${portIndex.toString().substring(1)}`,
                                    "signal-type":
                                        portComponent.port?.config?.["layer-protocol-name"]?._?.split(":")?.pop() ??
                                        getDeviceStateValue(rs.state, portComponent.name, "layer-protocol-name"),
                                    "reverse-mode":
                                        getDeviceStateValue(rs.state, portComponent.name, "reverse-mode") ?? ""
                                };

                                const transceiverName = portComponent.name.replace("PORT", "TRANSCEIVER");
                                const transceiverList = components.find(i => i.name === transceiverName); // transceiver
                                if (transceiverList) {
                                    port["module-type"] =
                                        getDeviceStateValue(rs.state, portComponent.name, "actual-vendor-type") ??
                                        transceiverList?.config?.["actual-vendor-type"] ??
                                        NULL_VALUE;
                                    port["laser-enable"] = transceiverList.transceiver?.config?.enabled;
                                    // port["laser-enable"] =
                                    //     getDeviceStateValue(rs.state, portComponent.name, "enabled") ?? NULL_VALUE;
                                } else {
                                    port["module-type"] = NULL_VALUE;
                                }
                                // loopback
                                const _portNameInfo = portComponent.name.replace("PORT", "");
                                channels.forEach(c => {
                                    const channelName = c.config?.description ?? c.state?.description;
                                    if (
                                        channelName &&
                                        channelName.endsWith(_portNameInfo) &&
                                        c.config?.["loopback-mode"]
                                    ) {
                                        port["channel-index"] = c.index;
                                        port.loopback = getDeviceStateValue(
                                            rs.state,
                                            portComponent.name,
                                            "loopback-mode"
                                        );
                                        return false;
                                    }
                                });
                                if (port.port.indexOf("L") > -1) {
                                    const ochPort = portComponent.name.replace("PORT", "OCH");
                                    const ochObject = components.find(i => i.name === ochPort)?.["optical-channel"]
                                        ?.config;
                                    if (ochObject) {
                                        port.modulation = ochObject["operational-mode"];
                                        port["target-output-power"] = ochObject["target-output-power"];
                                    } else {
                                        port.modulation = getDeviceStateValue(rs.state, ochPort, "operational-mode");
                                        port["target-output-power"] = getDeviceStateValue(
                                            rs.state,
                                            ochPort,
                                            "target-output-power"
                                        );
                                    }
                                    const ochWavelength = components.reduce((acc, item) => {
                                        if (
                                            item["optical-channel"] &&
                                            item["optical-channel"].config &&
                                            item.name === ochPort
                                        ) {
                                            acc.push(item["optical-channel"].config.frequency);
                                        }
                                        return acc;
                                    }, []);
                                    port.wavelength_origin = ochWavelength[0];
                                    port.wavelength = ochWavelength;
                                    const frequencyValue = frequencyOptions.reduce((acc, item) => {
                                        const filteredChildren = item.children?.filter(
                                            labal =>
                                                labal.label.includes((port.wavelength / 1000000).toFixed(2)) ||
                                                labal.label.includes((port.wavelength / 1000000).toFixed(4))
                                        );

                                        if (filteredChildren && filteredChildren.length > 0) {
                                            acc = filteredChildren;
                                        }
                                        return acc;
                                    }, {});
                                    const substringIndex = frequencyValue?.[0]?.value?.indexOf?.("-");
                                    if (substringIndex) {
                                        port.wavelength = frequencyValue?.[0].value?.substring(substringIndex + 1);
                                    }

                                    port["pre-fec"] = getDeviceStateValue(rs.state, ochPort, "pre-fec-ber");
                                    port["post-fec"] = getDeviceStateValue(rs.state, ochPort, "post-fec-ber");
                                    channels.forEach(c => {
                                        const channelName = c.config?.description ?? c.state?.description;
                                        if (
                                            channelName &&
                                            channelName.endsWith(_portNameInfo) &&
                                            c.otn &&
                                            c.config?.description.startsWith("OTUC")
                                        ) {
                                            port["signal-type"] = c.config?.description?.split("-")?.[0];
                                            return false;
                                        }
                                    });
                                    port["port-used"] = services.find(
                                        service =>
                                            service.value.type === "och" &&
                                            ((service.value.a.ne_id === selectNe &&
                                                service.value.a.port === port.port) ||
                                                (service.value.z.ne_id === selectNe &&
                                                    service.value.z.port === port.port))
                                    )
                                        ? "True"
                                        : "False";
                                } else {
                                    port.wavelength = NULL_VALUE;
                                    port["target-output-power"] = NULL_VALUE;
                                    port["pre-fec"] = NULL_VALUE;
                                    port["post-fec"] = NULL_VALUE;
                                    port["port-used"] = services.find(
                                        service =>
                                            service.value.type === "client" &&
                                            service.value.ne.find(
                                                neInfo => neInfo.ne_id === selectNe && neInfo.port === port.port
                                            )
                                    )
                                        ? "True"
                                        : "False";
                                }
                                // interface
                                const _interface = interfaces.filter(i => i.name === `INTERFACE${_portNameInfo}`);
                                if (_interface.length > 0) {
                                    let subObj;
                                    if (_interface[0].fc) {
                                        subObj = _interface[0].fc;
                                        port.interface_type = "fc";
                                    } else if (_interface[0].sdh) {
                                        subObj = _interface[0].sdh;
                                        port.interface_type = "sdh";
                                    } else if (_interface[0].otn) {
                                        subObj = _interface[0].otn;
                                        port.interface_type = "otn";
                                    } else if (_interface[0].ethernet) {
                                        port.interface_type = "ethernet";
                                        subObj = _interface[0].ethernet;
                                    }
                                    if (subObj) {
                                        port.fec =
                                            subObj?.config?.["client-fec"]?._ ??
                                            getDeviceStateValue(rs.state, portComponent.name, "client-fec");
                                        let _als = getDeviceStateValue(rs.state, portComponent.name, "client-als");
                                        port.als_org = _als;
                                        if (!_als) {
                                            _als = "";
                                        } else if (_als === "LASER_SHUTDOWN") {
                                            _als = "ENABLED";
                                        } else if (_als === "ETHERNET") {
                                            _als = "DISABLED";
                                        }
                                        port.als = _als;
                                    }
                                    port["interface-name"] = _interface[0].name;
                                }
                                _dataList.push(port);
                            });
                        }
                    });
                    _dataList.sort((a, b) => {
                        if (a.card !== b.card) {
                            return a.card.localeCompare(b.card, "ZH-CN", {numeric: true});
                        }
                        return a.port.localeCompare(b.port, "ZH-CN", {numeric: true});
                    });
                    updateTableData("port", cardMach, _dataList);
                    setPortData(_dataList);
                    if (selectedTreeNodeRef.current && selectedTreeNodeRef.current.toString().startsWith("PORT")) {
                        setPortFilterData(_dataList.filter(i => i.port === selectedTreeNodeRef.current));
                    }
                    setLoading(false);
                });
            });
        } catch (e) {
            setPortData([]);
            setLoading(false);
            console.log(e);
        }
    };

    const loadTTIData = async () => {
        try {
            const cardMach = selectCardRef.current.linecard;
            getDataFromAllData().then(rs => {
                const _dataList = [];
                const components = rs?.config?.components?.component ?? [];
                const channels = rs?.config?.["terminal-device"]?.["logical-channels"]?.channel ?? [];
                components.forEach(component => {
                    if (component.name.startsWith("LINECARD-")) {
                        if (cardMach && component.name !== cardMach) {
                            return true;
                        }
                        const card = {
                            card: component.name,
                            "actual-vendor-type":
                                component?.config?.["vendor-type-preconf"] ?? component?.state?.["actual-vendor-type"]
                        }; // card
                        const cardNameKeys = `${component.name.replace("LINECARD", "")}-`;
                        const ports = components.filter(cp => cp.name.startsWith(`PORT${cardNameKeys}`));
                        ports?.forEach(portComponent => {
                            if (!portComponent) {
                                return true;
                            }
                            const port = {
                                ...card,
                                port: portComponent.name,
                                key: portComponent.name,
                                "signal-type":
                                    portComponent.port?.config?.["layer-protocol-name"]?._?.split(":")?.pop() ??
                                    getDeviceStateValue(rs.state, portComponent.name, "layer-protocol-name")
                            };
                            const _portNameInfo = portComponent.name.substring(portComponent.name.indexOf("-"));
                            for (let i = 0; i < channels.length; i++) {
                                const c = channels[i];
                                const channelName = c.config?.description ?? c.state?.description;
                                if (channelName.endsWith(_portNameInfo) && c.otn) {
                                    if (c.config?.description?.startsWith("OTU")) {
                                        port.otn_signal = true;
                                        port["sm-tti-msg-expected"] = c.otn.config["tti-msg-expected"];
                                        port["sm-tti-msg-recv"] = getDeviceStateValue(
                                            rs.state,
                                            portComponent.name,
                                            "sm-tti-msg-recv"
                                        );
                                        port["sm-tti-msg-transmit"] = c.otn.config["tti-msg-transmit"];
                                        port["sm-channel-index"] = c.index;
                                        // eslint-disable-next-line prefer-destructuring
                                        port["signal-type"] = c.config.description.split("-")[0];
                                        break;
                                    } else {
                                        port.otn_signal = false;
                                        port["pm-tti-msg-expected"] = c.otn?.config?.["tti-msg-expected"];
                                        port["pm-tti-msg-recv"] = getDeviceStateValue(
                                            rs.state,
                                            portComponent.name,
                                            "pm-tti-msg-recv"
                                        );
                                        port["pm-tti-msg-transmit"] = c.otn?.config?.["tti-msg-transmit"];
                                        port["pm-channel-index"] = c.index;
                                    }
                                }
                            }
                            _dataList.push(port);
                        });
                    }
                });
                _dataList.sort((a, b) => a.port.localeCompare(b.port, "ZH-CN", {numeric: true}));
                // updateTableData("otn-tti", cardMach, _dataList);
                setTtiData(_dataList);
                if (selectedTreeNodeRef.current && selectedTreeNodeRef.current.toString().startsWith("PORT")) {
                    setTtiFilterData(_dataList.filter(i => i.port === selectedTreeNodeRef.current));
                }
                setLoading(false);
            });
        } catch (e) {
            console.log(e);
            setTtiData([]);
            setLoading(false);
        }
    };

    const loadLLDPData = async () => {
        try {
            // const cardMach = selectCardRef.current.linecard;
            netconfGetByXML({
                msg: true,
                ne_id: selectNe,
                xml: {
                    lldp: {
                        $: {
                            xmlns: "http://openconfig.net/yang/lldp"
                        }
                    }
                }
            }).then(rs => {
                let interfaces = [];
                convertToArray(rs?.lldp?.interfaces?.interface).map(i => {
                    const it = {
                        name: i.name,
                        enabled: i.state?.enabled
                    };
                    if (i.neighbors?.neighbor) {
                        convertToArray(i.neighbors.neighbor).map(n => {
                            interfaces.push({...it, ...n.state});
                        });
                    } else {
                        interfaces.push(it);
                    }
                });
                interfaces = interfaces.map(i => ({...i, key: i.name}));
                const state = rs?.lldp?.state;
                // state.enable = rs?.lldp?.state?.enabled;
                // updateTableData("lldp", cardMach, [state, interfaces]);
                setLldpData([state, interfaces]);
                if (selectedTreeNodeRef.current) {
                    setLldpFilterData([
                        state,
                        interfaces?.filter?.(
                            i =>
                                i.name.indexOf(
                                    selectedTreeNodeRef.current
                                        .toString()
                                        .substring(selectedTreeNodeRef.current.toString().indexOf("-"))
                                ) > -1
                        )
                    ]);
                }
                setLoading(false);
            });
        } catch (e) {
            setLoading(false);
            setLldpData([]);
            console.log(e);
        }
    };

    const loadOSCData = async () => {
        try {
            const _dataList = [];
            const cardMach = selectCardRef.current[tabType];
            getDataFromAllData().then(rs => {
                const components = rs?.config?.components?.component ?? [];
                const machKey = `TRANSCEIVER${cardMach.substring(cardMach.indexOf("-"))}-OSC`;
                convertToArray(components).forEach(component => {
                    if (component.name.startsWith(machKey)) {
                        _dataList.push({
                            ...component,
                            "used-service-port-type-preconf":
                                component.transceiver.config?.["used-service-port-type-preconf"],
                            enabled: component.transceiver.config?.enabled
                        });
                    }
                });
                setOscData(_dataList);
                // updateTableData("osc", cardMach, _dataList);
                setLoading(false);
            });
        } catch (e) {
            setOscData([]);
            setLoading(false);
            console.log(e);
        }
    };

    const loadData = async (needLoading = false, resetFilter = true) => {
        if (needLoading && resetFilter) {
            setFilterTableData(null);
        }
        if (tabType === "power") {
            loadOpticalPowerData(needLoading).then();
        } else if (selectCard?.[tabType]) {
            let load = false;
            if (activeTab !== "alarm" && needLoading) {
                setLoading(true);
                setTableData([]);
                load = true;
            }
            if (tabType === "edfa") {
                if (!activeTab || activeTab === "edfa") {
                    if (load) setEdfaData([]);
                    loadEDFAData().then();
                } else if (activeTab === "osc") {
                    if (load) setOscData([]);
                    loadOSCData().then();
                }
            } else if (tabType === "wss") {
                if (!activeTab || activeTab === "edfa") {
                    if (load) setEdfaData([]);
                    loadEDFAData().then();
                } else if (activeTab === "osc") {
                    if (load) setOscData([]);
                    loadOSCData().then();
                } else {
                    loadWSSData().then();
                }
            } else if (tabType === "tff") {
                loadTFFData().then();
            } else if (tabType === "linecard") {
                if (!activeTab || activeTab === "port") {
                    if (load) setPortData([]);
                    loadPortManagerData().then();
                } else if (activeTab === "otn-tti") {
                    if (load) setTtiData([]);
                    loadTTIData().then();
                } else if (activeTab === "lldp") {
                    if (load) setLldpData([]);
                    loadLLDPData().then();
                } else if (activeTab === "power") {
                    loadOpticalPowerData(needLoading, selectCardRef.current.linecard).then();
                }
            }
        }
    };

    const {runAsync: getServiceData} = useRequest(
        () =>
            Promise.all([
                objectGet("ne:5:component", {
                    ne_id: selectNe
                }),
                objectGet("ne:5:interface", {
                    ne_id: selectNe
                }),
                objectGet("ne:5:otdr", {
                    ne_id: selectNe
                }),
                objectGet("ne:5:aps-module", {
                    ne_id: selectNe
                }),
                objectGet("ne:5:amplifier", {
                    ne_id: selectNe
                }),
                objectGet("ne:5:channel", {
                    ne_id: selectNe
                }),
                objectGet("ne:5:channel-monitor", {
                    ne_id: selectNe
                }),
                objectGet("ne:5:supervisory-channel", {
                    ne_id: selectNe
                })
            ]),
        {
            manual: true,
            onSuccess: res => {
                const [com, inter, otdr, _aps, amp, chan, _chan, sup] = res;
                if (com && inter && otdr && _aps) {
                    const components = com.documents.map(item => item.value.data);
                    const interfaces = inter.documents.map(item => item.value.data);
                    const otdrs = otdr.documents.map(item => item.value.data);
                    const amplifier = amp.documents.map(item => item.value.data);
                    const aps = _aps.documents.map(item => item.value.data);
                    const channel = chan.documents.map(item => item.value.data);
                    const channelMonitors = _chan.documents.map(item => item.value.data);
                    const supervisoryChannel = sup.documents.map(item => item.value.data);
                    const allComponents = generateComponentTree(components);
                    // 当componet出问题（无parent）时，删除掉
                    Object.entries(allComponents)?.forEach(([key, value]) => {
                        if (!value?.parent && !Object.values(allComponents)?.some(item => item?.parent === key)) {
                            delete allComponents[key];
                        }
                    });
                    // interface
                    const allInterfaces = generateInterfaceTree(interfaces, allComponents);

                    // amplifier
                    convertToArray(amplifier)?.forEach(amplifierObj => {
                        const entity_type = getAttrValue(amplifierObj.state, "type");
                        if (entity_type && typeof entity_type === "string") {
                            const treeData = {
                                id: amplifierObj.name,
                                key: `amplifier${seperator}${removeNS(entity_type)}${seperator}${amplifierObj.name}`,
                                title: amplifierObj.name,
                                objectType: "amplifier",
                                entityType: removeNS(entity_type),
                                children: []
                            };
                            allComponents[amplifierObj.name]?.treeData.children.push(treeData);
                        }
                    });
                    convertToArray(supervisoryChannel)?.forEach(supervisoryChannel => {
                        const parentName = supervisoryChannel.interface;
                        if (parentName) {
                            const entity_type = "supervisory-channel";
                            const treeData = {
                                id: supervisoryChannel.interface,
                                key: `supervisory-channel${seperator}${entity_type}${seperator}${supervisoryChannel.interface}`,
                                title: supervisoryChannel.interface,
                                objectType: "supervisory-channel",
                                entityType: entity_type,
                                children: []
                            };
                            allInterfaces[parentName]?.children.push(treeData);
                        }
                    });
                    // otdrs
                    convertToArray(otdrs)?.forEach(otdr => {
                        const parentName = otdr.name;
                        if (parentName) {
                            const entity_type = "otdr";
                            const treeData = {
                                id: otdr.name,
                                key: `otdr${seperator}${entity_type}${seperator}${otdr.name}`,
                                title: otdr.name,
                                objectType: "otdr",
                                entityType: entity_type,
                                children: []
                            };
                            allComponents[parentName]?.treeData.children.push(treeData);
                        }
                    });
                    // channel-monitor
                    convertToArray(channelMonitors?.["channel-monitor"])?.forEach(channelMonitor => {
                        if (channelMonitor.name) {
                            const treeData = {
                                id: channelMonitor.name,
                                key: `channel-monitor${seperator}channel-monitor${seperator}${channelMonitor.name}`,
                                title: channelMonitor.name,
                                objectType: "channel-monitor",
                                entityType: "channel-monitor",
                                children: []
                            };
                            allComponents[channelMonitor.name]?.treeData.children.push(treeData);
                        }
                    });

                    // aps
                    convertToArray(aps?.["aps-modules"]?.["aps-module"])?.forEach(aps => {
                        if (allComponents[aps.name]) {
                            const entity_type = "aps";
                            const treeData = {
                                id: aps.name,
                                key: `aps${seperator}${entity_type}${seperator}${aps.name}`,
                                title: aps.name,
                                objectType: "aps",
                                entityType: entity_type,
                                children: []
                            };
                            allComponents[aps.name]?.treeData.children.push(treeData);
                        }
                    });

                    let rootNode;

                    const cardData = [];
                    const channels = convertToArray(channel);
                    const allChannels = {};
                    channels?.forEach(channel => {
                        const assignment = channel["logical-channel-assignments"]?.assignment?.state;
                        if (assignment) {
                            const parentType = assignment["assignment-type"].toLowerCase().replace("_", "-");
                            allChannels[channel.index] = {
                                parentType,
                                parent: assignment[parentType],
                                treeData: {
                                    id: channel.index,
                                    value: {
                                        title: channel.state.description
                                    },
                                    key: `channel${seperator}${channel.index}`,
                                    title: channel.state.description,
                                    objectType: "channel",
                                    delayTestMode: channel.config?.["delay-test-mode"] ?? null,
                                    children: []
                                }
                            };
                        }
                    });
                    Object.values({
                        ...allComponents,
                        ...allChannels,
                        ...allInterfaces
                    }).forEach(({parent, parentType, treeData}) => {
                        if (treeData) {
                            if (!parent) {
                                rootNode = treeData;
                            } else if (parentType === "logical-channel") {
                                // allChannels[parent]?.treeData.children.push(treeData);
                            } else if (treeData?.id.startsWith("PORT") || parent?.startsWith("CHASSIS")) {
                                allComponents[parent]?.treeData.children.push(treeData);
                            }
                        }
                    });

                    rootNode?.children.forEach(child => {
                        if (child.entityType === "LINECARD" && !cardData.find(item => item.id === child.id)) {
                            cardData.push(child);
                        }
                        if (
                            !["LINECARD", "FAN", "CONTROLLER_CARD", "SLOT", "POWER_SUPPLY"].includes(child.entityType)
                        ) {
                            if (child.entityType === "WSS") {
                                const portGroup = {
                                    key: "PortGroup",
                                    title: "Port AD List",
                                    objectType: "PortGroup",
                                    children: []
                                };
                                for (let i = child.children.length - 1; i >= 0; --i) {
                                    const subChild = child.children[i];
                                    if (subChild.entityType === "PORT" && subChild.title?.match(/AD\d+$/) != null) {
                                        portGroup.children.push(subChild);
                                        child.children.splice(i, 1);
                                    }
                                }
                                portGroup.children.reverse();
                                if (portGroup.children.length > 0) {
                                    child.children.push(portGroup);
                                }
                            }

                            cardData.push(child);
                        }
                    });
                    const sortByTitle = arr => {
                        if (!arr || !Array.isArray(arr)) {
                            return arr;
                        }

                        arr.forEach(item => {
                            if (item.children && item.children.length > 0) {
                                item.children = sortByTitle(item.children);
                            }
                        });

                        arr.sort((a, b) => {
                            const lowerCaseA = `${a.title}`;
                            const lowerCaseB = `${b.title}`;
                            return lowerCaseA.localeCompare(lowerCaseB, "ZH-CN", {
                                numeric: true
                            });
                        });

                        return arr;
                    };
                    sortByTitle(cardData);
                    setDrawerTreeData(
                        cardData.sort((a, b) => {
                            const lowerCaseA = a.title.split("-").at(-1);
                            const lowerCaseB = b.title.split("-").at(-1);
                            return lowerCaseA.localeCompare(lowerCaseB, "ZH-CN", {
                                numeric: true
                            });
                        })
                    );
                }
            }
        }
    );

    const createTree = neType => {
        let treeData = filterTree;
        let defaultSelected;
        if (neType === "2") {
            const items = tableData.map(item => ({
                key: `port-1-${item.ramanChannelIndex}`,
                title: `PORT-1-${item.ramanChannelIndex}`
            }));
            treeData = [
                {
                    key: "chassis",
                    title: neNameMap[selectNe],
                    children: items
                }
            ];
            defaultSelected = "port-1-1";
        }
        return treeData.length > 0 ? (
            <ResourceTree
                treeData={treeData}
                ne_id={selectNe}
                updateSelectedTreeNode={updateSelectedTreeNode}
                defaultSelected={defaultSelected}
            />
        ) : (
            ""
        );
    };

    const createView = (type, neType) => {
        let cardType;
        if (neType === "2") {
            cardType = tableData?.[0]?.ramanChannelWorkMode === "1" ? "RFA" : "CORFA";
        } else {
            cardType = selectCard[`${type}_cardType`]?.split?.("-")?.[0];
        }
        if (cardType) {
            if (cardType.startsWith("TFF")) {
                cardType = "TFF";
            }
            return <Image style={{padding: 20}} src={DeviceImageMap[cardType]} height={350} preview={false} />;
        }
        return <a>Error Card Type</a>;
    };

    const filterPowerManagementData = data => {
        if (selectCardRef.current?.power) {
            const allData = data ?? tableData;
            if (selectPortRef.current) {
                setFilterTableData(
                    allData
                        .filter(
                            i =>
                                i["card-name"] === selectCardRef.current.power &&
                                i["port-name"] === selectPortRef.current
                        )
                        .map((v, i) => ({...v, index: i + 1}))
                );
            } else {
                setFilterTableData(
                    allData
                        .filter(i => i["card-name"] === selectCardRef.current.power)
                        .map((v, i) => ({...v, index: i + 1}))
                );
            }
        } else {
            setFilterTableData(null);
        }
    };

    const execFilterTableData = data => {
        if (loading) {
            return;
        }
        if (!selectedTreeNodeRef.current) {
            setFilterTableData(null);
            setPortFilterData(null);
            setTtiFilterData(null);
            setLldpFilterData(null);
            return;
        }
        const allData = data ?? tableData;
        if (tabType === "power") {
            //
        } else if (selectCard?.[tabType]) {
            if (tabType === "edfa") {
                if (!activeTab || activeTab === "edfa") {
                    //
                } else if (activeTab === "osc") {
                    //
                }
            } else if (tabType === "wss") {
                if (!activeTab || activeTab === "edfa") {
                    //
                } else if (activeTab === "osc") {
                    //
                } else {
                    //
                }
            } else if (tabType === "tff") {
                //
            } else if (tabType === "linecard") {
                if (!activeTab || activeTab === "port" || activeTab === "otn-tti" || activeTab === "power") {
                    let notFilter = false;
                    if (!selectedTreeNodeRef.current.toString().startsWith("PORT")) {
                        notFilter = true;
                    }
                    if (!activeTab || activeTab === "port") {
                        if (notFilter) {
                            setPortFilterData(null);
                            return;
                        }
                        setPortFilterData(portData.filter(i => i.port === selectedTreeNodeRef.current));
                    } else if (activeTab === "power") {
                        if (notFilter) {
                            setFilterTableData(null);
                            return;
                        }
                        updateFilterTableData(allData.filter(i => i["port-name"] === selectedTreeNodeRef.current));
                    } else if (activeTab === "otn-tti") {
                        if (notFilter) {
                            setTtiFilterData(null);
                            return;
                        }
                        setTtiFilterData(ttiData.filter(i => i.port === selectedTreeNodeRef.current));
                    }
                } else if (activeTab === "lldp") {
                    setLldpFilterData([
                        lldpData?.[0],
                        lldpData?.[1]?.filter?.(
                            i =>
                                i.name.indexOf(
                                    selectedTreeNodeRef.current
                                        .toString()
                                        .substring(selectedTreeNodeRef.current.toString().indexOf("-"))
                                ) > -1
                        )
                    ]);
                }
            }
        }
    };

    const getServiceDataForNe2 = async () => {
        const rs = await NEGet({
            ne_id: selectNeRef.current,
            parameter: {ramanChannelEntry: {}}
        });
        setTableData(
            rs.apiResult === "fail"
                ? []
                : rs.ramanChannelEntry.map((item, index) => ({...item, name: `PORT-1-${index + 1}`}))
        );
    };

    useEffect(() => {
        updateSelectedTreeNode(null);
        loadData(true).then();
        if (ampconOTNDeviceList.some(device => device.ip === selectNe)) {
            setSelectCard(null);
            setSelectPort(null);
        }
    }, [selectNe]);

    useEffect(() => {
        updateSelectedTreeNode(null);
        if (tabType === "power") {
            if (loading) {
                return;
            }
            filterPowerManagementData();
        } else {
            loadData(true).then();
        }
    }, [selectCard]);

    useEffect(() => {
        if (loading) {
            return;
        }
        filterPowerManagementData();
    }, [selectPort]);

    useEffect(() => {
        loadData(true).then();
    }, [activeTab]);

    useEffect(() => {
        execFilterTableData();
    }, [selectedTreeNode]);

    useEffect(() => {
        if (dataChanged?.data?.ne_id === selectNe && dataChanged?.data?.type === "components" && !updateSignalType) {
            loadData(false).then();
        }
    }, [dataChanged]);

    useEffect(() => {
        getServiceData().then();
        const neType = neOptions.find(i => i.value === selectNeRef.current)?.type;
        if (neType === "2") {
            getServiceDataForNe2().then();
        }
    }, [selectNe]);

    useEffect(() => {
        if (drawerTreeData.length > 0) {
            setFilterTree([]);
            const treeData = drawerTreeData.find(f => f.id === selectCard?.[tabType]);
            if (treeData) {
                setTimeout(() => {
                    setFilterTree([treeData]);
                }, 100);
            }
        }
    }, [selectCard]);
    const neType = neOptions.find(i => i.value === selectNeRef.current)?.type;
    return (
        <div
            className={classNames([styles.container, tabType === "linecard" ? "service_layer1" : ""])}
            style={tabType === "linecard" ? {padding: "32px 24px 24px", borderRadius: 5} : {paddingBottom: 20}}
        >
            {getFilterPanel(tabType)}
            {tabType === "power" &&
                (ampconOTNDeviceList.some(device => device.ip === selectNe) ? (
                    <DynamicTable
                        data={otnDevicePorts}
                        tabType={tabType}
                        CardId={selectCard?.value}
                        CardName={selectCard?.label}
                        NeIP={selectNe}
                        NeName={(ampconOTNDeviceList.find(device => device.ip === selectNe) || {})?.name}
                        PortName={selectPort}
                    />
                ) : (
                    <CustomTable
                        type="power_manager"
                        initDataSource={filterTableData ?? tableData}
                        loading={loading}
                        scroll={false}
                        refreshParent={() => {
                            loadData(true, false).then();
                        }}
                        initRowOperation={operationConfig.power}
                    />
                ))}

            {tabType === "edfa" && ampconOTNDeviceList.some(device => device.ip === selectNe) && selectCard && (
                <Layer0Config
                    tabType={tabType}
                    NeIp={selectNe}
                    CardId={selectCard?.value}
                    CardName={selectCard?.label}
                />
            )}

            {tabType === "oeo" && ampconOTNDeviceList.some(device => device.ip === selectNe) && selectCard && (
                <Layer0Config
                    tabType={tabType}
                    NeIp={selectNe}
                    CardId={selectCard?.value}
                    CardName={selectCard?.label}
                />
            )}

            {tabType === "linecard" && ampconOTNDeviceList.some(device => device.ip === selectNe) && selectCard && (
                <Layer1Config
                    tabType={tabType}
                    NeIp={selectNe}
                    CardId={selectCard?.value}
                    CardName={selectCard?.label}
                    NeName={(ampconOTNDeviceList.find(device => device.ip === selectNe) || {})?.name}
                />
            )}

            {tabType !== "power" && (selectCard?.[tabType] || neType === "2") && (
                <div className={styles.container}>
                    <div className={styles.up}>
                        <div className={styles.tree} style={{borderRadius: 5}}>
                            {createTree(neType)}
                        </div>
                        <div className={styles.view} style={{borderRadius: 5}}>
                            {createView(tabType, neType)}
                        </div>
                    </div>
                    <div className={styles.down} style={{borderRadius: 5}}>
                        <Tabs
                            style={{overflow: "hidden"}}
                            destroyInactiveTabPane
                            onTabClick={tab => {
                                setActiveTab(tab);
                                activeTabRef.current = tab;
                            }}
                            items={dynamicTableConfig[neType === "2" ? "RAMAN_BOX" : tabType].map(i =>
                                createDynamicTable(i)
                            )}
                        />
                    </div>
                </div>
            )}
        </div>
    );
};

export default ServiceCommon;

const seperator = "___";

const generateComponentTree = data => {
    const allComponents = {};
    convertToArray(data).forEach(component => {
        const entity_type = getAttrValue(component.state, "type");
        if (entity_type) {
            allComponents[component.name] = {
                parent: component.state?.parent,
                treeData: {
                    id: component.name,
                    value: {
                        title: component.name
                    },
                    key: `${component.name}`,
                    title: component.name,
                    objectType: "component",
                    entityType: removeNS(entity_type),
                    preconfType: getAttrValue(component.state, "vendor-type-preconf"),
                    location: getAttrValue(component.state, "location"),
                    children: []
                }
            };
        }
    });
    return allComponents;
};

const generateInterfaceTree = (interfaces, componentTree) => {
    const allInterface = {};
    convertToArray(interfaces)?.forEach(interfaceObj => {
        const parentName =
            getAttrValue(interfaceObj.state, "transceiver") ?? getAttrValue(interfaceObj.state, "hardware-port");
        if (parentName && componentTree[parentName]) {
            const entity_type = getAttrValue(interfaceObj.state, "type");
            if (entity_type && typeof entity_type === "string") {
                const treeData = {
                    id: interfaceObj.name,
                    value: {
                        title: interfaceObj.name
                    },
                    title: interfaceObj.name,
                    key: `interface${seperator}${removeNS(entity_type)}${seperator}${interfaceObj.name}`,
                    objectType: "interface",
                    entityType: removeNS(entity_type),
                    children: []
                };
                allInterface[interfaceObj.name] = treeData;
                componentTree[parentName].treeData.children.push(treeData);

                // add addresses and neighbors
                const ipv4Addresses = interfaceObj.subinterfaces?.subinterface?.ipv4?.addresses?.address;
                if (ipv4Addresses) {
                    convertToArray(ipv4Addresses).forEach(address => {
                        if (address.ip) {
                            treeData.children.push({
                                id: address.ip,
                                value: {
                                    title: address.ip
                                },
                                title: address.ip,
                                key: `ipv4-address${seperator}address${seperator}${address.ip}`,
                                objectType: "ipv4-address",
                                parent: interfaceObj.name,
                                entityType: "address",
                                children: []
                            });
                        }
                    });
                }
                const ipv6Addresses = interfaceObj.subinterfaces?.subinterface?.ipv6?.addresses?.address;
                if (ipv6Addresses) {
                    convertToArray(ipv6Addresses).forEach(address => {
                        if (address.ip) {
                            treeData.children.push({
                                id: address.ip,
                                value: {
                                    title: address.ip
                                },
                                title: address.ip,
                                key: `ipv4-address${seperator}address${seperator}${address.ip}`,
                                objectType: "ipv4-address",
                                parent: interfaceObj.name,
                                entityType: "address",
                                children: []
                            });
                        }
                    });
                }
                const ipv4Neighbors = interfaceObj.subinterfaces?.subinterface?.ipv4?.neighbors?.neighbor;
                if (ipv4Neighbors) {
                    convertToArray(ipv4Neighbors).forEach(neighbor => {
                        treeData.children.push({
                            id: neighbor.ip,
                            value: {
                                title: neighbor.ip
                            },
                            title: neighbor.ip,
                            key: `ipv4-neighbor${seperator}neighbor${seperator}${neighbor.ip}`,
                            objectType: "ipv4-neighbor",
                            parent: interfaceObj.name,
                            entityType: "neighbor",
                            children: []
                        });
                    });
                }
                const ipv6Neighbors = interfaceObj.subinterfaces?.subinterface?.ipv6?.neighbors?.neighbor;
                if (ipv6Neighbors) {
                    convertToArray(ipv6Neighbors).forEach(neighbor => {
                        treeData.children.push({
                            id: neighbor.ip,
                            value: {
                                title: neighbor.ip
                            },
                            title: neighbor.ip,
                            key: `ipv4-neighbor${seperator}neighbor${seperator}${neighbor.ip}`,
                            objectType: "ipv4-neighbor",
                            parent: interfaceObj.name,
                            entityType: "neighbor",
                            children: []
                        });
                    });
                }
            }
        }
    });
    return allInterface;
};
