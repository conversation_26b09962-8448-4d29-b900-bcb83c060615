import React, {useEffect, useRef, useState} from "react";
import * as echarts from "echarts";
import {Checkbox, message} from "antd";
import {isEmpty} from "lodash";
import {useRequest} from "ahooks";
import {useSelector} from "react-redux";
import useUserRight from "@/modules-otn/hooks/useUserRight";
import Icon from "@ant-design/icons";
import {CustomTable} from "@/modules-otn/components/common/custom_table";
import {openDBModalCreate} from "@/modules-otn/components/form/create_form_db";
import {netconfByXML, netconfChange, netconfGetByXML} from "@/modules-otn/apis/api";
import ProcessTool from "@/modules-otn/components/common/process_tool";
import {gLabelList} from "@/store/modules/otn/languageOTNSlice";
import {testConfigSvg, SpectrogramSvg} from "@/modules-otn/utils/iconSvg";

const OCM = () => {
    const {labelList} = useSelector(state => state.languageOTN);
    const [selectNE, setSelectNE] = useState({});
    const {neNameMap} = useSelector(state => state.neName);
    const [auto, setAuto] = useState(false);
    const [data, setData] = useState([]);
    const [dataList, setDataList] = useState([]);
    const [loading, setLoading] = useState(false);
    const [showType, setShowType] = useState(0);
    const [chart, setChart] = useState();
    const userRight = useUserRight();
    const chartRef = useRef();
    const [process, setProcess] = useState({percent: -1});
    const {runAsync, cancel, run} = useRequest(
        async filter => {
            await loadData(filter);
        },
        {
            manual: true,
            pollingErrorRetryCount: 3,
            pollingInterval: 1000,
            pollingWhenHidden: false,
            loadingDelay: 500
        }
    );

    const loadData = async obj => {
        try {
            const filter = obj ?? selectNE;
            if (!filter || !filter.ne_id) {
                return;
            }
            const _filter = {...filter};
            delete _filter["active-local-port"];
            delete _filter["channel-interval"];
            netconfGetByXML({
                ne_id: _filter.ne_id,
                msg: true,
                xml: {
                    "channel-monitors": {
                        $: {
                            xmlns: "http://openconfig.net/yang/channel-monitor"
                        },
                        "channel-monitor": {
                            name: _filter.name,
                            channels: {
                                channel: {}
                            }
                        }
                    }
                }
            })
                .then(rs => {
                    let _data = rs["channel-monitors"]["channel-monitor"].channels.channel.map(item => {
                        return item.state;
                    });
                    _data.sort((a, b) => {
                        return parseInt(a["lower-frequency"]) < parseInt(b["lower-frequency"]) ? -1 : 1;
                    });
                    _data = _data.map((item, index) => ({
                        ...item,
                        "lower-frequency":(item?.["lower-frequency"]/1000000).toFixed(6),
                        "upper-frequency":(item?.["upper-frequency"]/1000000).toFixed(6),
                        channel: filter?.["channel-interval"] === "CHANNEL_50G"
                            ? ( index< 48  ? `C${13 + index}` : `H${index - 35}`)
                            : (filter?.["channel-interval"] === "CHANNEL_75G"?`CM${index+1}`:`C${index+14}`)
                    }));
                    if (showType === 0) {
                        setData(_data);
                    } else {
                        chart.setOption(getChartData(_data));
                    }
                    setDataList(_data);
                })
                .catch(e => {
                    // eslint-disable-next-line no-console
                    console.log(e.message);
                });
        } catch (e) {
            // console.log(e);
        }
    };

    const getChartData = data => {
        try {
            const x = [];
            const y = [];
            let max = data?.[0]?.power ?? 0;
            let min = data?.[0]?.power ?? 0;
            data.map(item => {
                x.push(parseInt(item["lower-frequency"]) + (item["upper-frequency"] - item["lower-frequency"]) / 2);
                const yValue = parseFloat(item.power);
                y.push(yValue);
                if (max < yValue) {
                    max = yValue;
                }
                if (min > yValue) {
                    min = yValue;
                }
            });

            const option = {
                tooltip: {
                    trigger: "axis",
                    enterable: true,
                    hideDelay: 200, // 浮层隐藏的延迟
                    confine: true,
                    formatter(params) {
                        let tooltipContent = "";
                        tooltipContent += `${gLabelList.frequency}: ${params[0].name}MHz<br/>`;
                        tooltipContent += `${gLabelList.power}: ${params[0].data}dB<br/>`;
                        return tooltipContent;
                    }
                },
                toolbox: {
                    feature: {
                        dataZoom: {
                            yAxisIndex: "none",
                            emphasis: {
                                iconStyle: {
                                    borderColor: "#14c9bb"
                                }
                            }
                        },
                        saveAsImage: {
                            emphasis: {
                                iconStyle: {
                                    borderColor: "#14c9bb"
                                }
                            }
                        }
                    }
                },
                xAxis: {
                    type: "category",
                    data: x
                },
                yAxis: {
                    type: "value",
                    min: Math.floor(min),
                    max: Math.ceil(max)
                },
                series: [
                    {
                        data: y,
                        type: "line",
                        symbol: "none",
                        smooth: true
                    }
                ]
            };
            return option;
        } catch (e) {
            // console.log(e);
        }
    };

    const saveFilter = values => {
        const filter = {
            ne_id: values.ne,
            name: values.name,
            "active-local-port": values["active-local-port"],
            "channel-interval": values["channel-interval"]
        };
        setSelectNE(filter);
        runAsync(filter).then();
        if (!auto) {
            cancel();
        }
    };

    useEffect(() => {
        if (auto) {
            run();
        } else {
            cancel();
        }
    }, [auto]);

    useEffect(() => {
        try {
            if (showType === 0) {
                if (chart) {
                    chart.dispose();
                    setChart(null);
                }
                setData(data);
            } else if (!chart) {
                const c = echarts.init(chartRef.current);
                c.setOption(getChartData(data));
                window.onresize = () => {
                    c.resize();
                };
                setChart(c);
            }
            if (dataList.length > 0) setData(dataList);
        } catch (e) {
            // console.log(e);
        }
    }, [showType]);

    const divContent = {1: <div style={{flex: 1}} ref={chartRef} />};
    const refreshDisabled = !(
        neNameMap[selectNE.ne_id] &&
        selectNE.name &&
        selectNE["active-local-port"] &&
        selectNE["channel-interval"]
    );

    return (
        <div style={{display: "flex", flexDirection: "column", height: "auto", flex: 1}}>
            <div style={{margin: "8px 0 40px 0"}}>
                {gLabelList.auto_refresh}
                <Checkbox
                    key="auto_refresh"
                    style={{margin: "0 31px 0 31px"}}
                    onChange={event => {
                        setAuto(event.target.checked);
                    }}
                    disabled={loading}
                />
            </div>
            <CustomTable
                type="ocm"
                initTitle=""
                initDataSource={data}
                refreshParent={() => {
                    if (isEmpty(selectNE)) {
                        message.error(labelList.select_card_before);
                        return;
                    }
                    loadData().then();
                }}
                showType={showType}
                divContent={divContent}
                scroll={false}
                buttons={[
                    {
                        label: "test_config",
                        disabled: loading || userRight.disabled,
                        type: "primary",
                        icon: <Icon component={testConfigSvg} />,
                        onClick() {
                            openDBModalCreate({
                                type: "test_ocm",
                                initData: {
                                    "power-offset-select": {
                                        options: [
                                            {
                                                label: "1% Split Ratio",
                                                value: "20.00"
                                            },
                                            {
                                                label: "0.5% Split Ratio",
                                                value: "23.00"
                                            },
                                            {
                                                label: "Customize",
                                                value: "-customize-"
                                            }
                                        ]
                                    }
                                },
                                submit: async (values, defaultValue, diffValue, cancel, fail) => {
                                    const change = {...diffValue};
                                    const powerOffset = change["power-offset"];
                                    if (
                                        change["power-offset-select"] === "-customize-" &&
                                        (powerOffset === undefined || powerOffset === null || powerOffset === "")
                                    ) {
                                        message.error(labelList["power-offset-not-empty"]);
                                        fail?.({});
                                        return;
                                    }
                                    if (change["power-offset-select"] && !change["power-offset"]) {
                                        change["power-offset"] = change["power-offset-select"];
                                    }
                                    delete change.ne;
                                    delete change.card;
                                    delete change.name;
                                    delete change["power-offset-select"];
                                    if (Object.keys(change).length > 0) {
                                        setLoading(true);
                                        if (change["power-offset"] !== undefined && change["power-offset"] !== null) {
                                            const rs = await netconfByXML({
                                                ne_id: values.ne,
                                                msg: false,
                                                xml: {
                                                    components: {
                                                        $: {xmlns: "http://openconfig.net/yang/platform"},
                                                        component: {
                                                            name: values["active-local-port"],
                                                            port: {
                                                                "optical-port": {
                                                                    $: {
                                                                        xmlns: "http://openconfig.net/yang/transport-line-common"
                                                                    },
                                                                    config: {
                                                                        "power-offset":
                                                                            change["power-offset"].toString()
                                                                    }
                                                                }
                                                            }
                                                        }
                                                    }
                                                }
                                            });
                                            if (rs.apiResult === "fail") {
                                                message.error(JSON.stringify(rs.apiMessage));
                                                return;
                                            }
                                            delete change["power-offset"];
                                            if (Object.keys(change).length === 0) {
                                                message.success(gLabelList.save_success);
                                            }
                                        }
                                    }
                                    if (Object.keys(change).length > 0) {
                                        await netconfChange({
                                            ne_id: values.ne,
                                            operation: "edit",
                                            entity: "channel-monitor",
                                            keys: [values.name],
                                            values: {config: change},
                                            msg: false,
                                            success: rs => {
                                                cancel(rs);
                                                setProcess({
                                                    percent: 20,
                                                    nextPercent: 99,
                                                    title: labelList.ocm_testing
                                                });
                                                setTimeout(() => {
                                                    saveFilter(values);
                                                    setProcess({
                                                        percent: 100,
                                                        nextPercent: 100,
                                                        title: labelList.test_done
                                                    });
                                                    setLoading(false);
                                                }, 10000);
                                            },
                                            fail: rs => {
                                                message.error(labelList.test_failed).then();
                                                fail?.(rs);
                                                setLoading(false);
                                            }
                                        });
                                    } else {
                                        cancel({});
                                        setProcess({
                                            percent: 50,
                                            nextPercent: 99,
                                            title: (
                                                <div
                                                    style={{
                                                        display: "flex",
                                                        alignItems: "center", // 垂直居中
                                                        justifyContent: "center", // 水平居中（可选）
                                                        height: "100%", // 确保高度一致
                                                        textAlign: "center" // 文本居中对齐
                                                    }}
                                                >
                                                    {labelList.ocm_testing}
                                                </div>
                                            )
                                        });
                                        setTimeout(() => {
                                            setLoading(false);
                                            saveFilter(values);
                                            setProcess({
                                                percent: 100,
                                                nextPercent: 100,
                                                title: labelList.test_done
                                            });
                                        }, 2000);
                                    }
                                }
                            });
                        }
                    },
                    {
                        label: showType === 0 ? "spectrogram" : "data_table",
                        icon: <Icon component={SpectrogramSvg} />,
                        onClick() {
                            setShowType(showType === 0 ? 1 : 0);
                        }
                    }
                ]}
                refreshDisabled={refreshDisabled}
            />
            <ProcessTool processState={process} />
        </div>
    );
};
export default OCM;
