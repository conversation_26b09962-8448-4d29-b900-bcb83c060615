import {forwardRef, useImperativeHandle, useState} from "react";
import {<PERSON><PERSON>, DatePicker, Divider, Form, Modal} from "antd";
import {useForm} from "antd/es/form/Form";

const AimHistoryTimeSliderModal = forwardRef(({aimHistoryTimeSliderOKCallback}, ref) => {
    const title = "Choose the Historical Time";

    useImperativeHandle(ref, () => ({
        showAimHistoryTimeSliderModal: () => {
            setIsShowModal(true);
        },
        hideAimHistoryTimeSliderModal: () => {
            clearModal();
        }
    }));

    const [formRef] = useForm();

    const [isShowModal, setIsShowModal] = useState(false);
    const [selectedDate, setSelectedDate] = useState(null);

    const clearModal = () => {
        formRef.resetFields();
        setSelectedDate(null);
        setIsShowModal(false);
    };

    const disabledDate = current => {
        const now = new Date();
        const thirtyDaysAgo = new Date(now.getFullYear(), now.getMonth(), now.getDate() - 30);
        return current && (current > now || current < thirtyDaysAgo);
    };

    return isShowModal ? (
        <Modal
            className="ampcon-middle-modal"
            title={
                <div>
                    {title}
                    <Divider style={{marginTop: 8, marginBottom: 0}} />
                </div>
            }
            open={isShowModal}
            onCancel={() => {
                clearModal();
            }}
            footer={
                <div>
                    <Divider style={{marginTop: 0, marginBottom: 20}} />
                    <Button
                        onClick={() => {
                            clearModal();
                        }}
                    >
                        Cancel
                    </Button>
                    <Button
                        type="primary"
                        onClick={async () => {
                            await formRef.validateFields();
                            aimHistoryTimeSliderOKCallback(selectedDate.toDate());
                            clearModal();
                        }}
                    >
                        OK
                    </Button>
                </div>
            }
        >
            <Form
                layout="horizontal"
                labelAlign="left"
                labelCol={{span: 7}}
                wrapperCol={{span: 17}}
                form={formRef}
                style={{minHeight: "267.63px"}}
            >
                <Form.Item
                    name="date"
                    label="Select Historical Time"
                    rules={[{required: true, message: "Please select a time!"}]}
                >
                    <DatePicker
                        style={{width: "280px"}}
                        showTime={{format: "HH:mm"}}
                        format="YYYY-MM-DD HH:mm"
                        onChange={date => setSelectedDate(date)}
                        disabledDate={disabledDate}
                    />
                </Form.Item>
            </Form>
        </Modal>
    ) : null;
});

export default AimHistoryTimeSliderModal;
