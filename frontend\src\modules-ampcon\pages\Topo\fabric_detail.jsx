/* eslint-disable */
import React, { useRef, useEffect, useState } from "react";
import { useNavigate, useLocation } from 'react-router-dom';
import {
    Divider, Space, Card, message,
    Form, Input, Radio, Select,
    Button, InputNumber,
} from "antd";
import {
    PlusOutlined, LineOutlined, ArrowLeftOutlined
} from "@ant-design/icons";
import Icon from "@ant-design/icons";
import { deleteGreySvg, backUpGraySvg } from "@/utils/common/iconSvg";
import styles from "@/modules-ampcon/pages/Topo/fabrim_form.module.scss";
import { addFabricInfo, viewFabricInfo } from "@/modules-ampcon/apis/fabric_api";
import { fetchUnitListInfo } from "@/modules-ampcon/apis/unit_api";
import { getResourcePoolDropdownList } from "@/modules-ampcon/apis/resource_pool_api";
import FabricTopo from "@/modules-ampcon/pages/Topo/fabric_topo";

import CreatePoolModal from "@/modules-ampcon/pages/Resource/Pool/IpPool/creat_pool_modal";
import CreateASNPoolModal from "@/modules-ampcon/pages/Resource/Pool/AsnPool/create_pool_modal";
import CreateAreaPoolModal from "@/modules-ampcon/pages/Resource/Pool/AreaPool/create_area_pool_modal";


const FabricAdd = () => {
    const navigate = useNavigate();
    const { state } = useLocation(); // 访问整个 state  
    const fabricFormData = state?.data; // 安全提取 record，默认值为 undefined
    const [fabricTitle, setFabricTitle] = useState(state?.actionType)
    const [editDisabled, setEditDisabled] = useState(false)

    const [routingProtocalList, setRoutingProtocalList] = useState(['BGP', 'OSPF'])
    const [form] = Form.useForm();  // 创建 Form 实例

    const [type, setStageValue] = useState('3-stage');
    const [name, setFabricName] = useState('');
    const [description, setDescription] = useState('');
    const [super_spine_count, setSuperSpineCount] = useState('')
    const [spine_count, setSpinedCount] = useState("");
    const [unit, setUnits] = useState([{ id: "", name: "", unit_info: {} }]);

    const [pod, setPods] = useState([{
        name: "",
        spine_count: '',
        link_pre_superspine_count: 1,
        unit: [{ id: '', name: "", unit_info: {} }],
    },]);

    const [underlay_routing_protocol, setUnderlayRoutingProtocal] = useState('BGP')
    const [super_spine_areaid, setSuperSpineAreaID] = useState(0)
    const [pods_areaid, setPodsAreaid] = useState('')
    const [underlay_ebgp_asn, setUnderlayEbgpAsn] = useState('')
    const [bgp_router_id, setBgpRouterId] = useState('')
    const [vtep_interface, setVtepInterface] = useState("")

    const [unitDropdownList, setUnitDropdownList] = useState([])
    const [resourcePoolDropdownList, setResourcePoolDropdownList] = useState({})

    const createPoolModalRef = useRef(null);
    const createASNPoolModalRef = useRef(null);
    const createAreaPoolModalRef = useRef(null);

    // 使用传递的数据设置表单值  
    React.useEffect(() => {
        const fetchData = async () => {
            if (fabricTitle === 'View') { await getFabricInfo(); setEditDisabled(true); }
            if (fabricTitle === 'Edit') { await getFabricInfo(); setEditDisabled(false); }
            await getUnitList();
            await getResourcePoolList();
        };
        fetchData();
    }, []);

    const getFabricInfo = async () => {
        let result = await viewFabricInfo({ template_id: fabricFormData.id });
        if (result.status === 200) {
            const { template_info, ...rest } = result.data;
            const updatedData = { ...rest, ...template_info };
            form.setFieldsValue(updatedData);
            setStageValue(result.data.type)
            if (result.data.template_info.unit && result.data.template_info.unit.length > 0) {
                setUnits(result.data.template_info.unit);
            }
            if (result.data.template_info.pod && result.data.template_info.pod.length > 0) {
                setPods(result.data.template_info.pod);
            }
        }
    };
    const getResourcePoolList = async () => {
        let result = await getResourcePoolDropdownList({ poolTypeList: ["asn", "area", "ipv4",] })
        if (result.status === 200) {
            setResourcePoolDropdownList(result.data)
        }
    }

    const getUnitList = async () => {
        let result = await fetchUnitListInfo()
        if (result.status === 200) {
            setUnitDropdownList(result.data)
        }
    }

    // 更新对应索引的 Unit 值  
    const handleUnitChange = (index, value) => {
        const unitListCheckValue = unitDropdownList.find(item => item.id === value);
        const newUnits = [...unit];
        newUnits[index] = { id: value, name: unitListCheckValue.name, unit_info: unitListCheckValue.unit_info }
        setUnits(newUnits);
    };
    // 删除指定索引的 unit  
    const removeUnit = (index) => {
        if (unit.length > 1) {
            const newUnits = unit.filter((_, i) => i !== index);
            setUnits(newUnits);
        }
    };

    // 新增一条新的 Pod  
    const addNewPod = () => {
        const newPod = {
            name: "pod " + parseInt(pod.length + 1),
            spine_count: "",
            link_pre_superspine_count: "",
            unit: [{ id: "", name: "", unit_info: {} }]
        };
        setPods([...pod, newPod]);
    }
    // 克隆一条新的 Pod  
    const clonePod = (index) => {
        const currentPod = pod[index];
        const newPod = {
            ...currentPod,
            name: 'pod' + parseInt(pod.length + 1),
            unit: [...currentPod.unit],
        };
        setPods([...pod, newPod]);
    };
    //删除指定 pod 中的 unit，并确保至少有一个 Pod  
    const removePod = (index) => {
        if (pod.length > 1) {
            const newPods = pod.filter((_, i) => i !== index);
            setPods(newPods);
        }
    };
    // 更新指定 Pod 的 unit 值  
    const handlePodUnitChange = (podIndex, unitIndex, value) => {
        const newPods = [...pod];
        newPods[podIndex].name = 'pod' + parseInt(podIndex + 1);
        const unitListCheckValue = unitDropdownList.find(item => item.id === value);
        newPods[podIndex].unit[unitIndex] = { id: value, name: unitListCheckValue.name, unit_info: unitListCheckValue.unit_info }
        setPods(newPods);
    };
    // 向指定 Pod 中添加新的 unit 输入  
    const addPodUnit = (podIndex, unitIndex) => {
        const newPods = [...pod];
        newPods[podIndex].unit.push({ id: '', name: '', unit_info: {} });
        setPods(newPods);
    };
    // 删除指定 pod 中的 unit  
    const removePodUnit = (podIndex, unitIndex) => {
        const newPods = [...pod];
        newPods[podIndex].unit.splice(unitIndex, 1);
        setPods(newPods);
    };
    // 点击Save按钮，提交表单
    const handleFormSubmit = async values => {
        let formData = {
            id: fabricTitle === 'Edit' ? fabricFormData.id : '',
            name: values.name,
            description: values.description,
            type: type,
            underlay_routing_protocol: values.underlay_routing_protocol || '',
            overlay_control_protocol: values.overlay_control_protocol || '',
            template_info: {
                spine_count: type === '3-stage' ? values.spine_count : '',
                super_spine_count: type === '5-stage' ? values.super_spine_count : '',
                unit: type === '3-stage' ? unit.filter(item => item.id !== "") : [],
                pod: type === '5-stage' ? pod.filter(item => item.name !== "") : [],
                underlay_ebgp_asn: underlay_routing_protocol === 'BGP' ? values.underlay_ebgp_asn : '',
                pods_areaid: underlay_routing_protocol === 'OSPF' ? values.pods_areaid : '',
                super_spine_areaid: underlay_routing_protocol === 'BGP' ? values.super_spine_areaid : '',
                bgp_router_id: values.bgp_router_id || '',
                vtep_interface: values.vtep_interface || '',
                overlay_ibgp_asn: values.overlay_ibgp_asn || '',
            }
        }

        let ret = await addFabricInfo(formData);

        if (ret.status === 200) {
            message.success(ret.info);
            navigate('/topo/fabric')
        } else {
            message.error(ret.info);
        }
    };

    return (
        <Card style={{ display: "flex", flex: 1, position: 'relative' }}>
            <p className={styles.goBack} onClick={() => { navigate(-1) }}>
                <ArrowLeftOutlined style={{ marginRight: '8px' }} />
                <span>Back</span>
            </p>

            <h2>{fabricTitle} Fabric </h2>
            <div style={{ overflowY: "scroll", height: " calc(100vh - 344px)" }}>
                <div className={styles.addFabricBox}>

                    <Form layout="horizontal" form={form} validateTrigger="onBlur" labelAlign="left"
                        onFinish={handleFormSubmit}
                        style={{ width: 505 }}  >

                        <h3>Basic Info</h3>
                        <Form.Item name="type" label="Stage" labelCol={{ style: { width: 175 } }}>
                            <Radio.Group value={type} defaultValue={type} onChange={(e) => { setStageValue(e.target.value) }} disabled={editDisabled}>
                                <Radio value="3-stage">3-stage</Radio>
                                <Radio value="5-stage">5-stage</Radio>
                            </Radio.Group>
                        </Form.Item>

                        <Form.Item name="name" label="Fabric Name" labelCol={{ style: { width: 175 } }}
                            rules={[
                                { required: true, message: "Please enter the  fabric name!" },
                                {
                                    validator: (_, value) => {
                                        if (!value || !/\s/.test(value)) { return Promise.resolve(); }
                                        return Promise.reject(new Error("Fabric name cannot contain spaces"));
                                    }
                                }
                            ]}>
                            <Input value={name} disabled={editDisabled} placeholder="Fabric Name" className={styles.formWidth} />
                        </Form.Item>

                        <Form.Item name="description" label="Description" labelCol={{ style: { width: 175 } }}
                            rules={[
                                {
                                    validator: (_, value) => {
                                        if (!value || /^[^\u4e00-\u9fa5，。！？、]*$/.test(value)) {
                                            return Promise.resolve();
                                        }
                                        return Promise.reject(new Error("Please enter English, numbers, or English characters"));
                                    }
                                }
                            ]}>
                            <Input value={description} disabled={editDisabled} placeholder="Enter English, numbers, or English characters" className={styles.formWidth} />
                        </Form.Item>


                        {type == '5-stage' && (
                            <>
                                <h3>Superspine Layer</h3>
                                <Form.Item name="super_spine_count" label="Superspines Count" labelCol={{ style: { width: 175 } }}
                                    rules={[
                                        {
                                            validator: (_, value) => {
                                                if (!value || /^\d+$/.test(value)) {
                                                    return Promise.resolve();
                                                }
                                                return Promise.reject(new Error("Please entry  a number"));
                                            }
                                        }
                                    ]}
                                >
                                    <InputNumber value={super_spine_count} disabled={editDisabled} min={0} className={styles.formWidth} />
                                </Form.Item>
                            </>
                        )}

                        {type == '3-stage' && (
                            <>
                                <h3>Spine Layer</h3>
                                <Form.Item name="spine_count" label="Spines Count" labelCol={{ style: { width: 175 } }}
                                    rules={[
                                        {
                                            validator: (_, value) => {
                                                if (!value || /^\d+$/.test(value)) { return Promise.resolve(); }
                                                return Promise.reject(new Error("Please entry  a number"));
                                            }
                                        }
                                    ]}
                                >
                                    <InputNumber value={spine_count} disabled={editDisabled} min={0} className={styles.formWidth} />
                                </Form.Item>

                                <h3>Unit</h3>
                                {unit.map((unitItem, index) => (
                                    <Form.Item label="Unit" key={index} labelCol={{ style: { width: 175 } }}
                                        rules={[{ required: true, message: "Please select a unit!" }]}
                                    >
                                        <Select value={unitItem.id} onChange={(e) => { handleUnitChange(index, e); }} disabled={editDisabled} style={{ width: 280 }}
                                            options={unitDropdownList?.map(item => ({
                                                value: item.id, label: item.name
                                            }))} />

                                        {!editDisabled && (
                                            <Space>
                                                {index === 0 ? (
                                                    <PlusOutlined onClick={() => { setUnits([...unit, { id: '', name: "", unit_info: {} }]) }} style={{ marginLeft: 8 }} />
                                                ) : (
                                                    <LineOutlined onClick={() => removeUnit(index)} style={{ marginLeft: 8 }} />
                                                )}
                                            </Space>
                                        )}

                                    </Form.Item>
                                ))}
                            </>
                        )}
                        {type == '5-stage' && (
                            <>
                                <h3>Pods</h3>
                                <div className={styles.podContainerBox}>
                                    {pod.map((podItem, podIndex) => (
                                        <div key={podIndex} className={styles.podContainer}>
                                            <h4>
                                                <span>pod {podIndex + 1}</span>
                                                {!editDisabled && (
                                                    <i>
                                                        <Icon component={backUpGraySvg} onClick={() => clonePod(podIndex)} style={{ marginLeft: 8, cursor: 'pointer' }} />
                                                        {podIndex > 0 && (
                                                            <Icon component={deleteGreySvg} onClick={() => removePod(podIndex)} style={{ marginLeft: 8, cursor: 'pointer' }} />
                                                        )}
                                                    </i>
                                                )}
                                            </h4>
                                            <Form.Item label="Spines Count" labelCol={{ style: { width: 150 } }}
                                                rules={[
                                                    {
                                                        validator: (_, value) => {
                                                            if (!value || /^\d+$/.test(value)) { return Promise.resolve(); }
                                                            return Promise.reject(new Error("Please enter a valid number"));
                                                        }
                                                    }
                                                ]}
                                            >
                                                <InputNumber value={podItem.spine_count} disabled={editDisabled} className={styles.formWidth}
                                                    onChange={(value) => {
                                                        const newPods = [...pod];
                                                        newPods[podIndex].name = 'pod' + (podIndex + 1)
                                                        newPods[podIndex].spine_count = value;
                                                        setPods(newPods);
                                                    }} />
                                            </Form.Item>

                                            {podItem.unit.map((unit, unitIndex) => (
                                                <Form.Item label="Unit" key={unitIndex} labelCol={{ style: { width: 150 } }}
                                                    rules={[{ required: true, message: "Please select a unit!" }]}
                                                >
                                                    <Select value={unit.id} onChange={(e) => { handlePodUnitChange(podIndex, unitIndex, e) }} disabled={editDisabled} style={{ width: 280 }}
                                                        options={unitDropdownList?.map(item => ({
                                                            value: item.id, label: item.name
                                                        }))} />

                                                    {!editDisabled && (
                                                        <Space>
                                                            {unitIndex === 0 ? (
                                                                <PlusOutlined onClick={() => { addPodUnit(podIndex, unitIndex) }} style={{ marginLeft: 8, cursor: 'pointer' }} />
                                                            ) : (
                                                                <LineOutlined onClick={() => removePodUnit(podIndex, unitIndex)} style={{ marginLeft: 8, cursor: 'pointer' }} />
                                                            )}
                                                        </Space>
                                                    )}
                                                </Form.Item>
                                            ))}
                                        </div>
                                    ))}
                                </div>
                                {!editDisabled && (
                                    <button type="button" onClick={() => { addNewPod() }} className={styles.addNewPodBtn}>
                                        <PlusOutlined style={{ marginRight: 8 }} />
                                        Add
                                    </button>
                                )}
                            </>
                        )}

                        <h3>Underlay</h3>
                        <Form.Item name="underlay_routing_protocol" label="Routing Protocal" labelCol={{ style: { width: 175 } }}>
                            <Select defaultValue={underlay_routing_protocol} onChange={(e) => { setUnderlayRoutingProtocal(e); }} disabled={editDisabled} allowClear
                                style={{ width: 280, }}
                                dropdownRender={(menu) => (
                                    <>
                                        {menu}
                                        <Divider style={{ margin: '8px 0' }} />
                                        <Button type="text" icon={<PlusOutlined display={routingProtocalList?.length >= 0} />}
                                            className={styles.selectCreateBtn}
                                            onClick={() => { createPoolModalRef.current.showCreatePoolModal(); }}>
                                            Go to Create
                                        </Button>
                                    </>
                                )}
                                options={routingProtocalList.map((item) => ({ label: item, value: item, }))}
                            />
                        </Form.Item>

                        {underlay_routing_protocol == 'OSPF' && (
                            <>
                                <Form.Item name="super_spine_areaid" label="Superspine Area ID" labelCol={{ style: { width: 175 } }}>
                                    <Select onChange={(e) => { setSuperSpineAreaID(e); }} disabled={editDisabled} allowClear style={{ width: 280, }}
                                        dropdownRender={(menu) => (
                                            <>
                                                {menu}
                                                <Divider style={{ margin: '8px 0', }} />
                                                <Button type="text" icon={<PlusOutlined display={resourcePoolDropdownList?.super_spine_areaid?.length >= 0} />}
                                                    className={styles.selectCreateBtn}
                                                    onClick={() => { createAreaPoolModalRef.current.showCreateAreaPoolModal(); }}>
                                                    Go to Create
                                                </Button>
                                            </>
                                        )}
                                        options={resourcePoolDropdownList?.area?.map(item => ({ value: item, label: item }))} />
                                </Form.Item>

                                <Form.Item name="pods_areaid" label="Pods Area ID" labelCol={{ style: { width: 175 } }}>
                                    <Select onChange={(e) => { setPodsAreaid(e); }} disabled={editDisabled} allowClear style={{ width: 280, }}
                                        dropdownRender={(menu) => (
                                            <>
                                                {menu}
                                                <Divider style={{ margin: '8px 0', }} />
                                                <Button type="text" icon={<PlusOutlined display={resourcePoolDropdownList?.pods_areaid?.length >= 0} />}
                                                    className={styles.selectCreateBtn}
                                                    onClick={() => { createAreaPoolModalRef.current.showCreateAreaPoolModal(); }}>
                                                    Go to Create
                                                </Button>
                                            </>
                                        )}
                                        options={resourcePoolDropdownList?.area?.map(item => ({ value: item, label: item }))} />
                                </Form.Item>
                            </>
                        )}
                        {underlay_routing_protocol == 'BGP' && (
                            <>
                                <Form.Item name="underlay_ebgp_asn" label="EBGP ASN" labelCol={{ style: { width: 175 } }}>
                                    <Select onChange={(e) => { setUnderlayEbgpAsn(e); }} disabled={editDisabled} allowClear style={{ width: 280, }}
                                        dropdownRender={(menu) => (
                                            <>
                                                {menu}
                                                <Divider style={{ margin: '8px 0', }} />
                                                <Button type="text" icon={<PlusOutlined display={resourcePoolDropdownList?.asn?.length >= 0} />}
                                                    className={styles.selectCreateBtn}
                                                    onClick={() => { createASNPoolModalRef.current.showCreatePoolModal(); }}>
                                                    Go to Create
                                                </Button>
                                            </>
                                        )}
                                        options={resourcePoolDropdownList?.asn?.map(item => ({ value: item.id, label: item.name }))}
                                    />
                                </Form.Item>
                            </>
                        )}

                        <Form.Item name="bgp_router_id" label="Router ID" labelCol={{ style: { width: 175 } }}>
                            <Select onChange={(e) => { setBgpRouterId(e); }} disabled={editDisabled} allowClear style={{ width: 280, }}
                                dropdownRender={(menu) => (
                                    <>
                                        {menu}
                                        <Divider style={{ margin: '8px 0', }} />
                                        <Button type="text" icon={<PlusOutlined display={resourcePoolDropdownList?.router_id?.length >= 0} />}
                                            className={styles.selectCreateBtn}
                                            onClick={() => { createPoolModalRef.current.showCreatePoolModal(); }}>
                                            Go to Create
                                        </Button>
                                    </>
                                )}
                                options={resourcePoolDropdownList?.router_id?.map(item => ({ value: item.id, label: item.name }))}
                            />
                        </Form.Item>

                        <Form.Item name="vtep_interface" label="VTEP Interface" labelCol={{ style: { width: 175 } }}>
                            <Select onChange={(e) => { setVtepInterface(e); }} disabled={editDisabled} allowClear style={{ width: 280, }}
                                dropdownRender={(menu) => (
                                    <>
                                        {menu}
                                        <Divider style={{ margin: '8px 0' }} />
                                        <Button type="text" icon={<PlusOutlined display={resourcePoolDropdownList?.vtep_interface?.length >= 0} />}
                                            className={styles.selectCreateBtn}
                                            onClick={() => { createPoolModalRef.current.showCreatePoolModal(); }}>
                                            Go to Create
                                        </Button>
                                    </>
                                )}
                                options={resourcePoolDropdownList?.vtep_interface?.map(item => ({ value: item.id, label: item.name }))}
                            />
                        </Form.Item>
                    </Form>

                    <div className={styles.topology}>
                        <h3>Topology</h3>
                        <div id="topology_container" style={{width: "100%", height: "100%"}}>
                            <FabricTopo unitNodes={unit} podNodes={pod} type={type} />
                        </div>
                    </div>

                </div>
            </div>

            <div className={styles.button_box}>
                <Button key="cancel" onClick={() => { navigate('/topo/fabric') }}> Cancel</Button>
                {editDisabled ? (
                    <Button key="Edit" type="primary" onClick={() => { setEditDisabled(false); setFabricTitle('Edit') }}>Edit </Button>
                ) : (
                    <Button key="Save" type="primary" onClick={form.submit}>Save </Button>
                )}
            </div>

            <CreatePoolModal ref={createPoolModalRef} saveCallback={() => { getResourcePoolList() }} />
            <CreateASNPoolModal ref={createASNPoolModalRef} saveCallback={() => { getResourcePoolList() }} />
            <CreateAreaPoolModal ref={createAreaPoolModalRef} saveCallback={() => { getResourcePoolList() }} />

        </Card >
    );
};

export default FabricAdd;