import React, {useState, useEffect, useCallback, useRef} from "react";
import {Card, Form, Tabs, DatePicker, Row, Col, Select, Flex, Button, Input, Checkbox, Tooltip} from "antd";
import {CustomSelect} from "@/modules-ampcon/components/custom_form";
import {fetchAllSwitch} from "@/modules-ampcon/apis/dashboard_api";
import {TelemetryChart} from "@/modules-ampcon/components/echarts_common";
import {fetchDLBTopK, fetchInterfaceList} from "@/modules-ampcon/apis/monitor_api";
import {AmpConCustomTreeSelect} from "@/modules-ampcon/components/custom_tree";
import {AmpConCustomTelemteryTable, createColumnConfig} from "@/modules-ampcon/components/custom_table";

import {getDlbTableAPI} from "@/modules-ampcon/apis/monitor_api";

const {RangePicker} = DatePicker;
const {Option} = Select;

const dlbCardOptions = {
    "out-bindwidth-utilization": "Bandwidth Utilization (Output)",
    "in-bindwidth-utilization": "Bandwidth Utilization (Input)",
    "out-packet-loss-rate": "Packet Loss Rate (Output)",
    "in-packet-loss-rate": "Packet Loss Rate (Input)",
    "out-throughput": "Throughput (Output)",
    "in-throughput": "Throughput (Input)",
};

const DLB = () => {
    const [switchSN, setSwitchSN] = useState("");
    const [switchSNList, setSwitchSNList] = useState([]);
    const [activeKey, setActiveKey] = useState("Performance Trend");

    const handleSelectChange = value => {
        setSwitchSN(value);
    };
    const items = [
        {
            key: "Performance Trend",
            label: "Performance Trend",
            children: <PerformanceTrend active={activeKey === "Performance Trend"} sn={switchSN} />
        },
        {
            key: "Real-time Statistics",
            label: "Real-time Statistics",
            children: <RealTimeStatistics active={activeKey === "Real-time Statistics"} sn={switchSN} />
        }
    ];

    const fetchSwitchList = async () => {
        let response = await fetchAllSwitch(1, 10, [], [], {});
        if (response.status === 200) {
            let allData = response.data;
            if (response.total > 10) {
                response = await fetchAllSwitch(1, response.total, [], [], {});
                if (response.status === 200) {
                    allData = response.data;
                }
            }
            const snList = allData.map(item => item.sn);
            setSwitchSNList(snList)
        }
    };

    useEffect(() => {
        fetchSwitchList().then();
    }, []);

    return (
        <Card style={{display: "flex", flex: 1}}>
            <h2 style={{marginTop: "8px", marginBottom: "20px"}}>DLB</h2>
            <Form layout="inline" style={{flexWrap: "nowrap", marginBottom: "30px"}}>
                <Form.Item
                    name="sn"
                    label="SN/Service Tag"
                    required="true"
                    wrapperCol={{style: {marginLeft: 20, width: 230}}}
                >
                    <CustomSelect onChange={handleSelectChange} options={switchSNList} />
                </Form.Item>
            </Form>
            <Tabs onChange={setActiveKey} className="sameLevelTabs" items={items} />
        </Card>
    );
};

const PerformanceTrend = ({active, sn}) => {

    const [timeRange, setTimeRange] = useState(["", ""]);
    const [interfaceList, setInterfaceList] = useState([]);

    useEffect(() => {
        fetchInterfaceList(sn).then(rs => {
            if (rs.status === 200) {
                if (rs.data.length > 0) {
                    const interfaceList ={
                        title: "All",
                        value: "all",
                        children: rs.data.map(item=> ({
                            title: item,
                            value: item
                        }))};
                    setInterfaceList([interfaceList]);
                }
            }
        })
    }, [sn]);

    return (
        <>
            <div>
                <div style={{textAlign: "right"}}>
                    Time
                    <RangePicker
                        showTime={{format: "HH:mm"}}
                        format="YYYY-MM-DD HH:mm"
                        style={{height: "32px", marginLeft: "32px"}}
                        onChange={(_, dateString) => {
                            setTimeRange(dateString);
                        }}
                        disabledDate={current => {
                            const now = new Date();
                            const oneMonthAgo = new Date();
                            oneMonthAgo.setMonth(now.getMonth() - 1);
                            return current && (current > now || current < oneMonthAgo);
                        }}
                    />
                </div>
            </div>
            <div style={{height: "100%", width: "100%", marginTop: "18px", marginBottom: "18px"}}>
                <Row gutter={[24, 24]}>
                    {Object.keys(dlbCardOptions).map((item, index) => (
                            <Col key={index} span={24} xxl={12} style={{display: "flex", justifyContent: "center"}}>
                                <DLBCard
                                    active={active}
                                    name={item}
                                    timeRange={timeRange}
                                    target={sn}
                                    interfaceList={interfaceList}
                                />
                            </Col>
                        ))}
                </Row>
            </div>
        </>
    );
};

export const DLBCard = ({active, name, timeRange, target, interfaceList}) => {
    const [chartData, setChartData] = useState([]);
    const [xAxisData, setXAxisData] = useState([]);
    const [topK, setTopK] = useState(5);
    const [selectedInterface, setSelectedInterface] = useState([]);
    const [filter, setFilter] = useState([]);
    const [useTopN, setUseTopN] = useState(false);
    const [xAxisInterval, setXAxisInterval] = useState(1);

    const onSelectChange = value => {
        if (value.length === 0) {
            setSelectedInterface([]);
            setFilter([]);
            setUseTopN(false);
            setTopK(5);
            return;
        }
        setSelectedInterface(value);
        setFilter(value);
    };

    const fetchData = useCallback(async () => {
        const finalTopK = useTopN && selectedInterface.length > 0 ? selectedInterface.length : topK;
        const response = await fetchDLBTopK(name, finalTopK, target, timeRange[0], timeRange[1], filter);
        if (response.status !== 200) {
            message.error(response.info);
            setChartData([]);
            setXAxisData([]);
        } else if (response.data.length > 0) {
            const series = response.data.map(item => {
                const name = `${item.interface_name}`;
                return {
                    name,
                    data: item.values.map(([x, y]) => [x, y])
                };
            });
            setChartData(series);

            const xAxisData = Array.from(
                new Set(response.data.flatMap(item => item.values.map(([x]) => x)))
            ).sort();

            if (timeRange[0] && timeRange[1]) {
                const totalPoints = xAxisData.length;
                const interval = Math.floor(totalPoints / 5);
                setXAxisInterval(interval);
            } else {
                setXAxisInterval(1);
            }

            setXAxisData(xAxisData);
        } else {
            setChartData([]);
            setXAxisData([]);
        }
    }, [name, timeRange, topK, useTopN, target, filter]);

    useEffect(() => {
        if (target && active) {
            fetchData();
        }
    }, [name, timeRange, topK, useTopN, selectedInterface, filter, target, active]);

    const getSelectValue = () => {
        if (selectedInterface.length === 0) {
            return `Top ${topK}`;
        }
        if (useTopN) {
            return `Total ${selectedInterface.length}`;
        }
        return `Top ${topK}`;
    };

    return (
        <Card
            title={
                <div
                    style={{display: "flex", justifyContent: "space-between", alignItems: "center"}}
                >
                    <span>{dlbCardOptions[name]}</span>
                    <div style={{display: "flex", gap: "10px"}}>
                        <AmpConCustomTreeSelect onChange={onSelectChange} treeData={interfaceList} />
                        <Select
                            style={{width: 120}}
                            value={getSelectValue()}
                            onChange={value => {
                                const isTopN = value === `Total ${selectedInterface.length}`;
                                setUseTopN(isTopN);
                                if (!isTopN) {
                                    const num = parseInt(value.split(" ")[1], 10);
                                    setTopK(num);
                                }
                            }}
                            defaultValue="Top 5"
                        >
                            <Option value="Top 5">Top 5</Option>
                            <Option value="Top 10">Top 10</Option>
                            <Option value="Top 25">Top 25</Option>
                            {selectedInterface.length > 0 && (
                                <Option value={`Total ${selectedInterface.length}`}>
                                    {`Total ${selectedInterface.length}`}
                                </Option>
                            )}
                        </Select> 
                    </div>
                </div>
            }
            bordered={false}
            style={{
                height: "350px",
                width: "100%",
            }}
            className="linechart"
        >
            <TelemetryChart chartData={chartData} xAxisData={xAxisData} xAxisInterval={xAxisInterval} timeRange={timeRange} />
        </Card>
    );
};

const RealTimeStatistics = ({active, sn}) => {
    const [formattedData, setFormattedData] = useState([]);

    const portColumns = [
        {
            ...createColumnConfig("Port Name", "port_name"),
            render: (_, record) => {
                return `${record.port_name}:${record.sn}`;
            }
        },
        {
            ...createColumnConfig("5 Sec Input Rate", "in_pkts_rate"),
            render: (_, record) => {
                return `${record.in_pkts_rate}:${record.sn}`;
            }
        },
        {
            ...createColumnConfig("5 Sec Output Rate", "out_pkts_rate"),
            render: (_, record) => {
                return `${record.out_pkts_rate}:${record.sn}`;
            }
        },
        createColumnConfig("input Total Packets Without Errors", "in_pkts"),
        createColumnConfig("input Total Packets With Errors", "in_errors"),
        createColumnConfig("output Total Packets Without Errors", "out_pkts"),
        createColumnConfig("output Total Packets With Errors", "out_errors")
    ];

    const fetchData = async () => {
        const response = await fetchInterfaceInfo(sn);
        if (response.status !== 200) {
            message.error(response.info);
        } else {
            const formattedData = Object.keys(response.data).map(key => {
                const port = response.data[key];
                // const port_speed =
                //     port?.ethernet_state?.negotiated_port_speed?.match(/SPEED_(.*)/)?.[1] ||
                //     port?.ethernet_state?.port_speed?.match(/SPEED_(.*)/)?.[1] ||
                //     "-";
                return {
                    name: port?.config?.name || "-",
                    in_pkts_rate: formatNumber(port?.ethernet_state?.in_pkts_rate),
                    out_pkts_rate: formatNumber(port?.ethernet_state?.out_pkts_rate),
                    in_pkts: formatNumber(port?.state?.counters?.in_pkts),
                    out_pkts: formatNumber(port?.state?.counters?.out_pkts),
                    in_errors: formatNumber(port?.state?.counters?.in_errors),
                    out_errors: formatNumber(port?.state?.counters?.out_errors)
                };
            });
            formattedData.sort(a => compareNumbers(a.name));
            setFormattedData(formattedData);
        }
    };

    useEffect(() => {
        if (active) {
            fetchData();
        }
    }, [active, sn]);

    return (
        <Flex gap={16} vertical>
            <AmpConCustomTelemteryTable
                columnsConfig={portColumns}
                data={formattedData}
                // tableWidth={tableWidth}
            />
        </Flex>
    );
};
export default DLB;
