import {But<PERSON>, Di<PERSON>r, Flex, Form, Input, message, Modal, Tag} from "antd";
import {forwardRef, useEffect, useImperativeHandle, useState} from "react";
import {updateTemplateTag} from "@/modules-ampcon/apis/automation_api";

const TemplateTagManagementModal = forwardRef((props, ref) => {
    useImperativeHandle(ref, () => ({
        showTemplateTagManagementModal: (templateId, templateName, tagContent) => {
            setTemplateId(templateId);
            syslogConfigForm.setFieldsValue({templateName});
            setTagContent(tagContent);
            setIsShowModal(true);
        },
        hideTemplateTagManagementModal: () => {}
    }));

    useEffect(() => {}, []);

    const title = "Tag Management";
    const templateNameLabel = "Template Name";
    const templateTagLabel = "Template Tag";
    const {saveCallback} = props;

    const [syslogConfigForm] = Form.useForm();

    const [isShowModal, setIsShowModal] = useState(false);
    const [tagContent, setTagContent] = useState("");
    const [inputTagName, setInputTagName] = useState("");
    const [templateId, setTemplateId] = useState(0);
    const [isTagNameValid, setIsTagNameValid] = useState(true);
    const [tagNameInvalidMessage, setTagNameInvalidMessage] = useState("");
    const [isInputTagNameInvalidBefore, setIsInputTagNameInvalidBefore] = useState(false);

    const isInputTagNameValid = (inputTagName, tagContent) => {
        if (inputTagName === "") {
            return true;
        }
        if (inputTagName.includes(",")) {
            setIsTagNameValid(false);
            setTagNameInvalidMessage("Tag Name cannot contain comma.");
            return false;
        }
        if (tagContent.split(",").includes(inputTagName)) {
            setIsTagNameValid(false);
            setTagNameInvalidMessage("Tag Name already exists.");
            return false;
        }
        if (inputTagName.length > 24) {
            setIsTagNameValid(false);
            setTagNameInvalidMessage("Tag Name over 24 character limit.");
            return false;
        }
        setIsTagNameValid(true);
        setTagNameInvalidMessage("");
        return true;
    };

    const handleAddTagButtonCallback = () => {
        let tagContentTemp = tagContent;

        if (!isInputTagNameValid(inputTagName, tagContent)) {
            setIsInputTagNameInvalidBefore(true);
            return tagContent;
        }
        setIsInputTagNameInvalidBefore(false);

        if (tagContentTemp !== "") {
            tagContentTemp += inputTagName ? `,${inputTagName}` : "";
        } else {
            tagContentTemp = inputTagName;
        }
        setTagContent(tagContentTemp);
        setInputTagName("");
        return tagContentTemp;
    };

    return isShowModal ? (
        <Modal
            className="ampcon-middle-modal"
            title={
                <div>
                    {title}
                    <Divider style={{marginTop: 8, marginBottom: 0}} />
                </div>
            }
            open={isShowModal}
            onOk={() => {}}
            onCancel={() => {
                setIsShowModal(false);
                setTagContent("");
                setInputTagName("");
                setIsTagNameValid(true);
                setTagNameInvalidMessage("");
                setIsInputTagNameInvalidBefore(false);
            }}
            footer={
                <Flex vertical>
                    <Divider style={{marginTop: 0, marginBottom: 20}} />
                    <Flex justify="flex-end">
                        <Button
                            onClick={() => {
                                setIsShowModal(false);
                            }}
                        >
                            Cancel
                        </Button>
                        <Button
                            type="primary"
                            onClick={() => {
                                updateTemplateTag(templateId, handleAddTagButtonCallback()).then(response => {
                                    if (response.status !== 200) {
                                        message.error(response.info);
                                    } else {
                                        message.success(response.info);
                                        setIsShowModal(false);
                                        if (saveCallback) {
                                            saveCallback();
                                        }
                                    }
                                });
                            }}
                        >
                            Save
                        </Button>
                    </Flex>
                </Flex>
            }
        >
            <Form
                layout="horizontal"
                labelAlign="left"
                labelCol={{flex: "138px"}}
                wrapperCol={{flex: "280px"}}
                labelWrap
                className="label-wrap"
                form={syslogConfigForm}
                style={{minHeight: "280.23px"}}
            >
                <Form.Item name="templateName" label={templateNameLabel} initialValue="">
                    <Input style={{backgroundColor: "#f0f0f0"}} readOnly />
                </Form.Item>
                <Form.Item name="templateTag" label={templateTagLabel}>
                    <div style={{border: "1px solid #ccc", minHeight: "50px", padding: 10}}>
                        <Flex style={{flexWrap: "wrap", rowGap: "5px"}}>
                            {tagContent &&
                                tagContent.split(",").map(tag => (
                                    <Tag
                                        style={{
                                            color: "#14C9BB",
                                            backgroundColor: "rgba(20, 201, 187, 0.1)",
                                            border: "1px solid #14C9BB"
                                        }}
                                        key={tag}
                                        closable
                                        onClose={() => {
                                            let tags = tagContent.split(",");
                                            tags = tags.filter(t => t !== tag);
                                            setTagContent(tags.join(","));
                                        }}
                                    >
                                        {tag}
                                    </Tag>
                                ))}
                        </Flex>
                    </div>
                </Form.Item>
                <Form.Item
                    label="Tag Name"
                    validateStatus={isTagNameValid ? "success" : "error"}
                    help={isTagNameValid ? "" : tagNameInvalidMessage}
                >
                    <Flex>
                        <Input
                            placeholder="Input Tag Name"
                            style={{width: "calc(100% - 64px)", marginRight: "16px"}}
                            value={inputTagName}
                            onChange={e => {
                                setInputTagName(e.target.value);
                                if (isInputTagNameInvalidBefore) {
                                    isInputTagNameValid(e.target.value, tagContent);
                                }
                            }}
                        />
                        <Button type="primary" onClick={handleAddTagButtonCallback}>
                            Add
                        </Button>
                    </Flex>
                </Form.Item>
            </Form>
        </Modal>
    ) : null;
});

export default TemplateTagManagementModal;
