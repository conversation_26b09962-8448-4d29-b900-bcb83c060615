import {Progress, theme} from "antd";
import {useEffect, useRef, useState} from "react";
import toolStyle from "./process_tool.module.scss";

/** *
 * @param processState
 * @param mask
 * @returns {JSX.Element}
 * @constructor
 */

const ProcessTool = ({processState, mask = false}) => {
    const [show, setShow] = useState(false);
    const [percent, setPercent] = useState(0);
    const timerRef = useRef();
    const timerValue = useRef();
    const {
        token: {colorPrimary}
    } = theme.useToken();

    useEffect(() => {
        if (processState.percent === -1) {
            if (timerRef.current) {
                clearInterval(timerRef.current);
                timerRef.current = null;
            }
            setShow(false);
            return;
        }
        setPercent(processState.percent);
        timerValue.current = processState;
        if (processState.percent > 0) {
            if (!show) {
                setShow(true);
            }
            if (!timerRef.current) {
                const timer = setInterval(() => {
                    timerValue.current.percent = parseInt(timerValue.current.percent) + 1;
                    if (timerValue.current.percent >= timerValue.current.nextPercent) {
                        clearInterval(timer);
                        timerRef.current = null;
                    } else {
                        setPercent(timerValue.current.percent);
                    }
                }, 1000);
                timerRef.current = timer;
            }
        }
        if (processState.percent === 100) {
            if (timerRef.current) {
                clearInterval(timerRef.current);
                timerRef.current = null;
            }
            setTimeout(() => {
                setShow(false);
            }, 2000);
        }
    }, [processState, setPercent]);

    return show ? (
        <div className={mask ? toolStyle.content_mask : toolStyle.content_div}>
            <div className={toolStyle.content}>
                <Progress percent={percent} type="circle" strokeColor={colorPrimary} />
                <a className={toolStyle.content_a} style={{color: colorPrimary}}>
                    {processState?.title ?? ""}
                </a>
            </div>
        </div>
    ) : (
        <div />
    );
};

export default ProcessTool;
