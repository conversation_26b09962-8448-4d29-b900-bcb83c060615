import React, {forwardRef, useEffect, useImperativeHandle, useState} from "react";
import {Slider} from "antd";
import {formatDate} from "@/utils/topo_layout_utils";

let timeoutId = null;

const TimeSlider = forwardRef(({refreshTopoCallback}, ref) => {
    const maxNodesCount = 70;
    const oneMinuteInterval = 60 * 1000;
    const tenMinutesInterval = 10 * 60 * 1000;
    const fifteenMinutesInterval = 15 * 60 * 1000;
    const twentyMinutesInterval = 20 * 60 * 1000;

    const [value, setValue] = useState(0);
    const [isShowTimeSlider, setIsShowTimeSlider] = useState(false);
    const [marks, setMarks] = useState(null);
    const [fullMarks, setFullMarks] = useState(null);
    const [startDate, setStartDate] = useState(null);
    const [endDate, setEndDate] = useState(null);

    useImperativeHandle(ref, () => ({
        showTimeSlider: (startDate, endDate) => {
            const endDateBeforeFiveMinutes = new Date(endDate.getTime() - 5 * 60 * 1000);
            const startDateBeforeFiveMinutes = new Date(startDate.getTime() - 5 * 60 * 1000);
            setStartDate(startDateBeforeFiveMinutes);
            setEndDate(endDateBeforeFiveMinutes);
            const marksData = generateMarks(startDateBeforeFiveMinutes, endDateBeforeFiveMinutes);
            setMarks(marksData[0]);
            setFullMarks(marksData[1]);
            setIsShowTimeSlider(true);
            setValue(65);
            refreshTopoCallback(`${marksData[1][65]}:00`, true);
        },
        hideTimeSlider: () => {
            setIsShowTimeSlider(false);
        },
        aimDate: date => {
            const now = new Date();
            const diff = Math.abs(now - date);
            let startDate, endDate;
            if (diff < 30 * 60 * 1000) {
                startDate = new Date(now.getTime() - 65 * 60 * 1000);
                endDate = new Date(now.getTime() - 5 * 60 * 1000);
            } else {
                startDate = new Date(date.getTime() - 30 * 60 * 1000);
                endDate = new Date(date.getTime() + 30 * 60 * 1000);
            }
            setStartDate(startDate);
            setEndDate(endDate);
            const marksData = generateMarks(startDate, endDate);
            setMarks(marksData[0]);
            setFullMarks(marksData[1]);
            const selectedValue = findKeyByValue(marksData, formatDate(date));
            setValue(selectedValue);
            refreshTopoCallback(`${marksData[1][selectedValue]}:00`, true);
        },
        getSelectedDate: () => {
            return fullMarks ? `${fullMarks[value]}:00` : null;
        }
    }));

    useEffect(() => {
        const handleResize = () => {
            if (isShowTimeSlider) {
                const marksData = generateMarks(startDate, endDate);
                setMarks(marksData[0]);
                setFullMarks(marksData[1]);
            }
        };

        window.addEventListener("resize", handleResize);
        return () => {
            window.removeEventListener("resize", handleResize);
        };
    }, [isShowTimeSlider]);

    const generateMarks = (startDate, endDate) => {
        const start = new Date(startDate);
        const end = new Date(endDate);
        const diff = Math.abs(end - start);
        let endDateTemp = null;
        const marksTemp = {};
        const fullMarksTemp = {};
        let interval = tenMinutesInterval;
        if (window.innerWidth < 1920) {
            interval = fifteenMinutesInterval;
        } else if (window.innerWidth < 1200) {
            interval = twentyMinutesInterval;
        }
        for (let i = 0; i <= diff; i += interval) {
            const date = new Date(start.getTime() + i);
            marksTemp[i / (60 * 1000) + 5] = `${formatDate(date)}`;
        }
        for (let i = 0; i <= diff; i += oneMinuteInterval) {
            const date = new Date(start.getTime() + i);
            fullMarksTemp[i / oneMinuteInterval + 5] = `${formatDate(date)}`;
            endDateTemp = date;
        }
        // Adjust the first 5 marks to be 5 minutes before the start time
        for (let i = 0; i < 5; i++) {
            const date = new Date(start.getTime() - oneMinuteInterval * (5 - i));
            fullMarksTemp[i] = `${formatDate(date)}`;
        }
        // Adjust the last 5 marks to be 5 minutes after the end time
        const maxMarksKey = Math.max(...Object.keys(fullMarksTemp).map(Number));
        for (let i = 1; i <= 5; i++) {
            const date = new Date(endDateTemp.getTime() + oneMinuteInterval * i);
            fullMarksTemp[maxMarksKey + i] = `${formatDate(date)}`;
        }
        return [marksTemp, fullMarksTemp];
    };

    const findKeyByValue = (marksData, formattedDate) => {
        const fullMarks = marksData[1];
        for (const key in fullMarks) {
            if (fullMarks[key] === formattedDate) {
                return key;
            }
        }
        return null;
    };

    const formatter = () => {
        const date = new Date(fullMarks[value]);
        return formatDate(date);
    };

    return isShowTimeSlider ? (
        <div style={{flex: 1}}>
            <Slider
                min={0}
                max={maxNodesCount}
                marks={marks}
                value={value}
                onChange={newValue => {
                    setValue(newValue);
                    // for performance, debounce the callback
                    if (timeoutId) {
                        clearTimeout(timeoutId);
                    }
                    timeoutId = setTimeout(() => {
                        refreshTopoCallback(`${fullMarks[newValue]}:00`, true);
                        timeoutId = null;
                    }, 500);
                }}
                step={1}
                included={false}
                tooltip={{
                    formatter,
                    open: true
                }}
            />
        </div>
    ) : null;
});

export default TimeSlider;
