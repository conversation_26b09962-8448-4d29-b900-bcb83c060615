import {useEffect, useMemo, useState} from "react";
import {useRequest} from "ahooks";
import {DatePicker, Select, message, Tag, Button, Switch, Space, Form, theme} from "antd";
import {useSelector} from "react-redux";
import {EditOutlined} from "@ant-design/icons";
import dayjs from "dayjs";
import {NEStateConfig} from "@/modules-otn/config/state_config";
import useUserRight from "@/modules-otn/hooks/useUserRight";
import {apiRpc, netconfByXML} from "@/modules-otn/apis/api";
import {CustomTable} from "@/modules-otn/components/common/custom_table";
import {gLabelList} from "@/store/modules/otn/languageOTNSlice";
import {NE_TYPE_CONFIG, DebounceButton} from "@/modules-otn/utils/util";
import {bigModal, middleModal} from "@/modules-otn/components/modal/custom_modal";
import {apiGetNTPTemplates, apiSetNTPConfig, getDevicesTime} from "./api";
import NtpTemplateItem from "./ntp_template_item";
import {getTimeManagement} from "@/modules-ampcon/apis/m6200_api";

const timeZones = {};
for (let i = -12; i < 13; ++i) {
    let z = "";
    if (i > 0 && i < 10) {
        z = `+0${i}`;
    } else if (i < 0 && i > 10) {
        z = `-0${i.toString().split("-").pop()}`;
    }
    // eslint-disable-next-line no-nested-ternary
    timeZones[`UTC${i > 0 ? `+${i}` : i === 0 ? "" : i}`] = `${z}:00`;
}

const formatTimezoneToTime = timezone => {
    const symbol = timezone.slice(3, 4);
    const hour = timezone.slice(4);
    if (symbol === "") return "+00:00";
    return `${symbol}${hour > 9 ? hour : `0${hour}`}:00`;
};

export default function DeviceTime() {
    const [dataSource, setDataSource] = useState([]);
    const [commonData, setCommonData] = useState({});
    const [ntpTemplates, setNtpTemplates] = useState([]);
    const {labelList} = useSelector(state => state.languageOTN);
    const userRight = useUserRight();
    const [loading, setLoading] = useState(true);

    const {
        token: {colorPrimary}
    } = theme.useToken();

    useRequest(apiGetNTPTemplates, {
        onSuccess(rs) {
            const {apiResult, data} = rs;
            if (apiResult === "fail") return;

            setNtpTemplates(data);
        }
    });

    const fetchData = async () => {
        setLoading(true);
        try {
            const [devicesData, managementData] = await Promise.all([getDevicesTime(), getTimeManagement()]);
            if (!managementData || managementData.status !== "success") {
                message.error("Failed to fetch management data");
                setDataSource([]);
                return;
            }
            const safeManagementData = Object.values(managementData.data);
            const filteredDevices = devicesData.filter(i => i.type === "5");
            const combinedData = [...filteredDevices, ...safeManagementData];
            console.log("combinedData", combinedData);
            setDataSource(combinedData);
        } catch (error) {
            console.error("Error fetching data:", error);
            message.error("Failed to fetch device data");
        } finally {
            setLoading(false);
        }
    };
    const refresh = () => {
        fetchData();
    };
    useEffect(() => {
        fetchData();
    }, []);
    const formatSNTPTime = sntpTime => {
        if (!sntpTime) return sntpTime;

        const parts = sntpTime.split(/\s+/).filter(Boolean);
        if (parts.length < 6) return sntpTime;

        const [_, monthStr, day, time, tz, year] = parts;

        const monthMap = {
            Jan: "01",
            Feb: "02",
            Mar: "03",
            Apr: "04",
            May: "05",
            Jun: "06",
            Jul: "07",
            Aug: "08",
            Sep: "09",
            Oct: "10",
            Nov: "11",
            Dec: "12"
        };

        if (!monthMap[monthStr]) return sntpTime;

        const paddedDay = day.padStart(2, "0");

        let formattedTz = tz;
        if (tz.match(/^[+-]\d{2}$/)) {
            formattedTz = `${tz}:00`;
        } else if (tz.match(/^[+-]\d{4}$/)) {
            formattedTz = `${tz.slice(0, 3)}:${tz.slice(3)}`;
        }
        return `${year}-${monthMap[monthStr]}-${paddedDay}T${time}${formattedTz}`;
    };

    const setNEsTime = async ({newDatetime, newTimeZone, nes}) => {
        setCommonData({});
        const newDatetimeWithTimezone = newDatetime.format(`YYYY-MM-DDTHH:mm:ss${formatTimezoneToTime(newTimeZone)}`);
        const needUpdateItems = nes.filter(selectItem => selectItem["current-datetime"] !== newDatetimeWithTimezone);

        return Promise.allSettled(
            needUpdateItems.map(async needUpdateItem => {
                const {
                    ne_id,
                    type,
                    "current-datetime": currentDatetime,
                    "timezone-name": timezoneName
                } = needUpdateItem;
                const updateRs = {timezoneRs: {}, datetimeRs: {}};
                if (type === "5") {
                    if (newTimeZone !== timezoneName) {
                        updateRs.timezoneRs = await netconfByXML({
                            ne_id,
                            msg: true,
                            xml: {
                                system: {
                                    $: {
                                        xmlns: "http://openconfig.net/yang/system"
                                    },
                                    clock: {
                                        config: {
                                            "timezone-name": newTimeZone
                                        }
                                    }
                                }
                            }
                        });
                    } else {
                        updateRs.timezoneRs = {result: true};
                    }
                    const formatNewDatetime = dayjs(newDatetime).format("YYYY-MM-DD HH:mm:ss");
                    const formatCurrentDatetime = dayjs(currentDatetime.slice(0, -6)).format("YYYY-MM-DD HH:mm:ss");
                    if (formatNewDatetime !== formatCurrentDatetime) {
                        updateRs.datetimeRs = await apiRpc({
                            ne_id,
                            rpcName: "set-datetime",
                            rpcConfig: {
                                datetime: newDatetimeWithTimezone
                            }
                        });
                    } else {
                        updateRs.datetimeRs = {result: true};
                    }
                }
                return updateRs;
            })
        )
            .then(rs => {
                if (
                    rs.every(
                        item =>
                            item.status === "fulfilled" &&
                            item?.value?.timezoneRs?.result &&
                            item?.value?.datetimeRs?.result
                    )
                ) {
                    message.success(labelList.save_success).then();
                    setTimeout(() => {
                        refresh();
                    }, 2000);
                } else {
                    message.error(labelList.save_failed).then();
                    setTimeout(() => {
                        refresh();
                    }, 2000);
                }
            })
            .catch(() => {
                setTimeout(() => {
                    refresh();
                }, 2000);
            });
    };

    const initColumns = [
        {
            title: labelList.ne_name,
            dataIndex: "name",
            defaultSortOrder: "ascend"
        },
        {
            title: "Type/Model",
            dataIndex: "type",
            filter: true,
            render: (state, record) => {
                const dynamicValue = record.type !== undefined ? record.type : record.model;
                return <span>{NE_TYPE_CONFIG[parseInt(dynamicValue)] || dynamicValue}</span>;
            }
        },
        {
            title: "state",
            dataIndex: "state",
            filter: true,
            render: (value, record) => {
                const {state} = record;

                let processedState;
                if (record?.sntp_data?.sntpSyncStatus === "notsync" || record?.sntp_data === null) {
                    processedState = 0;
                } else if (record?.sntp_data?.sntpSyncStatus === "synced") {
                    processedState = 1;
                } else {
                    processedState = state;
                }

                const _state = processedState < 0 ? "error" : processedState;
                const upgradeInfo = NEStateConfig.upgrade[_state];

                if (!upgradeInfo) {
                    return <span>{labelList.unknown_state}</span>;
                }

                return (
                    <span>
                        {upgradeInfo.icon} {labelList[upgradeInfo.title2]}
                    </span>
                );
            }
        },
        {
            title: "time",
            dataIndex: "current-datetime",
            render: (value, record) => {
                const timeValue =
                    record["current-datetime"] ||
                    (record?.sntp_data?.sntpSyncedTime ? formatSNTPTime(record.sntp_data.sntpSyncedTime) : null);

                const disabled = record?.state !== 1 || userRight.disabled;
                return (
                    <Space>
                        {timeValue && <span>{timeValue}</span>}
                        <DebounceButton
                            type="link"
                            disabled={disabled}
                            onClick={() => {
                                const {
                                    name,
                                    ntp,
                                    "current-datetime": currentDatetime,
                                    "timezone-name": timezoneName
                                } = record;

                                const title =
                                    commonData?.time_manage?.selectedRows?.reduce(
                                        (result, selectRecord, index) =>
                                            `${result}${selectRecord.name}${
                                                index !== commonData.time_manage.selectedRows.length - 1 ? ", " : ""
                                            }`,
                                        ""
                                    ) ?? name;

                                let formRef;
                                const bindForm = f => {
                                    formRef = f;
                                };
                                const initialValues = {
                                    "current-datetime": timeValue ? dayjs(timeValue) : null,
                                    "timezone-name": timezoneName
                                };
                                const onFinish = async values => {
                                    const newDatetime = values["current-datetime"];
                                    const newTimeZone = values["timezone-name"];

                                    setNEsTime({
                                        newDatetime,
                                        newTimeZone,
                                        nes: commonData?.time_manage?.selectedRows ?? [record]
                                    });
                                };

                                const items = [
                                    {
                                        label: gLabelList.time,
                                        name: "current-datetime",
                                        render: (
                                            <DatePicker
                                                style={{width: 280}}
                                                showTime
                                                disabled={ntp?.state?.enabled === "true"}
                                            />
                                        )
                                    },
                                    {
                                        label: gLabelList.timezone,
                                        name: "timezone-name",
                                        render: (
                                            <Select style={{width: 280}}>
                                                {Object.keys(timeZones).map(tz => (
                                                    <Select.Option key={tz} value={tz}>
                                                        {tz}
                                                    </Select.Option>
                                                ))}
                                            </Select>
                                        )
                                    }
                                ];

                                const onOk = () => {
                                    formRef
                                        .validateFields()
                                        .then(() => {
                                            formRef.submit();
                                        })
                                        .catch(() => {});
                                };

                                const modal = middleModal({
                                    title,
                                    content: (
                                        <EditTimeForm
                                            bindForm={bindForm}
                                            initialValues={initialValues}
                                            onFinish={onFinish}
                                            items={items}
                                        />
                                    ),
                                    footer: (
                                        <Space
                                            style={{display: "flex", flex: 1, justifyContent: "flex-end"}}
                                            className="ant-modal-confirm-btns"
                                            size={16}
                                        >
                                            <Button
                                                key="use-current-datetime"
                                                disabled={ntp?.state?.enabled === "true"}
                                                style={{width: "auto"}}
                                                onClick={() => {
                                                    setCommonData({});
                                                    const newDatetime = dayjs();
                                                    const newTimeZone = getLocalTimezone();

                                                    setNEsTime({
                                                        newDatetime,
                                                        newTimeZone,
                                                        nes: commonData?.time_manage?.selectedRows ?? [record]
                                                    });
                                                }}
                                            >
                                                {gLabelList.use_current_time_and_timezone}
                                            </Button>
                                            <Button
                                                onClick={() => {
                                                    modal.destroy();
                                                }}
                                            >
                                                {gLabelList.cancel}
                                            </Button>
                                            <Button type="primary" onClick={onOk}>
                                                {gLabelList.ok}
                                            </Button>
                                        </Space>
                                    )
                                });
                            }}
                            title="edit time"
                            icon={<EditOutlined style={{color: disabled ? "#bbbbbb" : colorPrimary}} />}
                        />
                    </Space>
                );
            }
        },
        {
            title: "NTP",
            dataIndex: "ntp",
            render: (value, record) => {
                let servers = value?.servers?.server ?? [];
                if (servers && !Array.isArray(servers)) servers = [servers];
                const children = [];
                const disabled = record?.state !== 1 || userRight.disabled;
                const editBtn = (
                    <DebounceButton
                        type="link"
                        disabled={record?.state !== 1 || userRight.disabled}
                        key={`${record?.name}:ntp_edit`}
                        onClick={() => {
                            setCommonData({});
                            let formRef;
                            const bindForm = f => {
                                formRef = f;
                            };

                            const nes = commonData?.time_manage?.selectedRows ?? [record];

                            const onFinish = (values, prefer) => {
                                modal.update({okButtonProps: {loading: true}});

                                apiSetNTPConfig({
                                    ...values,
                                    prefer,
                                    neList: nes
                                }).then(rs => {
                                    modal.update({okButtonProps: {loading: false}});

                                    const {apiResult, apiMessage} = rs;
                                    if (apiResult === "fail") {
                                        message.error(apiMessage).then();
                                        refresh();
                                        return;
                                    }
                                    modal.destroy();
                                    message.success(gLabelList.Operation_success).then();
                                    refresh();
                                });
                            };

                            const modal = bigModal({
                                title: gLabelList.edit_ntp,
                                content: (
                                    <NtpTemplateForm
                                        record={record}
                                        ntpTempaltes={ntpTemplates}
                                        bindForm={bindForm}
                                        onFinish={onFinish}
                                    />
                                ),
                                // eslint-disable-next-line no-unused-vars
                                onOk: _ => {
                                    formRef
                                        .validateFields()
                                        .then(() => {
                                            formRef.submit();
                                        })
                                        .catch(() => {});
                                }
                            });
                        }}
                        title="edit ntp"
                        icon={<EditOutlined style={{color: disabled ? "#bbbbbb" : colorPrimary}} />}
                    />
                );

                if (!servers?.length) return [editBtn];

                const ntpEnable = value?.config?.enabled === "true";
                servers?.forEach(server => {
                    const {address, state} = server;
                    const {port} = state ?? {};
                    if (address !== "") {
                        children.push(
                            <Tag
                                key={`${record?.name}:${address}`}
                                style={
                                    ntpEnable
                                        ? {
                                              margin: "0 8px 8px 0",
                                              padding: "0 7px",
                                              background: "#14C9BB1A",
                                              color: "#14C9BB",
                                              borderColor: "#14C9BB"
                                          }
                                        : {
                                              margin: "0 8px 8px 0",
                                              padding: "0 7px",
                                              background: "rgba(0, 0, 0, 0.04)",
                                              color: "rgba(0, 0, 0, 0.25)",
                                              borderColor: "#d9d9d9"
                                          }
                                }
                            >{`${address}${port ? `:${port}` : ""}`}</Tag>
                        );
                    }
                });

                children.push(editBtn);

                return children;
            }
        }
    ];

    return (
        <CustomTable
            type="time_manage"
            initTitle={
                !!commonData?.time_manage?.selectedRows.length && (
                    <span style={{fontSize: 14, fontWeight: "normal"}}>
                        {labelList.check_selected_text_preffix}
                        <span>{commonData.time_manage.selectedRows.length}</span>
                        {labelList.check_selected_text_suffix}
                    </span>
                )
            }
            scroll={false}
            initColumns={initColumns}
            initDataSource={dataSource}
            loading={loading}
            refreshParent={refresh}
            checkboxProps={record => {
                return {
                    disabled: record?.state !== 1
                };
            }}
            commonData={commonData}
            setCommonData={setCommonData}
        />
    );
}

const NtpTemplateForm = props => {
    const {record = {}, ntpTempaltes = [], bindForm, onFinish} = props;
    const [form] = Form.useForm();
    bindForm(form);
    const ntpState = useMemo(() => {
        return record?.ntp?.state?.enabled === "true";
    }, [record]);
    const defaultPrefer = useMemo(() => {
        let servers = record?.ntp?.servers?.server;
        if (!servers) return "ntp-server1";
        if (!Array.isArray(servers)) servers = [servers];
        return servers[1]?.state?.prefer === "true" ? "ntp-server2" : "ntp-server1";
    }, [record]);
    const [usedTemplate, setUsedTemplate] = useState({});
    const [prefer, setPrefer] = useState(defaultPrefer);

    const _onFinish = values => {
        onFinish(values, prefer);
    };

    return (
        <Form
            form={form}
            onFinish={_onFinish}
            initialValues={{"ntp-state": ntpState}}
            labelCol={{span: 4}}
            wrapperCol={{span: 14}}
            labelAlign="left"
            layout="horizontal"
            LabelWrap
            className="label-wrap"
        >
            <Form.Item name="ntp-state" label={gLabelList.ntp_enable}>
                <Switch defaultChecked={ntpState} />
            </Form.Item>
            <Form.Item name="ntp-server1" label={gLabelList.ntp_server1}>
                <NtpTemplateItem
                    templates={ntpTempaltes}
                    usedTemplate={usedTemplate}
                    setUsedTemplate={setUsedTemplate}
                    prefer={prefer}
                    setPrefer={setPrefer}
                    record={record}
                    formRef={form}
                />
            </Form.Item>
            <Form.Item name="ntp-server2" label={gLabelList.ntp_server2}>
                <NtpTemplateItem
                    templates={ntpTempaltes}
                    usedTemplate={usedTemplate}
                    setUsedTemplate={setUsedTemplate}
                    prefer={prefer}
                    setPrefer={setPrefer}
                    record={record}
                    formRef={form}
                />
            </Form.Item>
        </Form>
    );
};

const EditTimeForm = ({bindForm, items, initialValues, onFinish}) => {
    const [form] = Form.useForm();
    bindForm(form);

    return (
        <Form
            form={form}
            onFinish={onFinish}
            initialValues={initialValues}
            labelCol={{span: 8}}
            wrapperCol={{span: 14}}
            labelAlign="left"
            layout="horizontal"
            LabelWrap
            className="label-wrap"
        >
            {items.map((item, index) => {
                const {render, ...otherProps} = item;
                return (
                    // eslint-disable-next-line react/no-array-index-key
                    <Form.Item key={index} {...otherProps}>
                        {render}
                    </Form.Item>
                );
            })}
        </Form>
    );
};

const getLocalTimezone = () => {
    const rawTimezone = new Date().getTimezoneOffset() / 60;
    if (rawTimezone === 0) return "UTC";
    return `UTC${rawTimezone > 0 ? "-" : "+"}${Math.abs(rawTimezone)}`;
};
