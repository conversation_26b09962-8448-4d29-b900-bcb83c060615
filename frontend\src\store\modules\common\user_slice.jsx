import {createSlice} from "@reduxjs/toolkit";
import {loginAPI, logoutAPI, checkStatusAPI} from "@/apis/common/user";
import {message} from "antd";

const userStore = createSlice({
    name: "user",
    initialState: {
        userInfo: {}
    },
    reducers: {
        setUserInfo(state, action) {
            state.userInfo = action.payload;
        },
        clearUserInfo(state) {
            state.userInfo = {};
        }
    }
});

const {setUserInfo, clearUserInfo} = userStore.actions;

const fetchLogin = (loginForm, navigate) => {
    return async dispatch => {
        const res = await loginAPI(loginForm);
        if (res.status === 200) {
            navigate("/");
            message.success("Login Success");
        } else {
            dispatch(clearUserInfo());
            navigate("/login");
            message.error(`Login failed:${res.msg}`);
        }
    };
};

const fetchLogout = () => {
    return async dispatch => {
        const res = await logoutAPI();
        if (res.status === 200) {
            dispatch(clearUserInfo());
        }
    };
};

const checkStatus = () => {
    return async dispatch => {
        const res = await checkStatusAPI();
        if (res.status === 200) {
            dispatch(setUserInfo(res.data));
        } else {
            dispatch(clearUserInfo());
        }
    };
};

const userReducer = userStore.reducer;

export {fetchLogin, fetchLogout, checkStatus};

export default userReducer;
