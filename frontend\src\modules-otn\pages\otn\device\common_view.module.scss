.wrap {
    height: 100%;
    width: 100%;
    position: relative;
    background: url("@/assets/images/common_view/bg_map.png");
    background-position: center;
    background-repeat: no-repeat;
    background-size: 100% 100%;
}

.item {
    height: 50%;
    width: 100%;
    background-image: url("@/assets/images/common_view/bg-common-view.png");
    background-size: 90% 90%;
    background-position: center;
    background-repeat: no-repeat;
    background-color: rgba(255, 255, 255, 0.5);
}

.container {
  display: flex;
  flex-direction: column;
  width: 196px;
  height: 256px;
  background: #FFFFFF;
  box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.1);
  border-radius: 4px 4px 4px 4px;
  position: absolute;
  right: 24px;
  top: 10px;

  &_header {
    padding: 0px 20px;
    height: 40px;
    background: #F8FAFB;
    border-radius: 4px 4px 0px 0px;
    display: flex;
    justify-content: space-between;
    align-items: center;

    &_text {
      width: 73px;
      height: 17px;
      font-family: <PERSON><PERSON>, <PERSON><PERSON>;
      font-weight: 600;
      font-size: 14px;
      color: #212519;
      line-height: 17px;
      text-align: left;
      font-style: normal;
      text-transform: none;
    }
  }

  &_content {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    flex: 1;
    padding-left: 20px;
    margin-top: 16px;
  }

  &_round {
    width: 6px;
    height: 6px;
    background: #14C9BB;
    border-radius: 50%;
  }

  &_button {
    width: 142px;
    height: 36px;
    border-radius: 2px 2px 2px 2px;
    margin: 24px auto;
  }
  &_background{
    display: flex;
    justify-content: center;
    align-items: center;
    width: 14px;
    height: 14px;
  }
  &_background:hover{
    color: rgba(0, 0, 0, 0.88);
    background-color: rgba(0, 0, 0, 0.06);
    border-radius: 2px 2px 2px 2px;
  }

}
