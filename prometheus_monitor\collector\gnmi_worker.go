package collector

import (
	"context"
	"fmt"
	"log"
	"reflect"
	"sync"
	"time"

	"github.com/openconfig/gnmi/proto/gnmi"
	"github.com/openconfig/gnmic/pkg/api"
)

var hookRegistry = map[string]func(string) Hook{
	"openconfig-interfaces:interfaces": func(target string) Hook {
		return NewOpenconfigInterfaces(target)
	},
	"openconfig-platform:components": func(target string) Hook {
		return NewOpenconfigPlatform(target)
	},
}

type Hook interface {
	AfterSubscribe(result interface{})
}

// 负责采集和构建指标
type CollectorWorker struct {
	wg            sync.WaitGroup
	mutex         sync.RWMutex
	target        CollectTarget
	Path          string
	Interval      int
	stopCh        chan struct{}
	isRunning     bool
	collectResult map[string]BaseMetric
	Hooks         Hook
}

func NewCollectorWorker(target CollectTarget, path string, interval int) *CollectorWorker {
	hookCreator, exists := hookRegistry[path]
	if !exists {
		log.Println("No such hook registered. Proceeding without hooks.")
		return &CollectorWorker{
			target:        target,
			Path:          path,
			Interval:      interval,
			stopCh:        make(chan struct{}),
			collectResult: make(map[string]BaseMetric),
			Hooks:         nil,
		}
	}

	hook := hookCreator(target.Name)
	return &CollectorWorker{
		target:        target,
		Path:          path,
		Interval:      interval,
		stopCh:        make(chan struct{}),
		collectResult: make(map[string]BaseMetric),
		Hooks:         hook,
	}
}

func (w *CollectorWorker) Start() {
	w.wg.Add(1)
	go w.Subscribe(w.target)
	go w.CheckSubscribe()
}

func (w *CollectorWorker) Stop() {
	close(w.stopCh)
	w.wg.Wait()
}

func (w *CollectorWorker) UpdateTarget(target CollectTarget) {
	w.target = target
}

func (w *CollectorWorker) Restart() {
	w.stopCh = make(chan struct{})
	w.wg.Add(1)
	go w.Subscribe(w.target)
	go w.CheckSubscribe()
}

func (w *CollectorWorker) Subscribe(target CollectTarget) {
	defer w.wg.Done()
	defer func() {
		// log.Printf("before return %q ", target.Name)
		w.isRunning = false
	}()
	log.Println("Subscribe: ", w.Path, target.Name, target.Address)
	w.isRunning = true
	tg, err := api.NewTarget(
		api.Name(target.Name),
		api.Address(target.Address),
		api.Username(target.Username),
		api.Password(target.Password),
		api.SkipVerify(true),
	)
	if err != nil {
		log.Println("target create failed:", err)
		return
	}

	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()
	err = tg.CreateGNMIClient(ctx)
	if err != nil {
		log.Println("GNMI client create failed:", err)
		return
	}
	defer tg.Close()
	// create a gNMI subscribeRequest
	subReq, err := api.NewSubscribeRequest(
		api.Encoding("json_ietf"),
		api.SubscriptionListMode("stream"),
		api.Subscription(
			api.Path(w.Path),
			api.SubscriptionMode("sample"),
			api.SampleInterval(time.Duration(w.Interval)*time.Second),
		))
	if err != nil {
		log.Println(err)
		return
	}
	// start the subscription
	go tg.Subscribe(ctx, subReq, target.Name)
	stopSubCh := make(chan struct{})
	// // start a goroutine that will stop the subscription after stopCh close
	go func() {
		select {
		case <-ctx.Done():
			time.Sleep(1 * time.Second)
			log.Printf("cancel  %q after waiting 1 second.", target.Name)
			close(stopSubCh)
		case <-w.stopCh:
			tg.StopSubscription(target.Name)
			cancel()
			time.Sleep(1 * time.Second)
			log.Printf("stop  %q after waiting 1 second.", target.Name)
			close(stopSubCh)
		}
	}()
	subRspChan, subErrChan := tg.ReadSubscriptions()
	for {
		select {
		case rsp := <-subRspChan:
			switch rsp.Response.Response.(type) {
			case *gnmi.SubscribeResponse_Update:
				// fmt.Println(target.Name, "gnmi.SubscribeResponse_Update")
				// fmt.Println(reflect.TypeOf(rsp.Response.Response.(*gnmi.SubscribeResponse_Update).Update))
				// fmt.Println((rsp.Response.Response.(*gnmi.SubscribeResponse_Update).Update.Timestamp))
				w.UpdateResult(rsp.Response.Response.(*gnmi.SubscribeResponse_Update).Update)
				if w.Hooks != nil {
					go w.Hooks.AfterSubscribe(rsp.Response.Response.(*gnmi.SubscribeResponse_Update).Update)
				}
			default:
				log.Printf(target.Name, reflect.TypeOf(rsp.Response.Response))
			}
		case tgErr := <-subErrChan:
			log.Printf("subscription %q error: %v", tgErr.SubscriptionName, tgErr.Err)
			cancel()
		case <-stopSubCh:
			log.Printf("subscription %q stopped", target.Name)
			return
		}
	}
}

func (w *CollectorWorker) CheckSubscribe() {
	ticker := time.NewTicker(10 * time.Second)
	defer ticker.Stop()

	for range ticker.C {
		select {
		case <-w.stopCh:
			return
		default:
			// 检查Subscribe是否异常
			if !w.isRunning {
				log.Printf("Subscribe %v is not running. Restarting...", w.target.Name)
				w.wg.Add(1)
				go w.Subscribe(w.target)
			}
		}
	}
}

func (w *CollectorWorker) UpdateResult(result interface{}) {
	w.mutex.Lock()
	defer w.mutex.Unlock()
	// w.collectResult = result
	w.collectResult = make(map[string]BaseMetric)
	for _, update := range result.(*gnmi.Notification).Update {
		baseMetric := updateToBaseMetric(time.Now(), update)
		label := fmt.Sprintf("%v", baseMetric.Label)
		// fmt.Println(baseMetric.Name + "_" + label)
		if baseMetric.Name != "" {
			w.collectResult[baseMetric.Name+"_"+label] = baseMetric
		}
	}
}

func (w *CollectorWorker) GetResult() map[string]BaseMetric {
	w.mutex.RLock()
	defer w.mutex.RUnlock()
	// copy一份 防止collectResult逃逸导致map竞争
	copy := make(map[string]BaseMetric, len(w.collectResult))
	for k, v := range w.collectResult {
		copy[k] = v
	}
	return copy
}

func (w *CollectorWorker) GetWorkerStatus() int {
	var i int
	if w.isRunning {
		i = 1
	} else {
		i = 0
	}
	return i
}
