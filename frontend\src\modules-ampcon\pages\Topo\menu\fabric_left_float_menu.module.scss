.topoMenuButton {
    position: absolute;
    height: 32px;
    width: 32px;
    transform: translate(-50%, -50%);
    z-index: 500;
    display: flex;
    align-items: center;
    justify-content: center;

    color: #212519;
    background: #ffffff;
    box-shadow: 0px 1px 12px 1px #e6e8ea;
    border-radius: 4px 4px 4px 4px;
    border: 1px solid #e6e8ea;
}

.topoMenuLayoutBaseButton {
    @extend .topoMenuButton;
    top: 438px;
    left: 50px;
}

.topoMenuEditButton {
    @extend .topoMenuButton;
    top: 50px;
    left: 50px;
}

.topoMenuAimButton {
    @extend .topoMenuButton;
    top: 50px;
    left: 50px;
}

.topoMenuSaveButton {
    @extend .topoMenuButton;
    top: 50px;
    left: 50px;
}

.topoMenuCancelEditButton {
    @extend .topoMenuButton;
    top: 84px;
    left: 50px;
}

.topoMenuUndoButton {
    @extend .topoMenuButton;
    top: 131px;
    left: 50px;
}

.topoMenuRedoButton {
    @extend .topoMenuButton;
    top: 165px;
    left: 50px;
}

.topoEditMenuZoomInButton {
    @extend .topoMenuButton;
    top: 212px;
    left: 50px;
}

.topoEditMenuZoomResetButton {
    @extend .topoMenuButton;
    top: 246px;
    left: 50px;
}

.topoEditMenuZoomOutButton {
    @extend .topoMenuButton;
    top: 280px;
    left: 50px;
}

.topoAutoDiscoverButton {
    @extend .topoMenuButton;
    top: 327px;
    left: 50px;
}

.topoEditMenuRefreshButton {
    @extend .topoMenuButton;
    top: 361px;
    left: 50px;
}

.topoEditMenuShowLegendButton {
    @extend .topoMenuButton;
    top: 404px;
    left: 50px;
}

.topoAutoHierarchyLayoutButton {
    @extend .topoMenuButton;
    top: 404px;
    left: 50px;
}

.topoAutoGirdLayoutButton {
    @extend .topoMenuButton;
    top: 448px;
    left: 50px;
}

.topoAutoCircularLayoutButton {
    @extend .topoMenuButton;
    top: 490px;
    left: 50px;
}

.topoAutoEllipticalLayoutButton {
    @extend .topoMenuButton;
    top: 524px;
    left: 50px;
}

.topoMenuHistoryButton {
    @extend .topoMenuButton;
    top: 97px;
    left: 50px;
}

.topoMenuRealTimeButton {
    @extend .topoMenuButton;
    top: 97px;
    left: 50px;
}

.topoMenuZoomInButton {
    @extend .topoMenuButton;
    top: 144px;
    left: 50px;
}

.topoMenuZoomResetButton {
    @extend .topoMenuButton;
    top: 178px;
    left: 50px;
}

.topoMenuZoomOutButton {
    @extend .topoMenuButton;
    top: 212px;
    left: 50px;
}

.topoMenuRefreshButton {
    @extend .topoMenuButton;
    top: 259px;
    left: 50px;
}

.topoMenuDownloadImgButton {
    @extend .topoMenuButton;
    top: 306px;
    left: 50px;
}

.topoHistoryMenuDownloadImgButton {
    @extend .topoMenuButton;
    top: 259px;
    left: 50px;
}

.topoHistoryMenuDownloadImgButton {
    @extend .topoMenuButton;
    top: 259px;
    left: 50px;
}

.topoMenuReadonlyHistoryButton {
    @extend .topoMenuButton;
    top: 50px;
    left: 50px;
}

.topoMenuReadonlyZoomInButton {
    @extend .topoMenuButton;
    top: 97px;
    left: 50px;
}

.topoMenuReadonlyZoomResetButton {
    @extend .topoMenuButton;
    top: 131px;
    left: 50px;
}

.topoMenuReadonlyZoomOutButton {
    @extend .topoMenuButton;
    top: 165px;
    left: 50px;
}

.topoMenuReadonlyRefreshButton {
    @extend .topoMenuButton;
    top: 212px;
    left: 50px;
}

.topoMenuReadonlyDownloadImgButton {
    @extend .topoMenuButton;
    top: 259px;
    left: 50px;
}