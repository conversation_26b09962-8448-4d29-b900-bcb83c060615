import {Button, Descriptions, Divider, Form, Input, message, Modal, Tooltip} from "antd";
import {forwardRef, useImperativeHandle, useState} from "react";
import {InboxOutlined, QuestionCircleOutlined} from "@ant-design/icons";
import <PERSON><PERSON> from "antd/es/upload/Dragger";
import {batchUpgradeSwitch} from "@/modules-ampcon/apis/rma_api";

const BatchSwitchUpgradeModal = forwardRef((props, ref) => {
    useImperativeHandle(ref, () => ({
        showBatchSwitchUpgradeModal: (useImageInfo, selectedSwitchListInfo) => {
            if (useImageInfo.length === 0) {
                return;
            }
            setUseImageInfo(useImageInfo[0]);
            setSelectedSwitchListInfo(selectedSwitchListInfo.tableSelectedRows);
            batchUpgradeForm.setFieldValue("usedImage", useImageInfo[0].image_name);
            setIsShowModal(true);
        },
        hideBatchSwitchUpgradeModal: () => {
            setIsShowModal(false);
            resetModal();
        }
    }));

    const title = "Upgrade Task";
    const usedImageLabel = "Use Image";
    const selectedSwitchLabel = "Switches for Upgrade";
    const scriptLabel = "Scripts";
    const scriptTooltip =
        "Use this field to specify an optional post-upgrade script file. Click on + to add more than one script file";

    const {saveCallback} = props;

    const [batchUpgradeForm] = Form.useForm();

    const [isShowModal, setIsShowModal] = useState(false);
    const [useImageInfo, setUseImageInfo] = useState({});
    const [selectedSwitchListInfo, setSelectedSwitchListInfo] = useState([]);
    const [uploadFileList, setUploadFileList] = useState([]);

    const resetModal = () => {
        batchUpgradeForm.resetFields();
        setUploadFileList([]);
    };

    return isShowModal ? (
        <Modal
            className="ampcon-middle-modal"
            title={
                <div>
                    {title}
                    <Divider style={{marginTop: 8, marginBottom: 0}} />
                </div>
            }
            open={isShowModal}
            onOk={() => {}}
            onCancel={() => {
                setIsShowModal(false);
            }}
            footer={
                <>
                    <Divider style={{marginTop: 0, marginBottom: 20}} />
                    <Button
                        onClick={() => {
                            setIsShowModal(false);
                            resetModal();
                        }}
                    >
                        Cancel
                    </Button>
                    <Button
                        type="primary"
                        onClick={() => {
                            batchUpgradeSwitch(
                                useImageInfo.image_name,
                                selectedSwitchListInfo.map(switchItem => switchItem.sn),
                                uploadFileList
                            ).then(response => {
                                if (response.status !== 200) {
                                    message.error(response.info);
                                } else {
                                    message.success(response.info);
                                    setIsShowModal(false);
                                    resetModal();
                                    saveCallback();
                                }
                            });
                        }}
                    >
                        Upgrade
                    </Button>
                </>
            }
        >
            <Form
                layout="horizontal"
                labelAlign="left"
                labelCol={{span: 7}}
                wrapperCol={{span: 17}}
                labelWrap
                className="label-wrap"
                form={batchUpgradeForm}
                style={{minHeight: "260.23px"}}
            >
                <Form.Item name="usedImage" label={usedImageLabel}>
                    <Input style={{backgroundColor: "#f0f0f0"}} readOnly />
                </Form.Item>
                <Form.Item label={selectedSwitchLabel}>
                    <Descriptions bordered size="small" column={1}>
                        {selectedSwitchListInfo.map(switchItem => (
                            <Descriptions.Item label={switchItem.host_name}>{switchItem.sn}</Descriptions.Item>
                        ))}
                    </Descriptions>
                </Form.Item>
                <Form.Item
                    label={
                        <Tooltip title={scriptTooltip}>
                            <span style={{marginRight: "10px"}}>{scriptLabel}</span>
                            <QuestionCircleOutlined className="questioncircle-color" />
                        </Tooltip>
                    }
                    rules={[{required: true}]}
                >
                    <Dragger
                        name="file"
                        beforeUpload={file => {
                            const isAlreadyExist = uploadFileList.some(item => item.name === file.name);
                            const isContainPostXorplus = uploadFileList.some(file => file.name === "post-xorplus");
                            if (isAlreadyExist) {
                                message.error(`Script file named ${file.name} is already exist`);
                                return false;
                            }
                            if (!isContainPostXorplus && file.name !== "post-xorplus") {
                                message.error(`Script file named post-xorplus is required`);
                                return false;
                            }
                            setUploadFileList([...uploadFileList, file]);
                            return false;
                        }}
                        fileList={uploadFileList}
                        multiple
                        style={{marginBottom: "20px"}}
                        onRemove={file => {
                            setUploadFileList(uploadFileList.filter(item => item.uid !== file.uid));
                        }}
                    >
                        <p className="ant-upload-drag-icon">
                            <InboxOutlined />
                        </p>
                        <p className="ant-upload-text">Click or drag file to this area to upload</p>
                        <p className="ant-upload-hint">Support for a bulk upload.</p>
                    </Dragger>
                </Form.Item>
            </Form>
        </Modal>
    ) : null;
});

export default BatchSwitchUpgradeModal;
