import {
    AmpConCustomTable,
    TableFilterDropdown,
    createColumnConfigMultipleParams
} from "@/modules-ampcon/components/custom_table";
import {
    exportTemplate,
    fetchTemplateListData,
    removeTemplate,
    updateInternalTemplate,
    uploadTemplateBySingleFile
} from "@/modules-ampcon/apis/template_api";
import {Button, Flex, Form, Input, message, Space, Spin, Switch, Tag} from "antd";
import Icon from "@ant-design/icons";
import React, {useRef, useState} from "react";
import UploadSingleFileModal from "@/modules-ampcon/components/upload_singel_file_modal";
import TemplatePreviewModal from "@/modules-ampcon/components/template_preview_modal";
import TemplateTagManagementModal from "@/modules-ampcon/pages/Service/Switch/ConfigTemplate/TemplateList/template_tag_management_modal";
import TemplateCopyModal from "@/modules-ampcon/pages/Service/Switch/ConfigTemplate/TemplateList/template_copy_modal";
import {updateWhiteSvg, exportSvg, updatePlayBookSvg} from "@/utils/common/iconSvg";
import {confirmModalAction} from "@/modules-ampcon/components/custom_modal";

const TemplateList = () => {
    const uploadTemplateModalTitle = "Upload Template";
    const uploadTemplateNameLabel = "Name";
    const uploadTemplateDescriptionLabel = "Description";

    const matchFieldsList = [
        {name: "name", matchMode: "fuzzy"},
        {name: "description", matchMode: "fuzzy"},
        {name: "tag", matchMode: "fuzzy"}
    ];

    const searchFieldsList = ["name", "description", "tag"];

    // spin
    const [isShowSpin, setIsShowSpin] = useState(false);

    // is show pre-built template
    const [isShowPreBuiltTemplate, setIsShowPreBuiltTemplate] = useState(false);

    // for upload template modal
    const [isShowUploadTemplateModal, setIsShowUploadTemplateModal] = useState(false);
    const [templateModalFileList, setTemplateModalFileList] = useState([]);
    const [uploadTemplateForm] = Form.useForm();

    // for view template modal
    const templatePreviewModalRef = useRef(null);

    // for tag management modal
    const templateTagManagementModalRef = useRef(null);

    // for template copy modal
    const templateCopyModalRef = useRef(null);

    // template table
    const tableRef = useRef(null);

    const viewTemplateButtonCallback = record => {
        templatePreviewModalRef.current.showTemplatePreviewModal(record.name);
    };

    const uploadTemplateCallback = async () => {
        try {
            // Perform all validations
            await uploadTemplateForm.validateFields();
            if (templateModalFileList.length === 0) {
                message.error("Please upload a template file!");
                return;
            }
            // If no errors, proceed with saving the form
            const {uploadTemplateName} = uploadTemplateForm.getFieldValue();
            const {uploadTemplateDescription} = uploadTemplateForm.getFieldValue();
            setIsShowUploadTemplateModal(false);
            setIsShowSpin(true);
            try {
                await uploadTemplateBySingleFile(
                    uploadTemplateName,
                    uploadTemplateDescription || "",
                    templateModalFileList[0]
                ).then(response => {
                    if (response.status !== 200) {
                        message.error(response.info);
                    } else {
                        message.success(response.info);
                    }
                });
                await tableRef.current.refreshTable();
                setIsShowSpin(false);
                uploadTemplateForm.resetFields();
                setTemplateModalFileList([]);
            } catch (e) {
                message.error("An error occurred during the process of save");
            } finally {
                setIsShowSpin(false);
            }
        } catch (errorInfo) {
            /* empty */
        } finally {
            setIsShowSpin(false);
        }
    };

    return (
        <>
            <Spin spinning={isShowSpin} tip="Loading..." fullscreen />
            <div>
                <TemplatePreviewModal ref={templatePreviewModalRef} />
                <UploadSingleFileModal
                    uploadByJsonModalTitle={uploadTemplateModalTitle}
                    isShowUploadJsonModal={isShowUploadTemplateModal}
                    fileList={templateModalFileList}
                    preForm={
                        <Form
                            layout="horizontal"
                            labelAlign="left"
                            labelCol={{span: 5}}
                            wrapperCol={{span: 19}}
                            form={uploadTemplateForm}
                        >
                            <Form.Item
                                name="uploadTemplateName"
                                label={uploadTemplateNameLabel}
                                rules={[
                                    {required: true, message: "Please input your template name!"},
                                    {
                                        validator: (_, value) => {
                                            if (value.includes(" ")) {
                                                return Promise.reject(
                                                    new Error("Template name should not have spaces.")
                                                );
                                            }
                                            return Promise.resolve();
                                        }
                                    }
                                ]}
                            >
                                <Input />
                            </Form.Item>
                            <Form.Item name="uploadTemplateDescription" label={uploadTemplateDescriptionLabel}>
                                <Input />
                            </Form.Item>
                        </Form>
                    }
                    uploadButtonCallback={uploadTemplateCallback}
                    beforeUploadCallback={file => {
                        setTemplateModalFileList(file);
                        return false;
                    }}
                    onRemoveCallback={() => {
                        setTemplateModalFileList([]);
                    }}
                    cancelCallback={() => {
                        setIsShowUploadTemplateModal(false);
                        uploadTemplateForm.resetFields();
                        setTemplateModalFileList([]);
                    }}
                />
                <TemplateTagManagementModal
                    ref={templateTagManagementModalRef}
                    saveCallback={() => {
                        tableRef.current.refreshTable();
                    }}
                />
                <TemplateCopyModal
                    ref={templateCopyModalRef}
                    saveCallback={() => {
                        tableRef.current.refreshTable();
                    }}
                />
            </div>

            <Flex vertical style={{minWidth: "1200px"}}>
                <AmpConCustomTable
                    ref={tableRef}
                    columns={[
                        createColumnConfigMultipleParams({
                            title: "Template Name",
                            dataIndex: "name",
                            filterDropdownComponent: TableFilterDropdown,
                            width: "15%",
                            render: (_, record) => {
                                if (record.internal) {
                                    return (
                                        <Space>
                                            {record.name}
                                            <Tag
                                                style={{
                                                    color: "#14C9BB",
                                                    backgroundColor: "rgba(20, 201, 187, 0.1)",
                                                    border: "1px solid #14C9BB"
                                                }}
                                            >
                                                Pre-built
                                            </Tag>
                                        </Space>
                                    );
                                }
                                return record.name;
                            }
                        }),
                        createColumnConfigMultipleParams({
                            title: "Description",
                            dataIndex: "description",
                            filterDropdownComponent: TableFilterDropdown,
                            width: "15%"
                        }),
                        createColumnConfigMultipleParams({
                            title: "Create Time",
                            dataIndex: "create_time",
                            enableFilter: false,
                            enableSorter: true,
                            width: "15%",
                            defaultSortOrder: "descend"
                        }),
                        createColumnConfigMultipleParams({
                            title: "Tag",
                            dataIndex: "tag",
                            enableFilter: true,
                            filterDropdownComponent: TableFilterDropdown,
                            enableSorter: false,
                            width: "15%",
                            render: (_, record) => {
                                return (
                                    <Flex
                                        style={{flexWrap: "wrap", rowGap: "5px", marginTop: "6px", marginBottom: "6px"}}
                                    >
                                        {record.tag &&
                                            record.tag.split(",").map(tag => (
                                                <Tag
                                                    style={{
                                                        color: "#14C9BB",
                                                        backgroundColor: "rgba(20, 201, 187, 0.1)",
                                                        border: "1px solid #14C9BB"
                                                    }}
                                                    key={tag}
                                                >
                                                    {tag}
                                                </Tag>
                                            ))}
                                    </Flex>
                                );
                            }
                        }),
                        {
                            title: "Operation",
                            render: (_, record) => {
                                return (
                                    <Flex
                                        style={{flexWrap: "wrap", columnGap: "15px", rowGap: "3px"}}
                                        className="actionLink"
                                    >
                                        <a onClick={() => viewTemplateButtonCallback(record)}>View Template</a>
                                        {record.internal ? null : (
                                            <a
                                                onClick={() => {
                                                    confirmModalAction(
                                                        `Do you want to remove template ${record.name}?`,
                                                        () => {
                                                            removeTemplate(record.name).then(response => {
                                                                if (response.status !== 200) {
                                                                    message.error(response.info);
                                                                } else {
                                                                    message.success(response.info);
                                                                    tableRef.current.refreshTable();
                                                                }
                                                            });
                                                        }
                                                    );
                                                }}
                                            >
                                                Remove Template
                                            </a>
                                        )}
                                        <a
                                            onClick={() => {
                                                templateCopyModalRef.current.showTemplateCopyModal(record.name);
                                            }}
                                        >
                                            Copy
                                        </a>
                                        <a
                                            onClick={() => {
                                                exportTemplate(record.name);
                                            }}
                                        >
                                            Export
                                        </a>
                                        {record.internal ? null : (
                                            <a
                                                onClick={() => {
                                                    templateTagManagementModalRef.current.showTemplateTagManagementModal(
                                                        record.id,
                                                        record.name,
                                                        record.tag || ""
                                                    );
                                                }}
                                            >
                                                Tag Management
                                            </a>
                                        )}
                                    </Flex>
                                );
                            }
                        }
                    ]}
                    searchFieldsList={searchFieldsList}
                    matchFieldsList={matchFieldsList}
                    extraButton={
                        <Space size="middle">
                            <Button
                                type="primary"
                                onClick={() => {
                                    setIsShowUploadTemplateModal(true);
                                }}
                                style={{display: "flex", alignItems: "center"}}
                            >
                                <Icon component={updateWhiteSvg} />
                                Upload Template
                            </Button>
                            <Button
                                onClick={() => {
                                    exportTemplate("all");
                                }}
                                style={{display: "flex", alignItems: "center"}}
                            >
                                <Icon component={exportSvg} />
                                Export All Template
                            </Button>
                            <Button
                                onClick={async () => {
                                    setIsShowSpin(true);
                                    try {
                                        await updateInternalTemplate().then(response => {
                                            if (response.status !== 200) {
                                                message.error(response.info);
                                            } else {
                                                message.success(response.info);
                                                setIsShowPreBuiltTemplate(true);
                                            }
                                        });
                                        setIsShowSpin(false);
                                    } catch (e) {
                                        message.error("An error occurred during the process of download");
                                    } finally {
                                        setIsShowSpin(false);
                                    }
                                }}
                                style={{display: "flex", alignItems: "center"}}
                            >
                                <Icon component={updatePlayBookSvg} />
                                Update Pre-built Template
                            </Button>
                            Show Pre-built Templates
                            <Switch
                                checked={isShowPreBuiltTemplate}
                                onChange={checked => {
                                    setIsShowPreBuiltTemplate(checked);
                                }}
                            />
                        </Space>
                    }
                    fetchAPIInfo={fetchTemplateListData}
                    fetchAPIParams={[isShowPreBuiltTemplate]}
                />
            </Flex>
        </>
    );
};

export default TemplateList;
