import React, {useState} from "react";
import Icon from "@ant-design/icons";
import {Button} from "antd";

export const NULL_VALUE = "--";

export const UPPER_CASES = [
    "pm",
    "mcu",
    "cpu",
    "lldp",
    "aps",
    "ntp",
    "tti",
    "bbe",
    "es",
    "ses",
    "uas",
    "fec",
    "als",
    "mac",
    "ad",
    "voa",
    "id",
    "tlv",
    "fan",
    "tff",
    "oeo",
    "ber",
    "tec",
    "lcd",
    "wss",
    "apr",
    "otdr",
    "tx",
    "rx",
    "crc",
    "pcs",
    "bip",
    "usz",
    "ip",
    "ttl",
    "osnr",
    "src",
    "dst",
    "nat",
    "pa",
    "ba",
    "la1",
    "la2",
    "att",
    "odu",
    "ptp",
    "ctp",
    "ftp",
    "tcp",
    "eq",
    "mtu",
    "sdh",
    "ts",
    "ne",
    "nni",
    "nni2",
    "uni",
    "cir",
    "pir",
    "cbs",
    "pbs",
    "vlan",
    "lcas",
    "vc",
    "wtr",
    "tsd",
    "eh",
    "xc",
    "tcm",
    "eth",
    "tpid",
    "eos",
    "pac",
    "vcg",
    "mc",
    "vlan",
    "uuid",
    "vlan",
    "pvid",
    "tp",
    "sd",
    "pg",
    "tca",
    "tim",
    "ssm",
    "clk",
    "sa",
    "sapi",
    "dapi",
    "sn",
    "pn",
    "otn",
    "num"
];

export const getText = text => {
    if (!text) return null;
    return (
        text
            .split("-")
            ?.map(i => (UPPER_CASES.some(u => u === i) ? i.toUpperCase() : i[0].toUpperCase() + i.substring(1)))
            .join(" ") ?? text
    );
};

/**
 * 将对象，字符串，数字封装为数组
 * @param {*} obj
 * @returns {Array} array
 */
export const convertToArray = obj => {
    if (Array.isArray(obj)) return obj;
    if (["string", "object", "number"].includes(typeof obj)) return [obj];
    return [];
};

export const ToolButton = ({enabledIcon, disabledIcon, onFocusIcon, title, disabled, onClick, confirm, style}) => {
    const [buttonOnFocus, setButtonOnFocus] = useState(false);
    let buttonIcon = disabledIcon;
    if (!disabled) {
        buttonIcon = buttonOnFocus ? onFocusIcon : enabledIcon;
    }
    const iconButton = (
        <span title={title} style={style}>
            <Icon
                onClick={() => {
                    if (!confirm && !disabled) {
                        onClick();
                    }
                }}
                component={buttonIcon}
                onMouseEnter={() => {
                    if (buttonOnFocus) {
                        return;
                    }
                    setButtonOnFocus(true);
                }}
                onMouseLeave={() => {
                    if (!buttonOnFocus) {
                        return;
                    }
                    setButtonOnFocus(false);
                }}
                style={{cursor: disabled ? "not-allowed" : "pointer"}}
            />
        </span>
    );
    return iconButton;
};

export const DebounceButton = props => {
    const [receiveEvent, setReceiveEvent] = useState(true);
    const handleClick = e => {
        if (!receiveEvent) {
            return;
        }
        setReceiveEvent(false);
        props.onClick(e);
        setTimeout(() => {
            setReceiveEvent(true);
        }, 500);
    };
    const {containerType, ...rest} = props;
    if (containerType === "Icon") {
        return <Icon {...rest} onClick={handleClick} />;
    }
    if (containerType === "div") {
        return <div {...rest} onClick={handleClick} />;
    }
    if (containerType === "a") {
        return <a {...rest} onClick={handleClick} />;
    }
    if (containerType === "ToolButton") {
        return <ToolButton {...rest} onClick={handleClick} />;
    }
    return <Button {...rest} onClick={handleClick} />;
};

export const SortPortsDefault = ports => {
    const order = ["C1", "C2", "C3", "C4", "L1", "L2", "L3", "L4"];

    return ports.sort((a, b) => {
        const extractKey = name => {
            const parts = name.split("-");
            return parts[parts.length - 1];
        };

        const keyA = extractKey(a?.name || a?.key);
        const keyB = extractKey(b?.name || b?.key);

        const indexA = order.indexOf(keyA);
        const indexB = order.indexOf(keyB);

        if (indexA === -1 || indexB === -1) {
            return a?.no.localeCompare(b?.no);
        }

        return indexA - indexB;
    });
};
