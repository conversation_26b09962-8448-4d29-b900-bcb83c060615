.unitComponent [class*="ant-tabs-nav"] {
    background-color: transparent;
    margin: 0;
}

.unitComponent [class*="ant-tabs-tab"] {
    padding: 8px 0 !important;
    background: transparent;
    border: 0;
}

.unitComponent [class*="ant-tabs-tab"]+[class*="ant-tabs-tab"]:not([class*="ant-tabs-tabpane"]) {
    margin: 0 0 0 32px !important;
}

.unitComponent [class*="ant-tabs-ink-bar"] {
    top: auto !important;
}

.viewContainer {
    height: 70vh;
    & div > div {
        display: flex;
        & > div {
            width: 17vw;
            height: 40px;
            line-height: 40px;
            background: #F7F9F9;
            padding: 0 24px;
            & > span+span {
                margin-left: 24px;
            }
            & + div {
                margin-left: 12px;
            }
        }
    }
}

.unitCard {
    display: flex;
    flex: 1;
    [class*="ant-card-body"] {
        padding: 0 24px !important;
    }
}