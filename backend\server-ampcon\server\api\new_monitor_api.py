import os
import copy
import json
import logging
import traceback
import requests
import uuid
from datetime import datetime, timedelta
from pygnmi.client import gNMIclient
from concurrent.futures import ThreadPoolExecutor, as_completed
from flask import Blueprint, render_template, jsonify, request, flash, redirect, send_file
from sqlalchemy import or_, and_
from server.db.models.otn import OtnDeviceBasic
from server.db.models.monitor import Event, OperationLog
from server.db.models.monitor import monitor_db
from server.db.models.inventory import SystemConfig, License, Switch, MonitorTarget, Topology, TopologyEdge, TopologyNode
from server.db.models.inventory import inven_db, PushConfigTask, PushConfigTaskDetails
from server.db.models.user import User
from server.db.models.user import user_db
from server.util import utils
from server.util import ssh_util as conn_client
from server import cfg, constants
from server.util.permission import admin_permission
from server.util.yaml_util import YAMLEditor
from server.util.encrypt_util import encrypt_with_static_key
from server.util.prometheus_util import get_target_interfaces, query_counter_delta_topk, query_counters_with_prefix, query_lldp_neighbor_state, query_metric_filter_by_interface,\
    query_modules_topk, query_attenuation_topk, query_counters, query_ai_with_prefix, query_lldp_state, query_node_metric, query_node_range_metric, query_node_topk, \
    query_rate_topk, get_port_speed_usage, query_cpu_usage_topk, query_memory_usage_topk, query_metric, query_dlb_rate_topk

new_monitor_mold = Blueprint("new_monitor_mold", __name__, template_folder='templates')

LOG = logging.getLogger(__name__)

timestamp_format = '%Y-%m-%d %H:%M'

# @monitor_mold.route('/server/status', methods=['GET'])
# def server_status():
#     ram_status = utils.get_memory_state()
#     ram_status.pop('free')
#     return jsonify({
#         'cpu': utils.get_CPU_state(),
#         'memory': ram_status,
#         'disk': utils.get_disk_satate()
#     })


# @monitor_mold.route('/server/available')
# def server_available():
#     try:
#         user_db.get_collection(User)
#         return 'Application and database are up and functioning'
#     except:
#         return 'db error'


# @monitor_mold.route('/switch_alarm')
# def switch_alarm():
#     # db_session = db.get_session()
#     # all_event = db_session.query(Event).filter(Event.status == 'unread').all()
#     active = ('monitor', 'switch_alarm')
#     return render_template('monitor/switch_alarm.html', active=active)


# @monitor_mold.route('/switch_alarm/data')
# def get_alarm_data():
#     search_value = request.args.get('search[value]')

#     rule = [Event.sn.in_(list(map(lambda x: x.sn, utils.query_switch())))]

#     status_filter = Event.status.in_(['unread'])

#     if search_value and search_value != '':
#         search_value = '%' + search_value + '%'
#         rule.append(or_(*[Event.sn.like(search_value),
#                      Event.type.like(search_value),
#                      Event.msg.like(search_value)]))
#     rule = and_(*rule)
#     return utils.page_helper(request.args, Event, rule=rule, extra_filter=status_filter)


# @monitor_mold.route('/read/<int:event_id>')
# def switch_alarm_read(event_id):
#     monitor_db.update_event_status(event_id, 'read')
#     return 'ok'


# @monitor_mold.route('/all_message')
# def switch_alarm_all_message():
#     active = ('monitor', 'switch_alarm')
#     return render_template('monitor/switch_alarm_all_data.html', active=active)


# @monitor_mold.route('/del/<int:event_id>')
# @admin_permission.require(http_exception=403)
# def switch_alarm_del(event_id):
#     inven_db.delete_collection(Event, filters={'id': [event_id]})
#     return 'ok'


# @monitor_mold.route('/all_message/data')
# def get_alarm_all_data():
#     search_value = request.args.get('search[value]')
#     rule = [Event.sn.in_(list(map(lambda x: x.sn, utils.query_switch())))]
    
#     if search_value and search_value != '':
#         search_value = '%' + search_value + '%'
#         rule.append(or_(*[Event.sn.like(search_value),
#                      Event.type.like(search_value),
#                      Event.msg.like(search_value)]))
#     rule = and_(*rule)
#     return utils.page_helper(request.args, Event, rule=rule)


# @monitor_mold.route('/operation_log/view')
# def operation_log_view():
#     active = ('monitor', 'operation_log')
#     return render_template('monitor/operation_log.html', active=active)


# @monitor_mold.route('/operation_log/data')
# def operation_log():
#     search_value = request.args.get('search[value]')
#     rule = None
#     if search_value and search_value != '':
#         search_value = '%' + search_value + '%'
#         rule = or_(*[OperationLog.user.like(search_value),
#                      OperationLog.path.like(search_value),
#                      OperationLog.content_original.like(search_value),
#                      OperationLog.status.like(search_value)])
    
#     return utils.page_helper(request.args, OperationLog, rule=rule)


# @monitor_mold.route('/push_config_logs/view')
# def push_config_tasks_view():
#     active = ('monitor', 'push_config_logs')
#     return render_template('monitor/push_config_tasks.html', active=active)


# @monitor_mold.route('/all_config_logs')
# def all_config_tasks():
#     active = ('monitor', 'push_config_logs')
#     return render_template('monitor/push_config_tasks_all.html', active=active)


# @monitor_mold.route('/push_config_tasks/data')
# def push_config_tasks():
#     search_value = request.args.get('search[value]')
#     rule = None
#     status_filter = PushConfigTask.read_tag.in_([0])
#     if search_value and search_value != '':
#         search_value = '%' + search_value + '%'
#         rule = or_(*[PushConfigTask.user.like(search_value),
#                      PushConfigTask.path.like(search_value),
#                      PushConfigTask.content_original.like(search_value),
#                      PushConfigTask.status.like(search_value)])
    
#     return utils.page_helper(request.args, PushConfigTask, rule=rule, extra_filter=status_filter)


@new_monitor_mold.route('/push_config_tasks/data_all', methods=['POST'])
def push_config_all_tasks():
    # search_value = request.args.get('search[value]')
    # rule = None
    # if search_value and search_value != '':
    #     search_value = '%' + search_value + '%'
    #     rule = or_(*[PushConfigTask.user.like(search_value),
    #                  PushConfigTask.path.like(search_value),
    #                  PushConfigTask.content_original.like(search_value),
    #                  PushConfigTask.status.like(search_value)])
    
    # # return utils.page_helper(request.args, PushConfigTask, rule=rule)
    # db_session = inven_db.get_session()
    # # local_licenses = db_session.query(PushConfigTask).filter(inventory.License.local_lic.isnot(None))
    page_num, page_size, total_count, query_pushconfig = utils.query_helper(PushConfigTask)
    return jsonify({"data": [task.make_dict() for task in query_pushconfig], "page": page_num,
                    "pageSize": page_size,
                    "total": total_count,
                    "status": 200})


@new_monitor_mold.route('/show_switch_ret/<string:task_name>/<string:sn>')
def show_switch_ret(sn, task_name):
    db_session = monitor_db.get_session()
    switch_ret_obj = db_session.query(PushConfigTaskDetails).filter(PushConfigTaskDetails.name == task_name, PushConfigTaskDetails.sn == sn).first()
    return jsonify({"status": 200, "data": switch_ret_obj.push_ret})


@new_monitor_mold.route('/get_config_content/<string:task_name>')
def get_config_content(task_name):
    db_session = monitor_db.get_session()
    config_content_obj = db_session.query(PushConfigTask).filter(PushConfigTask.task_name == task_name).first()
    return jsonify({"status": 200, "data": config_content_obj.task_content})


@new_monitor_mold.route('/del_push_config/<string:task_name>')
def del_push_config(task_name):
    db_session = monitor_db.get_session()
    details_to_delete = db_session.query(PushConfigTaskDetails).filter(PushConfigTaskDetails.name == task_name).all()
    if details_to_delete:
        for detail in details_to_delete:
            db_session.delete(detail)
    db_session.query(PushConfigTask).filter(PushConfigTask.task_name == task_name).delete()
    return jsonify({"status": 200})


@new_monitor_mold.route('/read_config/<int:task_id>')
def read_push_config(task_id):
    db_session = monitor_db.get_session()
    db_session.query(PushConfigTask).filter(PushConfigTask.id == task_id).update({"read_tag": 1})
    return jsonify({"status": 200})



# @monitor_mold.route('/read_all_config')
# def read_all_config():
#     db_session = monitor_db.get_session()
#     db_session.query(PushConfigTask).update({"read_tag": 1})
#     return 'ok'


@new_monitor_mold.route('/get_push_config_task_items/<task_name>', methods=['GET'])
def get_push_config_task_items(task_name):
    db_session = monitor_db.get_session()
    push_config_task_details = db_session.query(PushConfigTaskDetails).filter(
        PushConfigTaskDetails.name == task_name).all()
    config_data_list = list()
    for dt in push_config_task_details:
        config_data = dict()
        config_data['sn'] = dt.sn
        config_data['status'] = dt.push_status
        config_data['push_time'] = dt.push_time
        config_data_list.append(config_data)
    return jsonify({"status": 200, "data": config_data_list})


# @monitor_mold.route('/operation_log/export_backend_log')
# def export_backend_log():
#     logPath = "/var/log/automation/gunicorn_app_server.log"
#     return send_file(logPath, as_attachment=True, download_name='system.log')

DATE_FORMAT = '%Y-%m-%d %H:%M:%S'


@new_monitor_mold.route('/get_switch_alarm', methods=['POST'])
def get_switch_alarm():
    return get_common_switch_alarm()


@new_monitor_mold.route('/get_history_switch_alarm', methods=['POST'])
def get_history_switch_alarm():
    return get_common_switch_alarm("history")


def get_common_switch_alarm(tag=None):
    info = {}
    try:
        data = request.get_json()
        start_time = data.get("startTime", "")
        end_time = data.get("endTime", "")
        session = monitor_db.get_session()
        otn_list = session.query(OtnDeviceBasic.ip, OtnDeviceBasic.model).all()
        event_objs = session.query(Event).filter(Event.type != "info")
        otns = {ip: model for ip, model in otn_list}
        if start_time:
            start_time = datetime.fromtimestamp(start_time).strptime(DATE_FORMAT)
            event_objs = session.query(Event).filter(Event.create_time >= start_time)

        if end_time:
            end_time = datetime.fromtimestamp(end_time).strptime(DATE_FORMAT)
            event_objs = event_objs.filter(Event.create_time <= end_time).all()

        if tag == "history":
            event_objs = event_objs.filter(Event.status != "unread")
        else:
            event_objs = event_objs.filter(Event.status == "unread")

        warn_level = {
            "warn": "MAJOR",
            "error": "CRITICAL",
            "info": "WARNING"
        }

        resp = list()
        for event_obj in event_objs:
            ret = dict()
            if event_obj.sn in otns:
                ret["ne_id"] = f"{event_obj.sn}"
                ret["data"] = dict()
                ret["data"]["id"] = event_obj.id
                ret["data"]["state"] = dict()
                ret["data"]["state"]["resource"] = event_obj.resource_name if event_obj.resource_name else otns.get(event_obj.sn)
                ret["data"]["state"]["deviceType"] = 3
            else:
                ret["ne_id"] = f"switch:{event_obj.id}:{event_obj.sn}"
                ret["data"] = dict()
                ret["data"]["id"] = event_obj.id
                ret["data"]["state"] = dict()
                ret["data"]["state"]["resource"] = "switch"
                ret["data"]["state"]["deviceType"] = 1

            ret["data"]["state"]["text"] = event_obj.msg
            ret["data"]["state"]["time-created"] = str(int(event_obj.create_time.timestamp() * 10**9)) if event_obj.create_time else ""
            ret["data"]["state"]["time-cleared"] = str(int(event_obj.modified_time.timestamp() * 10**9)) if event_obj.modified_time else ""
            ret["data"]["state"]["time-acknowledged"] = str(int(event_obj.operator_time.timestamp() * 10**9)) if event_obj.operator_time else ""
            ret["data"]["state"]["operator-name"] = event_obj.operator_name
            ret["data"]["state"]["operator-text"] = event_obj.operator_text
            ret["data"]["state"]["severity"] = warn_level.get(event_obj.type, "")
            ret["data"]["state"]["alarm-abbreviate"] = "/"
            ret["data"]["state"]["alarm-count"] = event_obj.count
            resp.append(ret)
    except Exception as e:
        LOG.error(f"get_switch_alarm error: {traceback.format_exc()}")
        info = {"status": 500, "msg": "Get_switch_alarm error"}
    else:
        info = {"status": 200, "data": resp}
    finally:
        return jsonify(info)


@new_monitor_mold.route('/clear_switch_history_alarm', methods=['post'])
def clear_history_switch_alarm():
    info = request.get_json()
    id_list = info.get("idList", '')
    fail_list = []
    success_list = []
    for item in id_list:
        alarm_data_by_id = monitor_db.get_model(Event, filters={'id': [item]})
        if not alarm_data_by_id:
            fail_list.append(item)
        else:
            success_list.append(item)
    if len(success_list) > 0:
        monitor_db.delete_collection(Event, filters={'id': success_list})
        if len(fail_list) > 0:
            info = {"status": 200, "msg": "Success clear {} switch history alarm, but {} failed, maybe no switch "
                                          "history alarm".format(len(success_list), len(fail_list))}
        else:
            info = {"status": 200, "msg": "Success clear {} switch history alarm".format(len(success_list))}
    else:
        info = {"status": 500, "msg": "No switch history alarm with id {}".format(fail_list)}
    return jsonify(info)


@new_monitor_mold.route('/ack_switch_alarm', methods=['POST'])
def ack_switch_alarm():
    info = {}
    try:
        data = request.get_json()
        event_id = data.get("eventId", "")
        operator_name = data.get("operatorName", "")
        operator_text = data.get("operatorText", "")
        session = monitor_db.get_session()
        session.query(Event).filter(Event.id == event_id).update({"status": "read", "operator_name": operator_name, "operator_text": operator_text, "operator_time": datetime.now()})
    except Exception as e:
        LOG.error(f"ack_switch_alarm error: {traceback.format_exc()}")
        info = {"status": 500, "msg": "Ack_switch_alarm error"}
    else:
        info = {"status": 200, "msg": "Ack_switch_alarm success"}
    finally:
        return jsonify(info)


@new_monitor_mold.route('/get_all_alarm', methods=['post'])
def get_all_alarm():
    info = {}
    try:
        session = monitor_db.get_session()
        pre_query = session.query(Event).filter(or_(Event.status == "read", Event.status == "unread"))
        page_num, page_size, total_count, alarms_list = utils.query_helper(Event, pre_query=pre_query)
    except Exception as e:
        info = {"status": 500, "msg": "Get_all_alarm error"}
    else:
        info = {"data": [pk.make_dict() for pk in alarms_list],
                "page": page_num,
                "pageSize": page_size,
                "total": total_count,
                "status": 200}
    finally:
        return jsonify(info)


@new_monitor_mold.route('/update_alarm_status', methods=['post'])
def update_alarm_status():
    info = {}
    data = request.get_json()
    try:
        alarm_id = data.get("id", "")
        db_session = monitor_db.get_session()
        alarm_event = db_session.query(Event).filter(Event.id == alarm_id)
        if alarm_event:
            monitor_db.update_event_status(alarm_id, "read")
            info = {"status": 200, "msg": "Update alarm status success"}
        else:
            info = {"status": 500, "msg": "Update alarm status failed"}
    except Exception as e:
        info = {"status": 500, "msg": "Update alarm status failed"}
    finally:
        return jsonify(info)

@new_monitor_mold.route('/get_unread_alarm', methods=['post'])
def get_unread_alarm():
    info = {}
    try:
        session = monitor_db.get_session()
        pre_query = session.query(Event).filter(Event.status == "unread")
        page_num, page_size, total_count, alarms_list = utils.query_helper(Event, pre_query=pre_query)
    except Exception as e:
        info = {"status": 500, "msg": "Get_unread_alarm error"}
    else:
        info = {"data": [pk.make_dict() for pk in alarms_list],
                "unread_data": [item.make_dict() for item in pre_query],
                "page": page_num,
                "pageSize": page_size,
                "total": total_count,
                "status": 200}
    finally:
        return jsonify(info)

@new_monitor_mold.route("/delete_alarm/<int:alarm_id>", methods=['POST'])
def delete_alarm(alarm_id):
    db_session = monitor_db.get_session()
    try:
        db_session.begin()
        alarm = db_session.query(Event).filter_by(id=alarm_id).first()
        if not alarm:
            info = {"status": 404, "msg": "Alarm not found"}
            raise ValueError(info["msg"])
        db_session.delete(alarm)
        db_session.commit()
        info = {"status": 200, "msg": "Delete Alarm Successful!"}
    except Exception as e:
        print(str(e))
        db_session.rollback()
        info = {"status": 500, "msg": "Failed to delete alarm: {}".format(str(e))}
    finally:
        db_session.close()
    return jsonify(info)


@new_monitor_mold.route("/alert_log", methods=['POST', 'GET'])
def alert_log():
    content = request.data
    data = json.loads(content)
    for alert in data.get("alerts", []):
        monitor_db.add_event(alert["labels"]["target"], alert["labels"]["severity"], alert["annotations"]["description"])
    return jsonify({"status": 200})


# Prometheus
@new_monitor_mold.route('/get_montior_target', methods=['GET'])
def get_montior_target():
    try:
        session_query = monitor_db.get_session()
        db_session = inven_db.get_session()

        monitor_query = session_query.query(MonitorTarget).filter(MonitorTarget.device_type == 1).all()
        res = []
        for target in monitor_query:
            config = db_session.query(SystemConfig).filter(SystemConfig.id == target.switch.system_config_id).first()
            if target.switch.mgt_ip and target.switch.status in [constants.SwitchStatus.PROVISIONING_SUCCESS, constants.SwitchStatus.IMPORTED]:
                info = {
                    "name": target.sn,
                    "address": target.switch.mgt_ip + ":" + str(target.port),
                    "username": config.switch_op_user,
                    "password": encrypt_with_static_key(config.switch_op_password),
                    "enable": target.enable
                }
                res.append(info)
        msg = {'status': 200, 'data': res}
        return jsonify(msg)
    except Exception as e:
        LOG.error(traceback.format_exc())
        msg = {'info': 'Failed to get monitor target', 'status': 500, 'data': []}
        return jsonify(msg)


@new_monitor_mold.route('/add_topology', methods=['POST'])
@admin_permission.require(http_exception=403)
def add_topology():
    try:
        data = request.get_json()
        topology_name = data.get("name")
        description = data.get("description")
        if topology_name == "" or len(topology_name) > 32 or " " in topology_name:
            msg = {'info': 'Topology name is invalid', 'status': 500}
            return jsonify(msg)
        if len(description) > 128:
            msg = {'info': 'Topology description is invalid', 'status': 500}
            return jsonify(msg)
        session = monitor_db.get_session()
        if session.query(Topology).filter(Topology.name == topology_name, Topology.topology_type == 'topology').first():
            msg = {'info': 'Topology name already exists', 'status': 500}
            return jsonify(msg)
        topology = Topology(name=topology_name, description=description, topology_type='topology')
        monitor_db.insert(topology, session)
        msg = {'info': 'Topology Added Successfully', 'status': 200}
        return jsonify(msg)
    except Exception as e:
        LOG.error(traceback.format_exc())
        msg = {'info': 'Topology add fail', 'status': 500}
        return jsonify(msg)


@new_monitor_mold.route('/edit_topology', methods=['POST'])
@admin_permission.require(http_exception=403)
def edit_topology():
    try:
        data = request.get_json()
        topology_id = data.get("id")
        topology_name = data.get("name")
        description = data.get("description")
        session = monitor_db.get_session()
        target_topology = session.query(Topology).filter(Topology.id == topology_id).first()
        if not target_topology:
            msg = {'info': 'Topology not exists', 'status': 500}
            return jsonify(msg)
        if len(description) > 128:
            msg = {'info': 'Topology description is invalid', 'status': 500}
            return jsonify(msg)
        if topology_name == "" or len(topology_name) > 32 or " " in topology_name:
            msg = {'info': 'Topology name is invalid', 'status': 500}
            return jsonify(msg)
        with session.begin(subtransactions=True):
            target_topology.name = topology_name
            target_topology.description = description
        msg = {'info': 'Topology save successfully', 'status': 200}
        return jsonify(msg)
    except Exception as e:
        LOG.error(traceback.format_exc())
        msg = {'info': 'Topology save fail', 'status': 500}
        return jsonify(msg)


@new_monitor_mold.route('/get_topology_list', methods=['GET'])
def get_topology_list():
    try:
        session = monitor_db.get_session()
        query = session.query(Topology).all()
        res = {
            'topology': [{"id": topology.id, "name": topology.name, "description": topology.description, "isShowDefault": topology.is_show_default} for topology in query if topology.topology_type == 'topology'],
            'fabric': [{"id": topology.id, "name": topology.name, "description": topology.description, "isShowDefault": topology.is_show_default} for topology in query if topology.topology_type == 'fabric'],
            'site': [{"id": topology.id, "name": topology.name, "description": topology.description, "isShowDefault": topology.is_show_default} for topology in query if topology.topology_type == 'site']
        }
        msg = {'info': 'Topology Added Successfully', 'status': 200, "data": res}
        return jsonify(msg)
    except Exception as e:
        LOG.error(traceback.format_exc())
        msg = {'info': 'Topology get fail', 'status': 500}
        return jsonify(msg)


@new_monitor_mold.route('/set_default_topology', methods=['POST'])
@admin_permission.require(http_exception=403)
def set_default_topology():
    data = request.get_json()
    session = monitor_db.get_session()
    topology_id = data.get("topologyId")
    is_show_default = data.get("isShowDefault")
    try:
        with session.begin():
            if is_show_default:
                session.query(Topology).update({"is_show_default": False})
            session.query(Topology).filter(Topology.id == topology_id).update({"is_show_default": is_show_default})
        msg = {'info': 'Topology set Successfully' if is_show_default else 'Topology unset Successfully', 'status': 200}
        return jsonify(msg)
    except Exception as e:
        session.rollback()
        LOG.error(traceback.format_exc())
        msg = {'info': 'Topology set fail', 'status': 500}
        return jsonify(msg)


@new_monitor_mold.route('/save_topology', methods=['POST'])
@admin_permission.require(http_exception=403)
def save_topology():
    data = request.get_json()
    topology_id = data.get("topologyId")
    topology_nodes = data.get("topologyNodes", None)
    topology_edges = data.get("topologyEdges", None)
    is_show_legend = data.get("isShowLegend", True)
    is_in_tree_mode = data.get("isInTreeMode", False)
    zoom = data.get("zoom", 1)
    translate_x = data.get("translateX", 1)
    translate_y = data.get("translateY", 1)

    session = monitor_db.get_session()
    topology = session.query(Topology).filter(Topology.id == topology_id).first()
    if not topology:
        msg = {'info': 'Topology not found', 'status': 500}
        return jsonify(msg)

    if is_show_legend not in [True, False]:
        msg = {'info': 'IsShowLegend is invalid', 'status': 500}
        return jsonify(msg)

    if is_in_tree_mode not in [True, False]:
        msg = {'info': 'IsInTreeMode is invalid', 'status': 500}
        return jsonify(msg)

    if str == type(zoom):
        try:
            zoom = float(zoom)
        except ValueError:
            msg = {'info': 'Zoom is invalid', 'status': 500}
            return jsonify(msg)

    if str == type(translate_x):
        try:
            translate_x = float(translate_x)
        except ValueError:
            msg = {'info': 'translateX is invalid', 'status': 500}
            return jsonify(msg)

    if str == type(translate_y):
        try:
            translate_y = float(translate_y)
        except ValueError:
            msg = {'info': 'TranslateY is invalid', 'status': 500}
            return jsonify(msg)

    try:
        with session.begin():
            topology.is_show_legend = is_show_legend
            topology.zoom = zoom
            topology.translate_x = translate_x
            topology.translate_y = translate_y
            topology.is_in_tree_mode = is_in_tree_mode
            if topology:
                # 删除原有的拓扑节点和拓扑边
                session.query(TopologyNode).filter(TopologyNode.topology_id == topology_id).delete()
                session.query(TopologyEdge).filter(TopologyEdge.topology_id == topology_id).delete()
                # 保存新的拓扑节点和拓扑边
                for node in topology_nodes:
                    new_node = TopologyNode(node_label=node["label"], topology_id=topology_id, monitor_target_id=node["monitorTargetId"], layer=node["layer"], position_x=node["positionX"], position_y=node["positionY"])
                    monitor_db.insert(new_node, session)
                # 获取新的拓扑节点label和id对应关系
                query_node = session.query(TopologyNode).filter(TopologyNode.topology_id == topology_id).all()
                node_id_map = {
                    node.monitor_target.name if node.monitor_target.device_type == 2 else node.monitor_target.sn: node.id
                    for node in query_node
                }
                for edge in topology_edges:
                    # ensure source_node_id < target_node_id
                    if node_id_map[edge["source"]] > node_id_map[edge["target"]]:
                        source_node_id = node_id_map[edge["target"]]
                        target_node_id = node_id_map[edge["source"]]
                        source_port = str(edge["targetPort"])
                        target_port = str(edge["sourcePort"])
                    else:
                        source_node_id = node_id_map[edge["source"]]
                        target_node_id = node_id_map[edge["target"]]
                        source_port = str(edge["sourcePort"])
                        target_port = str(edge["targetPort"])
                    source_node = session.query(TopologyNode).filter(TopologyNode.topology_id == topology_id) \
                                                             .filter(TopologyNode.id == source_node_id).first()
                    target_node = session.query(TopologyNode).filter(TopologyNode.topology_id == topology_id) \
                                                             .filter(TopologyNode.id == target_node_id).first()
                    new_edge = TopologyEdge(topology_id=topology_id, source_node=source_node, target_node=target_node, source_port=source_port, target_port=target_port)
                    monitor_db.insert(new_edge, session)
                # 开启节点的lldp监控        
                query_node = session.query(TopologyNode).filter(TopologyNode.topology_id == topology_id).all()
                for node in query_node:
                    if node.monitor_target.device_type == 1:
                        inven_db.update_switch_montior(node.monitor_target.sn, 1, lldp=True)
                msg = {'info': 'Topology save Successfully', 'status': 200}
            else:
                msg = {'info': 'Topology not found', 'status': 500}
        return jsonify(msg)
    except Exception as e:
        LOG.error(traceback.format_exc())
        session.rollback()
        msg = {'info': 'Topology save fail', 'status': 500}
        return jsonify(msg)


@new_monitor_mold.route('/get_topology', methods=['POST'])
def get_topology():
    try:
        data = request.get_json()
        topology_id = data.get("topologyId")
        date = data.get("date", None)
        if date:
            date = datetime.strptime(date, "%Y-%m-%d %H:%M:%S").timestamp()
        session = monitor_db.get_session()
        topology = session.query(Topology).filter(Topology.id == topology_id).first()
        if not topology:
            msg = {'info': 'Topology not found', 'status': 500}
            return jsonify(msg)
        query_node = session.query(TopologyNode).filter(TopologyNode.topology_id == topology_id).all()
        query_edge = session.query(TopologyEdge).filter(TopologyEdge.topology_id == topology_id).all()

        # 获取lldp状态，构建拓扑节点信息
        node_sn_list = []
        nodes = []
        otn_node_info = {}
        for node in query_node:
            # 暂时仅针对switch
            if node.monitor_target.device_type == 1:
                node_sn_list.append(node.monitor_target.sn)
                lldp_state = query_lldp_state(node.monitor_target.sn, node.monitor_target.switch.mac_addr, date)
                node_info = {
                    "id": node.id,
                    "label": node.node_label,
                    "switch_sn": node.monitor_target.sn,
                    "switch_id": node.monitor_target.switch.id,
                    "device_type": node.monitor_target.device_type,
                    "layer": node.layer,
                    "x": node.position_x,
                    "y": node.position_y,
                    "monitor_target_id": node.monitor_target_id,
                    "model": node.monitor_target.switch.platform_model,
                    "version": node.monitor_target.switch.version,
                    "mgt_ip": node.monitor_target.switch.mgt_ip,
                    "hwid": node.monitor_target.switch.hwid,
                    "status": "online" if node.monitor_target.switch.reachable_status == 0 else "offline" 
                }
                node_info.update(lldp_state)
                nodes.append(node_info)
            elif node.monitor_target.device_type == 2:
                otn_info = query_otn_platform_info(node.monitor_target.name)
                node_info = {
                    "id": node.id,
                    "label": node.node_label,
                    "ne_name": node.monitor_target.name,
                    "device_type": node.monitor_target.device_type,
                    "layer": node.layer,
                    "x": node.position_x,
                    "y": node.position_y,
                    "monitor_target_id": node.monitor_target_id,
                    "status": "online" if otn_info else "offline"
                }
                if otn_info:
                    otn_info.pop("subcomponent")
                    node_info.update(otn_info)
                    otn_node_info[node.id] = otn_info
                nodes.append(node_info)
            
        
        # 获取当前lldp neighbor信息，构建拓扑向量信息
        # 没去重 是否去重
        neighbors_state = {}
        if node_sn_list:
            neighbors_state = query_lldp_neighbor_state(node_sn_list, date)

        edges_source_target_feature = {}
         # 当前neighbor新增的链路信息是否需要展示，暂未展示
        for edge in query_edge:
            # otn设备只能被动发现作为target, 但数据库中可能为source, 必须用switch sn_port作为key
            if edge.source_node.monitor_target.device_type == 2:
                source_sn = edge.target_node.monitor_target.sn
                source_port = edge.target_port
            else:
                source_sn = edge.source_node.monitor_target.sn
                source_port = edge.source_port
            key = f"{source_sn}_{source_port}"
            edge_info = {}
            neighbor = neighbors_state.get(key, {})
            if neighbor:
                # source为otn时需要用source的mac和port比较
                if edge.source_node.monitor_target.device_type == 2 and edge.source_node.monitor_target.mac.lower() == neighbor["target_mac"].lower() and edge.source_port == neighbor["target_port"]:
                    edge_info["status"] = "up"
                elif edge.target_node.monitor_target.device_type == 2 and edge.target_node.monitor_target.mac.lower() == neighbor["target_mac"].lower() and edge.target_port == neighbor["target_port"]:
                    edge_info["status"] = "up"
                elif edge.target_node.monitor_target.switch.mac_addr.lower() == neighbor["target_mac"].lower() and edge.target_port == neighbor["target_port"]:
                    edge_info["status"] = "up"
                else:
                    edge_info["status"] = "down"
            else:
                # 当前lldp neighbor中不存在这条
                edge_info["status"] = "down"
            if frozenset([edge.topology_id, edge.target_id, edge.source_id]) in edges_source_target_feature.keys():
                if edge_info["status"] != edges_source_target_feature[frozenset([edge.topology_id, edge.target_id, edge.source_id])]["status"]:
                    edges_source_target_feature[frozenset([edge.topology_id, edge.target_id, edge.source_id])]["status"] = "mixed"
                edges_source_target_feature[frozenset([edge.topology_id, edge.target_id, edge.source_id])][
                    "port_info"].append(
                    {"source_port": edge.source_port, "target_port": edge.target_port, "status": edge_info["status"]}
                )
            else:
                edges_source_target_feature[frozenset([edge.topology_id, edge.target_id, edge.source_id])] = {
                    "source": edge.source_id, 
                    'source_sn': otn_node_info[edge.source_id].get("sn", None) if edge.source_node.monitor_target.device_type == 2 else edge.source_node.monitor_target.sn,
                    "source_label": edge.source_node.node_label,
                    "target": edge.target_id, 
                    'target_sn': otn_node_info[edge.target_id].get("sn", None) if edge.target_node.monitor_target.device_type == 2 else edge.target_node.monitor_target.sn,
                    "target_label": edge.target_node.node_label,
                    "source_mac_addr": edge.source_node.monitor_target.mac if edge.source_node.monitor_target.device_type == 2 else edge.source_node.monitor_target.switch.mac_addr,
                    "target_mac_addr": edge.target_node.monitor_target.mac if edge.target_node.monitor_target.device_type == 2 else edge.target_node.monitor_target.switch.mac_addr,
                    "status": edge_info["status"],
                    "port_info": [
                        {"source_port": edge.source_port, "target_port": edge.target_port, "status": edge_info["status"]}
                    ]
                }
        edges = list(edges_source_target_feature.values())
        query_topology = session.query(Topology).filter(Topology.id == topology_id).first()
        res = {
            "isShowLegend": query_topology.is_show_legend if query_topology else True,
            "zoom": query_topology.zoom if query_topology else 1,
            "translateX": query_topology.translate_x if query_topology else 0,
            "translateY": query_topology.translate_y if query_topology else 0,
            "isInTreeMode": query_topology.is_in_tree_mode if query_topology else False,
            "nodes": nodes,
            "edges": edges
        }
        msg = {'status': 200, 'data': res}
        return jsonify(msg)
    except Exception as e:
        LOG.error(traceback.format_exc())
        msg = {'info': 'Topology get fail', 'status': 500}
        return jsonify(msg)


@new_monitor_mold.route('/del_topology', methods=['POST'])
@admin_permission.require(http_exception=403)
def del_topology():
    try:
        data = request.get_json()
        topology_id = data.get("id")
        session = monitor_db.get_session()
        topology = session.query(Topology).filter(Topology.id == topology_id).first()
        if not topology:
            msg = {'info': 'Topology not found', 'status': 500}
            return jsonify(msg)
        session.query(Topology).filter(Topology.id == topology_id).delete()
        # 检查是否需要关闭lldp监控
        need_close_lldp_targets = session.query(MonitorTarget) \
                                         .outerjoin(TopologyNode, MonitorTarget.id == TopologyNode.monitor_target_id) \
                                         .filter(MonitorTarget.device_type == 1) \
                                         .filter(TopologyNode.monitor_target_id.is_(None)).all()
        for target in need_close_lldp_targets:
            inven_db.update_switch_montior(target.sn, 1, lldp=False)
        msg = {'info': 'Topology delete success', 'status': 200}
        return jsonify(msg)
    except Exception as e:
        LOG.error(traceback.format_exc())
        msg = {'info': 'Topology delete fail', 'status': 500}
        return jsonify(msg)


@new_monitor_mold.route('/refresh_lldp_info', methods=['POST'])
@admin_permission.require(http_exception=403)
def refresh_lldp_info():
    data = request.get_json()
    monitor_target_ids = data.get("monitorTargetIds", [])
    session = monitor_db.get_session()
    db_session = inven_db.get_session()
    try:
        # 目前只做switch侧的自动发现
        query_node = session.query(MonitorTarget).filter(MonitorTarget.id.in_(monitor_target_ids)).filter(MonitorTarget.device_type == 1).all()
        neighbors = []

        futures = []
        with ThreadPoolExecutor(max_workers=10) as executor:
            for monitor_target in query_node:
                system_config = db_session.query(SystemConfig).filter(SystemConfig.id == monitor_target.switch.system_config_id).first()
                futures.append(executor.submit(get_lldp_neighbor, monitor_target.switch.mgt_ip, system_config.switch_op_user, system_config.switch_op_password))

            for future in as_completed(futures):
                results = future.result()
                neighbors.extend(results)

        neighbors_info = {}
        for neighbor in neighbors:
            source_switch = session.query(MonitorTarget).join(MonitorTarget.switch).filter(Switch.mac_addr == neighbor["source_mac"]).first()
            target_switch = session.query(MonitorTarget).join(MonitorTarget.switch).filter(Switch.mac_addr == neighbor["target_mac"]).first()
            # 不在switch表，查看是否是OTN设备, switch单侧自动发现只匹配target是否是OTN
            if not target_switch:
                target_switch = session.query(MonitorTarget).filter(MonitorTarget.mac == neighbor["target_mac"]).first()
            # 不在监控范围内的数据暂时不存
            if source_switch and target_switch:
                key = f"{source_switch.id}_{target_switch.id}"
                link_info = f'{neighbor["source_port"]}_{neighbor["target_port"]}'

                # 用于去重
                dup_key = f"{target_switch.id}_{source_switch.id}"
                dup_link_info = f'{neighbor["target_port"]}_{neighbor["source_port"]}'

                if key not in neighbors_info and dup_key not in neighbors_info:
                    neighbors_info[key] = []
                elif dup_key in neighbors_info:
                    key = dup_key
                    link_info = dup_link_info

                if link_info not in neighbors_info[key]:
                    neighbors_info[key].append(link_info)

        res = []
        for key, value in neighbors_info.items():
            port_info = []
            for port_pair in value:
                source_port, target_port = port_pair.split('_')
                port_info.append({'target_port': target_port, 'source_port': source_port, "status": "up"})

            source_monitor_id, target_monitor_id = key.split('_')
            info = {
                "port_info": port_info,
                "source_monitor_id": int(source_monitor_id), # 这里用monitor_target_id 没有使用sn 为了便于后续扩展otn设备
                "target_monitor_id": int(target_monitor_id)
            }
            res.append(info)
        msg = {'info': 'Topology refresh success', 'status': 200, 'data': res}
        return jsonify(msg)
    except Exception as e:
        LOG.error(traceback.format_exc())
        msg = {'info': 'Topology refresh fail', 'status': 500}
        return jsonify(msg)


@new_monitor_mold.route('/get_add_device_modal_table_data', methods=['POST'])
@admin_permission.require(http_exception=403)
def get_add_device_modal_table_data():
    try:
        data = request.get_json()
        sn_list = data.get("snList", [])
        page_num, page_size, total_count, query_obj = utils.query_helper(Switch, pre_query=utils.query_switch(sn_list=sn_list, is_show_active_only=True))
        if not sn_list:
            response = {
                "data": [{
                    "id": switch.id,
                    "host_name": switch.host_name,
                    "sn": switch.sn,
                    "create_time": switch.create_time.strftime('%Y-%m-%d %H:%M:%S') if switch.create_time else "",
                    "version": switch.version,
                    "revision": switch.revision,
                    "status": switch.status,
                    "mgt_ip": switch.mgt_ip,
                    "platform_model": switch.platform_model,
                    "selected": False
                } for switch in query_obj],
                "page": page_num,
                "pageSize": page_size,
                "total": total_count,
                "status": 200
            }
        else:
            response = {
                "data": [{
                    "id": switch.id,
                    "host_name": switch.host_name,
                    "sn": switch.sn,
                    "create_time": switch.create_time.strftime('%Y-%m-%d %H:%M:%S') if switch.create_time else "",
                    "version": switch.version,
                    "revision": switch.revision,
                    "status": switch.status,
                    "mgt_ip": switch.mgt_ip,
                    "platform_model": switch.platform_model,
                    "selected": selected
                } for switch, selected in query_obj],
                "page": page_num,
                "pageSize": page_size,
                "total": total_count,
                "status": 200
            }
        return jsonify(response)
    except Exception as e:
        LOG.error(traceback.format_exc())
        msg = {'info': 'Get add device modal table data fail', 'status': 500}
        return jsonify(msg)


@new_monitor_mold.route('/get_topology_to_be_added_switch', methods=['POST'])
@admin_permission.require(http_exception=403)
def get_topology_to_be_added_switch():
    try:
        data = request.get_json()
        switch_list_id = data.get("switchIdList", [])
        otn_ip_list = data.get("otnIpList", [])  # otn设备预留 需要传入otn设备ip
        session = monitor_db.get_session()
        nodes = []

        if otn_ip_list:
            failed_otn_list = []
            for otn_ip in otn_ip_list:
                # 调用 otn api 获取 lldp chassis-id
                lldp_info = query_otn_lldp_info(otn_ip)
                if lldp_info and lldp_info.get("state", {}).get("chassis-id", ""):
                    mac = lldp_info.get("state", {}).get("chassis-id", "")
                    inven_db.update_switch_montior(otn_ip, 2, mac=mac.replace('-', ':').lower())
                else:
                    failed_otn_list.append(otn_ip)

            if failed_otn_list:
                failed_otn = ",".join(failed_otn_list)
                msg = {'info': f"{failed_otn} get lldp info failed, can not add to topology", 'status': 500}
                return jsonify(msg)

            query_target = session.query(MonitorTarget).filter(MonitorTarget.name.in_(otn_ip_list)).all()
            for index, monitor_target in enumerate(query_target):
                otn_info = query_otn_platform_info(monitor_target.name)
                node_info = {
                    "id": str(uuid.uuid4()),
                    "label": monitor_target.name,
                    "ne_name": monitor_target.name,
                    "monitor_target_id": monitor_target.id if monitor_target else None,
                    "device_type": monitor_target.device_type if monitor_target else None,
                    "layer": 0,
                    "x": 50 + index * 100,
                    "y": 150,
                }
                if otn_info:
                    otn_info.pop("subcomponent")
                    node_info.update(otn_info)
                nodes.append(node_info)
        
        if switch_list_id:
            query_node = session.query(Switch, MonitorTarget).outerjoin(MonitorTarget, Switch.sn == MonitorTarget.sn).filter(Switch.id.in_(switch_list_id)).all()
            for index, node in enumerate(query_node):
                switch_info, monitor_target = node
                lldp_info = query_lldp_state(switch_info.sn, switch_info.mac_addr)
                node_info = {
                    "id": str(uuid.uuid4()),
                    "label": switch_info.host_name if switch_info.host_name else 'PICOS',
                    "switch_sn": switch_info.sn,
                    "switch_id": switch_info.id,
                    "monitor_target_id": monitor_target.id if monitor_target else None,
                    "device_type": monitor_target.device_type if monitor_target else None,
                    "model": switch_info.platform_model,
                    "version": switch_info.version,
                    "mgt_ip": switch_info.mgt_ip,
                    "mac_addr": switch_info.mac_addr,
                    "layer": 0,
                    "x": 50 + index * 100,
                    "y": 50,
                    "status": "online" if switch_info.reachable_status == 0 else "offline"
                }
                node_info.update(lldp_info)
                nodes.append(node_info)
        msg = {'data': nodes, 'status': 200}
        return jsonify(msg)
    except Exception as e:
        LOG.error(traceback.format_exc())
        msg = {'info': 'Get topology to be added switch fail', 'status': 500}
        return jsonify(msg)


def get_lldp_neighbor(target_ip, username, passwd, port="9339"):
    res = []
    try:
        with gNMIclient(target=(target_ip, port), username=username, password=passwd, timeout=15) as gc:
            result = gc.get(path=['openconfig-lldp:lldp'])
            for notification in result["notification"]:
                for update in notification["update"]:
                    lldp_interface = update['val']['openconfig-lldp:interfaces']['interface']
                    for interface in lldp_interface:
                        if interface.get("neighbors", {}):
                            for neighbor in interface["neighbors"]["neighbor"]:
                                info = {
                                    "source_mac": update['val']['openconfig-lldp:state']['chassis-id'],
                                    "target_mac": neighbor["state"]['chassis-id'],
                                    "source_port": interface["name"],
                                    "target_port": neighbor["state"]['port-id']
                                }
                                res.append(info)
    except Exception as e:
        LOG.error(traceback.format_exc())
    return res


@new_monitor_mold.route('/get_interfaces_topk', methods=['POST'])
def get_interfaces_topk():
    try:
        data =  request.get_json()
        metric_name = data.get("metricName", None) #in-octets in-pkts in-discards in-error in-fcs-errors out-octets out-discards out-errors
        topk = data.get("topK", 5)
        target = data.get("target", None)
        start_time = data.get("startTime", None)
        if start_time:
            start_time = datetime.strptime(start_time, timestamp_format).timestamp()
        end_time = data.get("endTime", None)
        if end_time:
            end_time = datetime.strptime(end_time, timestamp_format).timestamp()

        if metric_name is None:
            msg = {'info': 'MetricName is None', 'status': 500}
            return jsonify(msg)

        if "ecn" in metric_name:
            res = query_counter_delta_topk(metric_name.replace("-", "_"), metric_prefix="openconfig_qos:qos_interfaces_interface_state_fsconfig_qos_ai_extensions:",
                                           topk=topk, target=target, start_time=start_time, end_time=end_time)
        elif "pfc" in metric_name:
            res = query_counter_delta_topk(metric_name.replace("-", "_"), metric_prefix="openconfig_qos:qos_interfaces_interface_output_queues_queue_state_fsconfig_qos_ai_extensions:",
                                           topk=topk, target=target, start_time=start_time, end_time=end_time)
        else:
            res = query_counter_delta_topk(metric_name.replace("-", "_"), topk=topk, target=target, start_time=start_time, end_time=end_time)
            
        if metric_name in ["load-interval", "out-bits-rate", "in-bits-rate", "out-pkts-rate", "in-pkts-rate"]:
            res = query_rate_topk("openconfig_interfaces:interfaces_interface_openconfig_if_rates:rates_state_" + metric_name.replace("-", "_"),
                                  topk=topk, target=target, start_time=start_time, end_time=end_time)

        msg = {'data': res, 'status': 200}
        return jsonify(msg)
    except Exception as e:
        LOG.error(traceback.format_exc())
        msg = {'info': 'Interfaces counters fail', 'status': 500}
        return jsonify(msg)

@new_monitor_mold.route('/get_interface_info', methods=['POST'])
def get_interface_info():
    try:
        data =  request.get_json()
        target = data.get("target", None)
        date = data.get("date", None)
        if date:
            date = datetime.strptime(date, "%Y-%m-%d %H:%M:%S").timestamp()
        interface_list = get_target_interfaces(target, date)
        interface_config = query_metric_filter_by_interface("openconfig_interfaces:interfaces_interface_config", target, interface_list, date)
        ethernet_config = query_metric_filter_by_interface("openconfig_interfaces:interfaces_interface_openconfig_if_ethernet:ethernet_config", target, interface_list, date)
        interface_state = query_metric_filter_by_interface("openconfig_interfaces:interfaces_interface_state", target, interface_list, date)
        etherenr_state = query_metric_filter_by_interface("openconfig_interfaces:interfaces_interface_openconfig_if_ethernet:ethernet_state", target, interface_list, date)
        counters_list = query_counters_with_prefix("openconfig_interfaces:interfaces_interface_state_counters_", target, interface_list, date)
        ethernet_list = query_counters_with_prefix("openconfig_interfaces:interfaces_interface_openconfig_if_ethernet:ethernet_state_counters_", target, interface_list, date)

        in_bits_port_speed_usage = get_port_speed_usage("openconfig_interfaces:interfaces_interface_openconfig_if_rates:rates_state_in_bits_rate", target, interface_list, "in_bits_port_speed_usage", date)
        out_bits_port_speed_usage = get_port_speed_usage("openconfig_interfaces:interfaces_interface_openconfig_if_rates:rates_state_out_bits_rate", target, interface_list, "out_bits_port_speed_usage", date)

        rates_state = query_counters_with_prefix("openconfig_interfaces:interfaces_interface_openconfig_if_rates:rates_state_", target, interface_list, date)

        interface_info = {
            key: {
                "config": interface_config.get(key, {}),
                "ethernet_config": ethernet_config.get(key, {}),
                "state": {
                    **interface_state.get(key, {}),
                    "counters": counters_list.get(key, {}),
                },
                "ethernet_state": {
                    **etherenr_state.get(key, {}),
                    "counters": ethernet_list.get(key, {}),
                    **in_bits_port_speed_usage.get(key, {}),
                    **out_bits_port_speed_usage.get(key, {}),
                    **rates_state.get(key, {})
                }
            }
            for key in set(interface_config) | set(ethernet_config) | set(interface_state) | set (etherenr_state) | set(counters_list) | set(ethernet_list)
        }

        msg = {'data': interface_info, 'status': 200}
        return jsonify(msg)
    except Exception as e:
        LOG.error(traceback.format_exc())
        msg = {'info': 'Get Interfaces info fail', 'status': 500}
        return jsonify(msg)

@new_monitor_mold.route('/get_modules_topk', methods=['POST'])
def get_modules_topk():
    try:
        data =  request.get_json()
        metric_name = data.get("metricName", None) #input-power output-power laser-temperature attenuation
        topk = data.get("topK", 5)
        target = data.get("target", None)
        start_time = data.get("startTime", None)
        if start_time:
            start_time = datetime.strptime(start_time, timestamp_format).timestamp()
        end_time = data.get("endTime", None)
        if end_time:
            end_time = datetime.strptime(end_time, timestamp_format).timestamp()

        if metric_name is None:
            msg = {'info': 'MetricName is None', 'status': 500}
            return jsonify(msg)

        if metric_name in ["input-power", "output-power"]:
            res = query_modules_topk("openconfig_platform:components_component_openconfig_platform_transceiver:transceiver_state_" + metric_name.replace("-", "_"),
                                     topk=topk, target=target, start_time=start_time, end_time=end_time, mode="bottomK")
        elif metric_name == "laser-temperature":
            res = query_modules_topk("openconfig_platform:components_component_openconfig_platform_transceiver:transceiver_state_fsconfig_platform_transceiver_extensions:" + metric_name.replace("-", "_"),
                                     topk=topk, target=target, start_time=start_time, end_time=end_time)
        else:
            res = query_attenuation_topk(topk=topk, target=target, start_time=start_time, end_time=end_time)

        msg = {'data': res, 'status': 200}
        return jsonify(msg)
    except Exception as e:
        LOG.error(traceback.format_exc())
        msg = {'info': 'Interfaces counters fail', 'status': 500}
        return jsonify(msg)


@new_monitor_mold.route('/get_modules_info', methods=['POST'])
def get_modules_info():
    try:
        data =  request.get_json()
        target = data.get("target", None)
        date = data.get("date", None)
        if date:
            date = datetime.strptime(date, "%Y-%m-%d %H:%M:%S").timestamp()
        interface_list = get_target_interfaces(target, date)
        transceiver_state = query_metric_filter_by_interface("openconfig_platform:components_component_openconfig_platform_transceiver:transceiver_state", target, interface_list, date)
        input_power = query_counters("openconfig_platform:components_component_openconfig_platform_transceiver:transceiver_state_input_power", target, interface_list, date)
        output_power = query_counters("openconfig_platform:components_component_openconfig_platform_transceiver:transceiver_state_output_power", target, interface_list, date)
        temperature = query_counters("openconfig_platform:components_component_openconfig_platform_transceiver:transceiver_state_fsconfig_platform_transceiver_extensions:laser_temperature", target, interface_list, date)

        interface_info = {
            key: {
                "transceiver_state": {
                    **transceiver_state.get(key, {}),
                    **({ "input_power": input_power[key] } if input_power.get(key) is not None else {}),
                    **({ "output_power": output_power[key] } if output_power.get(key) is not None else {}),
                    **({ "temperature": temperature[key] } if temperature.get(key) is not None else {}),
                    **({"attenuation": round(output_power[key] - input_power[key], 2)} if input_power.get(key) is not None and output_power.get(key) is not None else {})
                }
            }
            for key in set(transceiver_state) | set(input_power) | set(output_power) | set (temperature)
        }

        msg = {'data': interface_info, 'status': 200}
        return jsonify(msg)
    except Exception as e:
        LOG.error(traceback.format_exc())
        msg = {'info': 'Get modules info fail', 'status': 500}
        return jsonify(msg)

@new_monitor_mold.route('/get_ai_info', methods=['POST'])
def get_ai_info():
    try:
        data =  request.get_json()
        target = data.get("target", None)
        date = data.get("date", None)

        if date:
            date = datetime.strptime(date, "%Y-%m-%d %H:%M:%S").timestamp()
        interface_list = get_target_interfaces(target, date)
        pfc_info = query_ai_with_prefix("openconfig_qos:qos_interfaces_interface_output_queues_queue_state_fsconfig_qos_ai_extensions:", target, interface_list, date)
        ecn_info = query_ai_with_prefix("openconfig_qos:qos_interfaces_interface_state_fsconfig_qos_ai_extensions:", target, interface_list, date)

        merged_infos = [
            {**pfc_info.get(interface, {}).get(q), **{'queue_name': q}, **{'interface_name': interface}, **ecn_info.get(interface, {})}
            for interface in interface_list
            for q in set(pfc_info.get(interface, {}))
        ]

        sorted_merged_infos = sorted(
            merged_infos,
            key=lambda x: (x['interface_name'], x['queue_name'])
        )

        msg = {'data': sorted_merged_infos, 'status': 200}
        return jsonify(msg)
    except Exception as e:
        LOG.error(traceback.format_exc())
        msg = {'info': 'Get ai info fail', 'status': 500}
        return jsonify(msg)


@new_monitor_mold.route('/get_otn_port_info', methods=['POST'])
def get_otn_port_info():
    try:
        data =  request.get_json()
        otn_ip = data.get("otnIp", None)
        
        otn_info = query_otn_platform_info(otn_ip)
        slots = otn_info["subcomponent"]
        card_info_list = []
        if slots:
            for slot in slots:
                card_name = slot["name"]
                slot_num = int(card_name[card_name.rindex("-") + 1:]) if "-" in card_name else -1
                if slot_num >= 1 and slot_num <= 8:
                    card_info = query_otn_card_info(otn_ip, card_name)
                    actual_type = card_info.get("state", {}).get("actual-vendor-type")
                    preconf_type = card_info.get("config", {}).get("vendor-type-preconf", None)
                    info = {
                        "slot_no": slot_num,
                        "name": card_name,
                        "card_type": card_name[:card_name.find("-")] if card_name.find("-") != -1 else card_name,
                        "card_name": actual_type if actual_type is not None else preconf_type,
                        "empty": card_info.get("state", {}).get("empty", "true"),
                        "equipment_mismatch":  "true" if (actual_type is not None and preconf_type is not None and actual_type != preconf_type) else "false",
                        "slot_note": card_info.get("config", {}).get("description")
                    }
                    card_info_list.append(info)
        
        msg = {'data': card_info_list, 'status': 200}
        return jsonify(msg)
    except Exception as e:
        LOG.error(traceback.format_exc())
        msg = {'info': 'Get OTN Port info fail', 'status': 500}
        return jsonify(msg)

@new_monitor_mold.route('/get_usage_topk', methods=['POST'])
def get_usage_topk():
    try:
        data =  request.get_json()
        metric_name = data.get("metricName", None)
        topk = data.get("topK", 5)
        target = data.get("target", None)
        start_time = data.get("startTime", None)
        if start_time:
            start_time = datetime.strptime(start_time, timestamp_format).timestamp()
        end_time = data.get("endTime", None)
        if end_time:
            end_time = datetime.strptime(end_time, timestamp_format).timestamp()

        if metric_name is None:
            msg = {'info': 'MetricName is None', 'status': 500}
            return jsonify(msg)
        
        if metric_name == "cpu":
            res = query_cpu_usage_topk(topk=topk, target=target, start_time=start_time, end_time=end_time)
        elif metric_name == "memory":
            res = query_memory_usage_topk(topk=topk, target=target, start_time=start_time, end_time=end_time)
        elif metric_name == "both":
            cpu_res = query_cpu_usage_topk(topk=topk, target=target, start_time=start_time, end_time=end_time)
            memory_res = query_memory_usage_topk(topk=topk, target=target, start_time=start_time, end_time=end_time)
            res = cpu_res + memory_res
        else:
            msg = {'info': 'MetricName is not supported', 'status': 500}
            return jsonify(msg)

        msg = {'data': res, 'status': 200}
        return jsonify(msg)
    except Exception as e:
        LOG.error(traceback.format_exc())
        msg = {'info': 'Interfaces counters fail', 'status': 500}
        return jsonify(msg)
    
@new_monitor_mold.route('/get_mac_table', methods=['POST'])
def get_mac_table():
    try:
        data =  request.get_json()
        target = data.get("target", None)
        date = data.get("date", None)

        if date:
            date = datetime.strptime(date, "%Y-%m-%d %H:%M:%S").timestamp()

        mac_table_state = query_metric("openconfig_network_instance:network_instances_network_instance_fdb_mac_table_entries_entry_state", target, date)
        mac_table_interface = query_metric("openconfig_network_instance:network_instances_network_instance_fdb_mac_table_entries_entry_interface_interface_ref_state", target, date)
        # print(mac_table_interface, mac_table_state)
        
        mac_table_dict = {}
        for item in mac_table_state:
            mac = item['entry_mac_address']
            mac_table_dict[mac] = {**item}  

        for item in mac_table_interface:
            mac = item['entry_mac_address']
            if mac in mac_table_dict:
                mac_table_dict[mac].update(item) 
            else:
                mac_table_dict[mac] = {**item}  

        mac_table_list = list(mac_table_dict.values())
        msg = {'data': mac_table_list, 'status': 200}
        return jsonify(msg)
    except Exception as e:
        LOG.error(traceback.format_exc())
        msg = {'info': 'Get mac table fail', 'status': 500}
        return jsonify(msg)
    

@new_monitor_mold.route('/get_arp_table', methods=['POST'])
def get_arp_table():
    try:
        data =  request.get_json()
        target = data.get("target", None)
        date = data.get("date", None)

        if date:
            date = datetime.strptime(date, "%Y-%m-%d %H:%M:%S").timestamp()

        arp_config = query_metric("openconfig_interfaces:interfaces_interface_openconfig_vlan:routed_vlan_openconfig_if_ip:ipv4_neighbors_neighbor_config", target, date)
        arp_state = query_metric("openconfig_interfaces:interfaces_interface_openconfig_vlan:routed_vlan_openconfig_if_ip:ipv4_neighbors_neighbor_state", target, date)
        
        arp_table_dict = {}
        for item in arp_config:
            if item['interface_name'].startswith("eth"): # 过滤管理口
                continue
            ip = item['ip']
            arp_table_dict[ip] = {**item}  

        for item in arp_state:
            if item['interface_name'].startswith("eth"):
                continue
            neighbor_ip = item['neighbor_ip']
            if neighbor_ip in arp_table_dict:
                arp_table_dict[neighbor_ip].update(item) 
            else:
                arp_table_dict[neighbor_ip] = {**item}  

        arp_table_list = list(arp_table_dict.values())
        msg = {'data': arp_table_list, 'status': 200}
        return jsonify(msg)
    except Exception as e:
        LOG.error(traceback.format_exc())
        msg = {'info': 'Get arp table fail', 'status': 500}
        return jsonify(msg)


@new_monitor_mold.route('/get_nic_info', methods=['POST'])
def get_nic_info():
    try:
        session_query = monitor_db.get_session()
        monitor_query = session_query.query(MonitorTarget).filter(MonitorTarget.device_type == 3).all()

        data = request.get_json()

        search_fields = data.get("searchFields", {})
        
        server_search_fields = {}
        nic_search_fields = {}
        if search_fields:
            server_search_fields = {"fields": ["nodename"], "value": search_fields.get("value", "")}
            nic_search_fields = {"fields": ["name", "device"], "value": search_fields.get("value", "")}
        
        res = []
        key = 1
        for device in monitor_query:
            instance = device.name + ":9100"
            server_info = query_node_metric(instance, metric="node_uname_info", search_fields=server_search_fields)
            node_infos = query_node_metric(instance, metric="node_nic_info", search_fields=nic_search_fields)
            if server_info or node_infos:
                if not server_info:
                    server_info = query_node_metric(instance, metric="node_uname_info")
                if not node_infos:
                    node_infos = query_node_metric(instance, metric="node_nic_info")
                
                info = {
                    "name": server_info.get("nodename", ""),
                    "type" : "server",
                    "deviceIp": device.name,
                    "id": key
                }
                network_infos = query_node_metric(instance, metric="node_network_info")
                ethtool_infos = query_node_metric(instance, metric="node_ethtool_info")
                
                nic_dicts = {}
                for device, net in network_infos.items():


                    if device not in node_infos:
                        continue
                    
                    node_info = node_infos[device]
                    ethtool_info = ethtool_infos[device]

                    
                    net["device"] = device
                    net["bus_info"] = ethtool_info["bus_info"]
                    new_nic = node_info
                    new_nic.update(ethtool_info)
                    new_nic.pop("bus_info")
                    
                    bus_info = ethtool_info["bus_info"].split(".")[0]
                    # 如果后续有其他型号还需要继续增加匹配
                    chip_number  = [word for word in new_nic["chip_number"].split() if 'bcm' in word.lower() or 'mt' in word.lower()]
                    new_nic["chip_number"] = chip_number[0] if chip_number else new_nic["chip_number"]
                    
                    new_nic.update(net)
                    new_nic["type"] = "nics"

                    nic_dicts[device] = new_nic
                    # nic_dicts[bus_info] = new_nic
                    # else:

                    #     new_nic["children"].append(network_info[device]) 
                        
                nic_infos = list(nic_dicts.values()) 
                for index, nic in enumerate(nic_infos):
                    nic["host_port"] = 1
                    nic["id"] = int(str(key) + str(index+1))
                    # for i, child in enumerate(nic["children"]):
                    #     child["id"] = int(str(key) + str(index+1) + str(i+1))
                
                info.update(children=nic_infos)

                key += 1
                res.append(info) 
        
        msg = {'status': 200, "data": res, "total": len(res)}
        return jsonify(msg)
    except Exception as e:
        LOG.error(traceback.format_exc())
        msg = {'info': 'Nic info get fail', 'status': 500}
        return jsonify(msg)


@new_monitor_mold.route('/get_nic_history_info', methods=['POST'])
def get_nic_history_info():
    try:
        data =  request.get_json()
        start_time = data.get("startTime", None)
        if start_time:
            start_time = datetime.strptime(start_time, timestamp_format).timestamp()
        end_time = data.get("endTime", None)
        if end_time:
            end_time = datetime.strptime(end_time, timestamp_format).timestamp()

        node_info = query_node_range_metric(metric="node_uname_info", start_time=start_time, end_time=end_time)
        node_nic_info = query_node_range_metric(metric="node_nic_info", start_time=start_time, end_time=end_time)
        
        res = []
        for instance, nodes in node_info.items():
            nic_list = [nic.get("device") for nic in node_nic_info[instance] if nic.get("device") is not None] 
            node_nic = {
                "hostname":nodes[0]["nodename"],
                "ip": instance.split(":")[0],
                "children": list(set(nic_list))
            }
            res.append(node_nic)

        msg = {'data': res, 'status': 200}
        return jsonify(msg)
    except Exception as e:
        LOG.error(traceback.format_exc())
        msg = {'info': 'Get nic history info fail', 'status': 500}
        return jsonify(msg)


@new_monitor_mold.route('/get_nic_topk', methods=['POST'])
def get_nic_topk():
    try:
        data =  request.get_json()
        metric_name = data.get("metricName", None) 
        topk = data.get("topK", 5)
        start_time = data.get("startTime", None)
        if start_time:
            start_time = datetime.strptime(start_time, timestamp_format).timestamp()
        end_time = data.get("endTime", None)
        if end_time:
            end_time = datetime.strptime(end_time, timestamp_format).timestamp()

        if metric_name is None:
            msg = {'info': 'MetricName is None', 'status': 500}
            return jsonify(msg)
        
        filter = data.get("filter", {})

        res = query_node_topk(metric_name.replace("-", "_"), topk=topk, filter=filter, start_time=start_time, end_time=end_time)
        
        ip_hostname_map = {}
        for value in res:
            if value["instance"] in ip_hostname_map.keys():
                value["hostname"] = ip_hostname_map[value["instance"]]
            else:
                server_info = query_node_metric(value["instance"] + ":9100", metric="node_uname_info")
                ip_hostname_map[value["instance"]] = server_info.get("nodename", "")
                value["hostname"] = server_info.get("nodename", "")

        msg = {'data': res, 'status': 200}
        return jsonify(msg)
    except Exception as e:
        LOG.error(traceback.format_exc())
        msg = {'info': 'Nic counters fail', 'status': 500}
        return jsonify(msg)

@new_monitor_mold.route('/get_target_interface', methods=['POST'])
def get_target_interface():
    try:
        data =  request.get_json()
        target = data.get("target", None)
        date = data.get("date", None)
        if date:
            date = datetime.strptime(date, "%Y-%m-%d %H:%M:%S").timestamp()
        interface_list = get_target_interfaces(target, date)
        msg = {'data': interface_list, 'status': 200}
        return jsonify(msg)
    except Exception as e:
        LOG.error(traceback.format_exc())
        msg = {'info': 'Get dlb info fail', 'status': 500}
        return jsonify(msg)    


@new_monitor_mold.route('/get_dlb_topk', methods=['POST'])
def get_dlb_topk():
    try:
        data =  request.get_json()
        metric_name = data.get("metricName", None)
        topk = data.get("topK", 5)
        target = data.get("target", None)
        start_time = data.get("startTime", None)
        if start_time:
            start_time = datetime.strptime(start_time, timestamp_format).timestamp()
        end_time = data.get("endTime", None)
        if end_time:
            end_time = datetime.strptime(end_time, timestamp_format).timestamp()

        if metric_name is None:
            msg = {'info': 'MetricName is none', 'status': 500}
            return jsonify(msg)
        
        filter = data.get("filter", [])
        
        if metric_name == "out-bindwidth-utilization":
            res = query_dlb_rate_topk(dividend_metric_name="openconfig_interfaces:interfaces_interface_openconfig_if_rates:rates_state_out_bits_rate", 
                                      divisor_metric_name="openconfig_interfaces:interfaces_interface_openconfig_if_ethernet:ethernet_state_port_speed", 
                                      topk=topk, target=target, filter=filter, start_time=start_time, end_time=end_time)
        elif metric_name == "in-bindwidth-utilization":
            res = query_dlb_rate_topk(dividend_metric_name="openconfig_interfaces:interfaces_interface_openconfig_if_rates:rates_state_in_bits_rate", 
                                      divisor_metric_name="openconfig_interfaces:interfaces_interface_openconfig_if_ethernet:ethernet_state_port_speed", 
                                      topk=topk, target=target, filter=filter, start_time=start_time, end_time=end_time)
        elif metric_name == "out-packet-loss-rate":
            res = query_dlb_rate_topk(dividend_metric_name="openconfig_interfaces:interfaces_interface_state_counters_out_discards", 
                                      divisor_metric_name="openconfig_interfaces:interfaces_interface_state_counters_out_pkts", 
                                      topk=topk, target=target, filter=filter, start_time=start_time, end_time=end_time)
        elif metric_name == "in-packet-loss-rate":
            res = query_dlb_rate_topk(dividend_metric_name="openconfig_interfaces:interfaces_interface_state_counters_in_discards", 
                                      divisor_metric_name="openconfig_interfaces:interfaces_interface_state_counters_in_pkts", 
                                      topk=topk, target=target, filter=filter, start_time=start_time, end_time=end_time)
        elif metric_name == "out-throughput":
            res = query_rate_topk("openconfig_interfaces:interfaces_interface_openconfig_if_rates:rates_state_out_pkts_rate",
                                  topk=topk, target=target, filter=filter, start_time=start_time, end_time=end_time)
        elif metric_name == "in-throughput":
            res = query_rate_topk("openconfig_interfaces:interfaces_interface_openconfig_if_rates:rates_state_in_pkts_rate",
                                  topk=topk, target=target, filter=filter, start_time=start_time, end_time=end_time)
        else:
            msg = {'info': 'MetricName is not supported', 'status': 500}
            return jsonify(msg)

        msg = {'data': res, 'status': 200}
        return jsonify(msg)
    except Exception as e:
        LOG.error(traceback.format_exc())
        msg = {'info': 'Get dlb topk fail', 'status': 500}
        return jsonify(msg)
  
    
@new_monitor_mold.route('/get_dlb_table', methods=['POST'])
def get_dlb_table():
    try:
        data =  request.get_json()
        target = data.get("target", None)
        date = data.get("date", None)
        if date:
            date = datetime.strptime(date, "%Y-%m-%d %H:%M:%S").timestamp()
        interface_list = get_target_interfaces(target, date)
        counters_list = query_counters_with_prefix("openconfig_interfaces:interfaces_interface_state_counters_", target, interface_list, date)
        rates_state = query_counters_with_prefix("openconfig_interfaces:interfaces_interface_openconfig_if_rates:rates_state_", target, interface_list, date)

        interface_info = {
            key: {
                **counters_list.get(key, {}),
                **rates_state.get(key, {})
            }
            for key in set(interface_list)
        }

        msg = {'data': interface_info, 'status': 200}
        return jsonify(msg)
    except Exception as e:
        LOG.error(traceback.format_exc())
        msg = {'info': 'Get dlb info fail', 'status': 500}
        return jsonify(msg)
    
    
def query_otn_lldp_info(otn_ip):
    url = "http://tnms_web:8888/api/ne/get"
    payload = {
        "ne_id": otn_ip + ":830",
        "xml": {
            "lldp": {
                "$": {
                    "xmlns": "http://openconfig.net/yang/lldp"
                }
            }
        }
    }
    
    try:
        response = requests.post(url, json=payload)
        data = response.json()
        if data['message'] != 'SUCCESS':
            LOG.error(f"query lldp info failed: {data['message']}")
            return []
        else:
            # print(data)
            return data['data']['lldp']
    except Exception as e:
        LOG.error(f"query lldp info failed: {traceback.format_exc()}")
        return []


def query_otn_platform_info(otn_ip):
    url = "http://tnms_web:8888/api/ne/get"
    payload = {
        "ne_id": otn_ip + ":830",
        "xml": {
            "components": {
                "$": {
                    "xmlns": "http://openconfig.net/yang/platform"
                },
                "component": {
                    "name": "CHASSIS-1"
                }
            }
        }
    }

    try:
        response = requests.post(url, json=payload)
        data = response.json()
        if data['message'] != 'SUCCESS':
            LOG.error(f"query otn platform info failed: {data['message']}")
            return {}
        else:
            data = data['data']['components']['component']
            otn_info = {
                "chaasis_type": data.get("chassis", {}).get("state", {}).get("chassis-type", {}).get("_"),
                "hardware_version": data.get("state", {}).get("hardware-version"),
                "temperature": data.get("state", {}).get("temperature", {}).get("instant"),
                "sn": data.get("state", {}).get("serial-no"),
                "manufacture": data.get("state", {}).get("mfg-name"),
                "firmware_version": data.get("state", {}).get("firmware-version"),
                "actual_power": data.get("state", {}).get("used-power"),
                "pn": data.get("state", {}).get("part-no"),
                "production_date": data.get("state", {}).get("mfg-date"),
                "software_version": data.get("state", {}).get("software-version"),
                "other_info": data.get("state", {}).get("description"),
                "subcomponent": data.get("subcomponents", {}).get("subcomponent", [])
            }
            return otn_info
    except Exception as e:
        LOG.error(f"query otn platform info failed: {traceback.format_exc()}")
        return {}


def query_otn_card_info(otn_ip, card_name):          
    url = "http://tnms_web:8888/api/data/get_key"
    payload = {
        "DBKey": f"ne:5:component:{otn_ip}:830:{card_name}"
    }

    try:
        response = requests.post(url, json=payload)
        data = response.json()
        if data.get("apiResult", "") == 'fail':
            LOG.error(f"query otn card info failed: {data}")
            return {}
        else:
            data = data['data']
            return data
    except Exception as e:
        LOG.error(f"query lldp info failed: {traceback.format_exc()}")
        return {}