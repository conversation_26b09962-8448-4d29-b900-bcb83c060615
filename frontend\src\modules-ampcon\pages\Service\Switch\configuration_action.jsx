import {AmpConCustomModalForm} from "@/modules-ampcon/components/custom_table";
import {useState} from "react";
import {useForm} from "antd/es/form/Form";
import {Select, Form, message} from "antd";
import {getAllSystemConfigBrief, updateSystemConfigAndSwitchMapping} from "@/modules-ampcon/apis/config_api";

const ConfigurationAction = ({record, tableRef}) => {
    const [configurationModal, setConfigurationModal] = useState(false);
    const [configurationList, setConfigurationList] = useState([]);
    const [form] = useForm();

    const configurationForm = () => {
        return (
            <Form.Item
                name="configuration"
                label="System Configuration"
                rules={[{required: true, message: "Please Select System Configuration!"}]}
            >
                <Select style={{width: "280px"}}>
                    {configurationList.map(configuration => (
                        <Select.Option key={configuration.system_config_id} value={configuration.system_config_name}>
                            {configuration.system_config_name}
                        </Select.Option>
                    ))}
                </Select>
            </Form.Item>
        );
    };

    const onSubmit = async values => {
        const operation = {
            [record.id]: "add"
        };
        updateSystemConfigAndSwitchMapping(values.configuration, operation).then(response => {
            if (response.status === 200) {
                message.success(response.info);
                tableRef.current.refreshTable();
            } else {
                message.error(response.info);
            }
        });
        form.resetFields();
        setConfigurationModal(false);
    };

    return (
        <>
            <a
                onClick={async () => {
                    setConfigurationModal(true);
                    const response = await getAllSystemConfigBrief();
                    setConfigurationList(response.data);
                    const defaultSystemConfig = response.data.find(
                        configuration => configuration.system_config_id === record.system_config_id
                    );
                    form.setFieldsValue({configuration: defaultSystemConfig.system_config_name});
                }}
            >
                Configuration
            </a>
            <AmpConCustomModalForm
                title={record.sn}
                isModalOpen={configurationModal}
                formInstance={form}
                layoutProps={{
                    labelCol: {
                        span: 8
                    }
                }}
                CustomFormItems={configurationForm}
                onCancel={() => {
                    form.resetFields();
                    setConfigurationModal(false);
                }}
                onSubmit={onSubmit}
                modalClass="ampcon-middle-modal"
            />
        </>
    );
};
export default ConfigurationAction;
