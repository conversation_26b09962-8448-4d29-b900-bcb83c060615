import React, {useEffect, useState, useRef, isValidElement, Children} from "react";
import {useSelector} from "react-redux";
import {
    Table,
    Button,
    Upload,
    message,
    Select,
    Checkbox,
    Input,
    Divider,
    Modal,
    Space,
    theme,
    Empty,
    ConfigProvider
} from "antd";
import Icon, {SyncOutlined, SearchOutlined} from "@ant-design/icons";
import dayjs from "dayjs";
import {
    exportDisabledIcon,
    exportEnabledIcon,
    refreshDisabledIcon,
    refreshEnabledIcon,
    searchIcon,
    uploadEnabledIcon
} from "@/modules-otn/pages/otn/device/device_icons";
import EmptyPic from "@/assets/images/App/empty.png";
import {isEqual} from "lodash";
import {TableConfig} from "@/modules-otn/config/table_config";
import useUserRight from "@/modules-otn/hooks/useUserRight";
import {openModalEdit} from "@/modules-otn/components/form/edit_form";
import {g<PERSON><PERSON>lList} from "@/store/modules/otn/languageOTNSlice";
import {getText, sortArr, isNumberStr, convertToArray, DebounceButton, classNames} from "@/modules-otn/utils/util";
import styles from "./custom_table.module.scss";
import tableStyle from "./edit_table.module.scss";
import {smallModal} from "../modal/custom_modal";

export function CustomTable(props) {
    const {
        initColumns,
        initDataSource,
        type,
        buttons,
        commonData,
        setCommonData,
        initRowOperation,
        columnFormat,
        rowClick,
        rowDbClick,
        checkboxProps,
        clearData,
        execRefresh,
        refreshParent,
        initTitle,
        initHead,
        expandable,
        loading = false,
        clearFilter,
        showType = 0,
        divContent = {},
        tableID,
        defaultFilters,
        sectionTypeProps,
        search,
        scroll = true,
        paginationEnable,
        tabSelectionType,
        refreshDisabled = false,
        rootStyle = {},
        showExportButton = true,
        showRefreshButton = true
    } = props;

    const {labelList} = useSelector(state => state.languageOTN);
    const {
        token: {colorPrimary}
    } = theme.useToken();
    const userRight = useUserRight();
    const [dataList, setDataList] = useState([]);
    const [filterDataList, setFilterDataList] = useState(null);

    const {title, columns, refresh, delAPI, head, operateColumnWidth, dbClickConfig} = TableConfig[type];
    const pagination = paginationEnable ?? TableConfig[type].pagination;
    const selectionType = tabSelectionType ?? TableConfig[type].selectionType;

    const ref = useRef();
    const [size, setSize] = useState(0);
    useEffect(() => {
        const observer = new ResizeObserver(entries => {
            const element = entries[0].target;
            const hHeader = element.querySelector(".ant-table-thead").offsetHeight;
            const hContainer = element.offsetHeight;
            setSize(hContainer - hHeader - ((pagination ?? true) ? 63 : 2));
        });
        observer.observe(ref.current);
        return () => {
            observer.disconnect();
        };
    }, []);

    const [commData, setCommData] = useState({});
    const [uploading, setUploading] = useState(false);
    const [searchNoData, setSearchNoData] = useState(false);
    // 表格应用的过滤值
    const [filteredInfo, setFilteredInfo] = useState({});
    // 记录选中但未应用的过滤值（解决表格动态更新勾选被取消的问题）
    const [unSaveFilteredInfo, setUnSaveFilteredInfo] = useState({});
    // 过滤器的内容搜索
    const [searchValue, setSearchValue] = useState({});
    const readyOnlyRight = useUserRight();
    useEffect(() => {
        setFilteredInfo({});
        setUnSaveFilteredInfo({});
    }, [clearFilter]);

    useEffect(() => {
        if (defaultFilters) {
            setFilteredInfo(defaultFilters);
            setUnSaveFilteredInfo(defaultFilters);
        }
    }, [defaultFilters]);

    const doRefresh = () => {
        if (initDataSource) {
            if (refreshParent) refreshParent();
        } else {
            loadData();
        }
    };
    const loadData = () => {
        if (TableConfig?.[type]) {
            if (initDataSource) {
                if (!(initDataSource instanceof Array)) {
                    setDataList([]);
                    setAllData([]);
                    return;
                }
                initDataSource.forEach((item, index) => {
                    if (item === undefined) {
                        delete initDataSource[index];
                    } else if (!item?.key) {
                        item.key = index.toString();
                    }
                });
                setDataList(initDataSource);
                setAllData(initDataSource);
            } else if (TableConfig[type].dataAPI) {
                let parameter = {};
                if (TableConfig[type].dataAPI.APIParameter) {
                    parameter = TableConfig[type].dataAPI.APIParameter();
                }
                TableConfig[type].dataAPI.APIName(parameter).then(result => {
                    if (result.apiResult === "fail") return;
                    result?.forEach((item, index) => {
                        if (!item?.key) {
                            if (TableConfig[type].dataAPI.key) {
                                item.key = item[TableConfig[type].dataAPI.key];
                            } else {
                                item.key = index.toString();
                            }
                        }
                    });
                    if (TableConfig[type].dataAPI.sortKey) {
                        result?.sort((optionA, optionB) => {
                            return (optionA?.[TableConfig[type].dataAPI.sortKey] ?? "")
                                .toString()
                                .toLowerCase()
                                .localeCompare(
                                    (optionB?.[TableConfig[type].dataAPI.sortKey] ?? "").toString().toLowerCase()
                                );
                        });
                    }
                    setDataList(result);
                    setAllData(result);
                });
            }
        }
    };

    useEffect(() => {
        loadData();
    }, [execRefresh, initDataSource]);

    if (!TableConfig?.[type]) return <div>Not found</div>;

    const getFilterData = key => {
        const keySet = new Set();
        dataList.forEach(item => {
            keySet.add(item[key]);
        });
        const filterData = [];
        keySet.forEach(item => {
            if (item && !Array.isArray(item)) {
                filterData.push({
                    text: item,
                    value: item
                });
            }
        });
        return filterData;
    };

    const createColumn = item => {
        const columnCfg = {
            ...item,
            title:
                (gLabelList[item.title] ??
                    gLabelList[item.dataIndex] ??
                    getText(item.title) ??
                    getText(item.dataIndex)) + (item.unit ? `(${item.unit})` : ""),
            key: item.dataIndex
        };
        if (item.filters) {
            item.filters.map(s => {
                s.text = labelList[s.text] ?? s.text;
            });
        }
        if (item.filter) {
            if (!item.filters) {
                columnCfg.filters = sortArr(getFilterData(item.dataIndex), ["text"]);
            }
            columnCfg.filteredValue = filteredInfo[item.dataIndex] || [];
            if (!item.onFilter) {
                columnCfg.onFilter = (value, record) => record[item.dataIndex] === value;
            }
            columnCfg.filterSearch = true;
        }
        if (columnCfg.filters) {
            columnCfg.filterDropdown = filterProps => {
                const {setSelectedKeys, filters, confirm, clearFilters} = filterProps;
                const onChange = checkedValues => {
                    setUnSaveFilteredInfo(_state => ({..._state, [item.dataIndex]: checkedValues}));
                };
                const clear = () => {
                    setSelectedKeys([]);
                    clearFilters();
                    setFilteredInfo(filteredInfo => ({...filteredInfo, [item.dataIndex]: []}));
                    setUnSaveFilteredInfo(filteredInfo => ({...filteredInfo, [item.dataIndex]: []}));
                };
                const onOk = () => {
                    setFilteredInfo({...filteredInfo, [item.dataIndex]: unSaveFilteredInfo[item.dataIndex]});
                    confirm();
                };
                const onSearchChange = e => {
                    setSearchValue(_state => ({..._state, [item.dataIndex]: e.target.value}));
                };

                const options = filters
                    .filter(option => {
                        return searchValue[item.dataIndex] ? option.text.includes(searchValue[item.dataIndex]) : true;
                    })
                    .map(({text, value}) => ({value, label: text}));

                return (
                    <div>
                        <div style={{padding: 8}}>
                            <Input
                                prefix={<SearchOutlined />}
                                onChange={onSearchChange}
                                value={searchValue[item.dataIndex]}
                            />
                        </div>
                        <Divider style={{margin: "0 0 2px 0"}} />
                        {options.length ? (
                            <ul
                                style={{
                                    padding: "8px 12px",
                                    display: "flex",
                                    flexDirection: "column",
                                    maxHeight: 200,
                                    margin: 0,
                                    overflowY: "auto"
                                }}
                            >
                                <Checkbox.Group
                                    onChange={onChange}
                                    value={unSaveFilteredInfo[item.dataIndex]}
                                    style={{display: "grid"}}
                                    options={options}
                                />
                            </ul>
                        ) : (
                            <div
                                style={{
                                    display: "block",
                                    padding: "8px 0",
                                    color: "rgba(0, 0, 0, 0.25)",
                                    fontSize: 12,
                                    textAlign: "center"
                                }}
                            >
                                {gLabelList.not_find}
                            </div>
                        )}
                        <Divider style={{margin: 2}} />
                        <div style={{padding: "8px 7px", display: "flex", justifyContent: "space-between"}}>
                            <Button
                                type="link"
                                disabled={!unSaveFilteredInfo?.[item.dataIndex]?.length}
                                style={{
                                    color: !unSaveFilteredInfo?.[item.dataIndex]?.length
                                        ? "rgba(0,0,0,0.25)"
                                        : colorPrimary
                                }}
                                onClick={clear}
                                // size="small"
                            >
                                {gLabelList.reset}
                            </Button>
                            <Button type="primary" onClick={onOk}>
                                {gLabelList.ok}
                            </Button>
                        </div>
                    </div>
                );
            };
        }
        if (columnFormat && columnFormat?.[item.dataIndex]) {
            columnCfg.render = columnFormat[item.dataIndex];
        }
        return columnCfg;
    };

    const onExportToExcel = () => {
        const filterColumn = _columns.filter(item => !["操作", "Operate"].includes(item.title));
        const headers = `${filterColumn.map(({title}) => `"${title}"`).join(",")}\n`;
        const afterFilterByHead = (filterDataList ?? dataList).filter(_data => {
            return !filterColumn.find(column => {
                if (column.filteredValue?.length > 0) {
                    return !column.filteredValue.includes(_data[column.dataIndex]);
                }
            });
        });
        const data = afterFilterByHead.reduce((res, item) => {
            const lineData = `${filterColumn
                .map(column => {
                    let matchValue = column.render
                        ? column.render(item[column.dataIndex], item)
                        : item[column.dataIndex];

                    if (typeof matchValue !== "string") {
                        // React 元素简单处理
                        if (
                            isValidElement(matchValue) ||
                            (Array.isArray(matchValue) && matchValue.some(item => isValidElement(item)))
                        ) {
                            matchValue = extractTextFromReactElement(matchValue);
                        } else {
                            matchValue = formatObjectOrArrayToString(item[column.dataIndex]);
                        }
                    }
                    return `"${matchValue ?? ""}"`;
                })
                .join(",")}\n`;
            return `${res}${lineData}`;
        }, `\uFEFF${headers}`);

        const blob = new Blob([data], {type: "text/csv"});
        const downloadLink = document.createElement("a");
        downloadLink.href = URL.createObjectURL(blob);
        const fileName = gLabelList[initTitle] || initTitle || gLabelList[title] || title || type;
        const fileTimeFlag = dayjs().format("YYYY/MM/DD_HH:mm:ss");
        downloadLink.download = `${fileName}_${fileTimeFlag}.csv`;
        downloadLink.click();
    };

    const _head = initHead !== undefined ? initHead : head;
    let _columns = initColumns;
    if (initColumns == null) {
        _columns = [];
        columns.forEach(item => {
            const columnCfg = createColumn(item);
            if (item.children) {
                columnCfg.children = [];
                item.children.forEach(subItem => {
                    columnCfg.children.push(createColumn(subItem));
                });
            }
            _columns.push(columnCfg);
        });
        if (delAPI || initRowOperation?.length > 0) {
            _columns.push({
                title: labelList.operation,
                width: operateColumnWidth ?? 100,
                sorter: false,
                fixed: "right",
                render: (_, record) => {
                    const rowOperation = [];
                    if (delAPI) {
                        rowOperation.push(
                            <DebounceButton
                                disabled={userRight.disabled}
                                type="link"
                                style={{color: userRight.disabled ? "" : colorPrimary}}
                                onClick={() => {
                                    const onOK = async () => {
                                        let parameter = {};
                                        if (delAPI.APIParameter) {
                                            parameter = delAPI.APIParameter(record);
                                        }
                                        const r = await delAPI.APIName(parameter);
                                        if (r.apiResult === "complete") {
                                            message.success(gLabelList.delete_success);
                                            modal.destroy();
                                            if (refreshParent) refreshParent();
                                            try {
                                                if (
                                                    selectionType &&
                                                    commonData?.[type]?.selectedRows?.[0]?.name === record.name
                                                ) {
                                                    const newData = {...commonData};
                                                    delete newData[type];
                                                    setCommonData(newData);
                                                }
                                            } catch (er) {
                                                // eslint-disable-next-line no-console
                                                console.log(er);
                                            }
                                            loadData();
                                        } else {
                                            message.error(gLabelList.delete_failed);
                                        }
                                    };

                                    const modal = smallModal({
                                        content: gLabelList.delete_confirm_msg,
                                        // eslint-disable-next-line no-unused-vars
                                        onOk: _ => {
                                            onOK();
                                            modal.destroy();
                                        }
                                    });
                                }}
                            >
                                {gLabelList.delete}
                            </DebounceButton>
                        );
                    }
                    if (initRowOperation) {
                        initRowOperation.forEach((item, index) => {
                            if (item.display && !item.display(record)) {
                                return;
                            }
                            const _disabled = typeof item?.disabled === "function" && item.disabled(record);
                            if (item.confirm) {
                                rowOperation.push(
                                    <DebounceButton
                                        key={item.label}
                                        onClick={event => {
                                            event?.stopPropagation();
                                            smallModal({
                                                content: gLabelList[item.confirm.title] ?? item.confirm.title,
                                                onOk: item.onClick.bind(record)
                                            });
                                        }}
                                        type="link"
                                        // size="small"
                                        style={{color: _disabled ? "" : colorPrimary}}
                                        disabled={_disabled}
                                        title={gLabelList[item?.title] ?? item?.title}
                                    >
                                        {gLabelList[item.label] ?? item.label}
                                    </DebounceButton>
                                );
                            } else {
                                rowOperation.push(
                                    <DebounceButton
                                        // eslint-disable-next-line react/no-array-index-key
                                        key={item.label + index}
                                        onClick={item.onClick.bind(record)}
                                        type="link"
                                        // size="small"
                                        title={gLabelList[item?.title] ?? item?.title}
                                        disabled={_disabled}
                                        style={{color: _disabled ? "" : colorPrimary}}
                                    >
                                        {gLabelList[item.label] ?? item.label}
                                    </DebounceButton>
                                );
                            }
                        });
                    }
                    return <Space size={24}>{rowOperation}</Space>;
                }
            });
        }
    }
    _columns?.forEach(col => {
        if (col.onCell) {
            const func = col.onCell;
            col.onCell = (record, index) => {
                const rt = func(record, index);
                if (rt) {
                    rt.className = styles.customTable_ctn_content_cell;
                    rt.title = "";
                }
                return rt;
            };
        } else {
            col.onCell = record => ({
                record,
                ...filterColumnAttributes(col),
                title: "",
                className: styles.customTable_ctn_content_cell
            });
        }
        col.onHeaderCell = column => ({
            ...filterColumnAttributes(column),
            title: "",
            className: styles.customTable_ctn_content_headerCell
        });
    });
    let _rowSelection = null;
    if (selectionType) {
        _rowSelection = {};
        const rowSelection = {
            onChange: (selectedRowKeys, selectedRows) => {
                if (
                    (clearData && typeof clearData === "boolean") ||
                    (clearData &&
                        typeof clearData === "function" &&
                        clearData(selectedRows, structuredClone(commonData)))
                ) {
                    const data = {...commonData};
                    data[type] = {
                        selectedRowKeys,
                        selectedRows
                    };
                    data?.upgrade?.selectedRowKeys?.splice(0);
                    data?.upgrade?.selectedRows?.splice(0);
                    setCommonData(data);
                } else if (selectedRows.length > 0) {
                    const data = {...commonData};
                    data[type] = {
                        selectedRowKeys,
                        selectedRows
                    };
                    setCommonData(data);
                } else {
                    const data = {...commonData};
                    delete data[type];
                    setCommonData(data);
                }
            },
            getCheckboxProps: checkboxProps
        };
        _rowSelection = {...rowSelection, type: sectionTypeProps ?? selectionType};
        if (commonData?.[type]?.selectedRows) {
            try {
                // _rowSelection.selectedRowKeys = commonData?.[type]?.selectedRows.map(
                //     item => dataList.filter(i => i?.name === item?.name)?.[0]?.key
                // );
                _rowSelection.selectedRowKeys = commonData?.[type]?.selectedRows.map(item => item?.key);
            } catch (er) {
                // eslint-disable-next-line no-console
                console.log(er);
            }
        }
    }
    _columns?.forEach(col => {
        if (col.sorter === undefined || col.sorter) {
            if (col.sortFun) {
                col.sorter = col.sortFun();
            } else {
                col.sorter = (optionA, optionB) => {
                    const compareA = (optionA?.[col.dataIndex] ?? "").toString().toLowerCase();
                    const compareB = (optionB?.[col.dataIndex] ?? "").toString().toLowerCase();
                    return isNumberStr(compareA) && isNumberStr(compareB)
                        ? compareA - compareB
                        : compareA.localeCompare(compareB, "ZH-CN", {numeric: true});
                };
            }
        }
    });

    const setAllData = _data => {
        if (setCommonData) {
            if (!isEqual(_data, commonData?.allData?.[type])) {
                setCommonData(commonData => ({...commonData, allData: {...commonData.allData, [type]: _data}}));
            }
        }
    };

    const titleText = gLabelList[initTitle] ?? initTitle ?? gLabelList[title] ?? title;

    const isNeedShowCustomFilterEmpty = searchNoData || Object.values(filteredInfo)?.some(item => item?.length);

    const containerStyle = {
        display: "flex",
        flexDirection: "column",
        flex: 1,
        ...(scroll ? {overflow: "hidden"} : {}),
        ...rootStyle
    };

    const containerClass = classNames([
        Array.isArray(_columns) && _columns.some(item => item?.fixed === "left") ? styles.fixFixedTableLeftBorder : "",
        scroll ? styles.fixTableYScroll : ""
    ]);

    let tableScroll = scroll ? {y: size, x: "max-content"} : {x: "max-content"};
    if (!filterDataList?.length && !dataList?.length) {
        tableScroll = {x: tableScroll.x};
    }

    return (
        <div style={containerStyle} className={containerClass}>
            {(_head == null || _head === true) && (
                <div id="edit_table_header" className={styles.table_header} style={{borderBottom: "none"}}>
                    {titleText && typeof titleText === "string" && (
                        <span
                            className={styles.table_header_title}
                            style={{
                                fontSize: 18,
                                paddingBottom: 18,
                                borderBottom: "1px solid #F2F2F2",
                                marginBottom: 24
                            }}
                        >
                            {titleText}
                        </span>
                    )}
                    <div style={{display: "flex", justifyContent: "space-between"}}>
                        <Space style={{marginBottom: 24}} size={16}>
                            {buttons?.map((item, index) => {
                                if (item.display === false) {
                                    return;
                                }
                                if (item.type === "label") {
                                    return <div key={item.value}>{gLabelList[item.value] ?? item.value}</div>;
                                }
                                if (item.type === "checkbox") {
                                    return (
                                        <Checkbox key={item.key} onChange={item.onChange} disabled={item?.disabled}>
                                            {gLabelList[item.label] ?? item.label}
                                        </Checkbox>
                                    );
                                }
                                if (item.type === "select") {
                                    return (
                                        <div key={item.hooksKey}>
                                            {item.label ? item.label : null}
                                            <Select
                                                style={{
                                                    marginLeft: "5px",
                                                    width: item.width || 140
                                                }}
                                                placeholder={gLabelList.please_select}
                                                onChange={item.onChange}
                                                value={item?.value}
                                                onDropdownVisibleChange={open => {
                                                    if (open) {
                                                        item?.dataAPI
                                                            .APIName(item?.dataAPI?.APIParameter())
                                                            .then(rs => {
                                                                const selectData = [];
                                                                rs.forEach(_item => {
                                                                    selectData.push({
                                                                        label: _item[item.dataKey],
                                                                        value: _item[item.dataKey]
                                                                    });
                                                                });
                                                                setCommData({
                                                                    ...commData,
                                                                    [item.hooksKey]: selectData
                                                                });
                                                            });
                                                    }
                                                }}
                                                options={commData[item.hooksKey]}
                                            />
                                        </div>
                                    );
                                }
                                if (item.upload) {
                                    return (
                                        <Upload
                                            // eslint-disable-next-line react/no-array-index-key
                                            key={index}
                                            name={item.upload.name}
                                            accept={item.upload.accept}
                                            action={item.upload.api}
                                            disabled={uploading}
                                            showUploadList={false}
                                            multiple
                                            beforeUpload={(file, fileList) => {
                                                const isBinFie =
                                                    file.type === "application/octet-stream" &&
                                                    file.name.endsWith(".bin");
                                                const isTarGz =
                                                    file.type === "application/x-gzip" && file.name.endsWith(".tar.gz");
                                                if (item.upload.name === "upgrade" && !isBinFie) {
                                                    message.error(labelList.bin_files).then();
                                                    return false;
                                                }
                                                if (item.upload.name === "data" && !isTarGz) {
                                                    message.error(labelList.tar_gz).then();
                                                    return false;
                                                }
                                                return new Promise(resolve => {
                                                    const sameFile = fileList.filter(component => {
                                                        return dataList.some(
                                                            components => component.name === components.name
                                                        );
                                                    });
                                                    if (
                                                        (fileList[0].name !== file.name && sameFile.length === 0) ||
                                                        fileList[0].name !== file.name ||
                                                        sameFile.length === 0
                                                    ) {
                                                        resolve();
                                                        return;
                                                    }
                                                    Modal.confirm({
                                                        width: "40%",
                                                        closable: true,
                                                        okText: gLabelList.overwrite,
                                                        cancelText: gLabelList.cancel,
                                                        centered: true,
                                                        onOk: close => {
                                                            close();
                                                            resolve();
                                                        },
                                                        content: (
                                                            <div>
                                                                <p>
                                                                    {sameFile.length > 1
                                                                        ? gLabelList.multiple_same_file
                                                                        : gLabelList.single_same_file}
                                                                </p>
                                                                <p>
                                                                    {sameFile.map(item => (
                                                                        <p>{item.name}</p>
                                                                    ))}
                                                                </p>
                                                                <p> {gLabelList.will_be_overwritten}</p>
                                                            </div>
                                                        )
                                                    });
                                                });
                                            }}
                                            onChange={rs => {
                                                setUploading(["uploading"].includes(rs?.file?.status));
                                                if (rs?.file?.status === "error") {
                                                    message.error(gLabelList.upload_failed).then();
                                                    return;
                                                }
                                                if (rs?.file?.status === "done") {
                                                    setTimeout(() => {
                                                        message.success(gLabelList.upload_success).then();
                                                        if (refreshParent) refreshParent();
                                                        loadData();
                                                    }, 1000);
                                                }
                                            }}
                                        >
                                            <Button
                                                disabled={uploading || userRight.disabled}
                                                type="primary"
                                                icon={
                                                    uploading ? (
                                                        <SyncOutlined spin style={{color: "#14C9BB"}} />
                                                    ) : (
                                                        <Icon component={uploadEnabledIcon} />
                                                    )
                                                }
                                            >
                                                {gLabelList[item.label] ?? item.label}
                                            </Button>
                                        </Upload>
                                    );
                                }
                                if (item.confirm) {
                                    return (
                                        <DebounceButton
                                            {...item}
                                            key={item.label}
                                            title={item.title ? labelList[item.title] : item.label}
                                            onClick={() => {
                                                const modal = smallModal({
                                                    content: gLabelList[item.confirm.title] ?? item.confirm.title,
                                                    // eslint-disable-next-line no-unused-vars
                                                    onOk: _ => {
                                                        item.onClick();
                                                        modal.destroy();
                                                    }
                                                });
                                            }}
                                        >
                                            {gLabelList[item.label] ?? item.label}
                                        </DebounceButton>
                                    );
                                }
                                return (
                                    <DebounceButton
                                        {...item}
                                        key={item.label}
                                        // style={{marginRight: 14}}
                                        onClick={item.onClick.bind({filter: filteredInfo})}
                                    >
                                        {gLabelList[item.label] ?? item.label}
                                    </DebounceButton>
                                );
                            })}
                            {showExportButton && (
                                <DebounceButton
                                    icon={
                                        <Icon component={!dataList.length ? exportDisabledIcon : exportEnabledIcon} />
                                    }
                                    onClick={onExportToExcel}
                                    title={labelList.export_to_excel}
                                    disabled={!dataList.length}
                                >
                                    {labelList.export}
                                </DebounceButton>
                            )}
                            {(showRefreshButton && refresh == null) || refresh ? (
                                <DebounceButton
                                    onClick={doRefresh}
                                    icon={
                                        <Icon component={refreshDisabled ? refreshDisabledIcon : refreshEnabledIcon} />
                                    }
                                    disabled={refreshDisabled}
                                >
                                    {labelList.refresh}
                                </DebounceButton>
                            ) : null}
                        </Space>
                        <Space style={{marginBottom: 24, display: "inline-block"}}>
                            {search && (
                                <Input
                                    placeholder="Search"
                                    style={{width: 280, borderColor: "#B8BFBF"}}
                                    prefix={<Icon component={searchIcon} />}
                                    onChange={event => {
                                        const fiterValue = event.target.value?.trim()?.toLowerCase();
                                        if (!fiterValue || fiterValue === "") {
                                            setSearchNoData(false);
                                            setFilterDataList(null);
                                            return;
                                        }
                                        const filterList = [];
                                        dataList.filter(_data =>
                                            Object.entries(_data).find(([_k, _v]) => {
                                                const found =
                                                    columns.find(_column => _column.dataIndex === _k) &&
                                                    _v?.toString()?.toLowerCase()?.includes(fiterValue);
                                                if (found) {
                                                    const str = _v.toString();
                                                    const foundIndex = str.toLowerCase().indexOf(fiterValue);

                                                    const formatStr = (
                                                        <>
                                                            {str.substring(0, foundIndex)}
                                                            <a style={{color: "#383636", backgroundColor: "#ff9632"}}>
                                                                {str.substring(
                                                                    foundIndex,
                                                                    foundIndex + fiterValue.length
                                                                )}
                                                            </a>
                                                            {str.substring(foundIndex + fiterValue.length)}
                                                        </>
                                                    );
                                                    filterList.push({..._data, [_k]: formatStr});
                                                }
                                                return found;
                                            })
                                        );
                                        if (filterList.length > 0) {
                                            setSearchNoData(false);
                                            setFilterDataList(filterList);
                                        } else {
                                            setSearchNoData(true);
                                            setFilterDataList([]);
                                        }
                                    }}
                                />
                            )}
                        </Space>
                    </div>
                </div>
            )}
            <div
                ref={ref}
                className={classNames([tableStyle.edit_table_content, styles.fixExpandableWidth])}
                id={tableID}
            >
                {showType === 0 && (
                    <ConfigProvider renderEmpty={isNeedShowCustomFilterEmpty ? customSearchNoDataEmpty : undefined}>
                        <Table
                            tableLayout="fixed"
                            style={{flex: 1}}
                            rowSelection={_rowSelection}
                            bordered
                            loading={loading}
                            expandable={expandable}
                            showSorterTooltip={false}
                            onRow={record => {
                                return {
                                    onClick: event => {
                                        if (rowClick) {
                                            rowClick(event, record);
                                        }
                                    },
                                    onDoubleClick: event => {
                                        if (rowDbClick) {
                                            rowDbClick(event, record);
                                        } else if (dbClickConfig) {
                                            if (dbClickConfig.getDBKey(record)) {
                                                openModalEdit(
                                                    typeof dbClickConfig.type === "function"
                                                        ? dbClickConfig.type(record)
                                                        : dbClickConfig.type,
                                                    convertToArray(dbClickConfig.keyName).map(i => record[i]),
                                                    dbClickConfig.neType,
                                                    dbClickConfig.getDBKey(record),
                                                    record.ne_id,
                                                    null,
                                                    null,
                                                    readyOnlyRight.disabled
                                                );
                                            }
                                        }
                                    }
                                };
                            }}
                            scroll={tableScroll}
                            dataSource={filterDataList ?? dataList}
                            columns={_columns}
                            pagination={
                                pagination ?? {
                                    showSizeChanger: true,
                                    showTotal(total, range) {
                                        return `${range.join("-")} ${gLabelList.of} ${total} ${gLabelList.items}`;
                                    },
                                    defaultPageSize: 20,
                                    showQuickJumper: true
                                    // total,
                                    // onChange: (page, pageSize) => {
                                    //     setPagePlug?.({page, pageSize});
                                    // }
                                }
                            }
                            rowClassName={styles.customTable_ctn_content_row}
                            onSelect={() => {}}
                        />
                    </ConfigProvider>
                )}
                {showType !== 0 && divContent[showType]}
            </div>
        </div>
    );
}

const formatObjectOrArrayToString = value => {
    if (Object.prototype.toString.call(value).slice(8, -1) === "Object" || Array.isArray(value)) {
        return JSON.stringify(value).replace(/"/g, "");
    }
    return value;
};

// 提取React元素内部文本
function extractTextFromReactElement(element) {
    if (typeof element === "string") {
        return element;
    }

    if (isValidElement(element)) {
        // 如果是 React 元素，递归提取文本
        return Children.toArray(element.props.children)
            .map(child => extractTextFromReactElement(child))
            .join("");
    }

    // 处理其他情况，例如数组
    if (Array.isArray(element)) {
        return element.map(item => extractTextFromReactElement(item)).join(" ");
    }

    // 处理其他类型
    return String(element);
}

const customSearchNoDataEmpty = () => {
    return (
        <div className="custom_empty_container">
            <Empty
                image={EmptyPic}
                description="No matching records found"
                imageStyle={{marginTop: 16, marginBottom: 0}}
            />
        </div>
    );
};

const filterColumnAttributes = column => {
    const {dataIndex, sorter, render, ...rest} = column;
    return rest;
};
