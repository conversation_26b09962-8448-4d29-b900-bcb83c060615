import pexpect
from pexpect import pxssh

SSH_CONFIRM_MATCH = 'yes/no'
PASSWD_MATCH = '(.*)password:'
COMMIT_SUCCESS_MATCH = 'Commit OK'
COMMIT_FAILED_MATCH = 'Commit failed'
PROMPT = ["# ",  ">>> ", "> ", "\$ ", pexpect.EOF, pexpect.TIMEOUT]
TIMEOUT = 10


def cmd_in_cli(host, user, password, cmds, configure_mode=False):
    try:
        ssh = login(host, user, password)
        i = ssh.expect(PROMPT, TIMEOUT)

        if i == 3:
            # in linux mode
            ssh.sendline('cli')
            i = ssh.expect(PROMPT, TIMEOUT)

        if configure_mode == False:
            for cmd in cmds:
                if i == 2:
                    ssh.sendline(cmd)
                    i = ssh.expect(PROMPT, TIMEOUT)
            return ssh.before.rstrip(user), 0
        else:
            # need enter into config mode
            if i == 2:
                ssh.sendline('configure')
                i = ssh.expect(PROMPT, TIMEOUT)
                for cmd in cmds:
                    if i == 0:
                        ssh.sendline(cmd)
                        i = ssh.expect(PROMPT, TIMEOUT)
                # finially, conifgure commit
                if i == 0:
                    ssh.sendline('commit')
                    i = ssh.expect(PROMPT + [COMMIT_SUCCESS_MATCH, COMMIT_FAILED_MATCH], TIMEOUT)
                if i == 6:
                    return ssh.before.rstrip(user), 0
                if i == 7:
                    return ssh.before.rstrip(user), 1
    finally:
        if ssh:
            ssh.close()


def copy_file_from_switch(host, user, password, server_user, server_passwd, server_ip, source, dest):
    try:
        ssh = login(host, user, password)
        i = ssh.expect(PROMPT, TIMEOUT)

        if i == 3:
            # in linux mode
            ssh.sendline('cli')
            i = ssh.expect(PROMPT, TIMEOUT)
        if i == 2:
            scp_cmd = 'bash \"scp ' + source + ' ' + server_user + '@' + server_ip + ':' + dest + '\"'
            ssh.sendline(scp_cmd)
            i = ssh.expect([SSH_CONFIRM_MATCH, PASSWD_MATCH], TIMEOUT)
        if i == 0:
            ssh.sendline('yes')
            i = ssh.expect([SSH_CONFIRM_MATCH, PASSWD_MATCH], TIMEOUT)
        if i == 1:
            ssh.sendline(server_passwd)
            i = ssh.expect(PROMPT, TIMEOUT)
    finally:
        if ssh:
            ssh.close()


def login(host, user, password):
    ssh = pexpect.spawn('ssh %s@%s' % (user, host))

    # login
    i = ssh.expect([SSH_CONFIRM_MATCH, PASSWD_MATCH], TIMEOUT)
    if i == 0:
        ssh.sendline('yes')
        i = ssh.expect([SSH_CONFIRM_MATCH, PASSWD_MATCH], TIMEOUT)
    if i == 1:
        ssh.sendline(password)
    return ssh

ip='************'
loginName='admin'
loginPasswd='pica8'
cmds=['show version']
cmd_in_cli(ip, loginName, loginPasswd, cmds, configure_mode=False)
server_ip='************'
dest='/home/<USER>/'
source='/pica/config/pica_startup.boot'
server_user='pica8'
server_passwd='pica8pica8'
copy_file_from_switch(ip, loginName, loginPasswd,server_user,server_passwd, server_ip, source, dest)