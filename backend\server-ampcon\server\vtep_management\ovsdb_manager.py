from ovs.db import idl
from ovs import poller
import logging
from queue import Queue
from server.south_api.ovsdb import ovsdb
import types

Log = logging.getLogger(__name__)


def notify(self, event, row, updates=None):
    Log.debug("An OVSDB event happend: %s %s %s", event,row,updates)
    self.event_queue.put([event, row, updates])


class Ovsdb_manager(ovsdb.OvsdbManager):

    def __init__(self, sn, ip):
        super(Ovsdb_manager, self).__init__(sn, ip)

        # self.idl_inst.notify = notify
        self.local_mac_dict = {}
        self.ovsdb_logical_sw = {}
        self.idl.event_queue = Queue()
        self.idl.notify = types.MethodType(notify, self.idl)
        self.disconnected_times = 0

    def init_schema(self):
        schema_helper = idl.SchemaHelper("ovsdb.ovsschema")
        schema_helper.register_columns("Physical_Switch", ["management_ips", "tunnel_ips", "switch_fault_status"])
        schema_helper.register_columns("Physical_Port", ["name", "vlan_bindings"])
        schema_helper.register_columns("Logical_Switch", ["name", "tunnel_key"])
        schema_helper.register_columns("Physical_Locator", ["dst_ip", "encapsulation_type"])
        schema_helper.register_columns("Physical_Locator_Set", ["locators"])
        schema_helper.register_columns("Ucast_Macs_Remote", ["MAC", "locator", "logical_switch"])
        schema_helper.register_columns("Ucast_Macs_Local", ["MAC", "locator", "logical_switch"])
        schema_helper.register_columns("Mcast_Macs_Remote", ["MAC", "locator_set", "logical_switch"])
        schema_helper.register_columns("Arp_Sources_Local", ["locator", "src_mac"])
        schema_helper.register_columns("Arp_Sources_Remote", ["locator", "src_mac"])
        return schema_helper

    def add_locator(self, remote_ip):
        txn = idl.Transaction(self.idl)
        locator = txn.insert(self.idl.tables["Physical_Locator"])
        locator.dst_ip = remote_ip
        locator.encapsulation_type = 'vxlan_over_ipv4'
        return self.handle_commit(txn)

    def get_arp_local_macs(self):
        self.run()
        return self.idl.tables["Arp_Sources_Local"].rows.values()

    def get_arp_remote_macs(self):
        self.run()
        return self.idl.tables["Arp_Sources_Remote"].rows.values()

    def add_arp_remote_mac(self, mac, remote_ip):
        locator = self.get_locator(remote_ip)
        txn = idl.Transaction(self.idl)
        if not locator:
            locator = txn.insert(self.idl.tables["Physical_Locator"])
            locator.dst_ip = remote_ip
            locator.encapsulation_type = 'vxlan_over_ipv4'
        arp_remote = txn.insert(self.idl.tables["Arp_Sources_Remote"])
        arp_remote.src_mac = mac
        arp_remote.locator = locator
        self.handle_commit(txn)

    def del_arp_remote_mac(self, macs):
        self.run()
        del_nums = 0
        txn = idl.Transaction(self.idl)
        for mac_remote in self.idl.tables["Arp_Sources_Remote"].rows.values():
            if mac_remote.src_mac in macs:
                mac_remote.delete()
                del_nums += 1
                if del_nums == 10:
                    self.handle_commit(txn)
                    txn = idl.Transaction(self.idl)

        if del_nums > 0:
            self.handle_commit(txn)
        else:
            txn.abort()

    def add_logical_switch(self, vlan=None):
        txn = idl.Transaction(self.idl)
        logical_switch = txn.insert(self.idl.tables["Logical_Switch"])
        logical_switch.name = "ls" + str(vlan)
        logical_switch.tunnel_key = 10000 + int(vlan)
        self.handle_commit(txn)

    def del_logical_switch(self, vlan=None):
        txn = idl.Transaction(self.idl)
        for ls in self.idl.tables["Logical_Switch"].rows.values():
            if ls.tunnel_key[0] == 10000 + vlan:
                self.del_logical_switch_related(txn, ls.uuid, vlan)
                ls.delete()
        self.handle_commit(txn)

    def add_logical_switches(self, vlans=[]):
        txn = idl.Transaction(self.idl)
        for vlan in vlans:
            logical_switch = txn.insert(self.idl.tables["Logical_Switch"])
            logical_switch.name = "ls" + str(vlan)
            logical_switch.tunnel_key = 10000 + int(vlan)
        self.handle_commit(txn)

    def del_logical_switches(self, vlans=[]):
        txn = idl.Transaction(self.idl)
        for vlan in vlans:
            for ls in self.idl.tables["Logical_Switch"].rows.values():
                if ls.tunnel_key[0] == 10000 + vlan:
                    self.del_logical_switch_related(txn, ls.uuid, vlan)
                    ls.delete()
        self.handle_commit(txn)

    def del_logical_switch_related(self, txn, uuid, vlan):
        for mcast_r in self.idl.tables["Mcast_Macs_Remote"].rows.values():
            if mcast_r.logical_switch.uuid  == uuid:
                mcast_r.delete()
        for row in self.idl.tables["Physical_Port"].rows.values():
            tmp = row.vlan_bindings
            if vlan in tmp:
                tmp.pop(vlan)
                row.vlan_bindings=tmp

    def add_port_binding(self,port=None, vlan=None):
        txn = idl.Transaction(self.idl)
        #find the logic switch
        vlan_mapp_ls = None
        for ls in self.idl.tables["Logical_Switch"].rows.values():
            if int(ls.tunnel_key[0]) == 10000+vlan:
                vlan_mapp_ls = ls
        for row in self.idl.tables["Physical_Port"].rows.values():
            if row.name == port:
                tmp = row.vlan_bindings
                tmp.update({vlan: vlan_mapp_ls})
                row.vlan_bindings=tmp
        self.handle_commit(txn)

    def del_port_binding(self,port=None, vlan=None):
        txn = idl.Transaction(self.idl)
        #find the logic switch
        vlan_mapp_ls = None
        for ls in self.idl.tables["Logical_Switch"].rows.values():
            if int(ls.tunnel_key[0]) == 10000+vlan:
                vlan_mapp_ls = ls
        for row in self.idl.tables["Physical_Port"].rows.values():
            if row.name == port:
                tmp = row.vlan_bindings
                tmp.pop(vlan)
                row.vlan_bindings=tmp
        self.handle_commit(txn)

    def get_port_binding(self):
        self.run()
        # should be {"ge-1/1/1": {"100": "10100", "200": "10200"}}
        port_binding = {}
        for port in self.idl.tables["Physical_Port"].rows.values():
            temp_vlan_bind = {}
            for vlan in port.vlan_bindings.keys():
                temp_vlan_bind.update({vlan: vlan+10000})
            port_binding.update({port.name: temp_vlan_bind})
        return port_binding

    def add_remote_mac(self,mac=None,vlan=None,vtep_ip=None):
        #check there is no existing entry in it, otherwise, return
        for item in self.idl.tables["Ucast_Macs_Remote"].rows.values():
            if item.MAC == mac and item.locator.dst_ip == vtep_ip:
                Log.warn("::::There is existing entry for %s %s", mac, vtep_ip )
                return
        txn = idl.Transaction(self.idl)
        vlan_mapp_ls = None
        for ls in self.idl.tables["Logical_Switch"].rows.values():
            if int(ls.tunnel_key[0]) == 10000+vlan:
                vlan_mapp_ls = ls
        locater = None
        #maybe tbere is no locater in here, we need add it when not found
        for lc in self.idl.tables["Physical_Locator"].rows.values():
            if lc.dst_ip == vtep_ip:
                locater = lc
        if locater == None:
            Log.info("::::There is no locater for %s, will create one", vtep_ip)
            new_loc = txn.insert(self.idl.tables["Physical_Locator"])
            new_loc.dst_ip = vtep_ip
            new_loc.encapsulation_type = "vxlan_over_ipv4"
            locater = new_loc
        remote_mac = txn.insert(self.idl.tables["Ucast_Macs_Remote"])
        remote_mac.MAC = mac
        remote_mac.logical_switch = vlan_mapp_ls
        remote_mac.locator = locater
        self.handle_commit(txn)

    def del_remote_mac(self,mac=None,vlan=None):
        txn = idl.Transaction(self.idl)
        vlan_mapp_ls = None
        for ls in self.idl.tables["Logical_Switch"].rows.values():
            if int(ls.tunnel_key[0]) == 10000+vlan:
                vlan_mapp_ls = ls
        for mac_entry in self.idl.tables["Ucast_Macs_Remote"].rows.values():
            if mac_entry.MAC == mac and mac_entry.logical_switch == vlan_mapp_ls:
                mac_entry.delete()
        self.handle_commit(txn)

    def del_local_mac_by_logic_uuid(self, uuid):
        txn = idl.Transaction(self.idl)
        for ls in self.idl.tables["Ucast_Macs_Local"].rows.values():
            if ls.logical_switch.uuid == uuid:
                ls.delete()
        self.handle_commit(txn)

    def get_vtep_tunnel_ip(self):
        self.run()
        if self.idl.tables["Physical_Switch"].rows and \
                self.idl.tables["Physical_Switch"].rows.values()[0].tunnel_ips:
            tunnel_ip = self.idl.tables["Physical_Switch"].rows.values()[0].tunnel_ips[0]
            return tunnel_ip
        return

    def get_all_local_mac(self):
        self.run()
        # the reture should be {'22:22:22:22:22:22.10100':'************', '22:22:22:22:22:33.10100':'************'}
        mac_dict = {}
        logic_switches = {}
        for mac in self.idl.tables["Ucast_Macs_Local"].rows.values():
            mac_dict.update({mac.MAC + '.' + str(mac.logical_switch.tunnel_key[0]): mac.locator.dst_ip})
            logic_switches.update({mac.MAC + '.' + str(mac.logical_switch.tunnel_key[0]): mac.logical_switch.uuid})
        return mac_dict, logic_switches

    def get_all_remote_mac(self):
        self.run()
        #the reture should be {'22:22:22:22:22:22.10100':'************', '22:22:22:22:22:33.10100':'************'}
        mac_dict = {}
        for mac in self.idl.tables["Ucast_Macs_Remote"].rows.values():
            mac_dict.update({ mac.MAC + '.'+ str(mac.logical_switch.tunnel_key[0]): mac.locator.dst_ip})
        return mac_dict

    def get_vlans_list(self):
        self.run()
        vlans = []
        for ls in self.idl.tables["Logical_Switch"].rows.values():
            vlans.append(int(ls.tunnel_key[0])-10000)
        return vlans

    def get_logic_sw_list(self):
        self.run()
        ls = []
        for switch in self.idl.tables["Logical_Switch"].rows.values():
            ls.append(switch.name)
        return ls

    def get_connection_status(self):
        self.run()
        if self.idl._session.is_connected():
            self.disconnected_times = 0
        else:
            self.disconnected_times = self.disconnected_times + 1
        return self.idl._session.is_connected()

    def get_table(self, table_name):
        self.run()
        return self.idl.tables[table_name].rows.values()

    def get_all_logic_switches(self):
        return self.get_table('Logical_Switch')

    def get_logic_switch(self, vni):
        logic_switches = self.get_table('Logical_Switch')
        for logic_switch in logic_switches:
            if logic_switch.tunnel_key[0] == vni:
                return logic_switch

    def get_locator(self, remote_ip):
        self.run()
        locators = self.idl.tables["Physical_Locator"].rows.values()
        for locator in locators:
            if locator.dst_ip == remote_ip:
                return locator

    def get_locator_ip(self, uuid):
        self.run()
        locators = self.idl.tables["Physical_Locator"].rows.values()
        for locator in locators:
            if locator.uuid == uuid:
                return locator.dst_ip

    def get_locator_set(self, locator_uuid):
        self.run()
        locator_sets = self.idl.tables["Physical_Locator_Set"].rows.values()
        for locator_set in locator_sets:
            if locator_set.locators[0].uuid == locator_uuid:
                return locator_set

    def get_mcast_remote(self, locator_set_uuid, logic_uuid):
        self.run()
        mcast_remotes = self.idl.tables["Mcast_Macs_Remote"].rows.values()
        for mcast_remote in mcast_remotes:
            if mcast_remote.locator_set.uuid == locator_set_uuid and mcast_remote.logical_switch.uuid == logic_uuid:
                return mcast_remote

    def add_logic_switch(self, vni):
        txn = idl.Transaction(self.idl)
        logic_switch = txn.insert(self.idl.tables["Logical_Switch"])
        logic_switch.name = 'ls' + str(int(vni) - 10000)
        logic_switch.tunnel_key = vni
        self.handle_commit(txn)
        return logic_switch

    def add_logic_switches(self, tunnel_keys):
        for tunnel_key in tunnel_keys:
            txn = idl.Transaction(self.idl)
            logic_switch = txn.insert(self.idl.tables["Logical_Switch"])
            logic_switch.name = 'ls' + str(int(tunnel_key) - 10000)
            logic_switch.tunnel_key = tunnel_key
            self.handle_commit(txn)

    def set_mcast_macs_remote(self, remote_ip, have_tunnel_keys):

        locator = self.get_locator(remote_ip)
        locator_set = None
        if locator:
            locator_set = self.get_locator_set(locator.uuid)

        locator_set_new = False
        txn = idl.Transaction(self.idl)
        if not locator:
            # physical_locator
            locator = txn.insert(self.idl.tables["Physical_Locator"])
            locator.dst_ip = remote_ip
            locator.encapsulation_type = 'vxlan_over_ipv4'

        if not locator_set:
            # physical_locator_set
            locator_set_new = True
            locator_set = txn.insert(self.idl.tables["Physical_Locator_Set"])
            locator_set.locators = locator.uuid

            if have_tunnel_keys:
                logical_uuid = have_tunnel_keys[0]
                mcast_remote = txn.insert(self.idl.tables["Mcast_Macs_Remote"])
                mcast_remote.MAC = 'unknown-dst'
                mcast_remote.locator_set = locator_set.uuid
                mcast_remote.logical_switch = logical_uuid
            else:
                # have no logic switch add, return direct
                txn.abort()
                return []

        self.handle_commit(txn)

        if locator_set_new:
            locator = self.get_locator(remote_ip)
            locator_set = self.get_locator_set(locator.uuid)

        # add mcast_macs_remote
        list_for_add = []
        for logic_switch_uuid in have_tunnel_keys:
            if self.get_mcast_remote(locator_set.uuid, logic_switch_uuid):
                continue
            mcast_row = {}
            mcast_row['mac'] = 'unknown-dst'
            mcast_row['locator_set'] = locator_set.uuid
            mcast_row['logical_switch'] = logic_switch_uuid
            list_for_add.append(mcast_row)
        return list_for_add
    
    def add_mcast_macs_remote(self, mcast_rows):
        txn = idl.Transaction(self.idl)
        for mcast_row in mcast_rows:
            mcast_remote = txn.insert(self.idl.tables["Mcast_Macs_Remote"])
            mcast_remote.MAC = mcast_row['mac']
            mcast_remote.locator_set = mcast_row['locator_set']
            mcast_remote.logical_switch = mcast_row['logical_switch']
        self.handle_commit(txn)

    def del_mcast_remotes(self, del_tunnel_keys):
        # clear no use logic switch ref
        self.run()
        mcast_remotes = self.idl.tables["Mcast_Macs_Remote"].rows.values()
        for mcast_remote in mcast_remotes:
            if mcast_remote.logical_switch.uuid in del_tunnel_keys:
                del_txn = idl.Transaction(self.idl)
                mcast_remote.delete()
                self.handle_commit(del_txn)

    def del_unused_physical_locator(self, switch_dst_ip_list):
        self.run()

        locators = self.idl.tables["Physical_Locator"].rows.values()
        locator_sets = self.idl.tables["Physical_Locator_Set"].rows.values()
        mcast_remotes = self.idl.tables["Mcast_Macs_Remote"].rows.values()
        mcast_remotes_delete_list = []

        for locator in locators:
            if locator.dst_ip not in switch_dst_ip_list:
                # delete Physical_Locator & Physical_Locator_Set & Mcast Macs Remote if dst_ip is not in db
                # Mcast Macs Remote rely on the Physical_Locator_Set
                # Physical_Locator_Set rely on Physical_Locator
                # they are all not is-root, so it will be automatically delete when Mcast Macs Remote is deleted.
                for locator_set in locator_sets:
                    if locator_set.locators[0].uuid == locator.uuid:
                        for mcast_remote in mcast_remotes:
                            if mcast_remote.locator_set.uuid == locator_set.uuid:
                                # mcast_remote.delete()
                                mcast_remotes_delete_list.append(mcast_remote)
        
        del_txn = idl.Transaction(self.idl)
        for i in mcast_remotes_delete_list:
            i.delete()
        self.handle_commit(del_txn)
                
    def get_switch_status(self):
        self.run()
        switch_status = self.idl.tables["Physical_Switch"].rows.values()
        if switch_status:
            return switch_status[0].switch_fault_status
        return []


if __name__ == '__main__':
    version = 0.1

    mgt_ip = '*********'
    sn = 'CN00YNWT282985640149'
    test_ovs = Ovsdb_manager(sn,mgt_ip)
    print("===================")
    print(test_ovs.get_port_binding())

