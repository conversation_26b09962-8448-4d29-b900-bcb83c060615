import {PlusOutlined, MinusOutlined} from "@ant-design/icons";
import {<PERSON><PERSON>, <PERSON>, Col, Divider, Dropdown, Form, Input, Flex, Row, Select, Space} from "antd";
import {useForm} from "antd/es/form/Form";
import React, {useState, useEffect, forwardRef, useImperativeHandle} from "react";
import {AmpConCustomModal} from "./custom_table";

const TemplateEditor = forwardRef((props, ref) => {
    useImperativeHandle(ref, () => ({
        addNewLine: text => {
            setValueList(preList => [...preList, text]);
        },
        getContentValue: () => {
            return valueList.join("\n");
        },
        clearContentValue: () => {
            setValueList([]);
        }
    }));

    const [valueList, setValueList] = useState([]);
    const [paramList, setParamList] = useState([]);
    const actionMap = {
        tolist: "To List",
        tomaplist: "To Map List",
        enum: "To Enum",
        static: "Set Static",
        rename: "Rename",
        associative: "Associative"
    };
    const [setStaticForm] = useForm();
    const [renameForm] = useForm();
    const [toenumForm] = useForm();
    const [enumIndex, setEnumIndex] = useState(0);
    const [associativeForm] = useForm();

    const [currentActionType, setcurrentActionType] = useState("");
    const [showFormModal, setShowFormModal] = useState(false);
    const [globalOuterIndex, setGlobalOuterIndex] = useState(0);
    const [globalLineIndex, setGlobalLineIndex] = useState(0);

    useEffect(() => {
        let tParamList = [];
        valueList.map(row => {
            const treeList = row.split(" ").filter(i => i !== "");
            treeList.map(item => {
                if (tParamList.indexOf(item) < 0 && item.indexOf(":") >= 0) {
                    tParamList = [...tParamList, item];
                }
            });
        });
        setParamList(tParamList);
    }, [valueList]);

    const handleModalFormOK = () => {
        const itemValue = valueList[globalOuterIndex].split(" ");
        if (currentActionType === "enum") {
            toenumForm
                .validateFields()
                .then(values => {
                    const enmuStr = `enum[${Object.values(values).join(",")}]`;
                    const [valueStr] = itemValue[globalLineIndex].split(":");
                    itemValue[globalLineIndex] = `${valueStr}:${enmuStr}`;
                    changeLineValue(globalOuterIndex, itemValue.join(" "));
                    setShowFormModal(false);
                    setEnumIndex(0);
                })
                .catch(() => {});
        }
        if (currentActionType === "static") {
            setStaticForm
                .validateFields()
                .then(values => {
                    const {staticValue} = values;
                    itemValue[globalLineIndex] = staticValue;
                    changeLineValue(globalOuterIndex, itemValue.join(" "));
                    setShowFormModal(false);
                })
                .catch(() => {});
        }
        if (currentActionType === "rename") {
            renameForm
                .validateFields()
                .then(values => {
                    const {renameValue} = values;
                    const [typeStr] = itemValue[globalLineIndex].split(":");
                    itemValue[globalLineIndex] = `${renameValue}:${typeStr}`;
                    changeLineValue(globalOuterIndex, itemValue.join(" "));
                    setShowFormModal(false);
                })
                .catch(() => {});
        }
        if (currentActionType === "associative") {
            associativeForm
                .validateFields()
                .then(values => {
                    const {associativeValue} = values;
                    itemValue[globalLineIndex] = associativeValue;
                    changeLineValue(globalOuterIndex, itemValue.join(" "));
                    setShowFormModal(false);
                })
                .catch(() => {});
        }
    };
    const handleModalFormCancel = () => {
        setShowFormModal(false);
        setEnumIndex(0);
    };

    const renderModalForm = () => {
        if (currentActionType === "enum") {
            return (
                <Form form={toenumForm} key="toenumForm" preserve={false} style={{minHeight: "256.23px"}}>
                    <Row gutter={16}>
                        <Col span={16}>
                            <Form.Item
                                name="enum0"
                                label="Enum Option 0"
                                rules={[{required: true, message: "This field is required."}]}
                                validateTrigger={["onChange", "onBlur"]}
                            >
                                <Input
                                    placeholder="Enum Option Value"
                                    style={{width: "280px", marginRight: 8, marginLeft: 32}}
                                />
                            </Form.Item>
                        </Col>

                        <Col span={4}>
                            <Button
                                style={{
                                    backgroundColor: "transparent",
                                    color: "#BFBFBF"
                                }}
                                type="link"
                                onClick={() => {
                                    setEnumIndex(enumIndex + 1);
                                }}
                                icon={<PlusOutlined />}
                            />
                        </Col>
                    </Row>
                    {Array.from({length: enumIndex}, (_, i) => (
                        <Row gutter={16}>
                            <Col span={16}>
                                <Form.Item
                                    name={`enum${i + 1}`}
                                    label={`Enum Option ${i + 1}`}
                                    rules={[{required: true, message: "This field is required."}]}
                                    validateTrigger={["onChange", "onBlur"]}
                                >
                                    <Input
                                        placeholder="Enum Option Value"
                                        style={{width: "280px", marginRight: 8, marginLeft: 32}}
                                    />
                                </Form.Item>
                            </Col>

                            <Col span={4}>
                                <Button
                                    style={{
                                        backgroundColor: "transparent",
                                        color: "#BFBFBF"
                                    }}
                                    type="minus"
                                    onClick={() => {
                                        setEnumIndex(enumIndex - 1);
                                    }}
                                    icon={<MinusOutlined />}
                                />
                            </Col>
                        </Row>
                    ))}
                </Form>
            );
        }
        if (currentActionType === "static") {
            return (
                <Form form={setStaticForm} key="setStaticForm" preserve={false} style={{minHeight: "256.23px"}}>
                    <Form.Item
                        label="Set Static"
                        name="staticValue"
                        rules={[{required: true, message: "This field is required."}]}
                    >
                        <Input style={{width: "280px", marginLeft: 32}} />
                    </Form.Item>
                </Form>
            );
        }
        if (currentActionType === "rename") {
            return (
                <Form form={renameForm} key="renameForm" preserve={false} style={{minHeight: "256.23px"}}>
                    <Form.Item
                        label="Rename"
                        name="renameValue"
                        rules={[{required: true, message: "This field is required."}]}
                    >
                        <Input style={{width: "280px", marginLeft: 32}} />
                    </Form.Item>
                </Form>
            );
        }
        if (currentActionType === "associative") {
            return (
                <Form form={associativeForm} key="associativeForm" preserve={false} style={{minHeight: "256.23px"}}>
                    <Form.Item
                        label="Associative"
                        name="associativeValue"
                        rules={[{required: true, message: "This field is required."}]}
                    >
                        <Select
                            options={paramList.map(item => {
                                return {value: item, label: item};
                            })}
                            style={{width: "280px", marginLeft: 32}}
                        />
                    </Form.Item>
                </Form>
            );
        }
        return;
    };

    const changeLineValue = (outerIndex, lineContent) => {
        const currValueList = [...valueList];
        currValueList[outerIndex] = lineContent;
        setValueList(currValueList);
    };

    const menuItemOnClick = (outerIndex, index, key) => {
        setGlobalOuterIndex(outerIndex);
        setGlobalLineIndex(index);

        const action = key.key.split("-")[key.key.split("-").length - 1];
        setcurrentActionType(action);

        const itemValue = valueList[outerIndex].split(" ");
        if (action === "tolist") {
            let [valueStr, typeStr] = itemValue[index].split(":");
            if (valueStr.indexOf("_map_list") >= 0 || valueStr.indexOf("_list") >= 0) {
                valueStr = valueStr.replace("_map_list", "").replace("_list", "");
                typeStr = typeStr.replace("_map_list", "").replace("_list", "");
            }
            itemValue[index] = `${valueStr}_list:${typeStr}_list`;
            changeLineValue(outerIndex, itemValue.join(" "));
        }
        if (action === "tomaplist") {
            let [valueStr, typeStr] = itemValue[index].split(":");

            if (valueStr.indexOf("_map_list") >= 0 || valueStr.indexOf("_list") >= 0) {
                valueStr = valueStr.replace("_map_list", "").replace("_list", "");
                typeStr = typeStr.replace("_map_list", "").replace("_list", "");
            }

            itemValue[index] = `${valueStr}_map_list:${typeStr}_map_list`;
            changeLineValue(outerIndex, itemValue.join(" "));
        }
        if (action === "enum") {
            setShowFormModal(true);
        }
        if (action === "static") {
            setShowFormModal(true);
        }
        if (action === "rename") {
            setShowFormModal(true);
        }
        if (action === "associative") {
            setShowFormModal(true);
        }
    };

    const removeLine = lineIndex => {
        setValueList(valueList.filter((_, index) => index !== lineIndex));
    };

    const converToLine = (treePath, outerIndex) => {
        const treeList = treePath.split(" ").filter(i => i !== "");
        return (
            <Row>
                <Col>
                    <Space>
                        {treeList.map((item, index) => {
                            const menuItems = [
                                {
                                    key: `${outerIndex}-${index}-tolist`,
                                    label: "To List"
                                },
                                {
                                    key: `${outerIndex}-${index}-tomaplist`,
                                    label: "To Map List"
                                },
                                {
                                    key: `${outerIndex}-${index}-enum`,
                                    label: "To enum"
                                },
                                {
                                    key: `${outerIndex}-${index}-static`,
                                    label: "Set Static"
                                },
                                {
                                    key: `${outerIndex}-${index}-rename`,
                                    label: "Rename"
                                },
                                {
                                    key: `${outerIndex}-${index}-associative`,
                                    label: "Associative"
                                }
                            ];

                            if (item.indexOf(":") >= 0) {
                                return (
                                    <Dropdown
                                        menu={{
                                            items: menuItems,
                                            onClick: key => menuItemOnClick(outerIndex, index, key)
                                        }}
                                    >
                                        <a className="drop-down-click" onClick={e => e.preventDefault()}>
                                            {item}
                                        </a>
                                    </Dropdown>
                                );
                            }
                            return <p>{item}</p>;
                        })}

                        <Button style={{padding: "4px 7px"}} type="text" danger onClick={() => removeLine(outerIndex)}>
                            X
                        </Button>
                    </Space>
                </Col>
            </Row>
        );
    };
    return (
        <>
            {/* <Modal
                title={actionMap[currentActionType]}
                open={showFormModal}
                onOk={handleModalFormOK}
                onCancel={handleModalFormCancel}
                destroyOnClose
                className="ampcon-middle-modal"
            >
                {renderModalForm()}
            </Modal> */}
            <AmpConCustomModal
                modalClass="ampcon-middle-modal"
                title={actionMap[currentActionType]}
                onCancel={handleModalFormCancel}
                childItems={renderModalForm()}
                isModalOpen={showFormModal}
                footer={
                    <Flex vertical>
                        <Divider style={{marginTop: 8}} />
                        <Flex justify="flex-end">
                            <Button htmlType="button" onClick={handleModalFormCancel}>
                                Cancel
                            </Button>
                            <Button type="primary" onClick={handleModalFormOK}>
                                Ok
                            </Button>
                        </Flex>
                    </Flex>
                }
            />
            <Card style={{height: "100%"}}>
                {valueList.map((row, index) => {
                    return converToLine(row, index);
                })}
            </Card>
        </>
    );
});

export default TemplateEditor;
