import sys
from server import cfg
from .session_factory import EngineFacade
import sqlalchemy
import datetime
# from werkzeug.local import Local, release_local
# from sqlalchemy.util.compat import threading
from _threading_local import local

_FACADE = None

MAX_RETRIES = 10

# thread_sessions = threading.local()
thread_sessions = local()


def _create_facade_lazily():
    global _FACADE
    
    if _FACADE is None:
        _FACADE = EngineFacade.from_config(cfg.CONF)
    
    return _FACADE


def get_engine():
    """Helper method to grab engine."""
    facade = _create_facade_lazily()
    return facade.get_engine()


def clear_facade():
    global _FACADE
    _FACADE = None


def get_session(autoflush=True, autocommit=True, expire_on_commit=False):
    """Helper method to grab session."""
    facade = _create_facade_lazily()
    if hasattr(thread_sessions, 'session'):
        s_same_args = thread_sessions.autoflush == autoflush and thread_sessions.autocommit == autocommit
        same_args = s_same_args and expire_on_commit == expire_on_commit
        session = facade.get_session()
        if same_args:
            return session
        else:
            clear_session()
    session = facade.get_session(autoflush=autoflush,
                                 autocommit=autocommit,
                                 expire_on_commit=expire_on_commit)
    thread_sessions.session = True
    thread_sessions.autoflush = autoflush
    thread_sessions.autocommit = autocommit
    thread_sessions.expire_on_commit = expire_on_commit
    return session


def clear_session():
    facade = _create_facade_lazily()
    facade.remove_session()
    try:
        del thread_sessions.session
        del thread_sessions.autoflush
        del thread_sessions.autocommit
        del thread_sessions.expire_on_commit
    except AttributeError:
        pass


def clear_all_sessions():
    global thread_sessions
    thread_sessions = local()


def timer_clean_stats_data():
    engine = get_engine()
    meta = sqlalchemy.MetaData()
    meta.reflect(bind=engine)
    too_old = datetime.datetime.today() - datetime.timedelta(days=1)
    for table in reversed(meta.sorted_tables):
        engine.execute(table.delete().where(table.c.timestampinserted <= too_old))
