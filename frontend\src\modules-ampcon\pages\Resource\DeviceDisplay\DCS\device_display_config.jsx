export const SIDES = ["front", "rear"];

const CARDWIDTH = 393;
const INIT = 180;

export const LED_STATUS = Object.freeze({
    GREEN: 1,
    GREEN_FLASHING: 2,
    RED: 3,
    RED_FLASHING: 4
});

const DCS_CHASSIS_CONFIG = {
    1: {
        side: SIDES[0],
        left: INIT,
        top: 0
    },
    2: {
        side: SIDES[0],
        left: INIT + CARDWIDTH,
        top: 0
    },
    3: {
        side: SIDES[0],
        left: INIT + 20 + 2 * CARDWIDTH,
        top: 0
    },
    4: {
        side: SIDES[0],
        left: INIT + 20 + 3 * CARDWIDTH,
        top: 0
    },
    5: {
        side: SIDES[1],
        left: 1260,
        top: 5
    },
    6: {
        side: SIDES[1],
        left: 1093,
        top: 5
    },
    7: {
        side: SIDES[1],
        left: 925,
        top: 5
    },
    8: {
        side: SIDES[1],
        left: 759,
        top: 5
    },
    9: {
        side: SIDES[1],
        left: 420,
        top: 5
    },
    10: {
        side: SIDES[1],
        left: 78,
        top: 5
    },
    16: {
        side: SIDES[1],
        left: 1428,
        top: 5
    }
};

const DCS_NMU_CONFIG = {
    "LED-PWR1": {
        top: 71,
        left: 40,
        status: {
            key: "businessInfo.query.power_state1",
            judge: statusValues => {
                const {key} = statusValues;
                if (key === "1") {
                    return LED_STATUS.GREEN;
                }
                return null;
            }
        }
    },
    "LED-PWR2": {
        top: 90,
        left: 40,
        status: {
            key: "businessInfo.query.power_state2",
            judge: statusValues => {
                const {key} = statusValues;
                if (key === "1") {
                    return LED_STATUS.GREEN;
                }
                return null;
            }
        }
    },
    "LED-SYS": {
        top: 108,
        left: 40,
        status: {
            key: "businessInfo.query",
            judge: statusValues => {
                const {key} = statusValues;
                if (key) {
                    return LED_STATUS.GREEN;
                }
                return null;
            }
        }
    }
};

function arrayAnyZero(arr, start, end) {
    start = Math.max(0, start);
    end = Math.min(arr.length - 1, end);
    for (let i = start; i <= end; i++) {
        if (parseInt(arr[i]) === 0) {
            return true;
        }
    }
    return false;
}

function arrayALLZero(arr, start, end) {
    start = Math.max(0, start);
    end = Math.min(arr.length - 1, end);
    for (let i = start; i <= end; i++) {
        if (parseInt(arr[i]) !== 0) {
            return false;
        }
    }
    return true;
}

const DCS_4M4_CONFIG = {
    "LED-SYS": {
        top: 81,
        left: 11,
        status: {
            key: "businessInfo.query",
            judge: statusValues => {
                const {key} = statusValues;
                if (key) {
                    return LED_STATUS.GREEN;
                }
                return null;
            }
        }
    },
    "LED-ALARM": {
        top: 122,
        left: 12,
        status: {
            key: "businessInfo.query.temperature_alarm",
            threshold: "businessInfo.query.rx_alarm",
            threshold2: "businessInfo.query.tx_alarm",
            threshold3: "businessInfo.query.tx_los_alarm",
            threshold4: "businessInfo.query.tx_lol_alarm",
            threshold5: "businessInfo.query.rx_los_alarm",
            threshold6: "businessInfo.query.rx_lol_alarm",
            judge: statusValues => {
                const {key, threshold, threshold2, threshold3, threshold4, threshold5, threshold6} = statusValues;
                if (
                    threshold2[0] === "0" ||
                    threshold[0] === "0" ||
                    key[0] === "0" ||
                    arrayAnyZero(threshold2, 1, 4) ||
                    arrayALLZero(threshold3, 0, 4) ||
                    arrayALLZero(threshold4, 0, 4) ||
                    arrayALLZero(threshold5, 0, 4) ||
                    arrayALLZero(threshold6, 0, 4)
                ) {
                    return LED_STATUS.RED;
                }
                if (arrayAnyZero(key, 1, 4) || arrayAnyZero(threshold, 1, 4)) {
                    return LED_STATUS.RED_FLASHING;
                }
                return null;
            }
        }
    },
    "LED-L1": {
        top: 38,
        left: 208,
        status: {
            key: "businessInfo.query.existence",
            threshold: "businessInfo.query.rx_alarm",
            threshold2: "businessInfo.query.tx_alarm",
            judge: statusValues => {
                const {key, threshold, threshold2} = statusValues;
                if (key[0] === "0") {
                    return null;
                }
                if (threshold[0] === "0" || threshold2[0] === "0") {
                    return LED_STATUS.RED;
                }
                return null;
            }
        }
    },
    "LED-C1": {
        top: 152,
        left: 73,
        status: {
            key: "businessInfo.query.existence",
            threshold: "businessInfo.query.rx_alarm",
            threshold2: "businessInfo.query.tx_alarm",
            judge: statusValues => {
                const {key, threshold, threshold2} = statusValues;
                if (key[1] === "0") {
                    return null;
                }
                if (threshold[1] === "0" || threshold2[1] === "0") {
                    return LED_STATUS.RED;
                }
                return null;
            }
        }
    },
    "LED-C2": {
        top: 152,
        left: 158,
        status: {
            key: "businessInfo.query.existence",
            threshold: "businessInfo.query.rx_alarm",
            threshold2: "businessInfo.query.tx_alarm",
            judge: statusValues => {
                const {key, threshold, threshold2} = statusValues;
                if (key[2] === "0") {
                    return null;
                }
                if (threshold[2] === "0" || threshold2[2] === "0") {
                    return LED_STATUS.RED;
                }
                return null;
            }
        }
    },
    "LED-C3": {
        top: 152,
        left: 242,
        status: {
            key: "businessInfo.query.existence",
            threshold: "businessInfo.query.rx_alarm",
            threshold2: "businessInfo.query.tx_alarm",
            judge: statusValues => {
                const {key, threshold, threshold2} = statusValues;
                if (key[3] === "0") {
                    return null;
                }
                if (threshold[3] === "0" || threshold2[3] === "0") {
                    return LED_STATUS.RED;
                }
                return null;
            }
        }
    },
    "LED-C4": {
        top: 152,
        left: 327,
        status: {
            key: "businessInfo.query.existence",
            threshold: "businessInfo.query.rx_alarm",
            threshold2: "businessInfo.query.tx_alarm",
            judge: statusValues => {
                const {key, threshold, threshold2} = statusValues;
                if (key[4] === "0") {
                    return null;
                }
                if (threshold[4] === "0" || threshold2[4] === "0") {
                    return LED_STATUS.RED;
                }
                return null;
            }
        }
    },
    "PORT-L1": {
        top: 49,
        left: 174,
        status: {
            key: "businessInfo.query.existence",
            judge: statusValues => {
                const {key} = statusValues;
                return key[0] === "1";
            }
        }
    },
    "PORT-C1": {
        top: 103,
        left: 39,
        status: {
            key: "businessInfo.query.existence",
            judge: statusValues => {
                const {key} = statusValues;
                return key[1] === "1";
            }
        }
    },
    "PORT-C2": {
        top: 103,
        left: 124,
        status: {
            key: "businessInfo.query.existence",
            judge: statusValues => {
                const {key} = statusValues;
                return key[2] === "1";
            }
        }
    },
    "PORT-C3": {
        top: 103,
        left: 208,
        status: {
            key: "businessInfo.query.existence",
            judge: statusValues => {
                const {key} = statusValues;
                return key[3] === "1";
            }
        }
    },
    "PORT-C4": {
        top: 103,
        left: 292,
        status: {
            key: "businessInfo.query.existence",
            judge: statusValues => {
                const {key} = statusValues;
                return key[4] === "1";
            }
        }
    }
};

const DCS_4T4_CONFIG = {
    "LED-SYS": {
        top: 81,
        left: 11,
        status: {
            key: "businessInfo.query",
            judge: statusValues => {
                const {key} = statusValues;
                if (key) {
                    return LED_STATUS.GREEN;
                }
                return null;
            }
        }
    },
    "LED-ALARM": {
        top: 123,
        left: 11,
        status: {
            key: "businessInfo.query.temperature_alarm",
            threshold: "businessInfo.query.rx_alarm",
            threshold2: "businessInfo.query.tx_alarm",
            threshold3: "businessInfo.query.tx_los_alarm",
            threshold4: "businessInfo.query.tx_lol_alarm",
            threshold5: "businessInfo.query.rx_los_alarm",
            threshold6: "businessInfo.query.rx_lol_alarm",
            judge: statusValues => {
                const {key, threshold, threshold2, threshold3, threshold4, threshold5, threshold6} = statusValues;
                if (
                    threshold2[0] === "0" ||
                    threshold[0] === "0" ||
                    key[0] === "0" ||
                    arrayAnyZero(threshold2, 1, 4) ||
                    arrayALLZero(threshold3, 0, 4) ||
                    arrayALLZero(threshold4, 0, 4) ||
                    arrayALLZero(threshold5, 0, 4) ||
                    arrayALLZero(threshold6, 0, 4)
                ) {
                    return LED_STATUS.RED;
                }
                if (arrayAnyZero(key, 1, 4) || arrayAnyZero(threshold, 1, 4)) {
                    return LED_STATUS.RED_FLASHING;
                }
                return null;
            }
        }
    },
    "LED-L1": {
        top: 38,
        left: 74,
        status: {
            key: "businessInfo.query.existence",
            threshold: "businessInfo.query.rx_alarm",
            threshold2: "businessInfo.query.tx_alarm",
            judge: statusValues => {
                const {key, threshold, threshold2} = statusValues;
                if (key[0] === "0") {
                    return null;
                }
                if (threshold[0] === "0" || threshold2[0] === "0") {
                    return LED_STATUS.RED;
                }
                return null;
            }
        }
    },
    "LED-L2": {
        top: 38,
        left: 158,
        status: {
            key: "businessInfo.query.existence",
            threshold: "businessInfo.query.rx_alarm",
            threshold2: "businessInfo.query.tx_alarm",
            judge: statusValues => {
                const {key, threshold, threshold2} = statusValues;
                if (key[1] === "0") {
                    return null;
                }
                if (threshold[1] === "0" || threshold2[1] === "0") {
                    return LED_STATUS.RED;
                }
                return null;
            }
        }
    },
    "LED-L3": {
        top: 38,
        left: 241,
        status: {
            key: "businessInfo.query.existence",
            threshold: "businessInfo.query.rx_alarm",
            threshold2: "businessInfo.query.tx_alarm",
            judge: statusValues => {
                const {key, threshold, threshold2} = statusValues;
                if (key[2] === "0") {
                    return null;
                }
                if (threshold[2] === "0" || threshold2[2] === "0") {
                    return LED_STATUS.RED;
                }
                return null;
            }
        }
    },
    "LED-L4": {
        top: 38,
        left: 324,
        status: {
            key: "businessInfo.query.existence",
            threshold: "businessInfo.query.rx_alarm",
            threshold2: "businessInfo.query.tx_alarm",
            judge: statusValues => {
                const {key, threshold, threshold2} = statusValues;
                if (key[3] === "0") {
                    return null;
                }
                if (threshold[3] === "0" || threshold2[3] === "0") {
                    return LED_STATUS.RED;
                }
                return null;
            }
        }
    },
    "LED-C1": {
        top: 152,
        left: 74,
        status: {
            key: "businessInfo.query.existence",
            threshold: "businessInfo.query.rx_alarm",
            threshold2: "businessInfo.query.tx_alarm",
            judge: statusValues => {
                const {key, threshold, threshold2} = statusValues;
                if (key[4] === "0") {
                    return null;
                }
                if (threshold[4] === "0" || threshold2[4] === "0") {
                    return LED_STATUS.RED;
                }
                return null;
            }
        }
    },
    "LED-C2": {
        top: 152,
        left: 158,
        status: {
            key: "businessInfo.query.existence",
            threshold: "businessInfo.query.rx_alarm",
            threshold2: "businessInfo.query.tx_alarm",
            judge: statusValues => {
                const {key, threshold, threshold2} = statusValues;
                if (key[5] === "0") {
                    return null;
                }
                if (threshold[5] === "0" || threshold2[5] === "0") {
                    return LED_STATUS.RED;
                }
                return null;
            }
        }
    },
    "LED-C3": {
        top: 152,
        left: 241,
        status: {
            key: "businessInfo.query.existence",
            threshold: "businessInfo.query.rx_alarm",
            threshold2: "businessInfo.query.tx_alarm",
            judge: statusValues => {
                const {key, threshold, threshold2} = statusValues;
                if (key[6] === "0") {
                    return null;
                }
                if (threshold[6] === "0" || threshold2[6] === "0") {
                    return LED_STATUS.RED;
                }
                return null;
            }
        }
    },
    "LED-C4": {
        top: 152,
        left: 324,
        status: {
            key: "businessInfo.query.existence",
            threshold: "businessInfo.query.rx_alarm",
            threshold2: "businessInfo.query.tx_alarm",
            judge: statusValues => {
                const {key, threshold, threshold2} = statusValues;
                if (key[7] === "0") {
                    return null;
                }
                if (threshold[7] === "0" || threshold2[7] === "0") {
                    return LED_STATUS.RED;
                }
                return null;
            }
        }
    },
    "PORT-L1": {
        top: 49,
        left: 40,
        status: {
            key: "businessInfo.query.existence",
            judge: statusValues => {
                const {key} = statusValues;
                return key[0] === "1";
            }
        }
    },
    "PORT-L2": {
        top: 49,
        left: 123,
        status: {
            key: "businessInfo.query.existence",
            judge: statusValues => {
                const {key} = statusValues;
                return key[1] === "1";
            }
        }
    },
    "PORT-L3": {
        top: 49,
        left: 207,
        status: {
            key: "businessInfo.query.existence",
            judge: statusValues => {
                const {key} = statusValues;
                return key[2] === "1";
            }
        }
    },
    "PORT-L4": {
        top: 49,
        left: 290,
        status: {
            key: "businessInfo.query.existence",
            judge: statusValues => {
                const {key} = statusValues;
                return key[3] === "1";
            }
        }
    },
    "PORT-C1": {
        top: 103,
        left: 40,
        status: {
            key: "businessInfo.query.existence",
            judge: statusValues => {
                const {key} = statusValues;
                return key[4] === "1";
            }
        }
    },
    "PORT-C2": {
        top: 103,
        left: 123,
        status: {
            key: "businessInfo.query.existence",
            judge: statusValues => {
                const {key} = statusValues;
                return key[5] === "1";
            }
        }
    },
    "PORT-C3": {
        top: 103,
        left: 207,
        status: {
            key: "businessInfo.query.existence",
            judge: statusValues => {
                const {key} = statusValues;
                return key[6] === "1";
            }
        }
    },
    "PORT-C4": {
        top: 103,
        left: 290,
        status: {
            key: "businessInfo.query.existence",
            judge: statusValues => {
                const {key} = statusValues;
                return key[7] === "1";
            }
        }
    }
};

const DCS_PSU_CONFIG = {
    "LED-SYS": {
        top: 17,
        left: 294,
        status: {
            key: "businessInfo.query.power_state",
            judge: statusValues => {
                const {key} = statusValues;
                if (key === "1") {
                    return LED_STATUS.GREEN;
                }
                return null;
            }
        }
    },
    "LED-ALARM": {
        top: 124,
        left: 11
    }
};

const DCS_FAN_CONFIG = {
    "LED-SYS": {
        top: 26,
        left: 99,
        status: {
            key: "businessInfo.query",
            judge: statusValues => {
                const {key} = statusValues;
                if (key) {
                    return LED_STATUS.GREEN;
                }
                return null;
            }
        }
    },
    "LED-ALARM": {
        top: 26,
        left: 122,
        status: {
            key: "businessInfo.query.state",
            judge: statusValues => {
                const {key} = statusValues;
                if (key === "0") {
                    return LED_STATUS.RED;
                }
                return null;
            }
        }
    }
};

export const chassisConfig = {
    D6000: DCS_CHASSIS_CONFIG,
    NMU: DCS_NMU_CONFIG,
    "4ME4C": DCS_4M4_CONFIG,
    "4T4E4C": DCS_4T4_CONFIG,
    PSU: DCS_PSU_CONFIG,
    FAN: DCS_FAN_CONFIG
};
