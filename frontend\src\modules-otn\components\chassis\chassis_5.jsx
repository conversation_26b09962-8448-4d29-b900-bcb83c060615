import {useEffect, useRef, useState} from "react";
import {Button, Dropdown, message, Radio} from "antd";
import {useRequest, useSize} from "ahooks";
import {useDispatch, useSelector} from "react-redux";
import Icon, {ChromeOutlined, DeleteOutlined, UndoOutlined} from "@ant-design/icons";

import {debounce} from "lodash";
import {apiEditRpc, apiGetNEData, objectGet, requestSyncTimer, syncNE} from "@/modules-otn/apis/api";
import {setSelectedItem, setTableFilter} from "@/store/modules/otn/mapSlice";
import {openModalRpc} from "@/modules-otn/components/form/edit_rpc";
import {offLineSvg} from "@/modules-otn/utils/iconSvg";
import useUserRight from "@/modules-otn/hooks/useUserRight";
import {chassisConfigFS, SIDES} from "@/modules-otn/components/chassis/chassis_5_config_fs";
import {openEditComponent} from "@/modules-otn/components/form/edit_component";
import {smallModal} from "@/modules-otn/components/modal/custom_modal";
import styles_common from "./chassis.module.scss";
import styles_fs from "./chassis_5_fs.module.scss";
import Fan_1 from "./img_fs/port/fan.svg?react";

const SUPPORTED_REBOOT_TYPE = [
    "CHASSIS",
    "LINECARD",
    "OCM_CARD",
    "OTDR_CARD",
    "OLP",
    "WSS",
    "TFF",
    "PTM",
    "CONTROLLER_CARD",
    "OA",
    "OLA"
];

const NOT_SUPPORT_DELETE = ["SLOT", "CHASSIS", "PORT", "MCU", "FAN", "POWER_SUPPLY", "TRANSCEIVER", "CONTROLLER_CARD"];

const getMenu = (key, label, icon, onClick, disabled) => ({key, label, icon, onClick, disabled});

const Chassis5 = () => {
    const {runAsync} = useRequest(apiGetNEData, {manual: true});
    const dispatch = useDispatch();
    const {neNameMap} = useSelector(state => state.neName);
    const {dataChanged, upgrade, alarms} = useSelector(state => state.notification);
    const [data, setData] = useState();
    const [menuItems, setMenuItems] = useState([]);
    const {labelList} = useSelector(state => state.languageOTN);
    const {selectedItem, onSelectItem, wrapMock} = useSelector(state => state.map);
    const selectedItemRef = useRef(selectedItem);
    const [syncState, setSyncState] = useState({});
    const [allComponents, setAllComponents] = useState({});
    const containerRef = useRef();
    const readyOnlyRight = useUserRight();
    const size = useSize(containerRef.current);
    const [openDropdown, setOpenDropdown] = useState(false);
    const chassisWrapRef = useRef();
    const chassisRef = useRef();

    useEffect(() => {
        updateData().then();
    }, [selectedItem]);
    useEffect(() => setShowType(0), [onSelectItem]);
    // 0:面板 1: 背板 2: mux
    const [showType, setShowType] = useState(0);
    // 指向选中的模块元素
    const selectedPortRef = useRef({style: {}});
    // 离线
    const [offLine, setOffLine] = useState(false);

    const [showFS, setShowFS] = useState(false);

    const setSelectPort = selectPort => {
        if (selectPort) {
            dispatch(
                setSelectedItem({
                    ...selectedItem,
                    selectPort: {type: selectPort.type, value: selectPort.value},
                    resource: {type: selectPort.type, value: selectPort.value}
                })
            );
            dispatch(
                setTableFilter({
                    type: "NODE_NE",
                    id: selectedItem.value.ne_id,
                    resource: {type: selectPort.type, value: selectPort.value}
                })
            );
        } else {
            const newSelect = {...selectedItem};
            delete newSelect.selectPort;
            dispatch(setSelectedItem(newSelect));
            dispatch(setTableFilter({type: "NODE_NE", id: selectedItem.value.ne_id}));
        }
    };

    useEffect(() => {
        if (size?.width > 1310) {
            document.querySelector("#tabChassis").setAttribute("style", "overflow-x: none;");
        } else {
            document.querySelector("#tabChassis").setAttribute("style", `overflow: hidden;`);
        }
    }, [size]);

    const updateData = async () => {
        let chassisConfigDef;
        let styles;

        const neID = selectedItem?.value?.ne_id;
        if (!neID) return;
        getOffLineState().then();
        const neData = (await runAsync("ne:5:component", {ne_id: neID})).documents;

        const _allComponents = {};
        neData.forEach(item => {
            _allComponents[item.value.data.name] = {
                ...item.value.data.state,
                mcu: item.value.data.mcu ?? "",
                db_key: item.id
            };
        });
        setAllComponents(_allComponents);
        const neAlarms = alarms.filter(alarm => alarm.ne_id === neID);

        // Construct Component tree by component parent parameter
        const treeData = neData
            .map(({id: db_key, value: {data}}) => ({db_key, ...data}))
            .reduce((prev, data, _, components) => {
                if (data.chassis) {
                    setShowFS(true);
                    chassisConfigDef = chassisConfigFS;
                    styles = styles_fs;
                    data.state = {"part-no": data.state["part-no"]};
                }

                if (!data.state) {
                    // eslint-disable-next-line no-console
                    // console.error(`${data.name} has No state`);
                    return prev;
                }
                const parentName = data.state?.parent;
                if (parentName) {
                    const parent = components.filter(i => i.name === parentName)[0];
                    if (parent) {
                        if (!parent.children) parent.children = [];
                        parent.children.push(data);
                    }
                }
                return data.chassis ? data : prev;
            }, null);
        if (treeData === null) {
            // no NE info
            setData(null);
            return;
        }
        // Construct virtual chassis list (Front, Rear, MUX)
        const chassis_config = chassisConfigDef[treeData.state["part-no"]];
        const vChassisList = SIDES.map(side => {
            let _side = side;
            // Construct Slot and Card
            const children = treeData.children
                .filter(component => side === chassis_config[component.state.location].side)
                .map(child => {
                    if (_side.startsWith("mux")) {
                        _side = child.state["vendor-type-preconf"];
                    }
                    const {
                        location,
                        "vendor-type-preconf": preconf,
                        "actual-vendor-type": actual,
                        name,
                        type,
                        empty
                    } = child.state;
                    if (!location) {
                        // eslint-disable-next-line no-console
                        console.error(`location is empty, not able find slot/card: ${child}`);
                        return;
                    }
                    const {side, ...style} = chassis_config[location];

                    let className = `${styles.entity_common} ${styles[`CARD_${preconf}`]}`;
                    let title = `${labelList.name}: ${name}`;
                    let fan = {show: false};
                    let led = {show: false};
                    let ledPort;
                    let nodeType;
                    if (type === "SLOT") {
                        // Slot
                        className += ` ${styles.SLOT_common}`;
                        nodeType = "slot";
                    } else {
                        // Card
                        nodeType = "card";
                        if (empty === "true") {
                            // Preconf
                            if (preconf !== "OMD48ECM") {
                                className += ` ${styles.CARD_FAKE}`;
                            }
                            title += `${preconf ? `\n${labelList.preconf}: ${preconf}` : ""}`;
                        } else if (preconf !== actual) {
                            // Mismatch
                            className += ` ${styles.alarm_CRITICAL}`;
                            title += `${preconf ? `\n${labelList.preconf}: ${preconf}` : ""}${
                                actual ? `\n${labelList.actual}: ${actual}` : ""
                            }`;
                        } else {
                            // Match
                            title += `${preconf ? `\n${labelList.type}: ${preconf}` : ""}`;
                            if (chassisConfigDef?.[actual]?.led) {
                                // Card LED
                                let className = styles.led_COMMON;
                                if (["EMU-P", "D7000-EMU"].includes(actual) && child?.mcu?.state?.active === "false") {
                                    className += ` ${styles.led_ACTIVE}`;
                                }

                                const alarmSeverity = neAlarms.reduce((p, c) => {
                                    const {resource, severity} = c;
                                    if (
                                        (name === resource ||
                                            (["EMU-P", "D7000-EMU"].includes(actual) &&
                                                child?.mcu?.state?.active !== "false" &&
                                                resource === "CHASSIS-1")) &&
                                        severity < p
                                    )
                                        return severity;
                                    return p;
                                }, "Z");
                                if (alarmSeverity !== "Z") className += ` ${styles[`led_${alarmSeverity}`]}`;
                                led = {show: true, className, style: chassisConfigDef[actual].led};

                                // Port LED
                                ledPort = child.children
                                    ?.map(port => {
                                        if (port.state.type !== "PORT") return;
                                        // eslint-disable-next-line no-unused-vars
                                        const [_, chassisNo, slotNo, ...portNo] = port.name.split("-");
                                        if (!chassisConfigDef[preconf]?.[`LED_${portNo.join("-")}`]) return;

                                        const portSeverity = neAlarms.reduce((p, c) => {
                                            const {resource, severity} = c;

                                            // Port alarm
                                            if (port.name === resource && severity < p) p = severity;

                                            // All children alarm
                                            // eslint-disable-next-line no-unused-vars
                                            const [_, cNo, sNo, ...pNo] = resource.split("-");

                                            if (
                                                pNo &&
                                                cNo === chassisNo &&
                                                sNo === slotNo &&
                                                (pNo.join("-").startsWith(`${portNo.join("-")}-`) ||
                                                    pNo.join("-") === portNo.join("-")) &&
                                                severity < p
                                            )
                                                p = severity;
                                            return p;
                                        }, "Z");

                                        let className = styles.led_COMMON;
                                        if (portSeverity !== "Z") className += ` ${styles[`led_${portSeverity}`]}`;
                                        return {
                                            show: true,
                                            className,
                                            style: {...chassisConfigDef[preconf][`LED_${portNo.join("-")}`]},
                                            db_key: `LED_${portNo}`
                                        };
                                    })
                                    .filter(i => !!i);
                            }
                            if (type === "FAN") {
                                nodeType = "fan";
                                const speed = child.fan?.state?.["fan-speed"];
                                fan = {
                                    show: true,
                                    className: `${styles.icon_fan_common} ${styles[`fanSpeed_${speed}`]}`
                                };
                            }
                        }
                    }
                    // Construct Port list (Only visible on card)
                    const ports = child.children
                        ?.map(port => {
                            const title = `${labelList.name}: ${port.name}`;
                            if (port.state.type !== "PORT") return;
                            const port_no = port.name.match(/(?<=(\w+-){3}).*/)[0];
                            if (!chassisConfigDef[preconf]?.[port_no]) return;
                            const style = {...chassisConfigDef[preconf][port_no]};
                            if (style.transform) {
                                style.transform = `rotate(${style.transform}deg)`;
                            }
                            const className = `${styles.entity_common} ${
                                styles[`Port-${type === "CONTROLLER_CARD" ? preconf : type}-${port_no}`]
                            }`;

                            const trans = port.children
                                ?.map(({db_key, state: {name, "actual-vendor-type": actual}}) => {
                                    if (!actual) return;
                                    const title = `${labelList.name}: ${name}`;
                                    const className = `${styles.entity_common} ${styles.TRANSCEIVER_common} ${
                                        styles[`TRANSCEIVER_${actual}`]
                                    }`;
                                    return {name, nodeType: "TRANSCEIVER", db_key, title, className};
                                })
                                .filter(i => !!i);
                            return {
                                title,
                                style,
                                className,
                                name: port.name,
                                db_key: port.db_key,
                                trans,
                                nodeType: "port",
                                type: port.state.type
                            };
                        })
                        .filter(i => !!i);
                    return {
                        ports,
                        name,
                        db_key: child.db_key,
                        style,
                        className,
                        title,
                        led,
                        ledPort,
                        fan,
                        nodeType,
                        type
                    };
                });

            return {
                name: treeData.name,
                db_key: treeData.db_key,
                className: `${styles.entity_common} ${styles.chassis_common} ${styles[`chassis_${_side}`]}`,
                children,
                nodeType: "chassis",
                type: "chassis"
            };
        }).filter(i => i.children.length > 0);
        setData(vChassisList);
    };

    let isDBClick;

    const handleDoubleClick = (e, name) => {
        isDBClick = true;
        e.stopPropagation();

        if (offLine) {
            return message.error(`${labelList.get_data_fail} Client not connected`).then();
        }
        openEditComponent(
            "component",
            name,
            "5",
            selectedItem?.value?.ne_id,
            null,
            () => {
                requestSyncTimer({
                    ne_id: selectedItem?.value?.ne_id,
                    sync: {type: ["components", "terminal-device"], interval: 20000, timeout: 120000, startTime: 5000}
                });
            },
            readyOnlyRight.disabled,
            null,
            {
                component: null
            },
            2
        );
    };

    // 机框图模块MouseMove
    const handleModuleMouseMove = e => {
        // 处理hover旋转的fan svg时，选中样式附加到外层的fan div元素上
        if (e.target.nodeName === "svg") {
            let isFanDiv = true;
            while (isFanDiv) {
                const allDivs = e.target.parentNode.querySelectorAll("div");
                if (allDivs.length > 1) {
                    isFanDiv = false;
                    continue;
                }
                e.target = e.target.parentNode;
            }
        }

        e.stopPropagation();
        e.target.style.boxShadow = "inset 0px 0px 80px 4px rgb(0 255 0 / 30%)";
    };

    // const reboot_success = () => {
    //     message.success(labelList.switch_lost, 2).then(() => {
    //         navigate("/login");
    //     });
    // };

    const handleMenu = e => {
        const items = [];
        const name = e.target.getAttribute("data-value");
        const type = (e.target.getAttribute("data-rawtype") || "").toUpperCase();
        const componentName = name;
        const stateData = allComponents[name];
        if (!componentName) return;

        if (
            stateData &&
            SUPPORTED_REBOOT_TYPE.includes(type) &&
            stateData.empty !== "true" &&
            stateData.location !== "1-40"
        ) {
            items.push(
                getMenu(
                    "reboot",
                    labelList.reboot,
                    <UndoOutlined />,
                    () => {
                        setOpenDropdown(false);
                        openModalRpc(
                            selectedItem.value.ne_id,
                            "reboot",
                            "5",
                            labelList.reboot,
                            {"component-name": componentName},
                            ["component-name"],
                            null,
                            v => {
                                if (v["reboot-type"] === "cold") {
                                    return labelList.rebootConfirm;
                                }
                            },
                            null,
                            readyOnlyRight.disabled
                        );
                    },
                    readyOnlyRight?.disabled
                )
            );
        }
        if (!NOT_SUPPORT_DELETE.includes(type) && !componentName.startsWith("TRANSCEIVER")) {
            items.push(
                getMenu(
                    "delete",
                    labelList.delete,
                    <DeleteOutlined />,
                    () => {
                        setOpenDropdown(false);
                        const modal = smallModal({
                            content: `${labelList.delete_confirm2.format(componentName)}`,
                            okText: labelList.ok,
                            cancelText: labelList.cancel,
                            onOk: () => {
                                apiEditRpc({
                                    ne_id: selectedItem.value.ne_id,
                                    params: {
                                        components: {
                                            component: {
                                                name: componentName,
                                                "@nc:operation": "delete"
                                            }
                                        }
                                    },
                                    success: () => {
                                        modal.destroy();
                                        if (type === "MUX") {
                                            setShowType(0);
                                        }
                                    },
                                    sync: {type: "components", interval: 20000, timeout: 60000, startTime: 5000}
                                }).then();
                            }
                        });
                    },
                    readyOnlyRight.disabled
                )
            );
        }
        setMenuItems(items);
    };

    // 机框图模块MouseOut
    const handleModuleMouseOut = e => {
        e.stopPropagation();

        if (e.target.title === selectedPortRef.current?.title) {
            return;
        }
        e.target.style.boxShadow = "";
    };

    // 机框图模块Click
    const handleModuleClick = e => {
        isDBClick = false;
        const {dataset} = e.currentTarget;
        e.stopPropagation();
        if (e.button === 0) {
            // console.log("Left button clicked");
            setOpenDropdown(false);
        }

        setTimeout(() => {
            if (!isDBClick) {
                if (e.target.title === selectedPortRef.current?.title) {
                    selectedPortRef.current.style.boxShadow = "";
                    selectedPortRef.current = {style: {}};
                    setSelectPort(null);
                    return;
                }
                e.target.style.boxShadow = "inset 0px 0px 80px 4px rgb(0 255 0 / 30%)";
                selectedPortRef.current.style.boxShadow = "";
                selectedPortRef.current = e.target;
                setSelectPort(dataset);
            }
        }, 300);
    };

    const callSyncNE = async _id => {
        const newState = {...syncState};
        newState[_id] = true;
        setSyncState(newState);
        await syncNE({
            ne_id: _id,
            success: () => {
                const newState = {...syncState.current};
                delete newState[_id];
                setSyncState(newState);
                if (_id === selectedItemRef.current?.value?.ne_id) {
                    updateData();
                }
            },
            fail: () => {
                const newState = {...syncState.current};
                delete newState[_id];
                setSyncState(newState);
            }
        }).then();
    };

    const sync = async () => {
        const ne_id = selectedItem?.value?.ne_id;
        // const {state, upgrade_type} = (await objectGet("config:ne", {ne_id})).documents[0].value;
        // if (state > 1) {
        //     const modal = Modal.confirm({
        //         title: gLabelList.warning,
        //         content: gLabelList.sync_confirm.format(
        //             upgrade_type === "upgrade" ? gLabelList.upgrade : gLabelList.activate_data_state
        //         ),
        //         // eslint-disable-next-line no-unused-vars
        //         onOk: async _ => {
        //             modal.destroy();
        //             await callSyncNE(ne_id);
        //         },
        //         closable: true
        //     });
        // } else {
        await callSyncNE(ne_id);
        // }
    };

    const getOffLineState = async () => {
        const ne_id = selectedItem?.value?.ne_id;
        const {runState} = (await objectGet("config:ne", {ne_id})).documents[0].value;
        if (runState === 0) {
            setOffLine(true);
        } else {
            setOffLine(false);
        }
    };

    const {name, className, db_key, children, nodeType, type} = data?.[showType] ?? {};
    const bt = [labelList.front, labelList.rear];

    useEffect(() => {
        if (
            dataChanged?.data?.ne_id === selectedItem?.value?.ne_id &&
            ["all", "components"].includes(dataChanged?.data?.type)
        ) {
            updateData();
        }
    }, [dataChanged]);

    useEffect(() => {
        updateData();
    }, [alarms]);

    useEffect(() => {
        getOffLineState();
    }, [upgrade]);

    // 切换网元更新抽屉内树的数据
    useEffect(() => {
        selectedItemRef.current = selectedItem;
    }, [selectedItem]);

    useEffect(() => {
        if (chassisWrapRef.current && chassisRef.current) {
            chassisRef.current.style = `zoom: ${(chassisWrapRef?.current.clientWidth || 0) / 1308.5}`;
        }
    }, [data]);

    useEffect(() => {
        const observer = new ResizeObserver(
            debounce(() => {
                if (chassisRef.current)
                    chassisRef.current.style = `zoom: ${(chassisWrapRef?.current.clientWidth || 0) / 1308.5}`;
            }, 5)
        );
        observer.observe(containerRef.current);

        return () => {
            observer.disconnect();
        };
    }, []);

    return (
        <div
            style={{
                border: "1px solid #ddd",
                padding: 10,
                marginRight: 24,
                marginLeft: 24,
                borderRadius: 5
                // height: 446,
                // minWidth: 1350
            }}
        >
            <div ref={containerRef} style={{padding: "24px 23px", overflow: "hidden"}}>
                {/* 视图切换工具栏 */}
                <div className={styles_common.head_div} style={{width: "100%"}}>
                    <div>
                        {data?.length ? (
                            <Radio.Group
                                value={showType}
                                className={styles_fs.viewTypeTools}
                                onChange={e => {
                                    setShowType(e.target.value);
                                }}
                            >
                                {data.map((item, i) => {
                                    const _key = bt[i] || item?.children?.[0]?.name;
                                    return <Radio.Button value={i}>{_key}</Radio.Button>;
                                })}
                            </Radio.Group>
                        ) : null}
                    </div>
                    <Button
                        disabled={syncState[selectedItem?.value?.ne_id]}
                        loading={syncState[selectedItem?.value?.ne_id]}
                        onClick={sync}
                    >
                        {labelList.sync}
                    </Button>
                </div>
                {/* 机框图 */}
                <div
                    className={styles_common.wrap}
                    ref={chassisWrapRef}
                    onMouseLeave={() => {
                        setOpenDropdown(false);
                    }}
                >
                    {data && (
                        <Dropdown
                            menu={{items: menuItems}}
                            trigger={["contextMenu"]}
                            open={openDropdown}
                            onContextMenu={e => {
                                e.preventDefault();
                                if (e.button === 2) setOpenDropdown(true);
                            }}
                        >
                            <div
                                ref={chassisRef}
                                title={name}
                                className={className}
                                data-type={nodeType}
                                data-rawtype={type}
                                data-value={name}
                                onDoubleClick={e => handleDoubleClick(e, name, db_key)}
                                onMouseMove={handleModuleMouseMove}
                                onMouseOut={handleModuleMouseOut}
                                onClick={handleModuleClick}
                                onContextMenu={e => handleMenu(e, db_key)}
                                onBlur={() => {}}
                            >
                                {/* Slot and Card */}
                                {children?.map(
                                    ({
                                        ports,
                                        name,
                                        db_key,
                                        style,
                                        className,
                                        title,
                                        nodeType,
                                        led,
                                        ledPort,
                                        fan,
                                        type
                                    }) => (
                                        <div
                                            key={name}
                                            title={title}
                                            data-type={nodeType}
                                            data-rawtype={type}
                                            data-value={name}
                                            className={className}
                                            style={style}
                                            onClick={handleModuleClick}
                                            onDoubleClick={e => handleDoubleClick(e, name, db_key)}
                                            onContextMenu={e => handleMenu(e, db_key)}
                                        >
                                            {/* LED */}
                                            {led.show && <div className={led.className} style={led.style} />}
                                            {/* FAN */}
                                            {fan.show &&
                                                (showFS ? (
                                                    // eslint-disable-next-line react/jsx-pascal-case
                                                    <Fan_1 spin className={fan.className} />
                                                ) : (
                                                    <ChromeOutlined spin className={fan.className} />
                                                ))}
                                            {/* Port */}
                                            {ports?.map(
                                                ({title, style, className, name, db_key, nodeType, type, trans}) => (
                                                    <div
                                                        key={name}
                                                        data-type={nodeType}
                                                        data-rawtype={type}
                                                        data-value={name}
                                                        title={title}
                                                        className={className}
                                                        style={style}
                                                        onClick={handleModuleClick}
                                                        onDoubleClick={e => handleDoubleClick(e, name, db_key)}
                                                        onContextMenu={e => handleMenu(e, db_key)}
                                                    >
                                                        {/* Transceiver */}
                                                        {trans?.map(
                                                            ({name, db_key, title, className, nodeType, type}) => (
                                                                <div
                                                                    key={name}
                                                                    data-type={nodeType}
                                                                    data-rawtype={type}
                                                                    data-value={name}
                                                                    title={title}
                                                                    className={className}
                                                                    onClick={handleModuleClick}
                                                                    onDoubleClick={e =>
                                                                        handleDoubleClick(e, name, db_key)
                                                                    }
                                                                    onContextMenu={e => handleMenu(e, db_key)}
                                                                />
                                                            )
                                                        )}
                                                    </div>
                                                )
                                            )}
                                            {ledPort?.map(({style, className, db_key}) => (
                                                <div key={db_key} className={className} style={style} />
                                            ))}
                                        </div>
                                    )
                                )}
                            </div>
                        </Dropdown>
                    )}
                </div>
                <div
                    style={{
                        marginTop: 24,
                        width: "100%",
                        textAlign: "center",
                        fontSize: 14
                    }}
                >
                    {neNameMap[selectedItem?.value?.ne_id]} ({selectedItem?.value?.ne_id}){" "}
                    {offLine && <Icon component={offLineSvg} style={{marginRight: 4}} />}
                </div>
            </div>
            {wrapMock && <div style={{position: "absolute", top: 0, width: "100%", height: "100%", zIndex: 100}} />}
        </div>
    );
};

export default Chassis5;
