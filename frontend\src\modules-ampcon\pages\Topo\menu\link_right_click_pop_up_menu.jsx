import React, {forwardRef, useImperativeHandle, useState} from "react";
import {Menu} from "@antv/x6-react-components";
import {usePopper} from "react-popper";

const LinkRightClickPopUpMenu = forwardRef(({deleteLinkCallback, editLinkCallback}, ref) => {
    const MenuItem = Menu.Item;

    const [isLinkRightClickPopUpMenuVisible, setIsLinkRightClickPopUpMenuVisible] = useState(false);
    const [selectedLink, setSelectedLink] = useState(null);

    const [referenceElement, setReferenceElement] = useState(null);
    const [menuElement, setMenuElement] = useState(null);

    const {styles, attributes} = usePopper(referenceElement, menuElement, {
        placement: "right-start"
    });

    useImperativeHandle(ref, () => ({
        showLinkRightClickPopUpMenu: (edge, e) => {
            setReferenceElement({
                getBoundingClientRect: () => ({
                    width: 0,
                    height: 0,
                    top: e.clientY,
                    left: e.clientX,
                    right: e.clientX,
                    bottom: e.clientY
                }),
                contextElement: document.body
            });
            setIsLinkRightClickPopUpMenuVisible(true);
            setSelectedLink(edge);
        },
        hideLinkRightClickPopUpMenu: () => {
            setIsLinkRightClickPopUpMenuVisible(false);
        },
        isShowLinkRightClickPopUpMenu: () => {
            return isLinkRightClickPopUpMenuVisible;
        }
    }));

    return isLinkRightClickPopUpMenuVisible ? (
        <div
            ref={setMenuElement}
            style={{
                ...styles.popper,
                zIndex: 1000
            }}
            {...attributes.popper}
        >
            <Menu>
                <MenuItem
                    name="delete"
                    text="Delete"
                    hotkey="Delete"
                    onClick={() => {
                        deleteLinkCallback(selectedLink);
                        setIsLinkRightClickPopUpMenuVisible(false);
                    }}
                />
                <MenuItem
                    name="edit"
                    text="Edit"
                    onClick={() => {
                        editLinkCallback(selectedLink);
                        setIsLinkRightClickPopUpMenuVisible(false);
                    }}
                />
            </Menu>
        </div>
    ) : null;
});

export default LinkRightClickPopUpMenu;
