import Calendar from "@/modules-ampcon/components/calendar_view";
import {Card, message} from "antd";
import {JobResultView} from "@/modules-ampcon/pages/Maintain/NetworkConfiguration/Automation/job_result";
import {useEffect, useState} from "react";
import {fetchJobDetailInfo, fetchTaskOutputInfo} from "@/modules-ampcon/apis/automation_api";

const Schedule = () => {
    const [eventItems, setEventItems] = useState([]);
    useEffect(() => {
        fetchJobDetailInfo().then(res => {
            if (res.status === 200) {
                setEventItems(res.info);
            } else {
                message.error("Failed to fetch task output info");
            }
        });
    }, []);

    const [isModalOpen, setIsModalOpen] = useState(false);
    const [isModalOpenItems, setIsModalOpenItems] = useState(false);
    const [curJob, setCurJob] = useState("");
    const [textAreaValue, setTextAreaValue] = useState("");

    const itemClickHandler = job_name => {
        setIsModalOpen(true);
        setCurJob(job_name);
        fetchTaskOutputInfo({job_name}).then(res => {
            if (res.status === 200) {
                setTextAreaValue(res.info);
            } else {
                message.error("Failed to fetch task output info");
            }
        });
    };

    return (
        <div style={{minWidth: "780px"}}>
            <JobResultView
                useJob
                extraParams={curJob}
                textAreaValue={textAreaValue}
                isModalOpen={isModalOpen}
                setIsModalOpen={setIsModalOpen}
                isModalOpenItems={isModalOpenItems}
                setIsModalOpenItems={setIsModalOpenItems}
            />
            <div style={{display: "flex", flexDirection: "column"}}>
                <Card style={{paddingTop: "20px", paddingBottom: "20px", flex: 1}}>
                    <Calendar eventItems={eventItems} itemClickHandler={itemClickHandler} />
                </Card>
            </div>
        </div>
    );
};

export default Schedule;
