import {Flex, Progress} from "antd";

const ProgressBarComponent = ({title = "", perCent = 100, perCentColor = "#14C9BB"}) => {
    return (
        <Flex wrap="wrap">
            <Flex justify="space-between" style={{width: "100%"}}>
                <p style={{fontSize: "14px"}}>{title}</p>
                <p style={{color: "#14C9BB", fontSize: "18px"}}>{perCent}%</p>
            </Flex>
            <Progress percent={perCent} showInfo={false} strokeColor={perCentColor} style={{width: "100%"}} />
        </Flex>
    );
};

export default ProgressBarComponent;
