.lldpContainer {
    position: relative;
    padding: 0px;
    height: 100%;
    overflow: auto;
    width: 100%;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;

    .loading {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(255, 255, 255, 0.6);
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 1000;
    }

    .configPanel {
        background: #ffffff;
        padding: 24px 0 0 0;
        margin-bottom: 24px;
        width: 100%;
        box-sizing: border-box;
        border-radius: 0;
        border: none;
        box-shadow: none;
    }

    .enableItem {
        display: flex;
        align-items: center;
        margin-bottom: 16px;

        span {
            display: inline-block;
            width: 70px;
            color: rgba(0, 0, 0, 0.85);
        }

        .ant-switch {
            border: 1px solid transparent;
        }
    }

    .tableContainer {
        flex: 1;
        overflow: auto;
        width: 100%;
    }

    .configRow {
        display: flex;
        width: 100%;
        margin-bottom: 20px;
        flex-wrap: wrap;
        
        &:last-child {
            margin-bottom: 0;
        }

        @media (max-width: 768px) {
            flex-direction: column;
        }
    }

    .leftCol {
        width: 35%;
        padding-right: 20px;
    }

    .rightCol {
        width: 65%;
    }

    .configItem {
        display: flex;
        align-items: center;
        padding-right: 15px;

        &:last-child {
            margin-bottom: 0;
        }

        @media (max-width: 768px) {
            width: 100% !important;
            margin-right: 0;
            padding-right: 0 !important;
            margin-bottom: 16px;
        }

        span {
            display: inline-block;
            width: 130px;
            min-width: 130px;
            color: rgba(0, 0, 0, 0.65);
            font-weight: normal;
            margin-right: 24px;
            font-size: 14px;
        }

        :global {
            .ant-input {
                flex: 1;
                width: 100%;
                height: 32px;
                border-radius: 2px;
                background-color: #f5f5f5;
                padding: 4px 11px;
                border: 1px solid #d9d9d9;
                color: rgba(0, 0, 0, 0.65);

                &:disabled {
                    background-color: #f5f5f5;
                    color: rgba(0, 0, 0, 0.65);
                    cursor: not-allowed;
                }
            }

            .ant-switch {
                background-color: rgba(0, 0, 0, 0.25);
                width: 44px !important;
                height: 22px !important;
            }

            .ant-switch-checked {
                background-color: #00bfb3 !important;
            }
        }
    }

    .enabledSwitch {
        width: 44px !important;
        height: 22px !important;
        background-color: #00bfb3 !important;

        :global {
            .ant-switch-handle {
                height: 18px !important;
                width: 18px !important;
                top: 2px !important;
                left: calc(100% - 20px) !important;
            }
        }
    }

    .disabledSwitch {
        width: 44px !important;
        height: 22px !important;

        :global {
            .ant-switch-handle {
                height: 18px !important;
                width: 18px !important;
                top: 2px !important;
                left: 2px !important;
            }
        }
    }

    .configHeader {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 16px;

        h3 {
            margin: 0;
            font-size: 16px;
            font-weight: 500;
        }
    }

    .header {
        display: flex;
        margin-bottom: 24px;
        background: #fff;
        padding: 0px;
        border-radius: 4px;

        .selectItem {
            display: flex;
            align-items: center;

            &:first-child {
                margin-right: 40px;
            }

            span {
                display: inline-block;
                margin-right: 32px;
                color: rgba(0, 0, 0, 0.85);
            }

            :global {
                .ant-select {
                    width: 200px;
                    position: relative;

                    .ant-select-selector {
                        border-radius: 2px;
                        height: 32px;
                        display: flex;
                        align-items: center;
                        padding: 0 11px !important;
                    }

                    .ant-select-selection-placeholder {
                        color: rgba(0, 0, 0, 0.25);
                    }

                    .ant-select-arrow {
                        position: absolute;
                        top: 65%;
                        transform: translateY(-50%);
                        right: -55px !important;
                        color: rgba(0, 0, 0, 0.25);
                        pointer-events: none;
                    }

                    .ant-select-clear {
                        right: -50px;
                    }
                }
            }
        }
    }

    :global {
        .ant-switch {
            background-color: rgba(0, 0, 0, 0.25);
            width: 44px !important;
            height: 22px !important;
        }

        .ant-switch-checked {
            background-color: #00bfb3 !important;
        }

        .ant-switch-handle::before {
            border-radius: 50% !important;
            height: 18px !important;
            width: 18px !important;
        }

        .ant-switch-handle {
            height: 18px !important;
            width: 18px !important;
            top: 2px !important;
        }

        .ant-switch-checked .ant-switch-handle {
            left: calc(100% - 20px) !important;
        }

        .ant-table .ant-switch {
            width: 44px !important;
        }

        .ant-table-wrapper {
            width: 100%;

            .ant-table-thead > tr > th {
                background: #fafafa;
                font-weight: 500;
                padding: 12px 16px;
            }

            .ant-table-tbody > tr > td {
                padding: 12px 16px;
            }

            .ant-pagination {
                margin: 16px 0;

                .ant-pagination-total-text {
                    margin-right: 12px;
                }

                .ant-pagination-options {
                    .ant-select {
                        width: 100px;
                    }
                }
            }
        }
    }
}
.pageJumpContainer {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    margin-top: 8px;
    
    span {
        color: rgba(0, 0, 0, 0.65);
        font-size: 14px;
    }
}

.paginationExtra {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    margin-top: -36px;
    padding-right: 16px;
}

.jumpWrapper {
    display: flex;
    align-items: center;
}

.jumpInPagination {
    display: inline-flex;
    align-items: center;
    margin-left: 16px;
}

.customPagination {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 16px;
    border-top: 1px solid #f0f0f0;
    margin-top: 8px;
    
    .paginationLeft {
        display: flex;
        align-items: center;
        
        span {
            margin-right: 16px;
            color: rgba(0, 0, 0, 0.65);
        }
        
        .paginationButton {
            min-width: 32px;
            height: 32px;
            margin: 0 4px;
            padding: 0;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .currentPageButton {
            min-width: 32px;
            height: 32px;
            margin: 0 4px;
            padding: 0;
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: #1890ff;
            border-color: #1890ff;
        }
    }
    
    .paginationCenter {
        display: flex;
        align-items: center;
        
        .jumpButton {
            margin-left: 8px;
        }
    }
    
    .paginationRight {
        display: flex;
        align-items: center;
        
        span {
            margin-right: 8px;
            color: rgba(0, 0, 0, 0.65);
        }
        
        .paginationButton {
            min-width: 32px;
            height: 32px;
            padding: 0;
            display: flex;
            align-items: center;
            justify-content: center;
        }
    }
}

.standardPagination {
    padding: 8px 16px;
    border-top: 1px solid #f0f0f0;
    
    .paginationContent {
        display: flex;
        justify-content: space-between;
        align-items: center;
    }
    
    .paginationLeft {
        display: flex;
        align-items: center;
        min-width: 120px;
        
        span {
            color: rgba(0, 0, 0, 0.65);
            font-size: 14px;
        }
    }
    
    .paginationCenter {
        display: flex;
        align-items: center;
        justify-content: center;
        flex: 1;
        
        .pageButton {
            min-width: 32px;
            height: 32px;
            padding: 0;
            margin: 0 4px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        span {
            margin: 0 4px;
            color: rgba(0, 0, 0, 0.65);
        }
    }
    
    .paginationRight {
        display: flex;
        align-items: center;
        
        .ant-select {
            width: 90px;
            margin-right: 16px;
        }
    }
    
    .goToText {
        margin: 0 8px;
        color: rgba(0, 0, 0, 0.65);
    }
    
    .goToInput {
        width: 50px;
        text-align: center;
    }
}

