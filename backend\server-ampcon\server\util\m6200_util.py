import json
import logging
import socket
import time
import traceback
from collections import defaultdict

from pyasn1.type import univ
from pysnmp.error import PySnmpError
from pysnmp.smi import exval

# sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), "../..")))

from server.db.models import inventory
from server.db.models.otn import OtnTempData, OtnDeviceBasic, M6200DeviceCards, M6200DeviceBasic
from server.util.redis_distributed_lock import DistributedLock
from server.util.snmp_util import SNMPClient, convert_asn1_value
from server import cfg
from util.snmp_util import SNMP_SET_INDICES

invent_db = inventory.inven_db
LOG = logging.getLogger(__name__)

OID_BASE_PATHS = {
    "M6200": "*******.4.1.52642.1.10"
}

SERIES2MODEL = {
    4: "M6200"
}

DEFAULT_SHELF_ID = 1
DEFAULT_SUB_SLOT_NO = 0
DEFAULT_SUB_PORT_NO = 0

FIXED_INDICES = {
    "shelfId": DEFAULT_SHELF_ID,
    "subSlotNo": DEFAULT_SUB_SLOT_NO,
    "subPortNo": DEFAULT_SUB_PORT_NO
}

# snmpTargetObjects = "*******.********"
# # snmpTargetAddr
# snmpTargetAddrTable = snmpTargetObjects + ".2"
# snmpTargetAddrEntry = snmpTargetAddrTable + ".1"
# snmpTargetAddrTDomain = snmpTargetAddrEntry + ".2."
# snmpTargetAddrTAddress = snmpTargetAddrEntry + ".3."
# snmpTargetAddrTagList = snmpTargetAddrEntry + ".6."
# snmpTargetAddrParams = snmpTargetAddrEntry + ".7."
# snmpTargetAddrstorageType = snmpTargetAddrEntry + ".8."
# snmpTargetAddrRowStatus = snmpTargetAddrEntry + ".9."
# # snmpTargetParams
# snmpTargetParamsTable = snmpTargetObjects + ".3"
# snmpTargetParamsEntry = snmpTargetParamsTable + ".1"
# snmpTargetParamsName = snmpTargetParamsEntry + ".1."
# snmpTargetParamsMPModel = snmpTargetParamsEntry + ".2."
# snmpTargetParamsSecurityModel = snmpTargetParamsEntry + ".3."
# snmpTargetParamsSecurityName = snmpTargetParamsEntry + ".4."
# snmpTargetParamsSecurityLevel = snmpTargetParamsEntry + ".5."
# snmpTargetParamsStorageType = snmpTargetParamsEntry + ".6."
# snmpTargetParamsRowStatus = snmpTargetParamsEntry + ".7."

def generate_hex_from_ip_port(ip_address, port):
    try:
        ip_bytes = socket.inet_aton(ip_address)
        port_bytes = port.to_bytes(2, 'big')
        hex_string = (ip_bytes + port_bytes).hex()
        return f"hex_{hex_string}"
    except socket.error as e:
        print(f"Error converting IP address: {e}")
        return None
    except OverflowError as e:
        print(f"Error converting port: {e}")
        return None

def beat_sync_m6200_device_info_all():
    """Synchronize all m6200 device information"""
    LOG.info('Start sync all m6200 device info.')

    db_session = invent_db.get_session()
    device_info_list = db_session.query(OtnDeviceBasic).filter(OtnDeviceBasic.series == 4).all()

    for device_info in device_info_list:
        result, nmu_data = get_device_info_by_ip(device_info.ip, device_info.id, "M6200")
        temp_data = OtnTempData(id=device_info.id, ip=device_info.ip, nmu=json.dumps(nmu_data),
                                    data=json.dumps(result))
        db_session.merge(temp_data)

    LOG.info('End sync all M6200 device info.')


def sync_otn_device_info_single(id, ip):
    """Synchronize a single OTN device's information"""
    result, otn_device_basic = get_device_info(id, ip)
    otn_data = result[0]
    nmu = result[1]
    if otn_data is None:
        return None

    db_session = invent_db.get_session()
    with db_session.begin():
        otn_temp_data = OtnTempData(id=otn_device_basic.id, ip=otn_device_basic.ip, nmu=json.dumps(nmu),
                                    data=json.dumps(otn_data))
        db_session.merge(otn_temp_data)

    return get_device_detail(otn_device_basic.id, SERIES2MODEL[otn_device_basic.series])

def _set_snmp_target_address(snmp_client, origin_str, mib_name, value, ip):
    success = snmp_client.process('set', f"SNMP-TARGET-MIB::{mib_name}", (origin_str,), value)
    if not success:
        LOG.error(f"Failed to set trap destination for device {ip}. OID: {mib_name}")
        return False
    return True

def subscribe_trap_message(ip, device_type="M6200"):
    controller_ip = cfg.CONF.global_ip
    snmp_client = SNMPClient(ip, community_read="private", community_write="private", device_type=device_type)
    origin_str = f"ampcon{controller_ip.replace('.', '')}"

    snmp_trap_settings = [
        ("snmpTargetAddrRowStatus", 4),
        ("snmpTargetAddrTDomain", "*******.6.1.1"),
        ("snmpTargetAddrTAddress", generate_hex_from_ip_port(controller_ip, 162)),
        ("snmpTargetAddrTagList", "internal1"),
        ("snmpTargetAddrParams", "internal1"),
        ("snmpTargetAddrRowStatus", 1)
    ]
    for oid_suffix, value in snmp_trap_settings:
        if not _set_snmp_target_address(snmp_client, origin_str, oid_suffix, value, ip):
            return False, 1, "Failed to set trap destination"
    LOG.info(f"Successfully add trap destination {controller_ip} for device {ip}")
    return True, 0, "success"

def unsubscribe_trap_message(ip, device_type="M6200"):
    controller_ip = cfg.CONF.global_ip
    snmp_client = SNMPClient(ip, community_read="private", community_write="private", device_type=device_type)
    origin_str = f"ampcon{controller_ip.replace('.', '')}"

    snmp_trap_settings = [
        ("snmpTargetAddrRowStatus", 6)  # DELETE ROW
    ]
    for oid_suffix, value in snmp_trap_settings:
        if not _set_snmp_target_address(snmp_client, origin_str, oid_suffix, value, ip):
            return False
    LOG.info(f"Successfully delete trap destination {controller_ip} for device {ip}")
    return True


def get_device_detail(device_id, device_type="M6200"):
    db_session = invent_db.get_session()
    otn_temp_data = db_session.query(OtnTempData).filter(OtnTempData.id == device_id).first()

    if device_type == "M6200":
        cards_data = db_session.query(M6200DeviceCards).filter(M6200DeviceCards.device_id == device_id).all()
    else:
        return ""

    otn_temp_data_result = otn_temp_data.data

    if otn_temp_data_result is None or len(otn_temp_data_result) == 0 or otn_temp_data_result == '""':
        return ""

    result_data = json.loads(otn_temp_data_result)
    if result_data.get("boardInfos") is not None:
        board_infos = result_data["boardInfos"]
        for item in cards_data:
            slot_index = item.slot_index
            card = get_card_by_slot_index(board_infos, slot_index)
            if card is None:
                LOG.info(f"Slot index:{slot_index} have no card.")
                continue
            card["cardId"] = item.card_id
            card["model"] = item.model
            card["sn"] = item.serial_number
            card["production_date"] = item.production_date
            card["hardware_version"] = item.hardware_version
            card["software_version"] = item.software_version
            card["temperature"] = item.temperature
            card["ports_data"] = json.loads(item.ports_data)
            if card.get("basicInfo") is not None:
                del card["basicInfo"]
            if slot_index == 0:
                card["description"] = otn_temp_data.description

    return result_data

def get_card_by_slot_index(board_infos, slot_index):
    for item in board_infos:
        if item["slotIndex"] == slot_index:
            return item
    return None


def get_device_info(id, ip=None):
    db_session = invent_db.get_session()

    if ip is not None:
        LOG.info(f"Getting device info by IP: {ip}")
        otn_device_basic = db_session.query(OtnDeviceBasic).filter(OtnDeviceBasic.ip == ip).first()
        if otn_device_basic is None:
            return [None, None], None
        return get_device_info_by_ip(ip, otn_device_basic.id, SERIES2MODEL[otn_device_basic.series]), otn_device_basic

    LOG.info(f"Getting device info by ID: {id}")
    otn_device_basic = db_session.query(OtnDeviceBasic).filter(OtnDeviceBasic.id == id).first()
    if otn_device_basic is None:
        return [None, None], None

    LOG.info(str(otn_device_basic))
    ip = otn_device_basic.ip
    return get_device_info_by_ip(ip, id, SERIES2MODEL[otn_device_basic.series]), otn_device_basic


def get_device_info_by_ip(ip, id=None, device_type="M6200"):
    start_time = time.time()
    distributed_lock = DistributedLock(ip)

    if distributed_lock.acquire():
        snmp_client = None
        try:
            LOG.info(f"Got lock for {ip} in {time.time() - start_time} seconds")
            snmp_client = SNMPClient(
                host=ip,
                community_read="private",
                community_write="private",
                device_type=device_type
            )
            rec_data, nmu = get_all_board_info(snmp_client, device_type)
            LOG.info(f"Got data for {ip} in {time.time() - start_time} seconds")
            if rec_data["boardInfos"]:
                update_reachable_status(ip, 1)
            else:
                update_reachable_status(ip, 0)

            if device_type == "M6200":
                update_device_info(id, rec_data)

            return rec_data, nmu

        except Exception as e:
            LOG.error(f"Exception getting device info for {ip}: {e}")
            LOG.error(traceback.format_exc())
        finally:
            distributed_lock.release()
            LOG.info("End work, released lock")
    else:
        LOG.error(f"Failed to get lock for {ip}")

    update_reachable_status(ip, 0)
    if device_type == "M6200":
        update_device_info(id, None)

    return {"boardInfos": [], "slot_number": 0}, ""


def get_all_board_info(snmp_client, device_type):
    result = {"boardInfos": []}
    nmu = {}

    chassis_oids = [
        ("ST-COMMON-MIB::shelfType", (1,)),
        ("ST-COMMON-MIB::shelfSwVersion", (1,)),
        ("ST-COMMON-MIB::shelfHwVersion", (1,)),
        ("ST-COMMON-MIB::shelfSN", (1,)),
        ("ST-COMMON-MIB::shelfTemperature", (1,)),
        ("ST-COMMON-MIB::shelfMacAddressMgmt1", (1,)),
    ]
    chassis_data = snmp_client.process('get', chassis_oids)
    if chassis_data:
        result["sn"] = convert_asn1_value(chassis_data.get(("ST-COMMON-MIB::shelfSN", (1,)), ""))
        result["hardware_version"] = convert_asn1_value(chassis_data.get(("ST-COMMON-MIB::shelfHwVersion", (1,)), ""))
        result["software_version"] = convert_asn1_value(chassis_data.get(("ST-COMMON-MIB::shelfSwVersion", (1,)), ""))
        result["temperature"] = convert_asn1_value(chassis_data.get(("ST-COMMON-MIB::shelfTemperature", (1,)), ""))
        result["mac"] = convert_asn1_value(chassis_data.get(("ST-COMMON-MIB::shelfMacAddressMgmt1", (1,)), ""))
        result["model"] = device_type
        cards_data = defaultdict(dict)
        ports_data = defaultdict(dict)
        card_table_mib_name = 'ST-COMMON-MIB::slotTable'
        port_table_mib_name = 'ST-COMMON-MIB::portTable'
        card_walk_results = snmp_client.process('walk', card_table_mib_name, ())
        if card_walk_results is not None:
            slot_count = 0
            board_infos = []
            for oid_obj, value_obj in card_walk_results:
                try:
                    mib_module, mib_object, indices = oid_obj.get_mib_symbol()
                    if len(indices) == 3:
                        shelf, slot, subslot = map(int, indices)
                        if slot not in cards_data:
                            cards_data[slot]['slotIndex'] = slot
                            # cards_data[slot]['shelfId'] = shelf
                            # cards_data[slot]['subSlotNo'] = subslot
                        value = convert_asn1_value(value_obj)
                        cards_data[slot][mib_object] = value
                except Exception as e:
                    LOG.warning(f"Error processing card walk result {oid_obj}: {e}")
        else:
            LOG.error(f"Walk failed for {card_table_mib_name}")

        port_walk_results = snmp_client.process('walk', port_table_mib_name, ())
        if port_walk_results is not None:
            for oid_obj, value_obj in port_walk_results:
                try:
                    mib_module, mib_object, indices = oid_obj.get_mib_symbol()
                    if len(indices) == 5:
                        shelf, slot, subslot, port, subport = map(int, indices)
                        if slot not in ports_data:
                            ports_data[slot] = defaultdict(dict)
                        if port not in ports_data[slot]:
                            ports_data[slot][port] = {"no": port}
                        value = convert_asn1_value(value_obj)
                        ports_data[slot][port][mib_object] = value
                        if mib_object == "portMode":
                            if value == "f100G40G":
                                ports_data[slot][port]["name"] = f"PORT-1-{slot}-{port}"
                            elif value == "oa":
                                ports_data[slot][port]["name"] = f"PORT-1-{slot}-{port}"
                            elif value == "oap":
                                ports_data[slot][port]["name"] = f"PORT-1-{slot}-{port}"
                except Exception as e:
                    LOG.warning(f"Error processing port walk result {oid_obj}: {e}")
        else:
            LOG.error(f"Walk failed for {port_table_mib_name}")

        card_inventory_table_name = "cardInventoryTable"
        led_status_table_name = "cardLedTable"
        slot_iteration_range = list(range(1, 12))
        fixed_card_indices = {"shelfId": 1, "subSlotNo": 0}

        card_slot_data = snmp_client.get_slot_data(
            table_name=card_inventory_table_name,
            slot_list=slot_iteration_range,
            other_fixed_indices=fixed_card_indices
        )
        card_led_data = snmp_client.get_slot_data(
            table_name=led_status_table_name,
            slot_list=slot_iteration_range,
            other_fixed_indices=fixed_card_indices
        )

        for slot_index, slot_info in cards_data.items():
            board_type = slot_info.get("slotActualCardType", "Unknown")
            if slot_index in card_slot_data:
                # cards_data[slot_index] = {**slot_info, **card_slot_data[slot_index]}
                cards_data[slot_index].update(card_slot_data[slot_index])
            if slot_index in card_led_data:
                cards_data[slot_index].update(card_led_data[slot_index])
            if slot_index in ports_data:
                biz_data = get_business_info(snmp_client, slot_index, board_type)
                current_slot_ports_data = dict(ports_data[slot_index])
                for port_idx_str, port_biz_data in biz_data.items():
                    try:
                        port_idx = int(port_idx_str)  # Assuming port keys in biz_data are strings like "1", "2"
                        if port_idx in current_slot_ports_data:
                            current_slot_ports_data[port_idx].update(port_biz_data)
                        else:
                            pass
                    except ValueError:
                        # Handle cases where keys in biz_data are not integers (e.g., "aaaa")
                        # These can be added directly to the slot_info or handled as needed.
                        # For this example, let's merge them at the slot_info level if they don't collide
                        # with 'ports_data' or other existing keys.
                        if port_idx_str not in slot_info:
                            slot_info[port_idx_str] = port_biz_data
                slot_info['ports_data'] = current_slot_ports_data
            else:
                slot_info['ports_data'] = {}
            nmu[str(slot_index)] = board_type
        # print(json.dumps(cards_data, indent=4))
        result["boardInfos"] = list(cards_data.values())

    return result, nmu

def update_device_info(id, rec_data):
    db_session = invent_db.get_session()
    with db_session.begin():
        if rec_data is not None:
            # 更新m6200设备表、单板表
            deviceBasic = M6200DeviceBasic(device_id=id, serial_number=rec_data.get("sn"),
                                            slot_number=rec_data.get("slot_number"),
                                            mask=rec_data.get("mask"),
                                            gateway=rec_data.get("gateway"),
                                            mac=rec_data.get("mac"),
                                            key_lock_status=rec_data.get("key"),
                                            bzc_status=rec_data.get("BZC"),
                                            bzs_status=rec_data.get("BZS"),
                                            fnc_status=rec_data.get("FNC"),
                                            fns_status=rec_data.get("FNS"),
                                            pwr_status=rec_data.get("PWR"),
                                            production_date=rec_data.get("production_date"),
                                            hardware_version=rec_data.get("hardware_version"),
                                            software_version=rec_data.get("software_version"),
                                            firmware_version=rec_data.get("hardware_version"),
                                            temperature=rec_data.get("temperature"))
            deviceCardList = []
            lengthCards = len(rec_data.get("boardInfos"))
            if lengthCards > 0:
                # 取历史同步数据，获得各槽位上单板数据
                deviceCardList = db_session.query(M6200DeviceCards).filter(M6200DeviceCards.device_id == id).all()
                for index in range(lengthCards):
                    cardInfo = rec_data.get("boardInfos")[index]
                    # cardDetail = cardInfo.get("basicInfo", {})
                    boardType = cardInfo.get("slotActualCardType")
                    portsData = cardInfo.get("ports_data")
                    slotIndex = cardInfo.get("slotIndex")
                    deviceCard = M6200DeviceCards(card_id=f"{id}_{slotIndex}", device_id=id,
                                                   slot_index=slotIndex,
                                                   type=boardType, model=cardInfo.get("board_model"),
                                                   serial_number=cardInfo.get("sn"),
                                                   production_date=cardInfo.get("production_date"),
                                                   hardware_version=cardInfo.get("hardware_version"),
                                                   software_version=cardInfo.get("software_version"),
                                                   firmware_version=cardInfo.get("hardware_version"),
                                                   temperature=cardInfo.get("temperature"),
                                                   ports_data=json.dumps(portsData))
                    deviceCardList.append(deviceCard)

            db_session.query(M6200DeviceBasic).filter(M6200DeviceBasic.device_id == id).delete()
            db_session.add(deviceBasic)
            if len(deviceCardList) > 0:
                for item in deviceCardList:
                    db_session.add(item)

def set_note(ip, slotIndex, outKey, innerKey, newNote, jsonData):
    if innerKey == "port_note":
        port_mib_name = "ST-COMMON-MIB::portDesc"
        indices = (DEFAULT_SHELF_ID, int(slotIndex), DEFAULT_SUB_SLOT_NO, int(outKey), DEFAULT_SUB_PORT_NO)
    elif innerKey == "slot_note":
        port_mib_name = "ST-COMMON-MIB::slotDesc"
        indices = (DEFAULT_SHELF_ID, int(slotIndex), DEFAULT_SUB_SLOT_NO)
    snmp_client = SNMPClient(
        host=ip,
        community_read="private",
        community_write="private"
    )
    success = snmp_client.process('set', port_mib_name, indices, newNote)
    if not success:
        LOG.error(f"SNMP SET failed for {ip} slot {slotIndex}, key {outKey}, innerKey {innerKey}.")
        return jsonData, False
    _data = json.loads(jsonData)
    if _data is not None:
        _data[outKey][innerKey] = newNote
        return json.dumps(_data), True
    else:
        return jsonData, False

def update_reachable_status(ip, status):
    db_session = invent_db.get_session()

    db_session.query(OtnDeviceBasic).filter(OtnDeviceBasic.ip == ip).update(
        {OtnDeviceBasic.reachable_status: status})

def get_nmu(ip):
    db_session = invent_db.get_session()
    try:
        data = db_session.query(OtnTempData).filter(OtnTempData.ip == ip).first()
        if data and data.nmu:
             nmu_data = json.loads(data.nmu)
             return {str(k): v for k, v in nmu_data.items()}
        else:
             LOG.warning(f"NMU data not found in cache for IP {ip}")
             return {}
    except json.JSONDecodeError:
        LOG.error(f"Failed to decode cached NMU data for IP {ip}")
        return {}
    except Exception as e:
         LOG.error(f"Error fetching NMU cache for {ip}: {e}")
         return {}
    finally:
        if db_session: db_session.close()


def get_business_info(snmp_client, slotIndex, board_type):
    rec_data = {}
    if board_type == 'OLP':
        config_table_name_in_map = "olpConfigTable"
        iterating_indices = {
            "portNo": list(range(1, 6)),
        }

        row_data = snmp_client.get_slot_ports_data(
            table_name=config_table_name_in_map,
            slot_index=slotIndex,
            other_fixed_indices=FIXED_INDICES,
            iterating_indices_config=iterating_indices
        )
        if row_data:
            if isinstance(row_data, dict) and None in row_data and isinstance(row_data[None],
                                                                              dict):
                rec_data = row_data[None]
            elif isinstance(row_data, dict):
                rec_data = row_data
            else:
                LOG.warning(f"Unexpected data structure from get_slot_ports_data for OLP: {type(row_data)}")
                rec_data = {}
        else:
            LOG.warning(f"get_slot_ports_data returned None for OLP config {snmp_client.host} slot {slotIndex}.")
            rec_data = {}

    elif 'otu' in board_type or "OEO" in board_type:
        config_table_name_in_map = "otu10PortTable"
        iterating_indices = {
            "portNo": list(range(1, 7)),
        }

        row_data = snmp_client.get_slot_ports_data(
            table_name=config_table_name_in_map,
            slot_index=slotIndex,
            other_fixed_indices=FIXED_INDICES,
            iterating_indices_config=iterating_indices
        )
        if row_data:
            if isinstance(row_data, dict) and None in row_data and isinstance(row_data[None],
                                                                              dict):
                rec_data = row_data[None]
            elif isinstance(row_data, dict):
                rec_data = row_data
            else:
                LOG.warning(f"Unexpected data structure from get_slot_ports_data for OEO: {type(row_data)}")
                rec_data = {}
        else:
            LOG.warning(f"get_slot_ports_data returned None for OEO config {snmp_client.host} slot {slotIndex}.")
            rec_data = {}

    elif 'EDFA' in board_type:
        config_table_name_in_map = "oaPortTable"
        iterating_indices = {
            "portNo": list(range(1, 3)),
        }

        row_data = snmp_client.get_slot_ports_data(
            table_name=config_table_name_in_map,
            slot_index=slotIndex,
            other_fixed_indices=FIXED_INDICES,
            iterating_indices_config=iterating_indices
        )
        if row_data:
            if isinstance(row_data, dict) and None in row_data and isinstance(row_data[None],
                                                                              dict):
                rec_data = row_data[None]
            elif isinstance(row_data, dict):
                rec_data = row_data
            else:
                LOG.warning(f"Unexpected data structure from get_slot_ports_data for OEO: {type(row_data)}")
                rec_data = {}
        else:
            LOG.warning(f"get_slot_ports_data returned None for OEO config {snmp_client.host} slot {slotIndex}.")
            rec_data = {}
    return rec_data


def get_config(ip, slotIndex):
    db_session = invent_db.get_session()

    try:
        slotIndex_int = int(slotIndex)
    except ValueError:
        LOG.error(f"Invalid slotIndex format: {slotIndex}. Must be an integer.")
        return {}, 1, "Invalid slotIndex format."

    try:
        otnDeviceBasic = db_session.query(OtnDeviceBasic).filter(OtnDeviceBasic.ip == ip).first()
        if not otnDeviceBasic:
            return {}, 1, f"Device {ip} not found in DB"

        device_type = SERIES2MODEL.get(otnDeviceBasic.series)
        if not device_type:
            return {}, 1, f"Unknown device series for {ip}"

        snmp_client = SNMPClient(
            host=ip,
            community_read="private",
            community_write="private",
            device_type=device_type
        )

        nmu = get_nmu(ip)
        if not nmu:
            return {}, 1, "NMU info not found in cache!"

        board_type = nmu.get(str(slotIndex_int))
        if not board_type:
            if slotIndex_int == 0:
                board_type = "NMU"
                LOG.info(f"Assuming board type NMU for slot 0 as it's not in NMU cache for IP {ip}.")
            else:
                return {}, 1, f"Slot {slotIndex_int} is empty or its type is unknown in NMU cache!"

        LOG.info(f"Getting config for IP: {ip}, Slot: {slotIndex_int}, Board Type: {board_type}")

        rec_data = get_business_info(snmp_client, slotIndex_int, board_type)
        # board_model = "Unknown"
        # if otnDeviceBasic:
        #     card_basic_info = db_session.query(M6200DeviceCards.model).filter(
        #         M6200DeviceCards.device_id == otnDeviceBasic.id,
        #         M6200DeviceCards.slot_index == slotIndex_int
        #     ).first()
        #     if card_basic_info:
        #         board_model = card_basic_info[0]
        # else:
        #     LOG.error("otnDeviceBasic became None unexpectedly in get_config.")

        return {"configData": rec_data, "boardInfo": {"type": board_type}}, 0, "success"

    except PySnmpError as e:
        LOG.error(f"SNMP Error in get_config for ip={ip}, slot={slotIndex}: {e}")
        LOG.error(traceback.format_exc())
        return {}, 1, f"Failed to get configuration due to SNMP error: {str(e)}"
    except Exception as e:
        LOG.error(f"Error in get_config for ip={ip}, slot={slotIndex}: {e}")
        LOG.error(traceback.format_exc())
        return {}, 1, f"Failed to get configuration: {str(e)}"
    finally:
        if db_session:
            db_session.close()
        # SNMPClient's process method should manage its own resources/event loop.
        # If SNMPClient had a close method, it would be called here.
        # if snmp_client and hasattr(snmp_client, 'close'):
        #     snmp_client.close()


def get_param_port(key):
    parts = key.split('#')
    if len(parts) > 1 and parts[-1].isdigit():
        param = "#".join(parts[:-1])
        port = int(parts[-1])
    else:
        param = key
        port = None

    return param, port

def modify_config(ip, slotIndex, key, value, device_type):
    """Modifies configuration using SNMP SET. MIB module and indices are inferred."""
    try:
        slotIndex_int = int(slotIndex)
    except ValueError:
        return {}, 1, "Invalid slotIndex format, must be an integer."

    nmu = get_nmu(ip)
    if not nmu:
        return {}, 1, "NMU info not found in cache!"

    distributed_lock = DistributedLock(ip)
    if distributed_lock.acquire():
        snmp_client = None
        try:
            snmp_client = SNMPClient(
                host=ip,
                community_write="private",
                device_type=device_type
            )

            board_type = nmu.get(str(slotIndex_int))
            if slotIndex_int == 0:
                if not board_type: board_type = "NMU"
            elif not board_type:
                raise ValueError(
                    f"Slot {slotIndex_int} is empty or its type is unknown in NMU cache, cannot determine context for SET.")

            mib_object_name, port = get_param_port(key)

            if mib_object_name in SNMP_SET_INDICES:
                mib_info = SNMP_SET_INDICES[mib_object_name]
                mib_module = mib_info.get("mib_module", "ST-6200-MIB")
                if mib_info.get("indices", None) == ["shelfId", "slotNo", "subSlotNo"]:
                    indices = (DEFAULT_SHELF_ID, slotIndex_int, DEFAULT_SUB_SLOT_NO)
                else:
                    indices = (DEFAULT_SHELF_ID, slotIndex_int, DEFAULT_SUB_SLOT_NO, port, DEFAULT_SUB_PORT_NO)
            else:
                raise ValueError(
                    f"Cannot determine MIB module or indices for MIB object '{mib_object_name}' (from key '{key}') on slot {slotIndex_int}. Configuration unknown.")

            if indices is None:
                raise ValueError(f"Indices could not be determined for MIB object '{mib_object_name}'.")

            full_mib_name = f"{mib_module}::{mib_object_name}"

            LOG.info(
                f"Attempting SNMP SET: IP={ip}, Slot={slotIndex_int}, MIB={full_mib_name}, Indices={indices}, Value='{value}'")

            success = snmp_client.process('set', full_mib_name, indices, value)

            if success:
                LOG.info(f"SNMP SET successful for key '{key}' on {ip} slot {slotIndex_int}")
                return {}, 0, "success"
            else:
                LOG.error(
                    f"SNMP SET failed for key '{key}' on {ip} slot {slotIndex_int}. Agent might have rejected the value or operation, or an error occurred in snmp_util.")
                return {}, 1, "Failed, SNMP SET operation reported failure."

        except ValueError as e:
            LOG.error(f"Configuration input/logic error for {ip} slot {slotIndex_int}, key '{key}': {e}")
            return {}, 1, str(e)
        except PySnmpError as e:
            LOG.error(f"PySNMP SET Error for {ip} slot {slotIndex_int}, key '{key}': {e}")
            return {}, 1, f"Failed, SNMP Protocol Error: {e}"
        except Exception as e:
            LOG.error(f"Unexpected exception during SNMP SET for {ip} slot {slotIndex_int}, key '{key}': {e}")
            LOG.error(traceback.format_exc())
            return {}, 1, "Failed, configuration encountered an unexpected server-side exception."
        finally:
            distributed_lock.release()
            LOG.info(f"Released lock for SNMP SET on {ip} slot {slotIndex_int}")
            # if snmp_client and hasattr(snmp_client, 'close'):
            #     snmp_client.close()
    else:
        LOG.error(f"Could not acquire lock for SNMP SET on {ip} slot {slotIndex_int}")
        return {}, 1, "Failed, could not acquire device lock."


if __name__ == "__main__":
    get_device_info_by_ip("***********")