# -*- coding: utf-8 -*-
import time

from sqlalchemy import (
    <PERSON>umn,
    Integer,
    String,
    Text,
    <PERSON><PERSON>ey,
    Boolean,
    DateTime,
    Enum,
    JSON,
    Table
)
from sqlalchemy.orm import relationship, exc

from server.db.db_common import DBCommon
from server.db.models.base import Base
from server.util.encrypt_util import aes_cipher


class CampusTopologyConfig(Base):
    __tablename__ = "campus_topology_config"
    id = Column(Integer, primary_key=True, autoincrement=True)
    site_id = Column(Integer, ForeignKey('site.id', ondelete='CASCADE', onupdate='CASCADE'))
    topology_name = Column(String(128))
    configuration = Column(JSON)


class CampusSiteNodes(Base):
    __tablename__ = "campus_site_nodes"
    id = Column(Integer, primary_key=True, autoincrement=True)
    topology_config_id = Column(Integer, ForeignKey('campus_topology_config.id', ondelete='CASCADE', onupdate='CASCADE'))    
    switch_sn = Column(String(128))
    type = Column(String(32), default='core', nullable=False)
    node_info = Column(JSON)

class CampusBlueprintDB(DBCommon):

    def add_campus_site_config(self, site_id, topology_name, configuration):
        session = self.get_session()
        with session.begin(subtransactions=True):
            existing_config = session.query(CampusTopologyConfig).filter(
                CampusTopologyConfig.topology_name == topology_name
            ).first()
            
            if existing_config:
                existing_config.site_id = site_id
                existing_config.configuration = configuration
                return existing_config
            else:
                config = CampusTopologyConfig(site_id=site_id, topology_name=topology_name, configuration=configuration)
                session.add(config)
                return config
    
    def add_campus_site_node(self, config_id, switch_sn, node_type, node_info):
        session = self.get_session()
        with session.begin(subtransactions=True):
            existing_node = session.query(CampusSiteNodes).filter(
                CampusSiteNodes.topology_config_id == config_id,
                CampusSiteNodes.switch_sn == switch_sn,
                CampusSiteNodes.type == node_type
            ).first()
            
            if existing_node:
                existing_node.node_info = node_info
                return existing_node
            else:
                node = CampusSiteNodes(topology_config_id=config_id, switch_sn=switch_sn, type=node_type, node_info=node_info)
                session.add(node)
                return node
    
    def get_campus_site_config(self, config_id):
        session = self.get_session()
        config = session.query(CampusTopologyConfig).filter(CampusTopologyConfig.id == config_id).first()
        return config
    
    def get_campus_site_nodes(self, config_id):
        session = self.get_session()
        nodes = session.query(CampusSiteNodes).filter(CampusSiteNodes.topology_config_id == config_id).all()
        return nodes


campus_blueprint_db = CampusBlueprintDB()
