# revision identifiers, used by Alembic.
revision = 'v2.2.0'
down_revision = 'v9'
branch_labels = None
depends_on = None

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql
from sqlalchemy import inspect


# 获取数据库连接
connection = op.get_bind()

# 创建 Inspector
inspector = inspect(connection)

def upgrade():
    #### campus
    op.create_table('campus_topology_config',
                    sa.Column('id', sa.Integer(), primary_key=True),
                    sa.<PERSON>umn('site_id', sa.Integer(), nullable=False),
                    sa.Column('topology_name', sa.String(length=128), nullable=False),
                    sa.Column('configuration', sa.JSON(), nullable=False),
                    sa.Column('create_time', sa.DateTime(), nullable=True),
                    sa.Column('modified_time', sa.DateTime(), nullable=True),
                    )

    op.create_table('campus_site_nodes',
                    sa.Column('id', sa.Integer(), primary_key=True),
                    sa.Column('topology_config_id', sa.Integer(), nullable=False),
                    sa.Column('switch_sn', sa.String(length=128), nullable=False),
                    sa.Column('type', sa.String(length=32), nullable=False),
                    sa.Column('node_info', sa.JSON(), nullable=False),
                    sa.Column('create_time', sa.DateTime(), nullable=True),
                    sa.Column('modified_time', sa.DateTime(), nullable=True),
                    )

    op.create_foreign_key('fk_campus_topology_config_site_id', 'campus_topology_config', 'site', ['site_id'], ['id'],
                          ondelete='CASCADE', onupdate='CASCADE')
    op.create_foreign_key('fk_campus_site_nodes_topology_config_id', 'campus_site_nodes', 'campus_topology_config',
                          ['topology_config_id'], ['id'],
                          ondelete='CASCADE', onupdate='CASCADE')

    ##### dc
    op.create_table('dc_fabric_unit',
                    sa.Column('id', sa.Integer(), primary_key=True),
                    sa.Column('name', sa.String(length=128), nullable=False),
                    sa.Column('description', sa.String(length=256), nullable=True),
                    sa.Column('unit_info', sa.JSON(), nullable=False),
                    sa.Column('create_time', sa.DateTime(), nullable=True),
                    sa.Column('modified_time', sa.DateTime(), nullable=True),
                    )

    op.create_table('dc_fabric_template',
                    sa.Column('id', sa.Integer(), primary_key=True),
                    sa.Column('name', sa.String(length=128), nullable=False),
                    sa.Column('description', sa.String(length=256), nullable=True),
                    sa.Column('type', sa.String(length=32), nullable=False),
                    sa.Column('underlay_routing_protocol', sa.String(length=32), nullable=False),
                    sa.Column('overlay_control_protocol', sa.String(length=32), nullable=False),
                    sa.Column('template_info', sa.JSON(), nullable=False),
                    sa.Column('create_time', sa.DateTime(), nullable=True),
                    sa.Column('modified_time', sa.DateTime(), nullable=True),
                    )

    op.create_table('dc_fabric_topology',
                    sa.Column('id', sa.Integer(), primary_key=True),
                    sa.Column('fabric_id', sa.Integer(), nullable=False),
                    sa.Column('template_name', sa.String(length=128), nullable=False),
                    sa.Column('fabric_config', sa.JSON(), nullable=False),
                    sa.Column('create_time', sa.DateTime(), nullable=True),
                    sa.Column('modified_time', sa.DateTime(), nullable=True),
                    )

    op.create_table('dc_fabric_topology_node',
                    sa.Column('id', sa.Integer(), primary_key=True),
                    sa.Column('logic_name', sa.String(length=128), nullable=False),
                    sa.Column('switch_sn', sa.String(length=128), nullable=True),
                    sa.Column('fabric_topo_id', sa.Integer(), nullable=False),
                    sa.Column('type', sa.String(length=32), nullable=False),
                    sa.Column('node_info', sa.JSON(), nullable=False),
                    sa.Column('create_time', sa.DateTime(), nullable=True),
                    sa.Column('modified_time', sa.DateTime(), nullable=True),
                    )

    op.create_foreign_key('fk_dc_fabric_topology_fabric_id', 'dc_fabric_topology', 'fabric', ['fabric_id'], ['id'],
                          ondelete='CASCADE', onupdate='CASCADE')
    op.create_foreign_key('fk_dc_fabric_topology_node_fabric_topo_id', 'dc_fabric_topology_node', 'dc_fabric_topology',
                          ['fabric_topo_id'], ['id'],
                          ondelete='CASCADE', onupdate='CASCADE')

    # FMT
    if 'dcp_device_basic' in inspector.get_table_names():
        op.add_column('dcp_device_basic', sa.Column('series', sa.Integer(), nullable=True))
        # 新增series字段前都是dcp920设备数据，需要赋值series=1
        op.execute(
            '''update automation.dcp_device_basic set series=1 where series is null;''')
        op.rename_table('dcp_device_basic', 'otn_device_basic')

    if 'dcp_temp_data' in inspector.get_table_names():
        op.rename_table('dcp_temp_data', 'otn_temp_data')

    op.execute('''
            ALTER TABLE otn_temp_data MODIFY nmu VARCHAR(256);
        ''')
    op.execute('''
            ALTER TABLE otn_temp_data ADD COLUMN description VARCHAR(255) NULL;
        ''')

    op.create_table('snmp_alarm_trap_metadata',
                    sa.Column('trap_id', sa.Integer(), nullable=False, primary_key=True),
                    sa.Column('create_time', sa.DateTime(), nullable=False),
                    sa.Column('modified_time', sa.DateTime(), nullable=False),
                    sa.Column('name', sa.String(128), nullable=False),
                    sa.Column('alarm_type', sa.Integer(), nullable=False),
                    sa.Column('alarm_level', sa.Integer(), nullable=False),
                    sa.Column('oid', sa.String(128), nullable=False),
                    sa.Column('match_regular', sa.String(128), nullable=False),
                    sa.Column('sub_oid_mapping', sa.Text(4096), nullable=True),
                    sa.Column('value_mapping', sa.Text(4096), nullable=True),
                    sa.Column('description', sa.String(64), nullable=True)
                    )

    op.create_table('snmp_alarm_trap_original_data',
                    sa.Column('alarm_id', sa.Integer(), nullable=False, primary_key=True),
                    sa.Column('create_time', sa.DateTime(), nullable=False),
                    sa.Column('modified_time', sa.DateTime(), nullable=False),
                    sa.Column('source_ip', sa.String(64), nullable=False),
                    sa.Column('occurrence_time', sa.DateTime(), nullable=False),
                    sa.Column('name', sa.String(128), nullable=False),
                    sa.Column('value', sa.Text(4096), nullable=False),
                    sa.Column('description', sa.String(128), nullable=True)
                    )

    op.create_table('fmt_device_basic',
                    sa.Column('device_id', sa.Integer(), nullable=False, primary_key=True),
                    sa.Column('create_time', sa.DateTime(), nullable=False),
                    sa.Column('modified_time', sa.DateTime(), nullable=False),
                    sa.Column('serial_number', sa.String(48), nullable=True),
                    sa.Column('slot_number', sa.Integer(), nullable=False, default=0),
                    sa.Column('mask', sa.String(48), nullable=True),
                    sa.Column('gateway', sa.String(48), nullable=True),
                    sa.Column('mac', sa.String(48), nullable=True),
                    sa.Column('key_lock_status', sa.String(10), nullable=False, default="0"),
                    sa.Column('bzc_status', sa.String(10), nullable=False, default="0"),
                    sa.Column('bzs_status', sa.String(10), nullable=False, default="0"),
                    sa.Column('fnc_status', sa.String(10), nullable=False, default="0"),
                    sa.Column('fns_status', sa.String(10), nullable=False, default="0"),
                    sa.Column('pwr_status', sa.String(10), nullable=False, default="0"),
                    sa.Column('production_date', sa.String(48), nullable=True),
                    sa.Column('hardware_version', sa.String(48), nullable=True),
                    sa.Column('software_version', sa.String(48), nullable=True),
                    sa.Column('firmware_version', sa.String(48), nullable=True),
                    sa.Column('temperature', sa.String(10), nullable=True)
                    )

    op.create_foreign_key('fmt_device_basic_fk', 'fmt_device_basic', 'otn_device_basic', ['device_id'], ['id'],
                          ondelete='CASCADE')

    op.create_table('fmt_device_cards',
                    sa.Column('card_id', sa.String(32), nullable=False, primary_key=True),
                    sa.Column('device_id', sa.Integer(), nullable=False),
                    sa.Column('slot_index', sa.Integer(), nullable=False),
                    sa.Column('type', sa.String(32), nullable=False),
                    sa.Column('model', sa.String(32), nullable=True),
                    sa.Column('create_time', sa.DateTime(), nullable=False),
                    sa.Column('modified_time', sa.DateTime(), nullable=False),
                    sa.Column('serial_number', sa.String(48), nullable=True),
                    sa.Column('production_date', sa.String(48), nullable=True),
                    sa.Column('hardware_version', sa.String(48), nullable=True),
                    sa.Column('software_version', sa.String(48), nullable=True),
                    sa.Column('firmware_version', sa.String(48), nullable=True),
                    sa.Column('temperature', sa.String(10), nullable=True),
                    sa.Column('ports_data', sa.Text(65535), nullable=True)
                    )

    op.create_foreign_key('fmt_device_cards_fk', 'fmt_device_cards', 'fmt_device_basic', ['device_id'], ['device_id'],
                          ondelete='CASCADE')

    op.create_table('dcs_device_basic',
                    sa.Column('device_id', sa.Integer(), nullable=False, primary_key=True),
                    sa.Column('create_time', sa.DateTime(), nullable=False),
                    sa.Column('modified_time', sa.DateTime(), nullable=False),
                    sa.Column('serial_number', sa.String(48), nullable=True),
                    sa.Column('slot_number', sa.Integer(), nullable=False, default=0),
                    sa.Column('mask', sa.String(48), nullable=True),
                    sa.Column('gateway', sa.String(48), nullable=True),
                    sa.Column('mac', sa.String(48), nullable=True),
                    sa.Column('key_lock_status', sa.String(10), nullable=False, default="0"),
                    sa.Column('bzc_status', sa.String(10), nullable=False, default="0"),
                    sa.Column('bzs_status', sa.String(10), nullable=False, default="0"),
                    sa.Column('fnc_status', sa.String(10), nullable=False, default="0"),
                    sa.Column('fns_status', sa.String(10), nullable=False, default="0"),
                    sa.Column('pwr_status', sa.String(10), nullable=False, default="0"),
                    sa.Column('production_date', sa.String(48), nullable=True),
                    sa.Column('hardware_version', sa.String(48), nullable=True),
                    sa.Column('software_version', sa.String(48), nullable=True),
                    sa.Column('firmware_version', sa.String(48), nullable=True),
                    sa.Column('temperature', sa.String(10), nullable=True)
                    )

    op.create_foreign_key('dcs_device_basic_fk', 'dcs_device_basic', 'otn_device_basic', ['device_id'], ['id'],
                          ondelete='CASCADE')

    op.create_table('dcs_device_cards',
                    sa.Column('card_id', sa.String(32), nullable=False, primary_key=True),
                    sa.Column('device_id', sa.Integer(), nullable=False),
                    sa.Column('slot_index', sa.Integer(), nullable=False),
                    sa.Column('type', sa.String(32), nullable=False),
                    sa.Column('model', sa.String(32), nullable=True),
                    sa.Column('create_time', sa.DateTime(), nullable=False),
                    sa.Column('modified_time', sa.DateTime(), nullable=False),
                    sa.Column('serial_number', sa.String(48), nullable=True),
                    sa.Column('production_date', sa.String(48), nullable=True),
                    sa.Column('hardware_version', sa.String(48), nullable=True),
                    sa.Column('software_version', sa.String(48), nullable=True),
                    sa.Column('firmware_version', sa.String(48), nullable=True),
                    sa.Column('temperature', sa.String(10), nullable=True),
                    sa.Column('ports_data', sa.Text(65535), nullable=True)
                    )

    op.create_foreign_key('dcs_device_cards_fk', 'dcs_device_cards', 'dcs_device_basic', ['device_id'], ['device_id'],
                          ondelete='CASCADE')

    op.create_table('m6200_device_basic',
                    sa.Column('device_id', sa.Integer(), nullable=False, primary_key=True),
                    sa.Column('create_time', sa.DateTime(), nullable=False),
                    sa.Column('modified_time', sa.DateTime(), nullable=False),
                    sa.Column('serial_number', sa.String(48), nullable=True),
                    sa.Column('slot_number', sa.Integer(), nullable=False, default=0),
                    sa.Column('mask', sa.String(48), nullable=True),
                    sa.Column('gateway', sa.String(48), nullable=True),
                    sa.Column('mac', sa.String(48), nullable=True),
                    sa.Column('key_lock_status', sa.String(10), nullable=False, default="0"),
                    sa.Column('bzc_status', sa.String(10), nullable=False, default="0"),
                    sa.Column('bzs_status', sa.String(10), nullable=False, default="0"),
                    sa.Column('fnc_status', sa.String(10), nullable=False, default="0"),
                    sa.Column('fns_status', sa.String(10), nullable=False, default="0"),
                    sa.Column('pwr_status', sa.String(10), nullable=False, default="0"),
                    sa.Column('production_date', sa.String(48), nullable=True),
                    sa.Column('hardware_version', sa.String(48), nullable=True),
                    sa.Column('software_version', sa.String(48), nullable=True),
                    sa.Column('firmware_version', sa.String(48), nullable=True),
                    sa.Column('temperature', sa.String(10), nullable=True)
                    )

    op.create_foreign_key('m6200_device_basic_fk', 'm6200_device_basic', 'otn_device_basic', ['device_id'], ['id'],
                          ondelete='CASCADE')

    op.create_table('m6200_device_cards',
                    sa.Column('card_id', sa.String(32), nullable=False, primary_key=True),
                    sa.Column('device_id', sa.Integer(), nullable=False),
                    sa.Column('slot_index', sa.Integer(), nullable=False),
                    sa.Column('type', sa.String(32), nullable=False),
                    sa.Column('model', sa.String(32), nullable=True),
                    sa.Column('create_time', sa.DateTime(), nullable=False),
                    sa.Column('modified_time', sa.DateTime(), nullable=False),
                    sa.Column('serial_number', sa.String(48), nullable=True),
                    sa.Column('production_date', sa.String(48), nullable=True),
                    sa.Column('hardware_version', sa.String(48), nullable=True),
                    sa.Column('software_version', sa.String(48), nullable=True),
                    sa.Column('firmware_version', sa.String(48), nullable=True),
                    sa.Column('temperature', sa.String(10), nullable=True),
                    sa.Column('ports_data', sa.Text(65535), nullable=True)
                    )

    op.create_foreign_key('m6200_device_cards_fk', 'm6200_device_cards', 'm6200_device_basic', ['device_id'], ['device_id'],
                          ondelete='CASCADE')

    op.create_table('alarm_statistics_hourly',
                    sa.Column('id', sa.Integer(), primary_key=True),
                    sa.Column('create_time', sa.DateTime(), nullable=True),
                    sa.Column('modified_time', sa.DateTime(), nullable=True),
                    sa.Column('time', sa.DateTime(), nullable=False),
                    sa.Column('info', sa.Integer(), nullable=False),
                    sa.Column('warn', sa.Integer(), nullable=False),
                    sa.Column('error', sa.Integer(), nullable=False),
                    )

    # DC Fabric
    op.execute('''
        CREATE TABLE resource_pool_asn
            (
                `create_time`   datetime     NULL DEFAULT NULL,
                `modified_time` datetime     NULL DEFAULT NULL,
                `id`            BIGINT PRIMARY KEY AUTO_INCREMENT,
                `name`          VARCHAR(128) NOT NULL UNIQUE 
            );
    ''')
    op.execute('''
        CREATE TABLE resource_pool_asn_ranges
            (
                `create_time`      datetime NULL DEFAULT NULL,
                `modified_time`    datetime NULL DEFAULT NULL,
                `id`               BIGINT PRIMARY KEY AUTO_INCREMENT,
                `start_value`      BIGINT   NOT NULL,
                `end_value`        BIGINT   NOT NULL,
                `resource_pool_asn_id` BIGINT   NOT NULL,
                `is_in_use`        BOOLEAN  NOT NULL,
                CONSTRAINT fk_resource_pool FOREIGN KEY (resource_pool_asn_id)
                    REFERENCES resource_pool_asn (id)
                    ON DELETE CASCADE
            );
    ''')

    op.execute('''
        CREATE TABLE resource_pool_asn_use_detail
            (
                `create_time`        datetime NULL DEFAULT NULL,
                `modified_time`      datetime NULL DEFAULT NULL,
                `id`                 BIGINT PRIMARY KEY AUTO_INCREMENT,
                `value`              BIGINT   NOT NULL,
                `resource_pool_asn_ranges_id` BIGINT   NOT NULL,
                CONSTRAINT fk_resource_ranges_asn FOREIGN KEY (resource_pool_asn_ranges_id)
                    REFERENCES resource_pool_asn_ranges (id)
                    ON DELETE CASCADE
            );
    ''')

    op.execute('''
        CREATE TABLE resource_pool_area
            (
                `create_time`   datetime     NULL DEFAULT NULL,
                `modified_time` datetime     NULL DEFAULT NULL,
                `id`            BIGINT PRIMARY KEY AUTO_INCREMENT,
                `name`          VARCHAR(128) NOT NULL UNIQUE 
            );
    ''')
    op.execute('''
        CREATE TABLE resource_pool_area_ranges
            (
                `create_time`      datetime NULL DEFAULT NULL,
                `modified_time`    datetime NULL DEFAULT NULL,
                `id`               BIGINT PRIMARY KEY AUTO_INCREMENT,
                `start_value`      BIGINT   NOT NULL,
                `end_value`        BIGINT   NOT NULL,
                `resource_pool_area_id` BIGINT   NOT NULL,
                `is_in_use`        BOOLEAN  NOT NULL,
                CONSTRAINT fk_resource_pool_area FOREIGN KEY (resource_pool_area_id)
                    REFERENCES resource_pool_area (id)
                    ON DELETE CASCADE
            );
    ''')

    op.execute('''
        CREATE TABLE resource_pool_area_use_detail
            (
                `create_time`        datetime NULL DEFAULT NULL,
                `modified_time`      datetime NULL DEFAULT NULL,
                `id`                 BIGINT PRIMARY KEY AUTO_INCREMENT,
                `value`              BIGINT   NOT NULL,
                `resource_pool_area_ranges_id` BIGINT   NOT NULL,
                CONSTRAINT fk_resource_ranges_area FOREIGN KEY (resource_pool_area_ranges_id)
                    REFERENCES resource_pool_area_ranges (id)
                    ON DELETE CASCADE
            );
    ''')

    op.execute('''
            CREATE TABLE resource_pool_ip
                (
                    `create_time`   datetime     NULL DEFAULT NULL,
                    `modified_time` datetime     NULL DEFAULT NULL,
                    `id`            BIGINT PRIMARY KEY AUTO_INCREMENT,
                    `name`          VARCHAR(128) NOT NULL UNIQUE 
                );
        ''')
    op.execute('''
            CREATE TABLE resource_pool_ip_ranges
                (
                    `create_time`      datetime NULL DEFAULT NULL,
                    `modified_time`    datetime NULL DEFAULT NULL,
                    `id`               BIGINT PRIMARY KEY AUTO_INCREMENT,
                    `start_value`      BIGINT   NOT NULL,
                    `end_value`        BIGINT   NOT NULL,
                    `resource_pool_ip_id` BIGINT   NOT NULL,
                    `is_in_use`        BOOLEAN  NOT NULL,
                    CONSTRAINT fk_resource_pool_ip_ranges FOREIGN KEY (resource_pool_ip_id)
                        REFERENCES resource_pool_ip (id)
                        ON DELETE CASCADE
                );
        ''')

    op.execute('''
            CREATE TABLE resource_pool_ip_use_detail
                (
                    `create_time`        datetime NULL DEFAULT NULL,
                    `modified_time`      datetime NULL DEFAULT NULL,
                    `id`                 BIGINT PRIMARY KEY AUTO_INCREMENT,
                    `value`              BIGINT   NOT NULL,
                    `resource_pool_ip_ranges_id` BIGINT   NOT NULL,
                    CONSTRAINT fk_resource_ranges_ip FOREIGN KEY (resource_pool_ip_ranges_id)
                        REFERENCES resource_pool_ip_ranges (id)
                        ON DELETE CASCADE
                );
        ''')

    op.execute('''
            ALTER TABLE event ADD COLUMN resource_id VARCHAR(64) NULL;
        ''')
    op.execute('''
            ALTER TABLE event ADD COLUMN resource_name VARCHAR(32) NULL;
        ''')


def downgrade():
    op.drop_table('campus_site_nodes')
    op.drop_table('campus_topology_config')

    op.drop_table('dc_fabric_unit')
    op.drop_table('dc_fabric_template')
    op.drop_table('dc_fabric_topology_node')
    op.drop_table('dc_fabric_topology')

    op.drop_table('m6200_device_cards')
    op.drop_table('m6200_device_basic')
    op.drop_table('dcs_device_cards')
    op.drop_table('dcs_device_basic')
    op.drop_table('fmt_device_cards')
    op.drop_table('fmt_device_basic')
    op.drop_table('alarm_statistics_hourly')
    if 'otn_device_basic' in inspector.get_table_names():
        op.drop_column('otn_device_basic', 'series')
        op.rename_table('otn_device_basic', 'dcp_device_basic')
    if 'otn_temp_data' in inspector.get_table_names():
        op.execute('''
                ALTER TABLE otn_temp_data DROP COLUMN description;
            ''')
        op.rename_table('otn_temp_data', 'dcp_temp_data')

    op.drop_table('snmp_alarm_trap_metadata')
    op.drop_table('snmp_alarm_trap_original_data')

    op.execute('drop table if exists resource_pool_asn_use_detail;')
    op.execute('drop table if exists resource_pool_asn_ranges;')
    op.execute('drop table if exists resource_pool_asn;')
    op.execute('drop table if exists resource_pool_area_use_detail;')
    op.execute('drop table if exists resource_pool_area_ranges;')
    op.execute('drop table if exists resource_pool_area;')
    op.execute('drop table if exists resource_pool_ip_use_detail;')
    op.execute('drop table if exists resource_pool_ip_ranges;')
    op.execute('drop table if exists resource_pool_ip;')
    op.execute('ALTER TABLE event DROP COLUMN resource_id;')
    op.execute('ALTER TABLE event DROP COLUMN resource_name;')
