import json
import logging
import traceback
import threading
from flask import Blueprint, jsonify, Response, request
from datetime import timedelta, datetime, date

from server.util import utils
from server.util.permission import admin_permission
from server.db.models.dc_blueprint import dc_fabric_db, DCFabricUnit, DCFabricTemplate
from server.util.fabric_topology_util import TopologyBuilder, update_topology_by_template, update_topology_node_info

dc_blueprint_mold = Blueprint("dc_blueprint", __name__, template_folder="templates")
LOG = logging.getLogger(__name__)


@dc_blueprint_mold.route("/fabric_unit/list", methods=["POST"])
@admin_permission.require(http_exception=403)
def fabric_unit_list():
    try:
        page_num, page_size, total_count, query_obj = utils.query_helper(DCFabricUnit)
        response = {
            "data": [{
                "id": unit.id,
                "name": unit.name,
                "description": unit.description,
                "leaf_count": len(unit.unit_info.get("leaf", [])),
                "mlag_count": sum(1 for leaf in unit.unit_info.get("leaf", []) if leaf["strategy"] == "mlag"),
                "access_count": len(unit.unit_info.get("access", [])),
                "unit_info": unit.unit_info,
            } for unit in query_obj],
            "page": page_num,
            "pageSize": page_size,
            "total": total_count,
            "status": 200
        }
        return jsonify(response)
    except Exception as e:
        return jsonify({'status': 400, 'info': 'Failed to list unit'})


@dc_blueprint_mold.route("/fabric_unit/save", methods=["POST"])
@admin_permission.require(http_exception=403)
def fabric_unit_save():
    try:
        data = request.get_json()
        if not data:
            return jsonify({'info': "Invalid request data", 'status': 400})

        id = data.get('id', None)
        name = data.get('name')
        description = data.get('description')
        unit_info = data.get('unit_info', {})
        
        res, msg = validate_unit_info(unit_info)
        if res:
            dc_fabric_db.update_fabric_unit(id, name, unit_info, description)
            return jsonify({'status': 200, 'info': 'Save unit successed.'})
        else:
            return jsonify({'status': 400, 'info': f'Save unit failed: {msg}'})
    except Exception as e:
        LOG.error(traceback.format_exc())
        return jsonify({'status': 400, 'info': 'Save unit failed.'})


def validate_unit_info(unit_info):
    if "leaf" in unit_info:
        if not isinstance(unit_info["leaf"], list):
            return False, "leaf should be a list."

        for item in unit_info["leaf"]:
            if not isinstance(item, dict):
                return False, "Each item in leaf should be a dictionary."
    
    if "access" in unit_info:
        if not isinstance(unit_info["access"], list):
            return False, "access should be a list."

        for item in unit_info["access"]:
            if not isinstance(item, dict):
                return False, "Each item in access should be a dictionary."
    
    return True, "Validation successful."


@dc_blueprint_mold.route("/fabric_unit/view", methods=["POST"])
@admin_permission.require(http_exception=403)
def fabric_unit_view():
    try:
        data = request.get_json()
        if not data:
            return jsonify({'info': "Invalid request data", 'status': 400})

        unit_id = data.get('unit_id')
        unit = dc_fabric_db.get_fabric_unit_by_id(unit_id)

        return jsonify({'status': 200, 'info': 'Get unit successed.', "data": unit.make_dict()})
    except Exception as e:
        LOG.error(traceback.format_exc())
        return jsonify({'status': 400, 'info': 'Get unit failed.'})


@dc_blueprint_mold.route("/fabric_unit/delete", methods=["POST"])
@admin_permission.require(http_exception=403)
def fabric_unit_delete():
    try:
        data = request.get_json()
        if not data:
            return jsonify({'info': "Invalid request data", 'status': 400})

        unit_id = data.get('unit_id')
        dc_fabric_db.del_fabric_unit_by_id(unit_id)

        return jsonify({'status': 200, 'info': 'Delete unit successed.'})
    except Exception as e:
        LOG.error(traceback.format_exc())
        return jsonify({'status': 400, 'info': 'Delete unit failed.'})


@dc_blueprint_mold.route("/fabric_unit/clone", methods=["POST"])
@admin_permission.require(http_exception=403)
def fabric_unit_clone():
    try:
        data = request.get_json()
        if not data:
            return jsonify({'info': "Invalid request data", 'status': 400})

        unit_id = data.get('clone_unit_id')
        name = data.get('name')
        description = data.get('description')
        
        exist = dc_fabric_db.check_fabric_unit_name(name)
        if exist:
            return jsonify({'status': 400, 'info': 'Unit name existed.'})
        
        unit = dc_fabric_db.get_fabric_unit_by_id(unit_id)
        dc_fabric_db.update_fabric_unit(unit_id=None, unit_name=name, unit_info=unit.unit_info, description=description)

        return jsonify({'status': 200, 'info': 'Clone unit successed.'})
    except Exception as e:
        LOG.error(traceback.format_exc())
        return jsonify({'status': 400, 'info': 'Clone unit failed.'})


@dc_blueprint_mold.route("/fabric_template/list", methods=["POST"])
@admin_permission.require(http_exception=403)
def fabric_template_list():
    try:
        page_num, page_size, total_count, query_obj = utils.query_helper(DCFabricTemplate)
        response = {
            "data": [{
                "id": template.id,
                "name": template.name,
                "description": template.description,
                "type": template.type,
                "underlay_routing_protocol": template.underlay_routing_protocol,
                "overlay_control_protocol": template.overlay_control_protocol,
            } for template in query_obj],
            "page": page_num,
            "pageSize": page_size,
            "total": total_count,
            "status": 200
        }
        return jsonify(response)
    except Exception as e:
        return jsonify({'status': 400, 'info': 'Failed to list template'})
    

@dc_blueprint_mold.route("/fabric_template/save", methods=["POST"])
@admin_permission.require(http_exception=403)
def fabric_template_save():
    try:
        data = request.get_json()
        if not data:
            return jsonify({'info': "Invalid request data", 'status': 400})

        id = data.get('id', None)
        name = data.get('name')
        description = data.get('description')
        type = data.get("type")
        underlay_routing_protocol = data.get("underlay_routing_protocol")
        overlay_control_protocol = data.get("overlay_control_protocol")
        template_info = data.get('template_info', {})
        
        # 将unit全量快照存在template中
        if type == "3-stage":
            new_unit_list = []
            for unit in template_info.get("unit"):
                unit_id = unit.get("id")
                unit_info = dc_fabric_db.get_fabric_unit_by_id(unit_id)
                if unit_info:
                    new_unit_list.append(unit_info.make_dict())
                else:
                    return jsonify({'info': f"Unit {unit_id} not found", 'status': 400})
            template_info["unit"] = new_unit_list
        elif type == "5-stage":                
            for pod in template_info.get("pod"):
                new_unit_list = []
                for unit in pod.get("unit", []):
                    unit_id = unit.get("id")
                    unit_info = dc_fabric_db.get_fabric_unit_by_id(unit_id)
                    if unit_info:
                        new_unit_list.append(unit_info.make_dict())
                    else:
                        return jsonify({'info': f"Unit {unit_id} not found", 'status': 400})
                pod["unit"] = new_unit_list
        template_info['type'] = type

        dc_fabric_db.update_fabric_template(id, name, type=type, underlay_routing_protocol=underlay_routing_protocol, 
                                            overlay_control_protocol=overlay_control_protocol, template_info=template_info, description=description)
        return jsonify({'status': 200, 'info': 'Save template successed.'})
    except Exception as e:
        LOG.error(traceback.format_exc())
        return jsonify({'status': 400, 'info': 'Save template failed.'})


@dc_blueprint_mold.route("/fabric_template/view", methods=["POST"])
@admin_permission.require(http_exception=403)
def fabric_template_view():
    try:
        data = request.get_json()
        if not data:
            return jsonify({'info': "Invalid request data", 'status': 400})

        template_id = data.get('template_id')
        template = dc_fabric_db.get_fabric_template_by_id(template_id)

        return jsonify({'status': 200, 'info': 'Get template successed.', "data": template.make_dict()})
    except Exception as e:
        LOG.error(traceback.format_exc())
        return jsonify({'status': 400, 'info': 'Get template failed.'})


@dc_blueprint_mold.route("/fabric_template/delete", methods=["POST"])
@admin_permission.require(http_exception=403)
def fabric_template_delete():
    try:
        data = request.get_json()
        if not data:
            return jsonify({'info': "Invalid request data", 'status': 400})

        template_id = data.get('template_id')
        dc_fabric_db.del_fabric_template_by_id(template_id)

        return jsonify({'status': 200, 'info': 'Delete template successed.'})
    except Exception as e:
        LOG.error(traceback.format_exc())
        return jsonify({'status': 400, 'info': 'Delete template failed.'})
    

@dc_blueprint_mold.route("/fabric_template/clone", methods=["POST"])
@admin_permission.require(http_exception=403)
def fabric_template_clone():
    try:
        data = request.get_json()
        if not data:
            return jsonify({'info': "Invalid request data", 'status': 400})

        template_id = data.get('clone_template_id')
        name = data.get('name')
        description = data.get('description')
        
        exist = dc_fabric_db.check_fabric_template_name(name)
        if exist:
            return jsonify({'status': 400, 'info': 'Template name existed.'})
        
        template = dc_fabric_db.get_fabric_template_by_id(template_id)
        dc_fabric_db.update_fabric_template(template_id=None, template_name=name, type=template.type, underlay_routing_protocol=template.underlay_routing_protocol, 
                                            overlay_control_protocol=template.overlay_control_protocol, template_info=template.template_info, 
                                            description=description)

        return jsonify({'status': 200, 'info': 'Clone template successed.'})
    except Exception as e:
        LOG.error(traceback.format_exc())
        return jsonify({'status': 400, 'info': 'Clone template failed.'})


@dc_blueprint_mold.route("/fabric_topo/create", methods=["POST"])
@admin_permission.require(http_exception=403)
def fabric_topo_create():
    try:
        data = request.get_json()
        if not data:
            return jsonify({'info': "Invalid request data", 'status': 400})

        fabric_id = data.get('fabric_id')
        fabric_template_id = data.get('fabric_template_id')
        
        template = dc_fabric_db.get_fabric_template_by_id(fabric_template_id)
        
        #构建拓扑 
        template_info = template.template_info
        builder = TopologyBuilder(template_info, template.type, template.underlay_routing_protocol, template.overlay_control_protocol)
        topology = builder.build_topology()
        template_info['topology']= topology
        fabric_topology = dc_fabric_db.update_fabric_topology(fabric_id=fabric_id, template_name=template.name, fabric_config=template.template_info)
        #创建node节点
        update_topology_node_info(topology, fabric_topology.id)

        return jsonify({'status': 200, 'info': 'Create fabric topology successed.', 'data': fabric_topology.make_dict()})
    except Exception as e:
        LOG.error(traceback.format_exc())
        return jsonify({'status': 400, 'info': 'Create fabric topology failed.'})


@dc_blueprint_mold.route("/fabric_topo/edit", methods=["POST"])
@admin_permission.require(http_exception=403)
def fabric_topo_edit():
    try:
        data = request.get_json()
        if not data:
            return jsonify({'info': "Invalid request data", 'status': 400})

        fabric_id = data.get('fabric_id')
        template_name = data.get('template_name')
        fabric_config = data.get('fabric_config')
     
        #更新拓扑结构 
        type = fabric_config.get("type")
        topology = fabric_config.get("topology")
        update_topology_by_template(topology, fabric_config, type)
        fabric_config['topology']= topology
        fabric_topology = dc_fabric_db.update_fabric_topology(fabric_id=fabric_id, template_name=template_name, fabric_config=fabric_config)
        #更新node节点
        update_topology_node_info(topology, fabric_topology.id)

        return jsonify({'status': 200, 'info': 'Create fabric topology successed.', 'data': fabric_topology.make_dict()})
    except Exception as e:
        LOG.error(traceback.format_exc())
        return jsonify({'status': 400, 'info': 'Create fabric topology failed.'})


@dc_blueprint_mold.route("/fabric_topo/view", methods=["POST"])
@admin_permission.require(http_exception=403)
def fabric_topo_view():
    try:
        data = request.get_json()
        if not data:
            return jsonify({'info': "Invalid request data", 'status': 400})

        fabric_id = data.get('fabric_id')
        topology = dc_fabric_db.get_fabric_topo_by_fabric_id(fabric_id)
        if topology:
            res = topology.make_dict()
        else:
            res = {}
        return jsonify({'status': 200, 'info': 'Get template successed.', "data": res})
    except Exception as e:
        LOG.error(traceback.format_exc())
        return jsonify({'status': 400, 'info': 'Get template failed.'})

