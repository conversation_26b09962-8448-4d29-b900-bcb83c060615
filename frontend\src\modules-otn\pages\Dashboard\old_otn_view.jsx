import React, {useEffect, useState} from "react";
import {Card, Col, Row, Space} from "antd";
import {getDashboardData} from "@/modules-otn/apis/api";
import {useSelector} from "react-redux";
import Icon from "@ant-design/icons";
import {criticalSvg, majorSvg, minorSvg, warningSvg} from "@/modules-otn/utils/iconSvg";
import {getOTNDeviceList} from "@/modules-ampcon/apis/otn";
import {PieEcharts, Histogramchart, Omschart, NeStatisticsEchart} from "./echarts_common";
import Alarm from "../otn/alarm/alarm";
import styles from "./old_otn_view.module.scss";

const OtnView = () => {
    // const {labelList} = useSelector(state => state.languageOTN);
    const {alarms} = useSelector(state => state.notification);
    // const currentYear = new Date().getFullYear();
    const [otnViewData, setOtnViewData] = useState();
    const [cardSeriesData, setCardSeriesData] = useState();
    const [neSeriesData, setNeSeriesData] = useState();
    const [businessSeriesData, setBusinessSeriesData] = useState();
    const ochSeriesData = [3, 5, 2, 3, 6, 1];
    const alarmRightImg = [criticalSvg, majorSvg, minorSvg, warningSvg];
    const alarmRightText = ["Critical", "Major", "Minor", "Warning"];
    const [alarmRightData, setAlarmRightData] = useState();
    useEffect(() => {
        const countProperty = (obj, property) => {
            return obj.reduce((count, item) => (item.severity === property ? count + 1 : count), 0);
        };
        const result = ["CRITICAL", "MAJOR", "MINOR", "WARNING"].map(property => countProperty(alarms, property));
        setAlarmRightData(result);
    }, [alarms]);
    useEffect(() => {
        setCardSeriesData(
            otnViewData?.card
                ? Object.entries(otnViewData?.card).map(item => {
                      return {
                          name: item[0],
                          value: item[1]
                      };
                  })
                : []
        );
        const seriesData = otnViewData?.ne
            ? Object.entries(otnViewData?.ne).map(item => {
                  if (item && item[0] !== "offLineStatistic") {
                      return {
                          name: item[0].charAt(0).toUpperCase() + item[0].slice(1).toLowerCase(),
                          value:
                              item[0] === "onLine"
                                  ? {
                                        value: item[1],
                                        itemStyle: {
                                            color: "#78D047"
                                        }
                                    }
                                  : {
                                        value: item[1],
                                        itemStyle: {
                                            color: "#C5CACD"
                                        }
                                    }
                      };
                  }
              })
            : [];
        setNeSeriesData(seriesData.filter(item => item !== undefined));
        setBusinessSeriesData(
            otnViewData?.serviceStatistic
                ? Object.entries(otnViewData?.serviceStatistic).map(item => {
                      return {
                          value: item[1],
                          name: `${item[0]}G`
                      };
                  })
                : []
        );
    }, [otnViewData]);
    useEffect(() => {
        const mergeStatus = (otnData, ampconOTNData) => {
            let onLineCount = otnData?.ne.onLine;
            let offLineCount = otnData?.ne.offLine;

            ampconOTNData?.neInfo?.forEach(item => {
                const runState = item?.value?.runState;
                if (runState === 1) {
                    onLineCount += 1;
                } else {
                    offLineCount += 1;
                }
            });

            return {
                ...otnData,
                ne: {
                    ...otnData?.ne,
                    onLine: onLineCount,
                    offLine: offLineCount
                }
            };
        };
        const fetchData = async () => {
            // getDashboardData().then(res => {
            //     setOtnViewData(res);
            // });
            const [dashboardData, ampconOTNData] = await Promise.all([getDashboardData(), getOTNDeviceList()]);
            const mergedData = mergeStatus(dashboardData, ampconOTNData);
            setOtnViewData(mergedData);
        };
        fetchData();
        const intervalId = setInterval(fetchData, 10000);
        return () => {
            clearInterval(intervalId);
        };
    }, []);
    return (
        <div className={styles.otnView}>
            <div className={styles.otnView_headerCard}>
                <Row gutter={24} justify="space-between" style={{height: "100%"}}>
                    <Col span={5}>
                        <Card
                            title={<div className={styles.otnView_custom_title}>OCH</div>}
                            bordered={false}
                            className={styles.otnView_container_height}
                        >
                            <div className={styles.otnView_header_cardBody}>
                                <div>
                                    <div className={styles.otnView_header_value}>{otnViewData?.service?.och}</div>
                                    <div className={styles.otnView_header_title}>Quantity</div>
                                </div>
                                <Histogramchart chartData={ochSeriesData} color="#14C9BB" />
                            </div>
                        </Card>
                    </Col>
                    <Col span={5}>
                        <Card
                            title={<div className={styles.otnView_custom_title}>OMS</div>}
                            bordered={false}
                            className={styles.otnView_container_height}
                        >
                            <div className={styles.otnView_header_cardBody}>
                                <div>
                                    <div className={styles.otnView_header_value}>{otnViewData?.service?.oms}</div>
                                    <div className={styles.otnView_header_title}>Quantity</div>
                                </div>
                                <Omschart />
                            </div>
                        </Card>
                    </Col>
                    <Col span={5}>
                        <Card
                            title={<div className={styles.otnView_custom_title}>OTS</div>}
                            bordered={false}
                            style={{height: "100%"}}
                        >
                            <div className={styles.otnView_header_cardBody}>
                                <div>
                                    <div className={styles.otnView_header_value}>{otnViewData?.service?.ots}</div>
                                    <div className={styles.otnView_header_title}>Quantity</div>
                                </div>
                                <div>
                                    <Histogramchart chartData={ochSeriesData} color="#FFBB00" />
                                </div>
                            </div>
                        </Card>
                    </Col>
                    <Col span={9}>
                        <Card
                            title={<div className={styles.otnView_custom_title}>Alarms</div>}
                            bordered={false}
                            style={{height: "100%"}}
                        >
                            <div className={styles.otnView_header_cardBody}>
                                {alarmRightData?.map((item, index) => (
                                    // eslint-disable-next-line react/no-array-index-key
                                    <div key={index}>
                                        <div className={styles.otnView_alarms_value}>
                                            <Icon component={alarmRightImg[index]} />
                                            <span className={styles.otnView_alarms_number}>{item}</span>
                                        </div>
                                        <div className={styles.otnView_header_title}>{alarmRightText[index]}</div>
                                    </div>
                                ))}
                            </div>
                        </Card>
                    </Col>
                </Row>
            </div>
            <div className={styles.otnView_echartCard}>
                <Row gutter={24} justify="space-between" style={{marginBottom: "20px", height: "100%"}}>
                    <Col span={8} className={styles.otnView_container_height}>
                        <Card title={<div className={styles.otnView_custom_title}>Card</div>} style={{height: "100%"}}>
                            <PieEcharts seriesData={cardSeriesData} seriesName="Card" />
                        </Card>
                    </Col>
                    <Col span={7} className={styles.otnView_container_height}>
                        <Card
                            title={<div className={styles.otnView_custom_title}>Business</div>}
                            style={{height: "100%"}}
                        >
                            <PieEcharts seriesData={businessSeriesData} seriesName="Business" />
                        </Card>
                    </Col>
                    <Col span={9} className={styles.otnView_container_height}>
                        <Card
                            title={<div className={styles.otnView_custom_title}>NE Statistics</div>}
                            extra={
                                <Space size={40}>
                                    <div className={styles.otnView_neRight}>
                                        <div className={styles.otnView_neOnlineCricle} />
                                        <span className={styles.otnView_onlineText}>Online</span>
                                        <span className={styles.otnView_sizeText}>{otnViewData?.ne?.onLine}</span>
                                    </div>
                                    <div className={styles.otnView_neRight}>
                                        <div className={styles.otnView_neOfflineCricle} />
                                        <span className={styles.otnView_onlineText}>Offline</span>
                                        <span className={styles.otnView_sizeText}>{otnViewData?.ne?.offLine}</span>
                                    </div>
                                </Space>
                            }
                            style={{
                                width: "100%",
                                height: "100%"
                            }}
                        >
                            <NeStatisticsEchart seriesData={neSeriesData} />
                        </Card>
                    </Col>
                </Row>
            </div>
            <div className={styles.otnView_alarmCard}>
                <Card title={<div className={styles.otnView_custom_title}>Alarm</div>} bordered={false}>
                    <div className={styles.otnView_alarmCard_alarmBody}>
                        <Alarm alarmType="currentAlarmStatistics" />
                    </div>
                </Card>
            </div>
        </div>
    );
};

export default OtnView;
