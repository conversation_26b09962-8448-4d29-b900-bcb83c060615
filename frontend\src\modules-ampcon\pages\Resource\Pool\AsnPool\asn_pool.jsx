import React, {useRef} from "react";
import {Button, Space, Card, Progress, Table, message} from "antd";
import {
    AmpConCustomTable,
    createColumnConfigMultipleParams,
    TableFilterDropdown
} from "@/modules-ampcon/components/custom_table";
import {PlusOutlined} from "@ant-design/icons";
import {confirmModalAction} from "@/modules-ampcon/components/custom_modal";
import {
    deleteResourcePoolAsnPool,
    deleteResourceRecord,
    generateResourceRecord,
    queryResourcePoolAnsTableData
} from "@/modules-ampcon/apis/resource_pool_api";
import CreatePoolModal from "@/modules-ampcon/pages/Resource/Pool/AsnPool/create_pool_modal";
import EditPoolModal from "@/modules-ampcon/pages/Resource/Pool/AsnPool/edit_pool_modal";
import CopyPoolModal from "@/modules-ampcon/pages/Resource/Pool/AsnPool/copy_pool_modal";

const AsnPool = () => {
    const createPoolModalRef = useRef(null);
    const editPoolModalRef = useRef(null);
    const copyPoolModalRef = useRef(null);
    const configSearchFieldsList = ["name"];
    const configMatchFieldsList = ["name"];

    const tableRef = useRef();

    const progressColors = {"0%": "#14c9bb", "100%": "#14c9bb"};

    const deleteASNPool = record => {
        deleteResourcePoolAsnPool({
            id: record.id
        }).then(response => {
            if (response.status === 200) {
                tableRef.current.refreshTable();
                message.success(response.info);
            } else {
                message.error(response.info);
            }
        });
    };

    const generateResourceRecordCallback = record => {
        generateResourceRecord({
            poolId: record.id,
            recordNum: 10
        }).then(response => {
            if (response.status === 200) {
                tableRef.current.refreshTable();
                message.success(response.info);
            } else {
                message.error(response.info);
            }
        });
    };

    const deleteResourceRecordCallback = record => {
        deleteResourceRecord({
            poolId: record.id,
            recordNum: 10
        }).then(response => {
            if (response.status === 200) {
                tableRef.current.refreshTable();
                message.success(response.info);
            } else {
                message.error(response.info);
            }
        });
    };

    const expandColumns = [
        {
            title: "ASN Range",
            width: "15%",
            render: (_, record) => {
                return (
                    <div>
                        {record.start_value} - {record.end_value}
                    </div>
                );
            }
        },
        createColumnConfigMultipleParams({
            title: "Used Asn Range Nums",
            dataIndex: "used_count",
            filterDropdownComponent: TableFilterDropdown,
            width: "16%"
        }),
        createColumnConfigMultipleParams({
            title: "Asn Range Nums",
            dataIndex: "ranges_count",
            filterDropdownComponent: TableFilterDropdown,
            width: "15%"
        }),
        {
            title: "ASN Range Usage",
            width: "21%",
            render: (_, record) => {
                return (
                    <div style={{marginLeft: "10px", marginRight: "30px"}}>
                        <Progress
                            percent={((record.used_count / record.ranges_count) * 100).toFixed(2)}
                            strokeColor={progressColors}
                            format={progressFormat}
                        />
                    </div>
                );
            }
        },
        {
            title: "Status",
            width: "15%",
            render: (_, record) => {
                if (record.is_in_use) {
                    return <div>Used</div>;
                }
                return <div>Not Used</div>;
            }
        },
        {
            title: "",
            width: "auto",
            render: () => <div style={{flexGrow: 1}} />
        }
    ];

    const configColumns = [
        createColumnConfigMultipleParams({
            title: "Pool Name",
            dataIndex: "name",
            filterDropdownComponent: TableFilterDropdown,
            width: "15%"
        }),
        createColumnConfigMultipleParams({
            title: "Used Asn Nums",
            dataIndex: "used_count",
            filterDropdownComponent: TableFilterDropdown,
            width: "15%"
        }),
        createColumnConfigMultipleParams({
            title: "Asn Nums",
            dataIndex: "ranges_count",
            filterDropdownComponent: TableFilterDropdown,
            width: "15%"
        }),
        {
            title: "Usage",
            width: "20%",
            render: (_, record) => {
                return (
                    <div style={{marginLeft: "10px", marginRight: "30px"}}>
                        <Progress
                            percent={((record.used_count / record.ranges_count) * 100).toFixed(2)}
                            strokeColor={progressColors}
                            format={progressFormat}
                        />
                    </div>
                );
            }
        },
        {
            title: "Status",
            width: "15%",
            render: (_, record) => {
                if (record.is_in_use) {
                    return <div>Used</div>;
                }
                return <div>Not Used</div>;
            }
        },
        {
            title: "Operation",
            render: (_, record) => {
                return (
                    <div>
                        <Space size="large" className="actionLink">
                            <a
                                onClick={() => {
                                    editPoolModalRef.current.showEditPoolModal(record);
                                }}
                            >
                                Edit
                            </a>
                            <a
                                onClick={() => {
                                    copyPoolModalRef.current.showCopyPoolModal(record);
                                }}
                            >
                                Copy
                            </a>
                            <a
                                className={record.is_in_use ? "disabled" : ""}
                                onClick={() =>
                                    confirmModalAction("Are you sure want to delete the ASN pool?", () => {
                                        deleteASNPool(record);
                                    })
                                }
                            >
                                Delete
                            </a>
                            <a
                                onClick={() => {
                                    generateResourceRecordCallback(record);
                                }}
                            >
                                Generate Ten Record
                            </a>
                            <a
                                onClick={() => {
                                    deleteResourceRecordCallback(record);
                                }}
                            >
                                Delete Ten Record
                            </a>
                        </Space>
                    </div>
                );
            }
        }
    ];

    const expandedRowDataFetch = record => {
        return record.ranges;
    };

    const expandedRowRender = record => {
        return (
            <div>
                <Table
                    style={{
                        paddingLeft: 33,
                        paddingRight: 30,
                        paddingBottom: 12,
                        paddingTop: 12,
                        backgroundColor: "#FFFFFF"
                    }}
                    columns={expandColumns}
                    searchFieldsList={configSearchFieldsList}
                    matchFieldsList={configMatchFieldsList}
                    dataSource={expandedRowDataFetch(record)}
                    bordered
                    pagination={false}
                    components={{
                        header: {
                            cell: props => (
                                <th
                                    {...props}
                                    style={{
                                        ...props.style,
                                        backgroundColor: "#FAFAFA" // 设置表头颜色
                                    }}
                                />
                            )
                        },
                        body: {
                            cell: props => (
                                <td
                                    {...props}
                                    style={{
                                        ...props.style,
                                        backgroundColor: "#FFFFFF" // 设置表格单元格背景色
                                    }}
                                />
                            )
                        }
                    }}
                />
            </div>
        );
    };

    const progressFormat = percent => {
        return <div style={{color: "black"}}>{`${percent}%`}</div>;
    };

    return (
        <Card style={{display: "flex", flex: 1}}>
            <h2 style={{marginTop: "8px", marginBottom: "30px"}}>ASN Pool</h2>
            <AmpConCustomTable
                columns={configColumns}
                searchFieldsList={configSearchFieldsList}
                matchFieldsList={configMatchFieldsList}
                fetchAPIInfo={queryResourcePoolAnsTableData}
                expandable={{expandedRowRender}}
                ref={tableRef}
                extraButton={
                    <Button
                        type="primary"
                        onClick={() => {
                            createPoolModalRef.current.showCreatePoolModal();
                        }}
                        icon={<PlusOutlined />}
                    >
                        ASN Pool
                    </Button>
                }
            />
            <CreatePoolModal
                ref={createPoolModalRef}
                saveCallback={() => {
                    tableRef.current.refreshTable();
                }}
            />
            <EditPoolModal
                ref={editPoolModalRef}
                saveCallback={() => {
                    tableRef.current.refreshTable();
                }}
            />
            <CopyPoolModal
                ref={copyPoolModalRef}
                saveCallback={() => {
                    tableRef.current.refreshTable();
                }}
            />
        </Card>
    );
};

export default AsnPool;
