import {Card, Empty} from "antd";
import {BasePieEcharts, Linechart} from "@/modules-ampcon/components/echarts_common";
import {useEffect, useRef, useState} from "react";
import {getAlarmCount} from "@/store/modules/common/alarm_slice";
import {useDispatch, useSelector} from "react-redux";
import {alarm1Svg, alarm3Svg, alarm4Svg} from "@/utils/common/iconSvg";
import Icon from "@ant-design/icons";
import {fetchStaticHistory, fetchImportedAndProvisioningSuccessSwitch} from "@/modules-ampcon/apis/dashboard_api";
import dayjs from "dayjs";
import {getUnreadAlarmList} from "@/modules-ampcon/apis/monitor_api";
import {ALARM_COLOR} from "@/modules-ampcon/utils/util";
import {AmpConCustomTable, createColumnConfig} from "@/modules-ampcon/components/custom_table";
import EmptyPic from "@/assets/images/App/empty.png";
import styles from "./global_view.module.scss";

const AmpconGlobalView = () => {
    const [statisticsData, setStatisticsData] = useState({
        serverCpuData: [],
        serverMenData: [],
        serverDiskData: [],
        loading: true
    });
    const [statisticsHistoryData, setStatisticsHistoryData] = useState({
        cpuUsageList: [],
        memUsageList: [],
        timeList: [],
        loading: true
    });
    const deviceColumns = [
        {
            title: "NE Name",
            render: (_, record) => {
                return `${record.host_name}:${record.sn}`;
            }
        },
        {
            ...createColumnConfig("State", "reachable_status"),
            render: (_, record) => {
                if (record.reachable_status === 0) {
                    return "online";
                }
                return "offline";
            }
        },
        createColumnConfig("Time", "modified_time"),
        createColumnConfig("Switch Model", "platform_model")
    ];
    const deviceMatchFieldsList = [{name: "ne_name"}];
    const [alarmSnMap, setAlarmMap] = useState({});
    const deviceRef = useRef(null);
    useEffect(() => {
        const fetchHistoryData = async () => {
            await fetchStaticHistory().then(res => {
                if (res.status === 200) {
                    setStatisticsHistoryData({
                        timeList: res.data.map(i => dayjs(i.create_time).format("HH:mm")).reverse(),
                        cpuUsageList: res.data.map(i => parseFloat(i.cpu)).reverse(),
                        memUsageList: res.data.map(i => parseFloat(i.mem)).reverse()
                    });

                    const cpu_count = parseFloat(parseFloat(res.data[0].cpu).toFixed(2));
                    const cpu_count_free = parseFloat(parseFloat(100 - cpu_count).toFixed(2));
                    const mem_count = parseFloat(parseFloat(res.data[0].mem).toFixed(2));
                    const mem_count_free = parseFloat(parseFloat(100 - mem_count).toFixed(2));
                    const disk_count = parseFloat(parseFloat(res.data[0].disk).toFixed(2));
                    const disk_count_free = parseFloat(parseFloat(100 - disk_count).toFixed(2));

                    setStatisticsData({
                        serverCpuData: [
                            {value: cpu_count, name: `Usage ${cpu_count}`},
                            {value: cpu_count_free, name: `Free ${cpu_count_free}`}
                        ],
                        serverMemData: [
                            {value: mem_count, name: `Usage ${mem_count}`},
                            {value: mem_count_free, name: `Free ${mem_count_free}`}
                        ],
                        serverDiskData: [
                            {value: disk_count, name: `Usage ${disk_count}`},
                            {value: disk_count_free, name: `Free ${disk_count_free}`}
                        ],
                        loading: false
                    });
                }
            });
            await fetchImportedAndProvisioningSuccessSwitch().then(res => {
                const alarmMap = {};
                res.data.map(item => {
                    alarmMap[item.sn] = item.host_name;
                });
                setAlarmMap(alarmMap);
            });
        };
        fetchHistoryData();

        const timer = setInterval(() => {
            fetchHistoryData().then();
            dispatch(getAlarmCount());
            deviceRef.current.refreshTable();
        }, 60000);

        return () => clearInterval(timer);
    }, []);
    const dispatch = useDispatch();
    const currentAlarm = useSelector(state => state.alarm.alarmInfo);
    const [alarmCount, setAlarmCount] = useState({});
    const [unreadAlarm, setUnreadAlarm] = useState([]);
    useEffect(() => {
        dispatch(getAlarmCount());
        setAlarmCount({
            CRITICAL: currentAlarm[0],
            WARNING: currentAlarm[2],
            INFO: currentAlarm[1]
        });
        getUnreadAlarmList().then(res => {
            if (res.status === 200) {
                setUnreadAlarm(res.unread_data);
            }
        });
    }, currentAlarm);
    const alarmIconMap = [alarm1Svg, alarm3Svg, alarm4Svg];
    const alarmRightText = ["Critical", "Warning", "Info"];
    return (
        <div className={styles.globalView}>
            <Card
                title={<div className={styles.globalView_custom_title}>CPU</div>}
                style={{height: "100%"}}
                loading={statisticsData.loading}
                className="chart-center"
            >
                <BasePieEcharts
                    name="CPU"
                    seriesData={statisticsData.serverCpuData}
                    chartType="ring"
                    colorList={["#FFBB00", "#14C9BB"]}
                    height="22vh"
                    maxWidth="300px"
                />
            </Card>

            <Card
                title={<div className={styles.globalView_custom_title}>MEM</div>}
                style={{height: "100%"}}
                loading={statisticsData.loading}
                className="chart-center"
            >
                <BasePieEcharts
                    name="MEM"
                    seriesData={statisticsData.serverMemData}
                    chartType="ring"
                    colorList={["#FFBB00", "#14C9BB"]}
                    height="22vh"
                    maxWidth="300px"
                />
            </Card>

            <Card
                title={<div className={styles.globalView_custom_title}>DISK</div>}
                style={{
                    height: "100%"
                }}
                loading={statisticsData.loading}
                className="chart-center"
            >
                <BasePieEcharts
                    name="DISK"
                    seriesData={statisticsData.serverDiskData}
                    chartType="ring"
                    colorList={["#FFBB00", "#14C9BB"]}
                    height="22vh"
                    maxWidth="300px"
                />
            </Card>
            <Card
                title={<div className={styles.globalView_custom_title}>Alarms</div>}
                bordered={false}
                style={{height: "100%"}}
            >
                <div className={styles.alarms}>
                    {Object.entries(alarmCount)?.map((item, index) => (
                        <div key={item}>
                            <div className={styles.alarms_alarms_value}>
                                <Icon component={alarmIconMap[index]} />
                                <span className={styles.alarms_alarms_number}>{item[1]}</span>
                            </div>
                            <div className={styles.alarms_title}>{alarmRightText[index]}</div>
                        </div>
                    ))}
                </div>
            </Card>
            <Card
                title={<div className={styles.globalView_custom_title}>Devices</div>}
                bordered={false}
                style={{
                    height: "100%",
                    width: "100%"
                }}
                className={styles.deviceCard}
            >
                <AmpConCustomTable
                    fetchAPIInfo={fetchImportedAndProvisioningSuccessSwitch}
                    columns={deviceColumns}
                    matchFieldsList={deviceMatchFieldsList}
                    ref={deviceRef}
                    isShowPagination
                />
            </Card>
            <Card
                title={<div className={styles.globalView_custom_title}>CPU Utilization</div>}
                bordered={false}
                style={{
                    height: "32vh"
                }}
                loading={statisticsHistoryData.loading}
                className="linechart"
            >
                <Linechart
                    title="CPU"
                    chartData={statisticsHistoryData.timeList}
                    chartXAxis={statisticsHistoryData.cpuUsageList}
                />
            </Card>
            <Card
                title={<div className={styles.globalView_custom_title}>Memory Utilization</div>}
                bordered={false}
                style={{
                    flex: 1,
                    height: "32vh"
                }}
                loading={statisticsHistoryData.loading}
                className="linechart"
            >
                <Linechart
                    title="MEM"
                    chartData={statisticsHistoryData.timeList}
                    chartXAxis={statisticsHistoryData.memUsageList}
                />
            </Card>
            <Card
                className={styles.globalView_recentAlarms}
                title={<div className={styles.globalView_custom_title}>Recent Alarms</div>}
                bordered={false}
                style={{
                    height: "32vh"
                }}
            >
                <div className={styles.globalView_recentAlarms_alarmBody}>
                    {unreadAlarm.length > 0 ? (
                        unreadAlarm
                            .sort((a, b) => (a.create_time < b.create_time ? 1 : -1))
                            .slice(0, 10)
                            .map((item, index) => {
                                return (
                                    <div key={index} className={styles.globalView_recentAlarms_alarmBody_alarmList}>
                                        <div
                                            className={styles.globalView_recentAlarms_alarmBody_alarmList_dot}
                                            style={{backgroundColor: ALARM_COLOR[item.type].color}}
                                        />
                                        <div className={styles.globalView_recentAlarms_alarmBody_alarmList_text}>
                                            NE={alarmSnMap[item.sn]}:{item.sn} Source=Switch Content=
                                            {item.msg.length > 20 ? `${item.msg.slice(0, 20)}...` : item.msg} Time=
                                            {item.create_time.replace(/-/g, "/")}
                                        </div>
                                    </div>
                                );
                            })
                    ) : (
                        <Empty image={EmptyPic} />
                    )}
                </div>
            </Card>
        </div>
    );
};

export default AmpconGlobalView;
