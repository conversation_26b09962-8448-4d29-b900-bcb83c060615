.cardHeight {
    display: flex;
    flex-grow: 1;
}
.titleSize1 {
    font-size: 1.15em;
    font-weight: bold;
    margin-top: 24px;
    font-size: 18px;
    width: 193px;
    height: 22px;
}
.titleSize2 {
    font-size: 18px;
    font-weight: bold;
    margin: 0px;
}
.cardStyle1 {
    margin-right: 24px;
}
.cardStyle2 {
    flex: 1;
}
// .custom-table-container .ant-table-thead > tr > th {
//     border-right: 1px solid #e8e8e8; /* 表头列之间的竖线 */
// }
// .custom-table-container .ant-table-thead > tr > th:first-child {
//     border-left: none; /* 第一个表头列左边框清除 */
// }
.buttonWidth {
    width: 129px;
    height: 36px;
}
.container {
    display: flex;
    height: 36px;
    align-items: center;
    justify-content: space-between; /* 使内容和按钮分别位于最左边和最右边 */
}
.buttonWidth {
    width: 90px;
}
.expiredTag {
    background-color: rgba(245, 63, 63, 0.1);
    color: rgb(245, 63, 63);
    border-color: rgb(245, 63, 63);
    font-size: 14px;
    // line-height: 32px;
    // width: 80px;
    text-align: center;
}

.avaliableTag {
    background-color: rgba(84, 196, 28, 0.1);
    color: #2BC174;
    border-color: #2BC174;
    font-size: 14px;
    text-align: center;
}

.invalidTag {
    background-color: rgba(253, 186, 0, 0.1);
    color: rgb(253, 186, 0);
    border-color: rgb(253, 186, 0);
    font-size: 14px;
    text-align: center;
}
.rightButton {
/* 按钮样式 */
    justify-content: end;
}
.divider1 {
    margin: 8px 0 24px 0;
}
.divider2 {
    margin: 12px 0 24px 0;
}