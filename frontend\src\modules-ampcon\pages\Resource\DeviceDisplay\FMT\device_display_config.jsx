export const SIDES = ["front", "rear"];

const CARDWIDTH = 575;
const INIT = 450;

export const LED_STATUS = Object.freeze({
    GREEN: 1,
    FLASHING: 2,
    RED: 3
});

const FMT_CHASSIS_CONFIG = {
    1: {
        side: SIDES[0],
        left: INIT,
        top: 4
    },
    2: {
        side: SIDES[0],
        left: INIT + CARDWIDTH,
        top: 4
    },
    3: {
        side: SIDES[0],
        left: INIT,
        top: 100
    },
    4: {
        side: SIDES[0],
        left: INIT + CARDWIDTH,
        top: 100
    }
};

const LSU_CONFIG = {
    "LED-POWER": {
        top: 33,
        left: 117.5
    },
    "LED-RUN": {
        top: 47,
        left: 117.5
    },
    PORT1: {
        top: 23,
        left: 286,
        transform: "rotate(-90deg)"
    },
    PORT2: {
        top: 43,
        left: 286,
        transform: "rotate(-90deg)"
    },
    PORT3: {
        top: 23,
        left: 312,
        transform: "rotate(90deg)"
    },
    PORT4: {
        top: 43,
        left: 312,
        transform: "rotate(90deg)"
    },
    PORT5: {
        top: 23,
        left: 346,
        transform: "rotate(-90deg)"
    },
    PORT6: {
        top: 43,
        left: 346,
        transform: "rotate(-90deg)"
    },
    PORT7: {
        top: 23,
        left: 374,
        transform: "rotate(90deg)"
    },
    PORT8: {
        top: 43,
        left: 374,
        transform: "rotate(90deg)"
    },
    PORT9: {
        top: 23,
        left: 408,
        transform: "rotate(-90deg)"
    },
    PORT10: {
        top: 43,
        left: 408,
        transform: "rotate(-90deg)"
    },
    PORT11: {
        top: 23,
        left: 435,
        transform: "rotate(90deg)"
    },
    PORT12: {
        top: 43,
        left: 435,
        transform: "rotate(90deg)"
    },
    PORT13: {
        top: 23,
        left: 469,
        transform: "rotate(-90deg)"
    },
    PORT14: {
        top: 43,
        left: 469,
        transform: "rotate(-90deg)"
    },
    PORT15: {
        top: 23,
        left: 498,
        transform: "rotate(90deg)"
    },
    PORT16: {
        top: 43,
        left: 498,
        transform: "rotate(90deg)"
    }
};

const EDFA_CONFIG = {
    "LED-POWER": {
        top: 33,
        left: 117.5,
        status: {
            key: "businessInfo.query.State.input_power",
            judge: data => (data ? LED_STATUS.GREEN : null)
        }
    },
    "LED-RUN": {
        top: 51,
        left: 117.5,
        status: {
            key: "businessInfo.query.State.input_power",
            judge: data => (data ? LED_STATUS.FLASHING : null)
        }
    },
    "LED-IN": {
        top: 33,
        left: 165.5,
        status: {
            key: "businessInfo.query.State.pin_alarm",
            judge: data => (data === "1" ? LED_STATUS.FLASHING : LED_STATUS.RED)
        }
    },
    "LED-OUT": {
        top: 51,
        left: 165.5,
        status: {
            key: "businessInfo.query.State.pout_alarm",
            judge: data => (data === "1" ? LED_STATUS.FLASHING : LED_STATUS.RED)
        }
    },
    "LED-MT": {
        top: 33,
        left: 206.5,
        status: {
            key: "businessInfo.query.State.mt_alarm",
            judge: data => (data === "1" ? LED_STATUS.FLASHING : LED_STATUS.RED)
        }
    },
    "LED-PT": {
        top: 51,
        left: 206.5,
        status: {
            key: "businessInfo.query.State.pt_alarm",
            judge: data => (data === "1" ? LED_STATUS.FLASHING : LED_STATUS.RED)
        }
    },
    "PORT-IN": {
        top: 38,
        left: 290
    },
    "PORT-OUT": {
        top: 38,
        left: 310
    }
};

const DEDFA_CONFIG = {
    "LED-POWER": {
        top: 33,
        left: 117.5,
        status: {
            key: "businessInfo.query.State.input_power",
            judge: data => (data ? LED_STATUS.GREEN : null)
        }
    },
    "LED-RUN": {
        top: 51,
        left: 117.5,
        status: {
            key: "businessInfo.query.State.input_power",
            judge: data => (data ? LED_STATUS.FLASHING : null)
        }
    },
    "LED-IN": {
        top: 33,
        left: 165.5,
        status: {
            key: "businessInfo.query.State.pin_alarm",
            judge: data => (data === "1" ? LED_STATUS.FLASHING : LED_STATUS.RED)
        }
    },
    "LED-OUT": {
        top: 51,
        left: 165.5,
        status: {
            key: "businessInfo.query.State.pout_alarm",
            judge: data => (data === "1" ? LED_STATUS.FLASHING : LED_STATUS.RED)
        }
    },
    "LED-MT": {
        top: 33,
        left: 206.5,
        status: {
            key: "businessInfo.query.State.mt_alarm",
            judge: data => (data === "1" ? LED_STATUS.FLASHING : LED_STATUS.RED)
        }
    },
    "LED-PT": {
        top: 51,
        left: 206.5,
        status: {
            key: "businessInfo.query.State.pt_alarm",
            judge: data => (data === "1" ? LED_STATUS.FLASHING : LED_STATUS.RED)
        }
    },
    PORT1: {
        top: 38,
        left: 290
    },
    PORT2: {
        top: 38,
        left: 310
    },
    PORT3: {
        top: 38,
        left: 348
    },
    PORT4: {
        top: 38,
        left: 368
    }
};

const SOA_CONFIG = {
    "LED-POWER": {
        top: 33,
        left: 117.5
    },
    "LED-RUN": {
        top: 51,
        left: 117.5
    },
    "LED-IN": {
        top: 33,
        left: 165.5
    },
    "LED-OUT": {
        top: 51,
        left: 165.5
    },
    "LED-MT": {
        top: 33,
        left: 206.5
    },
    "LED-PT": {
        top: 51,
        left: 206.5
    },
    PORT1: {
        top: 38,
        left: 290
    },
    PORT2: {
        top: 38,
        left: 310
    },
    PORT3: {
        top: 38,
        left: 348
    },
    PORT4: {
        top: 38,
        left: 368
    }
};

const TDCM_CONFIG = {
    "LED-POWER": {
        top: 33,
        left: 117.5,
        status: {
            key: "Module temperature",
            judge: data => (data ? LED_STATUS.GREEN : null)
        }
    },
    "LED-RUN": {
        top: 51,
        left: 117.5,
        status: {
            key: "Module temperature",
            judge: data => (data ? LED_STATUS.FLASHING : null)
        }
    },
    PORT1: {
        top: 38,
        left: 290
    },
    PORT2: {
        top: 38,
        left: 310
    }
};

const FMT_OTDR_CONFIG = {
    "LED-POWER": {
        top: 33,
        left: 102.5
    },
    "LED-RUN": {
        top: 51,
        left: 102.5
    },
    "LED-MEASURE": {
        top: 69,
        left: 102.5
    },
    PORT1: {
        top: 38,
        left: 310
    },
    PORT2: {
        top: 38,
        left: 330
    }
};

const OPD_CONFIG = {
    "LED-POWER": {
        top: 33,
        left: 115,
        status: {
            key: "businessInfo.query",
            judge: data => (data ? LED_STATUS.GREEN : null)
        }
    },
    "LED-RUN": {
        top: 48,
        left: 115,
        status: {
            key: "businessInfo.query",
            judge: data => (data ? LED_STATUS.FLASHING : null)
        }
    },
    PORT1: {
        top: 23,
        left: 287,
        transform: "rotate(-90deg)"
    },
    PORT2: {
        top: 23,
        left: 312,
        transform: "rotate(90deg)"
    },
    PORT3: {
        top: 23,
        left: 347,
        transform: "rotate(-90deg)"
    },
    PORT4: {
        top: 23,
        left: 374,
        transform: "rotate(90deg)"
    },
    PORT5: {
        top: 23,
        left: 408,
        transform: "rotate(-90deg)"
    },
    PORT6: {
        top: 23,
        left: 435,
        transform: "rotate(90deg)"
    },
    PORT7: {
        top: 23,
        left: 469,
        transform: "rotate(-90deg)"
    },
    PORT8: {
        top: 23,
        left: 498,
        transform: "rotate(90deg)"
    },
    PORT9: {
        top: 43,
        left: 287,
        transform: "rotate(-90deg)"
    },
    PORT10: {
        top: 43,
        left: 313,
        transform: "rotate(90deg)"
    },
    PORT11: {
        top: 43,
        left: 346,
        transform: "rotate(-90deg)"
    },
    PORT12: {
        top: 43,
        left: 374,
        transform: "rotate(90deg)"
    },
    PORT13: {
        top: 43,
        left: 408,
        transform: "rotate(-90deg)"
    },
    PORT14: {
        top: 43,
        left: 435,
        transform: "rotate(90deg)"
    },
    PORT15: {
        top: 43,
        left: 469,
        transform: "rotate(-90deg)"
    },
    PORT16: {
        top: 43,
        left: 498,
        transform: "rotate(90deg)"
    }
};

const FMT_BDOLP_CONFIG = {
    "LED-POWER": {
        top: 34,
        left: 92
    },
    "LED-Auto": {
        top: 34,
        left: 130
    },
    "LED-Prl": {
        top: 34,
        left: 167
    },
    "LED-RUN": {
        top: 51,
        left: 92
    },
    "LED-R1": {
        top: 51,
        left: 130
    },
    "LED-Tx": {
        top: 51,
        left: 167
    },
    "LED-ALM": {
        top: 68,
        left: 92
    },
    "LED-R2": {
        top: 68,
        left: 130
    },
    "LED-LS": {
        top: 68,
        left: 167
    },
    PORT1: {
        top: 38,
        left: 266
    },
    PORT2: {
        top: 38,
        left: 286
    },
    PORT3: {
        top: 38,
        left: 324
    },
    PORT4: {
        top: 38,
        left: 344
    },
    PORT5: {
        top: 38,
        left: 382
    },
    PORT6: {
        top: 38,
        left: 402
    }
};

const OLP_CONFIG = {
    "LED-POWER": {
        top: 34,
        left: 89,
        status: {
            key: "businessInfo.config.Alarm Limit.r1",
            judge: data => (data ? LED_STATUS.GREEN : null)
        }
    },
    "LED-Auto": {
        top: 34,
        left: 130,
        status: {
            key: "businessInfo.config.Working Parameter.work_route",
            judge: data => (data === "1" ? LED_STATUS.GREEN : null)
        }
    },
    "LED-Prl": {
        top: 34,
        left: 167,
        status: {
            key: "businessInfo.query.Power.r1",
            threshold: "businessInfo.config.Switch Parameter.r1_switch_limit",
            judge: (data, threshold) => {
                const powerValue = Number(data);
                const switchValue = Number(threshold);
                if (powerValue < switchValue) {
                    return null;
                }
                return LED_STATUS.GREEN;
            }
        }
    },
    "LED-RUN": {
        top: 51,
        left: 89,
        status: {
            key: "businessInfo.config.Alarm Limit.r1",
            judge: data => (data ? LED_STATUS.FLASHING : null)
        }
    },
    "LED-R1": {
        top: 51,
        left: 130,
        status: {
            key: "businessInfo.query.Power.alarm",
            judge: data => {
                const statusMap = {
                    0: null,
                    1: LED_STATUS.GREEN,
                    2: LED_STATUS.FLASHING
                };
                return statusMap[(data && data[1]) || "0"];
            }
        }
    },
    "LED-Tx": {
        top: 51,
        left: 167,
        status: {
            key: "businessInfo.query.Power.alarm",
            judge: data => {
                const statusMap = {
                    0: null,
                    1: LED_STATUS.GREEN
                };
                return statusMap[(data && data[3]) || "0"];
            }
        }
    },
    "LED-ALM": {
        top: 68,
        left: 89,
        status: {
            key: "businessInfo.query.Power.alarm",
            judge: data => {
                const statusMap = {
                    0: null,
                    1: LED_STATUS.RED
                };
                return statusMap[(data && data[0]) || "0"];
            }
        }
    },
    "LED-R2": {
        top: 68,
        left: 130,
        status: {
            key: "businessInfo.query.Power.alarm",
            judge: data => {
                const statusMap = {
                    0: null,
                    1: LED_STATUS.GREEN,
                    2: LED_STATUS.FLASHING
                };
                return statusMap[(data && data[2]) || "0"];
            }
        }
    },
    "LED-LS": {
        top: 68,
        left: 167,
        status: {
            key: "businessInfo.query.Power.alarm",
            judge: data => {
                const statusMap = {
                    0: null,
                    1: LED_STATUS.GREEN
                };
                return statusMap[(data && data[4]) || "0"];
            }
        }
    },
    PORT1: {
        top: 38,
        left: 267
    },
    PORT2: {
        top: 38,
        left: 287
    },
    PORT3: {
        top: 38,
        left: 326
    },
    PORT4: {
        top: 38,
        left: 345
    },
    PORT5: {
        top: 38,
        left: 382
    },
    PORT6: {
        top: 38,
        left: 402
    }
};

const VOA_CONFIG = {
    "LED-POWER": {
        top: 34,
        left: 91.5,
        status: {
            key: "businessInfo.config.voa2_threshold",
            judge: data => (data ? LED_STATUS.GREEN : null)
        }
    },
    "LED-RUN": {
        top: 34,
        left: 139.5,
        status: {
            key: "businessInfo.config.voa2_threshold",
            judge: data => (data ? LED_STATUS.FLASHING : null)
        }
    },
    "LED-OUT1": {
        top: 51,
        left: 91.5,
        status: {
            key: "businessInfo.query.voa1_power",
            threshold: "businessInfo.config.voa1_threshold",
            judge: (key, threshold) => {
                return parseFloat(key) >= parseFloat(threshold) ? LED_STATUS.GREEN : null;
            }
        }
    },
    "LED-OUT2": {
        top: 51,
        left: 139.5,
        status: {
            key: "businessInfo.query.voa2_power",
            threshold: "businessInfo.config.voa2_threshold",
            judge: (key, threshold) => {
                return parseFloat(key) >= parseFloat(threshold) ? LED_STATUS.GREEN : null;
            }
        }
    },
    "LED-OUT3": {
        top: 68,
        left: 91.5,
        status: {
            key: "businessInfo.query.voa3_power",
            threshold: "businessInfo.config.voa3_threshold",
            judge: (key, threshold) => {
                return parseFloat(key) >= parseFloat(threshold) ? LED_STATUS.GREEN : null;
            }
        }
    },
    "LED-OUT4": {
        top: 68,
        left: 139.5,
        status: {
            key: "businessInfo.query.voa4_power",
            threshold: "businessInfo.config.voa4_threshold",
            judge: (key, threshold) => {
                return parseFloat(key) >= parseFloat(threshold) ? LED_STATUS.GREEN : null;
            }
        }
    },
    PORT1: {
        top: 38,
        left: 268
    },
    PORT2: {
        top: 38,
        left: 286
    },
    PORT3: {
        top: 38,
        left: 331
    },
    PORT4: {
        top: 38,
        left: 349
    },
    PORT5: {
        top: 38,
        left: 394
    },
    PORT6: {
        top: 38,
        left: 414
    },
    PORT7: {
        top: 38,
        left: 457
    },
    PORT8: {
        top: 38,
        left: 478
    }
};

const FMT_BDSplitter_CONFIG = {
    PORT1: {
        top: 38,
        left: 266
    },
    PORT2: {
        top: 38,
        left: 286
    },
    PORT3: {
        top: 38,
        left: 324
    },
    PORT4: {
        top: 38,
        left: 344
    },
    PORT5: {
        top: 38,
        left: 382
    },
    PORT6: {
        top: 38,
        left: 402
    }
};

const FSBR_1x2_CONFIG = {
    PORT1: {
        top: 38,
        left: 353
    },
    PORT2: {
        top: 38,
        left: 373
    },
    PORT3: {
        top: 38,
        left: 411
    },
    PORT4: {
        top: 38,
        left: 430
    },
    PORT5: {
        top: 38,
        left: 468
    },
    PORT6: {
        top: 38,
        left: 488
    }
};

const HPA_CONFIG = {
    "LED-POWER": {
        top: 123,
        left: 114.5
    },
    "LED-RUN": {
        top: 140,
        left: 114.5
    },
    "LED-IN": {
        top: 123,
        left: 162
    },
    "LED-OUT": {
        top: 140,
        left: 162
    },
    "LED-MT": {
        top: 123,
        left: 209
    },
    "LED-PT": {
        top: 140,
        left: 209
    },
    PORT1: {
        top: 124,
        left: 263
    },
    PORT2: {
        top: 124,
        left: 284
    },
    PORT3: {
        top: 124,
        left: 345
    },
    PORT4: {
        top: 124,
        left: 363
    },
    PORT5: {
        top: 124,
        left: 384
    },
    PORT6: {
        top: 124,
        left: 403
    },
    PORT7: {
        top: 124,
        left: 440
    },
    PORT8: {
        top: 124,
        left: 459
    },
    PORT9: {
        top: 124,
        left: 479
    },
    PORT10: {
        top: 124,
        left: 500
    }
};

const FMTOSW_CONFIG = {
    "LED-POWER": {
        top: 122,
        left: 116.5
    },
    "LED-RUN": {
        top: 139,
        left: 116.5
    },
    "LED-MODE": {
        top: 157,
        left: 116.5
    },
    "PORT-NA": {
        top: 29,
        left: 226.5
    },
    "PORT-COM": {
        top: 29,
        left: 246.5
    },
    PORT1: {
        top: 23,
        left: 289,
        transform: "rotate(-90deg)"
    },
    PORT2: {
        top: 43,
        left: 289,
        transform: "rotate(-90deg)"
    },
    PORT3: {
        top: 62.5,
        left: 289,
        transform: "rotate(-90deg)"
    },
    PORT4: {
        top: 82,
        left: 289,
        transform: "rotate(-90deg)"
    },
    PORT5: {
        top: 23,
        left: 314,
        transform: "rotate(90deg)"
    },
    PORT6: {
        top: 43,
        left: 314,
        transform: "rotate(90deg)"
    },
    PORT7: {
        top: 62.5,
        left: 314,
        transform: "rotate(90deg)"
    },
    PORT8: {
        top: 82,
        left: 314,
        transform: "rotate(90deg)"
    },
    PORT9: {
        top: 23,
        left: 350,
        transform: "rotate(-90deg)"
    },
    PORT10: {
        top: 43,
        left: 350,
        transform: "rotate(-90deg)"
    },
    PORT11: {
        top: 62.5,
        left: 350,
        transform: "rotate(-90deg)"
    },
    PORT12: {
        top: 82,
        left: 350,
        transform: "rotate(-90deg)"
    },
    PORT13: {
        top: 23,
        left: 375,
        transform: "rotate(90deg)"
    },
    PORT14: {
        top: 43,
        left: 375,
        transform: "rotate(90deg)"
    },
    PORT15: {
        top: 62.5,
        left: 375,
        transform: "rotate(90deg)"
    },
    PORT16: {
        top: 82,
        left: 375,
        transform: "rotate(90deg)"
    },
    PORT17: {
        top: 23,
        left: 411,
        transform: "rotate(-90deg)"
    },
    PORT18: {
        top: 43,
        left: 411,
        transform: "rotate(-90deg)"
    },
    PORT19: {
        top: 62.5,
        left: 411,
        transform: "rotate(-90deg)"
    },
    PORT20: {
        top: 82,
        left: 411,
        transform: "rotate(-90deg)"
    },
    PORT21: {
        top: 23,
        left: 437,
        transform: "rotate(90deg)"
    },
    PORT22: {
        top: 43,
        left: 437,
        transform: "rotate(90deg)"
    },
    PORT23: {
        top: 62.5,
        left: 437,
        transform: "rotate(90deg)"
    },
    PORT24: {
        top: 82,
        left: 437,
        transform: "rotate(90deg)"
    },
    PORT25: {
        top: 23,
        left: 472,
        transform: "rotate(-90deg)"
    },
    PORT26: {
        top: 43,
        left: 472,
        transform: "rotate(-90deg)"
    },
    PORT27: {
        top: 62.5,
        left: 472,
        transform: "rotate(-90deg)"
    },
    PORT28: {
        top: 82,
        left: 472,
        transform: "rotate(-90deg)"
    },
    PORT29: {
        top: 23,
        left: 500,
        transform: "rotate(90deg)"
    },
    PORT30: {
        top: 43,
        left: 500,
        transform: "rotate(90deg)"
    },
    PORT31: {
        top: 62.5,
        left: 500,
        transform: "rotate(90deg)"
    },
    PORT32: {
        top: 82,
        left: 500,
        transform: "rotate(90deg)"
    }
};

const DCM_CONFIG = {
    "PORT-IN": {
        top: 132,
        left: 398
    },
    "PORT-OUT": {
        top: 132,
        left: 418
    }
};

const FMT_OPM_CONFIG = {
    "LED-POWER": {
        top: 122,
        left: 117
    },
    "LED-RUN": {
        top: 139,
        left: 117
    },
    "LED-Measure": {
        top: 157,
        left: 117
    },
    "PORT-IN": {
        top: 132,
        left: 398
    },
    "PORT-NA": {
        top: 132,
        left: 418
    }
};

const OEO_CONFIG = {
    "LED-POWER": {
        top: 28,
        left: 117.5,
        status: {
            key: "businessInfo.query.input_power",
            judge: data => (data ? LED_STATUS.GREEN : null)
        }
    },
    "LED-RUN": {
        top: 47.5,
        left: 117.5,
        status: {
            key: "businessInfo.query.input_power",
            judge: data => (data ? LED_STATUS.FLASHING : null)
        }
    },
    "PORT-A1": {
        top: 45,
        left: 158.5,
        status: {
            key: "businessInfo.query.module_temperature[0]",
            judge: data => parseFloat(data) > 0
        }
    },
    "PORT-A2": {
        top: 45,
        left: 201,
        status: {
            key: "businessInfo.query.module_temperature[1]",
            judge: data => parseFloat(data) > 0
        }
    },
    "PORT-B1": {
        top: 45,
        left: 244.5,
        status: {
            key: "businessInfo.query.module_temperature[2]",
            judge: data => parseFloat(data) > 0
        }
    },
    "PORT-B2": {
        top: 45,
        left: 287,
        status: {
            key: "businessInfo.query.module_temperature[3]",
            judge: data => parseFloat(data) > 0
        }
    },
    "PORT-C1": {
        top: 45,
        left: 345,
        status: {
            key: "businessInfo.query.module_temperature[4]",
            judge: data => parseFloat(data) > 0
        }
    },
    "PORT-C2": {
        top: 45,
        left: 386.5,
        status: {
            key: "businessInfo.query.module_temperature[5]",
            judge: data => parseFloat(data) > 0
        }
    },
    "PORT-D1": {
        top: 45,
        left: 430,
        status: {
            key: "businessInfo.query.module_temperature[6]",
            judge: data => parseFloat(data) > 0
        }
    },
    "PORT-D2": {
        top: 45,
        left: 473,
        status: {
            key: "businessInfo.query.module_temperature[7]",
            judge: data => parseFloat(data) > 0
        }
    }
};

const FMT_NMU_CONFIG = {
    "LED-P1": {
        top: 32,
        left: 109,
        status: {
            key: "PWR",
            judge: data => (data[0] === "1" ? LED_STATUS.GREEN : null)
        }
    },
    "LED-P2": {
        top: 32,
        left: 131,
        status: {
            key: "PWR",
            judge: data => (data[1] === "1" ? LED_STATUS.GREEN : null)
        }
    },
    "LED-RUN": {
        top: 32,
        left: 153,
        status: {
            key: "PWR",
            judge: data => (data !== "00" ? LED_STATUS.FLASHING : null)
        }
    },
    "LED-PWR": {
        top: 39,
        left: 1610,
        status: {
            key: "FNC",
            judge: data => (data === "1" ? LED_STATUS.GREEN : null)
        }
    },
    "LED-STATE": {
        top: 57,
        left: 1610,
        status: {
            key: "FNS",
            judge: data => (data === "1" ? LED_STATUS.GREEN : null)
        }
    }
};

export const chassisConfig = {
    FMT: FMT_CHASSIS_CONFIG,
    NMU: FMT_NMU_CONFIG,
    LSU: LSU_CONFIG,
    EDFA: EDFA_CONFIG,
    DEDFA: DEDFA_CONFIG,
    SOA: SOA_CONFIG,
    TDCM: TDCM_CONFIG,
    FMT_OTDR: FMT_OTDR_CONFIG,
    OPD: OPD_CONFIG,
    FMT_BDOLP: FMT_BDOLP_CONFIG,
    OLP: OLP_CONFIG,
    VOA: VOA_CONFIG,
    FMT_BDSplitter: FMT_BDSplitter_CONFIG,
    FSBR_1x2: FSBR_1x2_CONFIG,
    HPA: HPA_CONFIG,
    FMTOSW: FMTOSW_CONFIG,
    DCM: DCM_CONFIG,
    FMT_OPM: FMT_OPM_CONFIG,
    OEO: OEO_CONFIG
};
