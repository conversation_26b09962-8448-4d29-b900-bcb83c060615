import React, {useEffect, useState} from "react";
import Icon, {MenuUnfoldOutlined, MenuFoldOutlined} from "@ant-design/icons";
import {Layout, Menu, Button, theme, Breadcrumb, Space, Dropdown, Modal, Divider} from "antd";
import {Outlet, useLocation, useNavigate} from "react-router-dom";
import {useDispatch, useSelector} from "react-redux";
import {fetchLogout} from "@/store/modules/common/user_slice";
import {getAlarmCount, updateAlarmSearch, updateAlarmSearchStatus} from "@/store/modules/common/alarm_slice";

import {useForm} from "antd/es/form/Form";
import {UserEditModalForm} from "@/modules-ampcon/pages/System/user_modal";

import {
    collapsedLogoSvg,
    downSvg,
    helpSvg,
    upSvg,
    userSvg,
    alarm1Svg,
    alarm3Svg,
    alarm4Svg
} from "@/utils/common/iconSvg";
import {removeCssStyleByCssSelector, ALARM_COLOR} from "@/modules-ampcon/utils/util";
import {customTitle, sidebarItems, sideSvg} from "@/custom_modules";
import styles from "./layout.module.scss";

const {Header, Sider, Content} = Layout;

const FSAmpConLayout = () => {
    const currentUser = useSelector(state => state.user.userInfo);
    const userType = currentUser?.type;
    const userRole = currentUser?.role;
    const items = sidebarItems[userType] ? sidebarItems[userType] : sidebarItems.readonly;
    const [siderWidth, setSiderWidth] = useState(200);

    const {
        token: {colorPrimary}
    } = theme.useToken();

    const navigate = useNavigate();
    const location = useLocation();
    const dispatch = useDispatch();
    const [openKeys, setOpenKeys] = useState([]);

    const fourthLevelDefaultTAB = {
        ansible_jobs_list: "job_view",
        config_template: userType === "readonly" ? "template_verify" : "new_template",
        // upgrade_management: "otn",
        l0_config: "optical-power-management",
        device_license_management: "license_audit",
        e2e_service_config: "create-business",
        performance: "current",
        performance_subscription: "sensor_group",
        time_management: "time_management",
        link_measure: "otdr"
        // alarm: "current_alarm"
    };

    const onOpenChange = newOpenKeys => {
        // If only one menu item is allowed to expand, only the key of the last menu item is reserved
        const lastKey = newOpenKeys[newOpenKeys.length - 1];
        if (lastKey === undefined) {
            setOpenKeys(undefined);
        } else {
            const first_part_regex = `/${lastKey.match(/^\/(.+?)(?:\/|$)/)[1]}`;
            if (first_part_regex === newOpenKeys[0]) {
                setOpenKeys(newOpenKeys);
            } else if (first_part_regex !== lastKey) {
                setOpenKeys(undefined);
            } else {
                setOpenKeys([lastKey]);
            }
        }
    };

    const onMenuClick = item => {
        const path = fourthLevelDefaultTAB[item.key.split("/").pop()];
        if (path) {
            navigate(`${item.key}/${path}`);
        } else {
            if (item.key === "/monitor/alarm") {
                dispatch(updateAlarmSearch(""));
                dispatch(updateAlarmSearchStatus(false));
            }
            navigate(item.key);
        }
        if (item.keyPath.length === 1) {
            setOpenKeys([item.key]);
        }
    };
    const currentVersion = useSelector(state => state.version.currentVersionInfo);
    const [collapsed, setCollapsed] = useState(false);
    const [isVersionOpen, setIsVersionOpen] = useState(false);

    // findOpenKeys =>  get parent path all list
    // findSelectedKey => get 4th parent level path single list
    const findOpenKeys = currentPath => {
        const keys = [];
        const pathArray = currentPath.split("/").filter(Boolean);
        let currentKey = "";
        for (let i = 0; i < pathArray.length - 1; i++) {
            currentKey += `/${pathArray[i]}`;
            keys.push(currentKey);
        }
        return keys;
    };

    const findSelectedKey = () => {
        const pathName = location.pathname;
        if (pathName.startsWith("/service/switch/")) {
            return "/service/switch"; // 返回静态路由
        }
        if (Object.keys(fourthLevelDefaultTAB).some(key => pathName.split("/").includes(key))) {
            return findOpenKeys(pathName).slice(-1);
        }
        return pathName;
    };

    useEffect(() => {
        // if the sidebar is collapsed, open the parent path, otherwise will do nothing
        if (!collapsed) {
            setOpenKeys(findOpenKeys(location.pathname));
        }
    }, [location.pathname, colorPrimary]);

    const getBreadcrumb = () => {
        const uppercaseWords = ["otn", "e2e", "wss", "tff", "pmp", "otdr", "ocm", "ntp", "cli", "dlb"];

        const pathItems = location.pathname
            .split("/")
            .filter(item => item !== "")
            .map(item => {
                let title = item.replace(/[_-]/g, " ");
                title = title.replace(/\b\w/g, c => c.toUpperCase());

                uppercaseWords.forEach(word => {
                    if (title.toLowerCase().includes(word)) {
                        title = title.replace(new RegExp(word, "gi"), word.toUpperCase());
                    }
                });

                return {title};
            });
        if (pathItems.length === 0) {
            pathItems.push({title: "Dashboard"});
        }
        return pathItems;
    };

    const logoutConfirm = () => {
        dispatch(fetchLogout());
    };

    const toHome = () => {
        navigate("/");
    };

    const userItems = [
        userRole === "local" && {
            label: <a>User Management</a>,
            key: "item-2",
            onClick: () => {
                setIsModalOpen(true);
                form.setFieldValue("username", currentUser.username);
            }
        },
        {
            label: <a>Version</a>,
            key: "item-1",
            onClick: () => {
                setIsVersionOpen(true);
            }
        },
        {
            label: <a>Logout</a>,
            key: "item-3",
            onClick: logoutConfirm
        }
    ];

    const [form] = useForm();
    const [isModalOpen, setIsModalOpen] = useState(false);
    const [hoverStatus, setHoverStatus] = useState(false);
    const currentAlarm = useSelector(state => state.alarm.alarmInfo);
    const [alarmCount, setAlarmCount] = useState({});

    useEffect(() => {
        dispatch(getAlarmCount());
        setAlarmCount({
            error: currentAlarm[0],
            info: currentAlarm[1],
            warn: currentAlarm[2]
        });
    }, currentAlarm);

    useEffect(() => {
        const siderLogoElement = document.querySelector("svg");
        if (siderLogoElement) {
            const siderLogoWidth = siderLogoElement.getBoundingClientRect().width;
            if (siderLogoWidth > 200) {
                setSiderWidth(siderLogoWidth + 14);
            } else {
                setSiderWidth(200);
            }
        }
    }, []);

    const alarmIconMap = {
        error: alarm1Svg,
        info: alarm4Svg,
        warn: alarm3Svg
    };

    return (
        <Layout className={styles.layoutStyle}>
            <Modal title={currentVersion} open={isVersionOpen} onCancel={() => setIsVersionOpen(false)} footer={null}>
                <>
                    <Divider />
                    <ul>
                        <li className="actionLink">
                            <a>Release notes for {customTitle}</a>
                        </li>
                    </ul>
                </>
            </Modal>

            <UserEditModalForm
                title="User Account Settings"
                isModalOpen={isModalOpen}
                setIsModalOpen={setIsModalOpen}
                formInstance={form}
                layoutProps={{
                    labelCol: {
                        span: 8
                    },
                    wrapperCol: {
                        span: 19
                    }
                }}
            />
            <Sider collapsed={collapsed} className={styles.themeBackground} width={siderWidth}>
                <div className={styles.logoContainer}>
                    {collapsed ? (
                        <Icon component={collapsedLogoSvg} />
                    ) : (
                        <Icon component={sideSvg} onClick={() => toHome()} />
                    )}
                </div>
                <Menu
                    mode="inline"
                    items={items}
                    className={styles.themeBackground}
                    onClick={onMenuClick}
                    openKeys={openKeys}
                    // onOpenChange={keys => setOpenKeys(keys)}
                    onOpenChange={onOpenChange}
                    selectedKeys={findSelectedKey()}
                />
            </Sider>
            <Layout>
                <Header className={styles.layoutHeader}>
                    <div className={styles.breadCrumb}>
                        <Button
                            type="text"
                            icon={collapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}
                            onClick={() => {
                                if (collapsed) {
                                    // set sidebar tooltip to visible
                                    const stylesheet = document.styleSheets[0];
                                    removeCssStyleByCssSelector(".ant-tooltip:has(.fixed-tooltip)");
                                    stylesheet.insertRule(
                                        ".ant-tooltip:has(.fixed-tooltip) { position: fixed; left: 210px !important; }",
                                        stylesheet.cssRules.length
                                    );
                                } else {
                                    // set sidebar tooltip to hidden
                                    const stylesheet = document.styleSheets[0];
                                    removeCssStyleByCssSelector(".ant-tooltip:has(.fixed-tooltip)");
                                    stylesheet.insertRule(
                                        ".ant-tooltip:has(.fixed-tooltip) { display: none; }",
                                        stylesheet.cssRules.length
                                    );
                                }
                                setCollapsed(!collapsed);
                            }}
                            className={styles.collapsedButton}
                        />
                        <Breadcrumb separator=">" className={styles.breadCrumbInternal} items={getBreadcrumb()} />
                        <div className={styles.iconList}>
                            <Space size="middle">
                                <>
                                    {Object.entries(ALARM_COLOR).map(([k]) => (
                                        <div
                                            className={styles.iconList_iconDiv}
                                            key={`alarm_${k}`}
                                            onClick={() => {
                                                if (k === "error") {
                                                    dispatch(updateAlarmSearch("error"));
                                                }
                                                if (k === "warn") {
                                                    dispatch(updateAlarmSearch("warn"));
                                                }
                                                if (k === "info") {
                                                    dispatch(updateAlarmSearch("info"));
                                                }
                                                dispatch(updateAlarmSearchStatus(true));
                                                navigate("/monitor/alarm");
                                            }}
                                        >
                                            <div className={styles.iconList_icon}>
                                                <Icon component={alarmIconMap[k]} />
                                            </div>
                                            <div className={styles.iconList_label}>{alarmCount[k]}</div>
                                        </div>
                                    ))}
                                </>
                                <Dropdown
                                    menu={{items: userItems}}
                                    trigger={["hover"]}
                                    onOpenChange={val => setHoverStatus(val)}
                                >
                                    <div className={styles.iconList_iconUserDiv} style={{marginLeft: "8px"}}>
                                        <div className={styles.iconList_userLabel}>
                                            <Icon component={userSvg} />
                                            <span style={{marginLeft: "4px", marginRight: "2px"}}>
                                                {currentUser.username}
                                            </span>
                                        </div>
                                        <div
                                            className={styles.iconList_icon}
                                            style={{marginLeft: "2px", marginRight: "8px"}}
                                        >
                                            <Icon component={hoverStatus ? upSvg : downSvg} />
                                        </div>
                                    </div>
                                </Dropdown>
                                <div
                                    className={styles.helpSvg}
                                    onClick={() => {
                                        window.open("https://www.fs.com/tool/tool-home", "_blank");
                                    }}
                                >
                                    <Icon component={helpSvg} />
                                </div>
                            </Space>
                        </div>
                    </div>
                </Header>
                <Content className={styles.contentContainer}>
                    <Outlet />
                </Content>
            </Layout>
        </Layout>
    );
};
export default FSAmpConLayout;
