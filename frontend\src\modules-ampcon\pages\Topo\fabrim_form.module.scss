.goBack {
    font-weight: 500;
    font-size: 14px;
    color: #14C9BB;
    text-align: left;
    font-style: normal;
    text-transform: none;
}

h3:nth-child(1) {
    margin: 23px 0 24px 0;
}

h3 {
    font-size: 18px;
    margin: 40px 0 24px 0;
    font-weight: 700;
    color: #212519;
    text-align: left;
    font-style: normal;
    text-transform: none;
}

select {
    width: 280px;
}

.formWidth {
    width: 280px;
}

.groupButton {
    color: #FFFFFF;
    background: #5AD9CF;
}

.podContainerBox {
    margin-bottom: 24px;
}

.addNewPodBtn {
    width: 100%;
    height: 48px;
    border-radius: 4px 4px 4px 4px;
    border: 1px dotted #14C9BB;
    color: #14C9BB;
    display: block;
    background: none;
    cursor: pointer;
    margin-top: 24px;
}

.podContainer {
    width: 505px;
    padding: 24px;
    border: 1px dotted #DCDCDC;
    margin-bottom: 24px;

    h4 {
        font-size: 18px;
        margin: 0 0 24px 0;
        display: flex;
        align-items: center;
        justify-content: space-between;
    }
}

.addFabricBox {
    display: flex;
    flex: 1;
    flex-wrap: wrap;
    gap: 60px
}

.hrDivider {
    display: inline-block;
    width: 1px;
    height: 100%;
    background: #E7E7E7;
    border-radius: 0px 0px 0px 0px
}

.topology {
    width: calc(100% - 700px);
    min-width: 300px;
}

.button_box {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    position: absolute;
    right: 0;
    bottom: 0;
    height: 68px;
    border-top: 1px solid #E7E7E7;
    padding-right: 24px;
    background: #fff;

    button {
        margin-left: 24px;
    }
}

.fabricInfoEditBtn {
    width: auto;
    position: absolute;
    top: 67px;
    right: 24px;
    background: #14C9BB;
    color: #fff;
    border: none;
    padding: 9px 16px;
    cursor: pointer;
}

.fabricInfoEditBtn:hover {
    background: #34DCCF !important;
}

.fabricInfo {
    display: block;

    ul {
        display: flex;
        flex-wrap: wrap;
        padding-left: 0;
    }

    li {
        list-style: none;
        width: 390px;
        height: 40px;
        line-height: 40px;
        padding: 0 24px;
        background: #F7F9F9;
        margin-bottom: 24px;
        display: inline-flex;
    }

    li:nth-child(2n-1) {
        margin-right: 12px;
    }

    span:first-of-type {
        color: #788389;
        display: inline-block;
        width: 120px;
    }

    span:last-of-type {
        color: #212519;
        display: inline-block;
        width: calc(100% - 145px);
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis
    }
}

.selectCreateBtn {
    border: 1px solid #B2B2B2;
    color: #B2B2B2;
    width: 91%;
    margin: 8px 12px;
    background: #fff;
}

.selectCreateBtn:hover {
    background: rgba(20, 201, 187, 0.1) !important;
}