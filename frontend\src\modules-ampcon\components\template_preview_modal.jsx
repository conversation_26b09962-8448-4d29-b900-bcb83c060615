import {<PERSON><PERSON>, <PERSON>, Divider, Flex, Form, Input, message, Modal, Row, Select, Tooltip} from "antd";
import {forwardRef, useImperativeHandle, useState} from "react";
import {getTemplateInfo, saveEntireTemplateInfo} from "@/modules-ampcon/apis/template_api";

const TemplatePreviewModal = forwardRef((props, ref) => {
    const title = "View/Edit Template";
    const editConfigButtonTooltip = "Click Edit for Edit Template.";
    const saveConfigButtonToolTip = "Click Save, to save the new Template File.";
    const cancelEditConfigButtonTooltip = "Click to cancel editing";
    const enumOptions = [
        {
            value: "template",
            label: "Jinja2 Template"
        },
        {
            value: "variable",
            label: "Template Variable"
        }
    ];

    const [isShowModal, setIsShowModal] = useState(false);
    const [isEdit, setIsEdit] = useState(false);
    const [isButtonLoading, setIsButtonLoading] = useState(false);
    const [templateName, setTemplateName] = useState("");
    const [templateDescription, setTemplateDescription] = useState("");
    const [selectedTemplateFiles, setSelectedTemplateFiles] = useState(enumOptions[0]);
    const [templateContent, setTemplateContent] = useState("");
    const [variableContent, setVariableContent] = useState("");
    const [content, setContent] = useState("");

    const readonlyStyle = {
        height: `${window.innerHeight * 0.6}px`,
        border: "1px solid rgb(206, 212, 218)",
        fontSize: "16px",
        borderRadius: "4px",
        boxShadow: "none",
        marginBottom: "20px"
    };
    const editStyle = {
        height: `${window.innerHeight * 0.6}px`,
        border: "1px solid #14c9bb",
        fontSize: "16px",
        borderRadius: "4px",
        boxShadow: "none",
        marginBottom: "20px"
    };

    const saveCallback = () => {
        setIsButtonLoading(true);
        return new Promise((resolve, reject) => {
            saveEntireTemplateInfo(templateName, templateContent, variableContent)
                .then(response => {
                    if (response.status !== 200) {
                        message.error(response.info);
                        reject();
                    } else {
                        message.success(response.info);
                        setIsShowModal(false);
                        resolve();
                    }
                })
                .finally(() => {
                    setIsButtonLoading(false);
                    resolve();
                });
        });
    };

    const isKeyContainsMinus = obj => {
        let containsMinus = false;
        Object.keys(obj).forEach(key => {
            if (key.includes("-")) {
                containsMinus = true;
            }
            if (typeof obj[key] === "object" && obj[key] !== null) {
                if (isKeyContainsMinus(obj[key])) {
                    containsMinus = true;
                }
            }
        });
        return containsMinus;
    };

    const isTemplateValid = () => {
        try {
            JSON.parse(variableContent);
        } catch (e) {
            message.error("VariableContent is not a valid JSON.");
            return false;
        }
        if (isKeyContainsMinus(JSON.parse(variableContent)) === true) {
            message.error("VariableContent key should not contain '-'.");
            return false;
        }
    };

    const saveButtonCallback = () => {
        if (isTemplateValid() === false) {
            return;
        }
        saveCallback();
        setIsEdit(false);
    };

    const handleFileContentChange = e => {
        setContent(e.target.value);
        if (selectedTemplateFiles.value === "template") {
            setTemplateContent(e.target.value);
        } else if (selectedTemplateFiles.value === "variable") {
            setVariableContent(e.target.value);
        }
    };

    const handleCancel = () => {
        setIsShowModal(false);
    };

    const cancelButtonCallback = () => {
        handleCancel();
        setIsButtonLoading(false);
        setIsEdit(false);
        setSelectedTemplateFiles(enumOptions[0]);
    };

    const editCallback = () => {
        setIsEdit(true);
    };

    const cancelEditCallBack = () => {
        setIsEdit(false);
    };

    useImperativeHandle(ref, () => ({
        showTemplatePreviewModal: name => {
            getTemplateInfo(name).then(response => {
                if (response.status !== 200) {
                    message.error(response.info);
                } else {
                    setTemplateName(response.data.name);
                    setTemplateDescription(response.data.desc);
                    setSelectedTemplateFiles(enumOptions[0]);
                    setContent(response.data.templateContent);
                    setTemplateContent(response.data.templateContent);
                    try {
                        setVariableContent(JSON.stringify(JSON.parse(response.data.var), null, 4));
                    } catch (e) {
                        setVariableContent(response.data.var);
                    }
                    setIsShowModal(true);
                }
            });
        },
        hideTemplatePreviewModal: () => {
            cancelButtonCallback();
        }
    }));

    return isShowModal ? (
        <Modal
            className="ampcon-max-modal"
            title={
                <div>
                    {title}
                    <Divider style={{marginTop: 8, marginBottom: 0}} />
                </div>
            }
            open={isShowModal}
            onOk={saveCallback}
            onCancel={cancelButtonCallback}
            width="45%"
            footer={
                isEdit
                    ? [
                          <Divider />,
                          <Tooltip title={cancelEditConfigButtonTooltip} placement="topRight">
                              <Button key="cancel edit" onClick={cancelEditCallBack}>
                                  Cancel Edit
                              </Button>
                          </Tooltip>,
                          <Tooltip title={saveConfigButtonToolTip} placement="topRight">
                              <Button key="submit" type="primary" onClick={saveButtonCallback}>
                                  Save
                              </Button>
                          </Tooltip>
                      ]
                    : [
                          <Divider />,
                          <Tooltip title={editConfigButtonTooltip} placement="topRight">
                              <Button key="edit" onClick={editCallback}>
                                  Edit
                              </Button>
                          </Tooltip>,
                          <Tooltip title={saveConfigButtonToolTip} placement="topRight">
                              <Button
                                  key="submit"
                                  type="primary"
                                  loading={isButtonLoading}
                                  onClick={saveButtonCallback}
                              >
                                  Save
                              </Button>
                          </Tooltip>
                      ]
            }
        >
            <Flex flex={1} style={{maxHeight: "400px", minHeight: "400px", overflowY: "auto"}} vertical>
                <Form
                    layout="inline"
                    labelAlign="left"
                    style={{minWidth: "100%", marginBottom: "20px"}}
                    labelCol={{span: 7}}
                    wrapperCol={{span: 15}}
                >
                    <Row gutter={24} style={{width: "100%"}}>
                        <Col span={8}>
                            <Form.Item name="name" label="Name :" initialValue={templateName}>
                                <Input value={templateName} style={{backgroundColor: "#f0f0f0"}} readOnly />
                            </Form.Item>
                        </Col>
                        <Col span={8}>
                            <Form.Item name="description" label="Description :" initialValue={templateDescription}>
                                <Input style={{backgroundColor: "#f0f0f0"}} readOnly />
                            </Form.Item>
                        </Col>
                        <Col span={8}>
                            <Form.Item name="templateFiles" label="Template Files :">
                                <Select
                                    defaultValue={selectedTemplateFiles}
                                    value={selectedTemplateFiles}
                                    options={enumOptions}
                                    onChange={value => {
                                        if (value === "template") {
                                            setSelectedTemplateFiles(enumOptions[0]);
                                            setContent(templateContent);
                                        } else if (value === "variable") {
                                            setSelectedTemplateFiles(enumOptions[1]);
                                            setContent(variableContent);
                                        }
                                    }}
                                />
                            </Form.Item>
                        </Col>
                    </Row>
                </Form>
                {isEdit ? (
                    <Input.TextArea style={editStyle} value={content} onChange={handleFileContentChange} />
                ) : (
                    <Input.TextArea style={readonlyStyle} value={content} readonly />
                )}
            </Flex>
        </Modal>
    ) : null;
});

export default TemplatePreviewModal;
