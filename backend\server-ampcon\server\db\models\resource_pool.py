from flask import jsonify, request
from sqlalchemy import (
    <PERSON>umn,
    <PERSON><PERSON><PERSON>,
    String,
    Text,
    ForeignKey,
    Boolean,
    DateTime,
    Enum,
    JSON,
    Table
)
from sqlalchemy.orm import relationship, exc
from abc import ABC, abstractmethod

from server.util import utils

from server.db.db_common import DBCommon
from server.db.models.base import Base


def is_ranges_conflict(ranges):
    if len(ranges) == 1:
        return False
    for i in range(len(ranges)):
        if ranges[i]['start'] > ranges[i]['end']:
            return True
    for i in range(len(ranges)):
        for j in range(i + 1, len(ranges)):
            if ranges[i]['start'] <= ranges[j]['start'] <= ranges[i]['end'] or ranges[i]['start'] <= ranges[j]['end'] <= ranges[i]['end']:
                return True
    return False


def is_resource_pool_asn_in_use(resource_pool_asn_ranges):
    for asn_range in resource_pool_asn_ranges:
        if asn_range.is_in_use:
            return True
    return False

def is_resource_pool_area_in_use(resource_pool_area_ranges):
    for area_range in resource_pool_area_ranges:
        if area_range.is_in_use:
            return True
    return False

def is_resource_pool_ip_in_use(resource_pool_ip_ranges):
    for ip_range in resource_pool_ip_ranges:
        if ip_range.is_in_use:
            return True
    return False

def is_pool_in_use(pool):
    if(isinstance(pool, ResourcePoolAsn)):
        return is_resource_pool_asn_in_use(pool.resource_pool_asn_ranges)
    elif(isinstance(pool, ResourcePoolArea)):
        return is_resource_pool_area_in_use(pool.resource_pool_area_ranges)
    elif (isinstance(pool, ResourcePoolIp)):
        return is_resource_pool_ip_in_use(pool.resource_pool_ip_ranges)
    
def get_pool_ranges(pool):
    if(isinstance(pool, ResourcePoolAsn)):
        return pool.resource_pool_asn_ranges
    elif(isinstance(pool, ResourcePoolArea)):
        return pool.resource_pool_area_ranges
    elif(isinstance(pool, ResourcePoolIp)):
        return pool.resource_pool_ip_ranges
    
def get_pool_use_detail(pool_range):
    if(isinstance(pool_range, ResourcePoolAsnRanges)):
        return pool_range.resource_pool_asn_use_detail
    elif(isinstance(pool_range, ResourcePoolAreaRanges)):
        return pool_range.resource_pool_area_use_detail
    elif(isinstance(pool_range, ResourcePoolIpRanges)):
        return pool_range.resource_pool_ip_use_detail


class ResourcePoolAsn(Base):
    __tablename__ = "resource_pool_asn"
    id = Column(Integer, primary_key=True, autoincrement=True)
    name = Column(String(128), index=True, unique=True, nullable=False)
    resource_pool_asn_ranges = relationship("ResourcePoolAsnRanges", backref="resource_pool_asn", cascade="all, delete-orphan", uselist=True)


class ResourcePoolAsnRanges(Base):
    __tablename__ = "resource_pool_asn_ranges"
    id = Column(Integer, primary_key=True, autoincrement=True)
    start_value = Column(Integer, nullable=False)
    end_value = Column(Integer, nullable=False)
    resource_pool_asn_id = Column(Integer, ForeignKey("resource_pool_asn.id", ondelete='CASCADE', onupdate='CASCADE'))
    is_in_use = Column(Boolean, default=False)
    resource_pool_asn_use_detail = relationship("ResourcePoolAsnUseDetail", backref="resource_pool_asn_ranges", cascade="all, delete-orphan", uselist=True)


class ResourcePoolAsnUseDetail(Base):
    __tablename__ = "resource_pool_asn_use_detail"
    id = Column(Integer, primary_key=True, autoincrement=True)
    value = Column(Integer, nullable=False)
    resource_pool_asn_ranges_id = Column(Integer, ForeignKey("resource_pool_asn_ranges.id", ondelete='CASCADE', onupdate='CASCADE'))


# Area start
class ResourcePoolArea(Base):
    __tablename__ = "resource_pool_area"
    id = Column(Integer, primary_key=True, autoincrement=True)
    name = Column(String(128), index=True, unique=True, nullable=False)
    resource_pool_area_ranges = relationship("ResourcePoolAreaRanges", backref="resource_pool_area", cascade="all, delete-orphan", uselist=True)


class ResourcePoolAreaRanges(Base):
    __tablename__ = "resource_pool_area_ranges"
    id = Column(Integer, primary_key=True, autoincrement=True)
    start_value = Column(Integer, nullable=False)
    end_value = Column(Integer, nullable=False)
    resource_pool_area_id = Column(Integer, ForeignKey("resource_pool_area.id", ondelete='CASCADE', onupdate='CASCADE'))
    is_in_use = Column(Boolean, default=False)
    resource_pool_area_use_detail = relationship("ResourcePoolAreaUseDetail", backref="resource_pool_area_ranges", cascade="all, delete-orphan", uselist=True)


class ResourcePoolAreaUseDetail(Base):
    __tablename__ = "resource_pool_area_use_detail"
    id = Column(Integer, primary_key=True, autoincrement=True)
    value = Column(Integer, nullable=False)
    resource_pool_area_ranges_id = Column(Integer, ForeignKey("resource_pool_area_ranges.id", ondelete='CASCADE', onupdate='CASCADE'))
# Area end

class ResourcePoolIp(Base):
    __tablename__ = "resource_pool_ip"
    id = Column(Integer, primary_key=True, autoincrement=True)
    name = Column(String(128), index=True, unique=True, nullable=False)
    resource_pool_ip_ranges = relationship("ResourcePoolIpRanges", backref="resource_pool_ip", cascade="all, delete-orphan", uselist=True)


class ResourcePoolIpRanges(Base):
    __tablename__ = "resource_pool_ip_ranges"
    id = Column(Integer, primary_key=True, autoincrement=True)
    start_value = Column(Integer, nullable=False)
    end_value = Column(Integer, nullable=False)
    resource_pool_ip_id = Column(Integer, ForeignKey("resource_pool_ip.id", ondelete='CASCADE', onupdate='CASCADE'))
    is_in_use = Column(Boolean, default=False)
    resource_pool_ip_use_detail = relationship("ResourcePoolIpUseDetail", backref="resource_pool_ip_ranges", cascade="all, delete-orphan", uselist=True)


class ResourcePoolIpUseDetail(Base):
    __tablename__ = "resource_pool_ip_use_detail"
    id = Column(Integer, primary_key=True, autoincrement=True)
    value = Column(Integer, nullable=False)
    resource_pool_ip_ranges_id = Column(Integer, ForeignKey("resource_pool_ip_ranges.id", ondelete='CASCADE', onupdate='CASCADE'))

class ResourcePoolInterface(object):
    @abstractmethod
    def edit_resource_pool(self, pool_id, new_name, ranges):
        pass

    @abstractmethod
    def add_resource_pool(self, name, ranges):
        pass

    @abstractmethod
    def clone_resource_pool(self, pool_id, new_name):
        pass

    @abstractmethod
    def query_all_resource_pool(self):
        pass

    @abstractmethod
    def query_resource_pool_ranges_detail(self, pool_name):
        pass

    @abstractmethod
    def query_resource_pool_by_id(self, to_be_query_record_id):
        pass

    @abstractmethod
    def delete_resource_pool_by_id(self, to_be_deleted_record_id):
        pass

    @abstractmethod
    def generate_resource_from_pool(self, pool_id, count):
        pass


class ResourcePoolBase(ResourcePoolInterface, DBCommon, ABC):

    def __init__(self):
        self.pool_class = None
        self.pool_ranges_class = None
        self.pool_use_detail_class = None

    @staticmethod
    def _check_is_range_start_end_value_valid(start_value, end_value, asn_range_id=""):
        start_value_greater_than_end_value_error_msg = f"Resource pool range id {asn_range_id} start value {start_value} must less than end value {end_value}" if asn_range_id else f"Resource pool range start value {start_value} must less than end value {end_value}"
        if start_value > end_value:
            raise ValueError(start_value_greater_than_end_value_error_msg)
        start_value_less_than_zero_error_msg = f"Resource pool range id {asn_range_id} start value {start_value} must greater than 0" if asn_range_id else f"Resource pool range start value {start_value} must greater than 0"
        if start_value <= 0:
            raise ValueError(start_value_less_than_zero_error_msg)
        start_value_greater_than_max_error_msg = f"Resource pool range id {asn_range_id} start value {start_value} must less than 4294967295" if asn_range_id else f"Resource pool range start value {start_value} must less than 4294967295"
        if start_value >= 4294967295:
            raise ValueError(start_value_greater_than_max_error_msg)
        end_value_less_than_zero_error_msg = f"Resource pool range id {asn_range_id} end value {end_value} must greater than 0" if asn_range_id else f"Resource pool range end value {end_value} must greater than 0"
        if end_value <= 0:
            raise ValueError(end_value_less_than_zero_error_msg)
        end_value_greater_than_max_error_msg = f"Resource pool range id {asn_range_id} end value {end_value} must less than 4294967295" if asn_range_id else f"Resource pool range end value {end_value} must less than 4294967295"
        if end_value >= 4294967295:
            raise ValueError(end_value_greater_than_max_error_msg)

    def edit_resource_pool(self, pool_id, new_name, ranges):
        session = self.get_session()
        try:
            with session.begin():
                self.query_resource_pool_by_id(pool_id).update({"name": new_name})
                if ranges.get('delete') != []:
                    for asn_range_delete in ranges.get('delete', []):
                        query_range = session.query(self.pool_ranges_class).filter(self.pool_ranges_class.id == asn_range_delete['rangeId'])
                        range_data = query_range.first()
                        if not range_data:
                            raise ValueError(f"Resource pool range id {asn_range_delete['rangeId']} not found")
                        if range_data.is_in_use:
                            raise ValueError(f"Resource pool range id {asn_range_delete['rangeId']} is in use, cannot delete")
                        query_range.delete()
                    session.flush()
                if ranges.get('modify')!= []:
                    for asn_range_modify in ranges.get('modify', []):
                        if asn_range_modify.get('rangeId', None):
                            query_range = session.query(self.pool_ranges_class).filter(self.pool_ranges_class.id == asn_range_modify['rangeId'])
                            range_data = query_range.first()
                            if range_data:
                                self._check_is_range_start_end_value_valid(asn_range_modify['start'], asn_range_modify['end'], asn_range_modify['rangeId'])
                                if range_data.is_in_use and (range_data.start_value < asn_range_modify['start'] or range_data.end_value > asn_range_modify['end']):
                                    raise ValueError(f"Resource pool range id {asn_range_modify['rangeId']} is in use, cannot narrow range")
                                query_range.update({"start_value": asn_range_modify['start'], "end_value": asn_range_modify['end']})
                            else:
                                raise ValueError(f"Resource pool range id {asn_range_modify['rangeId']} not found")
                    session.flush()
                if ranges.get('add')!= []:
                    for asn_range_add in ranges.get('add', []):
                        self._check_is_range_start_end_value_valid(asn_range_add['start'], asn_range_add['end'])
                        if(str(self.pool_class).split('.')[-1].strip("'>")=="ResourcePoolAsn"):
                            new_range = self.pool_ranges_class(start_value=asn_range_add['start'], end_value=asn_range_add['end'], resource_pool_asn_id=pool_id)
                        elif(str(self.pool_class).split('.')[-1].strip("'>")=="ResourcePoolArea"):
                            new_range = self.pool_ranges_class(start_value=asn_range_add['start'], end_value=asn_range_add['end'], resource_pool_area_id=pool_id)
                        elif (str(self.pool_class).split('.')[-1].strip("'>") == "ResourcePoolIp"):
                            new_range = self.pool_ranges_class(start_value=asn_range_add['start'], end_value=asn_range_add['end'], resource_pool_ip_id=pool_id)
                        session.add(new_range)
                    session.flush()
                return True
        except Exception as e:
            session.rollback()
            raise e

    def add_resource_pool(self, name, ranges):
        # add asn resource pool and asn ranges
        session = self.get_session()
        try:
            with session.begin():
                new_pool = self.pool_class(name=name)
                session.add(new_pool)
                session.flush()
                for asn_range in ranges:
                    if(isinstance(new_pool, ResourcePoolAsn)):
                        new_range = self.pool_ranges_class(start_value=asn_range['start'], end_value=asn_range['end'], resource_pool_asn_id=new_pool.id)
                    elif(isinstance(new_pool, ResourcePoolArea)):
                        new_range = self.pool_ranges_class(start_value=asn_range['start'], end_value=asn_range['end'], resource_pool_area_id=new_pool.id)
                    elif (isinstance(new_pool, ResourcePoolIp)):
                        new_range = self.pool_ranges_class(start_value=asn_range['start'], end_value=asn_range['end'], resource_pool_ip_id=new_pool.id)
                    session.add(new_range)
                session.flush()
                return True
        except Exception as e:
            session.rollback()
            return False

    def clone_resource_pool(self, pool_id, new_name):
        session = self.get_session()
        try:
            with session.begin():
                pool_query = self.query_resource_pool_by_id(pool_id)
                pool_data = pool_query.first()
                if not pool_data:
                    raise ValueError(f"Resource pool id {pool_id} not found")
                new_pool = self.pool_class(name=new_name)
                session.add(new_pool)
                session.flush()
                if(isinstance(pool_data, ResourcePoolAsn)):
                    for asn_range in pool_data.resource_pool_asn_ranges:
                        new_range = self.pool_ranges_class(start_value=asn_range.start_value, end_value=asn_range.end_value, resource_pool_asn_id=new_pool.id)
                        session.add(new_range)
                elif(isinstance(pool_data, ResourcePoolArea)):
                    for area_range in pool_data.resource_pool_area_ranges:
                        new_range = self.pool_ranges_class(start_value=area_range.start_value, end_value = area_range.end_value, resource_pool_area_id = new_pool.id)
                        session.add(new_range)
                elif (isinstance(pool_data, ResourcePoolIp)):
                    for ip_range in pool_data.resource_pool_ip_ranges:
                        new_range = self.pool_ranges_class(start_value=ip_range.start_value, end_value=ip_range.end_value, resource_pool_ip_id=new_pool.id)
                        session.add(new_range)
                session.flush()
                return True
        except Exception as e:
            session.rollback()
            raise e

    def query_all_resource_pool(self):
        data = request.get_json()
        sort_fields = data.get("sortFields", [])
        sort_field = sort_fields[0].get("field") if sort_fields else "name"
        sort_order = sort_fields[0].get("order") if sort_fields else "asc"

        page_num, page_size, total_count, query_obj = utils.query_helper(self.pool_class, None, sort_field != "name")

        response_data = [{
            "id": pool.id,
            "name": pool.name,
            "is_in_use": is_pool_in_use(pool),
            "ranges": [{
                "id": pool_range.id,
                "start_value": pool_range.start_value,
                "end_value": pool_range.end_value,
                "used_count": len(get_pool_use_detail(pool_range)),
                "ranges_count": pool_range.end_value - pool_range.start_value + 1,
                "is_in_use": pool_range.is_in_use
            } for pool_range in get_pool_ranges(pool)],
            "used_count": sum(len(get_pool_use_detail(pool_range)) for pool_range in get_pool_ranges(pool)),
            "ranges_count": sum(pool_range.end_value - pool_range.start_value + 1 for pool_range in get_pool_ranges(pool))
        } for pool in query_obj]

        if sort_field in ["used_count", "ranges_count"]:
            response_data.sort(key=lambda x: x[sort_field], reverse=(sort_order == "desc"))

        response = {
            "data": response_data,
            "page": page_num,
            "pageSize": page_size,
            "total": total_count,
            "status": 200
        }
        return jsonify(response)

    def query_resource_pool_data_list(self):
        session = self.get_session()
        try:
            pool_data = session.query(self.pool_class).all()
            return pool_data
        except exc.NoResultFound:
            return None

    def query_resource_pool_by_id(self, to_be_deleted_record_id):
        session = self.get_session()
        query_pool = session.query(self.pool_class).filter(self.pool_class.id == to_be_deleted_record_id)
        return query_pool

    def query_resource_pool_by_name(self, pool_name):
        session = self.get_session()
        query_pool = session.query(self.pool_class).filter(self.pool_class.name == pool_name)
        return query_pool

    def delete_resource_pool_by_id(self, to_be_deleted_record_id):
        target_pool_query = self.query_resource_pool_by_id(to_be_deleted_record_id)
        target_pool = target_pool_query.first()
        if is_pool_in_use(get_pool_ranges(target_pool)):
            return jsonify({"status": 500, "msg": f"Resource pool id {to_be_deleted_record_id} is in use"})
        if target_pool is None:
            return jsonify({"status": 500, "msg": f"Resource pool id {to_be_deleted_record_id} not found"})
        return True if target_pool_query.delete() != 0 else False

    def query_resource_pool_ranges_detail(self, pool_name):
        session = self.get_session()
        query_pool = session.query(self.pool_class).filter(self.pool_class.name == pool_name).first()
        return query_pool

    def delete_used_records_from_pool(self, records):
        """
        :param records: list of dict, each dict contains pool_range_id and record_value
        """
        session = self.get_session()
        records_id_and_records_mapping = {}
        for record in records:
            pool_range_id = record.get('pool_range_id', None)
            record_value = record.get('record_value', None)
            if pool_range_id is None or record_value is None:
                continue
            if records_id_and_records_mapping.get(pool_range_id, None) is None:
                records_id_and_records_mapping[pool_range_id] = [record_value]
            else:
                records_id_and_records_mapping[pool_range_id].append(record_value)
            records_id_and_records_mapping.setdefault(pool_range_id, []).append(record_value)
        with session.begin():
            for record_mapping_key, record_mapping_values in records_id_and_records_mapping.items():
                session.query(self.pool_use_detail_class).filter(self.pool_use_detail_class.value.in_(record_mapping_values)).delete()
                session.flush()
                if(str(self.pool_class).split('.')[-1].strip("'>")=="ResourcePoolAsn"):
                    if session.query(self.pool_use_detail_class).filter(self.pool_use_detail_class.resource_pool_asn_ranges_id == record_mapping_key).count() == 0:
                        session.query(self.pool_ranges_class).filter(self.pool_ranges_class.id == record_mapping_key).update({"is_in_use": False})
                elif(str(self.pool_class).split('.')[-1].strip("'>")=="ResourcePoolArea"):
                    if session.query(self.pool_use_detail_class).filter(self.pool_use_detail_class.resource_pool_area_ranges_id == record_mapping_key).count() == 0:
                        session.query(self.pool_ranges_class).filter(self.pool_ranges_class.id == record_mapping_key).update({"is_in_use": False})
                elif (str(self.pool_class).split('.')[-1].strip("'>") == "ResourcePoolIp"):
                    if session.query(self.pool_use_detail_class).filter(self.pool_use_detail_class.resource_pool_ip_ranges_id == record_mapping_key).count() == 0:
                            session.query(self.pool_ranges_class).filter(self.pool_ranges_class.id == record_mapping_key).update({"is_in_use": False})
        return True

    # todo for test
    def delete_first_n_used_record(self, pool_id, record_num):
        session = self.get_session()
        pool = session.query(self.pool_class).filter(self.pool_class.id == pool_id).first()
        records = []
        if not pool:
            raise ValueError(f"Resource pool id {pool_id} not found")
        if(isinstance(pool, ResourcePoolAsn)):
            for asn_range in pool.resource_pool_asn_ranges:
                for use_detail in asn_range.resource_pool_asn_use_detail:
                    records.append({"pool_range_id": asn_range.id, "record_value": use_detail.value})
        elif(isinstance(pool, ResourcePoolArea)):
            for area_range in pool.resource_pool_area_ranges:
                for use_detail in area_range.resource_pool_area_use_detail:
                    records.append({"pool_range_id": area_range.id, "record_value": use_detail.value})
        elif(isinstance(pool, ResourcePoolIp)):
            for ip_range in pool.resource_pool_ip_ranges:
                for use_detail in ip_range.resource_pool_ip_use_detail:
                    records.append({"pool_range_id": ip_range.id, "record_value": use_detail.value})
        return self.delete_used_records_from_pool(records[:record_num])

    @staticmethod
    def is_have_enough_range_space(pool_data, count):
        try:
            if(isinstance(pool_data, ResourcePoolAsn)):
                pool_ranges = pool_data.resource_pool_asn_ranges
            elif(isinstance(pool_data, ResourcePoolArea)):
                pool_ranges = pool_data.resource_pool_area_ranges
            elif (isinstance(pool_data, ResourcePoolIp)):
                pool_ranges = pool_data.resource_pool_ip_ranges
            # asn_ranges = pool_data.resource_pool_asn_ranges
            asn_ranges = sorted(pool_ranges, key=lambda x: x.start_value)
            range_total_num_count = 0
            range_used_total_num_count = 0
            for asn_range in asn_ranges:
                range_total_num_count += asn_range.end_value - asn_range.start_value + 1
                if(isinstance(pool_data, ResourcePoolAsn)):
                    range_used_total_num_count += len(asn_range.resource_pool_asn_use_detail)
                elif(isinstance(pool_data, ResourcePoolArea)):
                    range_used_total_num_count += len(asn_range.resource_pool_area_use_detail)
                elif (isinstance(pool_data, ResourcePoolIp)):
                    range_used_total_num_count += len(asn_range.resource_pool_ip_use_detail)
            if range_total_num_count - range_used_total_num_count < count:
                return False
            return True
        except Exception as e:
            raise e

    @staticmethod
    def generate_numbers(start, all_used_num_list, total, ranges):
        all_used_num_list_copy = all_used_num_list.copy()
        numbers = []
        current = start
        current_range_index = 0
        count = 0
        while len(numbers) < total:
            current_range = ranges[current_range_index]
            if current_range[0] <= current <= current_range[1]:
                if current not in all_used_num_list_copy[current_range_index]:
                    numbers.append([current_range[2], current])
                    all_used_num_list_copy.append(current)
                current += 1
            else:
                current_range_index = (current_range_index + 1) % len(ranges)
                count += 1
                if count >= len(ranges) or len(all_used_num_list[current_range_index]) == 0:
                    current = ranges[current_range_index][0]
                else:
                    current = max(all_used_num_list[current_range_index]) + 1
        return numbers

    def generate_resource_num(self, pool_data, count):
        if(isinstance(pool_data, ResourcePoolAsn)):
            asn_ranges = sorted(pool_data.resource_pool_asn_ranges, key=lambda x: x.start_value)
        elif(isinstance(pool_data, ResourcePoolArea)):
            asn_ranges = sorted(pool_data.resource_pool_area_ranges, key=lambda x: x.start_value)
        elif(isinstance(pool_data, ResourcePoolIp)):
            asn_ranges = sorted(pool_data.resource_pool_ip_ranges, key=lambda x: x.start_value)
        all_ranges_list = list(map(lambda x: [x.start_value, x.end_value, x.id], asn_ranges))
        if(isinstance(pool_data, ResourcePoolAsn)):
            all_used_num_list = list(map(lambda x: [y.value for y in x.resource_pool_asn_use_detail], asn_ranges))
        elif(isinstance(pool_data, ResourcePoolArea)):
            all_used_num_list = list(map(lambda x: [y.value for y in x.resource_pool_area_use_detail], asn_ranges))
        elif (isinstance(pool_data, ResourcePoolIp)):
            all_used_num_list = list(map(lambda x: [y.value for y in x.resource_pool_ip_use_detail], asn_ranges))
        used_num_list = []
        for used_num in all_used_num_list:
            used_num_list.extend(used_num)
        if not used_num_list:
            max_used_num = all_ranges_list[0][0] - 1
        else:
            max_used_num = max(used_num_list)
        target_num_start = max_used_num + 1
        return self.generate_numbers(target_num_start, all_used_num_list, count, all_ranges_list)


class ResourcePoolAsnDB(ResourcePoolBase, ABC):
    def __init__(self):
        super().__init__()
        self.pool_class = ResourcePoolAsn
        self.pool_ranges_class = ResourcePoolAsnRanges
        self.pool_use_detail_class = ResourcePoolAsnUseDetail
        self.session = self.get_session()

    def generate_resource_from_pool(self, pool_id, count):
        session = self.get_session()
        try:
            with session.begin():
                pool_query = self.query_resource_pool_by_id(pool_id)
                pool_data = pool_query.first()
                if not pool_data:
                    raise ValueError(f"Resource pool id {pool_id} not found")
                if not self.is_have_enough_range_space(pool_data, count):
                    raise ValueError(f"Resource pool id {pool_id} has no available ranges")

                to_be_add_numbers = self.generate_resource_num(pool_data, count)

                session.query(self.pool_ranges_class).filter(self.pool_ranges_class.id.in_(list(set(map(lambda x: x[0], to_be_add_numbers))))).update({"is_in_use": True})

                for num in to_be_add_numbers:
                    new_use_detail = self.pool_use_detail_class(value=num[1], resource_pool_asn_ranges_id=num[0])
                    session.add(new_use_detail)
                session.flush()
                return True
        except Exception as e:
            session.rollback()
            raise e


class ResourcePoolIpDB(ResourcePoolBase, ABC):
    def __init__(self):
        super().__init__()
        self.pool_class = ResourcePoolIp
        self.pool_ranges_class = ResourcePoolIpRanges
        self.pool_use_detail_class = ResourcePoolIpUseDetail
        self.session = self.get_session()

    def generate_resource_from_pool(self, pool_id, count):
        session = self.get_session()
        try:
            with session.begin():
                pool_query = self.query_resource_pool_by_id(pool_id)
                pool_data = pool_query.first()
                if not pool_data:
                    raise ValueError(f"Resource pool id {pool_id} not found")
                if not self.is_have_enough_range_space(pool_data, count):
                    raise ValueError(f"Resource pool id {pool_id} has no available ranges")

                to_be_add_numbers = self.generate_resource_num(pool_data, count)

                session.query(self.pool_ranges_class).filter(
                    self.pool_ranges_class.id.in_(list(set(map(lambda x: x[0], to_be_add_numbers))))).update(
                    {"is_in_use": True})

                for num in to_be_add_numbers:
                    new_use_detail = self.pool_use_detail_class(value=num[1], resource_pool_ip_ranges_id=num[0])
                    session.add(new_use_detail)
                session.flush()
                return True
        except Exception as e:
            session.rollback()
            raise e
        pass


class ResourcePoolAreaDB(ResourcePoolBase, ABC):
    def __init__(self):
        super().__init__()
        self.pool_class = ResourcePoolArea
        self.pool_ranges_class = ResourcePoolAreaRanges
        self.pool_use_detail_class = ResourcePoolAreaUseDetail
        self.session = self.get_session()


    def generate_resource_from_area_pool(self, pool_id, count):
        session = self.get_session()
        try:
            with session.begin():
                pool_query = self.query_resource_pool_by_id(pool_id)
                pool_data = pool_query.first()
                if not pool_data:
                    raise ValueError(f"Resource pool id {pool_id} not found")
                if not self.is_have_enough_range_space(pool_data, count):
                    raise ValueError(f"Resource pool id {pool_id} has no available ranges")

                to_be_add_numbers = self.generate_resource_num(pool_data, count)

                session.query(self.pool_ranges_class).filter(self.pool_ranges_class.id.in_(list(set(map(lambda x: x[0], to_be_add_numbers))))).update({"is_in_use": True})

                for num in to_be_add_numbers:
                    new_use_detail = self.pool_use_detail_class(value=num[1], resource_pool_area_ranges_id=num[0])
                    session.add(new_use_detail)
                session.flush()
                return True
        except Exception as e:
            session.rollback()
            raise e
        pass


resource_pool_asn_db = ResourcePoolAsnDB()
resource_pool_area_db = ResourcePoolAreaDB()
resource_pool_ip_db = ResourcePoolIpDB()
