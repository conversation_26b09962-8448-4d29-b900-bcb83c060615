import {useEffect, useState, useRef} from "react";
import {useSelector, useDispatch} from "react-redux";
import DockLayout from "rc-dock";
import {useRequest} from "ahooks";
import {apiConnectionByGroup} from "@/modules-otn/apis/api";
import {
    setNeInfoListMap,
    setConnections,
    setTreeItemChangedTag,
    setTableFilter,
    setSelectedItem,
    setUpdateServiceView
} from "@/store/modules/otn/mapSlice";
import Fiber from "@/modules-otn/pages/otn/service/fiber";
import Alarm from "@/modules-otn/pages/otn/alarm/alarm";
import Event from "@/modules-otn/pages/otn/alarm/event";
import NeList from "@/modules-otn/pages/otn/device/neList";
import RelatePort from "@/modules-otn/pages/otn/device/relate_port";
import Aps from "@/modules-otn/pages/otn/service/aps";
import ProcessTool from "@/modules-otn/components/common/process_tool";
import ServiceViewContainer from "@/modules-otn/pages/otn/service/service_view_container";
import {classNames} from "@/modules-otn/utils/util";
import ConnectMapL7 from "../device/device_map";
import ServiceTree from "./service_tree";
import "rc-dock/dist/rc-dock.css";
import styles from "./service.module.scss";

const portMapping = {
    BAIN: "PAOUT",
    BAOUT: "PAIN",
    PAOUT: "BAIN",
    PAIN: "BAOUT",
    LA1IN: "LA2OUT",
    LA2OUT: "LA1IN",
    LA1OUT: "LA2IN",
    LA2IN: "LA1OUT"
};

const Service = ({serviceType}) => {
    let relateTabs = [{id: "ne"}, {id: "alarm"}, {id: "resource"}];
    if (serviceType === "optics") {
        relateTabs = [...relateTabs, {id: "fiber"}, {id: "protection_5"}];
    }
    const box = {
        dockbox: {
            mode: "horizontal",
            children: [
                {
                    size: 300,
                    tabs: [{id: "service_list"}]
                },
                {
                    size: 1600,
                    mode: "vertical",
                    children: [
                        {
                            id: "graphic_panel",
                            size: 300,
                            tabs: [{id: "map"}, {id: "service_view"}]
                        },
                        {
                            size: 500,
                            id: "relate_table",
                            tabs: relateTabs
                        }
                    ]
                }
            ]
        }
    };
    const [activeTab, setActiveTab] = useState("ne");
    const refActiveTab = useRef(activeTab);
    const refLayout = useRef();
    const {labelList} = useSelector(state => state.languageOTN);
    const [process, setProcess] = useState({percent: -1});
    const {
        tableFilter: {provision, action},
        treeItemChangedTag
    } = useSelector(state => state.map);
    const dispatch = useDispatch();
    const [layout, setLayout] = useState(box);
    refActiveTab.current = activeTab;
    const loadData = () => {
        runAsync("ne:5:connection", {}).then(res => {
            const {configNeDocuments, connectionInfo} = res;
            try {
                // 网元节点数据map配置
                const cloneConfigNeDocuments = structuredClone(configNeDocuments);
                dispatch(
                    setNeInfoListMap(
                        cloneConfigNeDocuments?.reduce((res, item) => {
                            const {id, value} = item;
                            res[id] = value;
                            return res;
                        }, {})
                    )
                );
                const connectionData = connectionInfo?.map(item => ({
                    ne_id: item.value.ne_id,
                    ...(item.value.data.config ?? item.value.data.state)
                }));
                const connObj = connectionData.reduce((prev, {ne_id, source, dest, ...others}) => {
                    const srcArr = source.split("|");
                    const dstArr = dest.split("|");

                    const srcPort = srcArr.pop();
                    const dstPort = dstArr.pop();

                    const sourceIP = srcArr[0] ?? ne_id;
                    const destIP = dstArr[0] ?? ne_id;

                    const getFullPorts = port => {
                        const [, prefix, suffix1] = port.match(/^(.+-)(.+)$/);
                        const suffix2 = portMapping[suffix1];
                        return suffix2 ? [`${prefix}${suffix1}`, `${prefix}${suffix2}`].sort().join("/") : port;
                    };

                    const sourcePort = getFullPorts(srcPort);
                    const destPort = getFullPorts(dstPort);
                    const uniqueStr = [`${sourceIP}|${sourcePort}`, `${destIP}|${destPort}`].sort().join();
                    if (prev[uniqueStr]) {
                        prev[uniqueStr].originValue.push({ne_id, source, dest, ...others});
                    } else {
                        prev[uniqueStr] = {
                            sourceIP,
                            sourcePort,
                            destIP,
                            destPort,
                            originValue: [{ne_id, source, dest, ...others}]
                        };
                    }
                    return prev;
                }, {});
                const result = Object.values(connObj).map(item => {
                    const {sourceIP, destIP, originValue} = item;
                    item.state =
                        (sourceIP === destIP && originValue.length === 2) ||
                        (sourceIP !== destIP && originValue.length === 4)
                            ? labelList.complete
                            : labelList.partial;
                    return item;
                });
                dispatch(setConnections(result));
                // 树状态变更标志关闭
                dispatch(setTreeItemChangedTag(false));
            } catch (e) {
                // eslint-disable-next-line no-console
                console.log(e);
            }
        });
    };

    const clientRef = useRef(null);
    const clientSelectTransfer = obj => {
        if (clientRef.current) {
            clientRef.current.selectTransfer(obj);
        }
    };
    const tabDef = {
        service_list: {
            id: "service_list",
            title: labelList.optics_service_list,
            content: <ServiceTree serviceType={serviceType} setProcess={setProcess} ref={clientRef} />,
            minWidth: 200,
            group: "default"
        },
        map: {
            id: "map",
            title: labelList.map,
            content: <ConnectMapL7 />,
            group: "default",
            minHeight: 100
        },
        service_view: {
            id: "service_view",
            title: labelList.service_view,
            content: <ServiceViewContainer clientSelectTransfer={clientSelectTransfer} />
        },
        ne: {
            id: "ne",
            title: labelList.ne,
            content: (
                <div className={styles.contentWrap}>
                    <NeList serviceType={serviceType} />
                </div>
            ),
            group: "default"
        },
        alarm: {
            id: "alarm",
            title: labelList.message_alarm,
            content: (
                <div className={styles.contentWrap}>
                    <Alarm head={false} serviceType={serviceType} alarmType="currentAlarm" scroll />
                </div>
            )
        },
        event: {
            id: "event",
            title: labelList.message_event,
            content: (
                <div className={styles.contentWrap}>
                    <Event head={false} serviceType={serviceType} />
                </div>
            )
        },
        resource: {
            id: "resource",
            title: labelList.inventory,
            content: (
                <div className={styles.contentWrap}>
                    <RelatePort serviceType={serviceType} />
                </div>
            )
        },
        fiber: {
            id: "fiber",
            title: labelList.fiber_connection,
            content: (
                <div className={styles.contentWrap}>
                    <Fiber loadData={loadData} />
                </div>
            )
        },
        protection_5: {
            id: "protection_5",
            title: labelList.protection_group,
            content: (
                <div className={styles.contentWrap}>
                    <Aps />
                </div>
            )
        }
    };

    useEffect(() => {
        return () => {
            dispatch(setSelectedItem({}));
            dispatch(setTableFilter({}));
        };
    }, []);

    useEffect(() => {
        setLayout(box);
    }, [labelList]);

    const loadTab = ({id}) => {
        return tabDef[id];
    };

    const {runAsync} = useRequest(apiConnectionByGroup, {manual: true});

    const resizeFunc = () => {};

    useEffect(() => {
        dispatch(
            setSelectedItem({
                id: "otnRoot",
                value: {
                    name: "OTN",
                    nodeType: "otn_group"
                },
                action: 0
            })
        );
        loadData();
        window.addEventListener("resize", resizeFunc);
        setTimeout(resizeFunc, 1000);
        return () => {
            window.removeEventListener("resize", resizeFunc);
            dispatch(setTableFilter({type: "UN_SELECTED"}));
        };
    }, []);

    useEffect(() => {
        setTimeout(resizeFunc, 300);
    }, [activeTab]);

    useEffect(() => {
        const tab = refLayout.current.find("service_view");
        if (provision) {
            if (!tab) {
                refLayout.current.dockMove(tabDef.service_view, "graphic_panel", "middle");
                if (action === "add") {
                    // add service
                } else {
                    refLayout.current.updateTab("map", refLayout.current.find("map"), true);
                }
            }
        } else {
            // eslint-disable-next-line no-lonely-if
            if (tab) {
                refLayout.current.dockMove(tab, null, "remove");
            }
        }
    }, [provision]);

    useEffect(() => {
        if (treeItemChangedTag || action === "add") {
            loadData();
        }
    }, [treeItemChangedTag, action]);

    const groups = {
        card: {floatable: false, tabLocked: true, disableDock: true, maximizable: true, animated: false},
        default: {floatable: false, tabLocked: true, disableDock: true, maximizable: true, animated: false}
    };

    const onLayoutChange = (newLayout, tabName, operation) => {
        // 修复因tabs切换后导致地图事件获取不到父元素高度的问题
        setTimeout(() => {
            const mapEl = document.querySelector("#map");
            if (tabName === "service_view" && operation === "active") {
                mapEl.classList.add("fix-map-tabs-zoom");
            } else if (tabName === "map" && operation === "active") {
                mapEl.classList.remove("fix-map-tabs-zoom");
            }
        }, 300);

        setLayout(newLayout);
        if (operation === "active" && tabName === "service_view") {
            dispatch(setUpdateServiceView());
        }
        if (
            operation === "active" &&
            ["ne", "alarm", "event", "resource", "fiber", "connection", "protection_6", "protection_5", "vcg"].includes(
                tabName
            )
        ) {
            setActiveTab(tabName);
        }
        if (operation === "maximize") setTimeout(resizeFunc, 1000);
        resizeFunc();
    };

    return layout ? (
        <div className={styles.dockLayout}>
            <DockLayout
                ref={refLayout}
                layout={layout}
                groups={groups}
                loadTab={loadTab}
                onLayoutChange={onLayoutChange}
                style={{flex: 1}}
            />
            <ProcessTool processState={process} mask />
        </div>
    ) : null;
};

export default Service;
