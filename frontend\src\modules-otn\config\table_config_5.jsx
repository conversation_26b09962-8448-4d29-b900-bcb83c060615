import {convertToArray, encode<PERSON><PERSON><PERSON><PERSON><PERSON>, sensorPathList} from "@/modules-otn/utils/util";
import {
    apiDelFile,
    apiGetFileList,
    apiRpc,
    getPmFilterInfo,
    getPortUsed,
    netconfGetByXML,
    objectGet
} from "@/modules-otn/apis/api";

export const combinePort = async (ne, card, side) => {
    const supportPort = [];
    const rs = await objectGet("ne:5:component", {
        ne_id: ne,
        parent: card,
        type: "PORT"
    });
    if (rs.total > 0) {
        const cardType = (
            await objectGet("ne:5:component", {
                ne_id: ne,
                name: card
            })
        ).documents[0].value.data.state.type;

        rs.documents.forEach(item => {
            const {name} = item.value.data;
            if (["OA", "WSS", "LINECARD", "OLA"].includes(cardType)) {
                if (
                    (["OA", "OLA"].includes(cardType) && (name.endsWith("IN") || name.endsWith("OUT"))) ||
                    (cardType === "WSS" &&
                        (name.endsWith("PAIN") || name.endsWith("BAOUT") || name.indexOf("AD") > -1)) ||
                    (cardType === "LINECARD" && (side === "C" ? name.indexOf("C") > -1 : name.indexOf("L") > -1))
                ) {
                    supportPort.push(name);
                }
            } else {
                supportPort.push(name);
            }
        });
    }
    const cbPort = [];

    const unSupportPortType = ["OUT", "NM", "MON", "OTDR", "OSC", "ETH", "CON"];

    supportPort.map(async item => {
        const portType = item.split("-").pop();
        if (unSupportPortType.filter(item => portType.indexOf(item) > -1).length > 0) {
            // drop it
        } else if (item.endsWith("BAIN")) {
            const t = `${item} / ${item.replace("BAIN", "PAOUT")}`;
            cbPort.push({label: t, value: item, title: t});
        } else if (item.endsWith("PAIN")) {
            const t = `${item} / ${item.replace("PAIN", "BAOUT")}`;
            cbPort.push({label: t, value: item, title: t});
        } else if (item.endsWith("LA1IN")) {
            const t = `${item} / ${item.replace("LA1IN", "LA2OUT")}`;
            cbPort.push({label: t, value: item, title: t});
        } else if (item.endsWith("LA2IN")) {
            const t = `${item} / ${item.replace("LA2IN", "LA1OUT")}`;
            cbPort.push({label: t, value: item, title: t});
        } else {
            cbPort.push({label: item, value: item, title: item});
        }
    });
    const ports = cbPort.map(item => item.value);
    const _rs = await getPortUsed({ne_id: ne, portName: ports});
    _rs.used.map((item, index) => {
        if (item) {
            for (let i = 0; i < cbPort.length; i++) {
                if (cbPort[i].value === ports[index]) {
                    cbPort[i].used = true;
                    break;
                }
            }
        } else if (ports[index].indexOf("-APS") > -1) {
            let opPortUsed = false;
            for (let i = 0; i < cbPort.length; i++) {
                if (cbPort[i].value.startsWith(ports[index].substring(0, ports[index].length - 1))) {
                    if (_rs.used[i]) {
                        opPortUsed = true;
                        break;
                    }
                }
            }
            if (opPortUsed) {
                for (let i = 0; i < cbPort.length; i++) {
                    if (cbPort[i].value.startsWith(ports[index].substring(0, ports[index].length - 1))) {
                        cbPort[i].used = true;
                    }
                }
            }
        }
    });
    return cbPort;
};

export const config = {
    "ne:5:connection": {
        title: "Connection",
        head: false,
        selectionType: "check",
        operateColumnWidth: 120,
        columns: [
            {dataIndex: "sourceNE", title: "NE_A", width: 150, filter: true, fixed: "left"},
            {dataIndex: "sourcePort", title: "Port_A"},
            {dataIndex: "destNE", title: "NE_B", width: 150, filter: true},
            {dataIndex: "destPort", title: "Port_B"},
            {dataIndex: "state", title: "State"}
        ]
    },
    "ne:5:aps-module": {
        head: false,
        operateColumnWidth: 170,
        columns: [
            {dataIndex: "ne_name", filter: true, width: 130, fixed: "left"},
            {dataIndex: "name", width: 100},
            {dataIndex: "active-path", width: 150},
            {dataIndex: "aps-status", width: 130},
            {dataIndex: "revertive", width: 130},
            {dataIndex: "wait-to-restore-time", title: "wtr-time", unit: "ms", width: 180},
            {dataIndex: "hold-off-time", unit: "ms", width: 180},
            {dataIndex: "primary-switch-threshold", unit: "dBm", width: 260},
            {dataIndex: "primary-switch-hysteresis", unit: "dB", width: 280},
            {dataIndex: "secondary-switch-threshold", unit: "dBm", width: 280},
            {dataIndex: "relative-switch-threshold", unit: "dB", width: 280},
            {dataIndex: "relative-switch-threshold-offset", unit: "dB", width: 280},
            {dataIndex: "force-to-port", width: 150}
        ]
    },
    "ne:sensor-group": {
        columns: [
            {
                dataIndex: "group",
                require: true,
                inputType: "select",
                edit: {
                    show: false
                },
                data: {
                    type: "nms:group",
                    inputKey: "dbKey",
                    titleKeys: ["name"],
                    valueKey: "name",
                    effect: ["ne_id"]
                }
            },
            {
                dataIndex: "ne_id",
                required: true,
                inputType: "select",
                fixed: "left",
                data: {
                    type: "config:ne",
                    valueKey: "ne_id",
                    labelKey: "name",
                    filter: [
                        {key: "type", value: "5"},
                        {filterIndex: "group", dataIndex: "group", required: false}
                    ]
                }
            },
            {
                dataIndex: "sensor-group-id",
                required: true,
                edit: {
                    disabled: true,
                    dataIndex: "data.sensor-group-id"
                }
            },
            {
                dataIndex: "path",
                title: "sensor-paths",
                inputType: "select",
                required: true,
                edit: {
                    dataIndex: "data.sensor-paths.sensor-path.path"
                },
                mode: "multiple",
                data: v => {
                    const selectedPath = v["exclude-filter"] ?? [];
                    const keys = Object.keys(sensorPathList);
                    const _list = [];
                    keys.map(item => {
                        if (!selectedPath.includes(sensorPathList[item].value)) {
                            _list.push({
                                label: item,
                                title: item,
                                value: sensorPathList[item].value
                            });
                        }
                    });
                    return _list;
                }
            },
            // {
            //     dataIndex: "exclude-filter",
            //     inputType: "select",
            //     mode: "multiple",
            //     data: v => {
            //         const selectedPath = v.path ?? [];
            //         const keys = Object.keys(sensorPathList);
            //         const _list = [];
            //         keys.map(item => {
            //             if (!selectedPath.includes(sensorPathList[item].value)) {
            //                 _list.push({
            //                     label: item,
            //                     title: item,
            //                     value: sensorPathList[item].value
            //                 });
            //             }
            //         });
            //         return _list;
            //     }
            // },
            {
                dataIndex: "object-name",
                title: "includes",
                inputType: "select",
                mode: "multiple",
                sortFun: (a, b) => a.localeCompare(b, "ZH-CN", {numeric: true}),
                data: async v => {
                    if (!v.ne_id) {
                        return [];
                    }
                    const selectedPath = v.path ?? [];
                    const _list = [];
                    for (let i = 0; i < selectedPath.length; i++) {
                        const sensorType = encodeSensorPath(selectedPath[i]);
                        const {type, filter} = sensorPathList[sensorType].request;
                        const rs = (await objectGet(type, {...filter, ne_id: v.ne_id})).documents;
                        rs.map(item => {
                            if (sensorType === "ethernet" || sensorType === "otn") {
                                if (item.value.data[sensorType]) {
                                    _list.push(
                                        item.value.data?.state?.description ?? item.value.data?.config?.description
                                    );
                                }
                            } else {
                                _list.push(item.value.data.name);
                            }
                        });
                    }
                    return _list;
                }
            }
        ],
        resetConfig: {
            ne_id: ["path", "object-name", "sensor-group-id"]
        }
    },
    "sensor-group": {
        title: "sensor_group",
        columns: [
            {
                dataIndex: "ne_name",
                fixed: "left",
                filter: true
            },
            {
                dataIndex: "sensor-group-id"
            },
            {
                dataIndex: "sensor-paths"
            },
            {dataIndex: "includes"}
        ]
    },
    "destination-groups": {
        title: "destination_group",
        columns: [
            {
                dataIndex: "ne_name",
                fixed: "left",
                filter: true
            },
            {dataIndex: "group-id"},
            {dataIndex: "destinations"}
        ]
    },
    "create-destination-groups": {
        title: "create_destination_group",
        columns: [
            {
                dataIndex: "group",
                require: true,
                inputType: "select",
                data: {
                    type: "nms:group",
                    inputKey: "dbKey",
                    titleKeys: ["name"],
                    valueKey: "name",
                    effect: ["ne_id"]
                }
            },
            {
                dataIndex: "ne_id",
                required: true,
                inputType: "select",
                data: {
                    type: "config:ne",
                    valueKey: "ne_id",
                    labelKey: "name",
                    filter: [
                        {key: "type", value: "5"},
                        {filterIndex: "group", dataIndex: "group", required: false}
                    ]
                }
            },
            {dataIndex: "group-id", required: true},
            {
                dataIndex: "default_tnms_server",
                inputType: "checkbox",
                defaultValue: [true]
            },
            {
                dataIndex: "destinations",
                // inputType: "select",
                disabled: v => {
                    if (v.default_tnms_server === undefined) {
                        return true;
                    }
                    return v.default_tnms_server;
                },
                placeholder: "entry_ip_port"
                // mode: "tags"
                // pattern: /((2(5[0-5]|[0-4]\d))|1?\d{1,2})(\.((2(5[0-5]|[0-4]\d))|1?\d{1,2})){3}:\d{1,5}/g,
                // message: "ip_patter"
            }
        ]
    },
    "persistent-subscription": {
        title: "subscription",
        columns: [
            {
                dataIndex: "ne_name",
                filter: true,
                fixed: "left"
            },
            {dataIndex: "name"},
            // {dataIndex: "id"},
            // {dataIndex: "local-source-address"},
            // {dataIndex: "local-source-port"},
            // {dataIndex: "originated-qos-marking"},
            {dataIndex: "protocol"},
            {dataIndex: "encoding"},
            {dataIndex: "sensor-profiles"},
            {dataIndex: "sample-interval"},
            {dataIndex: "heartbeat-interval"},
            {dataIndex: "suppress-redundant"},
            {dataIndex: "destination-groups"}
        ]
    },
    "create-persistent-subscription": {
        title: "create-persistent-subscription",
        columns: [
            {
                dataIndex: "group",
                require: true,
                inputType: "select",
                data: {
                    type: "nms:group",
                    inputKey: "dbKey",
                    titleKeys: ["name"],
                    valueKey: "name",
                    effect: ["ne_id", "sensor-profiles", "destination-groups"]
                }
            },
            {
                dataIndex: "ne_id",
                required: true,
                inputType: "select",
                data: {
                    type: "config:ne",
                    valueKey: "ne_id",
                    labelKey: "name",
                    filter: [
                        {key: "type", value: "5"},
                        {filterIndex: "group", dataIndex: "group", required: false}
                    ],
                    effect: ["sensor-profiles", "destination-groups"]
                }
            },
            {dataIndex: "name", required: true},
            // {dataIndex: "id"},
            // {
            //     dataIndex: "local-source-address",
            //     pattern: /((2(5[0-5]|[0-4]\d))|1?\d{1,2})(\.((2(5[0-5]|[0-4]\d))|1?\d{1,2})){3}/g,
            //     message: "ip_patter"
            // },
            // {
            //     dataIndex: "local-source-port",
            //     pattern: /((^[1-5]?\d{1,4}$)|(^6([0-4]\d{3}|(5([0-4]\d{2}|(5([0-2]\d|3[0-5])))))$))/g,
            //     message: "number_patter"
            // },
            // {dataIndex: "originated-qos-marking"},
            {
                dataIndex: "protocol",
                inputType: "select",
                defaultValue: "STREAM_GRPC",
                required: true,
                data: {
                    options: ["STREAM_GRPC"]
                }
            },
            {
                dataIndex: "encoding",
                required: true,
                inputType: "select",
                defaultValue: "ENC_PROTO3",
                data: {
                    options: ["ENC_PROTO3"]
                }
            },
            {
                dataIndex: "sensor-profiles",
                inputType: "select",
                required: true,
                mode: "multiple",
                data: {
                    type: "ne:5:sensor-group",
                    filter: [{filterIndex: "ne_id", dataIndex: "ne_id"}],
                    valueKey: "data.sensor-group-id"
                }
            },
            {
                dataIndex: "sample-interval",
                required: true,
                defaultValue: "1000",
                unit: "MS"
            },
            {
                dataIndex: "heartbeat-interval",
                required: true,
                defaultValue: "10",
                unit: "S"
            },
            {
                dataIndex: "suppress-redundant",
                inputType: "select",
                defaultValue: "false",
                data: {
                    options: ["false", "true"]
                }
            },
            {
                dataIndex: "destination-groups",
                inputType: "select",
                required: true,
                mode: "multiple",
                data: {
                    type: "ne:5:destination-group",
                    filter: [{filterIndex: "ne_id", dataIndex: "ne_id"}],
                    valueKey: "data.group-id"
                }
            }
        ]
    },
    pmp: {
        head: false,
        selectionType: "check",
        columns: [
            {dataIndex: "pm-point", defaultSortOrder: "ascend", width: "calc(100% / 3)"},
            {
                dataIndex: "pm-point-enable",
                filter: true,
                width: "calc(100% / 3)",
                filters: [
                    {text: "TRUE", value: "true"},
                    {text: "FALSE", value: "false"}
                ],
                onFilter: (value, record) => record["pm-point-enable"] === value
            },
            {
                dataIndex: "tca-enable",
                filter: true,
                width: "calc(100% / 3)",
                filters: [
                    {text: "TRUE", value: "true"},
                    {text: "FALSE", value: "false"}
                ],
                onFilter: (value, record) => record["tca-enable"] === value
            }
        ]
    },
    "port-manager": {
        head: false,
        columns: [
            {
                dataIndex: "port",
                fixed: "left"
            },
            {
                dataIndex: "signal-type&config",
                colSpan: 2,
                title: "signal-type"
            },
            {
                dataIndex: "signal-type&state",
                colSpan: 0,
                title: "signal-type"
            },
            {
                dataIndex: "module-type"
            },
            {
                dataIndex: "loopback&config",
                colSpan: 2,
                title: "loopback"
            },
            {
                dataIndex: "loopback&state",
                colSpan: 0,
                title: "loopback"
            },
            {
                dataIndex: "fec&config",
                colSpan: 2,
                title: "fec"
            },
            {
                dataIndex: "fec&state",
                colSpan: 0,
                title: "fec"
            },
            {
                dataIndex: "laser-manual-enable&config",
                colSpan: 2,
                title: "laser-enable"
            },
            {
                dataIndex: "laser-manual-enable&state",
                colSpan: 0,
                title: "laser-enable"
            },
            {
                dataIndex: "als&config",
                width: 150,
                colSpan: 2,
                title: "als"
            },
            {
                dataIndex: "als&state",
                colSpan: 0,
                title: "als"
            },
            {
                dataIndex: "reverse-mode&config",
                colSpan: 2,
                title: "reverse-mode"
            },
            {
                dataIndex: "reverse-mode&state",
                colSpan: 0,
                title: "reverse-mode"
            }
        ]
    },
    "och-manager": {
        head: false,
        columns: [
            {
                dataIndex: "och",
                fixed: "left"
            },
            {
                dataIndex: "operational-mode&config",
                colSpan: 2,
                width: 240,
                title: "operational-mode"
            },
            {
                dataIndex: "operational-mode&state",
                colSpan: 0,
                title: "operational-mode"
            },
            {
                dataIndex: "frequency&config",
                colSpan: 2,
                title: "frequency"
            },
            {
                dataIndex: "frequency&state",
                colSpan: 0,
                title: "frequency"
            },
            {
                dataIndex: "target-output-power&config",
                unit: "dBm",
                colSpan: 2,
                title: "target-output-power"
            },
            {
                dataIndex: "target-output-power&state",
                colSpan: 0,
                title: "target-output-power"
            },
            {
                dataIndex: "pre-fec"
            },
            {
                dataIndex: "post-fec"
            }
        ]
    },
    "otn-tti": {
        head: false,
        columns: [
            {
                dataIndex: "port",
                width: 100,
                fixed: "left"
            },
            {
                dataIndex: "signal-type",
                width: 150,
                fixed: "left"
            },
            {
                dataIndex: "sm-tti-msg-transmit-sapi",
                width: 130
            },
            {
                dataIndex: "sm-tti-msg-transmit-dapi",
                width: 130
            },
            {
                dataIndex: "sm-tti-msg-transmit-oper",
                width: 130
            },
            {
                dataIndex: "sm-tti-msg-expected-sapi",
                width: 130
            },
            {
                dataIndex: "sm-tti-msg-expected-dapi",
                width: 130
            },
            {
                dataIndex: "sm-tti-msg-expected-oper",
                width: 130
            },
            {
                dataIndex: "sm-tti-msg-recv-sapi",
                width: 130
            },
            {
                dataIndex: "sm-tti-msg-recv-dapi",
                width: 130
            },
            {
                dataIndex: "sm-tti-msg-recv-oper",
                width: 130
            },
            {
                dataIndex: "pm-tti-msg-transmit-sapi",
                width: 130
            },
            {
                dataIndex: "pm-tti-msg-transmit-dapi",
                width: 130
            },
            {
                dataIndex: "pm-tti-msg-transmit-oper",
                width: 130
            },
            {
                dataIndex: "pm-tti-msg-expected-sapi",
                width: 130
            },
            {
                dataIndex: "pm-tti-msg-expected-dapi",
                width: 130
            },
            {
                dataIndex: "pm-tti-msg-expected-oper",
                width: 130
            },
            {
                dataIndex: "pm-tti-msg-recv-sapi",
                width: 130
            },
            {
                dataIndex: "pm-tti-msg-recv-dapi",
                width: 130
            },
            {
                dataIndex: "pm-tti-msg-recv-oper",
                width: 130
            }
        ]
    },
    "lldp-state": {
        head: false,
        columns: [
            {
                dataIndex: "chassis-id"
            },
            {
                dataIndex: "chassis-id-type"
            },
            {
                dataIndex: "enabled&config",
                title: "enabled",
                colSpan: 2
            },
            {
                dataIndex: "enabled&state",
                title: "enabled",
                colSpan: 0
            },
            {
                dataIndex: "hello-timer",
                unit: "seconds"
            },
            {
                dataIndex: "system-name"
            },
            {
                dataIndex: "system-description"
            }
        ]
    },
    "lldp-interfaces": {
        head: false,
        pagination: false,
        columns: [
            {
                dataIndex: "name"
            },
            {
                dataIndex: "enabled&config",
                title: "enabled",
                colSpan: 2
            },
            {
                dataIndex: "enabled&state",
                title: "enabled",
                colSpan: 0
            },
            {
                dataIndex: "system-name"
            },
            {
                dataIndex: "system-description"
            },
            {
                dataIndex: "chassis-id"
            },
            {
                dataIndex: "chassis-id-type"
            },
            {
                dataIndex: "age"
            },
            {
                dataIndex: "last-update"
            },
            {
                dataIndex: "ttl"
            },
            {
                dataIndex: "port-id"
            },
            {
                dataIndex: "port-id-type"
            },
            {
                dataIndex: "port-description"
            },
            {
                dataIndex: "management-address"
            },
            {
                dataIndex: "management-address-type"
            }
        ]
    },
    alarm_mask: {
        selectionType: "checkbox",
        columns: [
            {dataIndex: "name", title: "ne_name", filter: true},
            {dataIndex: "index"},
            {dataIndex: "object-range", filter: true},
            {dataIndex: "object-name", filter: true}
        ]
    },
    create_alarm_mask: {
        title: "create_alarm_mask",
        columns: [
            {
                dataIndex: "group",
                require: true,
                inputType: "select",
                data: {
                    type: "nms:group",
                    inputKey: "dbKey",
                    titleKeys: ["name"],
                    valueKey: "name",
                    effect: ["ne_id", "object-name"]
                }
            },
            {
                dataIndex: "ne_id",
                title: "ne_name",
                required: true,
                inputType: "select",
                data: {
                    type: "config:ne",
                    valueKey: "ne_id",
                    labelKey: "name",
                    filter: [
                        {filterIndex: "group", dataIndex: "group", required: false},
                        {key: "type", value: "5"}
                    ],
                    effect: ["object-name"]
                }
            },
            {dataIndex: "index", required: true, pattern: "^[0-9a-zA-Z_]{1,15}$", message: "alarm_mask_index_pattern"},
            {
                dataIndex: "object-range",
                required: true,
                defaultValue: "",
                inputType: "select",
                data: {
                    options: ["CHASSIS", "CARD", "PORT"],
                    effect: ["object-name"]
                }
            },
            {
                dataIndex: "object-name",
                required: true,
                inputType: "select",
                sortFun: (a, b) => a.localeCompare(b, "ZH-CN", {numeric: true}),
                data: async data => {
                    if (data.ne_id && data["object-range"]) {
                        if (data["object-range"] === "CHASSIS") {
                            const ports = new Set();
                            (
                                await objectGet("ne:5:component", {
                                    ne_id: data.ne_id,
                                    type: "CHASSIS"
                                })
                            ).documents.map(item => {
                                ports.add(item.value.data.state.name);
                            });
                            return [...ports];
                        }
                        if (data["object-range"] === "CARD") {
                            const cardType = [
                                "LINECARD",
                                "TFF",
                                "WSS",
                                "OA",
                                "OLP",
                                "OLA",
                                "OTDR_CARD",
                                "POWER_SUPPLY",
                                "FAN",
                                "MCU"
                            ];
                            const cards = new Set();
                            (
                                await objectGet("ne:5:component", {
                                    ne_id: data.ne_id,
                                    parent: "CHASSIS-1"
                                })
                            ).documents.map(item => {
                                if (cardType.includes(item.value.data.state.type)) {
                                    cards.add(item.value.data.state.name);
                                }
                            });
                            return [...cards];
                        }
                        if (data["object-range"] === "PORT") {
                            const ports = new Set();
                            (await objectGet("ne:5:component", {ne_id: data.ne_id, type: "PORT"})).documents.map(
                                item => {
                                    ports.add(item.value.data.state.name);
                                }
                            );
                            return [...ports];
                        }
                    }
                    return [];
                }
            }
        ]
    },
    connection: {
        columns: [
            {
                dataIndex: "source_group",
                inputType: "select",
                data: {
                    type: "nms:group",
                    inputKey: "dbKey",
                    titleKeys: ["name"],
                    valueKey: "name"
                }
            },
            {
                dataIndex: "source_ne",
                required: true,
                inputType: "select",
                data: {
                    type: "config:ne",
                    valueKey: "ne_id",
                    filter: [{filterIndex: "source_group", dataIndex: "group", required: false}],
                    effect: ["source_card", "source_port"]
                }
            },
            {
                dataIndex: "source_card",
                required: true,
                inputType: "select",
                data: {
                    type: "ne:5:component",
                    filter: [
                        {filterIndex: "source_ne", dataIndex: "ne_id"},
                        {key: "parent", value: "CHASSIS-1"}
                    ],
                    exclude: [
                        {
                            key: "data.state.type",
                            value: [
                                "OTDR_CARD",
                                "SLOT",
                                "POWER_SUPPLY",
                                "FAN",
                                "CONTROLLER_CARD",
                                "PTM",
                                "PTMW",
                                "PTME",
                                "OCM_CARD"
                            ]
                        }
                    ],
                    effect: ["source_port"],
                    valueKey: "data.name"
                }
            },
            {
                dataIndex: "source_port",
                required: true,
                inputType: "select",
                data: data => {
                    return combinePort(data.source_ne, data.source_card);
                }
            },
            {
                dataIndex: "dest_group",
                inputType: "select",
                data: {
                    type: "nms:group",
                    inputKey: "dbKey",
                    titleKeys: ["name"],
                    valueKey: "name"
                }
            },
            {
                dataIndex: "dest_ne",
                required: true,
                inputType: "select",
                data: {
                    type: "config:ne",
                    valueKey: "ne_id",
                    filter: [{filterIndex: "dest_group", dataIndex: "group", required: false}],
                    effect: ["dest_card", "dest_port"]
                    // exclude: [{key: "ne_id", value: "$.source_ne"}]
                }
            },
            {
                dataIndex: "dest_card",
                required: true,
                inputType: "select",
                data: {
                    type: "ne:5:component",
                    filter: [
                        {filterIndex: "dest_ne", dataIndex: "ne_id"},
                        {key: "parent", value: "CHASSIS-1"}
                    ],
                    effect: ["dest_port"],
                    exclude: [
                        {
                            key: "data.state.type",
                            value: [
                                "OTDR_CARD",
                                "SLOT",
                                "POWER_SUPPLY",
                                "FAN",
                                "CONTROLLER_CARD",
                                "PTM",
                                "PTMW",
                                "PTME",
                                "OCM_CARD"
                            ]
                        }
                    ],
                    valueKey: "data.name"
                }
            },
            {
                dataIndex: "dest_port",
                required: true,
                inputType: "select",
                data: data => {
                    return combinePort(data.dest_ne, data.dest_card);
                }
            }
        ]
    },
    log: {
        title: "log",
        selectionType: "checkbox",
        columns: [{dataIndex: "name"}, {dataIndex: "time-created", defaultSortOrder: "descend"}],
        operateColumnWidth: 160
    },
    database: {
        title: "data_base",
        columns: [
            {dataIndex: "name", width: 280, fixed: "left"},
            {dataIndex: "type", width: 90},
            {dataIndex: "size", unit: "KiB", width: 90},
            {dataIndex: "time-created", width: 120}
        ],
        selectionType: "radio",
        operateColumnWidth: 120,
        delAPI: {
            APIName: apiDelFile,
            APIParameter: data => {
                return {
                    file: data.name,
                    type: "data"
                };
            }
        },
        dataAPI: {
            APIName: apiGetFileList,
            APIParameter: () => {
                return {
                    type: "data"
                };
            }
        },
        uploadAPI: {
            APIName: "../../otn/api/file/upload?type=data"
        }
    },
    startOTDR: {
        title: "test_config",
        columns: [
            {
                dataIndex: "ne",
                required: true,
                inputType: "select",
                data: {
                    type: "config:ne",
                    valueKey: "ne_id",
                    labelKey: "name",
                    filter: [{key: "type", value: "5"}],
                    effect: ["card", "name", "active-local-port", "measuring-state", "otdr-wavelength"]
                },
                covertDefault: async () => {
                    return {"fiber-refractive-index": "G.652"};
                }
            },
            {
                dataIndex: "card",
                inputType: "select",
                required: true,
                data: {
                    type: "ne:5:component",
                    filter: [
                        {filterIndex: "ne", dataIndex: "ne_id"},
                        {key: "type", value: "OTDR_CARD"}
                    ],
                    effect: ["ne_id", "active-local-port", "measuring-state", "otdr-wavelength"],
                    valueKey: "data.name"
                },
                covertDefault: async data => {
                    if (data.ne && data.card) {
                        const rs = await objectGet("ne:5:otdr", {ne_id: data.ne, name: `${data.card}-1`});
                        return {...rs.documents[0].value.data.state, "fiber-refractive-index": "G.652"};
                    }
                    return {};
                }
            },
            {dataIndex: "name", create: {disabled: true}},
            {
                dataIndex: "active-local-port",
                required: true,
                inputType: "select",
                data: {
                    type: "ne:5:component",
                    filter: [
                        {filterIndex: "ne", dataIndex: "ne_id"},
                        {filterIndex: "card", dataIndex: "parent"},
                        {key: "type", value: "PORT"}
                    ],
                    valueKey: "data.name"
                },
                description:
                    "Indicates the port on which monitoring is being performed. \n" +
                    "For example, if the OCM has eight monitoring ports, you \n" +
                    "need to switch the optical switch to this port to perform \n" +
                    "OCM scanning.This parameter is required only for independent \n" +
                    "OCM linecards"
            },
            {
                dataIndex: "fiber-refractive-index",
                // required: true,
                defaultValue: "G.652",
                disabled: true,
                description: "depend on the type of fiber, default is G.652"
            },
            {dataIndex: "measuring-state", create: {disabled: true}},
            {
                dataIndex: "otdr-wavelength",
                create: {disabled: true},
                description: " the wavelength of the OTDR laser, example 1610nm"
            },
            {
                dataIndex: "measure-mode",
                required: true,
                defaultValue: "AUTO",
                inputType: "select",
                data: {
                    options: ["AUTO", "MANUAL"]
                },
                description: "generally, otdr support auto and manual mode"
            },
            {
                dataIndex: "pulse-width",
                required: true,
                defaultValue: "20000",
                unit: "nanoseconds",
                description: "pulse width of otdr laser",
                disabled: v => {
                    return v["measure-mode"] !== "MANUAL";
                }
            },
            {
                dataIndex: "measuring-range",
                required: true,
                defaultValue: "80",
                unit: "kilometers",
                disabled: v => {
                    return v["measure-mode"] !== "MANUAL";
                }
            },
            {
                dataIndex: "measuring-time",
                required: true,
                defaultValue: "120",
                unit: "seconds",
                disabled: v => {
                    return v["measure-mode"] !== "MANUAL";
                }
            }
        ]
    },
    resultOTDR: {
        title: "otdr_result",
        columns: [
            {
                dataIndex: "ne",
                required: true,
                inputType: "select",
                data: {
                    type: "config:ne",
                    valueKey: "ne_id",
                    filter: [{key: "type", value: "5"}],
                    labelKey: "name",
                    effect: ["card", "name", "index"]
                }
            },
            {
                dataIndex: "card",
                required: true,
                inputType: "select",
                data: {
                    type: "ne:5:component",
                    filter: [
                        {filterIndex: "ne", dataIndex: "ne_id"},
                        {key: "type", value: "OTDR_CARD"}
                    ],
                    valueKey: "data.name",
                    effect: ["name", "index"]
                },
                covertDefault: async data => {
                    if (data.ne && data.card) {
                        const rs = await objectGet("ne:5:otdr", {ne_id: data.ne, name: `${data.card}-1`});
                        return rs.documents[0].value.data.state;
                    }
                    return {};
                }
            },
            {dataIndex: "name", create: {disabled: true}},
            {
                dataIndex: "index",
                title: "detect-time",
                required: true,
                inputType: "select",
                // data: {
                //     type: "ne:5:otdr",
                //     filter: [
                //         {filterIndex: "ne", dataIndex: "ne_id"},
                //         {filterIndex: "name", dataIndex: "name"}
                //     ],
                //     valueKey: "data.results.result.detect-time",
                //     titleKeys: ["monitor-port"],
                //     inputKey: "index"
                // }
                data: async v => {
                    const rs = await netconfGetByXML({
                        ne_id: v.ne,
                        msg: true,
                        xml: {
                            otdrs: {
                                $: {
                                    xmlns: "http://openconfig.net/yang/otdr"
                                },
                                otdr: {
                                    name: v.name,
                                    config: {},
                                    results: {
                                        result: {}
                                    }
                                }
                            }
                        }
                    });
                    const _data = [];
                    convertToArray(rs?.otdrs?.otdr?.results?.result)?.map(item => {
                        _data.push({
                            label: item["detect-time"],
                            title: `${item["detect-time"]} (${item["monitor-port"]})`,
                            value: item.index
                        });
                    });
                    return _data;
                }
            }
        ]
    },
    ocm: {
        title: "OCM",
        columns: [
            {dataIndex: "channel"},
            {dataIndex: "lower-frequency", unit: "THz"},
            {dataIndex: "upper-frequency", unit: "THz"},
            {dataIndex: "power", unit: "dBm"}
        ]
    },
    result_ocm: {
        columns: [
            {
                dataIndex: "ne",
                required: true,
                inputType: "select",
                data: {
                    type: "config:ne",
                    valueKey: "ne_id",
                    filter: [{key: "type", value: "5"}],
                    labelKey: "name"
                }
            },
            {
                dataIndex: "card",
                required: true,
                inputType: "select",
                data: {
                    type: "ne:5:component",
                    filter: [
                        {filterIndex: "ne", dataIndex: "ne_id"},
                        {key: "type", value: "OCM_CARD"}
                    ],
                    effect: ["name", "active-local-port", "lower-frequency", "upper-frequency", "channel-interval"],
                    valueKey: "data.name"
                },
                covertDefault: async data => {
                    if (data.ne && data.card) {
                        const rs = await objectGet("ne:5:channel-monitor", {ne_id: data.ne, name: `${data.card}-1`});
                        return rs.documents[0].value.data.state;
                    }
                    return {};
                }
            },
            {dataIndex: "name", create: {disabled: true}}
        ]
    },
    test_ocm: {
        title: "test_ocm",
        columns: [
            {
                dataIndex: "ne",
                required: true,
                inputType: "select",
                data: {
                    type: "config:ne",
                    valueKey: "ne_id",
                    filter: [{key: "type", value: "5"}],
                    labelKey: "name",
                    effect: [
                        "card",
                        "name",
                        "active-local-port",
                        // "lower-frequency",
                        // "upper-frequency",
                        "channel-interval"
                    ]
                }
            },
            {
                dataIndex: "card",
                required: true,
                inputType: "select",
                data: {
                    type: "ne:5:component",
                    filter: [
                        {filterIndex: "ne", dataIndex: "ne_id"},
                        {key: "type", value: "OCM_CARD"}
                    ],
                    effect: ["name", "active-local-port", "channel-interval", "power-offset"],
                    valueKey: "data.name"
                },
                covertDefault: async data => {
                    if (data.ne && data.card) {
                        const rs = await objectGet("ne:5:channel-monitor", {ne_id: data.ne, name: `${data.card}-1`});
                        const info = rs.documents[0].value.data.config;
                        const rs2 = await objectGet("ne:5:component", {
                            ne_id: data.ne,
                            name: info["active-local-port"]
                        });
                        const offset =
                            rs2.documents?.[0]?.value?.data?.port?.["optical-port"]?.config?.["power-offset"];
                        if (offset) {
                            info["power-offset"] = offset;
                            if (["20.00", "23.00"].includes(offset)) {
                                info["power-offset-select"] = offset;
                            } else {
                                info["power-offset-select"] = "-customize-";
                            }
                        }
                        return info;
                    }
                    return {};
                }
            },
            {dataIndex: "name", create: {disabled: true}},
            {
                dataIndex: "active-local-port",
                required: true,
                inputType: "select",
                data: {
                    type: "ne:5:component",
                    filter: [
                        {filterIndex: "ne", dataIndex: "ne_id"},
                        {filterIndex: "card", dataIndex: "parent"},
                        {key: "type", value: "PORT"}
                    ],
                    effect: ["power-offset"],
                    valueKey: "data.name"
                },
                covertDefault: async data => {
                    const rs2 = await objectGet("ne:5:component", {
                        ne_id: data.ne,
                        name: data["active-local-port"]
                    });
                    const offset =
                        rs2.documents?.[0]?.value?.data?.port?.["optical-port"]?.config?.["power-offset"] ?? "";

                    if (offset) {
                        const info = {
                            "power-offset": offset
                        };
                        info["power-offset"] = offset;
                        if (["20.00", "23.00"].includes(offset)) {
                            info["power-offset-select"] = offset;
                        } else {
                            info["power-offset-select"] = "-customize-";
                        }
                        return info;
                    }
                    return {};
                }
            },
            // {
            //     dataIndex: "lower-frequency",
            //     required: true
            // },
            // {
            //     dataIndex: "upper-frequency",
            //     required: true
            // },
            {
                dataIndex: "channel-interval",
                required: true,
                inputType: "select",
                data: {
                    options: ["CHANNEL_50G", "CHANNEL_75G", "CHANNEL_100G"]
                }
            },
            {
                dataIndex: "power-offset-select",
                title: "power-offset",
                width: 138,
                required: true,
                allowClear: false,
                inputType: "select",
                covertDefault: async v => ({
                    "power-offset": ["20.00", "23.00"].includes(v?.["power-offset-select"])
                        ? v["power-offset-select"]
                        : ""
                })
            },
            {
                dataIndex: "power-offset",
                title: " ",
                style: {marginTop: -61, width: 138, marginLeft: 142},
                inputType: "number",
                unit: "dB",
                step: 0.01,
                disabled: v => ["20.00", "23.00"].includes(v?.["power-offset-select"])
            }
        ]
    },
    "reset-counter": {
        title: "reset-counter",
        addAPI: {
            API: async values => {
                return await apiRpc({
                    ne_id: values.ne_id,
                    rpcName: "reset-counter",
                    rpcConfig: {
                        "pm-point": values["pm-point"],
                        "pm-point-type": values["pm-point-type"]
                    }
                });
            },
            successMessage: "reset_success",
            failMessage: "reset_fail_warning"
        },
        columns: [
            {
                dataIndex: "group",
                inputType: "select",
                data: {
                    type: "nms:group",
                    inputKey: "dbKey",
                    titleKeys: ["name"],
                    valueKey: "name",
                    effect: ["ne_id"]
                }
            },
            {
                dataIndex: "ne_id",
                title: "ne_name",
                required: true,
                inputType: "select",
                data: {
                    type: "config:ne",
                    valueKey: "ne_id",
                    labelKey: "name",
                    filter: [
                        {key: "type", value: "5"},
                        {filterIndex: "group", dataIndex: "group", required: false}
                    ]
                }
            },
            {
                dataIndex: "pm-point-type",
                inputType: "select",
                data: {
                    options: [
                        "CHASSIS",
                        "CARD",
                        "PORT",
                        "TRANSCEIVER",
                        "OPTICAL-CHANNEL",
                        "PHYSCIAL-CHANNEL",
                        "OLP",
                        "AMPLIFIER",
                        "OTU",
                        "ODU",
                        "ETH",
                        "OSC",
                        "OCM",
                        "ALL"
                    ]
                }
            },
            {
                dataIndex: "pm-point",
                inputType: "select",
                data: async data => {
                    const filter = {
                        action: "getPmPoint",
                        ne_id: data.ne_id
                    };
                    if (data["pm-point-type"]) {
                        filter["pm-point-type"] = data["pm-point-type"];
                    }
                    const rs = await getPmFilterInfo(filter);
                    return rs.data;
                }
                // data: {
                //     type: "ne:pmp",
                //     filter: [{filterIndex: "ne_id", dataIndex: "ne_id"}],
                //     valueKey: "data.pm-point"
                // }
            }
        ]
    },
    raman_manager: {
        head: false
    },
    edfa_manager: {
        head: false
    },
    osc_manager: {
        head: false
    },
    wss_manager: {
        head: false
    },
    tff_manager: {
        head: false
    },
    aps_manager: {
        head: false
    },
    ocm_manager: {
        head: false
    },
    port_list: {
        head: false,
        pagination: false
    },
    port_manager: {
        head: false,
        pagination: false
    },
    "otn-tti_manager": {
        head: false,
        pagination: false
    },
    lldp_manager: {
        head: false,
        pagination: false
    },
    power_manager: {
        // head: false,
        columns: [
            {dataIndex: "index", title: "NO.", width: 70, fixed: "left"},
            {dataIndex: "ne-name", width: 120},
            {dataIndex: "ne_id", title: "NE Addr.", width: 150},
            {dataIndex: "card-type", width: 120},
            {dataIndex: "card-name", width: 120},
            {dataIndex: "slot-no", title: "Slot NO.", width: 110},
            {dataIndex: "port-name", width: 140},
            {dataIndex: "voa-attenuation", width: 160},
            {dataIndex: "input-optical-power", unit: "dBm", width: 240},
            {dataIndex: "output-optical-power", unit: "dBm", width: 240},
            {
                dataIndex: "overlow-input-threshold-15min",
                title: "overlow-input-threshold",
                unit: "dBm",
                width: 240
            },
            {
                dataIndex: "overhigh-input-threshold-15min",
                title: "overhigh-input-threshold",
                unit: "dBm",
                width: 260
            },
            {
                dataIndex: "overlow-output-threshold-15min",
                title: "overlow-output-threshold",
                unit: "dBm",
                width: 260
            },
            {
                dataIndex: "overhigh-output-threshold-15min",
                title: "overhigh-output-threshold",
                unit: "dBm",
                width: 260
            }
        ]
    },
    power_manager_linecard: {
        head: false,
        columns: [
            {dataIndex: "index", title: "NO.", width: 70, fixed: "left"},
            {dataIndex: "ne-name", width: 120},
            {dataIndex: "ne_id", title: "NE Addr.", width: 150},
            {dataIndex: "card-type", width: 120},
            {dataIndex: "card-name", width: 120},
            {dataIndex: "slot-no", title: "Slot NO.", width: 110},
            {dataIndex: "port-name", width: 140},
            {dataIndex: "input-optical-power", unit: "dBm", width: 160},
            {dataIndex: "output-optical-power", unit: "dBm", width: 160},
            {
                dataIndex: "overlow-input-threshold-15min",
                title: "overlow-input-threshold",
                unit: "dBm",
                width: 160
            },
            {
                dataIndex: "overhigh-input-threshold-15min",
                title: "overhigh-input-threshold",
                unit: "dBm",
                width: 160
            },
            {
                dataIndex: "overlow-output-threshold-15min",
                title: "overlow-output-threshold",
                unit: "dBm",
                width: 160
            },
            {
                dataIndex: "overhigh-output-threshold-15min",
                title: "overhigh-output-threshold",
                unit: "dBm",
                width: 160
            }
        ]
    },
    LINECARD_info: {
        ok: false,
        columns: [
            {
                dataIndex: "part-no",
                edit: {
                    disabled: true
                }
            },
            {
                dataIndex: "temperature",
                title: "card-temperature",
                unit: "celsius",
                edit: {
                    disabled: true
                }
            },
            {
                dataIndex: "used-power",
                title: "actual-power",
                unit: "watts",
                edit: {
                    disabled: true
                }
            },
            {
                dataIndex: "mfg-name",
                title: "manufacturer",
                edit: {
                    disabled: true
                }
            },
            {
                dataIndex: "hardware-version",
                edit: {
                    disabled: true
                }
            },
            {
                dataIndex: "firmware-version",
                edit: {
                    disabled: true
                }
            },
            {
                dataIndex: "software-version",
                edit: {
                    disabled: true
                }
            },
            {
                dataIndex: "mfg-date",
                title: "production-date",
                edit: {
                    disabled: true
                }
            }
        ]
    },
    AMPLIFIER_info: {
        ok: false,
        columns: [
            {
                dataIndex: "part-no",
                edit: {
                    disabled: true
                }
            },
            {
                dataIndex: "temperature",
                title: "card-temperature",
                unit: "celsius",
                edit: {
                    disabled: true
                }
            },
            {
                dataIndex: "used-power",
                title: "actual-power",
                unit: "watts",
                edit: {
                    disabled: true
                }
            },
            {
                dataIndex: "ba-output-power",
                title: "BA/LA1-output-power",
                unit: "dBm",
                edit: {
                    disabled: true
                }
            },
            {
                dataIndex: "pa-output-power",
                title: "PA/LA2-output-power",
                unit: "dBm",
                edit: {
                    disabled: true
                }
            },
            {
                dataIndex: "ba-actual-gain",
                title: "BA/LA1-actual-gain",
                unit: "dBm",
                edit: {
                    disabled: true
                }
            },
            {
                dataIndex: "pa-actual-gain",
                title: "PA/LA2-actual-gain",
                unit: "dBm",
                edit: {
                    disabled: true
                }
            },
            {
                dataIndex: "mfg-name",
                title: "manufacturer",
                edit: {
                    disabled: true
                }
            },
            {
                dataIndex: "hardware-version",
                edit: {
                    disabled: true
                }
            },
            {
                dataIndex: "firmware-version",
                edit: {
                    disabled: true
                }
            },
            {
                dataIndex: "software-version",
                edit: {
                    disabled: true
                }
            },
            {
                dataIndex: "mfg-date",
                title: "production-date",
                edit: {
                    disabled: true
                }
            }
        ]
    },
    OLP_info: {
        ok: false,
        columns: [
            {
                dataIndex: "part-no",
                edit: {
                    disabled: true
                }
            },
            {
                dataIndex: "temperature",
                title: "card-temperature",
                unit: "celsius",
                edit: {
                    disabled: true
                }
            },
            {
                dataIndex: "used-power",
                title: "actual-power",
                unit: "watts",
                edit: {
                    disabled: true
                }
            },
            {
                dataIndex: "mfg-name",
                title: "manufacturer",
                edit: {
                    disabled: true
                }
            },
            {
                dataIndex: "hardware-version",
                edit: {
                    disabled: true
                }
            },
            {
                dataIndex: "firmware-version",
                edit: {
                    disabled: true
                }
            },
            {
                dataIndex: "software-version",
                edit: {
                    disabled: true
                }
            },
            {
                dataIndex: "mfg-date",
                title: "production-date",
                edit: {
                    disabled: true
                }
            }
        ],
        dynamicColumns: [
            {
                dataIndex: "input-power-c",
                unit: "dBm",
                edit: {
                    disabled: true
                }
            },
            {
                dataIndex: "output-power-c",
                unit: "dBm",
                edit: {
                    disabled: true
                }
            },
            {
                dataIndex: "input-power-p",
                unit: "dBm",
                edit: {
                    disabled: true
                }
            },
            {
                dataIndex: "output-power-p",
                unit: "dBm",
                edit: {
                    disabled: true
                }
            },
            {
                dataIndex: "input-power-s",
                unit: "dBm",
                edit: {
                    disabled: true
                }
            },
            {
                dataIndex: "output-power-s",
                unit: "dBm",
                edit: {
                    disabled: true
                }
            },
            {
                dataIndex: "revertive",
                edit: {
                    disabled: true
                }
            },
            {
                dataIndex: "force-to-port",
                edit: {
                    disabled: true
                }
            },
            {
                dataIndex: "active-path",
                edit: {
                    disabled: true
                }
            }
        ]
    },
    TRANSCEIVER_info: {
        ok: false,
        columns: [
            {
                dataIndex: "part-no",
                edit: {
                    disabled: true
                }
            },
            {
                dataIndex: "temperature",
                title: "module-temperature",
                unit: "celsius",
                edit: {
                    disabled: true
                }
            },
            {
                dataIndex: "mfg-name",
                title: "manufacturer",
                edit: {
                    disabled: true
                }
            },
            {
                dataIndex: "hardware-version",
                edit: {
                    disabled: true
                }
            },
            {
                dataIndex: "firmware-version",
                edit: {
                    disabled: true
                }
            },
            {
                dataIndex: "software-version",
                edit: {
                    disabled: true
                }
            },
            {
                dataIndex: "mfg-date",
                title: "production-date",
                edit: {
                    disabled: true
                }
            },
            {
                dataIndex: "input-power",
                title: "input-optical-power",
                unit: "dBm",
                edit: {
                    disabled: true
                }
            },
            {
                dataIndex: "output-power",
                title: "output-optical-power",
                unit: "dBm",
                edit: {
                    disabled: true
                }
            },
            {
                dataIndex: "osnr",
                edit: {
                    disabled: true
                }
            },
            {
                dataIndex: "post-fec-ber",
                edit: {
                    disabled: true
                }
            },
            {
                dataIndex: "pre-fec-ber",
                edit: {
                    disabled: true
                }
            }
        ]
    },
    "nms:provision": {
        title: "Edit",
        columns: [
            {
                dataIndex: "name",
                required: true,
                uniq: true,
                pattern: /^[\w+./:|\u4e00-\u9fa5-]{1,20}$/,
                message: "name_patter"
            }
        ]
    },
    parameters_test: {},
    otdr_event_data: {},
    otdr_data_comparison: {},
    "raman:info": {
        title: "Modify",
        columns: [
            {
                dataIndex: "raman-gain",
                unit: "dB",
                required: true
            },
            {
                dataIndex: "alarm-status",
                required: true,
                inputType: "select",
                data: {
                    options: ["ON", "OFF"]
                }
            },
            {
                dataIndex: "pump-status",
                required: true,
                inputType: "select",
                data: {
                    options: ["ON", "OFF"]
                }
            }
        ]
    }
};
