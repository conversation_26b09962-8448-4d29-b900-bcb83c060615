import {Space, Empty, Form, message, Table, Typography, Tag, Switch} from "antd";
import styles from "@/modules-ampcon/pages/Resource/DeviceDisplay/components/config_layer.module.scss";
import React, {useState, useEffect} from "react";
import dayjs from "dayjs";
import EmptyPic from "@/assets/images/App/empty.png";
import {
    queryFMTConfig,
    modifyFMTConfig,
    modifyFMTPortNote,
    getFMTDevicePort,
    getFMTDeviceSinglePort
} from "@/modules-ampcon/apis/fmt";

import {queryM6200Config, batchModifyDCSConfig, getM6200DevicePort} from "@/modules-ampcon/apis/m6200_api";

import Icon from "@ant-design/icons";
import openCustomEditForm from "@/modules-ampcon/pages/Resource/DeviceDisplay/components/custom_edit_form";
import {DebounceButton, NULL_VALUE} from "../utils";
import {editTableIcon, exportDisabledSvg, exportSvg, refreshDisabledSvg, refreshSvg} from "@/utils/common/iconSvg";
import {render} from "@fullcalendar/core/preact";
import {util} from "echarts";

const {Paragraph} = Typography;
/**
    OA板卡类型小类:
    FMT20PA-EDFA
    FMT17BA-EDFA
    FMT26PA-51EDFA
    FMT17BA-51EDFA
    FMT22BA-EDFA
    FMTPA-Array
    FMTBA-Array
    HPA
    FS-SOA
    EDFA-LA
* */

const switchComponent = (value, rowData, disabled, submit) => {
    if (value === NULL_VALUE) {
        return NULL_VALUE;
    }
    return (
        <Switch
            disabled={disabled ?? false}
            defaultChecked={!!value}
            onChange={newVal => {
                if (newVal === value) {
                    return;
                }
                if (submit) {
                    submit(newVal);
                    return;
                }
            }}
        />
    );
};

const editComponent = (value, rowData, submit) => {
    return (
        <Paragraph
            style={{margin: 0, display: "flex"}}
            editable={{
                maxLength: 32,
                icon: <Icon component={editTableIcon} />,
                text: value,
                onChange: newVal => {
                    if (newVal === value) return;
                    // direct submission
                    submit(newVal || "");
                },
                triggerType: ["icon", "text"]
            }}
        >
            <div style={{flex: 1}}>{value}</div>
        </Paragraph>
    );
};

export const DynamicTable = ({
    tabType,
    subTabType,
    data,
    CardId,
    CardName,
    NeIP,
    NeName = "",
    PortName = "",
    showExportButton = true,
    showRefreshButton = true
}) => {
    const [isShowSpin, setIsShowSpin] = useState(false);
    const {ports_data} = data || {};
    const parsedPortsData = ports_data ? JSON.parse(ports_data) : {};
    const refreshDisabled = !CardId;

    const [pagination, setPagination] = useState({
        current: 1,
        pageSize: 10,
        total: Object.keys(parsedPortsData).length
    });

    const [tableData, setTableData] = useState([]);
    useEffect(() => {
        if (data?.ports_data) {
            const parsedPortsData = JSON.parse(data.ports_data);
            const initialData = Object.entries(parsedPortsData).map(([portId, portData]) => ({
                key: portId,
                ...Object.fromEntries(Object.entries(data).map(([key, value]) => [key, value === "" ? "--" : value])),
                ...Object.fromEntries(
                    Object.entries(portData).map(([key, value]) => [key, value === "" ? "--" : value])
                ),
                "NE Name": NeName,
                "NE ID": `${NeIP}:4001`,
                "Card Name": CardName
            }));
            setTableData(initialData);
        } else {
            setTableData([]);
        }
    }, [data]);

    if (!data || !data.ports_data) {
        return <Empty image={EmptyPic} description="No Data" imageStyle={{margin: 0}} />;
    }
    const TableConfigs = {
        power: {
            EDFA: {
                columns: [
                    {dataIndex: "No", title: "Index", fixed: "left"},
                    {dataIndex: "NE Name"},
                    {dataIndex: "NE ID"},
                    {dataIndex: "type", title: "Card Type"},
                    {dataIndex: "Card Name"},
                    {dataIndex: "slot_index", title: "Slot No."},
                    {dataIndex: "Name", title: "Port Name"},
                    {dataIndex: "Input Optical Power", unit: "dBm"},
                    {dataIndex: "Output Optical Power", unit: "dBm"},
                    {dataIndex: "Input Warning Threshold", unit: "dBm"},
                    {dataIndex: "Output Warning Threshold", unit: "dBm"},
                    {
                        dataIndex: "operation",
                        title: "Operation",
                        fixed: "right",
                        render: (value, rowData) => {
                            const inputAlarmThresholdKey = `rx_power_alarm_threshold`;
                            const outputAlarmThresholdKey = `tx_power_alarm_threshold`;

                            return (
                                <DebounceButton
                                    type="link"
                                    title="Modify"
                                    onClick={async () => {
                                        openCustomEditForm({
                                            title: "Modify",
                                            columnNum: 1,
                                            columns: [
                                                [
                                                    {
                                                        dataIndex: inputAlarmThresholdKey,
                                                        label: `Input warning threshold`,
                                                        unit: "dB",
                                                        inputType: "number",
                                                        step: 0.01,
                                                        required: true,
                                                        transformValue: value => {
                                                            return value.toString();
                                                        }
                                                    }
                                                ],
                                                [
                                                    {
                                                        dataIndex: outputAlarmThresholdKey,
                                                        label: `Output warning threshold`,
                                                        unit: "dB",
                                                        inputType: "number",
                                                        step: 0.01,
                                                        required: true,
                                                        transformValue: value => {
                                                            return value.toString();
                                                        }
                                                    }
                                                ]
                                            ],
                                            getData: async () => {
                                                const ip = NeIP;
                                                const response = await queryFMTConfig(ip, null, CardId);
                                                const _data = response.data?.configData;

                                                const initialValues = {
                                                    [inputAlarmThresholdKey]: _data.rx_power_alarm_threshold,
                                                    [outputAlarmThresholdKey]: _data.tx_power_alarm_threshold
                                                };
                                                return initialValues;
                                            },
                                            setDataAPI: () => {
                                                const ip = NeIP;
                                                return {
                                                    APIName: modifyFMTConfig,
                                                    APIParameter: () => {
                                                        return {
                                                            ip,
                                                            cardId: CardId
                                                        };
                                                    }
                                                };
                                            }
                                        });
                                    }}
                                >
                                    Modify
                                </DebounceButton>
                            );
                        }
                    }
                ]
            },
            OEO: {
                columns: [
                    {dataIndex: "No", title: "Index", fixed: "left"},
                    {dataIndex: "NE Name"},
                    {dataIndex: "NE ID"},
                    {dataIndex: "type", title: "Card Type"},
                    {dataIndex: "Card Name"},
                    {dataIndex: "slot_index", title: "Slot No."},
                    {dataIndex: "Name", title: "Port Name"},
                    {dataIndex: "Input Optical Power", unit: "dBm"},
                    {dataIndex: "Output Optical Power", unit: "dBm"},
                    {dataIndex: "Input Alarm Threshold", unit: "dBm"},
                    {
                        dataIndex: "operation",
                        title: "Operation",
                        fixed: "right",
                        render: (value, rowData) => {
                            const portName = rowData.Name;
                            const portNames = Object.values(parsedPortsData).map(port => port.Name.toLowerCase());
                            const portIndex = portNames.indexOf(portName.toLowerCase());
                            const inputAlarmThresholdKey = `input_alarm_threshold_${portName.toLowerCase()}`;

                            return (
                                <DebounceButton
                                    type="link"
                                    title="Modify"
                                    onClick={async () => {
                                        openCustomEditForm({
                                            title: "Modify",
                                            columnNum: 1,
                                            columns: [
                                                [
                                                    {
                                                        dataIndex: inputAlarmThresholdKey,
                                                        label: `Input Alarm ${portName} threshold`,
                                                        unit: "dB",
                                                        inputType: "number",
                                                        step: 0.01,
                                                        required: true,
                                                        transformValue: value => {
                                                            return value.toString();
                                                        }
                                                    }
                                                ]
                                            ],
                                            getData: async () => {
                                                const ip = NeIP;
                                                const response = await queryFMTConfig(ip, null, CardId);
                                                const _data = response.data?.configData;

                                                const initialValues = {
                                                    [inputAlarmThresholdKey]: _data.input_alarm_threshold_[portIndex]
                                                };
                                                return initialValues;
                                            },
                                            setDataAPI: () => {
                                                const ip = NeIP;
                                                return {
                                                    APIName: modifyFMTConfig,
                                                    APIParameter: () => {
                                                        return {
                                                            ip,
                                                            cardId: CardId
                                                        };
                                                    }
                                                };
                                            }
                                        });
                                    }}
                                >
                                    Modify
                                </DebounceButton>
                            );
                        }
                    }
                ]
            },
            OLP: {
                columns: [
                    {dataIndex: "No", title: "Index", fixed: "left"},
                    {dataIndex: "NE Name"},
                    {dataIndex: "NE ID"},
                    {dataIndex: "type", title: "Card Type"},
                    {dataIndex: "Card Name"},
                    {dataIndex: "slot_index", title: "Slot No."},
                    {dataIndex: "Name", title: "Port Name"},
                    {dataIndex: "VOA Attenuation", unit: "dB"},
                    {dataIndex: "Optical Power", unit: "dBm"},
                    {dataIndex: "Optical Power Threshold", unit: "dBm"},
                    {
                        dataIndex: "operation",
                        title: "Operation",
                        fixed: "right",
                        render: (value, rowData) => {
                            const portName = rowData.Name;
                            const portNameMapping = {
                                "PORT-1-APSP-IN": "r1",
                                "PORT-2-APSS-IN": "r2",
                                "PORT-3-APSC-OUT": "tx"
                            };
                            const ThresholdKey = portNameMapping[portName];

                            return (
                                <DebounceButton
                                    type="link"
                                    title="Modify"
                                    onClick={async () => {
                                        openCustomEditForm({
                                            title: "Modify",
                                            columnNum: 1,
                                            columns: [
                                                [
                                                    {
                                                        dataIndex: ThresholdKey,
                                                        label: `${ThresholdKey} Alarm threshold`,
                                                        unit: "dB",
                                                        inputType: "number",
                                                        step: 0.01,
                                                        transformValue: value => value.toString()
                                                    }
                                                ]
                                            ],
                                            getData: async () => {
                                                const ip = NeIP;
                                                const response = await queryFMTConfig(ip, null, CardId);
                                                const _data = response.data?.configData;

                                                const initialValues = {
                                                    [ThresholdKey]: _data[ThresholdKey]
                                                };
                                                return initialValues;
                                            },
                                            setDataAPI: () => {
                                                const ip = NeIP;
                                                return {
                                                    APIName: modifyFMTConfig,
                                                    APIParameter: () => {
                                                        return {
                                                            ip,
                                                            cardId: CardId
                                                        };
                                                    }
                                                };
                                            }
                                        });
                                    }}
                                >
                                    Modify
                                </DebounceButton>
                            );
                        }
                    }
                ]
            },
            VOA: {
                columns: [
                    {dataIndex: "No", title: "Index", fixed: "left"},
                    {dataIndex: "NE Name"},
                    {dataIndex: "NE ID"},
                    {dataIndex: "type", title: "Card Type"},
                    {dataIndex: "Card Name"},
                    {dataIndex: "slot_index", title: "Slot No."},
                    {dataIndex: "Name", title: "Port Name"},
                    {dataIndex: "Actual Attenuation", unit: "dB"},
                    {dataIndex: "Expected Attenuation", unit: "dB"},
                    {dataIndex: "Actual Power", unit: "dBm"},
                    {dataIndex: "Expected Power", unit: "dBm"},
                    {
                        dataIndex: "operation",
                        title: "Operation",
                        fixed: "right",
                        render: (value, rowData) => {
                            const portName = rowData.Name;
                            const ThresholdKey = `${portName.toLowerCase()}_threshold`;
                            return (
                                <DebounceButton
                                    type="link"
                                    title="Modify"
                                    onClick={async () => {
                                        openCustomEditForm({
                                            title: "Modify",
                                            columnNum: 1,
                                            columns: [
                                                [
                                                    {
                                                        dataIndex: ThresholdKey,
                                                        label: `${portName} threshold`,
                                                        unit: "dB",
                                                        inputType: "number",
                                                        step: 0.01,
                                                        required: true,
                                                        transformValue: value => {
                                                            return value.toString();
                                                        }
                                                    }
                                                ]
                                            ],
                                            getData: async () => {
                                                const ip = NeIP;
                                                const response = await queryFMTConfig(ip, null, CardId);
                                                const _data = response.data?.configData;

                                                const initialValues = {
                                                    [ThresholdKey]: _data[ThresholdKey]
                                                };
                                                return initialValues;
                                            },
                                            setDataAPI: () => {
                                                const ip = NeIP;
                                                return {
                                                    APIName: modifyFMTConfig,
                                                    APIParameter: () => {
                                                        return {
                                                            ip,
                                                            cardId: CardId
                                                        };
                                                    }
                                                };
                                            }
                                        });
                                    }}
                                >
                                    Modify
                                </DebounceButton>
                            );
                        }
                    }
                ]
            },
            OPD: {
                columns: [
                    {dataIndex: "No", title: "Index", fixed: "left"},
                    {dataIndex: "NE Name"},
                    {dataIndex: "NE ID"},
                    {dataIndex: "type", title: "Card Type"},
                    {dataIndex: "Card Name"},
                    {dataIndex: "slot_index", title: "Slot No."},
                    {dataIndex: "Name", title: "Port Name"},
                    {dataIndex: "VOA Attenuation", unit: "dB"},
                    {dataIndex: "Power", unit: "dBm"},
                    {
                        dataIndex: "Wavelength",
                        unit: "nm",
                        render: value => {
                            if (value === "0") {
                                return "1310";
                            }
                            if (value === "1") {
                                return "1550";
                            }
                            return NULL_VALUE;
                        }
                    },
                    {
                        dataIndex: "operation",
                        title: "Operation",
                        fixed: "right",
                        render: (value, rowData) => {
                            const Index = rowData.No;
                            const portNames = Object.values(parsedPortsData).map(port => port.No.toLowerCase());
                            const portIndex = portNames.indexOf(Index.toLowerCase());
                            const Wavelength = `wavelength_${Index.toLowerCase()}`;

                            return (
                                <DebounceButton
                                    type="link"
                                    title="Modify"
                                    onClick={async () => {
                                        openCustomEditForm({
                                            title: "Modify",
                                            columnNum: 1,
                                            columns: [
                                                [
                                                    {
                                                        dataIndex: Wavelength,
                                                        label: `Wavelength${Index} `,
                                                        unit: "nm",
                                                        inputType: "select",
                                                        required: true,
                                                        data: {
                                                            options: [
                                                                {label: "1550", value: "1"},
                                                                {label: "1310", value: "0"}
                                                            ]
                                                        }
                                                    }
                                                ]
                                            ],
                                            getData: async () => {
                                                const ip = NeIP;
                                                const response = await queryFMTConfig(ip, null, CardId);
                                                const _data = response.data?.configData;

                                                const initialValues = {
                                                    [Wavelength]: _data.wavelength_[portIndex]
                                                };
                                                return initialValues;
                                            },
                                            setDataAPI: () => {
                                                const ip = NeIP;
                                                return {
                                                    APIName: modifyFMTConfig,
                                                    APIParameter: () => {
                                                        return {
                                                            ip,
                                                            cardId: CardId
                                                        };
                                                    }
                                                };
                                            }
                                        });
                                    }}
                                >
                                    Modify
                                </DebounceButton>
                            );
                        }
                    }
                ]
            }
        },
        edfa: {
            EDFA: {
                columns: [
                    {dataIndex: "No", title: "Index", fixed: "left"},
                    {dataIndex: "Name", title: "Port Name"},
                    {dataIndex: "Input Optical Power", unit: "dBm"},
                    {dataIndex: "Output Optical Power", unit: "dBm"},
                    {dataIndex: "VOA Attenuation Value", unit: "dB"},
                    {dataIndex: "VOA Attenuation Expected", unit: "dB"},
                    {dataIndex: "EDFA Gain Value", unit: "dB"},
                    {dataIndex: "Expected EDFA Gain", unit: "dB"},
                    {dataIndex: "Gain Slope", unit: "dB/40nm"},
                    {
                        dataIndex: "operation",
                        title: "Operation",
                        fixed: "right",
                        render: (value, rowData) => {
                            const GainAdjustment = `gain_adjustment`;
                            const Pump1State = `pump1_state`;

                            return (
                                <DebounceButton
                                    type="link"
                                    title="Modify"
                                    onClick={async () => {
                                        openCustomEditForm({
                                            title: "Modify",
                                            columnNum: 1,
                                            columns: [
                                                [
                                                    {
                                                        dataIndex: GainAdjustment,
                                                        label: `GainAdjustment`,
                                                        unit: "dB",
                                                        inputType: "number",
                                                        step: 0.01,
                                                        required: true,
                                                        transformValue: value => {
                                                            return value.toString();
                                                        }
                                                    }
                                                ],
                                                [
                                                    {
                                                        dataIndex: Pump1State,
                                                        label: "Pump1 State",
                                                        inputType: "select",
                                                        required: true,
                                                        data: {
                                                            options: [
                                                                {label: "Open", value: "1"},
                                                                {label: "Close", value: "0"}
                                                            ]
                                                        }
                                                    }
                                                ]
                                            ],
                                            getData: async () => {
                                                const ip = NeIP;
                                                const response = await queryFMTConfig(ip, null, CardId);
                                                const _data = response.data?.configData;

                                                const initialValues = {
                                                    [GainAdjustment]: _data.gain_adjustment,
                                                    [Pump1State]: _data.pump1_state
                                                };
                                                return initialValues;
                                            },
                                            setDataAPI: () => {
                                                const ip = NeIP;
                                                return {
                                                    APIName: modifyFMTConfig,
                                                    APIParameter: () => {
                                                        return {
                                                            ip,
                                                            cardId: CardId
                                                        };
                                                    }
                                                };
                                            }
                                        });
                                    }}
                                >
                                    Modify
                                </DebounceButton>
                            );
                        }
                    }
                ]
            }
        },
        oeo: {
            OEO: {
                port: {
                    columns: [
                        {
                            dataIndex: "no",
                            title: "Port Name",
                            fixed: "left",
                            render: text => `Port${text}`
                        },
                        {
                            dataIndex: "portMode",
                            title: "Module Type",
                            render: value => {
                                if (value === NULL_VALUE || value === undefined) {
                                    return NULL_VALUE;
                                }
                                return value;
                            }
                        },
                        {
                            dataIndex: "module_state",
                            title: "Module State",
                            render: value => {
                                if (value === NULL_VALUE || value === undefined) {
                                    return NULL_VALUE;
                                }
                                return value;
                            }
                        },
                        {
                            dataIndex: "module_wavelength",
                            title: "Module Wavelength",
                            unit: "nm",
                            render: value => {
                                if (value === "0") {
                                    return "1310";
                                }
                                if (value === "1") {
                                    return "1550";
                                }
                                return NULL_VALUE;
                            }
                        },
                        {
                            dataIndex: "input_optical_power",
                            title: "Input Optical Power",
                            unit: "dBm"
                        },
                        {
                            dataIndex: "output_optical_power",
                            title: "Output Optical Power",
                            unit: "dBm",
                            render: value => {
                                if (value === NULL_VALUE || value === undefined || parseInt(value) === 0) {
                                    return NULL_VALUE;
                                }
                                return parseFloat(value).toFixed(2);
                            }
                        },
                        {
                            dataIndex: "input_alarm_threshold",
                            title: "Input Alarm Threshold",
                            unit: "dBm",
                            render: value => {
                                if (value === NULL_VALUE || value === undefined || parseInt(value) === 0) {
                                    return NULL_VALUE;
                                }
                                return parseFloat(value).toFixed(2);
                            }
                        },
                        {
                            dataIndex: "laser_mode",
                            title: "Laser Mode",
                            render: value => (
                                <Tag color={value === 1 ? "green" : "red"}>{value === 1 ? "On" : "Close"}</Tag>
                            )
                        },
                        {
                            dataIndex: "transmission_distance",
                            title: "Transmission Distance",
                            unit: "km",
                            render: value => {
                                if (value === NULL_VALUE || value === undefined || parseInt(value) === 0) {
                                    return NULL_VALUE;
                                }
                                return parseFloat(value).toFixed(2);
                            }
                        },
                        {
                            dataIndex: "module_temperature",
                            title: "Module Temperature",
                            unit: "℃",
                            render: value => {
                                if (value === NULL_VALUE || value === undefined || parseInt(value) === 0) {
                                    return NULL_VALUE;
                                }
                                return parseFloat(value).toFixed(2);
                            }
                        },
                        {
                            dataIndex: "rate",
                            title: "Rate",
                            unit: "Gb/s",
                            render: value => {
                                if (value === NULL_VALUE || value === undefined || parseInt(value) === 0) {
                                    return NULL_VALUE;
                                }
                                return parseFloat(value).toFixed(2);
                            }
                        },
                        {
                            dataIndex: "present_or_absent",
                            title: "Present or Absent",
                            render: value => {
                                if (value === NULL_VALUE || value === undefined) {
                                    return NULL_VALUE;
                                }
                                return value;
                            }
                        },
                        {
                            dataIndex: "port_switch",
                            title: "Port Switch",
                            render: (value, rowData, index) =>
                                switchComponent(value === "0", rowData, rowData.port_switch === NULL_VALUE, newVal => {
                                    batchModifyDCSConfig({
                                        ip: NeIP,
                                        cardId: CardId,
                                        key: `port_switch_${rowData.no}`,
                                        value: newVal ? "0" : "1"
                                    })
                                        .then(response => {
                                            if (response.errorCode === 0) {
                                                setTableData(prevData => {
                                                    const newData = [...prevData];
                                                    newData[index].port_switch = newVal ? "0" : "1";
                                                    return newData;
                                                });
                                                message.success("Modify successfully");
                                            } else {
                                                message.error("Modify failed");
                                            }
                                        })
                                        .catch(() => {});
                                })
                        },
                        {
                            dataIndex: "operation",
                            title: "Operation",
                            fixed: "right",
                            render: (value, rowData) => {
                                const portNo = rowData.no;
                                const portIndex = portNo - 1;
                                const inputAlarmThresholdKey = `input_alarm_threshold_${portNo}`;
                                const LaserMode = `laser_mode_${portNo}`;

                                return (
                                    <DebounceButton
                                        type="link"
                                        title="Modify"
                                        onClick={async () => {
                                            openCustomEditForm({
                                                title: "Modify",
                                                columnNum: 1,
                                                columns: [
                                                    [
                                                        {
                                                            dataIndex: inputAlarmThresholdKey,
                                                            label: `Input Alarm threshold`,
                                                            unit: "dB",
                                                            inputType: "number",
                                                            step: 0.01,
                                                            required: true,
                                                            transformValue: value => {
                                                                return value.toString();
                                                            }
                                                        }
                                                    ],
                                                    [
                                                        {
                                                            dataIndex: LaserMode,
                                                            label: `Laser Mode`,
                                                            inputType: "select",
                                                            required: true,
                                                            data: {
                                                                options: [
                                                                    {label: "Close", value: "0"},
                                                                    {label: "On", value: "1"}
                                                                ]
                                                            }
                                                        }
                                                    ]
                                                ],
                                                getData: async () => {
                                                    const ip = NeIP;
                                                    const response = await queryM6200Config(ip, null, CardId);
                                                    const _data = response.data?.configData;

                                                    const initialValues = {
                                                        [inputAlarmThresholdKey]:
                                                            _data.input_alarm_threshold_[portIndex],
                                                        [LaserMode]: _data.laser_mode_[portIndex]
                                                    };
                                                    return initialValues;
                                                },
                                                setDataAPI: () => {
                                                    const ip = NeIP;
                                                    return {
                                                        APIName: batchModifyDCSConfig,
                                                        APIParameter: () => {
                                                            return {
                                                                ip,
                                                                cardId: CardId
                                                            };
                                                        }
                                                    };
                                                }
                                                // afterUpdate
                                            });
                                        }}
                                    >
                                        Modify
                                    </DebounceButton>
                                );
                            }
                        }
                    ]
                },
                otu: {
                    columns: [
                        {
                            dataIndex: "no",
                            title: "Port Name",
                            fixed: "left",
                            render: text => `Port${text}`
                        },
                        {
                            dataIndex: "laser_shutdown_control",
                            title: "Laser Shutdown Control"
                        },
                        {
                            dataIndex: "port_laser_state",
                            title: "Port Laser State",
                            render: value => (
                                <Tag color={value === 1 ? "green" : "red"}>{value === 1 ? "On" : "Close"}</Tag>
                            )
                        },
                        {
                            dataIndex: "port_source_select",
                            title: "Port Source Select"
                        },
                        {
                            dataIndex: "rx_optical_power_min_ala",
                            title: "Rx Optical Power Min Ala",
                            unit: "dBm",
                            render: value => {
                                if (value === NULL_VALUE || value === undefined) {
                                    return NULL_VALUE;
                                }
                                const num = Number(value);
                                const displayVal = num >= 0 ? Math.abs(num) : num;

                                return displayVal.toFixed(2);
                            }
                        },
                        {
                            dataIndex: "threshold_hysteresis",
                            title: "Threshold Hysteresis"
                        },
                        {
                            dataIndex: "operation",
                            title: "Operation",
                            fixed: "right",
                            render: (value, rowData) => {
                                const portNo = rowData.no;
                                const portIndex = portNo - 1;
                                const LaserShutdownControl = `laser_shutdown_control_${portNo}`;
                                const RxOpticalPowerMinAla = `rx_optical_power_min_ala_${portNo}`;
                                const ThresholdHysteresis = `threshold_hysteresis_${portNo}`;

                                return (
                                    <DebounceButton
                                        type="link"
                                        title="Modify"
                                        onClick={async () => {
                                            openCustomEditForm({
                                                title: "Modify",
                                                columnNum: 1,
                                                columns: [
                                                    [
                                                        {
                                                            dataIndex: LaserShutdownControl,
                                                            label: `Laser Shutdown Control`,
                                                            inputType: "select",
                                                            required: true,
                                                            data: {
                                                                options: [
                                                                    {label: "ALS Disable", value: "0"},
                                                                    {label: "Force Shutdown", value: "1"},
                                                                    {label: "Local ALS Enable", value: "0"}
                                                                ]
                                                            }
                                                        }
                                                    ],
                                                    [
                                                        {
                                                            dataIndex: RxOpticalPowerMinAla,
                                                            label: `Rx Optical Power Min Ala`,
                                                            inputType: "number",
                                                            unit: "dB",
                                                            step: 0.01,
                                                            required: true,
                                                            transformValue: value => {
                                                                return value.toString();
                                                            }
                                                        }
                                                    ],
                                                    [
                                                        {
                                                            dataIndex: ThresholdHysteresis,
                                                            label: `Threshold Hysteresis`,
                                                            inputType: "number",
                                                            step: 0.01,
                                                            required: true,
                                                            transformValue: value => {
                                                                return value.toString();
                                                            }
                                                        }
                                                    ]
                                                ],
                                                getData: async () => {
                                                    const ip = NeIP;
                                                    const response = await queryM6200Config(ip, null, CardId);
                                                    const _data = response.data?.configData;

                                                    const initialValues = {
                                                        [LaserShutdownControl]:
                                                            _data.laser_shutdown_control_[portIndex],
                                                        [RxOpticalPowerMinAla]:
                                                            _data.rx_optical_power_min_ala_[portIndex],
                                                        [ThresholdHysteresis]: _data.threshold_hysteresis_[portIndex]
                                                    };
                                                    return initialValues;
                                                },
                                                setDataAPI: () => {
                                                    const ip = NeIP;
                                                    return {
                                                        APIName: batchModifyDCSConfig,
                                                        APIParameter: () => {
                                                            return {
                                                                ip,
                                                                cardId: CardId
                                                            };
                                                        }
                                                    };
                                                }
                                                // afterUpdate
                                            });
                                        }}
                                    >
                                        Modify
                                    </DebounceButton>
                                );
                            }
                        }
                    ]
                },
                protection: {
                    columns: [
                        {dataIndex: "ppg", title: "PPG", fixed: "left"},
                        {dataIndex: "switch_mode", title: "Switch Mode"},
                        {dataIndex: "switch_state", title: "Switch State"},
                        {dataIndex: "switch_holdoff_time", title: "Switch Holdoff Time", unit: "ms"},
                        {dataIndex: "revertive_of_switchMode", title: "Revertive Of SwitchMode"},
                        {dataIndex: "revertive_of_switchModeTime", title: "Revertive Of SwitchModeTime", unit: "min"},
                        {dataIndex: "auto_back_mode", title: "Auto Back Mode"},
                        {dataIndex: "auto_back_delay", title: "Auto Back Delay", unit: "s"},
                        {
                            dataIndex: "operation",
                            title: "Operation",
                            fixed: "right",
                            render: (value, rowData) => {
                                const portNo = rowData.no;
                                const portIndex = portNo - 1;
                                const SwitchMode = `switch_mode_${portNo}`;
                                const SwitchState = `switch_state_${portNo}`;
                                const SwitchHoldoffTime = `switch_holdoff_time_${portNo}`;
                                const RevertiveOfSwitchMode = `revertive_of_switchMode_${portNo}`;
                                const RevertiveOfSwitchModeTime = `revertive_of_switchModeTime_${portNo}`;
                                const AutoBackMode = `auto_back_mode_${portNo}`;
                                const AutoBackDelay = `auto_back_delay_${portNo}`;

                                return (
                                    <DebounceButton
                                        type="link"
                                        title="Modify"
                                        onClick={async () => {
                                            openCustomEditForm({
                                                title: "Modify",
                                                columnNum: 1,
                                                columns: [
                                                    [
                                                        {
                                                            dataIndex: SwitchMode,
                                                            label: `Switch Mode`,
                                                            inputType: "select",
                                                            required: true,
                                                            data: {
                                                                options: [
                                                                    {label: "Auto", value: "0"},
                                                                    {label: "Manual", value: "1"}
                                                                ]
                                                            }
                                                        }
                                                    ],
                                                    [
                                                        {
                                                            dataIndex: SwitchState,
                                                            label: `Switch State`,
                                                            inputType: "select",
                                                            required: true,
                                                            data: {
                                                                options: [
                                                                    {label: "Switch to Primary", value: "0"},
                                                                    {label: "Switch to Secondary", value: "1"}
                                                                ]
                                                            }
                                                        }
                                                    ],
                                                    [
                                                        {
                                                            dataIndex: SwitchHoldoffTime,
                                                            label: `Switch Holdoff Time`,
                                                            inputType: "number",
                                                            unit: "ms",
                                                            step: 1,
                                                            required: true,
                                                            transformValue: value => {
                                                                return value.toString();
                                                            }
                                                        }
                                                    ],
                                                    [
                                                        {
                                                            dataIndex: RevertiveOfSwitchMode,
                                                            label: `Revertive Of Switch Mode`,
                                                            inputType: "select",
                                                            required: true,
                                                            data: {
                                                                options: [
                                                                    {label: "Revertive", value: "0"},
                                                                    {label: "NonRevertive", value: "1"}
                                                                ]
                                                            }
                                                        }
                                                    ],
                                                    [
                                                        {
                                                            dataIndex: RevertiveOfSwitchModeTime,
                                                            label: `Revertive Of SwitchModeTime`,
                                                            inputType: "number",
                                                            unit: "min",
                                                            step: 1,
                                                            required: true,
                                                            transformValue: value => {
                                                                return value.toString();
                                                            }
                                                        }
                                                    ],
                                                    [
                                                        {
                                                            dataIndex: AutoBackMode,
                                                            label: `Auto Back Mode`,
                                                            inputType: "select",
                                                            required: true,
                                                            data: {
                                                                options: [
                                                                    {label: "AutoBack", value: "0"},
                                                                    {label: "NotAutoBack", value: "1"}
                                                                ]
                                                            }
                                                        }
                                                    ],
                                                    [
                                                        {
                                                            dataIndex: AutoBackDelay,
                                                            label: `Auto Back Delay`,
                                                            inputType: "number",
                                                            unit: "s",
                                                            step: 1,
                                                            required: true,
                                                            transformValue: value => {
                                                                return value.toString();
                                                            }
                                                        }
                                                    ]
                                                ],
                                                getData: async () => {
                                                    const ip = NeIP;
                                                    const response = await queryM6200Config(ip, null, CardId);
                                                    const _data = response.data?.configData;

                                                    const initialValues = {
                                                        [SwitchMode]: _data.switch_mode_[portIndex],
                                                        [SwitchState]: _data.switch_state_[portIndex],
                                                        [SwitchHoldoffTime]: _data.switch_holdoff_time_[portIndex],
                                                        [RevertiveOfSwitchMode]:
                                                            _data.revertive_of_switchMode_[portIndex],
                                                        [RevertiveOfSwitchModeTime]:
                                                            _data.revertive_of_switchModeTime_[portIndex],
                                                        [AutoBackMode]: _data.auto_back_mode_[portIndex],
                                                        [AutoBackDelay]: _data.auto_back_delay_[portIndex]
                                                    };
                                                    return initialValues;
                                                },
                                                setDataAPI: () => {
                                                    const ip = NeIP;
                                                    return {
                                                        APIName: batchModifyDCSConfig,
                                                        APIParameter: () => {
                                                            return {
                                                                ip,
                                                                cardId: CardId
                                                            };
                                                        }
                                                    };
                                                }
                                                // afterUpdate
                                            });
                                        }}
                                    >
                                        Modify
                                    </DebounceButton>
                                );
                            }
                        }
                    ]
                }
            }
        }
    };
    const getTableConfig = () => {
        const typeMap = {
            power: data.type,
            edfa: "EDFA",
            oeo: "OEO"
        };
        const configType = typeMap[tabType];
        const columns =
            tabType === "oeo"
                ? TableConfigs[tabType]?.[configType]?.[subTabType]?.columns || []
                : TableConfigs[tabType]?.[configType]?.columns || [];

        if (tabType === "oeo") {
            if (subTabType === "port") {
                const desiredOrder = [
                    "no",
                    "portMode",
                    "present_or_absent",
                    "module_wavelength",
                    "input_optical_power",
                    "output_optical_power",
                    "input_alarm_threshold",
                    "laser_mode",
                    "transmission_distance",
                    "module_state",
                    "rate",
                    "module_temperature",
                    "port_switch",
                    "operation"
                ];
                return desiredOrder.map(key => columns.find(col => col.dataIndex === key)).filter(Boolean);
            }

            if (subTabType === "otu") {
                const desiredOrder = [
                    "no",
                    "laser_shutdown_control",
                    "port_laser_state",
                    "port_source_select",
                    "rx_optical_power_min_ala",
                    "threshold_hysteresis",
                    "operation"
                ];
                return desiredOrder.map(key => columns.find(col => col.dataIndex === key)).filter(Boolean);
            }

            if (subTabType === "protection") {
                const desiredOrder = [
                    "ppg",
                    "switch_mode",
                    "switch_state",
                    "switch_holdoff_time",
                    "revertive_of_switchMode",
                    "revertive_of_switchModeTime",
                    "auto_back_mode",
                    "auto_back_delay",
                    "operation"
                ];
                return desiredOrder.map(key => columns.find(col => col.dataIndex === key)).filter(Boolean);
            }
        }

        return columns;
    };

    const columns = getTableConfig().map(col => {
        const {dataIndex, title, unit, render, fixed} = col;
        const column = {
            title: title || dataIndex.replace(/_/g, " ").replace(/(^|\s)\S/g, match => match.toUpperCase()),
            dataIndex,
            key: dataIndex,
            fixed,
            sorter: (a, b) => {
                if (typeof a[dataIndex] === "number" && typeof b[dataIndex] === "number") {
                    return a[dataIndex] - b[dataIndex];
                }
                if (typeof a[dataIndex] === "string" && typeof b[dataIndex] === "string") {
                    return a[dataIndex].localeCompare(b[dataIndex]);
                }
                return 0;
            }
        };
        if (unit) {
            column.title += ` (${unit})`;
        }
        if (render) {
            column.render = render;
        }
        return column;
    });

    const onExportToExcel = () => {
        try {
            if (!tableData?.length) {
                message.warning("No Data");
                return;
            }

            const exportColumns = columns.filter(col => !col.hidden && col.title !== "Operation");
            const headers = exportColumns.map(col => col.title).join(",");

            const data = tableData.reduce((res, row) => {
                const lineData = `${columns
                    .filter(col => !col.hidden)
                    .map(col => {
                        const matchValue = row[col.dataIndex];
                        return `"${matchValue ?? ""}"`;
                    })
                    .join(",")}\n`;
                return `${res}${lineData}`;
            }, `\uFEFF${headers}\n`);

            const blob = new Blob([data], {type: "text/csv"});
            const downloadLink = document.createElement("a");
            downloadLink.href = URL.createObjectURL(blob);
            const fileName = `${tabType}_${dayjs().format("YYYY-MM-DD_HH:mm:ss")}.csv`;
            downloadLink.download = fileName;
            downloadLink.click();

            message.success("Export successfully");
        } catch (error) {
            message.error(`Export Failed：${error.message}`);
        }
    };

    const doRefresh = async type => {
        try {
            setIsShowSpin(true);
            let response;
            if (PortName) {
                response = await getFMTDeviceSinglePort(CardId, PortName);
            } else if (type === "oeo") {
                response = await getM6200DevicePort(CardId, type);
            } else {
                response = await getFMTDevicePort(CardId, type);
            }

            if (response?.data) {
                let newData;
                if (type === "power") {
                    if (PortName) {
                        newData = response.data;
                    } else {
                        newData = response.data.info;
                    }
                } else if (["oeo", "edfa"].includes(type)) {
                    newData = response.data.ports_info;
                }

                const newPortsData = newData?.ports_data ? JSON.parse(newData.ports_data) : {};
                const newDataSource = Object.entries(newPortsData).map(([portId, portData]) => ({
                    key: portId,
                    ...Object.fromEntries(
                        Object.entries(newData).map(([key, value]) => [key, value === "" ? "--" : value])
                    ),
                    ...Object.fromEntries(
                        Object.entries(portData).map(([key, value]) => [key, value === "" ? "--" : value])
                    ),
                    "NE Name": NeName,
                    "NE ID": NeIP,
                    "Card Name": CardName
                }));

                setTableData(newDataSource);
                setPagination(prev => ({
                    ...prev,
                    total: newDataSource.length
                }));
                message.success("Refresh successfully");
            } else {
                message.error("Refresh failed");
            }
        } catch (error) {
            message.error(`Refresh failed：${error.message}`);
        } finally {
            setIsShowSpin(false);
        }
    };

    return (
        <>
            <div style={{display: "flex", justifyContent: "space-between"}}>
                <Space style={{marginBottom: 24}} size={16}>
                    {showExportButton && (
                        <DebounceButton
                            icon={<Icon component={!tableData.length ? exportDisabledSvg : exportSvg} />}
                            onClick={onExportToExcel}
                            title="Export"
                            disabled={!tableData.length}
                        >
                            Export
                        </DebounceButton>
                    )}
                    {showRefreshButton && (
                        <DebounceButton
                            onClick={() => doRefresh(tabType)}
                            icon={<Icon component={refreshDisabled ? refreshDisabledSvg : refreshSvg} />}
                            disabled={refreshDisabled}
                        >
                            Refresh
                        </DebounceButton>
                    )}
                </Space>
            </div>
            <Table
                columns={columns}
                dataSource={tableData}
                bordered
                pagination={{
                    current: pagination.current,
                    pageSize: pagination.pageSize,
                    total: tableData.length,
                    onChange: (page, pageSize) => {
                        setPagination({...pagination, current: page, pageSize});
                    },
                    showSizeChanger: true,
                    pageSizeOptions: ["10", "20", "50", "100"],
                    showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} items`
                }}
                style={{whiteSpace: "nowrap"}}
            />
        </>
    );
};
