import {Node, ToolsView} from "@antv/x6";
import {createRoot} from "react-dom/client";
import {Toolt<PERSON>, Badge} from "antd";

class Group extends Node {
    collapsed = false;

    expandSize = {width: 0, height: 0};

    postprocess() {
        this.toggleCollapse(false);
    }

    isCollapsed() {
        return this.collapsed;
    }

    toggleCollapse(collapsed) {
        const target = collapsed == null ? !this.collapsed : collapsed;
        if (target) {
            this.attr("buttonSign", {d: "M 2 5 8 5"});
            if (this.expandSize) {
                this.resize(this.expandSize.width, this.expandSize.height);
            }
        } else {
            this.attr("buttonSign", {d: "M 1 5 9 5 M 5 1 5 9"});
            this.expandSize = this.getSize();
            this.resize(100, 32);
        }
        this.collapsed = target;
    }
}
class TooltipTool extends ToolsView.ToolItem {
    constructor() {
        super();
        this.knob = null;
        this.root = null;
    }

    render() {
        if (!this.knob) {
            this.knob = ToolsView.createElement("div", false);
            this.knob.style.position = "absolute";
            this.container.appendChild(this.knob);
        }
        return this;
    }

    toggleTooltip(visible) {
        if (this.knob) {
            if (this.root) {
                this.root.unmount();
                this.root = null;
            }
            if (visible) {
                const tooltip = this.cellView.cell?.attr().tooltip;
                const _div = (
                    <>
                        {tooltip.map(item => (
                            <div key={item} style={{display: "flex", alignItems: "center"}}>
                                <Badge count={1} color={item.split(",")[1]} dot />{" "}
                                <span style={{marginLeft: "10px"}}>{item.split(",")[0]}</span>
                            </div>
                        ))}
                        {!tooltip && <div>Loading...</div>}
                    </>
                );
                this.root = createRoot(this.knob);
                this.root.render(
                    <Tooltip title={_div ?? ""} open destroyTooltipOnHide overlayStyle={{maxWidth: "100%"}}>
                        <div />
                    </Tooltip>
                );
            }
        }
    }

    onMosueEnter({e}) {
        this.updatePosition(e);
        this.toggleTooltip(true);
    }

    onMouseLeave() {
        this.updatePosition();
        this.toggleTooltip(false);
    }

    onMouseMove() {
        this.updatePosition();
        this.toggleTooltip(false);
    }

    delegateEvents() {
        this.cellView.on("cell:mouseenter", this.onMosueEnter, this);
        this.cellView.on("cell:mouseleave", this.onMouseLeave, this);
        this.cellView.on("cell:mousemove", this.onMouseMove, this);
        return super.delegateEvents();
    }

    updatePosition(e) {
        const {style} = this.knob;
        if (e) {
            const {position, size} = this.cellView.cell.prop();
            const {x, y} = position;
            const {width} = size;
            const _x = x + width / 2;
            // 将获取到的节点位置宽高转换为client坐标,再从client转会graph坐标,实现在节点的中上方显示tooltip
            const point = this.graph.localToClient(_x, y);
            const p = this.graph.clientToGraph(point.x, point.y);
            style.display = "block";
            style.left = `${p.x}px`;
            style.top = `${p.y}px`;
        } else {
            style.display = "none";
            style.left = "-1000px";
            style.top = "-1000px";
        }
    }

    onRemove() {
        this.toggleTooltip(false);
        this.cellView.off("cell:mouseenter", this.onMosueEnter, this);
        this.cellView.off("cell:mouseleave", this.onMouseLeave, this);
        this.cellView.off("cell:mousemove", this.onMouseMove, this);
    }
}

export {Group, TooltipTool};
