import {createBrowserRouter, Navigate} from "react-router-dom";
import Login from "@/pages/Login/login";
import {eventBus} from "@/utils/common/event_bus";
import {AuthRoute} from "@/components/common/auth_route";
import NotFoundPage from "@/modules-ampcon/pages/NotFoundPage";
import {defaultRoute, customRoute} from "@/custom_modules";
import FSOTNLayout from "@/pages/Layout/otn/layout";
import FSAmpConLayout from "@/pages/Layout/ampcon/layout";

let Layout;

if (
    import.meta.env.VITE_APP_EXPORT_MODULE === "AmpCon-T" ||
    import.meta.env.VITE_APP_EXPORT_MODULE === "AmpCon-SUPER"
) {
    Layout = FSOTNLayout;
} else {
    Layout = FSAmpConLayout;
}

const router = createBrowserRouter([
    ...defaultRoute,
    {
        path: "/",
        element: (
            <AuthRoute>
                <Layout />
            </AuthRoute>
        ),
        children: [
            ...customRoute,
            {
                path: "NotFoundPage",
                element: <NotFoundPage />
            }
        ]
    },
    {
        path: "/login",
        element: <Login />
    },
    {
        path: "*",
        element: <Navigate to="/NotFoundPage" replace />
    }
]);

eventBus.on("redirectToLogin", () => {
    router.navigate("/login").then(() => {
        window.location.reload();
    });
});

eventBus.on("redirectTo", url => {
    router.navigate(url).then(() => {
        window.location.reload();
    });
});

export default router;
