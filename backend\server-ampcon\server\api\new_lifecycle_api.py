import logging
import platform
import random
import time
import traceback
from datetime import datetime, timedelta, date

import flask_login
from flask import Blueprint, render_template, request, jsonify, json, current_app, Response
from sqlalchemy import or_, and_, text

from server.constants import PICOS_V_SN
from server.service.upgrade_license import batch_upgrade_license
from server.util.permission import super_user_permission, super_admin_permission, admin_permission, readonly_permission

if platform.system() != 'Windows':
    from server.collect.rma_collect import collect_backup_config_single_group, collect_backup_config_single
from server import constants
from server.util import utils
from server.db.models import inventory
from server.db.models import user
from server import cfg
from server.ansible_lib.pica_lic import pica8_license
from celery_app import my_celery_app
from celery_app.automation_task import AmpConBaseTask, beat_task, AutomationTask
from server.util import ssh_util as conn_client
from server.util.utils import is_name_valid

inven_db = inventory.inven_db
new_lifecycle_model = Blueprint('new_lifecycle_model', __name__, template_folder='templates')
LOG = logging.getLogger(__name__)


@new_lifecycle_model.route('/license_audit')
@admin_permission.require(http_exception=403)
def license_audit():
    groups = utils.get_user_group().filter(inventory.Group.audit == True).all()
    report_time_list = get_last_reports(7, 'audit')

    content = {
        "groups": [group.group_name for group in groups],
        "reportTimeList": report_time_list,
    }
    return jsonify({'data': content, 'status': 200})

@new_lifecycle_model.route('/license_action')
@admin_permission.require(http_exception=403)
def license_action():
    groups = utils.get_user_group().filter(inventory.Group.action == True).all()
    report_time_list = get_last_reports(7, 'action')
    content = {
        "groups": [group.group_name for group in groups],
        "reportTimeList": report_time_list,
    }
    return jsonify({'data': content, 'status': 200})

@new_lifecycle_model.route('/lifecycle_license/<string:sn>/<string:action>')
@admin_permission.require(http_exception=403)
def lifecycle_license(sn, action):
    date_time = (date.today() + timedelta(days=30)).strftime('%Y-%m-%d')
    # app_user = inven_db.get_model(user.User, filters={'name': [flask_login.current_user.id]})
    switch_op_user, switch_op_password = utils.get_switch_default_user(sn=sn)
    switch_task_running = AmpConBaseTask.get_running_job_by_task_name(f"{sn}_{action}")
    if switch_task_running:
        LOG.info('license is in upgrade')
        return jsonify({'info': 'License is in upgrade', 'status': 200})
    batch_upgrade_license.delay(sn, '', '', '', switch_op_user,
                                switch_op_password, action, date_time, celery_sn=sn, celery_task_name=f"{sn}_{action}")
    return jsonify({'info': 'Upgrade license', 'status': 200})


def get_last_reports(days, report_action=''):
    current_time = datetime.utcnow()
    filter_time = current_time - timedelta(days=days)
    # Then we filter all log before the days
    db_session = inven_db.get_session()
    logs_register = db_session.query(inventory.SwitchLog).filter(inventory.SwitchLog.create_time > filter_time).filter(
        inventory.SwitchLog.report_action == report_action).order_by(inventory.SwitchLog.create_time.desc()).all()
    # Now we need get the list of report time
    logs_time_list = []
    if logs_register:
        for log in logs_register:
            if log.content[0:31] not in logs_time_list:
                logs_time_list.append(log.content[0:31])
    return logs_time_list


@new_lifecycle_model.route('/get_report_by_time/<string:report_time>')
@admin_permission.require(http_exception=403)
def get_report_by_time(report_time):
    logs_string = ''
    db_session = inven_db.get_session()
    logs_register = db_session.query(
        inventory.SwitchLog).filter(inventory.SwitchLog.content.contains(report_time)).order_by(
        inventory.SwitchLog.create_time.desc()).all()
    if logs_register:
        for log in logs_register:
            log_str = log.switch_id + ': ' + log.content + '\n'
            logs_string += log_str
    return logs_string


# @lifecycle_model.route('/get_report_by_push_image/<string:report_time>')
# @admin_permission.require(http_exception=403)
# def get_report_by_push_image(report_time):
#     logs_string = ''
#     db_session = inven_db.get_session()
#     logs_register = db_session.query(
#         inventory.SwitchLog).filter(inventory.SwitchLog.content.contains(report_time)).order_by(
#         inventory.SwitchLog.create_time.desc()).all()
#     if logs_register:
#         for log in logs_register:
#             log_str = str(log.create_time) + ': ' + log.content + '<br/>'
#             logs_string += log_str
#     modal_title = 'Push Image ' + report_time
#     return jsonify({'title': modal_title, 'log_str': logs_string})


# @lifecycle_model.route('/batch_remove_license', methods=["POST"])
# @admin_permission.require(http_exception=403)
# def batch_remove_license():
#     sn_list = json.loads(request.form.get('sn_array'))
#     db_session = inven_db.get_session()
#     for sn in sn_list:
#         license = db_session.query(inventory.License).filter(inventory.License.sn_num == sn).first()
#         license.upgrate = False
#         inven_db.merge(license)
#     return 'ok'


# @lifecycle_model.route('/group_management')
# @super_user_permission.require(http_exception=403)
# def group_management():
#     active = ('lifecycle', 'group_management')
#     return render_template('lifecycle/lifecycle_group_management.html', active=active,
#                            platforms=utils.get_search_models(), vpn_enable=cfg.CONF.vpn_enable)


# @lifecycle_model.route('/license_status/<string:action>')
# def license_status(action):
#     # 'import_%s_%s' % (action, group)
#     running_jobs = AmpConBaseTask.get_running_jobs()
#     task_name_list = []
#     if running_jobs:
#         for task in running_jobs:
#             # should only be license task
#             if 'switch_a' in task.task_name:
#                 model, lic_action, group = task.task_name.split('_', 2)
#                 if action == lic_action and group not in task_name_list:
#                     task_name_list.append(group)
#     return jsonify({'task_name': task_name_list})


@new_lifecycle_model.route('/group_management/data_with_picos_v', methods=['POST'])
def group_management_data_with_picos_v():
    # with picos_v
    extra_filter = [inventory.Switch.status.in_([constants.SwitchStatus.PROVISIONING_SUCCESS,
                                                  constants.SwitchStatus.IMPORTED])]
    page_num, page_size, total_count, data = utils.new_page_helper_lifecycle(extra_filter)
    return jsonify({'status': 200, 'data': data, 'total': total_count, 'page': page_num, "pageSize": page_size})


# @lifecycle_model.route('/group_management/data_with_all_group')
# def group_management_data_with_all_group():
#     # without picos_v
#     status_filter = [inventory.Switch.status.in_([constants.SwitchStatus.PROVISIONING_SUCCESS,
#                                                   constants.SwitchStatus.IMPORTED]), inventory.Switch.sn != PICOS_V_SN]
#     return group_management_data_implement(request, status_filter, False)


@new_lifecycle_model.route('/group_management/data', methods=['POST'])
def group_management_data():
    # without picos_v
    extra_filter = [inventory.Switch.status.in_([constants.SwitchStatus.PROVISIONING_SUCCESS,
                                                  constants.SwitchStatus.IMPORTED]), inventory.Switch.sn != PICOS_V_SN]
    page_num, page_size, total_count, data = utils.new_page_helper_lifecycle(extra_filter)
    return jsonify({'status': 200, 'data': data, 'total': total_count, 'page': page_num, "pageSize": page_size})


@new_lifecycle_model.route('/fabric_management/data', methods=['POST'])
def fabric_management_data():
    # without picos_v
    extra_filter = [inventory.Switch.status.in_([constants.SwitchStatus.PROVISIONING_SUCCESS,
                                                 constants.SwitchStatus.IMPORTED, constants.SwitchStatus.STAGED,
                                                 constants.SwitchStatus.STAGING, constants.SwitchStatus.CONFIGURED,
                                                 constants.SwitchStatus.PROVISIONING_FAILED,
                                                 constants.SwitchStatus.REGISTERED, constants.SwitchStatus.DECOM,
                                                 constants.SwitchStatus.DECOM_MANUAL,
                                                 constants.SwitchStatus.DECOM_INIT,
                                                 constants.SwitchStatus.DECOM_PENDING, constants.SwitchStatus.RMA])]
    page_num, page_size, total_count, data = utils.new_page_helper_lifecycle(extra_filter)
    return jsonify({'status': 200, 'data': data, 'total': total_count, 'page': page_num, "pageSize": page_size})


@new_lifecycle_model.route('/site_management/data', methods=['POST'])
def site_management_data():
    # without picos_v
    extra_filter = [inventory.Switch.status.in_([constants.SwitchStatus.PROVISIONING_SUCCESS,
                                                 constants.SwitchStatus.IMPORTED, constants.SwitchStatus.STAGED,
                                                 constants.SwitchStatus.STAGING, constants.SwitchStatus.CONFIGURED,
                                                 constants.SwitchStatus.PROVISIONING_FAILED,
                                                 constants.SwitchStatus.REGISTERED, constants.SwitchStatus.DECOM,
                                                 constants.SwitchStatus.DECOM_MANUAL,
                                                 constants.SwitchStatus.DECOM_INIT,
                                                 constants.SwitchStatus.DECOM_PENDING, constants.SwitchStatus.RMA])]
    page_num, page_size, total_count, data = utils.new_page_helper_lifecycle(extra_filter)
    return jsonify({'status': 200, 'data': data, 'total': total_count, 'page': page_num, "pageSize": page_size})


@new_lifecycle_model.route('/license_table_data/<string:group_name>', methods=['POST'])
@admin_permission.require(http_exception=403)
def license_table_data(group_name):
    extra_filter = [inventory.Switch.status.in_([constants.SwitchStatus.PROVISIONING_SUCCESS,
                                                 constants.SwitchStatus.IMPORTED])]
    page_num, page_size, total_count, data = utils.new_page_helper_lifecycle(extra_filter, group_name)
    return jsonify({'status': 200, 'data': data, 'total': total_count, 'page': page_num, "pageSize": page_size})


@new_lifecycle_model.route('/upgrade_table/data', methods=['POST'])
@admin_permission.require(http_exception=403)
def upgrade_table_data():
    selected_platform = request.get_json().get('selectedPlatform', None)
    extra_filter = [inventory.Switch.status.in_([constants.SwitchStatus.PROVISIONING_SUCCESS,
                                                 constants.SwitchStatus.IMPORTED])]
    page_num, page_size, total_count, data = utils.new_page_helper_lifecycle(extra_filter, selected_platform=selected_platform)
    return jsonify({'status': 200, 'data': data, 'total': total_count, 'page': page_num, "pageSize": page_size})


# def group_management_data_implement(request, status_filter, is_filter_group=True):
#     search_value = request.args.get('search[value]')
#     switch_rule = None
    
#     if search_value and search_value != '':
#         search_value = '%' + search_value + '%'
        
#         switch_rule = or_(*[inventory.Switch.sn.like(search_value),
#                             inventory.Switch.version.like(search_value),
#                             inventory.Switch.host_name.like(search_value),
#                             inventory.Switch.mgt_ip.like(search_value),
#                             inventory.License.license_expired.like(search_value),
#                             inventory.License.status.like(search_value),
#                             inventory.Switch.post_deployed_config.like(search_value)
#                             ])
    
#     # apply column filters
#     session = inven_db.get_session()
#     filter_rules = []
#     for key, value in request.args.items():
#         if '[search][value]' in key and value != '':
#             # get column name
#             data_key = key.replace('[search][value]', '[data]')
#             column_name = request.args.get(data_key, '')
#             if column_name == 'host_name':
#                 filter_rules.append(inventory.Switch.host_name.like('%' + value + '%'))
#             elif column_name == 'version':
#                 if value != 'All':
#                     filter_rules.append(inventory.Switch.version == value)
#             elif column_name == 'license_status':
#                 if value == 'No License':
#                     filter_rules.append(inventory.License.status == None)
#                 elif value != 'All':
#                     filter_rules.append(inventory.License.status == value)
#             elif column_name == 'mgt_ip':
#                 if '~' in value:
#                     start_ip, _, end_ip = value.partition('~')
#                     filter_rules.append(
#                         text("inet_aton(switch.mgt_ip) BETWEEN inet_aton('%s') and inet_aton('%s')" % (
#                             start_ip, end_ip)))
#                 elif value != '':
#                     filter_rules.append(inventory.Switch.mgt_ip.like('%' + value + '%'))
#             elif column_name == 'group':
#                 if value != 'All':
#                     subquery = session.query(inventory.AssociationGroup.switch_sn).filter(
#                         inventory.AssociationGroup.group_name == value).subquery()
#                     filter_rules.append(inventory.Switch.sn.in_(subquery))
#             elif column_name == 'platform' or column_name == 'platform_model':
#                 if value != 'All':
#                     filter_rules.append(inventory.Switch.platform_model == value)
#             elif column_name == 'address':
#                 if value != '':
#                     filter_rules.append(inventory.Switch.address == value)
    
#     if filter_rules:
#         if switch_rule is not None:
#             switch_rule = and_(switch_rule, *filter_rules)
#         else:
#             switch_rule = and_(*filter_rules)
    
#     return utils.page_helper_lifecycle(request.args, rule=switch_rule, status_filter=status_filter, session=session, is_filter_group=is_filter_group)


@new_lifecycle_model.route('/create_group', methods=["POST"])
@super_user_permission.require(http_exception=403)
@utils.operation_log(method='create_group', contents='create group {group_name}')
def create_group():
    params = request.get_json()

    if flask_login.current_user.type != 'superuser':
        return jsonify({'info': "Only superuser can create group!", 'status': 400})

    action_list = params.get('actionList')
    group_name = params.get('groupName')
    description = params.get('description')

    if not is_name_valid(group_name):
        return jsonify({'info': "Group name is invalid", 'status': 400})

    db_session = inven_db.get_session()
    if db_session.query(inventory.Group).filter(inventory.Group.group_name == group_name).first():
        return jsonify({'info': "Group already exist", 'status': 400})
    
    with db_session.begin():
        group = inventory.Group()
        group.group_name = group_name
        group.description = description
        for attr in action_list:
            setattr(group, attr, True)
        db_session.add(group)
    return jsonify({'info': "Group create successfully", 'status': 200})


@new_lifecycle_model.route('/groups')
@super_user_permission.require(http_exception=403)
def get_groups():
    db_session = inven_db.get_session()
    group_names = db_session.query(inventory.Group.group_name).group_by(inventory.Group.group_name).all()
    group_names = [group_name_i.group_name for group_name_i in group_names]
    return jsonify({'data': group_names, 'status': 200})


@new_lifecycle_model.route('/delete_group/<string:group_name>')
@super_user_permission.require(http_exception=403)
@utils.operation_log(method='delete_group', contents='delete group {group_name}')
def delete_group(group_name):
    db_session = inven_db.get_session()
    # check whether it is used in background tasks
    jobs = AmpConBaseTask.get_running_jobs()
    if jobs:
        for job in jobs:
            if 'group_upgrade::{0}::'.format(group_name) in job.task_name:
                msg = {'info': 'Group {0} is used for group upgrade tasks'.format(group_name), 'status': 400}
                return jsonify(msg)
            if 'group_push_image::{0}::'.format(group_name) in job.task_name:
                msg = {'info': 'Group {0} is used for group push image tasks'.format(group_name), 'status': 400}
                return jsonify(msg)
    utils.delete_group(group_name)
    msg = {'info': 'Success to delete group {0}'.format(group_name), 'status': 200}
    return jsonify(msg)


# @lifecycle_model.route('/show_group/<string:sn>')
# def show_group(sn):
#     db_session = inven_db.get_session()
#     groups = db_session.query(
#         inventory.AssociationGroup.group_name).filter(inventory.AssociationGroup.switch_sn == sn).group_by(
#         inventory.AssociationGroup.group_name).all()
#     group_names = [group.group_name for group in groups]
#     return jsonify(group_names)


@new_lifecycle_model.route('/load_group/<string:group_name>')
def load_group(group_name):
    db_session = inven_db.get_session()
    group = db_session.query(inventory.Group).filter(inventory.Group.group_name == group_name).first()
    audit = group.audit
    action = group.action
    upgrading = group.upgrading
    retrieve_config = group.retrieve_config
    return jsonify({'data': {'group_name': group.group_name, 'group_des': group.description,
         'audit': audit, 'action': action, 'upgrading': upgrading, 'retrieve_config': retrieve_config}, 'status': 200})


@new_lifecycle_model.route('/save_group', methods=["POST"])
@super_user_permission.require(http_exception=403)
def save_group():
    params = request.get_json()
    
    action_list = params.get('actionList')
    group_name = params.get('groupName')
    # description = params.get('description')
    del_switches = params.get('delSwitches')
    new_switches = params.get('addSwitches', [])
    
    group_action_list = ['audit', 'action', 'upgrading', 'retrieve_config']
    
    db_session = inven_db.get_session()
    db_session.query(inventory.AssociationGroup).filter(inventory.AssociationGroup.group_name == group_name,
                                                        inventory.AssociationGroup.switch_sn.in_(del_switches)) \
        .delete(synchronize_session=False)
    
    group = db_session.query(inventory.Group).filter(inventory.Group.group_name == group_name).first()
    if not group:
        # return Response('group %s not found' % Markup.escape(group_name), status=400)
        return jsonify({'info': 'Group %s not found' % str(group_name), 'status': 400})
    
    with db_session.begin():
        group.group_name = group_name
        # group.description = description
        for attr in group_action_list:
            if attr in action_list:
                setattr(group, attr, True)
            else:
                setattr(group, attr, False)
        
        for switch in new_switches:
            association_group = inventory.AssociationGroup()
            association_group.switch_sn = switch
            association_group.group_name = group.group_name
            group.association_group.append(association_group)
        db_session.merge(group)
    return jsonify({'info': "Group edit successfully", 'status': 200})


@new_lifecycle_model.route('/fabric')
@readonly_permission.require(http_exception=403)
def get_fabric():
    db_session = inven_db.get_session()
    fabric_names = list(map(lambda x: x[0], db_session.query(inventory.Fabric.fabric_name).all()))
    return jsonify({'data': fabric_names, 'status': 200})


@new_lifecycle_model.route('/create_fabric', methods=["POST"])
@admin_permission.require(http_exception=403)
@utils.operation_log(method='create_fabric', contents='create fabric')
def create_fabric():
    params = request.get_json()

    fabric_name = params.get('fabricName', '')
    description = params.get('description', '')

    if (not is_name_valid(fabric_name)) or ' ' in fabric_name or len(fabric_name) > 32:
        return jsonify({'info': "Fabric name is invalid", 'status': 400})

    if description and len(description) > 128:
        return jsonify({'info': "Fabric description is too long", 'status': 400})

    db_session = inven_db.get_session()
    if db_session.query(inventory.Fabric).filter(inventory.Fabric.fabric_name == fabric_name).first():
        return jsonify({'info': "Fabric already exist", 'status': 400})

    try:
        with db_session.begin():
            fabric_topology = inventory.Topology()
            fabric_topology.name = fabric_name
            fabric_topology.description = description
            fabric_topology.topology_type = 'fabric'
            db_session.add(fabric_topology)

            fabric_topology_id = db_session.query(inventory.Topology).filter(inventory.Topology.name == fabric_name, inventory.Topology.topology_type == 'fabric').first().id

            fabric = inventory.Fabric()
            fabric.fabric_name = fabric_name
            fabric.description = description
            fabric.topology_id = fabric_topology_id
            db_session.add(fabric)
        return jsonify({'info': "Fabric create successfully", 'status': 200})
    except Exception as e:
        LOG.error(traceback.format_exc())
        db_session.rollback()
        msg = {'info': 'Fabric create failed', 'status': 500}
        return jsonify(msg)


@new_lifecycle_model.route('/delete_fabric', methods=["POST"])
@admin_permission.require(http_exception=403)
@utils.operation_log(method='delete_fabric', contents='delete fabric')
def delete_fabric():
    params = request.get_json()

    fabric_name = params.get('fabricName')

    db_session = inven_db.get_session()
    fabric = db_session.query(inventory.Fabric).filter(inventory.Fabric.fabric_name == fabric_name).first()
    if not fabric:
        return jsonify({'info': "Fabric not found", 'status': 400})

    try:
        with db_session.begin():
            # remove association fabric data
            db_session.query(inventory.AssociationFabric).filter(inventory.AssociationFabric.fabric_id == fabric.id).delete()
            db_session.query(inventory.Topology).filter(inventory.Topology.id == fabric.topology_id).delete()
            db_session.query(inventory.Fabric).filter(inventory.Fabric.fabric_name == fabric_name).delete()

        msg = {'info': 'Success to delete fabric {0}'.format(fabric_name), 'status': 200}
        return jsonify(msg)
    except Exception as e:
        LOG.error(traceback.format_exc())
        db_session.rollback()
        msg = {'info': 'Failed to delete fabric {0}'.format(fabric_name), 'status': 500}
        return jsonify(msg)


@new_lifecycle_model.route('/load_fabric/<string:fabric_name>')
@readonly_permission.require(http_exception=403)
def load_fabric(fabric_name):
    db_session = inven_db.get_session()
    fabric = db_session.query(inventory.Fabric).filter(inventory.Fabric.fabric_name == fabric_name).first()
    if not fabric:
        return jsonify({'info': "Fabric not found", 'status': 400})
    return jsonify({'data': {'fabric_name': fabric.fabric_name, 'fabric_description': fabric.description}, 'status': 200})


@new_lifecycle_model.route('/load_fabric_switch', methods=["POST"])
@readonly_permission.require(http_exception=403)
def load_fabric_switch():
    params = request.get_json()

    fabric_name = params.get('fabricName')

    db_session = inven_db.get_session()
    fabric = db_session.query(inventory.Fabric).filter(inventory.Fabric.fabric_name == fabric_name).first()
    if not fabric:
        return jsonify({'info': "Fabric not found", 'status': 400})

    extra_filter = [inventory.Switch.status.in_([constants.SwitchStatus.PROVISIONING_SUCCESS,
                                                 constants.SwitchStatus.IMPORTED, constants.SwitchStatus.STAGED,
                                                 constants.SwitchStatus.STAGING, constants.SwitchStatus.CONFIGURED,
                                                 constants.SwitchStatus.PROVISIONING_FAILED,
                                                 constants.SwitchStatus.REGISTERED, constants.SwitchStatus.DECOM,
                                                 constants.SwitchStatus.DECOM_MANUAL,
                                                 constants.SwitchStatus.DECOM_INIT,
                                                 constants.SwitchStatus.DECOM_PENDING, constants.SwitchStatus.RMA])]
    page_num, page_size, total_count, data = utils.new_page_helper_lifecycle(extra_filter, fabric=[fabric_name])
    return jsonify({'status': 200, 'data': data, 'total': total_count, 'page': page_num, "pageSize": page_size})


@new_lifecycle_model.route('/save_fabric', methods=["POST"])
@admin_permission.require(http_exception=403)
def save_fabric():
    params = request.get_json()

    fabric_name = params.get('fabricName')
    del_switches = params.get('delSwitches', [])
    new_switches = params.get('addSwitches', [])
    save_result, save_result_msg = utils.save_switch_to_fabric(fabric_name, new_switches, del_switches)
    return jsonify({'info': save_result_msg, 'status': 200 if save_result else 400})


@new_lifecycle_model.route('/edit_fabric_table_data', methods=["POST"])
@readonly_permission.require(http_exception=403)
def edit_fabric_table_data():
    try:
        data = request.get_json()
        fabric_name = data.get("fabricName", None)
        page_num, page_size, total_count, query_obj = utils.query_helper(inventory.Switch, pre_query=utils.query_switch(fabric_list=[fabric_name] if fabric_name else None))
        if not fabric_name:
            response = {
                "data": [{
                    "id": switch.id,
                    "host_name": switch.host_name,
                    "sn": switch.sn,
                    "create_time": switch.create_time.strftime('%Y-%m-%d %H:%M:%S') if switch.create_time else "",
                    "version": switch.version,
                    "revision": switch.revision,
                    "status": switch.status,
                    "mgt_ip": switch.mgt_ip,
                    "platform_model": switch.platform_model,
                    "selected": False,
                    "reachable_status": switch.reachable_status
                } for switch in query_obj],
                "page": page_num,
                "pageSize": page_size,
                "total": total_count,
                "status": 200
            }
        else:
            response = {
                "data": [{
                    "id": switch.id,
                    "host_name": switch.host_name,
                    "sn": switch.sn,
                    "create_time": switch.create_time.strftime('%Y-%m-%d %H:%M:%S') if switch.create_time else "",
                    "version": switch.version,
                    "revision": switch.revision,
                    "status": switch.status,
                    "mgt_ip": switch.mgt_ip,
                    "platform_model": switch.platform_model,
                    "selected": selected,
                    "reachable_status": switch.reachable_status
                } for switch, selected in query_obj],
                "page": page_num,
                "pageSize": page_size,
                "total": total_count,
                "status": 200
            }
        return jsonify(response)
    except Exception as e:
        LOG.error(traceback.format_exc())
        msg = {'info': 'Get add device modal table data fail', 'status': 500}
        return jsonify(msg)


@new_lifecycle_model.route('/site')
@readonly_permission.require(http_exception=403)
def get_site():
    db_session = inven_db.get_session()
    site_names = list(map(lambda x: x[0], db_session.query(inventory.Site.site_name).all()))
    return jsonify({'data': site_names, 'status': 200})


@new_lifecycle_model.route('/create_site', methods=["POST"])
@admin_permission.require(http_exception=403)
@utils.operation_log(method='create_site', contents='create site')
def create_site():
    params = request.get_json()

    site_name = params.get('siteName', '')
    description = params.get('description', '')

    if not is_name_valid(site_name) or ' ' in site_name or len(site_name) > 32:
        return jsonify({'info': "Site name is invalid", 'status': 400})

    if description and len(description) > 128:
        return jsonify({'info': "Site description is too long", 'status': 400})

    db_session = inven_db.get_session()
    if db_session.query(inventory.Site).filter(inventory.Site.site_name == site_name).first():
        return jsonify({'info': "Site already exist", 'status': 400})

    try:
        with db_session.begin():
            site_topology = inventory.Topology()
            site_topology.name = site_name
            site_topology.description = description
            site_topology.topology_type = 'site'
            db_session.add(site_topology)

            site_topology_id = db_session.query(inventory.Topology).filter(inventory.Topology.name == site_name).first().id

            site = inventory.Site()
            site.site_name = site_name
            site.description = description
            site.topology_id = site_topology_id
            db_session.add(site)
        return jsonify({'info': "Site create successfully", 'status': 200})
    except Exception as e:
        LOG.error(traceback.format_exc())
        db_session.rollback()
        msg = {'info': 'Site create failed', 'status': 500}
        return jsonify(msg)


@new_lifecycle_model.route('/delete_site', methods=["POST"])
@admin_permission.require(http_exception=403)
@utils.operation_log(method='delete_site', contents='delete site')
def delete_site():
    params = request.get_json()

    site_name = params.get('siteName')

    db_session = inven_db.get_session()
    site = db_session.query(inventory.Site).filter(inventory.Site.site_name == site_name).first()
    if not site:
        return jsonify({'info': "Site not found", 'status': 400})

    try:
        with db_session.begin():
            # remove association site data
            db_session.query(inventory.AssociationSite).filter(inventory.AssociationSite.site_id == site.id).delete()
            db_session.query(inventory.Topology).filter(inventory.Topology.id == site.topology_id).delete()
            db_session.query(inventory.Site).filter(inventory.Site.site_name == site_name).delete()

        msg = {'info': 'Success to delete site {0}'.format(site_name), 'status': 200}
        return jsonify(msg)
    except Exception as e:
        LOG.error(traceback.format_exc())
        db_session.rollback()
        msg = {'info': 'Failed to delete site {0}'.format(site_name), 'status': 500}
        return jsonify(msg)


@new_lifecycle_model.route('/load_site/<string:site_name>')
@readonly_permission.require(http_exception=403)
def load_site(site_name):
    db_session = inven_db.get_session()
    site = db_session.query(inventory.Site).filter(inventory.Site.site_name == site_name).first()
    if not site:
        return jsonify({'info': "Site not found", 'status': 400})
    return jsonify({'data': {'site_name': site.site_name, 'site_description': site.description}, 'status': 200})


@new_lifecycle_model.route('/load_site_switch', methods=["POST"])
@readonly_permission.require(http_exception=403)
def load_site_switch():
    params = request.get_json()

    site_name = params.get('siteName')

    db_session = inven_db.get_session()
    site = db_session.query(inventory.Site).filter(inventory.Site.site_name == site_name).first()
    if not site:
        return jsonify({'info': "Site not found", 'status': 400})

    extra_filter = [inventory.Switch.status.in_([constants.SwitchStatus.PROVISIONING_SUCCESS,
                                                 constants.SwitchStatus.IMPORTED, constants.SwitchStatus.STAGED,
                                                 constants.SwitchStatus.STAGING, constants.SwitchStatus.CONFIGURED,
                                                 constants.SwitchStatus.PROVISIONING_FAILED,
                                                 constants.SwitchStatus.REGISTERED, constants.SwitchStatus.DECOM,
                                                 constants.SwitchStatus.DECOM_MANUAL,
                                                 constants.SwitchStatus.DECOM_INIT,
                                                 constants.SwitchStatus.DECOM_PENDING, constants.SwitchStatus.RMA])]
    page_num, page_size, total_count, data = utils.new_page_helper_lifecycle(extra_filter, site=[site_name])
    return jsonify({'status': 200, 'data': data, 'total': total_count, 'page': page_num, "pageSize": page_size})


@new_lifecycle_model.route('/save_site', methods=["POST"])
@admin_permission.require(http_exception=403)
def save_site():
    params = request.get_json()

    site_name = params.get('siteName')
    del_switches = params.get('delSwitches', [])
    new_switches = params.get('addSwitches', [])

    save_result, save_result_msg = utils.save_switch_to_site(site_name, new_switches, del_switches)
    return jsonify({'info': save_result_msg, 'status': 200 if save_result else 400})


@new_lifecycle_model.route('/edit_site_table_data', methods=["POST"])
@readonly_permission.require(http_exception=403)
def edit_site_table_data():
    try:
        data = request.get_json()
        site_name = data.get("siteName", None)
        page_num, page_size, total_count, query_obj = utils.query_helper(inventory.Switch, pre_query=utils.query_switch(site_list=[site_name] if site_name else None))
        if not site_name:
            response = {
                "data": [{
                    "id": switch.id,
                    "host_name": switch.host_name,
                    "sn": switch.sn,
                    "create_time": switch.create_time.strftime('%Y-%m-%d %H:%M:%S') if switch.create_time else "",
                    "version": switch.version,
                    "revision": switch.revision,
                    "status": switch.status,
                    "mgt_ip": switch.mgt_ip,
                    "platform_model": switch.platform_model,
                    "selected": False,
                    "reachable_status": switch.reachable_status
                } for switch in query_obj],
                "page": page_num,
                "pageSize": page_size,
                "total": total_count,
                "status": 200
            }
        else:
            response = {
                "data": [{
                    "id": switch.id,
                    "host_name": switch.host_name,
                    "sn": switch.sn,
                    "create_time": switch.create_time.strftime('%Y-%m-%d %H:%M:%S') if switch.create_time else "",
                    "version": switch.version,
                    "revision": switch.revision,
                    "status": switch.status,
                    "mgt_ip": switch.mgt_ip,
                    "platform_model": switch.platform_model,
                    "selected": selected,
                    "reachable_status": switch.reachable_status
                } for switch, selected in query_obj],
                "page": page_num,
                "pageSize": page_size,
                "total": total_count,
                "status": 200
            }
        return jsonify(response)
    except Exception as e:
        LOG.error(traceback.format_exc())
        msg = {'info': 'Get add device modal table data fail', 'status': 500}
        return jsonify(msg)


# @lifecycle_model.route('/switch/versions/get')
# def get_switch_versions():
#     versions = inven_db.get_all_switch_versions()
#     return jsonify(versions)


# @lifecycle_model.route('/main_panel')
# @admin_permission.require(http_exception=403)
# def main_panel():
#     active = ('lifecycle', 'lifecycle_main_panel')
#     versions = inven_db.get_all_switch_versions()
#     group_names = utils.get_user_group().all()
#     group_names = [group_name_i.group_name for group_name_i in group_names]
#     return render_template('lifecycle/lifecycle_main_panel.html', active=active, versions=versions, groups=group_names,
#                            p_models=utils.get_search_models(), vpn_enable=cfg.CONF.vpn_enable)


# @lifecycle_model.route('/license_audit')
# @admin_permission.require(http_exception=403)
# def license_audit():
#     active = ('license', 'license_audit')
#     groups = utils.get_user_group().filter(inventory.Group.audit == True).all()
#     report_time_list = get_last_reports(7, 'audit')
#     return render_template('lifecycle/lifecycle_license_audit.html', active=active, report_time_list=report_time_list,
#                            groups=groups, vpn_enable=cfg.CONF.vpn_enable)


# @lifecycle_model.route('/license_action')
# @admin_permission.require(http_exception=403)
# def license_action():
#     active = ('license', 'license_action')
#     groups = utils.get_user_group().filter(inventory.Group.action == True).all()
#     report_time_list = get_last_reports(7, 'action')
#     return render_template('lifecycle/lifecycle_license_action.html', active=active, report_time_list=report_time_list,
#                            groups=groups, vpn_enable=cfg.CONF.vpn_enable)


# @lifecycle_model.route('/lifecycle_upgrade')
# @admin_permission.require(http_exception=403)
# def lifecycle_upgrade():
#     active = ('lifecycle', 'lifecycle_upgrade')
#     groups = utils.get_user_group().filter(inventory.Group.upgrading == True).all()
    
#     current_time = datetime.utcnow()
#     filter_time = current_time - timedelta(days=7)
#     # Then we filter all log before the days
#     db_session = inven_db.get_session()
#     logs_register = db_session.query(inventory.SwitchLog).filter(inventory.SwitchLog.create_time > filter_time).filter(
#         inventory.SwitchLog.report_action == 'upgrade').order_by(inventory.SwitchLog.create_time.desc()).all()
#     # Now we need get the list of report time
#     report_time_list = []
#     if logs_register:
#         for log in logs_register:
#             tmp_content = log.content.partition("]")[0].replace("[", "")
#             if log.content.startswith("[") and tmp_content not in report_time_list:
#                 report_time_list.append(tmp_content)
#     return render_template('lifecycle/lifecycle_upgrade.html', active=active, report_time_list=report_time_list,
#                            groups=groups, vpn_enable=cfg.CONF.vpn_enable)


# @lifecycle_model.route('/retrieve_config')
# @admin_permission.require(http_exception=403)
# def retrieve_config():
#     active = ('lifecycle', 'retrieve_config')
#     groups = utils.get_user_group().filter(inventory.Group.retrieve_config == True).all()
#     report_time_list = get_last_reports(7, 'retrieve_config')
#     task_obj = beat_task.get_active_job_by_name("collect-backup-config-all-task")
#     target_str = str(task_obj).split(":")[1].strip().split(" ")
#     job_view = {'day': target_str[2].split('/')[1] if '/' in target_str[2] else target_str[2], 'hour': target_str[1]}
#     return render_template('lifecycle/lifecycle_retrieve_config.html', active=active, report_time_list=report_time_list,
#                            groups=groups, job=job_view, vpn_enable=cfg.CONF.vpn_enable)


# @lifecycle_model.route('/license_table_data/<string:group_name>')
# @admin_permission.require(http_exception=403)
# def license_table_data(group_name):
#     search_value = request.args.get('search[value]')
#     switch_rule = None
#     status_filter = inventory.Switch.status.in_([constants.SwitchStatus.PROVISIONING_SUCCESS,
#                                                  constants.SwitchStatus.IMPORTED])
#     if search_value and search_value != '':
#         search_value = '%' + search_value + '%'
        
#         switch_rule = or_(*[inventory.Switch.sn.like(search_value),
#                             inventory.Switch.version.like(search_value),
#                             inventory.Switch.host_name.like(search_value),
#                             inventory.Switch.mgt_ip.like(search_value),
#                             inventory.License.license_expired.like(search_value),
#                             inventory.License.status.like(search_value),
#                             ])
    
#     return utils.page_helper_lifecycle(request.args, rule=switch_rule,
#                                        status_filter=status_filter, group_name=group_name)


@new_lifecycle_model.route('/lifecycle_renewal/<string:action>/<string:group>')
def lifecycle_renewal(action, group):
    date_time = (date.today() + timedelta(days=30)).strftime('%Y-%m-%d')
    report_time = time.strftime("%Y-%m-%d %H:%M", time.localtime()) + "<" + action + " report> "
    switch_task_running = AmpConBaseTask.get_running_job_by_task_name('switch_%s_%s' % (action, group))
    if switch_task_running:
        LOG.info('license is in upgrade')
        return jsonify({'info': 'License is in upgrade', 'status': 200})
    else:
        # useless switch_op_user, switch_op_password
        switch_op_user, switch_op_password = utils.get_switch_default_user()
        batch_upgrade_license.delay('', group, '', '', switch_op_user,
                                    switch_op_password, action, date_time,
                                    report_time=report_time, celery_task_name=f"switch_{action}_{group}",
                                    celery_group=group)
    return jsonify({'info': 'Upgrade license', 'status': 200})


# @lifecycle_model.route('/task_management')
# @admin_permission.require(http_exception=403)
# def task_management():
#     active = ('lifecycle', 'task_management')
#     return render_template('task/task_management.html', active=active)


# @lifecycle_model.route('/task_management/data')
# @admin_permission.require(http_exception=403)
# def task_management_data():
#     now = datetime.now()
#     session = inven_db.get_session()
#     current_group_task = []
#     with session.begin(subtransactions=True):
#         group_tasks = session.query(inventory.GroupTask)
#         # order data
#         order_column_index = request.args.get('order[0][column]')
#         order_column = request.args.get('columns[' + order_column_index + '][data]')
#         order_dir = request.args.get('order[0][dir]')
#         # for support task_type order
#         if order_column and order_column != 'task_type':
#             if order_dir == 'desc':
#                 order = getattr(inventory.GroupTask, order_column).desc()
#             else:
#                 order = getattr(inventory.GroupTask, order_column).asc()
#         else:
#             order = None
#         if order is not None:
#             group_tasks = group_tasks.order_by(order)
#         # end order
#         for group_task in group_tasks:
#             status = group_task.status
#             if not group_task.start_date or not group_task.end_date:
#                 continue
#             if group_task.start_date < now < group_task.end_date:
#                 switches = inven_db.get_group_switchs_new(group_task.name, session=session)
#                 task_names = ['%s::%s::%s' % (constants.GROUP_TYPE_DICT[group_task.type], group_task.name, switch.sn.replace("_DECOM", ""))
#                               for switch in switches]
#                 query = session.query(AutomationTask)
#                 executed_num = query.filter(AutomationTask.task_name.in_(task_names),
#                                             AutomationTask.task_status == 'success').group_by(
#                     AutomationTask.task_name).count()
#                 error_num = query.filter(AutomationTask.task_name.in_(task_names),
#                                          AutomationTask.task_status == 'failure').group_by(
#                     AutomationTask.task_name).count()
                
#                 if executed_num + error_num == len(task_names):
#                     if status != 'finished':
#                         group_task.status = 'finished'
#                         status = 'finished'
#                 else:
#                     if status != 'paused':
#                         group_task.status = 'processing'
#                         status = 'processing'
            
#             elif group_task.end_date < now:
#                 if status != 'finished':
#                     group_task.status = 'finished'
#                     status = 'finished'
            
#             group_task = {
#                 'name': group_task.name,
#                 'task_type': group_task.type,
#                 'start_date': group_task.start_date,
#                 'end_date': group_task.end_date,
#                 'status': status
#             }
#             current_group_task.append(group_task)
#     # task_type order
#     if order_column == 'task_type':
#         if order_dir == 'desc':
#             current_group_task = list(sorted(current_group_task, key=lambda x: x.get('task_type'), reverse=True))
#         else:
#             current_group_task = list(sorted(current_group_task, key=lambda x: x.get('task_type')))
#     start = int(request.args.get('start'))
#     length = int(request.args.get('length'))
#     ret = current_group_task[start: start + length]
#     records_total = records_filtered = len(current_group_task)
#     return json.dumps({'data': ret, 'recordsTotal': records_total, 'recordsFiltered': records_filtered}, default=str)


# @lifecycle_model.route('/delete_upgrade_task/<string:task>/<string:task_type>')
# @admin_permission.require(http_exception=403)
# @utils.operation_log(method='/delete_upgrade_task/', contents='terminate a upgrading task {task}, type {task_type}')
# def delete_upgrade_task(task, task_type):
#     LOG.warn('Delete a upgrading task %s', task)
#     jobs = beat_task.get_all_jobs()
#     tmp_task = "%s::%s" % (constants.GROUP_TYPE_DICT[task_type], task)
#     for job in jobs:
#         if tmp_task in job.name:
#             beat_task.remove_job(job.name)
#             AmpConBaseTask.kill_process_by_task_name(job.name)
    
#     # clear group task db status
#     session = inven_db.get_session()
#     session.query(inventory.GroupTask).filter(inventory.GroupTask.name == task,
#                                               inventory.GroupTask.type == task_type).delete(synchronize_session=False)
#     msg = {'info': 'Task Terminated', 'status': '200'}
#     return jsonify(msg)


# @lifecycle_model.route('/lifecycle_retrieve/sn')
# @admin_permission.require(http_exception=403)
# @utils.operation_log(method='/lifecycle_retrieve/sn', contents='retrieve configuration in switch SN: {sn}, IP: {ip}.')
# def lifecycle_sn_retrieve_config():
#     sn = request.args.get('sn')
#     ip = request.args.get('ip')
#     # execute ansible to collect switch config
#     # need to know the switch is import or is registed
#     try:
#         if collect_backup_config_single(ip, sn) == constants.RMA_ACTIVE:
#             inven_db.add_switch_log(sn, "Retrieve config success", level='info')
#             msg = {'status': '200', 'info': 'back-up success'}
#         else:
#             inven_db.add_switch_log(sn, "Retrieve config failed", level='warn')
#             msg = {'status': '400', 'info': 'back-up failed'}
#     except Exception as e:
#         LOG.exception(e)
#         inven_db.add_switch_log(sn, "Retrieve config failed", level='warn')
#         msg = {'status': '500', 'info': 'back-up failed'}
#     return jsonify(msg)


# @lifecycle_model.route('/lifecycle_retrieve/group/<string:group_name>')
# @admin_permission.require(http_exception=403)
# @utils.operation_log(method='/lifecycle_retrieve/group/', contents='retrieve configuration in group {group_name}')
# def lifecycle_retrieve_config(group_name):
#     date_time = (date.today()).strftime('%Y-%m-%d')
#     switch_task_running = AmpConBaseTask.get_running_job_by_task_name(f'switch_retrieve_{group_name}')
#     if switch_task_running:
#         LOG.info('switch is retrieving  %s', date_time)
#         return 'ok'
#     else:
#         db_session = inven_db.get_session()
#         switches_sn_list = []
#         ag_switches = db_session.query(inventory.AssociationGroup).filter(
#             inventory.AssociationGroup.group_name == group_name).all()
#         for group_switch in ag_switches:
#             switches_sn_list.append(group_switch.switch_sn)
#         report_time = time.strftime("%Y-%m-%d %H:%M", time.localtime()) + "<" + 'retrieve_config' + " report> "
#         batch_retrieve_config.delay(switches_sn_list, report_time, celery_task_name=f"switch_retrieve_{group_name}",
#                                     celery_group=group_name)
#     return 'ok'


# @my_celery_app.task(name="batch_retrieve_config", base=AmpConBaseTask)
# def batch_retrieve_config(switches_sn_list, report_time, **kwargs):
#     db_session = inven_db.get_session()
#     for sn in switches_sn_list:
#         switch = db_session.query(inventory.Switch).filter(inventory.Switch.sn == sn).first()
#         if switch:
#             collect_backup_config_single_group(switch.mgt_ip, switch.sn, report_time, action='retrieve_config')


@new_lifecycle_model.route('/get_local_license', methods=['POST'])
@admin_permission.require(http_exception=403)
def get_local_license():
    db_session = inven_db.get_session()
    local_licenses = db_session.query(inventory.License).filter(inventory.License.local_lic.isnot(None))
    page_num, page_size, total_count, query_license = utils.query_helper(inventory.License, local_licenses)
    return jsonify({"data": [license.make_dict() for license in query_license], "page": page_num,
                    "pageSize": page_size,
                    "total": total_count,
                    "status": 200})


@new_lifecycle_model.route('/local_license_save', methods=["POST"])
@admin_permission.require(http_exception=403)
def local_license_save():
    info = request.get_json()
    sn = str(info['sn'])
    lic = info['license']
    
    # check the sn/lic is given
    if sn == '' or lic == '':
        msg = {'info': 'Please provide SN and License string', 'status': 500}
        return jsonify(msg)
    
    # check the sn is exist or not
    lic_entry = inven_db.get_collection(inventory.Switch, filters={'sn': [sn]})
    if lic_entry:
        new_lic = inventory.License()
        new_lic.sn_num = sn
        new_lic.local_lic = lic
        inven_db.insert_or_update(new_lic, primary_key='sn_num')
        msg = {'info': 'The license update success', 'status': 200}
        return jsonify(msg)
    else:
        msg = {'info': 'Switch not configured', 'status': 500}
        return jsonify(msg)


@new_lifecycle_model.route('/local_license_delete', methods=["POST"])
@admin_permission.require(http_exception=403)
def local_license_delete():
    info = request.get_json()
    sn = str(info['sn'])
    
    inven_db.delete_collection(inventory.License, filters={'sn_num': [sn]})
    msg = {'info': 'The license delete success', 'status': 200}
    return jsonify(msg)


# @lifecycle_model.route('/modify_hostname', methods=["POST"])
# @admin_permission.require(http_exception=403)
# def modify_hostname():
#     info = request.form
#     sn = info.get('device_sn')
#     mgt_ip = info.get('device_mgt_ip')
#     hostname = info.get('device_hostname')
    
#     db_session = inven_db.get_session()
#     sw_info = db_session.query(inventory.Switch).filter(inventory.Switch.sn == sn).first()
#     if not sw_info:
#         return Response('Switch not exist', status=400)
    
#     with db_session.begin():
#         sw_info.host_name = hostname
    
#     return 'ok'


@new_lifecycle_model.route('/update_link_ip_addr', methods=["POST"])
@admin_permission.require(http_exception=403)
def update_link_ip_addr():
    msg = {'status': 200, 'info': 'Success to update link IP address'}
    try:
        utils.update_vpn_link_ip_addr()
    except Exception as e:
        msg = {'status': 500, 'info': 'Fail to update link IP address: {0}'.format(e)}
    return jsonify(msg)


@new_lifecycle_model.route('/update_hostname', methods=["POST"])
@admin_permission.require(http_exception=403)
def update_hostname():
    params = request.get_json()
    try:
        for sn, ip in params.items():
            user, pw = utils.get_switch_default_user(sn=sn)
            new_host_name, code = conn_client.interactive_shell_linux('hostname', ip, username=user, password=pw)
            if code != constants.RMA_ACTIVE:
                return {'status': 500, 'info': f'Connect switch sn:{sn} failed'}
            inven_db.update_model(inventory.Switch, filters={'sn': [sn], 'ip': [ip]}, updates={
                inventory.Switch.host_name: new_host_name
            })
        msg = {'status': 200, 'info': 'Success to update hostname'}
    except Exception as e:
        LOG.error('update switch host name failed, %s', e)
        msg = {'status': 500, 'info': 'Failed to update hostname'}
    return jsonify(msg)
