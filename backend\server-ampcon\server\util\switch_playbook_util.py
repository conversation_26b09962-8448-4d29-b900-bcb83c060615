import logging
import os
import shutil
import zipfile

import requests

from server.db.models import automation
from server.db.models.automation import Playbook
from server.db.models.general import Tag
from server import constants
from server import cfg
from server.util import utils, http_client
from celery_app.automation_task import beat_task

automation_db = automation.automation_db

LOG = logging.getLogger(__name__)
DATE_FORMAT = '%Y-%m-%d %H:%M:%S'


def add_playbook_implement(name, description, nodelist, create_user, is_internal=False):
    db_session = automation_db.get_session()
    playbook = db_session.query(Playbook).filter(Playbook.name == name).first()
    if playbook:
        msg = {'status': 500, 'info': 'name {0} already exists in database.'.format(name)}
        return msg

    try:
        for node in nodelist:
            filePath = os.path.join(constants.ANSIBLE_PLAYBOOK_DIR, node['path'])
            if node['isFolder']:
                # This node is a folder
                if not os.path.exists(filePath):
                    os.makedirs(filePath)
            else:
                # This node is a file
                fileContent = node['fileContent']
                with open(filePath, 'w+') as f:
                    f.write(fileContent)
                os.chmod(filePath, 493)  # 493 represent 0x755
        http_client.start_transfer_file('', [{'filename': name, 'path': os.path.join(constants.ANSIBLE_PLAYBOOK_DIR, name),
                                          'dest': os.path.join(constants.ANSIBLE_PLAYBOOK_DIR, name)}])
    except Exception as e:
        LOG.error('Adding new playbook {0} when create files: {1}'.format(name, e))
        shutil.rmtree(os.path.join(constants.ANSIBLE_PLAYBOOK_DIR, name))
        msg = {'status': 500, 'info': 'can not create file folder.'}
        return msg
    return insert_playbook_record(name, description, create_user, is_internal, db_session)


def add_internal_playbook(name, description, path, create_user, is_internal=False):
    db_session = automation_db.get_session()
    playbook = db_session.query(Playbook).filter(Playbook.name == name).first()
    if playbook:
        msg = {'status': '500', 'info': 'name {0} already exists in database.'.format(name)}
        return msg
    shutil.copytree(path, os.path.join(constants.ANSIBLE_PLAYBOOK_DIR, name))
    http_client.start_transfer_file('', [{'filename': name, 'path': os.path.join(constants.ANSIBLE_PLAYBOOK_DIR, name),
                                      'dest': os.path.join(constants.ANSIBLE_PLAYBOOK_DIR, name)}])
    return insert_playbook_record(name, description, create_user, is_internal, db_session)


def insert_playbook_record(name, description, create_user, is_internal, db_session):
    try:
        new_playbook = Playbook()
        new_playbook.name = name
        new_playbook.description = description
        new_playbook.create_user = create_user
        new_playbook.internal = is_internal
        new_playbook.playbook_path = os.path.join(constants.ANSIBLE_PLAYBOOK_DIR, name, 'playbook.yml')
        automation_db.insert(new_playbook, db_session)
    except Exception as e:
        LOG.error('Adding new playbook {0} when insert database: {1}'.format(name, e))
        shutil.rmtree(os.path.join(constants.ANSIBLE_PLAYBOOK_DIR, name))
        msg = {'status': 500, 'info': 'can not insert the record into database.'}
        return msg

    msg = {'status': 200, 'info': 'add playbook success'}
    return msg


def delete_playbook_implement(playbook_name, db_session):
    playbook = db_session.query(Playbook).filter(Playbook.name == playbook_name).first()
    playbook_tag = db_session.query(Tag).filter(Tag.record_id == playbook.id, Tag.record_type == 'playbook')
    if not playbook:
        msg = {'status': 500, 'info': 'name {0} does not exist in database.'.format(playbook_name)}
        return msg

    if playbook_tag.first():
        playbook_tag.delete()

    # delete related jobs in apsceduler
    for job in playbook.jobs:
        beat_task.remove_job(job.name)
    # delete playbook folder
    filePath = os.path.join(constants.ANSIBLE_PLAYBOOK_DIR, playbook_name)
    try:
        if os.path.isdir(filePath):
            shutil.rmtree(filePath)
    except Exception as e:
        LOG.error('Failed to delete playbook folder: {0}'.format(e))
        msg = {'status': 500, 'info': 'Failed to delete playbook folder.'}
        return msg

    # delete playbook record
    try:
        automation_db.delete_collection(Playbook, {'name': [playbook_name]}, db_session)
    except Exception as e:
        LOG.error('fail to delete playbook record: {0}'.format(e))
        msg = {'status': 500, 'info': 'fail to delete playbook record in database.'}
        return msg

    msg = {'status': 200, 'info': 'Success to delete playbook.'}
    return msg


def download_internal_playbooks():
    res = []
    proxy = cfg.CONF.license_portal_proxy
    git_file_path = '/tmp/git'
    zip_file_path = '/tmp/main.zip'
    if os.path.exists(git_file_path):
        shutil.rmtree(git_file_path)
    if os.path.exists(zip_file_path):
        os.remove(zip_file_path)

    # max retry 3 times
    max_retries = 3
    response = None
    for _ in range(max_retries):
        try:
            if proxy.get('https'):
                response = requests.get(constants.INTERNAL_PLAYBOOK_GIT_DOWNLOAD_URL, proxies={'https': proxy.get('https')}, timeout=20)
            else:
                response = requests.get(constants.INTERNAL_PLAYBOOK_GIT_DOWNLOAD_URL, timeout=20)
            if response.status_code == 200:
                with open(zip_file_path, 'wb') as f:
                    f.write(response.content)
                break
        except requests.exceptions.RequestException as e:
            if _ < max_retries - 1:
                continue
            else:
                return []
    if response and response.status_code == 200:
        with open(zip_file_path, 'wb') as f:
            f.write(response.content)
    else:
        return []
    if not os.path.exists('/tmp/main.zip'):
        return []
    utils.unzipFile(zip_file_path='/tmp/main.zip', unzip_path='/tmp/git')
    source_path = '/tmp/git/Ansible-main/playbooks'
    names = list(os.walk(source_path, topdown=True))[0][1]
    for name in names:
        temp = {'name': name, 'description': name, 'path': os.path.join(source_path, name)}
        res.append(temp)
    return res


def is_internal_playbook(name):
    db_session = automation_db.get_session()
    playbook = db_session.query(Playbook).filter(Playbook.name == name).first()
    if playbook.internal:
        return True
    else:
        return False
