import {ApiOutlined, CheckCircleTwoTone, CloseCircleTwoTone, SyncOutlined} from "@ant-design/icons";

/**
 * <0 :错误
 * 0: 断开
 * 1 : 连接正常
 * 2: 下载网元版本
 * 3: 激活网元版本
 * 4：激活完成，等待重启
 * 5：网元重启
 */
export const NEStateConfig = {
    upgrade: {
        error: {
            title: "error",
            title2: "error",
            icon: <CloseCircleTwoTone twoToneColor="#e22020" />
        },
        0: {
            title: "lost",
            title2: "lost",
            icon: <ApiOutlined />
        },
        1: {
            title: "connected",
            title2: "connected",
            icon: <CheckCircleTwoTone twoToneColor="#14c9bb" />
        },
        2: {
            title: "download_sw",
            title2: "download_sw_ing",
            icon: <SyncOutlined spin style={{color: "#087bee"}} />
        },
        3: {
            title: "activate_sw",
            title2: "activate_sw_ing",
            icon: <SyncOutlined spin style={{color: "#087bee"}} />
        },
        4: {
            title: "activate_suc",
            title2: "activate_suc_ing",
            icon: <SyncOutlined spin style={{color: "#087bee"}} />
        },
        5: {
            title: "reboot",
            title2: "reboot_ing",
            icon: <SyncOutlined spin style={{color: "#087bee"}} />
        }
    },
    data: {
        error: {
            title: "error",
            title2: "error",
            icon: <CloseCircleTwoTone twoToneColor="#e22020" />
        },
        0: {
            title: "lost",
            title2: "lost",
            icon: <ApiOutlined />
        },
        1: {
            title: "connected",
            title2: "connected",
            icon: <CheckCircleTwoTone twoToneColor="#14c9bb" />
        },
        2: {
            title: "download_data",
            title2: "download_data_ing",
            icon: <SyncOutlined spin style={{color: "#087bee"}} />
        },
        3: {
            title: "activate_data",
            title2: "activate_data_ing",
            icon: <SyncOutlined spin style={{color: "#087bee"}} />
        },
        4: {
            title: "activate_suc",
            title2: "activate_suc_ing",
            icon: <SyncOutlined spin style={{color: "#087bee"}} />
        },
        5: {
            title: "reboot",
            title2: "reboot_ing",
            icon: <SyncOutlined spin style={{color: "#087bee"}} />
        }
    }
};
