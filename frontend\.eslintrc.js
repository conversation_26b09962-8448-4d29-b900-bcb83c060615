module.exports = {
    env: {
        browser: true,
        node: true,
        es6: true
    },
    extends: ["eslint:recommended", "plugin:react/recommended", "airbnb", "plugin:prettier/recommended"],
    parser: "@babel/eslint-parser",
    parserOptions: {
        babelOptions: {
            presets: ["@babel/preset-react"]
        },
        sourceType: "module",
        allowImportExportEverywhere: true
    },
    plugins: ["react", "react-hooks", "unicorn"],
    rules: {
        "react/jsx-one-expression-per-line": "off",
        "react/prop-types": "off",
        "react/forbid-prop-types": "off",
        "react/jsx-indent": "off",
        "react/jsx-wrap-multilines": ["error", {declaration: false, assignment: false}],
        "react/jsx-filename-extension": "off",
        "react/state-in-constructor": "off",
        "react/jsx-props-no-spreading": "off",
        "react/destructuring-assignment": "off",
        "react/require-default-props": "off",
        "react/sort-comp": "off",
        "react/display-name": "off",
        "react/static-property-placement": "off",
        "react/jsx-no-bind": "off",
        "react/no-find-dom-node": "off",
        "react/no-unused-prop-types": "off",
        "react/default-props-match-prop-types": "off",
        "react-hooks/rules-of-hooks": "error",
        "react/function-component-definition": "off",
        "react/no-unused-class-component-methods": "off",
        "import/no-unresolved": "warn",
        "import/extensions": "off",
        "prettier/prettier": [
            "error",
            {endOfLine: "auto", singleQuote: false, bracketSpacing: false, trailingComma: "none", printWidth: 120}
        ],
        "react/react-in-jsx-scope": "off",
        "import/prefer-default-export": "off",
        "jsx-a11y/no-static-element-interactions": "off",
        "jsx-a11y/anchor-has-content": "off",
        "jsx-a11y/click-events-have-key-events": "off",
        "jsx-a11y/anchor-is-valid": "off",
        "jsx-a11y/no-noninteractive-element-interactions": "off",
        "unicorn/better-regex": "error",
        "unicorn/prefer-string-trim-start-end": "error",
        "unicorn/expiring-todo-comments": "error",
        "unicorn/no-abusive-eslint-disable": "off",
        "no-use-before-define": "off",
        "no-shadow": "off",
        "consistent-return": "off", // TODO: remove later
        "no-param-reassign": "off", // TODO: remove later
        "no-underscore-dangle": "off",
        "no-plusplus": "off",
        "no-continue": "off",
        "no-restricted-globals": "off",
        "max-classes-per-file": "off",
        "no-useless-return": "off",
        "import/no-extraneous-dependencies": "warn",
        "no-unused-expressions": "warn",
        "no-return-await": "off",
        camelcase: "off",
        "prefer-const": "warn",
        "react/no-array-index-key": "warn",
        "no-unused-vars": "warn",
        "one-var-declaration-per-line": "off",
        "one-var": "off",
        "default-case": "warn",
        radix: "off",
        "no-case-declarations": "off",
        "react/no-unstable-nested-components": "warn",
        "array-callback-return": "off",
        "no-await-in-loop": "off",
        "no-constant-condition": "off",
        "prefer-destructuring": "warn",
        "no-console": "off",
        "no-restricted-syntax": "off",
        "no-loop-func": "off",
        "no-bitwise": "off",
        "import/order": "off"
    },
    settings: {
        "import/resolver": {
            node: {
                extensions: [".js, .jsx, .svg"],
                moduleDirectory: ["src", "node_modules"]
            },
            alias: {
                map: [["@", "./src"]],
                extensions: [".js", ".jsx"]
            }
        }
    },
    overrides: [
        {
            files: ["./src/**/*.{js,jsx,ts,tsx}"],
            rules: {
                "no-console": "warn"
            }
        },
        {
            files: ["*.mjs"],
            parserOptions: {
                sourceType: "module"
            }
        }
    ]
};
