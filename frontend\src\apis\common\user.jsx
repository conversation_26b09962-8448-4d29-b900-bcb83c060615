import {request} from "@/utils/common/request";

const baseUrl = "/ampcon";

export function logoutAPI() {
    return request({
        url: `${baseUrl}/logout`,
        method: "GET"
    });
}

export function loginAPI(formData) {
    return request({
        url: `${baseUrl}/login`,
        method: "POST",
        data: formData
    });
}

// export function fetchLoginUserInfo(page, pageSize) {
//     return request({
//         url: `${baseUrl}/get_login_users`,
//         method: "POST",
//         data: {
//             page,
//             pageSize
//         }
//     });
// }

export function checkStatusAPI() {
    return request({
        url: `${baseUrl}/check_status`,
        method: "POST"
    });
}

export function checkLicense() {
    return request({
        url: `${baseUrl}/check_lic`,
        method: "GET"
    });
}
