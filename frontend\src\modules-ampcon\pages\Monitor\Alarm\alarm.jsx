import React, {useState, useEffect, useRef} from "react";
import {Card, Tag, Space, message, Button} from "antd";
import {AmpConCustomTable, createColumnConfig, TableFilterDropdown} from "@/modules-ampcon/components/custom_table";
import {
    getAllAlarmList,
    updateAlarmStatus,
    getUnreadAlarmList,
    removeAlarmsAPI
} from "@/modules-ampcon/apis/monitor_api";
import {getAlarmCount, updateAlarmSearch, updateAlarmSearchStatus} from "@/store/modules/common/alarm_slice";
import {useDispatch, useSelector} from "react-redux";
import styles from "@/modules-ampcon/pages/Monitor/Alarm/alarm.module.scss";
import {confirmModalAction} from "@/modules-ampcon/components/custom_modal";

const Alarm = () => {
    const alarmRef = useRef(null);
    const dispatch = useDispatch();
    const alarmSearch = useSelector(state => state.alarm.alarmSearch);
    const [searchTxt, setSearchTxt] = useState(alarmSearch);
    const count = useSelector(state => state.alarm.alarmClickCount);
    const [searchCount, setSearchCount] = useState(count);
    const [showAll, setShowAll] = useState(false);
    const [isFirstRender, setIsFirstRender] = useState(true);

    useEffect(() => {
        if (searchTxt) {
            return;
        }
        alarmRef.current.refreshTable();
    }, []);

    useEffect(() => {
        setSearchTxt(alarmSearch === "" ? searchTxt : alarmSearch);
        setSearchCount(count);
    }, [count]);

    useEffect(() => {
        if (isFirstRender) {
            setIsFirstRender(false);
            return;
        }
        alarmRef.current.refreshTable();
    }, [showAll]);

    const handleToggleView = () => {
        setShowAll(!showAll);
    };

    const handleMarkAsRead = record => {
        updateAlarmStatus(record.id)
            .then(res => {
                if (res.status === 200) {
                    message.success(res.msg);
                    alarmRef.current.refreshTable();
                    dispatch(getAlarmCount());
                    dispatch(updateAlarmSearch(""));
                    dispatch(updateAlarmSearchStatus(false));
                }
            })
            .catch(() => {});
    };

    const handleRemove = record => {
        confirmModalAction("This action will remove this alarm, Do you want to continue?", () => {
            try {
                removeAlarmsAPI(record.id).then(res => {
                    if (res.status === 200) {
                        message.success(res.msg);
                        alarmRef.current.refreshTable();
                    } else {
                        message.error(res.msg);
                    }
                });
            } catch (e) {
                message.error("Failed to remove alarm.");
            }
        });
    };

    const tagStyle = type => {
        if (type === "info") {
            return <Tag className={styles.infoStyle}>Info</Tag>;
        }
        if (type === "warn") {
            return <Tag className={styles.warnStyle}>Warn</Tag>;
        }
        if (type === "error") {
            return <Tag className={styles.errorStyle}>Error</Tag>;
        }
    };

    const alarmColumns = [
        createColumnConfig("Last Time", "modified_time", null, "", "16%"),
        createColumnConfig("Switch SN", "sn", null, "", "10%"),
        {
            ...createColumnConfig("Type", "type", TableFilterDropdown, searchTxt, "10%"),
            render: (_, record) => (
                <div>
                    <Space>{tagStyle(record.type)}</Space>
                </div>
            )
        },
        createColumnConfig("Message", "msg", null, "", "44%"),
        createColumnConfig("Count", "count", null, "", "10%"),
        {
            title: "Operation",
            render: (_, record) => (
                <Space size="large" className="actionLink">
                    {!showAll && <a onClick={() => handleMarkAsRead(record)}>Mark as read</a>}
                    {showAll && <a onClick={() => handleRemove(record)}>Remove</a>}
                </Space>
            )
        }
    ];

    const alarmSearchFieldsList = ["modified_time", "sn", "msg", "type"];
    const alarmMatchFieldsList = [{name: "type", matchMode: "fuzzy"}];

    return (
        <Card style={{flex: 1}} className="alarmTablestyle">
            <h2 style={{margin: "8px 0 20px"}}>Alarms</h2>
            <AmpConCustomTable
                columns={alarmColumns}
                fetchAPIInfo={showAll ? getAllAlarmList : getUnreadAlarmList}
                searchFieldsList={alarmSearchFieldsList}
                matchFieldsList={alarmMatchFieldsList}
                ref={alarmRef}
                readTag={!showAll}
                extraButton={<Button onClick={handleToggleView}>{showAll ? "Back to Alarms" : "All Messages"}</Button>}
            />
        </Card>
    );
};

export default Alarm;
