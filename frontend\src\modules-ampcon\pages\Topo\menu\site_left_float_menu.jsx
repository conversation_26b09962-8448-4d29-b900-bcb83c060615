import {<PERSON><PERSON>, <PERSON><PERSON><PERSON>} from "antd";

import topoMenuButtonStyle from "@/modules-ampcon/pages/Topo/menu/site_left_float_menu.module.scss";
import {useState} from "react";
import {useSelector} from "react-redux";
import TopoLegendSvg from "../resource/topo_legend.svg?react";
import DownloadSvg from "../resource/download.svg?react";
import EditSvg from "../resource/edit.svg?react";
import FullscreenExitSvg from "../resource/fullscreen_exit.svg?react";
import ReloadSvg from "../resource/reload.svg?react";
import SaveSvg from "../resource/save.svg?react";
import ZoomInSvg from "../resource/zoom_in.svg?react";
import ZoomOutSvg from "../resource/zoom_out.svg?react";
import UndoSvg from "../resource/undo.svg?react";
import RedoSvg from "../resource/redo.svg?react";
import AutoDiscoverSvg from "../resource/auto_discover.svg?react";
import HierarchyLayoutSvg from "../resource/autolayout_hierarchy.svg?react";
import GirdLayoutSvg from "../resource/autolayout_gird.svg?react";
import CircularLayoutSvg from "../resource/autolayout_circular.svg?react";
import EllipticalLayoutSvg from "../resource/autolayout_elliptical.svg?react";
import ShowMoreLayoutSvg from "../resource/autolayout_up.svg?react";
import HideLayoutSvg from "../resource/autolayout_down.svg?react";
import CloseSvg from "../resource/topo_close.svg?react";
import AimSvg from "../resource/topo_aim.svg?react";
import HistorySvg from "../resource/topo_history.svg?react";

const SiteLeftFloatMenu = ({
    enableEditModeCallback,
    saveTopoCallback,
    zoomInCallback,
    zoomResetCallback,
    zoomOutCallback,
    downloadImgCallback,
    reloadCallback,
    cancelEditCallback,
    cancelHistoryModeCallback,
    autoDiscoverCallback,
    aimHistoryTimeSliderCallback,
    topoHistoryAttrs,
    autolayoutAttrs,
    canTreeLayout,
    isEditMode,
    setIsEditMode,
    isHistoryMode,
    setIsHistoryMode,
    isTopoLegend,
    setIsTopoLegend
}) => {
    const [morelayout, setMorelayout] = useState(false);

    const topoMenuButtonTitles = {
        save: "Save",
        cancelEdit: "Cancel Edit",
        undo: "Undo",
        redo: "Redo",
        add: "Add Device",
        delete: "Delete Node",
        zoomIn: "Zoom In",
        zoomReset: "Zoom Reset",
        zoomOut: "Zoom Out",
        autoDiscover: "Auto Discover",
        refresh: "Refresh",
        showLegend: isTopoLegend ? "Hide Legend" : "Show Legend",
        autoHierarchyLayout: "Auto Hierarchy Layout",
        autoGirdLayout: "Auto Gird Layout",
        autoCircularLayout: "Auto Circular Layout",
        autoEllipticalLayout: "Auto Elliptical Layout",
        history: "History",
        realTime: "Back To RealTime Topology",
        edit: "Edit",
        downloadImg: "Download Image",
        aim: "Aim"
    };

    const currentUser = useSelector(state => state.user.userInfo);

    if (isEditMode) {
        return (
            <>
                <Tooltip title={topoMenuButtonTitles.save} placement="right">
                    <Button
                        className={topoMenuButtonStyle.topoMenuSaveButton}
                        icon={<SaveSvg />}
                        onClick={async () => {
                            await saveTopoCallback();
                            setIsEditMode(false);
                        }}
                    />
                </Tooltip>
                <Tooltip title={topoMenuButtonTitles.cancelEdit} placement="right">
                    <Button
                        className={topoMenuButtonStyle.topoMenuCancelEditButton}
                        icon={<CloseSvg />}
                        onClick={async () => {
                            await cancelEditCallback();
                            setIsEditMode(false);
                        }}
                    />
                </Tooltip>
                <Tooltip title={topoMenuButtonTitles.undo} placement="right">
                    <Button
                        className={topoMenuButtonStyle.topoMenuUndoButton}
                        icon={<UndoSvg />}
                        onClick={topoHistoryAttrs.undo}
                        disabled={!topoHistoryAttrs.canUndo}
                    />
                </Tooltip>
                <Tooltip title={topoMenuButtonTitles.redo} placement="right">
                    <Button
                        className={topoMenuButtonStyle.topoMenuRedoButton}
                        icon={<RedoSvg />}
                        onClick={topoHistoryAttrs.redo}
                        disabled={!topoHistoryAttrs.canRedo}
                    />
                </Tooltip>
                <Tooltip title={topoMenuButtonTitles.zoomIn} placement="right">
                    <Button
                        className={topoMenuButtonStyle.topoEditMenuZoomInButton}
                        icon={<ZoomInSvg />}
                        onClick={zoomInCallback}
                    />
                </Tooltip>
                <Tooltip title={topoMenuButtonTitles.zoomReset} placement="right">
                    <Button
                        className={topoMenuButtonStyle.topoEditMenuZoomResetButton}
                        icon={<FullscreenExitSvg />}
                        onClick={zoomResetCallback}
                    />
                </Tooltip>
                <Tooltip title={topoMenuButtonTitles.zoomOut} placement="right">
                    <Button
                        className={topoMenuButtonStyle.topoEditMenuZoomOutButton}
                        icon={<ZoomOutSvg />}
                        onClick={zoomOutCallback}
                    />
                </Tooltip>
                <Tooltip title={topoMenuButtonTitles.autoDiscover} placement="right">
                    <Button
                        className={topoMenuButtonStyle.topoAutoDiscoverButton}
                        icon={<AutoDiscoverSvg />}
                        onClick={autoDiscoverCallback}
                    />
                </Tooltip>
                <Tooltip title={topoMenuButtonTitles.refresh} placement="right">
                    <Button
                        className={topoMenuButtonStyle.topoEditMenuRefreshButton}
                        icon={<ReloadSvg />}
                        onClick={reloadCallback}
                    />
                </Tooltip>
                <Tooltip title={topoMenuButtonTitles.showLegend} placement="right">
                    <Button
                        className={topoMenuButtonStyle.topoEditMenuShowLegendButton}
                        icon={<TopoLegendSvg />}
                        onClick={() => {
                            setIsTopoLegend(!isTopoLegend);
                        }}
                    />
                </Tooltip>
                <Tooltip title={topoMenuButtonTitles.autoHierarchyLayout} placement="right">
                    <Button
                        className={topoMenuButtonStyle.topoMenuLayoutBaseButton}
                        icon={<HierarchyLayoutSvg />}
                        onClick={() => {
                            autolayoutAttrs.autoHierarchyLayout();
                        }}
                        disabled={!autolayoutAttrs.isHierarchyLayoutValid() || !canTreeLayout}
                    />
                </Tooltip>
                <Tooltip title={topoMenuButtonTitles.autoGirdLayout} placement="right">
                    <Button
                        className={topoMenuButtonStyle.topoMenuLayoutBaseButton}
                        style={{top: morelayout ? 438 + 34 : 438, zIndex: morelayout ? 5 : -1}}
                        icon={<GirdLayoutSvg />}
                        onClick={() => {
                            autolayoutAttrs.autoGirdLayout();
                        }}
                        disabled={!autolayoutAttrs.hasNodes()}
                    />
                </Tooltip>
                <Tooltip title={topoMenuButtonTitles.autoCircularLayout} placement="right">
                    <Button
                        className={topoMenuButtonStyle.topoMenuLayoutBaseButton}
                        style={{top: morelayout ? 438 + 34 * 2 : 438 + 34 * 1, zIndex: morelayout ? 5 : -1}}
                        icon={<CircularLayoutSvg />}
                        onClick={() => {
                            autolayoutAttrs.autoCircularLayout();
                        }}
                        disabled={!autolayoutAttrs.hasNodes()}
                    />
                </Tooltip>
                <Tooltip title={topoMenuButtonTitles.autoEllipticalLayout} placement="right">
                    <Button
                        className={topoMenuButtonStyle.topoMenuLayoutBaseButton}
                        style={{top: morelayout ? 438 + 34 * 3 : 438 + 34 * 2, zIndex: morelayout ? 5 : -1}}
                        icon={<EllipticalLayoutSvg />}
                        onClick={() => {
                            autolayoutAttrs.autoEllipticalLayout();
                        }}
                        disabled={!autolayoutAttrs.hasNodes()}
                    />
                </Tooltip>
                <Tooltip title={morelayout ? "Hide Layouts" : "More Layouts"} placement="right">
                    <Button
                        className={topoMenuButtonStyle.topoMenuLayoutBaseButton}
                        style={{top: morelayout ? 438 + 34 * 4 : 438 + 34}}
                        icon={morelayout ? <ShowMoreLayoutSvg /> : <HideLayoutSvg />}
                        onClick={() => {
                            setMorelayout(!morelayout);
                        }}
                    />
                </Tooltip>
            </>
        );
    }
    if (isHistoryMode) {
        return (
            <>
                <Tooltip title={topoMenuButtonTitles.aim} placement="right">
                    <Button
                        className={topoMenuButtonStyle.topoMenuAimButton}
                        icon={<AimSvg />}
                        onClick={aimHistoryTimeSliderCallback}
                    />
                </Tooltip>
                <Tooltip title={topoMenuButtonTitles.realTime} placement="right">
                    <Button
                        className={topoMenuButtonStyle.topoMenuRealTimeButton}
                        icon={<CloseSvg />}
                        onClick={() => {
                            setIsHistoryMode(false);
                            cancelHistoryModeCallback();
                        }}
                    />
                </Tooltip>
                <Tooltip title={topoMenuButtonTitles.zoomIn} placement="right">
                    <Button
                        className={topoMenuButtonStyle.topoMenuZoomInButton}
                        icon={<ZoomInSvg />}
                        onClick={zoomInCallback}
                    />
                </Tooltip>
                <Tooltip title={topoMenuButtonTitles.zoomReset} placement="right">
                    <Button
                        className={topoMenuButtonStyle.topoMenuZoomResetButton}
                        icon={<FullscreenExitSvg />}
                        onClick={zoomResetCallback}
                    />
                </Tooltip>
                <Tooltip title={topoMenuButtonTitles.zoomOut} placement="right">
                    <Button
                        className={topoMenuButtonStyle.topoMenuZoomOutButton}
                        icon={<ZoomOutSvg />}
                        onClick={zoomOutCallback}
                    />
                </Tooltip>
                <Tooltip title={topoMenuButtonTitles.downloadImg} placement="right">
                    <Button
                        className={topoMenuButtonStyle.topoHistoryMenuDownloadImgButton}
                        icon={<DownloadSvg />}
                        onClick={downloadImgCallback}
                    />
                </Tooltip>
            </>
        );
    }

    if (currentUser.type === "readonly") {
        return (
            <>
                <Tooltip title={topoMenuButtonTitles.history} placement="right">
                    <Button
                        className={topoMenuButtonStyle.topoMenuReadonlyHistoryButton}
                        icon={<HistorySvg />}
                        onClick={() => {
                            setIsHistoryMode(true);
                        }}
                    />
                </Tooltip>
                <Tooltip title={topoMenuButtonTitles.zoomIn} placement="right">
                    <Button
                        className={topoMenuButtonStyle.topoMenuReadonlyZoomInButton}
                        icon={<ZoomInSvg />}
                        onClick={zoomInCallback}
                    />
                </Tooltip>
                <Tooltip title={topoMenuButtonTitles.zoomReset} placement="right">
                    <Button
                        className={topoMenuButtonStyle.topoMenuReadonlyZoomResetButton}
                        icon={<FullscreenExitSvg />}
                        onClick={zoomResetCallback}
                    />
                </Tooltip>
                <Tooltip title={topoMenuButtonTitles.zoomOut} placement="right">
                    <Button
                        className={topoMenuButtonStyle.topoMenuReadonlyZoomOutButton}
                        icon={<ZoomOutSvg />}
                        onClick={zoomOutCallback}
                    />
                </Tooltip>
                <Tooltip title={topoMenuButtonTitles.refresh} placement="right">
                    <Button
                        className={topoMenuButtonStyle.topoMenuReadonlyRefreshButton}
                        icon={<ReloadSvg />}
                        onClick={reloadCallback}
                    />
                </Tooltip>
                <Tooltip title={topoMenuButtonTitles.downloadImg} placement="right">
                    <Button
                        className={topoMenuButtonStyle.topoMenuReadonlyDownloadImgButton}
                        icon={<DownloadSvg />}
                        onClick={downloadImgCallback}
                    />
                </Tooltip>
            </>
        );
    }

    return (
        <>
            <Tooltip title={topoMenuButtonTitles.edit} placement="right">
                <Button
                    className={topoMenuButtonStyle.topoMenuEditButton}
                    icon={<EditSvg />}
                    onClick={async () => {
                        setMorelayout(false);
                        await enableEditModeCallback();
                        setIsEditMode(true);
                    }}
                />
            </Tooltip>
            <Tooltip title={topoMenuButtonTitles.history} placement="right">
                <Button
                    className={topoMenuButtonStyle.topoMenuHistoryButton}
                    icon={<HistorySvg />}
                    onClick={() => {
                        setIsHistoryMode(true);
                    }}
                />
            </Tooltip>
            <Tooltip title={topoMenuButtonTitles.zoomIn} placement="right">
                <Button
                    className={topoMenuButtonStyle.topoMenuZoomInButton}
                    icon={<ZoomInSvg />}
                    onClick={zoomInCallback}
                />
            </Tooltip>
            <Tooltip title={topoMenuButtonTitles.zoomReset} placement="right">
                <Button
                    className={topoMenuButtonStyle.topoMenuZoomResetButton}
                    icon={<FullscreenExitSvg />}
                    onClick={zoomResetCallback}
                />
            </Tooltip>
            <Tooltip title={topoMenuButtonTitles.zoomOut} placement="right">
                <Button
                    className={topoMenuButtonStyle.topoMenuZoomOutButton}
                    icon={<ZoomOutSvg />}
                    onClick={zoomOutCallback}
                />
            </Tooltip>
            <Tooltip title={topoMenuButtonTitles.refresh} placement="right">
                <Button
                    className={topoMenuButtonStyle.topoMenuRefreshButton}
                    icon={<ReloadSvg />}
                    onClick={reloadCallback}
                />
            </Tooltip>
            <Tooltip title={topoMenuButtonTitles.downloadImg} placement="right">
                <Button
                    className={topoMenuButtonStyle.topoMenuDownloadImgButton}
                    icon={<DownloadSvg />}
                    onClick={downloadImgCallback}
                />
            </Tooltip>
        </>
    );
};

export default SiteLeftFloatMenu;
