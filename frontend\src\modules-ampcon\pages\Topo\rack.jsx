import {Graph} from "@antv/x6";
import {useEffect, useRef} from "react";
import {DagreLayout, GridLayout} from "@antv/layout";

const Rack = () => {
    const containerRef = useRef(null);

    const handleRestricted = ({graph, cell}) => {
        if (cell.store.data.data.type === "type1") {
            return {
                x: -9000, // 限制区域起点x坐标
                y: -9000, // 限制区域起点y坐标
                width: 9000 + 9000, // 限制区域宽度
                height: 9000 + graph.getCellById("splitter1").getSourcePoint().y //  限制区域高度（分割线1的y坐标）
            };
        }
        if (cell.store.data.data.type === "type2") {
            return {
                x: -9000,
                y: graph.getCellById("splitter1").getSourcePoint().y,
                width: 9000 + 9000,
                height:
                    graph.getCellById("splitter2").getSourcePoint().y -
                    graph.getCellById("splitter1").getSourcePoint().y
            };
        }
        return {
            x: -9000,
            y: graph.getCellById("splitter2").getSourcePoint().y,
            width: 9000 + 9000,
            height: 9000
        };
    };

    useEffect(() => {
        const graph = new Graph({
            container: containerRef.current,
            width: 800,
            height: 600,
            autoResize: true, // 自动调整大小

            grid: true, // 是否显示网格
            translating: {
                restrict: handleRestricted // 节点移动限制
            },
            rotating: {
                enabled: true // 是否启用旋转
            },
            panning: {
                enabled: true, // 是否启用平移
                eventTypes: ["leftMouseDown", "mouseWheel"]
            },
            interacting: {
                nodeMovable: true, // 节点是否可移动
                edgeMovable: false // 边是否可移动
            },
            connecting: {
                connector: {
                    name: "smooth" // 连接线的类型
                }
            },
            mousewheel: {
                enabled: true, // 是否启用鼠标滚轮缩放
                modifiers: "ctrl"
            }
        });

        const dagreLayout = new DagreLayout({
            type: "dagre", // 布局类型
            rankdir: "TB", // 布局方向：自上而下
            align: "UL", // 节点对齐方式：左上对齐
            begin: [100, 100], // 布局起始点
            ranksep: 50, // 节点水平间距
            nodesep: 30, // 层间距
            controlPoints: true // 是否保留布局连线的控制点
        });

        const alldata = {
            nodes: [
                ...Array.from({length: 10}, (_, i) => ({
                    id: `node${i}test`,
                    width: 80,
                    height: 30,
                    label: `node${i}test`,
                    x: 0,
                    y: 100 + i * 50,
                    data: {type: "type3"}
                })),
                {id: "node1", width: 80, height: 30, label: "node1", data: {type: "type1"}},
                {
                    id: "node2",
                    width: 80,
                    height: 30,
                    ports: {
                        groups: {
                            out: {
                                position: "bottom",
                                attrs: {
                                    circle: {r: 1, magnet: false, stroke: "none", strokeWidth: 1, fill: "none"}
                                }
                            }
                        },
                        items: Array.from({length: 12}, (_, i) => ({id: `port${i + 1}`, group: "out"}))
                    },
                    children: Array.from({length: 10}, (_, i) => `node${i}test`).concat(["node4", "node5"]),
                    label: "node2",
                    data: {type: "type2"}
                },
                {id: "node3", width: 80, height: 30, label: "node3", data: {type: "type2"}},
                {id: "node4", width: 80, height: 30, label: "node4", data: {type: "type3"}},
                {id: "node5", width: 80, height: 30, label: "node5", data: {type: "type3"}},
                {id: "node6", width: 80, height: 30, label: "node6", data: {type: "type1"}},
                {id: "node7", width: 80, height: 30, label: "node7", data: {type: "type2"}, children: ["node8"]},
                {id: "node8", width: 80, height: 30, label: "node8", data: {type: "type3"}}
            ],
            edges: [
                {source: "node1", target: "node2"},
                {source: "node1", target: "node3"},
                {source: "node6", target: "node7"},
                {source: "node7", target: "node8"},
                ...Array.from({length: 10}, (_, i) => ({source: "node2", target: `node${i}test`})),
                {source: "node2", target: "node4"},
                {source: "node2", target: "node5"}
            ]
        };

        const data = {
            nodes: alldata.nodes.filter(node => node.data.type !== "type3"),
            edges: alldata.edges.filter(edge => {
                const sourceNode = alldata.nodes.find(node => node.id === edge.source);
                const targetNode = alldata.nodes.find(node => node.id === edge.target);
                return sourceNode.data.type !== "type3" && targetNode.data.type !== "type3";
            })
        };

        dagreLayout.layout(data);

        const gridLayout = new GridLayout({
            type: "grid",
            rows: 10,
            preventOverlap: true,
            begin: [100 - 40, 500 - 15],
            nodeSize: [100, 40],
            // height: 1000,
            // preventOverlapPadding: 200,
            // sortBy: "degree",
            workerEnabled: true
            // condense: true
        });
        // gridLayout.layout({nodes: alldata.nodes.filter(node => node.data.type === "type3")});

        graph.fromJSON(data);

        alldata.nodes
            .filter(node => node.data.type === "type3")
            .forEach(node => {
                const parentNode = alldata.nodes.find(n => n.children && n.children.includes(node.id));
                if (parentNode) {
                    const parentGraphNode = graph.getCellById(parentNode.id);
                    const parentPosition = parentGraphNode.getPosition();
                    const existingNodes = graph.getNodes().filter(n => n.data.parentId === parentNode.id);
                    const yOffset = existingNodes.length * 50;
                    graph.addNode({
                        ...node,
                        x: parentPosition.x,
                        y: parentPosition.y + 200 + yOffset,
                        data: {
                            ...node.data,
                            parentId: parentNode.id
                        }
                    });
                }
            });

        alldata.edges
            .filter(edge => {
                const sourceNode = alldata.nodes.find(node => node.id === edge.source);
                const targetNode = alldata.nodes.find(node => node.id === edge.target);
                return sourceNode.data.type === "type3" || targetNode.data.type === "type3";
            })
            .forEach((edge, index) => {
                graph.addEdge({
                    source: edge.source,
                    target: edge.target,
                    sourcePort: edge.sourcePort || `port${(index % 12) + 1}`
                });
            });

        graph.getEdges().forEach(edge => {
            edge.setAttrs({
                line: {
                    stroke: "orange",
                    strokeDasharray: "4, 5"
                }
            });
        });

        graph.getNodes().forEach(node => {
            node.setParent(null);
            node.setChildren([]);
            const nodeColorMap = {
                type1: "#88cfe8",
                type2: "#e5e6f8",
                type3: "#d4d4d2"
            };
            node.setAttrs({
                body: {
                    fill: nodeColorMap[node.data.type]
                }
            });
        });

        graph.addEdges([
            {
                id: "splitter1",
                sourcePoint: {x: -9000, y: 200}, // 分割线1的起始点
                targetPoint: {x: 9000, y: 200}, // 分割线1的结束点
                attrs: {
                    line: {
                        stroke: "#81817f", // 分割线1的颜色
                        strokeWidth: 5, // 分割线1的宽度
                        strokeDasharray: "10, 5" // 分割线1的样式
                    }
                }
            },
            {
                id: "splitter2",
                sourcePoint: {x: -9000, y: 400},
                targetPoint: {x: 9000, y: 400},
                attrs: {
                    line: {
                        stroke: "#81817f",
                        strokeWidth: 5,
                        strokeDasharray: "10, 5"
                    }
                }
            }
        ]);

        graph.on("node:click", ({node}) => {
            console.log(node.getPosition());
        });

        // // splitter moving
        // graph.on("edge:change:source", ({edge, current}) => {
        //     const getMaxY = type =>
        //         Math.max(
        //             ...graph
        //                 .getNodes()
        //                 .filter(node => node.data.type === type)
        //                 .map(node => node.getPosition().y)
        //         );

        //     const updateEdgeAndNodes = (edge, currentY, minY) => {
        //         if (currentY < minY + 50) {
        //             edge.setSource({x: edge.previous("source").x, y: minY + 50});
        //             edge.setTarget({x: edge.previous("target").x, y: minY + 50});
        //             return;
        //         }
        //         edge.setSource({x: edge.previous("source").x, y: currentY});
        //         edge.setTarget({x: edge.previous("target").x, y: currentY});
        //         const offsetY = currentY - edge.previous("source").y;
        //         graph.getNodes().forEach(node => {
        //             if (node.getPosition().y > edge.previous("source").y) {
        //                 node.translate(0, offsetY);
        //             }
        //         });
        //     };

        //     if (edge.id === "splitter2") {
        //         updateEdgeAndNodes(edge, current.y, getMaxY("type2"));
        //     } else if (edge.id === "splitter1") {
        //         updateEdgeAndNodes(edge, current.y, getMaxY("type1"));
        //         graph.getCellById("splitter2").translate(0, current.y - edge.previous("source").y);
        //     }
        // });

        return () => {
            graph.dispose();
        };
    }, []);

    return (
        <>
            {" "}
            <div
                className="graph-container"
                ref={containerRef}
                style={{
                    backgroundColor: "#F8FAFB",
                    // minWidth: "100%",
                    // minHeight: "100%",
                    width: "100%",
                    height: "100%"
                    // position: "relative"
                    // overflow: "hidden"
                }}
            />
        </>
    );
};

export default Rack;
