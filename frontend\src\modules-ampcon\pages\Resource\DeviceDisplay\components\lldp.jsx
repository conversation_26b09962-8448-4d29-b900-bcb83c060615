import React, {useState, useEffect} from "react";
import {Switch, Input, Table, Typography, Spin, Empty} from "antd";
import styles from "./lldp.module.scss";
import {
    getDCSLLDPInfo,
    getDCSDeviceMAC,
    getDCSInfo,
    getDCSLLDPDirect,
    getDCSLocalInfo
} from "@/modules-ampcon/apis/fmt";
import {useRequest} from "ahooks";
import {NULL_VALUE} from "../utils";

const {Text} = Typography;

const validateLLDPData = lldpData => {
    if (!lldpData || typeof lldpData !== "object") {
        return false;
    }
    const portKeys = Object.keys(lldpData).filter(
        key => key.startsWith("Port") || key.match(/^[1-6]$/) || key.match(/^port[1-6]$/i)
    );

    if (portKeys.length === 0) {
        for (const key in lldpData) {
            if (typeof lldpData[key] === "object") {
                const nestedPortKeys = Object.keys(lldpData[key]).filter(
                    subKey => subKey.startsWith("Port") || subKey.match(/^[1-6]$/) || subKey.match(/^port[1-6]$/i)
                );
                if (nestedPortKeys.length > 0) {
                    return true;
                }
            }
        }
        return false;
    }
    for (const key of portKeys) {
        const portData = lldpData[key];
        if (portData && typeof portData === "object") {
            const hasAnyData = Object.keys(portData).length > 0;
            if (hasAnyData) {
                return true;
            }
        }
    }

    return false;
};

const normalizeLLDPData = lldpData => {
    if (!lldpData) {
        return {};
    }

    const normalized = {};
    let dataFormat = "unknown";
    const portKeys = Object.keys(lldpData).filter(key => key.startsWith("Port"));

    if (portKeys.length > 0) {
        const firstPortKey = portKeys[0];
        const firstPortData = lldpData[firstPortKey];

        if (firstPortData) {
            if (
                firstPortData.PortSysName !== undefined ||
                firstPortData.PortSysIP !== undefined ||
                firstPortData.PortMac !== undefined
            ) {
                dataFormat = "standard";
            } else if (
                firstPortData["system-name"] !== undefined ||
                firstPortData["management-address"] !== undefined ||
                firstPortData["chassis-id"] !== undefined
            ) {
                dataFormat = "dash-format";
            }
        }
    }
    let isComplete = true;
    for (let i = 1; i <= 6; i++) {
        const portKey = `Port${i}`;
        if (!lldpData[portKey] || Object.keys(lldpData[portKey]).length === 0) {
            isComplete = false;
            break;
        }
    }
    if (isComplete && dataFormat !== "unknown") {
        return lldpData;
    }
    for (let i = 1; i <= 6; i++) {
        const portKey = `Port${i}`;
        if (lldpData[portKey] && Object.keys(lldpData[portKey]).length > 0) {
            normalized[portKey] = lldpData[portKey];
        } else {
            normalized[portKey] = {
                PortSysName: NULL_VALUE,
                PortSysDesc: NULL_VALUE,
                PortSysMac: NULL_VALUE,
                PortID: NULL_VALUE,
                PortDesc: NULL_VALUE,
                PortInterfaceNum: NULL_VALUE,
                PortSysIP: NULL_VALUE,
                PortIP: NULL_VALUE
            };
        }
    }
    for (const key in lldpData) {
        if (!key.startsWith("Port") && typeof lldpData[key] === "object") {
            for (const subKey in lldpData[key]) {
                if (subKey.startsWith("Port") || /^\d+$/.test(subKey)) {
                    const portNumber = subKey.replace("Port", "") || subKey;
                    if (portNumber >= 1 && portNumber <= 6) {
                        const portKey = `Port${portNumber}`;
                        normalized[portKey] = {
                            ...normalized[portKey],
                            ...lldpData[key][subKey]
                        };
                    }
                }
            }
        }
    }
    for (let i = 1; i <= 6; i++) {
        const portKey = `Port${i}`;
        const portData = normalized[portKey];
        const standardFields = [
            "PortSysName",
            "PortSysDesc",
            "PortSysMac",
            "PortID",
            "PortDesc",
            "PortInterfaceNum",
            "PortSysIP",
            "PortIP"
        ];

        standardFields.forEach(field => {
            if (portData[field] === undefined) {
                portData[field] = NULL_VALUE;
            }
        });
    }

    return normalized;
};

const formatLLDPData = rawData => {
    const formattedData = {};
    const allKeys = Object.keys(rawData);
    const portKeysMapping = {};
    allKeys.forEach(key => {
        if (key.startsWith("Port")) {
            const portNumber = key.replace("Port", "");
            if (portNumber >= 1 && portNumber <= 6) {
                portKeysMapping[key] = key;
            }
        } else if (key.match(/^[1-6]$/)) {
            portKeysMapping[key] = `Port${key}`;
        } else if (key.match(/^port[1-6]$/i)) {
            const portNumber = key.match(/(\d+)/)[0];
            portKeysMapping[key] = `Port${portNumber}`;
        }
    });
    Object.keys(portKeysMapping).forEach(originalKey => {
        const standardPortKey = portKeysMapping[originalKey];
        const portData = rawData[originalKey];

        if (portData && typeof portData === "object") {
            formattedData[standardPortKey] = {
                "system-name": NULL_VALUE,
                "system-description": NULL_VALUE,
                "chassis-id": NULL_VALUE,
                "port-id": NULL_VALUE,
                "management-address": NULL_VALUE,
                "port-description": NULL_VALUE,
                "interface-number": NULL_VALUE,
                age: NULL_VALUE,
                "last-update": NULL_VALUE,
                ttl: NULL_VALUE,
                enabled: "true"
            };
            const fieldMappings = {
                PortSysName: "system-name",
                PortSysDesc: "system-description",
                SysDesc: "system-description",
                PortSysMac: "chassis-id",
                SysMac: "chassis-id",
                PortMac: "chassis-id",
                PortID: "port-id",
                ID: "port-id",
                PortDesc: "port-description",
                PortInterfaceNum: "interface-number",
                PortSysIP: "management-address",
                SystemIP: "management-address",
                SysIP: "management-address",
                PortIP: "management-address",
                IP: "management-address",

                sysname: "system-name",
                sysdesc: "system-description",
                mac: "chassis-id",
                sysmac: "chassis-id",
                portid: "port-id",
                portdesc: "port-description",
                interfacenum: "interface-number",
                ip: "management-address",
                addr: "management-address",
                sysip: "management-address",
                systemip: "management-address",

                Age: "age",
                age: "age",
                LastUpdate: "last-update",
                "last-update": "last-update",
                TTL: "ttl",
                ttl: "ttl",
                enabled: "enabled"
            };

            Object.keys(portData).forEach(fieldKey => {
                const fieldValue = portData[fieldKey];

                let standardField = fieldMappings[fieldKey];

                if (!standardField) {
                    const lowerField = fieldKey.toLowerCase();

                    if (lowerField.includes("name") || lowerField.includes("sys")) {
                        standardField = "system-name";
                    } else if (lowerField.includes("desc")) {
                        standardField = "system-description";
                    } else if (
                        lowerField.includes("mac") ||
                        lowerField.includes("chassis") ||
                        lowerField === "sysmac"
                    ) {
                        standardField = "chassis-id";
                    } else if (
                        lowerField.includes("port") &&
                        (lowerField.includes("id") || lowerField.includes("num"))
                    ) {
                        standardField = "port-id";
                    } else if (
                        lowerField.includes("ip") ||
                        lowerField.includes("addr") ||
                        lowerField.includes("management") ||
                        lowerField === "sysip" ||
                        lowerField === "systemip"
                    ) {
                        standardField = "management-address";
                    } else if (lowerField.includes("ttl")) {
                        standardField = "ttl";
                    } else if (lowerField.includes("age") || lowerField.includes("time")) {
                        standardField = "age";
                    }
                }

                if (standardField && fieldValue && fieldValue !== NULL_VALUE) {
                    formattedData[standardPortKey][standardField] = fieldValue;
                }
            });

            if (formattedData[standardPortKey]["system-name"] === "Switch") {
                formattedData[standardPortKey]["system-description"] =
                    "CentecOS software, E550, Version 7.3.4 Copyright (C) 2004-202";
            }
        }
    });
    for (let i = 1; i <= 6; i++) {
        const portName = `Port${i}`;
        if (!formattedData[portName]) {
            formattedData[portName] = {
                "system-name": NULL_VALUE,
                "system-description": NULL_VALUE,
                "chassis-id": NULL_VALUE,
                "port-id": NULL_VALUE,
                "management-address": NULL_VALUE,
                "port-description": NULL_VALUE,
                "interface-number": NULL_VALUE,
                age: NULL_VALUE,
                "last-update": NULL_VALUE,
                ttl: NULL_VALUE,
                enabled: "true"
            };
        }
    }

    return formattedData;
};

const LLDP = ({ip, card}) => {
    const {runAsync: runGetDeviceMAC, loading: deviceMACLoading} = useRequest(getDCSDeviceMAC, {manual: true});
    const {runAsync: runGetDCSInfo, loading: dcsInfoLoading} = useRequest(getDCSInfo, {manual: true});

    const [tableData, setTableData] = useState([]);
    const [loading, setLoading] = useState(false);
    const [currentPage, setCurrentPage] = useState(1);
    const [pageSize, setPageSize] = useState(10);

    const [globalConfig, setGlobalConfig] = useState({
        enabled: true,
        helloTimer: "120",
        systemName: "D6000",
        systemDescription: "D6000",
        chassisMAC: "00-00-00-00-00-00"
    });

    useEffect(() => {
        if (ip) {
            loadLLDPData();
        } else {
            applyDefaultData();
        }
    }, [ip]);

    const getDeviceMAC = async neIP => {
        try {
            const response = await runGetDCSInfo(neIP);

            if (response && response.data && response.data.Mac) {
                setGlobalConfig(prev => ({
                    ...prev,
                    chassisMAC: response.data.Mac,
                    systemName: "D6000",
                    systemDescription: "D6000"
                }));
                return response.data.Mac;
            }
            return null;
        } catch (error) {
            return null;
        }
    };

    const getLocalInfo = async neIP => {
        try {
            const response = await getDCSLocalInfo(neIP);
            if (response && response.errorCode === 0 && response.data) {
                setGlobalConfig(prev => ({
                    ...prev,
                    systemName: response.data.SystemName || "D6000",
                    systemDescription: response.data.SystemDescription || "D6000"
                }));
            }
        } catch (error) {
            console.error("failed to obtain local device information:", error);
        }
    };

    const loadLLDPData = async () => {
        setLoading(true);
        try {
            const neIP = ip;
            if (!neIP) {
                setLoading(false);
                return;
            }

            const deviceMac = await getDeviceMAC(neIP);

            await getLocalInfo(neIP);

            try {
                const response = await getDCSLLDPDirect(neIP);

                if (response.errorCode === 0 && response.data) {
                    const lldpData = response.data;
                    processLldpData(lldpData, deviceMac);
                } else {
                    applyDefaultData(deviceMac);
                }
            } catch (error) {
                applyDefaultData(deviceMac);
            }
        } catch (error) {
            applyDefaultData();
        } finally {
            setLoading(false);
        }
    };

    const processLldpData = (lldpData, deviceMac) => {
        if (!validateLLDPData(lldpData)) {
            applyDefaultData(deviceMac);
            return;
        }

        const normalizedData = normalizeLLDPData(lldpData);
        const formattedData = formatLLDPData(normalizedData);

        const interfaces = Object.keys(formattedData)
            .filter(key => key.startsWith("Port"))
            .map(portName => {
                const portData = formattedData[portName];
                const hasValidData = Object.values(portData).some(
                    value => value !== NULL_VALUE && value !== "" && value !== undefined && value !== null
                );

                return {
                    key: portName,
                    name: portName,
                    "system-name": portData["system-name"] || NULL_VALUE,
                    "system-description": portData["system-description"] || NULL_VALUE,
                    "chassis-id": portData["chassis-id"] || NULL_VALUE,
                    "port-id": portData["port-id"] || NULL_VALUE,
                    "port-description": portData["port-description"] || NULL_VALUE,
                    "interface-number": portData["interface-number"] || NULL_VALUE,
                    "management-address": portData["management-address"] || NULL_VALUE,
                    age: portData.age || NULL_VALUE,
                    "last-update": portData["last-update"] || NULL_VALUE,
                    ttl: portData.ttl || NULL_VALUE,
                    enabled: portData.enabled || "true",
                    hasValidData
                };
            });

        setTableData(interfaces.map(({hasValidData, ...rest}) => rest));
        setGlobalConfig(prev => ({
            ...prev
        }));
    };

    const applyDefaultData = deviceMac => {
        const defaultPorts = [];
        for (let i = 1; i <= 6; i++) {
            defaultPorts.push({
                key: `Port${i}`,
                name: `Port${i}`,
                "system-name": NULL_VALUE,
                "system-description": NULL_VALUE,
                "chassis-id": NULL_VALUE,
                "port-id": NULL_VALUE,
                "port-description": NULL_VALUE,
                "interface-number": NULL_VALUE,
                "management-address": NULL_VALUE,
                age: NULL_VALUE,
                "last-update": NULL_VALUE,
                ttl: NULL_VALUE,
                enabled: "true"
            });
        }

        setTableData(defaultPorts);

        setGlobalConfig(prev => ({
            ...prev,
            systemName: "D6000",
            systemDescription: "D6000",
            chassisMAC: deviceMac || "00-00-00-00-00-00"
        }));
    };

    const columns = [
        {
            title: <strong>Name</strong>,
            dataIndex: "name",
            key: "name",
            sorter: (a, b) => a.name.localeCompare(b.name)
        },
        {
            title: <strong>System Name</strong>,
            dataIndex: "system-name",
            key: "system-name",
            render: value => <Text ellipsis>{value || NULL_VALUE}</Text>,
            sorter: (a, b) => {
                if (a["system-name"] === NULL_VALUE && b["system-name"] === NULL_VALUE) return 0;
                if (a["system-name"] === NULL_VALUE) return -1;
                if (b["system-name"] === NULL_VALUE) return 1;
                return a["system-name"].localeCompare(b["system-name"]);
            }
        },
        {
            title: <strong>System Description</strong>,
            dataIndex: "system-description",
            key: "system-description",
            render: value => value || NULL_VALUE,
            sorter: (a, b) => {
                if (a["system-description"] === NULL_VALUE && b["system-description"] === NULL_VALUE) return 0;
                if (a["system-description"] === NULL_VALUE) return -1;
                if (b["system-description"] === NULL_VALUE) return 1;
                return a["system-description"].localeCompare(b["system-description"]);
            }
        },
        {
            title: <strong>Chassis Mac</strong>,
            dataIndex: "chassis-id",
            key: "chassis-id",
            render: value => value || NULL_VALUE,
            sorter: (a, b) => {
                if (a["chassis-id"] === NULL_VALUE && b["chassis-id"] === NULL_VALUE) return 0;
                if (a["chassis-id"] === NULL_VALUE) return -1;
                if (b["chassis-id"] === NULL_VALUE) return 1;
                return a["chassis-id"].localeCompare(b["chassis-id"]);
            }
        },
        {
            title: <strong>Age</strong>,
            dataIndex: "age",
            key: "age",
            render: value => value || NULL_VALUE,
            sorter: (a, b) => {
                if (a.age === NULL_VALUE && b.age === NULL_VALUE) return 0;
                if (a.age === NULL_VALUE) return -1;
                if (b.age === NULL_VALUE) return 1;
                return Number(a.age) - Number(b.age);
            }
        },
        {
            title: <strong>Last Update</strong>,
            dataIndex: "last-update",
            key: "last-update",
            render: value => value || NULL_VALUE,
            sorter: (a, b) => {
                if (a["last-update"] === NULL_VALUE && b["last-update"] === NULL_VALUE) return 0;
                if (a["last-update"] === NULL_VALUE) return -1;
                if (b["last-update"] === NULL_VALUE) return 1;
                return Number(a["last-update"]) - Number(b["last-update"]);
            }
        },
        {
            title: <strong>TTL</strong>,
            dataIndex: "ttl",
            key: "ttl",
            render: value => value || NULL_VALUE,
            sorter: (a, b) => {
                if (a.ttl === NULL_VALUE && b.ttl === NULL_VALUE) return 0;
                if (a.ttl === NULL_VALUE) return -1;
                if (b.ttl === NULL_VALUE) return 1;
                return Number(a.ttl) - Number(b.ttl);
            }
        },
        {
            title: <strong>Port ID</strong>,
            dataIndex: "port-id",
            key: "port-id",
            render: value => value || NULL_VALUE,
            sorter: (a, b) => {
                if (a["port-id"] === NULL_VALUE && b["port-id"] === NULL_VALUE) return 0;
                if (a["port-id"] === NULL_VALUE) return -1;
                if (b["port-id"] === NULL_VALUE) return 1;
                return a["port-id"].localeCompare(b["port-id"]);
            }
        },
        {
            title: <strong>Management Address</strong>,
            dataIndex: "management-address",
            key: "management-address",
            render: value => value || NULL_VALUE,
            sorter: (a, b) => {
                if (a["management-address"] === NULL_VALUE && b["management-address"] === NULL_VALUE) return 0;
                if (a["management-address"] === NULL_VALUE) return -1;
                if (b["management-address"] === NULL_VALUE) return 1;
                return a["management-address"].localeCompare(b["management-address"]);
            }
        },
        {
            title: <strong>Enabled</strong>,
            dataIndex: "enabled",
            key: "enabled",
            render: value => (
                <Switch
                    checked={value === "true"}
                    disabled
                    className={value === "true" ? styles.enabledSwitch : styles.disabledSwitch}
                />
            ),
            sorter: (a, b) => {
                if (a.enabled === b.enabled) return 0;
                return a.enabled === "true" ? -1 : 1;
            },
            filters: [
                {text: "Enabled", value: "true"},
                {text: "Disabled", value: "false"}
            ],
            onFilter: (value, record) => record.enabled === value
        }
    ];

    const isLoading = loading || deviceMACLoading || dcsInfoLoading;

    return (
        <div className={styles.lldpContainer}>
            {isLoading && (
                <div className={styles.loading}>
                    <Spin tip="Loading..." />
                </div>
            )}

            {ip ? (
                <>
                    <div className={styles.configPanel}>
                        <div className={styles.configRow}>
                            <div className={styles.configItem} style={{width: "33%"}}>
                                <span>Enable</span>
                                <Switch
                                    checked={globalConfig.enabled}
                                    disabled
                                    className={globalConfig.enabled ? styles.enabledSwitch : styles.disabledSwitch}
                                />
                            </div>
                            <div className={styles.configItem} style={{width: "33%"}}>
                                <span>Hello Timer</span>
                                <Input value={globalConfig.helloTimer} disabled />
                            </div>
                            <div className={styles.configItem} style={{width: "33%"}}>
                                <span>System Name</span>
                                <Input value={globalConfig.systemName} disabled />
                            </div>
                        </div>

                        <div className={styles.configRow}>
                            <div className={styles.configItem} style={{width: "33%"}}>
                                <span>System Description</span>
                                <Input value={globalConfig.systemDescription} disabled />
                            </div>
                            <div className={styles.configItem} style={{width: "33%"}}>
                                <span>Chassis MAC</span>
                                <Input value={globalConfig.chassisMAC} disabled />
                            </div>
                            <div
                                className={styles.configItem}
                                style={{
                                    width: "33%",
                                    display: "flex",
                                    justifyContent: "flex-end",
                                    alignItems: "center",
                                    gap: "8px"
                                }}
                            >
                                {}
                            </div>
                        </div>
                    </div>

                    <div className={styles.tableContainer}>
                        <Table
                            columns={columns}
                            dataSource={tableData}
                            loading={isLoading}
                            locale={{
                                emptyText: <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} description="No Data" />
                            }}
                            pagination={{
                                current: currentPage,
                                pageSize,
                                total: tableData.length,
                                showSizeChanger: true,
                                showQuickJumper: true,
                                pageSizeOptions: ["10", "20", "50", "100"],
                                showTotal: total => `Total ${total} items`,
                                onChange: (page, size) => {
                                    setCurrentPage(page);
                                    setPageSize(size);
                                },
                                onShowSizeChange: (current, size) => {
                                    setCurrentPage(1);
                                    setPageSize(size);
                                },
                                locale: {items_per_page: "/page"},
                                size: "small"
                            }}
                            scroll={{x: "max-content"}}
                        />
                    </div>
                </>
            ) : (
                !isLoading && <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} description="Please select D6000 device" />
            )}
        </div>
    );
};

export default LLDP;
