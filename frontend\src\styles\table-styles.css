/* 表格样式重置 - 使用高优先级选择器确保不被覆盖 */
html body .ant-table-wrapper {
    overflow-x: auto !important;
    width: auto !important;
}

html body .ant-table {
    width: auto !important;
    table-layout: auto !important;
}

html body .ant-table-container {
    overflow-x: auto !important;
    width: auto !important;
}

html body .ant-table-tbody > tr > td,
html body .ant-table-thead > tr > th,
html body .ant-table-cell {
    white-space: nowrap !important;
    overflow: visible !important;
    text-overflow: clip !important;
    word-break: keep-all !important;
}

/* 移除所有宽度限制 */
html body .ant-table-wrapper .ant-table-thead > tr > th,
html body .ant-table-wrapper .ant-table-tbody > tr > td {
    width: auto !important;
    min-width: auto !important;
    max-width: none !important;
}

/* 移除特定列的固定宽度设置 */
html body .ant-table-wrapper .ant-table-thead > tr > th:nth-child(n), 
html body .ant-table-wrapper .ant-table-tbody > tr > td:nth-child(n) {
    width: auto !important;
    min-width: auto !important;
    max-width: none !important;
}

/* 移除内联样式 */
html body .ant-table [style*="width"],
html body .ant-table [style*="max-width"] {
    width: auto !important;
    max-width: none !important;
}

/* 保证表格行背景颜色正确显示 */
html body .ant-table-wrapper .ant-table-tbody .ant-table-row:hover > td {
    background-color: #fafafa !important;
}

html body .ant-table-wrapper .ant-table-tbody .ant-table-row-selected > td {
    background-color: #F5FFFE !important;
}

html body .ant-table-wrapper .ant-table-tbody .ant-table-row-selected .ant-table-cell-row-hover {
    background-color: #E6FDFB !important;
}

@media all and (-ms-high-contrast: none), (-ms-high-contrast: active) {
    html body .ant-table-tbody > tr > td {
        white-space: nowrap !important;
        overflow: visible !important;
    }
}

@supports (-moz-appearance:none) {
    html body .ant-table-tbody > tr > td {
        white-space: nowrap !important;
        overflow: visible !important;
    }
}

@media screen and (-webkit-min-device-pixel-ratio:0) {
    html body .ant-table-tbody > tr > td {
        white-space: nowrap !important;
        overflow: visible !important;
    }
} 