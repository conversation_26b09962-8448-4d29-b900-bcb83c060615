import {Input, Tree, Flex, TreeSelect} from "antd";
import {useEffect, useState} from "react";
import Icon from "@ant-design/icons/lib/components/Icon";
import {searchSvg} from "@/utils/common/iconSvg";
import filterSvg from "../pages/Service/Nics/Monitoring/resource/filter.svg?react";
import shrinkSvg from "../pages/Service/Nics/Monitoring/resource/shrink.svg?react";
import unfoldSvg from "../pages/Service/Nics/Monitoring/resource/unfold.svg?react";
import shrinkHoverSvg from "../pages/Service/Nics/Monitoring/resource/shrink_hover.svg?react";
import unfoldHoverSvg from "../pages/Service/Nics/Monitoring/resource/unfold_hover.svg?react";

const deepCopy = obj => {
    if (Array.isArray(obj)) {
        return obj.map(item => {
            if (typeof item === "object" && item !== null) {
                return deepCopy(item);
            }
            return item;
        });
    }
    if (typeof obj === "object" && obj !== null) {
        return Object.entries(obj).reduce((acc, [key, value]) => {
            acc[key] = typeof value === "object" && value !== null ? deepCopy(value) : value;
            return acc;
        }, {});
    }
    return obj;
};

export const AmpConCustomSearchTree = ({treeNodes, onSelect, onDragDrop}) => {
    // const [treeNodes, setTreeNodes] = useState([]);
    const [currTreeNodes, setCurrTreeNodes] = useState([]);
    const [expandedNodes, setExpandedNodes] = useState([]);

    const onSearchTree = e => {
        const {value} = e.target;
        if (value) {
            const newTreeNodes = deepCopy(treeNodes);
            const {filteredNodes, expandedKeys} = searchTree(newTreeNodes, value);
            setCurrTreeNodes(filteredNodes);
            setExpandedNodes(expandedKeys);
        } else {
            setCurrTreeNodes(treeNodes);
            setExpandedNodes([]);
        }
    };

    const searchTree = (nodes, searchContent) => {
        const expandedKeys = new Set();

        function dfs(node, path = []) {
            const newPath = path.concat(node.key);

            let isMatch = node.name.toLowerCase().includes(searchContent.toLowerCase());

            if (isMatch) {
                newPath.forEach(key => expandedKeys.add(key));
            }

            if (node.children) {
                node.children.forEach(child => {
                    const childMatch = dfs(child, newPath);
                    isMatch = isMatch || childMatch;
                });
            }
            node.match = isMatch;
            if (isMatch) {
                node.title = <span style={{color: "red"}}>{node.title}</span>;
            }
            return isMatch;
        }
        const filteredNodes = nodes.filter(node => dfs(node));
        return {filteredNodes, expandedKeys: Array.from(expandedKeys)};
    };

    useEffect(() => {
        setCurrTreeNodes(treeNodes);
        setExpandedNodes([]);
    }, [treeNodes]);
    return (
        <div>
            <Flex vertical gap={8}>
                <Input
                    placeholder="Search"
                    prefix={<Icon component={searchSvg} />}
                    allowClear
                    onChange={onSearchTree}
                    style={{width: "280px", height: "32px", float: "right"}}
                />
                <Tree
                    // className={NewTemplateStyle.templateTreeModule}
                    showIcon
                    defaultExpandedKeys={expandedNodes}
                    autoExpandParent
                    treeData={currTreeNodes}
                    style={{overflow: "auto"}}
                    onSelect={onSelect}
                    draggable={{
                        icon: false,
                        nodeDraggable: () => !!onDragDrop
                    }}
                    onDrop={onDragDrop || null}
                />
            </Flex>
        </div>
    );
};

export const AmpConCustomTreeSelect = ({onChange, treeData}) => {
    const [treeValue, setTreeValue] = useState([]);
    const [hoveredIcons, setHoveredIcons] = useState({});

    const handleMouseEnter = id => {
        setHoveredIcons(prev => ({...prev, [id]: true}));
    };

    const handleMouseLeave = id => {
        setHoveredIcons(prev => ({...prev, [id]: false}));
    };

    const getIconComponent = (expanded, isHovered) => {
        if (expanded) {
            return isHovered ? shrinkHoverSvg : shrinkSvg;
        }
        return isHovered ? unfoldHoverSvg : unfoldSvg;
    };

    const switcherIcon = ({expanded, id}) => {
        const IconComponent = getIconComponent(expanded, hoveredIcons[id]);

        return (
            <IconComponent
                style={{width: "16px", height: "16px", marginTop: "4px", marginRight: "8px"}}
                alt={expanded ? "shrink" : "unfold"}
                onMouseEnter={() => handleMouseEnter(id)}
                onMouseLeave={() => handleMouseLeave(id)}
            />
        );
    };

    const onSelectChange = value => {
        onChange(value);
        setTreeValue(value);
    };

    return (
        <TreeSelect
            maxTagCount={2}
            maxTagTextLength={6}
            treeData={treeData}
            value={treeValue}
            onChange={onSelectChange}
            treeCheckable
            switcherIcon={({expanded, value}) => switcherIcon({expanded, id: value})}
            placeholder="Filter"
            style={{width: 280}}
            allowClear
            virtual={false}
            suffixIcon={<Icon component={filterSvg} />}
        />
    );
};
