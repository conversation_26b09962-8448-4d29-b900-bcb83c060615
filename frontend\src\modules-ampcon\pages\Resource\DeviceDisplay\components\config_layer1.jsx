import styles from "@/modules-ampcon/pages/Resource/DeviceDisplay/components/config_layer.module.scss";
import React, {useEffect, useState, useRef} from "react";
import {useDispatch, useSelector} from "react-redux";
import {useForm} from "antd/es/form/Form";
import {message, Tabs, Tree, Tag, Button, Space, Form, Input, Empty, Modal, Divider, Select} from "antd";
import {updateSwitchAlarm} from "@/store/modules/otn/notificationSlice";
import {getFilterOTNEventAPI} from "@/modules-ampcon/apis/otn";
import {getDCSDevicePort} from "@/modules-ampcon/apis/fmt";
import {
    DCS_1M10,
    DCS_1T4,
    DCS_32T5,
    DCS_4M4,
    DCS_4T4
} from "@/modules-ampcon/pages/Resource/DeviceDisplay/DCS/cards_svg";

import Icon from "@ant-design/icons";
import {AmpConCustomTable, createColumnConfig, AmpConCustomModalForm} from "@/modules-ampcon/components/custom_table";
// import {DynamicTable} from "@/modules-ampcon/pages/Resource/DeviceDisplay/FMT/common_test";
import {DynamicTable} from "@/modules-ampcon/pages/Resource/DeviceDisplay/components/layer1_table";
import EmptyPic from "@/assets/images/App/empty.png";
import {ackSwitchAlarm, getSwitchAlarm} from "@/modules-ampcon/apis/monitor_api";
import LLDP from "@/modules-ampcon/pages/Resource/DeviceDisplay/components/lldp";
import {SortPortsDefault} from "@/modules-ampcon/pages/Resource/DeviceDisplay/utils";

const deviceSchemaMapping = {
    "D6000-4ME4C": DCS_4M4,
    "D6000-4T4E4C": DCS_4T4,
    "D6000-1T4": DCS_1T4,
    "D6000-1M10": DCS_1M10,
    "D6000-32T5": DCS_32T5
};
const {TextArea} = Input;

const DynamicIcon = ({cardSvgName}) => {
    if (cardSvgName === "-1") {
        return <Empty image={EmptyPic} description="No Data" imageStyle={{marginTop: 70}} />;
    }
    return <Icon component={deviceSchemaMapping[cardSvgName]} alt="" />;
};

const updateDCS4M4FecValues = data => {
    const fecValue = data["1"].fec;
    ["2", "3", "4", "5"].forEach(key => {
        if (data[key]) {
            data[key].fec = fecValue;
        }
    });
    return data;
};

const AlarmTable = ({NeIp}) => {
    const [form] = useForm();
    const alarmRef = useRef(null);
    const dispatch = useDispatch();
    const [isModalOpen, setIsModalOpen] = useState(false);
    const [selectedRecord, setSelectedRecord] = useState("");
    const currentUser = useSelector(state => state.user.userInfo);

    const tagStyles = {
        info: styles.infoStyle,
        warn: styles.warnStyle,
        minor: styles.minorStyle,
        major: styles.majorStyle,
        critical: styles.criticalStyle,
        error: styles.criticalStyle
    };

    const tagText = {
        info: "Info",
        warn: "Warning",
        minor: "Minor",
        major: "Major",
        critical: "Critical",
        error: "Critical"
    };

    const tagStyle = type => {
        const style = tagStyles[type] || "";
        const text = tagText[type] || "";
        return <Tag className={style}>{text}</Tag>;
    };
    const formItems = () => {
        return (
            <Form.Item
                name="operatorText"
                label="Operator Text"
                tooltip="Text provided by operator when changing alarm state."
                rules={[
                    {
                        required: true,
                        pattern: /^((?!&|<|>|"|').){0,32}$/,
                        message: "Only support 32 characters and not support the symbols < > & \" '"
                    }
                ]}
                labelCol={{span: 8}}
                wrapperCol={{span: 14}}
                labelAlign="left"
                layout="horizontal"
                LabelWrap
                className="label-wrap"
            >
                <TextArea style={{minHeight: 50, width: 280}} />
            </Form.Item>
        );
    };

    const columns = [
        {
            ...createColumnConfig("NE Name", "sn"),
            fixed: "left"
        },
        createColumnConfig("Resource", "resource"),
        {
            ...createColumnConfig("Serverity", "type"),
            render: (_, record) => (
                <div>
                    <Space>{tagStyle(record.type)}</Space>
                </div>
            )
        },
        createColumnConfig("Create Time", "create_time"),
        createColumnConfig("Count", "count"),
        createColumnConfig("Text", "msg"),
        createColumnConfig("Operator Text", "operator_text"),
        createColumnConfig("Operator Name", "operator_name"),
        createColumnConfig("Time Acknowledged", "operator_time"),
        {
            title: "Operation",
            dataIndex: "",
            fixed: "right",
            key: "operation",
            render: (_, record) => (
                <a
                    style={{color: "#14c9bb"}}
                    onClick={() => {
                        setIsModalOpen(true);
                        setSelectedRecord(record.id);
                    }}
                >
                    ACK
                </a>
            )
        }
    ];

    return (
        <>
            <AmpConCustomModalForm
                title="ACK"
                isModalOpen={isModalOpen}
                formInstance={form}
                layoutProps={{
                    labelCol: {
                        span: 5
                    }
                }}
                CustomFormItems={formItems}
                onCancel={() => {
                    form.resetFields();
                    setIsModalOpen(false);
                }}
                onSubmit={async values => {
                    const ret = await ackSwitchAlarm({
                        eventId: selectedRecord,
                        operatorName: currentUser.username,
                        operatorText: values.operatorText
                    });
                    if (ret.status === 200) {
                        form.resetFields();
                        setIsModalOpen(false);
                        await getSwitchAlarm().then(res => {
                            if (res.status === 200) {
                                dispatch(updateSwitchAlarm(res.data));
                                message.success("Success saved!");
                            } else {
                                message.error("Error fetching data.");
                            }
                        });
                    } else {
                        message.error(ret.msg);
                    }
                }}
                modalClass="ampcon-middle-modal"
            />
            <AmpConCustomTable
                className={styles.alarmStyle}
                fetchAPIInfo={getFilterOTNEventAPI}
                fetchAPIParams={[NeIp]}
                columns={columns}
                ref={alarmRef}
                isShowPagination
            />
        </>
    );
};

export const Layer1Config = ({NeIp, CardId, CardName, tabType, NeName}) => {
    const [treeData, setTreeData] = useState([]);
    const [tableData, setTableData] = useState([]);
    const [expandedKeys, setExpandedKeys] = useState([]);
    const [cardSvgName, setCardSvgName] = useState("");
    const [cardType, setCardType] = useState();
    const cardTypeRef = useRef(cardType);
    const [filteredTableData, setFilteredTableData] = useState([]);
    const [cardSuffix, setCardSuffix] = useState();
    const [slotNo, setSlotNo] = useState();
    const selectedPort = useRef("");

    useEffect(() => {
        loadData();
    }, [CardId]);

    const loadData = () => {
        getDCSDevicePort(CardId, tabType).then(rs => {
            if (rs.errorCode !== 200 && CardId !== undefined) {
                message.error("Get port info failed");
            }

            setSlotNo(rs.data.info.slot_index);
            const suffix = CardName.match(/(-\d+-\d+)$/)?.[0] || "";
            setCardSuffix(suffix);
            setCardType(rs.data.info.type);
            cardTypeRef.current = rs.data.info.type;

            const treeChildren = rs.data.port_name.map(item => ({
                title: `PORT${suffix}-${item}`,
                key: item
            }));
            const _data = JSON.parse(rs.data.ports_info?.ports_data);
            if (cardTypeRef.current === "4ME4C") {
                updateDCS4M4FecValues(_data);
            }
            setTableData(_data);
            setFilteredTableData(_data);

            setTreeData([
                {
                    title: CardName,
                    key: CardId,
                    children: SortPortsDefault(treeChildren)
                }
            ]);
            setExpandedKeys([CardId]);

            const CardType = rs.data.info.type;
            const CardModel = rs.data.info.model;

            if (deviceSchemaMapping[CardModel]) {
                setCardSvgName(CardModel);
            } else {
                const matchingKey = Object.keys(deviceSchemaMapping).find(key => key.includes(CardType));
                if (matchingKey) {
                    setCardSvgName(matchingKey);
                } else {
                    setCardSvgName("-1");
                }
            }
        });
    };

    const reloadData = () => {
        getDCSDevicePort(CardId, tabType).then(rs => {
            if (rs.errorCode !== 200) {
                message.error("Get port info failed");
            }
            const _data = JSON.parse(rs.data.ports_info?.ports_data);
            if (cardTypeRef.current === "4ME4C") {
                updateDCS4M4FecValues(_data);
            }
            setTableData(_data);
            if (selectedPort.current === "") {
                setFilteredTableData(_data);
                return;
            }
            const filteredData = Object.values(_data).filter(row => row.name === selectedPort.current);
            setFilteredTableData(filteredData);
        });
    };

    const items = [
        {
            key: "port",
            label: "Port Management",
            children: (
                <div style={{marginTop: 24}}>
                    <DynamicTable
                        label="port"
                        data={filteredTableData}
                        tabType="linecard"
                        CardName={CardName}
                        CardType={cardType}
                        CardSuffix={cardSuffix}
                        CardId={CardId}
                        NeIP={NeIp}
                        showExportButton={false}
                        showRefreshButton={false}
                        NeName={NeName}
                        afterUpdate={reloadData}
                    />
                </div>
            )
        },
        {
            key: "power",
            label: "Optical Power Management",
            children: (
                <div style={{marginTop: 24}}>
                    <DynamicTable
                        label="power"
                        data={filteredTableData}
                        tabType="linecard"
                        CardName={CardName}
                        CardSuffix={cardSuffix}
                        CardId={CardId}
                        CardType={cardType}
                        SlotNo={slotNo}
                        NeIP={NeIp}
                        showExportButton={false}
                        showRefreshButton={false}
                        NeName={NeName}
                        afterUpdate={reloadData}
                    />
                </div>
            )
        },
        {
            key: "lldp",
            label: "LLDP",
            children: (
                <div style={{marginTop: 24}}>
                    <LLDP ip={NeIp} />
                </div>
            )
        },
        {
            key: "alarm",
            label: "Alarm",
            children: (
                <div style={{marginTop: 24}}>
                    <AlarmTable NeIp={NeIp} />
                </div>
            )
        }
    ];

    const handleTreeSelect = selectedKeys => {
        if (selectedKeys.length === 0 || selectedKeys[0] === CardId) {
            setFilteredTableData(tableData);
            return;
        }
        selectedPort.current = selectedKeys[0];
        const filteredData = Object.values(tableData).filter(row => row.name === selectedPort.current);
        setFilteredTableData(filteredData);
    };

    return (
        <div className={styles.container}>
            <div className={styles.up}>
                <div className={styles.tree} style={{borderRadius: 5}}>
                    <Tree
                        treeData={treeData}
                        showLine
                        expandedKeys={expandedKeys}
                        onExpand={keys => setExpandedKeys(keys)}
                        onSelect={handleTreeSelect}
                    />
                </div>
                <div className={styles.view} style={{borderRadius: 5}}>
                    {cardSvgName && <DynamicIcon cardSvgName={cardSvgName} />}
                </div>
            </div>
            <div className={styles.down} style={{borderRadius: 5}}>
                <Tabs style={{overflow: "hidden"}} destroyInactiveTabPane items={items} />
            </div>
        </div>
    );
};
