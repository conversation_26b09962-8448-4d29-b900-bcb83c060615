.wrap {
    width: 100%;
    height: 100%;
    position: relative;
}

.loading {
  position: absolute;
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 4px;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.deviceDisplay {
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 100%;
  background-color: #f0f2f5;
  overflow: auto;
  gap: 18px 24px;
  grid-template-columns: repeat(12, 1fr);
  grid-template-rows: repeat(3, 1fr);
  > div {
        &:nth-child(1) {
            grid-column: 1 / 13;
        }
        &:nth-child(2) {
            grid-column: 1 / 13;
        }
        &:nth-child(3) {
            grid-column: 1 / 13;
        }
    }
}

.viewDisplay {
  width: 100%;
  background-color: #FFFFFF;
  border-radius: 0 0 8px 8px;
  padding-top: 10px;
  padding-bottom: 24px;
}

.customRadioGroup {
  :global {
    .ant-radio-button-wrapper {
      width: 100px;
      text-align: center;
      color: #B3BBC8;
      &:hover {
        color: #14C9BB;
      }
      &-checked {
        color: #14C9BB;
      }
    }
  }
}

.chassisFront {
  width: 1850px;
  height: 200px;
  background-image: url("./img/front_view.svg");
  background-size: 100% 100%;
  position: relative;
}

.chassisRear {
  width: 1850px;
  height: 200px;
  background-image: url("./img/rear_view.svg");
  background-size: 100% 100%;
  position: relative;
}

.CARD_common {
  background-size: 100% 100%;
  position: absolute;
}

@keyframes led-flashing {
    10% {
        opacity: 0;
    }
    90% {
        opacity: 1;
    }
}

.LED_COMMON {
    box-shadow: none;
    width: 8px;
    height: 8px;
    border-radius: 4px;
    position: absolute;
    //background-color: #2bbf2b;
}

.LED_ACTIVE {
    background-color: #2bbf2b;
}

.LED_CRITICAL {
    background-color: red;
}

.LED_FLASHING {
    background-color: #2bbf2b;
    animation: led-flashing 1s infinite;
}

.CARD_LSU-78469 {
  width: 570px;
  height: 92px;
  background-image: url("./img/LSU-78469.svg");
}

.CARD_OEO-10G {
  width: 570px;
  height: 92px;
  background-image: url("./img/OEO-10G.svg");
}

.CARD_EDFA {
  width: 570px;
  height: 92px;
  background-image: url("./img/EDFA-248133.svg");
}

.CARD_DEDFA {
  width: 570px;
  height: 92px;
  background-image: url("./img/EDFA-Pre-amplifier-248139.svg");
}

.CARD_OPD {
  width: 570px;
  height: 92px;
  background-image: url("./img/FMT-4OPD-78568.svg");
}

.CARD_FMT-BDOLP-72700 {
  width: 570px;
  height: 92px;
  background-image: url("./img/FMT-BDOLP-72700.svg");
}

.CARD_FMT-BDSplitter-73324 {
  width: 570px;
  height: 92px;
  background-image: url("./img/FMT-BDSplitter-73324.svg");
}

.CARD_FMT-OPM-71586 {
  width: 571px;
  height: 187px;
  background-image: url("./img/FMT-OPM-71586.svg");
}

.CARD_FMT-OTDR-73281 {
  width: 570px;
  height: 92px;
  background-image: url("./img/FMT-OTDR-73281.svg");
}

.CARD_DCM {
  width: 571px;
  height: 187px;
  background-image: url("./img/FMT40-DCM-65781.svg");
}

.CARD_FMTOSW-1x16-73287 {
  width: 571px;
  height: 187px;
  background-image: url("./img/FMTOSW-1x16-73287.svg");
}

.CARD_FSBR-1x2-65981 {
  width: 570px;
  height: 92px;
  background-image: url("./img/FSBR-1x2-65981.svg");
}

.CARD_HPA-65789 {
  width: 571px;
  height: 187px;
  background-image: url("./img/HPA-65789.svg");
}

.CARD_OEO {
  width: 570px;
  height: 92px;
  background-image: url("./img/OEO.svg");
}

.CARD_OLP {
  width: 570px;
  height: 92px;
  background-image: url("./img/OLP-34720.svg");
}

.CARD_SOA-69350 {
  width: 570px;
  height: 92px;
  background-image: url("./img/SOA-69350.svg");
}

.CARD_TDCM {
  width: 570px;
  height: 92px;
  background-image: url("./img/TDCM-72427.svg");
}

.CARD_VOA {
  width: 570px;
  height: 92px;
  background-image: url("./img/VOA-65271.svg");
}

.PORT1 {
  width: 17px;
  height: 25px;
  background-size: 100% 100%;
  position: absolute;
  background-image: url("./img/port_1.svg");
}

.PORT2 {
  width: 43px;
  height: 30px;
  background-image: url("./img/port_2.png");
  background-size: 100% 100%;
  position: absolute;
  //transform: rotate(-90deg);
}

.PORT2::after {
  content: ''; /* 伪元素需要设置content */
  position: absolute;
  bottom: -6px; /* 离底部20像素 */
  left: 50%; /* 水平居中 */
  transform: translateX(-50%); /* 精确居中 */
  width: 5px;
  height: 5px;
  background-color: #2bbf2b;
  border-radius: 50%;
}
