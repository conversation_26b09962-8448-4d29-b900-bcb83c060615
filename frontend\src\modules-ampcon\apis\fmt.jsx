import {request} from "@/utils/common/request";

const baseURL = "/ampcon";
const fmtUrl = `${baseURL}/fmt`;
const dcsUrl = `${baseURL}/dcs`;

// bypass database in backend
export function queryFMTInfo(ip) {
    return request({
        url: `${fmtUrl}/info/query`,
        method: "GET",
        params: {
            ip
        }
    });
}

// query database in backend
export function getFMTInfo(ip) {
    return request({
        url: `${fmtUrl}/info/get`,
        method: "GET",
        params: {
            ip
        }
    });
}

export function modifyFMTConfig(data) {
    return request({
        url: `${fmtUrl}/config/modify`,
        method: "PUT",
        data
    });
}

export function modifyFMTPortNote(data) {
    return request({
        url: `${fmtUrl}/config/set_note`,
        method: "POST",
        data
    });
}

export function queryFMTConfig(ip, slotIndex = null, cardId = null) {
    return request({
        url: `${fmtUrl}/config/query`,
        method: "GET",
        params: {
            ip,
            slotIndex,
            cardId
        }
    });
}

export function getFMTDeviceCard(filterCard, {ip}) {
    return request({
        url: `${fmtUrl}/get_fmt_device_card`,
        method: "POST",
        data: {
            ip,
            filterCard
        }
    });
}

export function getFMTDevicePort(id, type = "") {
    return request({
        url: `${fmtUrl}/get_fmt_device_port`,
        method: "POST",
        data: {
            id,
            type
        }
    });
}

export function getFMTDeviceSinglePort(cardID, portName) {
    return request({
        url: `${fmtUrl}/get_fmt_device_single_port`,
        method: "POST",
        data: {
            cardID,
            portName
        }
    });
}

// bypass database in backend
export function queryDCSInfo(ip) {
    return request({
        url: `${dcsUrl}/info/query`,
        method: "GET",
        params: {
            ip
        }
    });
}

// query database in backend
export function getDCSInfo(ip) {
    return request({
        url: `${dcsUrl}/info/get`,
        method: "GET",
        params: {
            ip
        }
    });
}

export function storeConnectionInfo(data) {
    return request({
        url: `${dcsUrl}/info/store_connection_info`,
        method: "POST",
        headers: {
            "Content-Type": "application/json"
        },
        data
    });
}

export function getConnectionInfo() {
    return request({
        url: `${dcsUrl}/info/get_connection_info`,
        method: "GET"
    });
}

export function getTopo(ip) {
    return request({
        url: `${dcsUrl}/get_topology`,
        method: "POST",
        params: {
            ip
        }
    });
}

export async function getDCSLLDPInfo(ip) {
    try {
        const response = await getTopo(ip);

        if (response?.lldp_info) {
            return {errorCode: 0, errorMsg: "", data: response.lldp_info};
        }

        const connections = response?.connectionInfo || [];
        if (connections.length > 0) {
            const lldpInfo = connections.reduce((acc, conn, idx) => {
                const portNum = idx + 1;
                if (conn.destIP && portNum <= 6) {
                    acc[`Port${portNum}`] = {
                        PortSysIP: conn.destIP,
                        PortSysName: conn.destName || "Switch",
                        SystemIP: conn.destIP
                    };
                }
                return acc;
            }, {});

            if (Object.keys(lldpInfo).length > 0) {
                return {errorCode: 0, errorMsg: "", data: lldpInfo};
            }
        }

        return {errorCode: 1, errorMsg: "No LLDP data available", data: null};
    } catch (error) {
        return {errorCode: 1, errorMsg: error?.message || "Failed to fetch LLDP data", data: null};
    }
}

export function getDCSDeviceMAC(ip) {
    return request({
        url: `${dcsUrl}/get_device_mac`,
        method: "GET",
        params: {
            ip
        }
    });
}

export function modifyDCSConfig(data) {
    return request({
        url: `${dcsUrl}/config/modify`,
        method: "PUT",
        data
    });
}

export function batchModifyDCSConfig(data) {
    return request({
        url: `${dcsUrl}/config/batch_modify`,
        method: "PUT",
        data
    });
}

export function modifyDCSPortNote(data) {
    return request({
        url: `${dcsUrl}/config/set_note`,
        method: "POST",
        data
    });
}

export function queryDCSConfig(ip, slotIndex = null, cardId = null) {
    return request({
        url: `${dcsUrl}/config/query`,
        method: "GET",
        params: {
            ip,
            slotIndex,
            cardId
        }
    });
}

export function getDCSDeviceCard(filterCard, {ip}) {
    return request({
        url: `${dcsUrl}/get_dcs_device_card`,
        method: "POST",
        data: {
            ip,
            filterCard
        }
    });
}

export function getDCSDevicePort(id, type = "") {
    return request({
        url: `${dcsUrl}/get_dcs_device_port`,
        method: "POST",
        data: {
            id,
            type
        }
    });
}

export function getDCSDeviceSinglePort(cardID, portName) {
    return request({
        url: `${dcsUrl}/get_dcs_device_single_port`,
        method: "POST",
        data: {
            cardID,
            portName
        }
    });
}

export function getFilterDCSEventAPI(NeIp, page, pageSize, filterFields = [], sortFields = [], searchFields = {}) {
    return request({
        url: `${dcsUrl}/get_filter_dcs_event`,
        method: "POST",
        data: {
            NeIp,
            page,
            pageSize,
            filterFields,
            sortFields,
            searchFields
        }
    });
}

export function getDCSLLDPDirect(ip) {
    return request({
        url: `${dcsUrl}/get_dcs_lldp_direct`,
        method: "GET",
        params: {
            ip
        }
    });
}

export function getDCSLocalInfo(ip) {
    return request({
        url: `${dcsUrl}/get_dcs_local_info`,
        method: "GET",
        params: {
            ip
        }
    });
}
