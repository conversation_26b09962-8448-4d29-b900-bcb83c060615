import {<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, Tag} from "antd";
import React, {useEffect, useState} from "react";
import Icon, {EditOutlined} from "@ant-design/icons";
import {apiEditRpc, apiGetCategory, apiGetYang, netconfGetByXML} from "@/modules-otn/apis/api";
import {gLabelList} from "@/store/modules/otn/languageOTNSlice";
import {
    addNS,
    convertToArray,
    getText,
    getValueByJPath,
    getYangByPath,
    removeNS1,
    removeNSForObj,
    TableWidthConfig,
    encodeSensorPath
} from "@/modules-otn/utils/util";
import {CustomTable} from "@/modules-otn/pages/otn/common/custom_table";
// eslint-disable-next-line import/no-extraneous-dependencies
import YANG_CONFIG from "@/modules-otn/config/yang_config";
import {plusIcon, refreshEnabledIcon} from "@/modules-otn/pages/otn/device/device_icons";
import {openModalEdit} from "./edit_form";
import {openModalCreate} from "./create_form";
import styles from "./edit_table.module.scss";
import {bigModal} from "../modal/custom_modal";

/**
 *
 * @param categoryName defined in "src/config/category.js"
 * @param keys string | string[] Object keys
 * @param operation string[] array includes: "create", "refresh"
 * @param rowOperation string[] array includes: "delete", "edit", etc...
 * @returns {JSX.Element}
 * @constructor
 */
const EditTable = ({
    categoryName,
    category,
    Yang,
    rowOperation,
    createEnable = false,
    refresh,
    ne_id,
    tags = [],
    keys = [],
    type,
    readonly,
    columnFormat,
    onCreate,
    onEdit,
    language
}) => {
    const {rootPath, configRootPath, tabs} = structuredClone(category);
    rowOperation = convertToArray(rowOperation);
    const [tableConfig, setTableConfig] = useState({});
    const [tableColumns, setTableColumns] = useState(null);
    const [loading, setLoading] = useState(false);
    const keyNames = getYangByPath(Yang, rootPath).definition.key.split(" ");
    const useDefine = YANG_CONFIG[categoryName];

    const updateTableConfig = () => {
        setLoading(true);
        const _keys = [...keys];
        const req = {};
        let obj;
        rootPath.reduce(
            // eslint-disable-next-line no-return-assign
            (p, c) => [
                (p[0][c] = Object.fromEntries(
                    p[1][c].definition.key && _keys.length > 0
                        ? p[1][c].definition.key.split(" ").map(k => [k, _keys.shift()])
                        : []
                )),
                (obj = p[1][c])
            ],
            [req, Yang]
        );
        netconfGetByXML({
            ne_id,
            msg: true,
            xml: addNS(req, Yang)
        })
            .then(rs => {
                let dataSource = convertToArray(getValueByJPath(removeNSForObj(rs), rootPath));
                dataSource =
                    dataSource === ""
                        ? []
                        : dataSource.map(item => {
                              const d = removeNS1(
                                  Object.values(tabs)[0].mode === 0 ? (item.state ?? item.config) : item
                              );
                              tags.forEach(tag => {
                                  if (Array.isArray(tag[tag.length - 1])) {
                                      const newTag = [...tag];
                                      const tagKeys = newTag.pop();
                                      const tmp = convertToArray(getValueByJPath(item, newTag));
                                      d[tag[0]] = tmp.map(t => tagKeys.map(k => t[k]).join(":"));
                                  } else {
                                      d[tag[0]] = convertToArray(getValueByJPath(item, tag));
                                  }
                              });
                              return d;
                          });
                setTableConfig({data: dataSource, keys: obj.definition.key});
            })
            .catch(e => {
                // eslint-disable-next-line no-console
                console.log(e.message);
            })
            .finally(() => {
                setLoading(false);
            });
    };

    useEffect(() => {
        updateTableConfig();
        const yangState = getYangByPath(Yang, Object.values(tabs)[0]?.path ?? [])?.state;
        const columns = [
            ...Object.keys(Object.values(tabs)[0].field)
                .filter(f => !useDefine?.[f]?.tableHidden)
                .map(f => {
                    const {units} = yangState?.[f] ?? {};

                    let title = gLabelList?.[f] ?? getText(f);
                    if (!title.includes("(") && units) title = `${title}(${units})`;

                    const col = {title, dataIndex: f};
                    if (f === "trace-data") {
                        col.ellipsis = true;
                    }
                    if (TableWidthConfig[categoryName]?.[f]) {
                        Object.assign(col, TableWidthConfig[categoryName][f]);
                    }
                    return col;
                }),
            ...tags.map(tag => ({
                title: getText(tag[0]),
                dataIndex: tag[0],
                render: (val, r) => (
                    <>
                        {val.map(item => (
                            <Tag
                                key={item}
                                style={{
                                    margin: "0 8px 0 0",
                                    padding: "0 7px",
                                    background: "#14C9BB1A",
                                    color: "#14C9BB",
                                    borderColor: "#14C9BB"
                                }}
                            >
                                {tag[0] === "sensor-paths" ? encodeSensorPath(item) : item}
                            </Tag>
                        ))}
                        <Button
                            type="link"
                            shape="circle"
                            icon={<EditOutlined />}
                            size="small"
                            onClick={() =>
                                openModalTable(tag[0], [...keys, ...keyNames.map(k => r[k])], type, null, {
                                    width: "80%",
                                    rowOperation: [{type: "delete"}],
                                    createEnable: true,
                                    refresh: true,
                                    callback: updateTableConfig
                                })
                            }
                        />
                    </>
                )
            }))
        ];
        if (rowOperation.length > 0) {
            columns.push({
                title: gLabelList.operation,
                fixed: "right",
                // width: 100,
                render: (_, r) => {
                    const arr = [];
                    // eslint-disable-next-line guard-for-in,no-restricted-syntax
                    for (const i in rowOperation) {
                        if (typeof rowOperation[i] === "object") {
                            if (rowOperation[i].type === "relate") {
                                arr.push(
                                    <a
                                        key={rowOperation[i].nodeType}
                                        style={{color: "#14C9BB"}}
                                        onClick={() => {
                                            const _keys = JSON.parse(JSON.stringify(keys));
                                            const requestKeys = [..._keys];
                                            const request = {};
                                            let obj = null;
                                            let yang = null;
                                            (configRootPath || rootPath).reduce(
                                                (p, c) => {
                                                    obj = Object.fromEntries(
                                                        p[1][c].definition.key
                                                            ? p[1][c].definition.key
                                                                  .split(" ")
                                                                  .map(k => [k, _keys.shift()])
                                                            : []
                                                    );
                                                    yang = p[1][c];
                                                    // eslint-disable-next-line no-return-assign
                                                    return [(p[0][c] = obj), p[1][c]];
                                                },
                                                [request, Yang]
                                            );
                                            yang.definition?.key?.split(" ").forEach(k => {
                                                requestKeys.push(r[k]);
                                            });
                                            openModalTable(
                                                rowOperation[i].nodeType,
                                                requestKeys,
                                                type,
                                                rowOperation[i].label
                                                    ? rowOperation[i].label
                                                    : rowOperation[i].nodeType,
                                                {
                                                    width: "80%"
                                                }
                                            );
                                        }}
                                    >
                                        {getText(rowOperation[i].nodeType)}
                                    </a>
                                );
                            } else if (rowOperation[i].type === "edit") {
                                arr.push(
                                    <a
                                        key={rowOperation[i].type}
                                        style={{color: "#14C9BB"}}
                                        onClick={async () => {
                                            if (onEdit) {
                                                onEdit({record: r, refresh: updateTableConfig});
                                                return;
                                            }
                                            // rowOperation[i].onClick?.(keyNames.map(k => r[k]));
                                            openModalEdit(
                                                categoryName,
                                                [...keys, ...keyNames.map(k => r[k])],
                                                type,
                                                "",
                                                ne_id,
                                                undefined,
                                                updateTableConfig
                                            );
                                        }}
                                    >
                                        {gLabelList.edit}
                                    </a>
                                );
                            } else if (rowOperation[i].type === "delete") {
                                arr.push(
                                    <Popconfirm
                                        key={rowOperation[i]}
                                        title={gLabelList.delete_confirm}
                                        okText={gLabelList.ok}
                                        cancelText={gLabelList.cancel}
                                        onConfirm={async () => {
                                            const _keys = JSON.parse(JSON.stringify(keys));
                                            // console.log(_keys);
                                            const request = {};
                                            let obj = null;
                                            let yang = null;
                                            (category.configRootPath || category.rootPath).reduce(
                                                (p, c) => {
                                                    obj = Object.fromEntries(
                                                        p[1][c].definition.key
                                                            ? p[1][c].definition.key
                                                                  .split(" ")
                                                                  .map(k => [k, _keys.shift()])
                                                            : []
                                                    );
                                                    yang = p[1][c];
                                                    // eslint-disable-next-line no-return-assign
                                                    return [(p[0][c] = obj), p[1][c]];
                                                },
                                                [request, Yang]
                                            );
                                            obj["@nc:operation"] = "delete";
                                            yang.definition?.key?.split(" ").forEach(k => {
                                                obj[k] = r[k];
                                            });
                                            await apiEditRpc({
                                                ne_id,
                                                params: request,
                                                success: () => {
                                                    setTimeout(() => {
                                                        updateTableConfig();
                                                    }, 2000);
                                                }
                                            });
                                        }}
                                    >
                                        <a style={{color: "#14C9BB"}}>{gLabelList.delete}</a>
                                    </Popconfirm>
                                );
                            } else {
                                arr.push(
                                    <a
                                        key={rowOperation[i].type}
                                        style={{color: "#14C9BB"}}
                                        onClick={async () => {
                                            rowOperation[i].onClick?.(keyNames.map(k => r[k]));
                                        }}
                                    >
                                        {getText(rowOperation[i].type)}
                                    </a>
                                );
                            }
                        } else {
                            arr.push(
                                <a key={rowOperation[i]} onClick={async () => {}}>
                                    {getText(rowOperation[i])}
                                </a>
                            );
                        }
                        if (i < rowOperation.length - 1) {
                            arr.push(<Divider key={i} type="vertical" />);
                        }
                    }
                    return arr;
                }
            });
        }

        if (columnFormat) {
            columns.forEach(item => {
                if (columnFormat?.[item.dataIndex]) {
                    item.render = columnFormat[item.dataIndex];
                }
            });
        }
        if (columns?.length) {
            columns[0].fixed = "left";
        }
        setTableColumns(columns);
    }, [ne_id, language]);

    return (
        <>
            <div
                className={styles.edit_table_header}
                style={{borderBottom: "none", paddingBottom: 0, paddingLeft: 10, marginBottom: 24}}
            >
                {/* <span className={styles.edit_table_header_title}>{title}</span> */}
                <Space size={16}>
                    {createEnable ? (
                        <Button
                            key="create"
                            icon={<Icon component={plusIcon} />}
                            type="primary"
                            onClick={() => {
                                if (onCreate) {
                                    onCreate(updateTableConfig);
                                    return;
                                }
                                openModalCreate({
                                    categoryName,
                                    type,
                                    title: `${gLabelList.create} ${getText(rootPath[rootPath.length - 1])}`,
                                    keys,
                                    ne_id,
                                    callback: () => {
                                        setTimeout(() => {
                                            updateTableConfig();
                                        }, 2000);
                                    }
                                });
                            }}
                        >
                            {gLabelList.create}
                        </Button>
                    ) : null}
                    {refresh ? (
                        <Button
                            key="refresh"
                            onClick={updateTableConfig}
                            icon={<Icon component={refreshEnabledIcon} />}
                        >
                            {gLabelList.refresh}
                        </Button>
                    ) : null}
                </Space>
            </div>
            <div className={styles.edit_table_content}>
                {CustomTable({
                    rowKey: tableConfig.keys,
                    columns: tableColumns,
                    dataSource: tableConfig.data,
                    pagination: false,
                    loading,
                    scroll: {x: "max-content"},
                    onRow: r => ({
                        onDoubleClick: () => {
                            openModalEdit(
                                categoryName,
                                [...keys, ...tableConfig.keys.split(" ").map(k => r[k])],
                                type,
                                null,
                                ne_id,
                                null,
                                null,
                                readonly
                            );
                        }
                    })
                })}
            </div>
        </>
    );
};

/**
 *
 * @param categoryName
 * @param keys
 * @param type
 * @param title
 * @param config
 * @param ne_id
 * @param db_key  如果有dbKey,下面两个参数就不要
 * @param requestType  没有dbkey，要从索引找值就要设置类型，和redis的字段同名
 * @param filter
 * @param requestCategoryName
 */
const openModalTable = (
    categoryName,
    keys,
    type,
    title,
    config,
    ne_id,
    db_key,
    requestType,
    filter,
    requestCategoryName,
    readonly
) => {
    if (!categoryName) return;
    apiGetCategory(categoryName, type).then(categoryRs => {
        apiGetYang(type).then(yang => {
            bigModal({
                title,
                okText: gLabelList.ok,
                onOk: () => {
                    config?.callback?.();
                },
                onCancel: () => {
                    config?.callback?.();
                },
                cancelText: " ",
                cancelButtonProps: {style: {display: "none"}},
                content: (
                    <EditTable
                        categoryName={categoryName}
                        rowOperation={config?.rowOperation}
                        createEnable={config?.createEnable}
                        refresh={config?.refresh}
                        category={categoryRs}
                        Yang={yang}
                        db_key={db_key}
                        ne_id={ne_id}
                        requestType={requestType}
                        filter={filter}
                        requestCategoryName={requestCategoryName}
                        keys={typeof keys === "string" ? [keys] : keys}
                        type={type}
                        readonly={readonly}
                        showType="Modal"
                    />
                )
            });
        });
    });
};

export {openModalTable, EditTable};
