import {<PERSON>, <PERSON>lex, Tag, Tooltip, Typography} from "antd";
import Icon, {CheckCircleFilled, CloseCircleFilled} from "@ant-design/icons";
import React from "react";
import {getDeviceStateValue, getText, NULL_VALUE, DebounceButton, rootModal} from "@/modules-otn/utils/util";
import {apiEditRpc, NEGet, NESet} from "@/modules-otn/apis/api";
import {editCommonIcon, editTableIcon, moreInfo} from "@/modules-otn/pages/otn/device/device_icons";
import openCustomEditForm from "@/modules-otn/components/form/edit_form_custom";
import styles from "@/modules-otn/pages/otn/device/cardPanel.module.scss";
import openDisplayDialog from "@/modules-otn/pages/otn/device/display_info";
import useUserRight from "@/modules-otn/hooks/useUserRight";
import {useSelector} from "react-redux";
import {smallModal} from "@/modules-otn/components/modal/custom_modal";

const {Paragraph} = Typography;

const CardInfo = ({data, refresh}) => {
    // const [dataOptions, setDataOptions] = useState({});
    // console.log("data", data);
    const readyOnlyRight = useUserRight();
    const {labelList} = useSelector(state => state.languageOTN);
    const {ne_id, type, name, neData, stateData} = data ?? {};

    const editComponent = cfg => {
        if (!cfg.value) return NULL_VALUE;
        return (
            <Paragraph
                key={cfg.key}
                style={{marginBottom: 0, display: "flex"}}
                editable={
                    readyOnlyRight.disabled
                        ? false
                        : {
                              maxLength: 32,
                              icon: <Icon component={editTableIcon} />,
                              onChange: newVal => {
                                  if (!newVal || newVal === cfg.value) {
                                      return;
                                  }
                                  cfg.change(newVal).then(rs => {
                                      if (rs.apiResult !== "fail") {
                                          refresh?.();
                                      }
                                  });
                              },
                              text: cfg.value,
                              triggerType: ["icon", "text"]
                          }
                }
            >
                <div style={{paddingRight: 15}} title={cfg.value}>
                    {cfg.value}
                </div>
            </Paragraph>
        );
    };

    const resetFactory = () => {
        const modal = smallModal({
            title: labelList.warning,
            content: labelList.reset_to_factory,
            // eslint-disable-next-line no-unused-vars
            onOk: _ => {
                NESet({
                    ne_id,
                    parameter: {
                        ramanModuleStatusGroup: {
                            ramanModuleResetToFactory: 1
                        }
                    },
                    success: () => {
                        modal.destroy();
                    }
                }).then();
            }
        });
    };

    const SHOW_INFO_CONFIG = {
        chassis: {
            title: "Chassis Info",
            columns: [
                {
                    dataIndex: "chassis-type",
                    getData: rs => rs?.chassis?.state?.["chassis-type"]
                },
                {
                    dataIndex: "slot-num",
                    getData: () => 8
                },
                {
                    dataIndex: "hardware-version"
                },
                {
                    dataIndex: "temperature",
                    unit: "°C",
                    getData: rs => rs?.state?.temperature?.instant
                },
                {
                    dataIndex: "serial-no",
                    label: "SN"
                },
                {
                    dataIndex: "mfg-name",
                    label: "manufacture"
                },
                {
                    dataIndex: "firmware-version"
                },
                {
                    dataIndex: "used-power",
                    label: "actual-power",
                    unit: "W"
                },
                {
                    dataIndex: "part-no",
                    label: "PN"
                },
                {
                    dataIndex: "mfg-date",
                    label: "production-date"
                },
                {
                    dataIndex: "software-version"
                },
                {
                    dataIndex: "description",
                    label: "other-info",
                    getData: rs => rs?.config?.description,
                    render: rs => {
                        return editComponent({
                            key: "description",
                            value: rs?.config?.description,
                            change: async newVal => {
                                return await apiEditRpc({
                                    ne_id,
                                    params: {
                                        components: {
                                            component: {
                                                name,
                                                config: {
                                                    description: newVal
                                                }
                                            }
                                        }
                                    }
                                });
                            }
                        });
                    }
                }
            ]
        },
        PSU: {
            title: "PSU Info",
            columns: [
                {
                    dataIndex: "card-type",
                    getData: rs => rs?.state.name
                },
                {
                    dataIndex: "slot-num",
                    getData: () => name.split("-").pop()
                },
                {
                    dataIndex: "hardware-version"
                },
                {
                    dataIndex: "temperature",
                    unit: "°C",
                    getData: rs => rs?.state?.temperature?.instant
                },
                {
                    dataIndex: "serial-no",
                    label: "SN"
                },
                {
                    dataIndex: "mfg-name",
                    label: "manufacture"
                },
                {
                    dataIndex: "firmware-version"
                },
                {
                    dataIndex: "output-voltage",
                    label: "output-voltage",
                    unit: "V",
                    getData: rs => rs?.["power-supply"]?.state?.["output-voltage"]
                },
                {
                    dataIndex: "part-no",
                    label: "PN"
                },
                {
                    dataIndex: "mfg-date",
                    label: "production-date"
                },
                {
                    dataIndex: "software-version"
                },
                {
                    dataIndex: "powering-mode",
                    getData: rs => rs?.["power-supply"]?.state?.["used-power-supply-mode"]
                },
                {
                    dataIndex: "input-current",
                    unit: "mA",
                    getData: rs => rs?.["power-supply"]?.state?.["input-current"]
                },
                {
                    dataIndex: "output-current",
                    unit: "mA",
                    getData: rs => rs?.["power-supply"]?.state?.["output-current"]
                },
                {
                    dataIndex: "input-voltage",
                    unit: "V",
                    getData: rs => rs?.["power-supply"]?.state?.["input-voltage"]
                }
            ]
        },
        fan: {
            title: "FAN Info",
            columns: [
                {
                    dataIndex: "card-type",
                    getData: rs => rs?.state.name
                },
                {
                    dataIndex: "slot-num",
                    getData: () => name.split("-").pop()
                },
                {
                    dataIndex: "hardware-version"
                },
                {
                    dataIndex: "temperature",
                    unit: "°C",
                    getData: rs => rs?.state?.temperature?.instant
                },
                {
                    dataIndex: "serial-no",
                    label: "SN"
                },
                {
                    dataIndex: "mfg-name",
                    label: "manufacture"
                },
                {
                    dataIndex: "firmware-version"
                },
                {
                    dataIndex: "fan-mode",
                    getData: rs => rs?.fan?.state?.["fan-mode"]
                },
                {
                    dataIndex: "part-no",
                    label: "PN"
                },
                {
                    dataIndex: "mfg-date",
                    label: "production-date"
                },
                {
                    dataIndex: "software-version"
                },
                {
                    dataIndex: "fan-speed",
                    getData: rs => rs?.fan?.state?.["fan-speed"]
                }
            ],
            editConfig: {
                width: 600,
                columns: [
                    [
                        {
                            dataIndex: "fan-mode",
                            inputType: "select",
                            data: {options: ["AUTO", "MANUAL"]}
                        }
                    ],
                    [
                        {
                            dataIndex: "fan-speed",
                            inputType: "select",
                            data: {options: ["HIGH", "MIDDLE", "LOW"]}
                        }
                    ]
                ],
                getData: async () => {
                    return {
                        "fan-mode": neData?.fan?.config?.["fan-mode"] ?? NULL_VALUE,
                        "fan-speed": neData?.fan?.config?.["fan-speed"] ?? NULL_VALUE
                    };
                },
                saveFun: async diffValue => {
                    const rs = await apiEditRpc({
                        ne_id,
                        params: {
                            components: {
                                component: {
                                    name,
                                    fan: {
                                        config: diffValue
                                    }
                                }
                            }
                        }
                    });
                    if (rs.message === "SUCCESS") {
                        refresh?.();
                    }
                    return rs;
                }
            }
        },
        MCU: {
            title: "MCU Info",
            columns: [
                {
                    dataIndex: "card-type",
                    getData: rs => rs?.state.name
                },
                {
                    dataIndex: "slot-num",
                    getData: () => name.split("-").pop()
                },
                {
                    dataIndex: "hardware-version"
                },
                {
                    dataIndex: "temperature",
                    unit: "°C",
                    getData: rs => rs?.state?.temperature?.instant
                },
                {
                    dataIndex: "serial-no",
                    label: "SN"
                },
                {
                    dataIndex: "mfg-name",
                    label: "manufacture"
                },
                {
                    dataIndex: "firmware-version"
                },
                {
                    dataIndex: "part-no",
                    label: "PN"
                },
                {
                    dataIndex: "mfg-date",
                    label: "production-date"
                },
                {
                    dataIndex: "software-version"
                },
                {
                    dataIndex: "mcu-total-memory",
                    unit: "M",
                    getData: rs => rs?.mcu?.state?.["mcu-total-memory"]
                },
                {
                    dataIndex: "mcu-available-memory",
                    unit: "M",
                    getData: rs => rs?.mcu?.state?.["mcu-available-memory"]
                },
                {
                    dataIndex: "mcu-cpu-utilization",
                    unit: "M",
                    getData: rs => rs?.mcu?.state?.["mcu-cpu-utilization"]
                }
            ]
        },
        "D7000-AUX": {
            title: "AUX Info",
            columns: [
                {
                    dataIndex: "card-type",
                    getData: rs => rs?.state?.["actual-vendor-type"]
                },
                {
                    dataIndex: "slot-num",
                    getData: () => name.split("-").pop()
                },
                {
                    dataIndex: "hardware-version"
                },
                {
                    dataIndex: "temperature",
                    unit: "°C",
                    getData: rs => rs?.state?.temperature?.instant
                },
                {
                    dataIndex: "serial-no",
                    label: "SN"
                },
                {
                    dataIndex: "mfg-name",
                    label: "manufacture"
                },
                {
                    dataIndex: "firmware-version"
                },
                {
                    //
                },
                {
                    dataIndex: "part-no",
                    label: "PN"
                },
                {
                    dataIndex: "mfg-date",
                    label: "production-date"
                },
                {
                    dataIndex: "software-version"
                }
            ]
        },
        LINECARD: {
            columns: [
                {
                    dataIndex: "card-type",
                    getData: rs => rs?.state?.["actual-vendor-type"] ?? rs?.state?.["vendor-type-preconf"]
                },
                {
                    dataIndex: "slot-num",
                    getData: () => name.split("-").pop()
                },
                {
                    dataIndex: "hardware-version"
                },
                {
                    dataIndex: "temperature",
                    unit: "°C",
                    getData: rs => rs?.state?.temperature?.instant
                },
                {
                    dataIndex: "serial-no",
                    label: "SN"
                },
                {
                    dataIndex: "mfg-name",
                    label: "manufacture"
                },
                {
                    dataIndex: "firmware-version"
                },
                {
                    //
                },
                {
                    dataIndex: "part-no",
                    label: "PN"
                },
                {
                    dataIndex: "mfg-date",
                    label: "production-date"
                },
                {
                    dataIndex: "software-version"
                }
            ]
        },
        OA: {
            columns: [
                {
                    dataIndex: "card-type",
                    getData: rs => rs?.state?.["actual-vendor-type"] ?? rs?.state?.["vendor-type-preconf"]
                },
                {
                    dataIndex: "slot-num",
                    getData: () => name.split("-").pop()
                },
                {
                    dataIndex: "hardware-version"
                },
                {
                    dataIndex: "temperature",
                    unit: "°C",
                    getData: rs => rs?.state?.temperature?.instant
                },
                {
                    dataIndex: "serial-no",
                    label: "SN"
                },
                {
                    dataIndex: "mfg-name",
                    label: "manufacture"
                },
                {
                    dataIndex: "firmware-version"
                },
                {
                    //
                },
                {
                    dataIndex: "part-no",
                    label: "PN"
                },
                {
                    dataIndex: "mfg-date",
                    label: "production-date"
                },
                {
                    dataIndex: "software-version"
                }
            ],
            moreInfo: {
                columns: [
                    [
                        {dataIndex: "pa-max-output-power", unit: "dBm"},
                        {dataIndex: "pa-gain-range", unit: "db"}
                    ],
                    [
                        {dataIndex: "ba-max-output-power", unit: "dBm"},
                        {dataIndex: "ba-gain-range", unit: "db"}
                    ]
                ],
                getData: async () => {
                    const edfaName = `EDFA${name.substring(name.indexOf("-"))}`;
                    return {
                        "pa-max-output-power": getDeviceStateValue(stateData, `${edfaName}-PA`, "output-power"),
                        "ba-max-output-power": getDeviceStateValue(stateData, `${edfaName}-BA`, "output-power"),
                        "pa-gain-range": getDeviceStateValue(stateData, `${edfaName}-PA`, "target-gain"),
                        "ba-gain-range": getDeviceStateValue(stateData, `${edfaName}-BA`, "target-gain")
                    };
                }
            }
        },
        WSS: {
            columns: [
                {
                    dataIndex: "card-type",
                    getData: rs => rs?.state?.["actual-vendor-type"] ?? rs?.state?.["vendor-type-preconf"]
                },
                {
                    dataIndex: "slot-num",
                    getData: () => name.split("-").pop()
                },
                {
                    dataIndex: "hardware-version"
                },
                {
                    dataIndex: "temperature",
                    unit: "°C",
                    getData: rs => rs?.state?.temperature?.instant
                },
                {
                    dataIndex: "serial-no",
                    label: "SN"
                },
                {
                    dataIndex: "mfg-name",
                    label: "manufacture"
                },
                {
                    dataIndex: "firmware-version"
                },
                {
                    dataIndex: "degree",
                    getData: rs => rs?.wss?.state?.["max-degree"]
                },
                {
                    dataIndex: "part-no",
                    label: "PN"
                },
                {
                    dataIndex: "mfg-date",
                    label: "production-date"
                },
                {
                    dataIndex: "software-version"
                }
            ],
            moreInfo: {
                columns: [
                    [
                        {dataIndex: "pa-max-output-power", unit: "dBm"},
                        {dataIndex: "pa-gain-range", unit: "db"}
                    ],
                    [
                        {dataIndex: "ba-max-output-power", unit: "dBm"},
                        {dataIndex: "ba-gain-range", unit: "db"}
                    ]
                ],
                getData: async () => {
                    const edfaName = `EDFA${name.substring(name.indexOf("-"))}`;
                    return {
                        "pa-max-output-power": getDeviceStateValue(stateData, `${edfaName}-PA`, "output-power"),
                        "ba-max-output-power": getDeviceStateValue(stateData, `${edfaName}-BA`, "output-power"),
                        "pa-gain-range": getDeviceStateValue(stateData, `${edfaName}-PA`, "target-gain"),
                        "ba-gain-range": getDeviceStateValue(stateData, `${edfaName}-BA`, "target-gain")
                    };
                }
            }
        },
        TFF: {
            columns: [
                {
                    dataIndex: "card-type",
                    getData: rs => rs?.state?.["actual-vendor-type"] ?? rs?.state?.["vendor-type-preconf"]
                },
                {
                    dataIndex: "slot-num",
                    getData: () => name.split("-").pop()
                },
                {
                    dataIndex: "hardware-version"
                },
                {
                    dataIndex: "temperature",
                    unit: "°C",
                    getData: rs => rs?.state?.temperature?.instant
                },
                {
                    dataIndex: "serial-no",
                    label: "SN"
                },
                {
                    dataIndex: "mfg-name",
                    label: "manufacture"
                },
                {
                    dataIndex: "firmware-version"
                },
                {
                    dataIndex: "Channel Num",
                    getData: () => 4
                },
                {
                    dataIndex: "part-no",
                    label: "PN"
                },
                {
                    dataIndex: "mfg-date",
                    label: "production-date"
                },
                {
                    dataIndex: "software-version"
                },
                {
                    dataIndex: "channel-interval",
                    unit: "GHz",
                    getData: () => 100
                }
            ]
        },
        MUX: {
            columns: [
                {
                    dataIndex: "card-type",
                    getData: rs => rs?.state?.["actual-vendor-type"] ?? rs?.state?.["vendor-type-preconf"]
                },
                {
                    dataIndex: "slot-num",
                    getData: () => name.split("-").pop()
                },
                {
                    dataIndex: "hardware-version"
                },
                {
                    dataIndex: "temperature",
                    unit: "°C",
                    getData: rs => rs?.state?.temperature?.instant
                },
                {
                    dataIndex: "serial-no",
                    label: "SN"
                },
                {
                    dataIndex: "mfg-name",
                    label: "manufacture"
                },
                {
                    dataIndex: "firmware-version"
                },
                {
                    dataIndex: "Channel Num",
                    getData: () => 48
                },
                {
                    dataIndex: "part-no",
                    label: "PN"
                },
                {
                    dataIndex: "mfg-date",
                    label: "production-date"
                },
                {
                    dataIndex: "software-version"
                },
                {
                    dataIndex: "channel-interval",
                    unit: "Hz",
                    getData: () => 100
                }
            ]
        },
        OLP: {
            columns: [
                {
                    dataIndex: "card-type",
                    getData: rs => rs?.state?.["actual-vendor-type"] ?? rs?.state?.["vendor-type-preconf"]
                },
                {
                    dataIndex: "slot-num",
                    getData: () => name.split("-").pop()
                },
                {
                    dataIndex: "hardware-version"
                },
                {
                    dataIndex: "temperature",
                    unit: "°C",
                    getData: rs => rs?.state?.temperature?.instant
                },
                {
                    dataIndex: "serial-no",
                    label: "SN"
                },
                {
                    dataIndex: "mfg-name",
                    label: "manufacture"
                },
                {
                    dataIndex: "firmware-version"
                },
                {
                    //
                },
                {
                    dataIndex: "part-no",
                    label: "PN"
                },
                {
                    dataIndex: "mfg-date",
                    label: "production-date"
                },
                {
                    dataIndex: "software-version"
                }
            ]
        },
        OTDR: {
            columns: [
                {
                    dataIndex: "card-type",
                    getData: rs => rs?.state?.["actual-vendor-type"] ?? rs?.state?.["vendor-type-preconf"]
                },
                {
                    dataIndex: "slot-num",
                    getData: () => name.split("-").pop()
                },
                {
                    dataIndex: "hardware-version"
                },
                {
                    dataIndex: "temperature",
                    unit: "°C",
                    getData: rs => rs?.state?.temperature?.instant
                },
                {
                    dataIndex: "serial-no",
                    label: "SN"
                },
                {
                    dataIndex: "mfg-name",
                    label: "manufacture"
                },
                {
                    dataIndex: "firmware-version"
                },
                {
                    dataIndex: "Operational Wavelength",
                    unit: "nm",
                    render: () => {
                        return 1501;
                    }
                },
                {
                    dataIndex: "part-no",
                    label: "PN"
                },
                {
                    dataIndex: "mfg-date",
                    label: "production-date"
                },
                {
                    dataIndex: "software-version"
                },
                {
                    dataIndex: "Distance",
                    unit: "km",
                    render: () => {
                        return 80;
                    }
                }
            ]
        },
        OCM: {
            columns: [
                {
                    dataIndex: "card-type",
                    getData: rs => rs?.state?.["actual-vendor-type"] ?? rs?.state?.["vendor-type-preconf"]
                },
                {
                    dataIndex: "slot-num",
                    getData: () => name.split("-").pop()
                },
                {
                    dataIndex: "hardware-version"
                },
                {
                    dataIndex: "temperature",
                    unit: "°C",
                    getData: rs => rs?.state?.temperature?.instant
                },
                {
                    dataIndex: "serial-no",
                    label: "SN"
                },
                {
                    dataIndex: "mfg-name",
                    label: "manufacture"
                },
                {
                    dataIndex: "firmware-version"
                },
                {
                    //
                },
                {
                    dataIndex: "part-no",
                    label: "PN"
                },
                {
                    dataIndex: "mfg-date",
                    label: "production-date"
                },
                {
                    dataIndex: "software-version"
                }
            ]
        },
        OLA: {
            columns: [
                {
                    dataIndex: "card-type",
                    getData: rs => rs?.state?.["actual-vendor-type"] ?? rs?.state?.["vendor-type-preconf"]
                },
                {
                    dataIndex: "slot-num",
                    getData: () => name.split("-").pop()
                },
                {
                    dataIndex: "hardware-version"
                },
                {
                    dataIndex: "temperature",
                    unit: "°C",
                    getData: rs => rs?.state?.temperature?.instant
                },
                {
                    dataIndex: "serial-no",
                    label: "SN"
                },
                {
                    dataIndex: "mfg-name",
                    label: "manufacture"
                },
                {
                    dataIndex: "firmware-version"
                },
                {
                    //
                },
                {
                    dataIndex: "part-no",
                    label: "PN"
                },
                {
                    dataIndex: "mfg-date",
                    label: "production-date"
                },
                {
                    dataIndex: "software-version"
                }
            ],
            moreInfo: {
                columns: [
                    [
                        {dataIndex: "pa-max-output-power", unit: "dBm"},
                        {dataIndex: "pa-gain-range", unit: "db"}
                    ],
                    [
                        {dataIndex: "ba-max-output-power", unit: "dBm"},
                        {dataIndex: "ba-gain-range", unit: "db"}
                    ]
                ],
                getData: async () => {
                    const edfaName = `EDFA${name.substring(name.indexOf("-"))}`;
                    return {
                        "pa-max-output-power": getDeviceStateValue(stateData, `${edfaName}-PA`, "output-power"),
                        "ba-max-output-power": getDeviceStateValue(stateData, `${edfaName}-BA`, "output-power"),
                        "pa-gain-range": getDeviceStateValue(stateData, `${edfaName}-PA`, "target-gain"),
                        "ba-gain-range": getDeviceStateValue(stateData, `${edfaName}-BA`, "target-gain")
                    };
                }
            }
        },
        RAMAN_BOX: {
            title: "NE Info",
            columns: [
                {
                    dataIndex: "ne-type",
                    render: data => (data.state["ne-type"] === "0" ? "Co-RFA" : "RFA")
                },
                {
                    dataIndex: "manufacture"
                },
                // {
                //     dataIndex: "hardware-version",
                //     render: () => "V2.1.0"
                // },
                {
                    dataIndex: "commonDeviceFW",
                    label: "firmware-version",
                    render: data => data.state?.commonDeviceFW?.replace?.("Version", "")?.trim()
                },
                {
                    dataIndex: "commonDeviceInternalTemperature",
                    label: "temperature",
                    unit: "°C"
                },
                {
                    dataIndex: "commonDeviceSerialNumber",
                    label: "SN"
                },
                {
                    dataIndex: "channel-num"
                },
                {
                    dataIndex: "gain",
                    unit: "dB",
                    render: value => {
                        return (value.state.gain / 10).toFixed(1);
                    }
                },
                {
                    dataIndex: "commonDeviceMACAddress",
                    label: "mac-address"
                },
                {
                    dataIndex: "commonNEModelNumber",
                    label: "PN"
                },
                {
                    dataIndex: "commonNetworkAddress",
                    label: "ip-address"
                },
                {
                    dataIndex: "power-type"
                }
                // {
                //     dataIndex: "operate",
                //     render: () => {
                //         return (
                //             <div className={styles.card_renderButton} onClick={resetFactory}>
                //                 Reset To Factory
                //             </div>
                //         );
                //     }
                // }
            ],
            moreInfo: {
                columns: data => {
                    const dynamicPumpColumns = [];
                    for (let i = 1; i <= data["pump-num"]; i++) {
                        dynamicPumpColumns.push([
                            {dataIndex: `pump${i}-bais`, unit: "mA"},
                            {dataIndex: `pump${i}-tec`, unit: "mA"}
                        ]);
                        dynamicPumpColumns.push([
                            {dataIndex: `pump${i}-temp`, unit: "°C"},
                            {dataIndex: `pump${i}-power`, unit: "dBm"}
                        ]);
                    }
                    return [
                        {
                            group: "Power",
                            columns: [
                                [
                                    {dataIndex: "power1-voltage", unit: "V"},
                                    {dataIndex: "power2-voltage", unit: "V"}
                                ],
                                [
                                    {dataIndex: "power1-current", unit: "A"},
                                    {dataIndex: "power2-current", unit: "A"}
                                ]
                            ]
                        },
                        {
                            group: "Pump",
                            columns: [[{dataIndex: "pump-num"}], ...dynamicPumpColumns]
                        }
                    ];
                },
                getData: async () => {
                    const rs = await NEGet({
                        ne_id,
                        parameter: {ramanDCPowerEntry: {}, ramanPumpEntry: {}}
                    });
                    const data = {"pump-num": rs.ramanPumpEntry.length};
                    rs.ramanDCPowerEntry.forEach(dc => {
                        data[`power${dc.ramanDCPowerIndex}-voltage`] = (dc.ramanDCPowerVoltage / 10).toFixed(1);
                        data[`power${dc.ramanDCPowerIndex}-current`] = (dc.ramanDCPowerCurrent / 10).toFixed(1);
                    });
                    rs.ramanPumpEntry.forEach(pump => {
                        data[`pump${pump.ramanPumpIndex}-bais`] = (pump.ramanPumpBIAS / 10).toFixed(1);
                        data[`pump${pump.ramanPumpIndex}-tec`] = (pump.ramanPumpTEC / 10).toFixed(1);
                        data[`pump${pump.ramanPumpIndex}-temp`] = (pump.ramanPumpTemp / 10).toFixed(1);
                        data[`pump${pump.ramanPumpIndex}-power`] = (pump.ramanPumpPower / 10).toFixed(1);
                    });
                    return data;
                }
            }
        }
    };

    const showInfoConfig = SHOW_INFO_CONFIG[type] ?? {columns: []};
    let state = "Active";
    if (type !== "chassis") {
        if (neData?.state?.empty === "true") {
            state = "Absent";
        } else {
            state =
                neData?.state?.["actual-vendor-type"] === neData?.config?.["vendor-type-preconf"]
                    ? "Present"
                    : "Mismatch";
        }
    }

    return (
        <Card
            loading={!type}
            title={
                <div>
                    {showInfoConfig.title ?? "Card Info"}
                    <Tag
                        className={["Absent", "Mismatch"].includes(state) ? styles.card_tagColor : styles.card_tag}
                        style={{marginLeft: 16}}
                        icon={["Absent", "Mismatch"].includes(state) ? <CloseCircleFilled /> : <CheckCircleFilled />}
                    >
                        {type === "chassis" || type === "RAMAN_BOX" ? "Active" : state}
                    </Tag>
                </div>
            }
            className={styles.card}
            extra={
                showInfoConfig.editConfig &&
                !readyOnlyRight.disabled && (
                    <DebounceButton
                        containerType="Icon"
                        title="Edit"
                        component={editCommonIcon}
                        style={{cursor: "pointer", width: 28, alignSelf: "flex-start", paddingTop: 10}}
                        onClick={() => {
                            const columnNum = type === "fan" ? 1 : 2;
                            openCustomEditForm({
                                title: "Modify",
                                width: showInfoConfig.editConfig?.width ?? 900,
                                columns: showInfoConfig.editConfig?.columns ?? [],
                                getData: showInfoConfig.editConfig?.getData ?? {},
                                saveFun: showInfoConfig.editConfig?.saveFun,
                                columnNum
                            });
                        }}
                    />
                )
            }
        >
            <div style={{display: "flex"}}>
                <Flex wrap="wrap" style={{flex: 1}}>
                    {showInfoConfig.columns.map(i => {
                        const _v =
                            i?.render?.(neData) ?? i?.getData?.(neData) ?? neData?.state?.[i.dataIndex] ?? NULL_VALUE;
                        return i.dataIndex ? (
                            <div key={i.dataIndex} className={styles.item}>
                                <div className={styles.dot} />
                                <div
                                    className={styles.item_label}
                                >{`${getText(i.label ?? i.dataIndex) + (i.unit ? ` (${i.unit})` : "")}`}</div>
                                <span style={{padding: "0 8px 0 2px"}}>:</span>
                                <div className={styles.item_value} title={typeof _v === "string" ? _v : ""}>
                                    {_v}
                                </div>
                            </div>
                        ) : (
                            <div className={styles.item} />
                        );
                    })}
                </Flex>
                {showInfoConfig.moreInfo && (
                    <Tooltip placement="top" title="More">
                        <DebounceButton
                            containerType="Icon"
                            component={moreInfo}
                            style={{cursor: "pointer", width: 28, alignSelf: "flex-start", marginTop: 10}}
                            onClick={() => {
                                openDisplayDialog({
                                    title: "More",
                                    width: 900,
                                    columns: showInfoConfig.moreInfo?.columns ?? [],
                                    getData: showInfoConfig.moreInfo?.getData ?? {}
                                });
                            }}
                        />
                    </Tooltip>
                )}
            </div>
        </Card>
    );
};
export default CardInfo;
