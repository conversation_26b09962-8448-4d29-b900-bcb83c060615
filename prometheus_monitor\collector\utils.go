package collector

import (
	"bytes"
	"crypto/aes"
	"crypto/cipher"
	"crypto/tls"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"strconv"
	"strings"
	"time"

	"github.com/openconfig/gnmi/proto/gnmi"
	"github.com/prometheus/client_golang/prometheus"
)

var ValueEnumMap = map[string]float64{
	"true":                                 1,
	"false":                                0,
	"FULL":                                 1,
	"HALF":                                 0,
	"UP":                                   1,
	"DOWN":                                 0,
	"openconfig-if-ethernet:SPEED_UNKNOWN": 0,
	"openconfig-if-ethernet:SPEED_10MB":    10 * 1000 * 1000,
	"openconfig-if-ethernet:SPEED_100MB":   100 * 1000 * 1000,
	"openconfig-if-ethernet:SPEED_2500MB":  2500 * 1000 * 1000,
	"openconfig-if-ethernet:SPEED_1GB":     1 * 1000 * 1000 * 1000,
	"openconfig-if-ethernet:SPEED_5GB":     5 * 1000 * 1000 * 1000,
	"openconfig-if-ethernet:SPEED_10GB":    10 * 1000 * 1000 * 1000,
	"openconfig-if-ethernet:SPEED_25GB":    25 * 1000 * 1000 * 1000,
	"openconfig-if-ethernet:SPEED_40GB":    40 * 1000 * 1000 * 1000,
	"openconfig-if-ethernet:SPEED_50GB":    50 * 1000 * 1000 * 1000,
	"openconfig-if-ethernet:SPEED_100GB":   100 * 1000 * 1000 * 1000,
	"openconfig-if-ethernet:SPEED_200GB":   200 * 1000 * 1000 * 1000,
	"openconfig-if-ethernet:SPEED_400GB":   400 * 1000 * 1000 * 1000,
	"openconfig-if-ethernet:SPEED_600GB":   600 * 1000 * 1000 * 1000,
	"openconfig-if-ethernet:SPEED_800GB":   800 * 1000 * 1000 * 1000,
}

type APIResponse struct {
	Data   []CollectTarget `json:"data"`
	Status int             `json:"status"`
	Info   string          `json:"info"`
}

func updateToBaseMetric(ts time.Time, upd *gnmi.Update) BaseMetric {

	labels := make(map[string]string)
	pathName, pLabels := labelsFromGNMIPath(upd.GetPath())
	for k, v := range pLabels {
		// 过滤ae端口
		if k == "interface_name" && strings.HasPrefix(v, "ae") {
			// fmt.Println(k, v)
			return BaseMetric{}
		}
		// 统一标签
		if k == "component_name" || k == "interface_interface-id" || k == "interface-id" {
			k = "interface_name"
		}
		if vv, ok := labels[k]; ok {
			if v != vv {
				labels[fmt.Sprintf("%s_%s", pathName, k)] = v
			}
			continue
		}
		labels[k] = v
	}

	updateValue := upd.GetVal()
	jsonData := updateValue.GetJsonIetfVal()

	return BaseMetric{
		Name:      pathName,
		Label:     labels,
		Value:     string(jsonData),
		Timestamp: ts,
	}
}

func labelsFromGNMIPath(p *gnmi.Path) (string, map[string]string) {
	if p == nil {
		return "", nil
	}
	labels := make(map[string]string)
	sb := strings.Builder{}
	if p.Origin != "" {
		sb.WriteString(p.Origin)
		sb.WriteString(":")
	}
	for _, e := range p.GetElem() {
		if e.Name != "" {
			sb.WriteString("/")
			sb.WriteString(e.Name)
		}
		if e.Key != nil {
			for k, v := range e.Key {
				if e.Name == "" {
					labels[k] = v
					continue
				}
				elems := strings.Split(e.Name, ":")
				ksb := strings.Builder{}
				ksb.WriteString(elems[len(elems)-1])
				ksb.WriteString("_")
				ksb.WriteString(k)
				labels[ksb.String()] = v
			}
		}
	}
	if p.GetTarget() != "" {
		labels["target"] = p.GetTarget()
	}
	return sb.String(), labels
}

func valueToFloat(v string) (float64, error) {
	f, err := strconv.ParseFloat(v, 64)
	if err != nil {
		if val, ok := ValueEnumMap[v]; ok {
			return val, nil
		}
		return 0.0, fmt.Errorf("invalid Enum value: %s", v)
	}
	return f, err
}

func buildMetric(name, value, targetName string, labelmap map[string]string, timestamp time.Time) prometheus.Metric {
	newLabel := make(map[string]string)
	for key, value := range labelmap {
		newLabel[key] = value
	}
	newMetric := BaseMetric{
		Name:      name,
		Label:     newLabel,
		Value:     value,
		Timestamp: timestamp,
	}
	newMetric.AddLabel("target", targetName)
	metric := newMetric.BuildMetric()
	return metric
}

func callAPI(method string, url string, data interface{}) ([]byte, error) {
	var reqBody io.Reader
	if method == "POST" && data != nil {
		jsonData, err := json.Marshal(data)
		if err != nil {
			return nil, fmt.Errorf("error encoding JSON: %v", err)
		}
		reqBody = bytes.NewBuffer(jsonData)
	}

	req, err := http.NewRequest(method, url, reqBody)
	if err != nil {
		return nil, err
	}

	if method == "POST" {
		req.Header.Set("Content-Type", "application/json")
	}

	tr := &http.Transport{
		TLSClientConfig: &tls.Config{
			InsecureSkipVerify: true,
		},
	}
	client := &http.Client{Transport: tr}

	resp, err := client.Do(req)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	respBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}

	return respBody, nil
}

func aesDecrypt(ciphertext string) (password string, err error) {
	defer func() {
		if r := recover(); r != nil {
			err = fmt.Errorf("panic occurred: %v", r)
		}
	}()
	key := []byte("pica8pica8                      ")

	// 转为base64标准格式
	if len(ciphertext)%4 != 0 {
		ciphertext = ciphertext + strings.Repeat("=", 4-len(ciphertext)%4)
	}

	// base64解码
	base64data, err := base64.StdEncoding.DecodeString(ciphertext)
	if err != nil {
		fmt.Println("Error decoding base64:", err)
		return "", err
	}

	// 前16位为iv
	iv := base64data[:16]

	// 创建 AES 解密器
	block, err := aes.NewCipher(key)
	if err != nil {
		fmt.Println("Error creating AES cipher:", err)
		return "", err
	}

	// 创建 CBC 解密器
	mode := cipher.NewCBCDecrypter(block, iv)

	// 解密数据 16位之后
	encryptedData := base64data[16:]
	plaintext := make([]byte, len(encryptedData))
	mode.CryptBlocks(plaintext, encryptedData)

	// 去除填充
	padding := int(plaintext[len(plaintext)-1])
	plaintext = plaintext[:len(plaintext)-padding]

	// log.Println("Decrypted plaintext:", string(plaintext))
	return string(plaintext), nil
}

type Alert struct {
	Labels      map[string]string `json:"labels"`
	Annotations map[string]string `json:"annotations"`
}

type AlertLogData struct {
	Alerts []Alert `json:"alerts"`
}

func sendAlertLog(url string, alerts []Alert) error {
	alertLogData := AlertLogData{Alerts: alerts}
	_, err := callAPI("POST", url, alertLogData)
	if err != nil {
		return fmt.Errorf("failed to send alert log: %v", err)
	}
	return nil
}
