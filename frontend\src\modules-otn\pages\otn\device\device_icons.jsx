export const addObjectIcon = () => (
    <svg xmlns="http://www.w3.org/2000/svg" fill="none" version="1.1" width="24" height="24" viewBox="0 0 24 24">
        <g>
            <g>
                <rect
                    x="0.5"
                    y="0.5"
                    width="23"
                    height="23"
                    rx="1.5"
                    fillOpacity="0"
                    strokeOpacity="1"
                    stroke="#DCDCDC"
                    fill="none"
                    strokeWidth="1"
                />
            </g>
            <g>
                <g>
                    <path
                        d="M11.300184059143067,6L11.300184059143067,18Q11.300184059143067,18.0689,11.313634059143066,18.1366Q11.327085059143066,18.2042,11.353468059143067,18.267899999999997Q11.379852059143067,18.3316,11.418155059143066,18.3889Q11.456459059143066,18.446199999999997,11.505209059143066,18.494999999999997Q11.553960059143066,18.5437,11.611285059143066,18.582Q11.668610059143067,18.6203,11.732306059143067,18.6467Q11.796002059143067,18.673099999999998,11.863621059143066,18.686500000000002Q11.931240059143066,18.7,12.000184059143066,18.7Q12.069128059143067,18.7,12.136747059143067,18.686500000000002Q12.204366059143066,18.673099999999998,12.268062059143066,18.6467Q12.331758059143066,18.6203,12.389083059143067,18.582Q12.446408059143067,18.5437,12.495159059143067,18.494999999999997Q12.543909059143067,18.446199999999997,12.582213059143067,18.3889Q12.620516059143066,18.3316,12.646900059143066,18.267899999999997Q12.673283059143067,18.2042,12.686734059143067,18.1366Q12.700184059143066,18.0689,12.700184059143066,18L12.700184059143066,6Q12.700184059143066,5.931056,12.686734059143067,5.863437Q12.673283059143067,5.795818,12.646900059143066,5.732122Q12.620516059143066,5.668426,12.582213059143067,5.611101Q12.543909059143067,5.553776,12.495159059143067,5.505025Q12.446408059143067,5.456275,12.389083059143067,5.417971Q12.331758059143066,5.379668,12.268062059143066,5.353284Q12.204366059143066,5.326901,12.136747059143067,5.31345Q12.069128059143067,5.3,12.000184059143066,5.3Q11.931240059143066,5.3,11.863621059143066,5.31345Q11.796002059143067,5.326901,11.732306059143067,5.353284Q11.668610059143067,5.379668,11.611285059143066,5.417971Q11.553960059143066,5.456275,11.505209059143066,5.505025Q11.456459059143066,5.553776,11.418155059143066,5.611101Q11.379852059143067,5.668426,11.353468059143067,5.732122Q11.327085059143066,5.795818,11.313634059143066,5.863437Q11.300184059143067,5.931056,11.300184059143067,6Z"
                        fillRule="evenodd"
                        fill="#212519"
                        fillOpacity="1"
                    />
                </g>
                <g>
                    <path
                        d="M6,12.6996337890625L18,12.6996337890625Q18.0689,12.6996337890625,18.1366,12.6861837890625Q18.2042,12.6727327890625,18.267899999999997,12.6463497890625Q18.3316,12.6199657890625,18.3889,12.5816627890625Q18.446199999999997,12.5433587890625,18.494999999999997,12.4946087890625Q18.5437,12.4458577890625,18.582,12.3885327890625Q18.6203,12.3312077890625,18.6467,12.2675117890625Q18.673099999999998,12.2038157890625,18.686500000000002,12.1361967890625Q18.7,12.0685777890625,18.7,11.9996337890625Q18.7,11.9306897890625,18.686500000000002,11.8630707890625Q18.673099999999998,11.7954517890625,18.6467,11.7317557890625Q18.6203,11.6680597890625,18.582,11.6107347890625Q18.5437,11.5534097890625,18.494999999999997,11.5046587890625Q18.446199999999997,11.4559087890625,18.3889,11.4176047890625Q18.3316,11.3793017890625,18.267899999999997,11.3529177890625Q18.2042,11.3265347890625,18.1366,11.3130837890625Q18.0689,11.2996337890625,18,11.2996337890625L6,11.2996337890625Q5.931056,11.2996337890625,5.863437,11.3130837890625Q5.795818,11.3265347890625,5.732122,11.3529177890625Q5.668426,11.3793017890625,5.611101,11.4176047890625Q5.553776,11.4559087890625,5.505025,11.5046587890625Q5.456275,11.5534097890625,5.417971,11.6107347890625Q5.379668,11.6680597890625,5.353284,11.7317557890625Q5.326901,11.7954517890625,5.31345,11.8630707890625Q5.3,11.9306897890625,5.3,11.9996337890625Q5.3,12.0685777890625,5.31345,12.1361967890625Q5.326901,12.2038157890625,5.353284,12.2675117890625Q5.379668,12.3312077890625,5.417971,12.3885327890625Q5.456275,12.4458577890625,5.505025,12.4946087890625Q5.553776,12.5433587890625,5.611101,12.5816627890625Q5.668426,12.6199657890625,5.732122,12.6463497890625Q5.795818,12.6727327890625,5.863437,12.6861837890625Q5.931056,12.6996337890625,6,12.6996337890625Z"
                        fillRule="evenodd"
                        fill="#212519"
                        fillOpacity="1"
                    />
                </g>
            </g>
        </g>
    </svg>
);

export const addObjectFocusIcon = () => (
    <svg xmlns="http://www.w3.org/2000/svg" fill="none" version="1.1" width="24" height="24" viewBox="0 0 24 24">
        <g>
            <g>
                <rect
                    x="0.5"
                    y="0.5"
                    width="23"
                    height="23"
                    rx="1.5"
                    fillOpacity="0"
                    strokeOpacity="1"
                    stroke="#14C9BB"
                    fill="none"
                    strokeWidth="1"
                />
            </g>
            <g>
                <g>
                    <path
                        d="M11.300184059143067,6L11.300184059143067,18Q11.300184059143067,18.0689,11.313634059143066,18.1366Q11.327085059143066,18.2042,11.353468059143067,18.267899999999997Q11.379852059143067,18.3316,11.418155059143066,18.3889Q11.456459059143066,18.446199999999997,11.505209059143066,18.494999999999997Q11.553960059143066,18.5437,11.611285059143066,18.582Q11.668610059143067,18.6203,11.732306059143067,18.6467Q11.796002059143067,18.673099999999998,11.863621059143066,18.686500000000002Q11.931240059143066,18.7,12.000184059143066,18.7Q12.069128059143067,18.7,12.136747059143067,18.686500000000002Q12.204366059143066,18.673099999999998,12.268062059143066,18.6467Q12.331758059143066,18.6203,12.389083059143067,18.582Q12.446408059143067,18.5437,12.495159059143067,18.494999999999997Q12.543909059143067,18.446199999999997,12.582213059143067,18.3889Q12.620516059143066,18.3316,12.646900059143066,18.267899999999997Q12.673283059143067,18.2042,12.686734059143067,18.1366Q12.700184059143066,18.0689,12.700184059143066,18L12.700184059143066,6Q12.700184059143066,5.931056,12.686734059143067,5.863437Q12.673283059143067,5.795818,12.646900059143066,5.732122Q12.620516059143066,5.668426,12.582213059143067,5.611101Q12.543909059143067,5.553776,12.495159059143067,5.505025Q12.446408059143067,5.456275,12.389083059143067,5.417971Q12.331758059143066,5.379668,12.268062059143066,5.353284Q12.204366059143066,5.326901,12.136747059143067,5.31345Q12.069128059143067,5.3,12.000184059143066,5.3Q11.931240059143066,5.3,11.863621059143066,5.31345Q11.796002059143067,5.326901,11.732306059143067,5.353284Q11.668610059143067,5.379668,11.611285059143066,5.417971Q11.553960059143066,5.456275,11.505209059143066,5.505025Q11.456459059143066,5.553776,11.418155059143066,5.611101Q11.379852059143067,5.668426,11.353468059143067,5.732122Q11.327085059143066,5.795818,11.313634059143066,5.863437Q11.300184059143067,5.931056,11.300184059143067,6Z"
                        fillRule="evenodd"
                        fill="#14C9BB"
                        fillOpacity="1"
                    />
                </g>
                <g>
                    <path
                        d="M6,12.6996337890625L18,12.6996337890625Q18.0689,12.6996337890625,18.1366,12.6861837890625Q18.2042,12.6727327890625,18.267899999999997,12.6463497890625Q18.3316,12.6199657890625,18.3889,12.5816627890625Q18.446199999999997,12.5433587890625,18.494999999999997,12.4946087890625Q18.5437,12.4458577890625,18.582,12.3885327890625Q18.6203,12.3312077890625,18.6467,12.2675117890625Q18.673099999999998,12.2038157890625,18.686500000000002,12.1361967890625Q18.7,12.0685777890625,18.7,11.9996337890625Q18.7,11.9306897890625,18.686500000000002,11.8630707890625Q18.673099999999998,11.7954517890625,18.6467,11.7317557890625Q18.6203,11.6680597890625,18.582,11.6107347890625Q18.5437,11.5534097890625,18.494999999999997,11.5046587890625Q18.446199999999997,11.4559087890625,18.3889,11.4176047890625Q18.3316,11.3793017890625,18.267899999999997,11.3529177890625Q18.2042,11.3265347890625,18.1366,11.3130837890625Q18.0689,11.2996337890625,18,11.2996337890625L6,11.2996337890625Q5.931056,11.2996337890625,5.863437,11.3130837890625Q5.795818,11.3265347890625,5.732122,11.3529177890625Q5.668426,11.3793017890625,5.611101,11.4176047890625Q5.553776,11.4559087890625,5.505025,11.5046587890625Q5.456275,11.5534097890625,5.417971,11.6107347890625Q5.379668,11.6680597890625,5.353284,11.7317557890625Q5.326901,11.7954517890625,5.31345,11.8630707890625Q5.3,11.9306897890625,5.3,11.9996337890625Q5.3,12.0685777890625,5.31345,12.1361967890625Q5.326901,12.2038157890625,5.353284,12.2675117890625Q5.379668,12.3312077890625,5.417971,12.3885327890625Q5.456275,12.4458577890625,5.505025,12.4946087890625Q5.553776,12.5433587890625,5.611101,12.5816627890625Q5.668426,12.6199657890625,5.732122,12.6463497890625Q5.795818,12.6727327890625,5.863437,12.6861837890625Q5.931056,12.6996337890625,6,12.6996337890625Z"
                        fillRule="evenodd"
                        fill="#14C9BB"
                        fillOpacity="1"
                    />
                </g>
            </g>
        </g>
    </svg>
);

export const addObjectDisabledIcon = () => (
    <svg xmlns="http://www.w3.org/2000/svg" fill="none" version="1.1" width="24" height="24" viewBox="0 0 24 24">
        <g>
            <g>
                <rect x="0" y="0" width="24" height="24" rx="2" fill="#F4F5F7" fillOpacity="1" />
                <rect
                    x="0.5"
                    y="0.5"
                    width="23"
                    height="23"
                    rx="1.5"
                    fillOpacity="0"
                    strokeOpacity="1"
                    stroke="#DADCE1"
                    fill="none"
                    strokeWidth="1"
                />
            </g>
            <g>
                <g>
                    <path
                        d="M11.300184059143067,6L11.300184059143067,18Q11.300184059143067,18.0689,11.313634059143066,18.1366Q11.327085059143066,18.2042,11.353468059143067,18.267899999999997Q11.379852059143067,18.3316,11.418155059143066,18.3889Q11.456459059143066,18.446199999999997,11.505209059143066,18.494999999999997Q11.553960059143066,18.5437,11.611285059143066,18.582Q11.668610059143067,18.6203,11.732306059143067,18.6467Q11.796002059143067,18.673099999999998,11.863621059143066,18.686500000000002Q11.931240059143066,18.7,12.000184059143066,18.7Q12.069128059143067,18.7,12.136747059143067,18.686500000000002Q12.204366059143066,18.673099999999998,12.268062059143066,18.6467Q12.331758059143066,18.6203,12.389083059143067,18.582Q12.446408059143067,18.5437,12.495159059143067,18.494999999999997Q12.543909059143067,18.446199999999997,12.582213059143067,18.3889Q12.620516059143066,18.3316,12.646900059143066,18.267899999999997Q12.673283059143067,18.2042,12.686734059143067,18.1366Q12.700184059143066,18.0689,12.700184059143066,18L12.700184059143066,6Q12.700184059143066,5.931056,12.686734059143067,5.863437Q12.673283059143067,5.795818,12.646900059143066,5.732122Q12.620516059143066,5.668426,12.582213059143067,5.611101Q12.543909059143067,5.553776,12.495159059143067,5.505025Q12.446408059143067,5.456275,12.389083059143067,5.417971Q12.331758059143066,5.379668,12.268062059143066,5.353284Q12.204366059143066,5.326901,12.136747059143067,5.31345Q12.069128059143067,5.3,12.000184059143066,5.3Q11.931240059143066,5.3,11.863621059143066,5.31345Q11.796002059143067,5.326901,11.732306059143067,5.353284Q11.668610059143067,5.379668,11.611285059143066,5.417971Q11.553960059143066,5.456275,11.505209059143066,5.505025Q11.456459059143066,5.553776,11.418155059143066,5.611101Q11.379852059143067,5.668426,11.353468059143067,5.732122Q11.327085059143066,5.795818,11.313634059143066,5.863437Q11.300184059143067,5.931056,11.300184059143067,6Z"
                        fillRule="evenodd"
                        fill="#B3BBC8"
                        fillOpacity="1"
                    />
                </g>
                <g>
                    <path
                        d="M6,12.6996337890625L18,12.6996337890625Q18.0689,12.6996337890625,18.1366,12.6861837890625Q18.2042,12.6727327890625,18.267899999999997,12.6463497890625Q18.3316,12.6199657890625,18.3889,12.5816627890625Q18.446199999999997,12.5433587890625,18.494999999999997,12.4946087890625Q18.5437,12.4458577890625,18.582,12.3885327890625Q18.6203,12.3312077890625,18.6467,12.2675117890625Q18.673099999999998,12.2038157890625,18.686500000000002,12.1361967890625Q18.7,12.0685777890625,18.7,11.9996337890625Q18.7,11.9306897890625,18.686500000000002,11.8630707890625Q18.673099999999998,11.7954517890625,18.6467,11.7317557890625Q18.6203,11.6680597890625,18.582,11.6107347890625Q18.5437,11.5534097890625,18.494999999999997,11.5046587890625Q18.446199999999997,11.4559087890625,18.3889,11.4176047890625Q18.3316,11.3793017890625,18.267899999999997,11.3529177890625Q18.2042,11.3265347890625,18.1366,11.3130837890625Q18.0689,11.2996337890625,18,11.2996337890625L6,11.2996337890625Q5.931056,11.2996337890625,5.863437,11.3130837890625Q5.795818,11.3265347890625,5.732122,11.3529177890625Q5.668426,11.3793017890625,5.611101,11.4176047890625Q5.553776,11.4559087890625,5.505025,11.5046587890625Q5.456275,11.5534097890625,5.417971,11.6107347890625Q5.379668,11.6680597890625,5.353284,11.7317557890625Q5.326901,11.7954517890625,5.31345,11.8630707890625Q5.3,11.9306897890625,5.3,11.9996337890625Q5.3,12.0685777890625,5.31345,12.1361967890625Q5.326901,12.2038157890625,5.353284,12.2675117890625Q5.379668,12.3312077890625,5.417971,12.3885327890625Q5.456275,12.4458577890625,5.505025,12.4946087890625Q5.553776,12.5433587890625,5.611101,12.5816627890625Q5.668426,12.6199657890625,5.732122,12.6463497890625Q5.795818,12.6727327890625,5.863437,12.6861837890625Q5.931056,12.6996337890625,6,12.6996337890625Z"
                        fillRule="evenodd"
                        fill="#B3BBC8"
                        fillOpacity="1"
                    />
                </g>
            </g>
        </g>
    </svg>
);

export const deleteObjectIcon = () => (
    <svg xmlns="http://www.w3.org/2000/svg" fill="none" version="1.1" width="24" height="24" viewBox="0 0 24 24">
        <g>
            <g>
                <rect
                    x="0.5"
                    y="0.5"
                    width="23"
                    height="23"
                    rx="1.5"
                    fillOpacity="0"
                    strokeOpacity="1"
                    stroke="#DCDCDC"
                    fill="none"
                    strokeWidth="1"
                />
            </g>
            <g>
                <path
                    d="M13.28567,15.42855C13.048680000000001,15.42855,12.85716,15.23701,12.85716,15.00004L12.85716,10.285689999999999C12.85716,10.04871,13.048680000000001,9.85709,13.28567,9.85709C13.522649999999999,9.85709,13.714269999999999,10.04872,13.714269999999999,10.285689999999999L13.714269999999999,15C13.714269999999999,15.23701,13.52267,15.42853,13.28567,15.42855L13.28567,15.42855ZM10.71429,15.42855C10.477229999999999,15.42855,10.285689999999999,15.23701,10.285689999999999,15.00004L10.285689999999999,10.285689999999999C10.285689999999999,10.04871,10.477229999999999,9.85709,10.71429,9.85709C10.95128,9.85709,11.142800000000001,10.04872,11.142800000000001,10.285689999999999L11.142800000000001,15C11.142800000000001,15.23701,10.95128,15.42853,10.71429,15.42855L10.71429,15.42855ZM17.5714,8.14287L15.42852,8.14287L15.42852,7.2857199999999995C15.42852,6.576841,14.85638,6.000000308225,14.15267,6.000000308225L9.85708,6.000000308225C9.14828,6.000000308225,8.57143,6.576854,8.57143,7.2857199999999995L8.57143,8.14287L6.4286,8.14287C6.191535,8.14287,6,8.334389999999999,6,8.57145C6,8.808440000000001,6.191535,8.99996,6.4286,8.99996L17.5714,8.99996C17.8084,8.99996,18,8.808440000000001,18,8.57145C18,8.334389999999999,17.8084,8.14285,17.5714,8.14285L17.5714,8.14287ZM9.428560000000001,7.2857199999999995C9.428560000000001,7.04957,9.62096,6.857134,9.85708,6.857134L14.15268,6.857134C14.3876,6.857134,14.57141,7.04529,14.57141,7.2857199999999995L14.57141,8.14287L9.428560000000001,8.14287L9.428560000000001,7.2857199999999995ZM15.0017,18L9.00081,18C8.292,18,7.71516,17.423099999999998,7.71516,16.7143L7.71516,10.2793C7.71516,10.0427,7.90715,9.85071,8.14369,9.85071C8.3803,9.85071,8.57228,10.0427,8.57228,10.2793L8.57228,16.7143C8.57228,16.9509,8.76469,17.142899999999997,9.000820000000001,17.142899999999997L15.0017,17.142899999999997C15.23824,17.142899999999997,15.43024,16.9509,15.43024,16.7143L15.43024,10.29683C15.43024,10.06029,15.62185,9.86828,15.85883,9.86828C16.0958,9.86828,16.287399999999998,10.06029,16.287399999999998,10.29683L16.287399999999998,16.7143C16.287399999999998,17.4232,15.71051,18,15.0017,18Z"
                    fill="#212519"
                    fillOpacity="1"
                />
                <path
                    d="M17.980600000000003,8.16218Q17.8113,7.99285,17.5714,7.99285L17.5714,8.29285Q17.85,8.29285,17.85,8.57145Q17.85,8.68704,17.7685,8.76849Q17.686999999999998,8.84996,17.5714,8.84996L6.4286,8.84996Q6.31293,8.84996,6.231457,8.7685Q6.15,8.687059999999999,6.15,8.57145Q6.15,8.29287,6.4286,8.29287L8.72143,8.29287L8.72143,7.2857199999999995Q8.72143,6.816206,9.05452,6.4831Q9.38761,6.150001,9.85708,6.150001L14.15267,6.15Q14.61793,6.15,14.948,6.482679Q15.27852,6.815801,15.27852,7.2857199999999995L15.27852,8.29287L17.5714,8.29287L17.5714,7.99287L15.57852,7.99287L15.57852,7.2857199999999995Q15.57852,6.692226,15.16097,6.271382Q14.74288,5.85,14.15267,5.85L9.85708,5.850001Q9.26334,5.850001,8.84239,6.270971Q8.42143,6.691946,8.42143,7.2857199999999995L8.42143,7.99287L6.4286,7.99287Q6.18867,7.99287,6.0193369,8.162189999999999Q5.85,8.33152,5.85,8.57145Q5.85,8.81134,6.0193438,8.98065Q6.188682,9.14996,6.4286,9.14996L17.5714,9.14996Q17.8113,9.14996,17.980600000000003,8.98066Q18.15,8.81133,18.15,8.57145Q18.15,8.33153,17.980600000000003,8.16218ZM9.27856,7.2857199999999995L9.27856,8.29287L14.72141,8.29287L14.72141,7.2857199999999995Q14.72141,7.0442800000000005,14.55743,6.876441Q14.39202,6.707134,14.15268,6.707134L9.85708,6.707134Q9.61785,6.707134,9.4482,6.8768139999999995Q9.27856,7.04648,9.27856,7.2857199999999995ZM9.57856,7.99287L14.42141,7.99287L14.42141,7.2857199999999995Q14.42141,7.00713,14.15268,7.00713L9.85708,7.00713Q9.57856,7.00713,9.57856,7.2857199999999995L9.57856,7.99287ZM16.0164,17.729Q16.4374,17.3081,16.4374,16.7143L16.4374,10.29683Q16.4374,10.05724,16.2681,9.88782Q16.0987,9.71828,15.85883,9.71828Q15.61901,9.71828,15.44957,9.88782Q15.28024,10.05725,15.28024,10.29683L15.28024,16.7143Q15.28024,16.9929,15.0017,16.9929L9.000820000000001,16.9929Q8.72228,16.9929,8.72228,16.7143L8.72228,10.2793Q8.72228,10.039719999999999,8.55278,9.87022Q8.38327,9.70071,8.14369,9.70071Q7.90414,9.70071,7.73465,9.87022Q7.5651600000000006,10.039729999999999,7.5651600000000006,10.2793L7.56515,16.7143Q7.5651600000000006,17.3081,7.98611,17.729Q8.40706,18.15,9.00081,18.15L15.0017,18.15Q15.59545,18.15,16.0164,17.729ZM11.12349,15.40921Q11.2928,15.23988,11.2928,15L11.2928,10.285689999999999Q11.2928,10.04584,11.1235,9.87648Q10.954170000000001,9.70709,10.71429,9.70709Q10.47437,9.70709,10.305019999999999,9.87647Q10.13569,10.045819999999999,10.13569,10.285689999999999L10.13569,15.00004Q10.13569,15.23992,10.30504,15.40923Q10.47438,15.57855,10.7143,15.57855Q10.954170000000001,15.57853,11.12349,15.40921ZM13.6949,15.40923Q13.864270000000001,15.23989,13.864270000000001,15L13.864270000000001,10.285689999999999Q13.864270000000001,10.045829999999999,13.6949,9.87647Q13.52554,9.70709,13.28567,9.70709Q13.04579,9.70709,12.87646,9.87648Q12.70716,10.045829999999999,12.70716,10.285689999999999L12.70716,15.00004Q12.70716,15.2399,12.87648,15.40922Q13.0458,15.57855,13.28568,15.57855Q13.525549999999999,15.57853,13.6949,15.40923ZM16.1374,10.29683L16.1374,16.7143Q16.1374,17.183799999999998,15.80431,17.5169Q15.47119,17.85,15.0017,17.85L9.00081,17.85Q8.53133,17.85,8.19825,17.5169Q7.86515,17.183799999999998,7.86515,16.7143L7.8651599999999995,10.2793Q7.8651599999999995,10.163979999999999,7.94679,10.08234Q8.028410000000001,10.00071,8.14369,10.00071Q8.28298,10.00071,8.35263,10.07036Q8.42228,10.14,8.42228,10.2793L8.42228,16.7143Q8.42228,16.9538,8.59196,17.1234Q8.76154,17.2929,9.000820000000001,17.2929L15.0017,17.2929Q15.24124,17.2929,15.41074,17.1234Q15.58024,16.9539,15.58024,16.7143L15.58024,10.29683Q15.58024,10.01828,15.85883,10.01828Q16.1374,10.01828,16.1374,10.29683ZM10.992799999999999,10.285689999999999L10.992799999999999,15Q10.992799999999999,15.27853,10.71429,15.27855Q10.59863,15.27855,10.51715,15.19708Q10.435690000000001,15.11564,10.435690000000001,15.00004L10.435690000000001,10.285689999999999Q10.435690000000001,10.00709,10.71429,10.00709Q10.82987,10.00709,10.91133,10.08858Q10.992799999999999,10.170069999999999,10.992799999999999,10.285689999999999ZM13.56427,10.285689999999999L13.56427,15Q13.56427,15.27853,13.28566,15.27855Q13.007159999999999,15.27855,13.007159999999999,15.00004L13.007159999999999,10.285689999999999Q13.007159999999999,10.00709,13.28567,10.00709Q13.56427,10.00709,13.56427,10.285689999999999Z"
                    fillRule="evenodd"
                    fill="#212519"
                    fillOpacity="1"
                />
            </g>
        </g>
    </svg>
);

export const deleteObjectFocusIcon = () => (
    <svg xmlns="http://www.w3.org/2000/svg" fill="none" version="1.1" width="24" height="24" viewBox="0 0 24 24">
        <g>
            <g>
                <rect
                    x="0.5"
                    y="0.5"
                    width="23"
                    height="23"
                    rx="1.5"
                    fillOpacity="0"
                    strokeOpacity="1"
                    stroke="#14C9BB"
                    fill="none"
                    strokeWidth="1"
                />
            </g>
            <g>
                <path
                    d="M13.28567,15.42855C13.048680000000001,15.42855,12.85716,15.23701,12.85716,15.00004L12.85716,10.285689999999999C12.85716,10.04871,13.048680000000001,9.85709,13.28567,9.85709C13.522649999999999,9.85709,13.714269999999999,10.04872,13.714269999999999,10.285689999999999L13.714269999999999,15C13.714269999999999,15.23701,13.52267,15.42853,13.28567,15.42855L13.28567,15.42855ZM10.71429,15.42855C10.477229999999999,15.42855,10.285689999999999,15.23701,10.285689999999999,15.00004L10.285689999999999,10.285689999999999C10.285689999999999,10.04871,10.477229999999999,9.85709,10.71429,9.85709C10.95128,9.85709,11.142800000000001,10.04872,11.142800000000001,10.285689999999999L11.142800000000001,15C11.142800000000001,15.23701,10.95128,15.42853,10.71429,15.42855L10.71429,15.42855ZM17.5714,8.14287L15.42852,8.14287L15.42852,7.2857199999999995C15.42852,6.576841,14.85638,6.000000308225,14.15267,6.000000308225L9.85708,6.000000308225C9.14828,6.000000308225,8.57143,6.576854,8.57143,7.2857199999999995L8.57143,8.14287L6.4286,8.14287C6.191535,8.14287,6,8.334389999999999,6,8.57145C6,8.808440000000001,6.191535,8.99996,6.4286,8.99996L17.5714,8.99996C17.8084,8.99996,18,8.808440000000001,18,8.57145C18,8.334389999999999,17.8084,8.14285,17.5714,8.14285L17.5714,8.14287ZM9.428560000000001,7.2857199999999995C9.428560000000001,7.04957,9.62096,6.857134,9.85708,6.857134L14.15268,6.857134C14.3876,6.857134,14.57141,7.04529,14.57141,7.2857199999999995L14.57141,8.14287L9.428560000000001,8.14287L9.428560000000001,7.2857199999999995ZM15.0017,18L9.00081,18C8.292,18,7.71516,17.423099999999998,7.71516,16.7143L7.71516,10.2793C7.71516,10.0427,7.90715,9.85071,8.14369,9.85071C8.3803,9.85071,8.57228,10.0427,8.57228,10.2793L8.57228,16.7143C8.57228,16.9509,8.76469,17.142899999999997,9.000820000000001,17.142899999999997L15.0017,17.142899999999997C15.23824,17.142899999999997,15.43024,16.9509,15.43024,16.7143L15.43024,10.29683C15.43024,10.06029,15.62185,9.86828,15.85883,9.86828C16.0958,9.86828,16.287399999999998,10.06029,16.287399999999998,10.29683L16.287399999999998,16.7143C16.287399999999998,17.4232,15.71051,18,15.0017,18Z"
                    fill="#14C9BB"
                    fillOpacity="1"
                />
                <path
                    d="M17.980600000000003,8.16218Q17.8113,7.99285,17.5714,7.99285L17.5714,8.29285Q17.85,8.29285,17.85,8.57145Q17.85,8.68704,17.7685,8.76849Q17.686999999999998,8.84996,17.5714,8.84996L6.4286,8.84996Q6.31293,8.84996,6.231457,8.7685Q6.15,8.687059999999999,6.15,8.57145Q6.15,8.29287,6.4286,8.29287L8.72143,8.29287L8.72143,7.2857199999999995Q8.72143,6.816206,9.05452,6.4831Q9.38761,6.150001,9.85708,6.150001L14.15267,6.15Q14.61793,6.15,14.948,6.482679Q15.27852,6.815801,15.27852,7.2857199999999995L15.27852,8.29287L17.5714,8.29287L17.5714,7.99287L15.57852,7.99287L15.57852,7.2857199999999995Q15.57852,6.692226,15.16097,6.271382Q14.74288,5.85,14.15267,5.85L9.85708,5.850001Q9.26334,5.850001,8.84239,6.270971Q8.42143,6.691946,8.42143,7.2857199999999995L8.42143,7.99287L6.4286,7.99287Q6.18867,7.99287,6.0193369,8.162189999999999Q5.85,8.33152,5.85,8.57145Q5.85,8.81134,6.0193438,8.98065Q6.188682,9.14996,6.4286,9.14996L17.5714,9.14996Q17.8113,9.14996,17.980600000000003,8.98066Q18.15,8.81133,18.15,8.57145Q18.15,8.33153,17.980600000000003,8.16218ZM9.27856,7.2857199999999995L9.27856,8.29287L14.72141,8.29287L14.72141,7.2857199999999995Q14.72141,7.0442800000000005,14.55743,6.876441Q14.39202,6.707134,14.15268,6.707134L9.85708,6.707134Q9.61785,6.707134,9.4482,6.8768139999999995Q9.27856,7.04648,9.27856,7.2857199999999995ZM9.57856,7.99287L14.42141,7.99287L14.42141,7.2857199999999995Q14.42141,7.00713,14.15268,7.00713L9.85708,7.00713Q9.57856,7.00713,9.57856,7.2857199999999995L9.57856,7.99287ZM16.0164,17.729Q16.4374,17.3081,16.4374,16.7143L16.4374,10.29683Q16.4374,10.05724,16.2681,9.88782Q16.0987,9.71828,15.85883,9.71828Q15.61901,9.71828,15.44957,9.88782Q15.28024,10.05725,15.28024,10.29683L15.28024,16.7143Q15.28024,16.9929,15.0017,16.9929L9.000820000000001,16.9929Q8.72228,16.9929,8.72228,16.7143L8.72228,10.2793Q8.72228,10.039719999999999,8.55278,9.87022Q8.38327,9.70071,8.14369,9.70071Q7.90414,9.70071,7.73465,9.87022Q7.5651600000000006,10.039729999999999,7.5651600000000006,10.2793L7.56515,16.7143Q7.5651600000000006,17.3081,7.98611,17.729Q8.40706,18.15,9.00081,18.15L15.0017,18.15Q15.59545,18.15,16.0164,17.729ZM11.12349,15.40921Q11.2928,15.23988,11.2928,15L11.2928,10.285689999999999Q11.2928,10.04584,11.1235,9.87648Q10.954170000000001,9.70709,10.71429,9.70709Q10.47437,9.70709,10.305019999999999,9.87647Q10.13569,10.045819999999999,10.13569,10.285689999999999L10.13569,15.00004Q10.13569,15.23992,10.30504,15.40923Q10.47438,15.57855,10.7143,15.57855Q10.954170000000001,15.57853,11.12349,15.40921ZM13.6949,15.40923Q13.864270000000001,15.23989,13.864270000000001,15L13.864270000000001,10.285689999999999Q13.864270000000001,10.045829999999999,13.6949,9.87647Q13.52554,9.70709,13.28567,9.70709Q13.04579,9.70709,12.87646,9.87648Q12.70716,10.045829999999999,12.70716,10.285689999999999L12.70716,15.00004Q12.70716,15.2399,12.87648,15.40922Q13.0458,15.57855,13.28568,15.57855Q13.525549999999999,15.57853,13.6949,15.40923ZM16.1374,10.29683L16.1374,16.7143Q16.1374,17.183799999999998,15.80431,17.5169Q15.47119,17.85,15.0017,17.85L9.00081,17.85Q8.53133,17.85,8.19825,17.5169Q7.86515,17.183799999999998,7.86515,16.7143L7.8651599999999995,10.2793Q7.8651599999999995,10.163979999999999,7.94679,10.08234Q8.028410000000001,10.00071,8.14369,10.00071Q8.28298,10.00071,8.35263,10.07036Q8.42228,10.14,8.42228,10.2793L8.42228,16.7143Q8.42228,16.9538,8.59196,17.1234Q8.76154,17.2929,9.000820000000001,17.2929L15.0017,17.2929Q15.24124,17.2929,15.41074,17.1234Q15.58024,16.9539,15.58024,16.7143L15.58024,10.29683Q15.58024,10.01828,15.85883,10.01828Q16.1374,10.01828,16.1374,10.29683ZM10.992799999999999,10.285689999999999L10.992799999999999,15Q10.992799999999999,15.27853,10.71429,15.27855Q10.59863,15.27855,10.51715,15.19708Q10.435690000000001,15.11564,10.435690000000001,15.00004L10.435690000000001,10.285689999999999Q10.435690000000001,10.00709,10.71429,10.00709Q10.82987,10.00709,10.91133,10.08858Q10.992799999999999,10.170069999999999,10.992799999999999,10.285689999999999ZM13.56427,10.285689999999999L13.56427,15Q13.56427,15.27853,13.28566,15.27855Q13.007159999999999,15.27855,13.007159999999999,15.00004L13.007159999999999,10.285689999999999Q13.007159999999999,10.00709,13.28567,10.00709Q13.56427,10.00709,13.56427,10.285689999999999Z"
                    fillRule="evenodd"
                    fill="#14C9BB"
                    fillOpacity="1"
                />
            </g>
        </g>
    </svg>
);

export const deleteObjectDisabledIcon = () => (
    <svg xmlns="http://www.w3.org/2000/svg" fill="none" version="1.1" width="24" height="24" viewBox="0 0 24 24">
        <g>
            <g>
                <rect x="0" y="0" width="24" height="24" rx="2" fill="#F4F5F7" fillOpacity="1" />
                <rect
                    x="0.5"
                    y="0.5"
                    width="23"
                    height="23"
                    rx="1.5"
                    fillOpacity="0"
                    strokeOpacity="1"
                    stroke="#DADCE1"
                    fill="none"
                    strokeWidth="1"
                />
            </g>
            <g>
                <path
                    d="M13.28567,15.42855C13.048680000000001,15.42855,12.85716,15.23701,12.85716,15.00004L12.85716,10.285689999999999C12.85716,10.04871,13.048680000000001,9.85709,13.28567,9.85709C13.522649999999999,9.85709,13.714269999999999,10.04872,13.714269999999999,10.285689999999999L13.714269999999999,15C13.714269999999999,15.23701,13.52267,15.42853,13.28567,15.42855L13.28567,15.42855ZM10.71429,15.42855C10.477229999999999,15.42855,10.285689999999999,15.23701,10.285689999999999,15.00004L10.285689999999999,10.285689999999999C10.285689999999999,10.04871,10.477229999999999,9.85709,10.71429,9.85709C10.95128,9.85709,11.142800000000001,10.04872,11.142800000000001,10.285689999999999L11.142800000000001,15C11.142800000000001,15.23701,10.95128,15.42853,10.71429,15.42855L10.71429,15.42855ZM17.5714,8.14287L15.42852,8.14287L15.42852,7.2857199999999995C15.42852,6.576841,14.85638,6.000000308225,14.15267,6.000000308225L9.85708,6.000000308225C9.14828,6.000000308225,8.57143,6.576854,8.57143,7.2857199999999995L8.57143,8.14287L6.4286,8.14287C6.191535,8.14287,6,8.334389999999999,6,8.57145C6,8.808440000000001,6.191535,8.99996,6.4286,8.99996L17.5714,8.99996C17.8084,8.99996,18,8.808440000000001,18,8.57145C18,8.334389999999999,17.8084,8.14285,17.5714,8.14285L17.5714,8.14287ZM9.428560000000001,7.2857199999999995C9.428560000000001,7.04957,9.62096,6.857134,9.85708,6.857134L14.15268,6.857134C14.3876,6.857134,14.57141,7.04529,14.57141,7.2857199999999995L14.57141,8.14287L9.428560000000001,8.14287L9.428560000000001,7.2857199999999995ZM15.0017,18L9.00081,18C8.292,18,7.71516,17.423099999999998,7.71516,16.7143L7.71516,10.2793C7.71516,10.0427,7.90715,9.85071,8.14369,9.85071C8.3803,9.85071,8.57228,10.0427,8.57228,10.2793L8.57228,16.7143C8.57228,16.9509,8.76469,17.142899999999997,9.000820000000001,17.142899999999997L15.0017,17.142899999999997C15.23824,17.142899999999997,15.43024,16.9509,15.43024,16.7143L15.43024,10.29683C15.43024,10.06029,15.62185,9.86828,15.85883,9.86828C16.0958,9.86828,16.287399999999998,10.06029,16.287399999999998,10.29683L16.287399999999998,16.7143C16.287399999999998,17.4232,15.71051,18,15.0017,18Z"
                    fill="#B3BBC8"
                    fillOpacity="1"
                />
                <path
                    d="M17.980600000000003,8.16218Q17.8113,7.99285,17.5714,7.99285L17.5714,8.29285Q17.85,8.29285,17.85,8.57145Q17.85,8.68704,17.7685,8.76849Q17.686999999999998,8.84996,17.5714,8.84996L6.4286,8.84996Q6.31293,8.84996,6.231457,8.7685Q6.15,8.687059999999999,6.15,8.57145Q6.15,8.29287,6.4286,8.29287L8.72143,8.29287L8.72143,7.2857199999999995Q8.72143,6.816206,9.05452,6.4831Q9.38761,6.150001,9.85708,6.150001L14.15267,6.15Q14.61793,6.15,14.948,6.482679Q15.27852,6.815801,15.27852,7.2857199999999995L15.27852,8.29287L17.5714,8.29287L17.5714,7.99287L15.57852,7.99287L15.57852,7.2857199999999995Q15.57852,6.692226,15.16097,6.271382Q14.74288,5.85,14.15267,5.85L9.85708,5.850001Q9.26334,5.850001,8.84239,6.270971Q8.42143,6.691946,8.42143,7.2857199999999995L8.42143,7.99287L6.4286,7.99287Q6.18867,7.99287,6.0193369,8.162189999999999Q5.85,8.33152,5.85,8.57145Q5.85,8.81134,6.0193438,8.98065Q6.188682,9.14996,6.4286,9.14996L17.5714,9.14996Q17.8113,9.14996,17.980600000000003,8.98066Q18.15,8.81133,18.15,8.57145Q18.15,8.33153,17.980600000000003,8.16218ZM9.27856,7.2857199999999995L9.27856,8.29287L14.72141,8.29287L14.72141,7.2857199999999995Q14.72141,7.0442800000000005,14.55743,6.876441Q14.39202,6.707134,14.15268,6.707134L9.85708,6.707134Q9.61785,6.707134,9.4482,6.8768139999999995Q9.27856,7.04648,9.27856,7.2857199999999995ZM9.57856,7.99287L14.42141,7.99287L14.42141,7.2857199999999995Q14.42141,7.00713,14.15268,7.00713L9.85708,7.00713Q9.57856,7.00713,9.57856,7.2857199999999995L9.57856,7.99287ZM16.0164,17.729Q16.4374,17.3081,16.4374,16.7143L16.4374,10.29683Q16.4374,10.05724,16.2681,9.88782Q16.0987,9.71828,15.85883,9.71828Q15.61901,9.71828,15.44957,9.88782Q15.28024,10.05725,15.28024,10.29683L15.28024,16.7143Q15.28024,16.9929,15.0017,16.9929L9.000820000000001,16.9929Q8.72228,16.9929,8.72228,16.7143L8.72228,10.2793Q8.72228,10.039719999999999,8.55278,9.87022Q8.38327,9.70071,8.14369,9.70071Q7.90414,9.70071,7.73465,9.87022Q7.5651600000000006,10.039729999999999,7.5651600000000006,10.2793L7.56515,16.7143Q7.5651600000000006,17.3081,7.98611,17.729Q8.40706,18.15,9.00081,18.15L15.0017,18.15Q15.59545,18.15,16.0164,17.729ZM11.12349,15.40921Q11.2928,15.23988,11.2928,15L11.2928,10.285689999999999Q11.2928,10.04584,11.1235,9.87648Q10.954170000000001,9.70709,10.71429,9.70709Q10.47437,9.70709,10.305019999999999,9.87647Q10.13569,10.045819999999999,10.13569,10.285689999999999L10.13569,15.00004Q10.13569,15.23992,10.30504,15.40923Q10.47438,15.57855,10.7143,15.57855Q10.954170000000001,15.57853,11.12349,15.40921ZM13.6949,15.40923Q13.864270000000001,15.23989,13.864270000000001,15L13.864270000000001,10.285689999999999Q13.864270000000001,10.045829999999999,13.6949,9.87647Q13.52554,9.70709,13.28567,9.70709Q13.04579,9.70709,12.87646,9.87648Q12.70716,10.045829999999999,12.70716,10.285689999999999L12.70716,15.00004Q12.70716,15.2399,12.87648,15.40922Q13.0458,15.57855,13.28568,15.57855Q13.525549999999999,15.57853,13.6949,15.40923ZM16.1374,10.29683L16.1374,16.7143Q16.1374,17.183799999999998,15.80431,17.5169Q15.47119,17.85,15.0017,17.85L9.00081,17.85Q8.53133,17.85,8.19825,17.5169Q7.86515,17.183799999999998,7.86515,16.7143L7.8651599999999995,10.2793Q7.8651599999999995,10.163979999999999,7.94679,10.08234Q8.028410000000001,10.00071,8.14369,10.00071Q8.28298,10.00071,8.35263,10.07036Q8.42228,10.14,8.42228,10.2793L8.42228,16.7143Q8.42228,16.9538,8.59196,17.1234Q8.76154,17.2929,9.000820000000001,17.2929L15.0017,17.2929Q15.24124,17.2929,15.41074,17.1234Q15.58024,16.9539,15.58024,16.7143L15.58024,10.29683Q15.58024,10.01828,15.85883,10.01828Q16.1374,10.01828,16.1374,10.29683ZM10.992799999999999,10.285689999999999L10.992799999999999,15Q10.992799999999999,15.27853,10.71429,15.27855Q10.59863,15.27855,10.51715,15.19708Q10.435690000000001,15.11564,10.435690000000001,15.00004L10.435690000000001,10.285689999999999Q10.435690000000001,10.00709,10.71429,10.00709Q10.82987,10.00709,10.91133,10.08858Q10.992799999999999,10.170069999999999,10.992799999999999,10.285689999999999ZM13.56427,10.285689999999999L13.56427,15Q13.56427,15.27853,13.28566,15.27855Q13.007159999999999,15.27855,13.007159999999999,15.00004L13.007159999999999,10.285689999999999Q13.007159999999999,10.00709,13.28567,10.00709Q13.56427,10.00709,13.56427,10.285689999999999Z"
                    fillRule="evenodd"
                    fill="#B3BBC8"
                    fillOpacity="1"
                />
            </g>
        </g>
    </svg>
);

export const editNodeIcon = () => (
    <svg xmlns="http://www.w3.org/2000/svg" fill="none" version="1.1" width="24" height="24" viewBox="0 0 24 24">
        <g>
            <g>
                <rect
                    x="0.5"
                    y="0.5"
                    width="23"
                    height="23"
                    rx="1.5"
                    fillOpacity="0"
                    strokeOpacity="1"
                    stroke="#DCDCDC"
                    fill="none"
                    strokeWidth="1"
                />
            </g>
            <g>
                <g>
                    <path
                        d="M6.4,6.6L6.4,17.4Q6.4,17.897100000000002,6.751473,18.2485Q7.102945,18.6,7.6,18.6L16,18.6Q16.497059999999998,18.6,16.84853,18.2485Q17.2,17.897100000000002,17.2,17.4L17.2,9Q17.2,8.88883,17.1602,8.78504Q17.1203,8.68125,17.046,8.59862L14.34598,5.598621Q14.26064,5.5038,14.1441,5.4519Q14.02757,5.4,13.9,5.4L7.6,5.4Q7.102943,5.4,6.751472,5.751472Q6.4,6.102944,6.4,6.6ZM16,17.4L7.6,17.4L7.6,6.6L13.63278,6.6L16,9.23024L16,17.4Z"
                        fillRule="evenodd"
                        fill="#212519"
                        fillOpacity="1"
                    />
                </g>
                <g>
                    <path
                        d="M10.899999809265136,15.299852561950683L13.899999809265136,12.299852561950683L12.699999809265137,11.099852561950684L9.699999809265137,14.099852561950684L9.699999809265137,15.299852561950683L10.899999809265136,15.299852561950683Z"
                        fill="#F4F5F7"
                        fillOpacity="1"
                    />
                    <path
                        d="M9.099999809265137,14.099852561950684L9.099999809265137,15.299852561950683Q9.099999809265137,15.358952561950684,9.111528809265137,15.416902561950684Q9.123057809265136,15.474862561950683,9.145671809265137,15.529462561950684Q9.168286809265137,15.584062561950685,9.201117809265137,15.633192561950683Q9.233949809265138,15.682332561950684,9.275735809265136,15.724112561950683Q9.317521809265136,15.765902561950684,9.366657809265137,15.798732561950683Q9.415792809265136,15.831562561950683,9.470389809265138,15.854182561950683Q9.524985809265136,15.876792561950683,9.582945809265137,15.888322561950684Q9.640905009265136,15.899852561950684,9.699999809265137,15.899852561950684L10.899999809265136,15.899852561950684Q11.019349809265137,15.899852561950684,11.129609809265137,15.854182561950683Q11.239869809265137,15.808512561950684,11.324259809265136,15.724112561950683L14.324259809265136,12.724112561950683Q14.366049809265137,12.682332561950684,14.398879809265136,12.633192561950683Q14.431709809265136,12.584062561950685,14.454329809265136,12.529462561950684Q14.476939809265136,12.474862561950683,14.488469809265137,12.416902561950684Q14.499999809265137,12.358942561950684,14.499999809265137,12.299852561950683Q14.499999809265137,12.240762561950683,14.488469809265137,12.182802561950684Q14.476939809265136,12.124842561950684,14.454329809265136,12.070242561950684Q14.431709809265136,12.015645561950684,14.398879809265136,11.966510561950683Q14.366049809265137,11.917374561950684,14.324259809265136,11.875588561950684L13.124259809265137,10.675588561950683Q13.082479809265138,10.633802561950684,13.033339809265136,10.600970561950684Q12.984209809265137,10.568139561950684,12.929609809265138,10.545524561950684Q12.875009809265137,10.522910561950683,12.817049809265136,10.511381561950683Q12.759099809265138,10.499852561950684,12.699999809265137,10.499852561950684Q12.640909809265137,10.499852561950684,12.582949809265138,10.511381561950683Q12.524989809265136,10.522910561950683,12.470389809265136,10.545524561950684Q12.415789809265137,10.568139561950684,12.366659809265137,10.600970561950684Q12.317519809265136,10.633802561950684,12.275739809265136,10.675588561950683L9.275735809265136,13.675592561950683Q9.191344809265138,13.759982561950684,9.145671809265137,13.870242561950683Q9.099999809265137,13.980502561950683,9.099999809265137,14.099852561950684ZM10.299999809265136,14.699852561950683L10.651471809265137,14.699852561950683L13.051469809265136,12.299852561950683L12.699999809265137,11.948380561950684L10.299999809265136,14.348382561950684L10.299999809265136,14.699852561950683Z"
                        fillRule="evenodd"
                        fill="#212519"
                        fillOpacity="1"
                    />
                </g>
                <g>
                    <path
                        d="M13.599926471710205,9.5L16.599926471710205,9.5Q16.649176471710206,9.5,16.697466471710204,9.49039Q16.745766471710205,9.48078,16.791266471710205,9.46194Q16.836766471710206,9.44309,16.877706471710205,9.41573Q16.918656471710204,9.38837,16.953476471710204,9.35355Q16.988296471710207,9.31873,17.015656471710205,9.27778Q17.043016471710207,9.23684,17.061866471710204,9.19134Q17.080706471710204,9.14584,17.090316471710207,9.09754Q17.099926471710205,9.04925,17.099926471710205,9Q17.099926471710205,8.95075,17.090316471710207,8.90245Q17.080706471710204,8.85415,17.061866471710204,8.80866Q17.043016471710207,8.76316,17.015656471710205,8.72221Q16.988296471710207,8.68127,16.953476471710204,8.64645Q16.918656471710204,8.61162,16.877706471710205,8.58426Q16.836766471710206,8.55691,16.791266471710205,8.53806Q16.745766471710205,8.519210000000001,16.697466471710204,8.50961Q16.649176471710206,8.5,16.599926471710205,8.5L14.099926471710205,8.5L14.099926471710205,6Q14.099926471710205,5.9507543,14.090319471710204,5.9024549Q14.080711471710206,5.854155,14.061866471710205,5.808658Q14.043020471710205,5.763161,14.015661471710205,5.722215Q13.988301471710205,5.681269,13.953479471710205,5.646447Q13.918657471710205,5.611625,13.877711471710205,5.584265Q13.836765471710205,5.556906,13.791268471710206,5.53806Q13.745771471710205,5.519215,13.697471571710205,5.509607Q13.649172171710205,5.5,13.599926471710205,5.5Q13.550680771710205,5.5,13.502381371710205,5.509607Q13.454081471710206,5.519215,13.408584471710205,5.53806Q13.363087471710205,5.556906,13.322141471710205,5.584265Q13.281195471710205,5.611625,13.246373471710205,5.646447Q13.211551471710205,5.681269,13.184191471710205,5.722215Q13.156832471710205,5.763161,13.137986471710205,5.808658Q13.119141471710204,5.854155,13.109533471710206,5.9024549Q13.099926471710205,5.9507543,13.099926471710205,6L13.099926471710205,9Q13.099926471710205,9.04925,13.109533471710206,9.09754Q13.119141471710204,9.14584,13.137986471710205,9.19134Q13.156832471710205,9.23684,13.184191471710205,9.27778Q13.211551471710205,9.31873,13.246373471710205,9.35355Q13.281195471710205,9.38837,13.322141471710205,9.41573Q13.363087471710205,9.44309,13.408584471710205,9.46194Q13.454081471710206,9.48078,13.502381371710205,9.49039Q13.550680771710205,9.5,13.599926471710205,9.5Z"
                        fillRule="evenodd"
                        fill="#212519"
                        fillOpacity="1"
                    />
                </g>
            </g>
        </g>
    </svg>
);

export const editNodeFocusIcon = () => (
    <svg xmlns="http://www.w3.org/2000/svg" fill="none" version="1.1" width="24" height="24" viewBox="0 0 24 24">
        <g>
            <g>
                <rect
                    x="0.5"
                    y="0.5"
                    width="23"
                    height="23"
                    rx="1.5"
                    fillOpacity="0"
                    strokeOpacity="1"
                    stroke="#14C9BB"
                    fill="none"
                    strokeWidth="1"
                />
            </g>
            <g>
                <g>
                    <path
                        d="M6.4,6.6L6.4,17.4Q6.4,17.897100000000002,6.751473,18.2485Q7.102945,18.6,7.6,18.6L16,18.6Q16.497059999999998,18.6,16.84853,18.2485Q17.2,17.897100000000002,17.2,17.4L17.2,9Q17.2,8.88883,17.1602,8.78504Q17.1203,8.68125,17.046,8.59862L14.34598,5.598621Q14.26064,5.5038,14.1441,5.4519Q14.02757,5.4,13.9,5.4L7.6,5.4Q7.102943,5.4,6.751472,5.751472Q6.4,6.102944,6.4,6.6ZM16,17.4L7.6,17.4L7.6,6.6L13.63278,6.6L16,9.23024L16,17.4Z"
                        fillRule="evenodd"
                        fill="#14C9BB"
                        fillOpacity="1"
                    />
                </g>
                <g>
                    <path
                        d="M9.099999809265137,14.099852561950684L9.099999809265137,15.299852561950683Q9.099999809265137,15.358952561950684,9.111528809265137,15.416902561950684Q9.123057809265136,15.474862561950683,9.145671809265137,15.529462561950684Q9.168286809265137,15.584062561950685,9.201117809265137,15.633192561950683Q9.233949809265138,15.682332561950684,9.275735809265136,15.724112561950683Q9.317521809265136,15.765902561950684,9.366657809265137,15.798732561950683Q9.415792809265136,15.831562561950683,9.470389809265138,15.854182561950683Q9.524985809265136,15.876792561950683,9.582945809265137,15.888322561950684Q9.640905009265136,15.899852561950684,9.699999809265137,15.899852561950684L10.899999809265136,15.899852561950684Q11.019349809265137,15.899852561950684,11.129609809265137,15.854182561950683Q11.239869809265137,15.808512561950684,11.324259809265136,15.724112561950683L14.324259809265136,12.724112561950683Q14.366049809265137,12.682332561950684,14.398879809265136,12.633192561950683Q14.431709809265136,12.584062561950685,14.454329809265136,12.529462561950684Q14.476939809265136,12.474862561950683,14.488469809265137,12.416902561950684Q14.499999809265137,12.358942561950684,14.499999809265137,12.299852561950683Q14.499999809265137,12.240762561950683,14.488469809265137,12.182802561950684Q14.476939809265136,12.124842561950684,14.454329809265136,12.070242561950684Q14.431709809265136,12.015645561950684,14.398879809265136,11.966510561950683Q14.366049809265137,11.917374561950684,14.324259809265136,11.875588561950684L13.124259809265137,10.675588561950683Q13.082479809265138,10.633802561950684,13.033339809265136,10.600970561950684Q12.984209809265137,10.568139561950684,12.929609809265138,10.545524561950684Q12.875009809265137,10.522910561950683,12.817049809265136,10.511381561950683Q12.759099809265138,10.499852561950684,12.699999809265137,10.499852561950684Q12.640909809265137,10.499852561950684,12.582949809265138,10.511381561950683Q12.524989809265136,10.522910561950683,12.470389809265136,10.545524561950684Q12.415789809265137,10.568139561950684,12.366659809265137,10.600970561950684Q12.317519809265136,10.633802561950684,12.275739809265136,10.675588561950683L9.275735809265136,13.675592561950683Q9.191344809265138,13.759982561950684,9.145671809265137,13.870242561950683Q9.099999809265137,13.980502561950683,9.099999809265137,14.099852561950684ZM10.299999809265136,14.699852561950683L10.651471809265137,14.699852561950683L13.051469809265136,12.299852561950683L12.699999809265137,11.948380561950684L10.299999809265136,14.348382561950684L10.299999809265136,14.699852561950683Z"
                        fillRule="evenodd"
                        fill="#14C9BB"
                        fillOpacity="1"
                    />
                </g>
                <g>
                    <path
                        d="M13.599926471710205,9.5L16.599926471710205,9.5Q16.649176471710206,9.5,16.697466471710204,9.49039Q16.745766471710205,9.48078,16.791266471710205,9.46194Q16.836766471710206,9.44309,16.877706471710205,9.41573Q16.918656471710204,9.38837,16.953476471710204,9.35355Q16.988296471710207,9.31873,17.015656471710205,9.27778Q17.043016471710207,9.23684,17.061866471710204,9.19134Q17.080706471710204,9.14584,17.090316471710207,9.09754Q17.099926471710205,9.04925,17.099926471710205,9Q17.099926471710205,8.95075,17.090316471710207,8.90245Q17.080706471710204,8.85415,17.061866471710204,8.80866Q17.043016471710207,8.76316,17.015656471710205,8.72221Q16.988296471710207,8.68127,16.953476471710204,8.64645Q16.918656471710204,8.61162,16.877706471710205,8.58426Q16.836766471710206,8.55691,16.791266471710205,8.53806Q16.745766471710205,8.519210000000001,16.697466471710204,8.50961Q16.649176471710206,8.5,16.599926471710205,8.5L14.099926471710205,8.5L14.099926471710205,6Q14.099926471710205,5.9507543,14.090319471710204,5.9024549Q14.080711471710206,5.854155,14.061866471710205,5.808658Q14.043020471710205,5.763161,14.015661471710205,5.722215Q13.988301471710205,5.681269,13.953479471710205,5.646447Q13.918657471710205,5.611625,13.877711471710205,5.584265Q13.836765471710205,5.556906,13.791268471710206,5.53806Q13.745771471710205,5.519215,13.697471571710205,5.509607Q13.649172171710205,5.5,13.599926471710205,5.5Q13.550680771710205,5.5,13.502381371710205,5.509607Q13.454081471710206,5.519215,13.408584471710205,5.53806Q13.363087471710205,5.556906,13.322141471710205,5.584265Q13.281195471710205,5.611625,13.246373471710205,5.646447Q13.211551471710205,5.681269,13.184191471710205,5.722215Q13.156832471710205,5.763161,13.137986471710205,5.808658Q13.119141471710204,5.854155,13.109533471710206,5.9024549Q13.099926471710205,5.9507543,13.099926471710205,6L13.099926471710205,9Q13.099926471710205,9.04925,13.109533471710206,9.09754Q13.119141471710204,9.14584,13.137986471710205,9.19134Q13.156832471710205,9.23684,13.184191471710205,9.27778Q13.211551471710205,9.31873,13.246373471710205,9.35355Q13.281195471710205,9.38837,13.322141471710205,9.41573Q13.363087471710205,9.44309,13.408584471710205,9.46194Q13.454081471710206,9.48078,13.502381371710205,9.49039Q13.550680771710205,9.5,13.599926471710205,9.5Z"
                        fillRule="evenodd"
                        fill="#14C9BB"
                        fillOpacity="1"
                    />
                </g>
            </g>
        </g>
    </svg>
);

export const editNodeDisabledIcon = () => (
    <svg xmlns="http://www.w3.org/2000/svg" fill="none" version="1.1" width="24" height="24" viewBox="0 0 24 24">
        <g>
            <g>
                <rect x="0" y="0" width="24" height="24" rx="2" fill="#F4F5F7" fillOpacity="1" />
                <rect
                    x="0.5"
                    y="0.5"
                    width="23"
                    height="23"
                    rx="1.5"
                    fillOpacity="0"
                    strokeOpacity="1"
                    stroke="#DADCE1"
                    fill="none"
                    strokeWidth="1"
                />
            </g>
            <g>
                <g>
                    <path
                        d="M6.4,6.6L6.4,17.4Q6.4,17.897100000000002,6.751473,18.2485Q7.102945,18.6,7.6,18.6L16,18.6Q16.497059999999998,18.6,16.84853,18.2485Q17.2,17.897100000000002,17.2,17.4L17.2,9Q17.2,8.88883,17.1602,8.78504Q17.1203,8.68125,17.046,8.59862L14.34598,5.598621Q14.26064,5.5038,14.1441,5.4519Q14.02757,5.4,13.9,5.4L7.6,5.4Q7.102943,5.4,6.751472,5.751472Q6.4,6.102944,6.4,6.6ZM16,17.4L7.6,17.4L7.6,6.6L13.63278,6.6L16,9.23024L16,17.4Z"
                        fillRule="evenodd"
                        fill="#B3BBC8"
                        fillOpacity="1"
                    />
                </g>
                <g>
                    <path
                        d="M9.099999809265137,14.099852561950684L9.099999809265137,15.299852561950683Q9.099999809265137,15.358952561950684,9.111528809265137,15.416902561950684Q9.123057809265136,15.474862561950683,9.145671809265137,15.529462561950684Q9.168286809265137,15.584062561950685,9.201117809265137,15.633192561950683Q9.233949809265138,15.682332561950684,9.275735809265136,15.724112561950683Q9.317521809265136,15.765902561950684,9.366657809265137,15.798732561950683Q9.415792809265136,15.831562561950683,9.470389809265138,15.854182561950683Q9.524985809265136,15.876792561950683,9.582945809265137,15.888322561950684Q9.640905009265136,15.899852561950684,9.699999809265137,15.899852561950684L10.899999809265136,15.899852561950684Q11.019349809265137,15.899852561950684,11.129609809265137,15.854182561950683Q11.239869809265137,15.808512561950684,11.324259809265136,15.724112561950683L14.324259809265136,12.724112561950683Q14.366049809265137,12.682332561950684,14.398879809265136,12.633192561950683Q14.431709809265136,12.584062561950685,14.454329809265136,12.529462561950684Q14.476939809265136,12.474862561950683,14.488469809265137,12.416902561950684Q14.499999809265137,12.358942561950684,14.499999809265137,12.299852561950683Q14.499999809265137,12.240762561950683,14.488469809265137,12.182802561950684Q14.476939809265136,12.124842561950684,14.454329809265136,12.070242561950684Q14.431709809265136,12.015645561950684,14.398879809265136,11.966510561950683Q14.366049809265137,11.917374561950684,14.324259809265136,11.875588561950684L13.124259809265137,10.675588561950683Q13.082479809265138,10.633802561950684,13.033339809265136,10.600970561950684Q12.984209809265137,10.568139561950684,12.929609809265138,10.545524561950684Q12.875009809265137,10.522910561950683,12.817049809265136,10.511381561950683Q12.759099809265138,10.499852561950684,12.699999809265137,10.499852561950684Q12.640909809265137,10.499852561950684,12.582949809265138,10.511381561950683Q12.524989809265136,10.522910561950683,12.470389809265136,10.545524561950684Q12.415789809265137,10.568139561950684,12.366659809265137,10.600970561950684Q12.317519809265136,10.633802561950684,12.275739809265136,10.675588561950683L9.275735809265136,13.675592561950683Q9.191344809265138,13.759982561950684,9.145671809265137,13.870242561950683Q9.099999809265137,13.980502561950683,9.099999809265137,14.099852561950684ZM10.299999809265136,14.699852561950683L10.651471809265137,14.699852561950683L13.051469809265136,12.299852561950683L12.699999809265137,11.948380561950684L10.299999809265136,14.348382561950684L10.299999809265136,14.699852561950683Z"
                        fillRule="evenodd"
                        fill="#B3BBC8"
                        fillOpacity="1"
                    />
                </g>
                <g>
                    <path
                        d="M13.599926471710205,9.5L16.599926471710205,9.5Q16.649176471710206,9.5,16.697466471710204,9.49039Q16.745766471710205,9.48078,16.791266471710205,9.46194Q16.836766471710206,9.44309,16.877706471710205,9.41573Q16.918656471710204,9.38837,16.953476471710204,9.35355Q16.988296471710207,9.31873,17.015656471710205,9.27778Q17.043016471710207,9.23684,17.061866471710204,9.19134Q17.080706471710204,9.14584,17.090316471710207,9.09754Q17.099926471710205,9.04925,17.099926471710205,9Q17.099926471710205,8.95075,17.090316471710207,8.90245Q17.080706471710204,8.85415,17.061866471710204,8.80866Q17.043016471710207,8.76316,17.015656471710205,8.72221Q16.988296471710207,8.68127,16.953476471710204,8.64645Q16.918656471710204,8.61162,16.877706471710205,8.58426Q16.836766471710206,8.55691,16.791266471710205,8.53806Q16.745766471710205,8.519210000000001,16.697466471710204,8.50961Q16.649176471710206,8.5,16.599926471710205,8.5L14.099926471710205,8.5L14.099926471710205,6Q14.099926471710205,5.9507543,14.090319471710204,5.9024549Q14.080711471710206,5.854155,14.061866471710205,5.808658Q14.043020471710205,5.763161,14.015661471710205,5.722215Q13.988301471710205,5.681269,13.953479471710205,5.646447Q13.918657471710205,5.611625,13.877711471710205,5.584265Q13.836765471710205,5.556906,13.791268471710206,5.53806Q13.745771471710205,5.519215,13.697471571710205,5.509607Q13.649172171710205,5.5,13.599926471710205,5.5Q13.550680771710205,5.5,13.502381371710205,5.509607Q13.454081471710206,5.519215,13.408584471710205,5.53806Q13.363087471710205,5.556906,13.322141471710205,5.584265Q13.281195471710205,5.611625,13.246373471710205,5.646447Q13.211551471710205,5.681269,13.184191471710205,5.722215Q13.156832471710205,5.763161,13.137986471710205,5.808658Q13.119141471710204,5.854155,13.109533471710206,5.9024549Q13.099926471710205,5.9507543,13.099926471710205,6L13.099926471710205,9Q13.099926471710205,9.04925,13.109533471710206,9.09754Q13.119141471710204,9.14584,13.137986471710205,9.19134Q13.156832471710205,9.23684,13.184191471710205,9.27778Q13.211551471710205,9.31873,13.246373471710205,9.35355Q13.281195471710205,9.38837,13.322141471710205,9.41573Q13.363087471710205,9.44309,13.408584471710205,9.46194Q13.454081471710206,9.48078,13.502381371710205,9.49039Q13.550680771710205,9.5,13.599926471710205,9.5Z"
                        fillRule="evenodd"
                        fill="#B3BBC8"
                        fillOpacity="1"
                    />
                </g>
            </g>
        </g>
    </svg>
);

export const addNEIcon = () => (
    <svg xmlns="http://www.w3.org/2000/svg" fill="none" version="1.1" width="24" height="24" viewBox="0 0 24 24">
        <g>
            <g>
                <rect
                    x="0.5"
                    y="0.5"
                    width="23"
                    height="23"
                    rx="1.5"
                    fillOpacity="0"
                    strokeOpacity="1"
                    stroke="#DCDCDC"
                    fill="none"
                    strokeWidth="1"
                />
            </g>
            <g>
                <path
                    d="M11.25,14.81325L9.1875,14.81325L9.1875,12.75075C9.1875,12.54364,9.01961,12.37575,8.8125,12.37575C8.60539,12.37575,8.4375,12.54364,8.4375,12.75075L8.4375,14.81325L6.375,14.81325C6.167893,14.81325,6,14.98114,6,15.18825C6,15.39536,6.167893,15.56325,6.375,15.56325L8.4375,15.56325L8.4375,17.625C8.4375,17.8321,8.60539,18,8.8125,18C9.01961,18,9.1875,17.8321,9.1875,17.625L9.1875,15.56325L11.25,15.56325C11.45711,15.56325,11.625,15.39536,11.625,15.18825C11.625,14.98114,11.45711,14.81325,11.25,14.81325ZM11.1,6L6.525,6C6.23505,5.9999999654353,6,6.23505,6,6.525L6,11.1C6.000000276517,11.389949999999999,6.235051,11.625,6.525,11.625L11.1,11.625C11.389949999999999,11.625,11.625,11.389949999999999,11.625,11.1L11.625,6.525C11.625,6.23505,11.389949999999999,6,11.1,6ZM10.875,10.875L6.75,10.875L6.75,6.75L10.875,6.75L10.875,10.875ZM17.475,6L12.9,6C12.610050000000001,5.9999999654353,12.375,6.23505,12.375,6.525L12.375,11.1C12.375,11.389949999999999,12.610050000000001,11.625,12.9,11.625L17.475,11.625C17.7648,11.624590000000001,17.9996,11.38978,18,11.1L18,6.525C17.9996,6.235222,17.7648,6.000412878,17.475,6ZM17.25,10.875L13.125,10.875L13.125,6.75L17.25,6.75L17.25,10.875ZM17.475,12.375L12.9,12.375C12.610050000000001,12.375,12.375,12.610050000000001,12.375,12.9L12.375,17.475C12.375,17.7649,12.610050000000001,18,12.9,18L17.475,18C17.7648,17.9996,17.9996,17.7648,18,17.475L18,12.9C17.9996,12.61022,17.7648,12.375409999999999,17.475,12.375ZM17.25,17.25L13.125,17.25L13.125,13.125L17.25,13.125L17.25,17.25Z"
                    fill="#212529"
                    fillOpacity="1"
                />
                <path
                    d="M11.775,11.1L11.775,6.525Q11.775,6.245406,11.577300000000001,6.0477029Q11.37959,5.85,11.1,5.85L6.525,5.85Q6.245406,5.85,6.0477029,6.0477029Q5.85,6.245406,5.85,6.525L5.85,11.1Q5.85,11.37959,6.0477031,11.577300000000001Q6.245407,11.775,6.525,11.775L11.1,11.775Q11.3796,11.775,11.577300000000001,11.577300000000001Q11.775,11.37959,11.775,11.1ZM18.15,11.10021L18.15,6.524786Q18.1496,6.245447,17.9521,6.047922Q17.7546,5.850398,17.4752,5.85L12.9,5.85Q12.6204,5.85,12.422699999999999,6.0477029Q12.225,6.245406,12.225,6.525L12.225,11.1Q12.225,11.37959,12.422699999999999,11.577300000000001Q12.62041,11.775,12.9,11.775L17.475,11.775Q17.7546,11.7746,17.9521,11.577079999999999Q18.1496,11.37955,18.15,11.10021ZM17.85,6.525L17.85,11.099789999999999Q17.8495,11.47447,17.475,11.475L12.9,11.475Q12.525,11.475,12.525,11.1L12.525,6.525Q12.525,6.15,12.9,6.15L17.474800000000002,6.15Q17.8494,6.150533,17.85,6.525ZM11.1,6.15Q11.475,6.15,11.475,6.525L11.475,11.1Q11.475,11.475,11.1,11.475L6.525,11.475Q6.15,11.475,6.15,11.1L6.15,6.525Q6.15,6.36967,6.259835,6.259835Q6.36967,6.15,6.525,6.15L11.1,6.15ZM11.025,11.025L11.025,6.6L6.6,6.6L6.6,11.025L11.025,11.025ZM17.4,11.025L17.4,6.6L12.975,6.6L12.975,11.025L17.4,11.025ZM10.725,6.9L10.725,10.725L6.9,10.725L6.9,6.9L10.725,6.9ZM17.1,6.9L17.1,10.725L13.275,10.725L13.275,6.9L17.1,6.9ZM18.15,17.4752L18.15,12.89979Q18.1496,12.62045,17.9521,12.422920000000001Q17.7546,12.2254,17.4752,12.225L12.9,12.225Q12.6204,12.225,12.422699999999999,12.422699999999999Q12.225,12.6204,12.225,12.9L12.225,17.475Q12.225,17.7546,12.422699999999999,17.9523Q12.62041,18.15,12.9,18.15L17.475,18.15Q17.7546,18.1496,17.9521,17.9521Q18.1496,17.7546,18.15,17.4752ZM11.62123,15.55948Q11.775,15.40571,11.775,15.18825Q11.775,14.97079,11.62123,14.81702Q11.467459999999999,14.66325,11.25,14.66325L9.3375,14.66325L9.3375,12.75075Q9.3375,12.533290000000001,9.18373,12.37952Q9.029959999999999,12.22575,8.8125,12.22575Q8.595040000000001,12.22575,8.44127,12.37952Q8.2875,12.533290000000001,8.2875,12.75075L8.2875,14.66325L6.375,14.66325Q6.157538,14.66325,6.00376892,14.81702Q5.85,14.97079,5.85,15.18825Q5.85,15.40571,6.00376891,15.55948Q6.157538,15.71325,6.375,15.71325L8.2875,15.71325L8.2875,17.625Q8.2875,17.8425,8.44127,17.9962Q8.595040000000001,18.15,8.8125,18.15Q9.029959999999999,18.15,9.18373,17.9962Q9.3375,17.8425,9.3375,17.625L9.3375,15.71325L11.25,15.71325Q11.467459999999999,15.71325,11.62123,15.55948ZM17.85,12.9L17.85,17.474800000000002Q17.8495,17.8495,17.475,17.85L12.9,17.85Q12.74467,17.85,12.634830000000001,17.7402Q12.525,17.6303,12.525,17.475L12.525,12.9Q12.525,12.525,12.9,12.525L17.474800000000002,12.525Q17.8494,12.52553,17.85,12.9ZM11.409099999999999,15.02915Q11.475,15.09505,11.475,15.18825Q11.475,15.41325,11.25,15.41325L9.0375,15.41325L9.0375,17.625Q9.0375,17.85,8.8125,17.85Q8.5875,17.85,8.5875,17.625L8.5875,15.41325L6.375,15.41325Q6.281802,15.41325,6.215901,15.34735Q6.15,15.28145,6.15,15.18825Q6.15,15.09505,6.215901,15.02915Q6.281802,14.96325,6.375,14.96325L8.5875,14.96325L8.5875,12.75075Q8.5875,12.52575,8.8125,12.52575Q9.0375,12.52575,9.0375,12.75075L9.0375,14.96325L11.25,14.96325Q11.3432,14.96325,11.409099999999999,15.02915ZM17.4,17.4L17.4,12.975L12.975,12.975L12.975,17.4L17.4,17.4ZM17.1,13.275L17.1,17.1L13.275,17.1L13.275,13.275L17.1,13.275Z"
                    fillRule="evenodd"
                    fill="#212529"
                    fillOpacity="1"
                />
            </g>
        </g>
    </svg>
);

export const addNEFocusIcon = () => (
    <svg xmlns="http://www.w3.org/2000/svg" fill="none" version="1.1" width="24" height="24" viewBox="0 0 24 24">
        <g>
            <g>
                <rect
                    x="0.5"
                    y="0.5"
                    width="23"
                    height="23"
                    rx="1.5"
                    fillOpacity="0"
                    strokeOpacity="1"
                    stroke="#14C9BB"
                    fill="none"
                    strokeWidth="1"
                />
            </g>
            <g>
                <path
                    d="M11.25,14.81325L9.1875,14.81325L9.1875,12.75075C9.1875,12.54364,9.01961,12.37575,8.8125,12.37575C8.60539,12.37575,8.4375,12.54364,8.4375,12.75075L8.4375,14.81325L6.375,14.81325C6.167893,14.81325,6,14.98114,6,15.18825C6,15.39536,6.167893,15.56325,6.375,15.56325L8.4375,15.56325L8.4375,17.625C8.4375,17.8321,8.60539,18,8.8125,18C9.01961,18,9.1875,17.8321,9.1875,17.625L9.1875,15.56325L11.25,15.56325C11.45711,15.56325,11.625,15.39536,11.625,15.18825C11.625,14.98114,11.45711,14.81325,11.25,14.81325ZM11.1,6L6.525,6C6.23505,5.9999999654353,6,6.23505,6,6.525L6,11.1C6.000000276517,11.389949999999999,6.235051,11.625,6.525,11.625L11.1,11.625C11.389949999999999,11.625,11.625,11.389949999999999,11.625,11.1L11.625,6.525C11.625,6.23505,11.389949999999999,6,11.1,6ZM10.875,10.875L6.75,10.875L6.75,6.75L10.875,6.75L10.875,10.875ZM17.475,6L12.9,6C12.610050000000001,5.9999999654353,12.375,6.23505,12.375,6.525L12.375,11.1C12.375,11.389949999999999,12.610050000000001,11.625,12.9,11.625L17.475,11.625C17.7648,11.624590000000001,17.9996,11.38978,18,11.1L18,6.525C17.9996,6.235222,17.7648,6.000412878,17.475,6ZM17.25,10.875L13.125,10.875L13.125,6.75L17.25,6.75L17.25,10.875ZM17.475,12.375L12.9,12.375C12.610050000000001,12.375,12.375,12.610050000000001,12.375,12.9L12.375,17.475C12.375,17.7649,12.610050000000001,18,12.9,18L17.475,18C17.7648,17.9996,17.9996,17.7648,18,17.475L18,12.9C17.9996,12.61022,17.7648,12.375409999999999,17.475,12.375ZM17.25,17.25L13.125,17.25L13.125,13.125L17.25,13.125L17.25,17.25Z"
                    fill="#14C9BB"
                    fillOpacity="1"
                />
                <path
                    d="M11.775,11.1L11.775,6.525Q11.775,6.245406,11.577300000000001,6.0477029Q11.37959,5.85,11.1,5.85L6.525,5.85Q6.245406,5.85,6.0477029,6.0477029Q5.85,6.245406,5.85,6.525L5.85,11.1Q5.85,11.37959,6.0477031,11.577300000000001Q6.245407,11.775,6.525,11.775L11.1,11.775Q11.3796,11.775,11.577300000000001,11.577300000000001Q11.775,11.37959,11.775,11.1ZM18.15,11.10021L18.15,6.524786Q18.1496,6.245447,17.9521,6.047922Q17.7546,5.850398,17.4752,5.85L12.9,5.85Q12.6204,5.85,12.422699999999999,6.0477029Q12.225,6.245406,12.225,6.525L12.225,11.1Q12.225,11.37959,12.422699999999999,11.577300000000001Q12.62041,11.775,12.9,11.775L17.475,11.775Q17.7546,11.7746,17.9521,11.577079999999999Q18.1496,11.37955,18.15,11.10021ZM17.85,6.525L17.85,11.099789999999999Q17.8495,11.47447,17.475,11.475L12.9,11.475Q12.525,11.475,12.525,11.1L12.525,6.525Q12.525,6.15,12.9,6.15L17.474800000000002,6.15Q17.8494,6.150533,17.85,6.525ZM11.1,6.15Q11.475,6.15,11.475,6.525L11.475,11.1Q11.475,11.475,11.1,11.475L6.525,11.475Q6.15,11.475,6.15,11.1L6.15,6.525Q6.15,6.36967,6.259835,6.259835Q6.36967,6.15,6.525,6.15L11.1,6.15ZM11.025,11.025L11.025,6.6L6.6,6.6L6.6,11.025L11.025,11.025ZM17.4,11.025L17.4,6.6L12.975,6.6L12.975,11.025L17.4,11.025ZM10.725,6.9L10.725,10.725L6.9,10.725L6.9,6.9L10.725,6.9ZM17.1,6.9L17.1,10.725L13.275,10.725L13.275,6.9L17.1,6.9ZM18.15,17.4752L18.15,12.89979Q18.1496,12.62045,17.9521,12.422920000000001Q17.7546,12.2254,17.4752,12.225L12.9,12.225Q12.6204,12.225,12.422699999999999,12.422699999999999Q12.225,12.6204,12.225,12.9L12.225,17.475Q12.225,17.7546,12.422699999999999,17.9523Q12.62041,18.15,12.9,18.15L17.475,18.15Q17.7546,18.1496,17.9521,17.9521Q18.1496,17.7546,18.15,17.4752ZM11.62123,15.55948Q11.775,15.40571,11.775,15.18825Q11.775,14.97079,11.62123,14.81702Q11.467459999999999,14.66325,11.25,14.66325L9.3375,14.66325L9.3375,12.75075Q9.3375,12.533290000000001,9.18373,12.37952Q9.029959999999999,12.22575,8.8125,12.22575Q8.595040000000001,12.22575,8.44127,12.37952Q8.2875,12.533290000000001,8.2875,12.75075L8.2875,14.66325L6.375,14.66325Q6.157538,14.66325,6.00376892,14.81702Q5.85,14.97079,5.85,15.18825Q5.85,15.40571,6.00376891,15.55948Q6.157538,15.71325,6.375,15.71325L8.2875,15.71325L8.2875,17.625Q8.2875,17.8425,8.44127,17.9962Q8.595040000000001,18.15,8.8125,18.15Q9.029959999999999,18.15,9.18373,17.9962Q9.3375,17.8425,9.3375,17.625L9.3375,15.71325L11.25,15.71325Q11.467459999999999,15.71325,11.62123,15.55948ZM17.85,12.9L17.85,17.474800000000002Q17.8495,17.8495,17.475,17.85L12.9,17.85Q12.74467,17.85,12.634830000000001,17.7402Q12.525,17.6303,12.525,17.475L12.525,12.9Q12.525,12.525,12.9,12.525L17.474800000000002,12.525Q17.8494,12.52553,17.85,12.9ZM11.409099999999999,15.02915Q11.475,15.09505,11.475,15.18825Q11.475,15.41325,11.25,15.41325L9.0375,15.41325L9.0375,17.625Q9.0375,17.85,8.8125,17.85Q8.5875,17.85,8.5875,17.625L8.5875,15.41325L6.375,15.41325Q6.281802,15.41325,6.215901,15.34735Q6.15,15.28145,6.15,15.18825Q6.15,15.09505,6.215901,15.02915Q6.281802,14.96325,6.375,14.96325L8.5875,14.96325L8.5875,12.75075Q8.5875,12.52575,8.8125,12.52575Q9.0375,12.52575,9.0375,12.75075L9.0375,14.96325L11.25,14.96325Q11.3432,14.96325,11.409099999999999,15.02915ZM17.4,17.4L17.4,12.975L12.975,12.975L12.975,17.4L17.4,17.4ZM17.1,13.275L17.1,17.1L13.275,17.1L13.275,13.275L17.1,13.275Z"
                    fillRule="evenodd"
                    fill="#14C9BB"
                    fillOpacity="1"
                />
            </g>
        </g>
    </svg>
);

export const addNEDisabledIcon = () => (
    <svg xmlns="http://www.w3.org/2000/svg" fill="none" version="1.1" width="24" height="24" viewBox="0 0 24 24">
        <g>
            <g>
                <rect x="0" y="0" width="24" height="24" rx="2" fill="#F4F5F7" fillOpacity="1" />
                <rect
                    x="0.5"
                    y="0.5"
                    width="23"
                    height="23"
                    rx="1.5"
                    fillOpacity="0"
                    strokeOpacity="1"
                    stroke="#DADCE1"
                    fill="none"
                    strokeWidth="1"
                />
            </g>
            <g>
                <path
                    d="M11.25,14.81325L9.1875,14.81325L9.1875,12.75075C9.1875,12.54364,9.01961,12.37575,8.8125,12.37575C8.60539,12.37575,8.4375,12.54364,8.4375,12.75075L8.4375,14.81325L6.375,14.81325C6.167893,14.81325,6,14.98114,6,15.18825C6,15.39536,6.167893,15.56325,6.375,15.56325L8.4375,15.56325L8.4375,17.625C8.4375,17.8321,8.60539,18,8.8125,18C9.01961,18,9.1875,17.8321,9.1875,17.625L9.1875,15.56325L11.25,15.56325C11.45711,15.56325,11.625,15.39536,11.625,15.18825C11.625,14.98114,11.45711,14.81325,11.25,14.81325ZM11.1,6L6.525,6C6.23505,5.9999999654353,6,6.23505,6,6.525L6,11.1C6.000000276517,11.389949999999999,6.235051,11.625,6.525,11.625L11.1,11.625C11.389949999999999,11.625,11.625,11.389949999999999,11.625,11.1L11.625,6.525C11.625,6.23505,11.389949999999999,6,11.1,6ZM10.875,10.875L6.75,10.875L6.75,6.75L10.875,6.75L10.875,10.875ZM17.475,6L12.9,6C12.610050000000001,5.9999999654353,12.375,6.23505,12.375,6.525L12.375,11.1C12.375,11.389949999999999,12.610050000000001,11.625,12.9,11.625L17.475,11.625C17.7648,11.624590000000001,17.9996,11.38978,18,11.1L18,6.525C17.9996,6.235222,17.7648,6.000412878,17.475,6ZM17.25,10.875L13.125,10.875L13.125,6.75L17.25,6.75L17.25,10.875ZM17.475,12.375L12.9,12.375C12.610050000000001,12.375,12.375,12.610050000000001,12.375,12.9L12.375,17.475C12.375,17.7649,12.610050000000001,18,12.9,18L17.475,18C17.7648,17.9996,17.9996,17.7648,18,17.475L18,12.9C17.9996,12.61022,17.7648,12.375409999999999,17.475,12.375ZM17.25,17.25L13.125,17.25L13.125,13.125L17.25,13.125L17.25,17.25Z"
                    fill="#B3BBC8"
                    fillOpacity="1"
                />
                <path
                    d="M11.775,11.1L11.775,6.525Q11.775,6.245406,11.577300000000001,6.0477029Q11.37959,5.85,11.1,5.85L6.525,5.85Q6.245406,5.85,6.0477029,6.0477029Q5.85,6.245406,5.85,6.525L5.85,11.1Q5.85,11.37959,6.0477031,11.577300000000001Q6.245407,11.775,6.525,11.775L11.1,11.775Q11.3796,11.775,11.577300000000001,11.577300000000001Q11.775,11.37959,11.775,11.1ZM18.15,11.10021L18.15,6.524786Q18.1496,6.245447,17.9521,6.047922Q17.7546,5.850398,17.4752,5.85L12.9,5.85Q12.6204,5.85,12.422699999999999,6.0477029Q12.225,6.245406,12.225,6.525L12.225,11.1Q12.225,11.37959,12.422699999999999,11.577300000000001Q12.62041,11.775,12.9,11.775L17.475,11.775Q17.7546,11.7746,17.9521,11.577079999999999Q18.1496,11.37955,18.15,11.10021ZM17.85,6.525L17.85,11.099789999999999Q17.8495,11.47447,17.475,11.475L12.9,11.475Q12.525,11.475,12.525,11.1L12.525,6.525Q12.525,6.15,12.9,6.15L17.474800000000002,6.15Q17.8494,6.150533,17.85,6.525ZM11.1,6.15Q11.475,6.15,11.475,6.525L11.475,11.1Q11.475,11.475,11.1,11.475L6.525,11.475Q6.15,11.475,6.15,11.1L6.15,6.525Q6.15,6.36967,6.259835,6.259835Q6.36967,6.15,6.525,6.15L11.1,6.15ZM11.025,11.025L11.025,6.6L6.6,6.6L6.6,11.025L11.025,11.025ZM17.4,11.025L17.4,6.6L12.975,6.6L12.975,11.025L17.4,11.025ZM10.725,6.9L10.725,10.725L6.9,10.725L6.9,6.9L10.725,6.9ZM17.1,6.9L17.1,10.725L13.275,10.725L13.275,6.9L17.1,6.9ZM18.15,17.4752L18.15,12.89979Q18.1496,12.62045,17.9521,12.422920000000001Q17.7546,12.2254,17.4752,12.225L12.9,12.225Q12.6204,12.225,12.422699999999999,12.422699999999999Q12.225,12.6204,12.225,12.9L12.225,17.475Q12.225,17.7546,12.422699999999999,17.9523Q12.62041,18.15,12.9,18.15L17.475,18.15Q17.7546,18.1496,17.9521,17.9521Q18.1496,17.7546,18.15,17.4752ZM11.62123,15.55948Q11.775,15.40571,11.775,15.18825Q11.775,14.97079,11.62123,14.81702Q11.467459999999999,14.66325,11.25,14.66325L9.3375,14.66325L9.3375,12.75075Q9.3375,12.533290000000001,9.18373,12.37952Q9.029959999999999,12.22575,8.8125,12.22575Q8.595040000000001,12.22575,8.44127,12.37952Q8.2875,12.533290000000001,8.2875,12.75075L8.2875,14.66325L6.375,14.66325Q6.157538,14.66325,6.00376892,14.81702Q5.85,14.97079,5.85,15.18825Q5.85,15.40571,6.00376891,15.55948Q6.157538,15.71325,6.375,15.71325L8.2875,15.71325L8.2875,17.625Q8.2875,17.8425,8.44127,17.9962Q8.595040000000001,18.15,8.8125,18.15Q9.029959999999999,18.15,9.18373,17.9962Q9.3375,17.8425,9.3375,17.625L9.3375,15.71325L11.25,15.71325Q11.467459999999999,15.71325,11.62123,15.55948ZM17.85,12.9L17.85,17.474800000000002Q17.8495,17.8495,17.475,17.85L12.9,17.85Q12.74467,17.85,12.634830000000001,17.7402Q12.525,17.6303,12.525,17.475L12.525,12.9Q12.525,12.525,12.9,12.525L17.474800000000002,12.525Q17.8494,12.52553,17.85,12.9ZM11.409099999999999,15.02915Q11.475,15.09505,11.475,15.18825Q11.475,15.41325,11.25,15.41325L9.0375,15.41325L9.0375,17.625Q9.0375,17.85,8.8125,17.85Q8.5875,17.85,8.5875,17.625L8.5875,15.41325L6.375,15.41325Q6.281802,15.41325,6.215901,15.34735Q6.15,15.28145,6.15,15.18825Q6.15,15.09505,6.215901,15.02915Q6.281802,14.96325,6.375,14.96325L8.5875,14.96325L8.5875,12.75075Q8.5875,12.52575,8.8125,12.52575Q9.0375,12.52575,9.0375,12.75075L9.0375,14.96325L11.25,14.96325Q11.3432,14.96325,11.409099999999999,15.02915ZM17.4,17.4L17.4,12.975L12.975,12.975L12.975,17.4L17.4,17.4ZM17.1,13.275L17.1,17.1L13.275,17.1L13.275,13.275L17.1,13.275Z"
                    fillRule="evenodd"
                    fill="#B3BBC8"
                    fillOpacity="1"
                />
            </g>
        </g>
    </svg>
);

export const expandIcon = () => (
    <svg xmlns="http://www.w3.org/2000/svg" fill="none" version="1.1" width="24" height="24" viewBox="0 0 24 24">
        <g>
            <g>
                <rect
                    x="0.5"
                    y="0.5"
                    width="23"
                    height="23"
                    rx="1.5"
                    fillOpacity="0"
                    strokeOpacity="1"
                    stroke="#DCDCDC"
                    fill="none"
                    strokeWidth="1"
                />
            </g>
            <g>
                <g>
                    <path
                        d="M6.422116,18.392742952575684L9.75545,15.092903952575684Q9.84027,15.008939952575684,9.88649,14.898909952575684Q9.93272,14.788878952575683,9.93333,14.669533262575683Q9.933399999999999,14.655114252575684,9.932780000000001,14.640708352575684Q9.92797,14.528939952575683,9.8832,14.426414952575684Q9.83844,14.323890952575683,9.75973,14.244386952575683Q9.67522,14.159010952575684,9.56434,14.112756952575683Q9.45347,14.066502952575684,9.33333,14.066502952575684Q9.31932,14.066502952575684,9.30531,14.067157952575684Q9.19414,14.072354952575683,9.09223,14.117077952575684Q8.990310000000001,14.161801952575683,8.91122,14.240101952575683L5.577884,17.539932952575683Q5.492508,17.624452952575684,5.446254,17.735322952575682Q5.4,17.846202952575684,5.4,17.966332952575684Q5.4,17.980352952575682,5.400655,17.994352952575685Q5.405852,18.105532952575683,5.450575,18.207442952575683Q5.495299,18.309362952575682,5.573599,18.388452952575683Q5.657563,18.473272952575684,5.767593,18.519492952575682Q5.877624,18.565722952575683,5.99696969,18.566332952575685Q6.0113887,18.566402952575682,6.0257946,18.565782952575685Q6.137563,18.560972952575682,6.240088,18.516202952575682Q6.342612,18.471442952575686,6.422116,18.392742952575684Z"
                        fillRule="evenodd"
                        fill="#212519"
                        fillOpacity="1"
                    />
                </g>
                <g>
                    <path
                        d="M17.4,6.6L17.4,9Q17.4,9.05909,17.41153,9.117049999999999Q17.42306,9.17501,17.44567,9.229610000000001Q17.46829,9.28421,17.50112,9.33334Q17.53395,9.382480000000001,17.57574,9.42426Q17.61752,9.46605,17.66666,9.49888Q17.71579,9.53171,17.77039,9.55433Q17.82499,9.57694,17.88295,9.588470000000001Q17.9409,9.6,18,9.6Q18.05909,9.6,18.11705,9.588470000000001Q18.17501,9.57694,18.22961,9.55433Q18.28421,9.53171,18.33334,9.49888Q18.38248,9.46605,18.42426,9.42426Q18.46605,9.382480000000001,18.49888,9.33334Q18.53171,9.28421,18.55433,9.229610000000001Q18.57694,9.17501,18.58847,9.117049999999999Q18.6,9.05909,18.6,9L18.6,6Q18.6,5.9409051999999996,18.58847,5.882946Q18.57694,5.824986,18.55433,5.77039Q18.53171,5.715793,18.49888,5.666658Q18.46605,5.617522,18.42426,5.575736Q18.38248,5.53395,18.33334,5.501118Q18.28421,5.468287,18.22961,5.445672Q18.17501,5.423058,18.11705,5.411529Q18.05909,5.4,18,5.4L15,5.4Q14.9409052,5.4,14.882946,5.411529Q14.824985999999999,5.423058,14.77039,5.445672Q14.715793,5.468287,14.666658,5.501118Q14.617522,5.53395,14.575736,5.575736Q14.53395,5.617522,14.501118,5.666658Q14.468287,5.715793,14.445672,5.77039Q14.423058,5.824986,14.411529,5.882946Q14.4,5.9409051999999996,14.4,6Q14.4,6.0590948000000004,14.411529,6.117054Q14.423058,6.175014,14.445672,6.22961Q14.468287,6.284207,14.501118,6.333342Q14.53395,6.382478,14.575736,6.424264Q14.617522,6.46605,14.666658,6.498882Q14.715793,6.531713,14.77039,6.554328Q14.824985999999999,6.576942,14.882946,6.588471Q14.9409052,6.6,15,6.6L17.4,6.6Z"
                        fillRule="evenodd"
                        fill="#212519"
                        fillOpacity="1"
                    />
                </g>
                <g>
                    <path
                        d="M17.4,17.4L15,17.4Q14.9409052,17.4,14.882946,17.41153Q14.824985999999999,17.42306,14.77039,17.44567Q14.715793,17.46829,14.666658,17.50112Q14.617522,17.53395,14.575736,17.57574Q14.53395,17.61752,14.501118,17.66666Q14.468287,17.71579,14.445672,17.77039Q14.423058,17.82499,14.411529,17.88295Q14.4,17.9409,14.4,18Q14.4,18.05909,14.411529,18.11705Q14.423058,18.17501,14.445672,18.22961Q14.468287,18.28421,14.501118,18.33334Q14.53395,18.38248,14.575736,18.42426Q14.617522,18.46605,14.666658,18.49888Q14.715793,18.53171,14.77039,18.55433Q14.824985999999999,18.57694,14.882946,18.58847Q14.9409052,18.6,15,18.6L18,18.6Q18.05909,18.6,18.11705,18.58847Q18.17501,18.57694,18.22961,18.55433Q18.28421,18.53171,18.33334,18.49888Q18.38248,18.46605,18.42426,18.42426Q18.46605,18.38248,18.49888,18.33334Q18.53171,18.28421,18.55433,18.22961Q18.57694,18.17501,18.58847,18.11705Q18.6,18.05909,18.6,18L18.6,15Q18.6,14.9409052,18.58847,14.882946Q18.57694,14.824985999999999,18.55433,14.77039Q18.53171,14.715793,18.49888,14.666658Q18.46605,14.617522,18.42426,14.575736Q18.38248,14.53395,18.33334,14.501118Q18.28421,14.468287,18.22961,14.445672Q18.17501,14.423058,18.11705,14.411529Q18.05909,14.4,18,14.4Q17.9409,14.4,17.88295,14.411529Q17.82499,14.423058,17.77039,14.445672Q17.71579,14.468287,17.66666,14.501118Q17.61752,14.53395,17.57574,14.575736Q17.53395,14.617522,17.50112,14.666658Q17.46829,14.715793,17.44567,14.77039Q17.42306,14.824985999999999,17.41153,14.882946Q17.4,14.9409052,17.4,15L17.4,17.4Z"
                        fillRule="evenodd"
                        fill="#212519"
                        fillOpacity="1"
                    />
                </g>
                <g>
                    <path
                        d="M6.6,6.6L9,6.6Q9.05909,6.6,9.117049999999999,6.588471Q9.17501,6.576942,9.229610000000001,6.554328Q9.28421,6.531713,9.33334,6.498882Q9.382480000000001,6.46605,9.42426,6.424264Q9.46605,6.382478,9.49888,6.333342Q9.53171,6.284207,9.55433,6.22961Q9.57694,6.175014,9.588470000000001,6.117054Q9.6,6.0590948000000004,9.6,6Q9.6,5.9409051999999996,9.588470000000001,5.882946Q9.57694,5.824986,9.55433,5.77039Q9.53171,5.715793,9.49888,5.666658Q9.46605,5.617522,9.42426,5.575736Q9.382480000000001,5.53395,9.33334,5.501118Q9.28421,5.468287,9.229610000000001,5.445672Q9.17501,5.423058,9.117049999999999,5.411529Q9.05909,5.4,9,5.4L6,5.4Q5.9409051999999996,5.4,5.882946,5.411529Q5.824986,5.423058,5.77039,5.445672Q5.715793,5.468287,5.666658,5.501118Q5.617522,5.53395,5.575736,5.575736Q5.53395,5.617522,5.501118,5.666658Q5.468287,5.715793,5.445672,5.77039Q5.423058,5.824986,5.411529,5.882946Q5.4,5.9409051999999996,5.4,6L5.4,9Q5.4,9.05909,5.411529,9.117049999999999Q5.423058,9.17501,5.445672,9.229610000000001Q5.468287,9.28421,5.501118,9.33334Q5.53395,9.382480000000001,5.575736,9.42426Q5.617522,9.46605,5.666658,9.49888Q5.715793,9.53171,5.77039,9.55433Q5.824986,9.57694,5.882946,9.588470000000001Q5.9409051999999996,9.6,6,9.6Q6.0590948000000004,9.6,6.117054,9.588470000000001Q6.175014,9.57694,6.22961,9.55433Q6.284207,9.53171,6.333342,9.49888Q6.382478,9.46605,6.424264,9.42426Q6.46605,9.382480000000001,6.498882,9.33334Q6.531713,9.28421,6.554328,9.229610000000001Q6.576942,9.17501,6.588471,9.117049999999999Q6.6,9.05909,6.6,9L6.6,6.6Z"
                        fillRule="evenodd"
                        fill="#212519"
                        fillOpacity="1"
                    />
                </g>
                <g>
                    <path
                        d="M5.577884,6.426401L8.91122,9.726230000000001Q8.995470000000001,9.80965,9.10512,9.85474Q9.21477,9.89983,9.33333,9.89983L9.336359999999999,9.89983Q9.45571,9.89922,9.56574,9.85299Q9.67577,9.80677,9.75973,9.72195Q9.84314,9.63769,9.88824,9.52804Q9.93333,9.41839,9.93333,9.29983L9.93333,9.296800000000001Q9.93272,9.17746,9.88649,9.06743Q9.84027,8.9574,9.75545,8.873429999999999L6.422116,5.573599Q6.337859,5.490189,6.22821,5.445094Q6.11856,5.4,6.000000221422,5.4L5.99696977,5.400008Q5.877624,5.40061,5.767593,5.446839Q5.657563,5.493068,5.573599,5.577884Q5.490188,5.662141,5.445094,5.77179Q5.4,5.88144,5.4,6L5.400008,6.00303023Q5.40061,6.122376,5.446839,6.232407Q5.493068,6.342437,5.577884,6.426401Z"
                        fillRule="evenodd"
                        fill="#212519"
                        fillOpacity="1"
                    />
                </g>
                <g>
                    <path
                        d="M18.424294358825684,17.542072952575683L15.124458358825684,14.242238952575683Q15.040067358825684,14.157847952575684,14.929804358825683,14.112174952575684Q14.819541358825683,14.066502952575684,14.700194419927984,14.066502952575684Q14.580847358825684,14.066502952575684,14.470584358825684,14.112174952575684Q14.360321358825683,14.157847952575684,14.275930358825683,14.242238952575683Q14.191539358825684,14.326629952575683,14.145866358825684,14.436892952575684Q14.100194358825684,14.547155952575684,14.100194358825684,14.666502952575684Q14.100194358825684,14.785849952575683,14.145866358825684,14.896112952575683Q14.191538358825683,15.006375952575684,14.275930358825683,15.090766952575684L17.575764358825683,18.390602952575684Q17.660154358825682,18.474992952575683,17.770414358825683,18.520662952575684Q17.880684358825683,18.566332952575685,18.000024358825684,18.566332952575685Q18.014444358825685,18.566332952575685,18.028844358825683,18.565642952575683Q18.140594358825684,18.560272952575684,18.242884358825684,18.514992952575682Q18.345184358825684,18.469702952575684,18.424294358825684,18.390602952575684Q18.508684358825683,18.306212952575684,18.554354358825684,18.195942952575685Q18.600024358825685,18.085682952575684,18.600024358825685,17.966332952575684Q18.600024358825685,17.950802952575685,18.599224358825683,17.935292952575683Q18.593464358825685,17.824142952575684,18.548224358825685,17.722462952575682Q18.502984358825685,17.620772952575685,18.424294358825684,17.542072952575683Z"
                        fillRule="evenodd"
                        fill="#212519"
                        fillOpacity="1"
                    />
                </g>
                <g>
                    <path
                        d="M17.542208046875,5.575843L14.242484046875,8.87557Q14.158093046875,8.95996,14.112420046875,9.070219999999999Q14.066748046875,9.180489999999999,14.066748046875,9.29983Q14.066748046875,9.31537,14.067552046875,9.33088Q14.073310046875,9.44202,14.118548046875,9.54371Q14.163786046875,9.6454,14.242484046875,9.7241Q14.326875046875,9.808489999999999,14.437138046875,9.85416Q14.547401046875,9.89983,14.666748046875,9.89983Q14.786095046875,9.89983,14.896358046875,9.85416Q15.006621046875,9.808489999999999,15.091012046875,9.7241L18.390738046875,6.424371L18.390848046875,6.424264Q18.475238046875,6.339873,18.520908046875,6.22961Q18.566578046875,6.119347,18.566578046875,6Q18.566578046875,5.880653,18.520908046875,5.77039Q18.475238046875,5.660127,18.390848046875,5.575736Q18.306458046875,5.491345,18.196188046875,5.445672Q18.085928046875,5.4,17.966578046875,5.4Q17.847238046875,5.4,17.736968046875,5.445672Q17.626708046875,5.491345,17.542318046875,5.575736L17.542208046875,5.575843Z"
                        fillRule="evenodd"
                        fill="#212519"
                        fillOpacity="1"
                    />
                </g>
                <g>
                    <path
                        d="M6.6,17.4L6.6,15Q6.6,14.9409052,6.588471,14.882946Q6.576942,14.824985999999999,6.554328,14.77039Q6.531713,14.715793,6.498882,14.666658Q6.46605,14.617522,6.424264,14.575736Q6.382478,14.53395,6.333342,14.501118Q6.284207,14.468287,6.22961,14.445672Q6.175014,14.423058,6.117054,14.411529Q6.0590948000000004,14.4,6,14.4Q5.9409051999999996,14.4,5.882946,14.411529Q5.824986,14.423058,5.77039,14.445672Q5.715793,14.468287,5.666658,14.501118Q5.617522,14.53395,5.575736,14.575736Q5.53395,14.617522,5.501118,14.666658Q5.468287,14.715793,5.445672,14.77039Q5.423058,14.824985999999999,5.411529,14.882946Q5.4,14.9409052,5.4,15L5.4,18Q5.4,18.05909,5.411529,18.11705Q5.423058,18.17501,5.445672,18.22961Q5.468287,18.28421,5.501118,18.33334Q5.53395,18.38248,5.575736,18.42426Q5.617522,18.46605,5.666658,18.49888Q5.715793,18.53171,5.77039,18.55433Q5.824986,18.57694,5.882946,18.58847Q5.9409051999999996,18.6,6,18.6L9,18.6Q9.05909,18.6,9.117049999999999,18.58847Q9.17501,18.57694,9.229610000000001,18.55433Q9.28421,18.53171,9.33334,18.49888Q9.382480000000001,18.46605,9.42426,18.42426Q9.46605,18.38248,9.49888,18.33334Q9.53171,18.28421,9.55433,18.22961Q9.57694,18.17501,9.588470000000001,18.11705Q9.6,18.05909,9.6,18Q9.6,17.9409,9.588470000000001,17.88295Q9.57694,17.82499,9.55433,17.77039Q9.53171,17.71579,9.49888,17.66666Q9.46605,17.61752,9.42426,17.57574Q9.382480000000001,17.53395,9.33334,17.50112Q9.28421,17.46829,9.229610000000001,17.44567Q9.17501,17.42306,9.117049999999999,17.41153Q9.05909,17.4,9,17.4L6.6,17.4Z"
                        fillRule="evenodd"
                        fill="#212519"
                        fillOpacity="1"
                    />
                </g>
            </g>
        </g>
    </svg>
);

export const expandFocusIcon = () => (
    <svg xmlns="http://www.w3.org/2000/svg" fill="none" version="1.1" width="24" height="24" viewBox="0 0 24 24">
        <g>
            <g>
                <rect
                    x="0.5"
                    y="0.5"
                    width="23"
                    height="23"
                    rx="1.5"
                    fillOpacity="0"
                    strokeOpacity="1"
                    stroke="#14C9BB"
                    fill="none"
                    strokeWidth="1"
                />
            </g>
            <g>
                <g>
                    <path
                        d="M6.422116,18.392742952575684L9.75545,15.092903952575684Q9.84027,15.008939952575684,9.88649,14.898909952575684Q9.93272,14.788878952575683,9.93333,14.669533262575683Q9.933399999999999,14.655114252575684,9.932780000000001,14.640708352575684Q9.92797,14.528939952575683,9.8832,14.426414952575684Q9.83844,14.323890952575683,9.75973,14.244386952575683Q9.67522,14.159010952575684,9.56434,14.112756952575683Q9.45347,14.066502952575684,9.33333,14.066502952575684Q9.31932,14.066502952575684,9.30531,14.067157952575684Q9.19414,14.072354952575683,9.09223,14.117077952575684Q8.990310000000001,14.161801952575683,8.91122,14.240101952575683L5.577884,17.539932952575683Q5.492508,17.624452952575684,5.446254,17.735322952575682Q5.4,17.846202952575684,5.4,17.966332952575684Q5.4,17.980352952575682,5.400655,17.994352952575685Q5.405852,18.105532952575683,5.450575,18.207442952575683Q5.495299,18.309362952575682,5.573599,18.388452952575683Q5.657563,18.473272952575684,5.767593,18.519492952575682Q5.877624,18.565722952575683,5.99696969,18.566332952575685Q6.0113887,18.566402952575682,6.0257946,18.565782952575685Q6.137563,18.560972952575682,6.240088,18.516202952575682Q6.342612,18.471442952575686,6.422116,18.392742952575684Z"
                        fillRule="evenodd"
                        fill="#14C9BB"
                        fillOpacity="1"
                    />
                </g>
                <g>
                    <path
                        d="M17.4,6.6L17.4,9Q17.4,9.05909,17.41153,9.117049999999999Q17.42306,9.17501,17.44567,9.229610000000001Q17.46829,9.28421,17.50112,9.33334Q17.53395,9.382480000000001,17.57574,9.42426Q17.61752,9.46605,17.66666,9.49888Q17.71579,9.53171,17.77039,9.55433Q17.82499,9.57694,17.88295,9.588470000000001Q17.9409,9.6,18,9.6Q18.05909,9.6,18.11705,9.588470000000001Q18.17501,9.57694,18.22961,9.55433Q18.28421,9.53171,18.33334,9.49888Q18.38248,9.46605,18.42426,9.42426Q18.46605,9.382480000000001,18.49888,9.33334Q18.53171,9.28421,18.55433,9.229610000000001Q18.57694,9.17501,18.58847,9.117049999999999Q18.6,9.05909,18.6,9L18.6,6Q18.6,5.9409051999999996,18.58847,5.882946Q18.57694,5.824986,18.55433,5.77039Q18.53171,5.715793,18.49888,5.666658Q18.46605,5.617522,18.42426,5.575736Q18.38248,5.53395,18.33334,5.501118Q18.28421,5.468287,18.22961,5.445672Q18.17501,5.423058,18.11705,5.411529Q18.05909,5.4,18,5.4L15,5.4Q14.9409052,5.4,14.882946,5.411529Q14.824985999999999,5.423058,14.77039,5.445672Q14.715793,5.468287,14.666658,5.501118Q14.617522,5.53395,14.575736,5.575736Q14.53395,5.617522,14.501118,5.666658Q14.468287,5.715793,14.445672,5.77039Q14.423058,5.824986,14.411529,5.882946Q14.4,5.9409051999999996,14.4,6Q14.4,6.0590948000000004,14.411529,6.117054Q14.423058,6.175014,14.445672,6.22961Q14.468287,6.284207,14.501118,6.333342Q14.53395,6.382478,14.575736,6.424264Q14.617522,6.46605,14.666658,6.498882Q14.715793,6.531713,14.77039,6.554328Q14.824985999999999,6.576942,14.882946,6.588471Q14.9409052,6.6,15,6.6L17.4,6.6Z"
                        fillRule="evenodd"
                        fill="#14C9BB"
                        fillOpacity="1"
                    />
                </g>
                <g>
                    <path
                        d="M17.4,17.4L15,17.4Q14.9409052,17.4,14.882946,17.41153Q14.824985999999999,17.42306,14.77039,17.44567Q14.715793,17.46829,14.666658,17.50112Q14.617522,17.53395,14.575736,17.57574Q14.53395,17.61752,14.501118,17.66666Q14.468287,17.71579,14.445672,17.77039Q14.423058,17.82499,14.411529,17.88295Q14.4,17.9409,14.4,18Q14.4,18.05909,14.411529,18.11705Q14.423058,18.17501,14.445672,18.22961Q14.468287,18.28421,14.501118,18.33334Q14.53395,18.38248,14.575736,18.42426Q14.617522,18.46605,14.666658,18.49888Q14.715793,18.53171,14.77039,18.55433Q14.824985999999999,18.57694,14.882946,18.58847Q14.9409052,18.6,15,18.6L18,18.6Q18.05909,18.6,18.11705,18.58847Q18.17501,18.57694,18.22961,18.55433Q18.28421,18.53171,18.33334,18.49888Q18.38248,18.46605,18.42426,18.42426Q18.46605,18.38248,18.49888,18.33334Q18.53171,18.28421,18.55433,18.22961Q18.57694,18.17501,18.58847,18.11705Q18.6,18.05909,18.6,18L18.6,15Q18.6,14.9409052,18.58847,14.882946Q18.57694,14.824985999999999,18.55433,14.77039Q18.53171,14.715793,18.49888,14.666658Q18.46605,14.617522,18.42426,14.575736Q18.38248,14.53395,18.33334,14.501118Q18.28421,14.468287,18.22961,14.445672Q18.17501,14.423058,18.11705,14.411529Q18.05909,14.4,18,14.4Q17.9409,14.4,17.88295,14.411529Q17.82499,14.423058,17.77039,14.445672Q17.71579,14.468287,17.66666,14.501118Q17.61752,14.53395,17.57574,14.575736Q17.53395,14.617522,17.50112,14.666658Q17.46829,14.715793,17.44567,14.77039Q17.42306,14.824985999999999,17.41153,14.882946Q17.4,14.9409052,17.4,15L17.4,17.4Z"
                        fillRule="evenodd"
                        fill="#14C9BB"
                        fillOpacity="1"
                    />
                </g>
                <g>
                    <path
                        d="M6.6,6.6L9,6.6Q9.05909,6.6,9.117049999999999,6.588471Q9.17501,6.576942,9.229610000000001,6.554328Q9.28421,6.531713,9.33334,6.498882Q9.382480000000001,6.46605,9.42426,6.424264Q9.46605,6.382478,9.49888,6.333342Q9.53171,6.284207,9.55433,6.22961Q9.57694,6.175014,9.588470000000001,6.117054Q9.6,6.0590948000000004,9.6,6Q9.6,5.9409051999999996,9.588470000000001,5.882946Q9.57694,5.824986,9.55433,5.77039Q9.53171,5.715793,9.49888,5.666658Q9.46605,5.617522,9.42426,5.575736Q9.382480000000001,5.53395,9.33334,5.501118Q9.28421,5.468287,9.229610000000001,5.445672Q9.17501,5.423058,9.117049999999999,5.411529Q9.05909,5.4,9,5.4L6,5.4Q5.9409051999999996,5.4,5.882946,5.411529Q5.824986,5.423058,5.77039,5.445672Q5.715793,5.468287,5.666658,5.501118Q5.617522,5.53395,5.575736,5.575736Q5.53395,5.617522,5.501118,5.666658Q5.468287,5.715793,5.445672,5.77039Q5.423058,5.824986,5.411529,5.882946Q5.4,5.9409051999999996,5.4,6L5.4,9Q5.4,9.05909,5.411529,9.117049999999999Q5.423058,9.17501,5.445672,9.229610000000001Q5.468287,9.28421,5.501118,9.33334Q5.53395,9.382480000000001,5.575736,9.42426Q5.617522,9.46605,5.666658,9.49888Q5.715793,9.53171,5.77039,9.55433Q5.824986,9.57694,5.882946,9.588470000000001Q5.9409051999999996,9.6,6,9.6Q6.0590948000000004,9.6,6.117054,9.588470000000001Q6.175014,9.57694,6.22961,9.55433Q6.284207,9.53171,6.333342,9.49888Q6.382478,9.46605,6.424264,9.42426Q6.46605,9.382480000000001,6.498882,9.33334Q6.531713,9.28421,6.554328,9.229610000000001Q6.576942,9.17501,6.588471,9.117049999999999Q6.6,9.05909,6.6,9L6.6,6.6Z"
                        fillRule="evenodd"
                        fill="#14C9BB"
                        fillOpacity="1"
                    />
                </g>
                <g>
                    <path
                        d="M5.577884,6.426401L8.91122,9.726230000000001Q8.995470000000001,9.80965,9.10512,9.85474Q9.21477,9.89983,9.33333,9.89983L9.336359999999999,9.89983Q9.45571,9.89922,9.56574,9.85299Q9.67577,9.80677,9.75973,9.72195Q9.84314,9.63769,9.88824,9.52804Q9.93333,9.41839,9.93333,9.29983L9.93333,9.296800000000001Q9.93272,9.17746,9.88649,9.06743Q9.84027,8.9574,9.75545,8.873429999999999L6.422116,5.573599Q6.337859,5.490189,6.22821,5.445094Q6.11856,5.4,6.000000221422,5.4L5.99696977,5.400008Q5.877624,5.40061,5.767593,5.446839Q5.657563,5.493068,5.573599,5.577884Q5.490188,5.662141,5.445094,5.77179Q5.4,5.88144,5.4,6L5.400008,6.00303023Q5.40061,6.122376,5.446839,6.232407Q5.493068,6.342437,5.577884,6.426401Z"
                        fillRule="evenodd"
                        fill="#14C9BB"
                        fillOpacity="1"
                    />
                </g>
                <g>
                    <path
                        d="M18.424294358825684,17.542072952575683L15.124458358825684,14.242238952575683Q15.040067358825684,14.157847952575684,14.929804358825683,14.112174952575684Q14.819541358825683,14.066502952575684,14.700194419927984,14.066502952575684Q14.580847358825684,14.066502952575684,14.470584358825684,14.112174952575684Q14.360321358825683,14.157847952575684,14.275930358825683,14.242238952575683Q14.191539358825684,14.326629952575683,14.145866358825684,14.436892952575684Q14.100194358825684,14.547155952575684,14.100194358825684,14.666502952575684Q14.100194358825684,14.785849952575683,14.145866358825684,14.896112952575683Q14.191538358825683,15.006375952575684,14.275930358825683,15.090766952575684L17.575764358825683,18.390602952575684Q17.660154358825682,18.474992952575683,17.770414358825683,18.520662952575684Q17.880684358825683,18.566332952575685,18.000024358825684,18.566332952575685Q18.014444358825685,18.566332952575685,18.028844358825683,18.565642952575683Q18.140594358825684,18.560272952575684,18.242884358825684,18.514992952575682Q18.345184358825684,18.469702952575684,18.424294358825684,18.390602952575684Q18.508684358825683,18.306212952575684,18.554354358825684,18.195942952575685Q18.600024358825685,18.085682952575684,18.600024358825685,17.966332952575684Q18.600024358825685,17.950802952575685,18.599224358825683,17.935292952575683Q18.593464358825685,17.824142952575684,18.548224358825685,17.722462952575682Q18.502984358825685,17.620772952575685,18.424294358825684,17.542072952575683Z"
                        fillRule="evenodd"
                        fill="#14C9BB"
                        fillOpacity="1"
                    />
                </g>
                <g>
                    <path
                        d="M17.542208046875,5.575843L14.242484046875,8.87557Q14.158093046875,8.95996,14.112420046875,9.070219999999999Q14.066748046875,9.180489999999999,14.066748046875,9.29983Q14.066748046875,9.31537,14.067552046875,9.33088Q14.073310046875,9.44202,14.118548046875,9.54371Q14.163786046875,9.6454,14.242484046875,9.7241Q14.326875046875,9.808489999999999,14.437138046875,9.85416Q14.547401046875,9.89983,14.666748046875,9.89983Q14.786095046875,9.89983,14.896358046875,9.85416Q15.006621046875,9.808489999999999,15.091012046875,9.7241L18.390738046875,6.424371L18.390848046875,6.424264Q18.475238046875,6.339873,18.520908046875,6.22961Q18.566578046875,6.119347,18.566578046875,6Q18.566578046875,5.880653,18.520908046875,5.77039Q18.475238046875,5.660127,18.390848046875,5.575736Q18.306458046875,5.491345,18.196188046875,5.445672Q18.085928046875,5.4,17.966578046875,5.4Q17.847238046875,5.4,17.736968046875,5.445672Q17.626708046875,5.491345,17.542318046875,5.575736L17.542208046875,5.575843Z"
                        fillRule="evenodd"
                        fill="#14C9BB"
                        fillOpacity="1"
                    />
                </g>
                <g>
                    <path
                        d="M6.6,17.4L6.6,15Q6.6,14.9409052,6.588471,14.882946Q6.576942,14.824985999999999,6.554328,14.77039Q6.531713,14.715793,6.498882,14.666658Q6.46605,14.617522,6.424264,14.575736Q6.382478,14.53395,6.333342,14.501118Q6.284207,14.468287,6.22961,14.445672Q6.175014,14.423058,6.117054,14.411529Q6.0590948000000004,14.4,6,14.4Q5.9409051999999996,14.4,5.882946,14.411529Q5.824986,14.423058,5.77039,14.445672Q5.715793,14.468287,5.666658,14.501118Q5.617522,14.53395,5.575736,14.575736Q5.53395,14.617522,5.501118,14.666658Q5.468287,14.715793,5.445672,14.77039Q5.423058,14.824985999999999,5.411529,14.882946Q5.4,14.9409052,5.4,15L5.4,18Q5.4,18.05909,5.411529,18.11705Q5.423058,18.17501,5.445672,18.22961Q5.468287,18.28421,5.501118,18.33334Q5.53395,18.38248,5.575736,18.42426Q5.617522,18.46605,5.666658,18.49888Q5.715793,18.53171,5.77039,18.55433Q5.824986,18.57694,5.882946,18.58847Q5.9409051999999996,18.6,6,18.6L9,18.6Q9.05909,18.6,9.117049999999999,18.58847Q9.17501,18.57694,9.229610000000001,18.55433Q9.28421,18.53171,9.33334,18.49888Q9.382480000000001,18.46605,9.42426,18.42426Q9.46605,18.38248,9.49888,18.33334Q9.53171,18.28421,9.55433,18.22961Q9.57694,18.17501,9.588470000000001,18.11705Q9.6,18.05909,9.6,18Q9.6,17.9409,9.588470000000001,17.88295Q9.57694,17.82499,9.55433,17.77039Q9.53171,17.71579,9.49888,17.66666Q9.46605,17.61752,9.42426,17.57574Q9.382480000000001,17.53395,9.33334,17.50112Q9.28421,17.46829,9.229610000000001,17.44567Q9.17501,17.42306,9.117049999999999,17.41153Q9.05909,17.4,9,17.4L6.6,17.4Z"
                        fillRule="evenodd"
                        fill="#14C9BB"
                        fillOpacity="1"
                    />
                </g>
            </g>
        </g>
    </svg>
);

export const commonViewIcon = () => (
    <svg xmlns="http://www.w3.org/2000/svg" fill="none" version="1.1" width="16" height="16" viewBox="0 0 16 16">
        <defs>
            <clipPath id="master_svg0_93_76094">
                <rect x="0" y="0" width="16" height="16" rx="0" />
            </clipPath>
        </defs>
        <g clipPath="url(#master_svg0_93_76094)">
            <g>
                <g>
                    <path
                        d="M6.285713925262451,5.142857074737549C6.049020925262451,5.142857074737549,5.857142925262451,5.334735074737549,5.857142925262451,5.571428074737549C5.857142925262451,5.808122074737549,6.049020925262451,6.000000074737549,6.285713925262451,6.000000074737549L9.71428292526245,6.000000074737549C9.950982925262451,6.000000074737549,10.142852925262451,5.808122074737549,10.142852925262451,5.571428074737549C10.142852925262451,5.334735074737549,9.950982925262451,5.142857074737549,9.71428292526245,5.142857074737549L6.285713925262451,5.142857074737549Z"
                        fill="#14C9BB"
                        fillOpacity="1"
                    />
                    <path
                        d="M10.088042925262451,5.197671974737549Q9.933222925262452,5.042857074737549,9.71428292526245,5.042857074737549L6.285713925262451,5.042857074737549Q6.066772925262451,5.042857074737549,5.911957825262451,5.197671974737549Q5.7571429252624515,5.352487074737549,5.7571429252624515,5.571428074737549Q5.7571429252624515,5.790370074737549,5.911957825262451,5.945185074737549Q6.066772925262451,6.100000074737549,6.285713925262451,6.100000074737549L9.71428292526245,6.100000074737549Q9.933222925262452,6.100000074737549,10.088042925262451,5.945185074737549Q10.24285292526245,5.790370074737549,10.24285292526245,5.571428074737549Q10.24285292526245,5.352487074737549,10.088042925262451,5.197671974737549ZM6.285713925262451,5.2428570747375485L9.71428292526245,5.2428570747375485Q10.042852925262451,5.242856974737549,10.042852925262451,5.571428074737549Q10.042852925262451,5.900000074737549,9.71428292526245,5.900000074737549L6.285713925262451,5.900000074737549Q5.957142925262451,5.900000074737549,5.957142925262451,5.571428074737549Q5.957142925262451,5.2428570747375485,6.285713925262451,5.2428570747375485Z"
                        fillRule="evenodd"
                        fill="#14C9BB"
                        fillOpacity="1"
                    />
                </g>
                <g>
                    <path
                        d="M2,4.71429C1.999999897821,3.767512,2.767512,3,3.71429,3L12.2857,3C13.2325,3,14,3.767512,14,4.71429L14,6.428570000000001C14,7.37535,13.2325,8.142859999999999,12.2857,8.142859999999999L3.71429,8.142859999999999C2.767512,8.142859999999999,2,7.37535,2,6.428570000000001L2,4.71429ZM3.71429,4.02857C3.33558,4.02857,3.02857,4.33558,3.02857,4.71429L3.02857,6.428570000000001C3.02857,6.80728,3.33558,7.11429,3.71429,7.11429L12.2857,7.11429C12.6644,7.11429,12.9714,6.80728,12.9714,6.428570000000001L12.9714,4.71429C12.9714,4.33558,12.6644,4.02857,12.2857,4.02857L3.71429,4.02857ZM6.28571,11.14286C6.04902,11.14286,5.857139999999999,11.33474,5.857139999999999,11.57143C5.857139999999999,11.80812,6.04902,12,6.28571,12L9.71429,12C9.950980000000001,12,10.14286,11.80812,10.14286,11.57143C10.14286,11.33474,9.950980000000001,11.14286,9.71429,11.14286L6.28571,11.14286Z"
                        fill="#14C9BB"
                        fillOpacity="1"
                    />
                    <path
                        d="M1.9,4.71429L1.9,6.428570000000001Q1.9000001,7.18007,2.4313919999999998,7.71146Q2.9627850000000002,8.24286,3.71429,8.24286L12.2857,8.24286Q13.0372,8.24286,13.5686,7.71147Q14.1,7.18008,14.1,6.428570000000001L14.1,4.71429Q14.1,3.962781,13.5686,3.4313919999999998Q13.0372,2.9,12.2857,2.9L3.71429,2.9Q2.962784,2.9,2.4313919999999998,3.4313919999999998Q1.9,3.962784,1.9,4.71429ZM2.572813,7.57004Q2.0999999,7.09723,2.1,6.428570000000001L2.1,4.71429Q2.0999999,4.04563,2.572813,3.572813Q3.04563,3.0999999,3.71429,3.1L12.2857,3.1Q12.9544,3.1,13.4272,3.572813Q13.9,4.0456199999999995,13.9,4.71429L13.9,6.428570000000001Q13.9,7.09723,13.4272,7.57004Q12.9544,8.042860000000001,12.2857,8.042860000000001L3.71429,8.042860000000001Q3.04563,8.042860000000001,2.572813,7.57004ZM12.8413,4.1587Q12.6112,3.928571,12.2857,3.928571L3.71429,3.928571Q3.38883,3.928571,3.1587,4.1587Q2.928571,4.3888300000000005,2.928571,4.71429L2.928571,6.428570000000001Q2.928571,6.754020000000001,3.1587,6.98416Q3.38883,7.21429,3.71429,7.21429L12.2857,7.21429Q12.6112,7.21429,12.8413,6.98416Q13.0714,6.75403,13.0714,6.428570000000001L13.0714,4.71429Q13.0714,4.3888300000000005,12.8413,4.1587ZM3.71429,4.12857L12.2857,4.12857Q12.5283,4.12857,12.6999,4.30012Q12.8714,4.47167,12.8714,4.71429L12.8714,6.428570000000001Q12.8714,6.67118,12.6999,6.8427299999999995Q12.5283,7.01429,12.2857,7.01429L3.71429,7.01429Q3.47168,7.01429,3.3001199999999997,6.8427299999999995Q3.12857,6.67118,3.12857,6.428570000000001L3.12857,4.71429Q3.12857,4.47167,3.3001199999999997,4.30012Q3.47167,4.12857,3.71429,4.12857ZM10.08804,11.19767Q9.93322,11.04286,9.71429,11.04286L6.28571,11.04286Q6.06677,11.04286,5.9119600000000005,11.19767Q5.75714,11.35249,5.75714,11.57143Q5.75714,11.79037,5.9119600000000005,11.94518Q6.06677,12.1,6.28571,12.1L9.71429,12.1Q9.93323,12.1,10.08804,11.94519Q10.24286,11.79037,10.24286,11.57143Q10.24286,11.35249,10.08804,11.19767ZM6.28571,11.24286L9.71429,11.24286Q9.850380000000001,11.24286,9.94662,11.33909Q10.04286,11.43533,10.04286,11.57143Q10.04286,11.9,9.71429,11.9L6.28571,11.9Q5.95714,11.9,5.95714,11.57143Q5.95714,11.43533,6.05338,11.33909Q6.14961,11.24286,6.28571,11.24286Z"
                        fillRule="evenodd"
                        fill="#14C9BB"
                        fillOpacity="1"
                    />
                </g>
                <g>
                    <path
                        d="M2,10.71429C1.999999897821,9.767512,2.767512,9,3.71429,9L12.2857,9C13.2325,9,14,9.767512,14,10.71429L14,12.42857C14,13.375350000000001,13.2325,14.142859999999999,12.2857,14.142859999999999L3.71429,14.142859999999999C2.767512,14.142859999999999,2,13.375350000000001,2,12.42857L2,10.71429ZM3.71429,10.02857C3.33558,10.02857,3.02857,10.33558,3.02857,10.71429L3.02857,12.42857C3.02857,12.80728,3.33558,13.11429,3.71429,13.11429L12.2857,13.11429C12.6644,13.11429,12.9714,12.80728,12.9714,12.42857L12.9714,10.71429C12.9714,10.33558,12.6644,10.02857,12.2857,10.02857L3.71429,10.02857Z"
                        fill="#14C9BB"
                        fillOpacity="1"
                    />
                    <path
                        d="M1.9,10.71429L1.9,12.42857Q1.9000001,13.18007,2.4313919999999998,13.711459999999999Q2.9627850000000002,14.24286,3.71429,14.24286L12.2857,14.24286Q13.0372,14.24286,13.5686,13.71147Q14.1,13.18008,14.1,12.42857L14.1,10.71429Q14.1,9.962781,13.5686,9.431392Q13.0372,8.9,12.2857,8.9L3.71429,8.9Q2.962784,8.9,2.4313919999999998,9.431392Q1.9,9.962784,1.9,10.71429ZM2.572813,13.570039999999999Q2.0999999,13.09723,2.1,12.42857L2.1,10.71429Q2.0999999,10.04563,2.572813,9.572813Q3.04563,9.0999999,3.71429,9.1L12.2857,9.1Q12.9544,9.1,13.4272,9.572813Q13.9,10.04562,13.9,10.71429L13.9,12.42857Q13.9,13.09723,13.4272,13.570039999999999Q12.9544,14.042860000000001,12.2857,14.042860000000001L3.71429,14.042860000000001Q3.04563,14.042860000000001,2.572813,13.570039999999999ZM12.8413,10.1587Q12.6112,9.928571,12.2857,9.928571L3.71429,9.928571Q3.38883,9.928571,3.1587,10.1587Q2.928571,10.38883,2.928571,10.71429L2.928571,12.42857Q2.928571,12.75402,3.1587,12.98416Q3.38883,13.21429,3.71429,13.21429L12.2857,13.21429Q12.6112,13.21429,12.8413,12.98416Q13.0714,12.75403,13.0714,12.42857L13.0714,10.71429Q13.0714,10.38883,12.8413,10.1587ZM3.71429,10.12857L12.2857,10.12857Q12.5283,10.12857,12.6999,10.30012Q12.8714,10.47167,12.8714,10.71429L12.8714,12.42857Q12.8714,12.67118,12.6999,12.84273Q12.5283,13.014289999999999,12.2857,13.014289999999999L3.71429,13.014289999999999Q3.47168,13.014289999999999,3.3001199999999997,12.84273Q3.12857,12.67118,3.12857,12.42857L3.12857,10.71429Q3.12857,10.47167,3.3001199999999997,10.30012Q3.47168,10.12857,3.71429,10.12857Z"
                        fillRule="evenodd"
                        fill="#14C9BB"
                        fillOpacity="1"
                    />
                </g>
            </g>
        </g>
    </svg>
);

export const otnGroupIcon = () => (
    <svg
        xmlns="http://www.w3.org/2000/svg"
        fill="none"
        version="1.1"
        width="16"
        height="16.00048828125"
        viewBox="0 0 16 16.00048828125"
    >
        <g>
            <g />
            <g>
                <g>
                    <g>
                        <rect
                            x="0.699999988079071"
                            y="0.699999988079071"
                            width="14.600000023841858"
                            height="14.600000023841858"
                            rx="7.300000011920929"
                            fillOpacity="0"
                            strokeOpacity="1"
                            stroke="#14C9BB"
                            fill="none"
                            strokeWidth="1.399999976158142"
                        />
                    </g>
                    <g>
                        <g>
                            <g>
                                <path
                                    d="M8.877023753356934,4.1233699999999995Q8.886453753356934,4.13416,8.894253753356933,4.14619Q8.902053753356935,4.15821,8.908053753356933,4.1712299999999995Q8.914053753356933,4.18424,8.918143753356933,4.19798Q8.922223753356933,4.21172,8.924313753356934,4.22589Q8.926403753356933,4.24007,8.926443753356933,4.2544Q8.926483753356933,4.26874,8.924483753356935,4.28293Q8.922483753356934,4.29712,8.918473753356933,4.31088Q8.914473753356933,4.3246400000000005,8.908543753356934,4.33769Q8.902623753356934,4.35074,8.894893753356934,4.36281Q8.887173753356933,4.37488,8.877803753356933,4.38572Q8.868433753356934,4.3965700000000005,8.857613753356933,4.40597Q8.846803753356934,4.41537,8.834753753356933,4.4231300000000005Q8.822703753356933,4.43089,8.809673753356934,4.43686Q8.796643753356934,4.44282,8.782893753356934,4.44686Q8.769143753356934,4.45091,8.754953753356933,4.4529499999999995Q8.740773753356933,4.455,8.726443753356934,4.455L7.063768753356934,4.455Q7.0494377533569335,4.455,7.035252753356934,4.4529499999999995Q7.021067753356934,4.45091,7.007318753356934,4.44686Q6.993569753356933,4.44282,6.980538753356933,4.43686Q6.9675067533569335,4.43089,6.955459753356934,4.4231300000000005Q6.9434117533569335,4.41537,6.932592753356934,4.40597Q6.921774753356933,4.3965700000000005,6.912407753356933,4.38572Q6.903039753356934,4.37488,6.895314753356933,4.36281Q6.887588753356933,4.35074,6.881663753356934,4.33769Q6.875738753356933,4.3246400000000005,6.871733753356933,4.31088Q6.867729753356934,4.29712,6.865728753356934,4.28293Q6.863726753356934,4.26874,6.863769753356934,4.2544Q6.863811753356933,4.24007,6.865897753356934,4.22589Q6.867982753356934,4.21172,6.872068753356934,4.19798Q6.876154753356934,4.18424,6.882156753356933,4.1712299999999995Q6.888159753356933,4.15821,6.895956753356933,4.14619Q6.903752753356933,4.13416,6.9131847533569335,4.1233699999999995L7.744523753356933,3.1722770000000002Q7.758603753356933,3.1561690000000002,7.775763753356934,3.143404Q7.792933753356934,3.130639,7.8124137533569336,3.121794Q7.831893753356933,3.112949,7.852803753356934,3.108424Q7.873713753356934,3.103899,7.895103753356933,3.103899Q7.916493753356933,3.103899,7.937403753356934,3.108424Q7.958313753356934,3.112949,7.977793753356933,3.121794Q7.997273753356934,3.130639,8.014443753356934,3.143404Q8.031613753356934,3.1561690000000002,8.045693753356934,3.1722770000000002L8.877023753356934,4.1233699999999995Z"
                                    fill="#14C9BB"
                                    fillOpacity="1"
                                />
                                <path
                                    d="M7.524903753356933,4.455L7.541063753356934,4.455Q7.532523753356934,4.4524799999999995,7.5240337533569335,4.44983Q7.469633753356934,4.12042,7.633813753356934,3.758853Q7.739613753356934,3.525849,7.895103753356933,3.369979Q8.050593753356933,3.525849,8.156403753356933,3.758853Q8.320583753356933,4.12042,8.266173753356934,4.44983Q8.257693753356934,4.4524799999999995,8.249153753356934,4.455L8.726443753356934,4.455Q8.785283753356934,4.455,8.834753753356933,4.4231300000000005Q8.884213753356933,4.39126,8.908543753356934,4.33769Q8.932873753356933,4.28411,8.924313753356934,4.22589Q8.915753753356935,4.16768,8.877023753356934,4.1233699999999995L8.086273753356934,3.21871L8.045693753356934,3.1722770000000002Q7.985923753356934,3.103899,7.895103753356933,3.103899Q7.804293753356934,3.103899,7.744523753356933,3.1722770000000002L7.703933753356933,3.21871L6.9131847533569335,4.1233699999999995Q6.874459753356934,4.16768,6.865897753356934,4.22589Q6.857334753356934,4.28411,6.881663753356934,4.33769Q6.9059917533569335,4.3912700000000005,6.955458753356933,4.4231300000000005Q7.0049267533569335,4.455,7.063768753356934,4.455L7.524903753356933,4.455Z"
                                    fillRule="evenodd"
                                    fill="#14C9BB"
                                    fillOpacity="1"
                                />
                            </g>
                            <g>
                                <path
                                    d="M6.900028021453857,5.694996824264527L6.671329021453857,5.694996824264527L6.671329021453857,7.094996824264527L6.900028021453857,7.094996824264527Q7.605611021453857,7.094996824264527,8.102219021453857,6.593196824264526Q8.595109021453858,6.095156824264526,8.595109021453858,5.3924028242645266L8.595109021453858,4.454996824264526L7.195105021453857,4.454996824264526L7.195105021453857,5.3924028242645266Q7.195105021453857,5.694996824264527,6.900028021453857,5.694996824264527Z"
                                    fillRule="evenodd"
                                    fill="#14C9BB"
                                    fillOpacity="1"
                                />
                            </g>
                        </g>
                        <g transform="matrix(0,1,-1,0,19.185203075408936,-6.395218372344971)">
                            <g>
                                <path
                                    d="M15.221910723876952,7.533282351531982Q15.231210723876952,7.544122351531982,15.238870723876953,7.556162351531983Q15.246530723876953,7.5682023515319825,15.252410723876952,7.581212351531983Q15.258280723876954,7.594222351531982,15.262250723876953,7.607932351531982Q15.266220723876954,7.621642351531983,15.268200723876953,7.635782351531983Q15.270180723876953,7.649922351531982,15.270130723876953,7.664192351531982Q15.270080723876953,7.678462351531983,15.268010723876953,7.692582351531982Q15.265930723876952,7.706712351531983,15.261870723876953,7.7203923515319826Q15.257810723876954,7.734082351531982,15.251850723876952,7.747052351531982Q15.245890723876954,7.760022351531982,15.238150723876952,7.772012351531982Q15.230410723876954,7.784002351531982,15.221040723876953,7.794772351531982Q15.211670723876953,7.805542351531982,15.200870723876953,7.8148723515319825Q15.190070723876953,7.824202351531983,15.178050723876954,7.8319023515319826Q15.166040723876954,7.839612351531983,15.153050723876953,7.845522351531983Q15.140060723876953,7.851442351531983,15.126360723876953,7.855452351531983Q15.112660723876953,7.859472351531982,15.098530723876953,7.861492351531982Q15.084400723876954,7.863522351531982,15.070130723876954,7.863522351531982L13.420284723876954,7.863522351531982Q13.406010723876953,7.863522351531982,13.391881723876953,7.861492351531982Q13.377752723876952,7.859472351531982,13.364054723876952,7.855452351531983Q13.350356723876953,7.851442351531983,13.337367723876953,7.845522351531983Q13.324377723876953,7.839612351531983,13.312360723876953,7.8319023515319826Q13.300343723876953,7.824202351531983,13.289541723876953,7.8148723515319825Q13.278740723876954,7.805542351531982,13.269373723876953,7.794772351531982Q13.260006723876954,7.784002351531982,13.252263723876952,7.772012351531982Q13.244521723876954,7.760022351531982,13.238560723876953,7.747052351531982Q13.232599723876953,7.734082351531982,13.228540723876954,7.7203923515319826Q13.224481723876954,7.706712351531983,13.222407723876954,7.692582351531982Q13.220333723876953,7.678462351531983,13.220285723876954,7.664192351531982Q13.220238723876953,7.649922351531982,13.222218723876953,7.635782351531983Q13.224198723876952,7.621642351531983,13.228165723876954,7.607932351531982Q13.232132723876953,7.594222351531982,13.238007723876953,7.581212351531983Q13.243881723876953,7.5682023515319825,13.251543723876953,7.556162351531983Q13.259206723876954,7.544122351531982,13.268500723876953,7.533282351531982L14.093420723876953,6.5718873515319824Q14.107510723876953,6.555476351531983,14.124770723876953,6.542453351531982Q14.142030723876953,6.529431351531983,14.161680723876954,6.520400351531983Q14.181330723876954,6.511369351531982,14.202460723876953,6.506747351531982Q14.223580723876953,6.502124351531982,14.245210723876953,6.502124351531982Q14.266830723876954,6.502124351531982,14.287960723876953,6.506747351531982Q14.309080723876953,6.511369351531982,14.328730723876953,6.520400351531983Q14.348380723876954,6.529431351531983,14.365640723876954,6.542453351531982Q14.382910723876954,6.555476351531983,14.396990723876954,6.5718873515319824L15.221910723876952,7.533282351531982Z"
                                    fill="#14C9BB"
                                    fillOpacity="1"
                                />
                                <path
                                    d="M13.868940723876953,7.863522351531982L13.927970723876953,7.863522351531982Q13.896610723876954,7.855442351531982,13.866030723876953,7.845662351531982Q13.815870723876953,7.519012351531982,13.979780723876953,7.1623783515319825Q14.087370723876953,6.928303351531983,14.245210723876953,6.772629351531982Q14.403040723876954,6.928303351531983,14.510630723876954,7.162379351531983Q14.674550723876953,7.519012351531982,14.624380723876953,7.845662351531982Q14.593800723876953,7.855442351531982,14.562450723876953,7.863522351531982L15.070130723876954,7.863522351531982Q15.128720723876953,7.863522351531982,15.178050723876954,7.8319023515319826Q15.227380723876953,7.800292351531983,15.251850723876952,7.747052351531982Q15.276320723876953,7.6938123515319825,15.268200723876953,7.635782351531983Q15.260070723876954,7.5777523515319825,15.221910723876952,7.533282351531982L14.439710723876953,6.621680351531983L14.396990723876954,6.5718873515319824Q14.337130723876953,6.502124351531982,14.245210723876953,6.502124351531982Q14.153280723876954,6.502124351531982,14.093420723876953,6.5718873515319824L14.050700723876954,6.621680351531983L13.268500723876953,7.533282351531982Q13.230345723876953,7.5777523515319825,13.222218723876953,7.635782351531983Q13.214090723876954,7.6938123515319825,13.238560723876953,7.747052351531982Q13.263030723876954,7.800292351531983,13.312360723876953,7.8319023515319826Q13.361691723876953,7.863522351531982,13.420284723876954,7.863522351531982L13.868940723876953,7.863522351531982Z"
                                    fillRule="evenodd"
                                    fill="#14C9BB"
                                    fillOpacity="1"
                                />
                            </g>
                            <g>
                                <path
                                    d="M13.24550322439003,9.121563960113525L13.03271022439003,9.121563960113525L13.03271022439003,10.521563960113525L13.24550322439003,10.521563960113525Q13.95195822439003,10.521563960113525,14.451560224390029,10.013533960113525Q14.94521022439003,9.511553960113526,14.94521022439003,8.809649960113525L14.94521022439003,7.863523960113525L13.54520722439003,7.863523960113525L13.54520822439003,8.809649960113525Q13.54520822439003,9.121563960113525,13.24550322439003,9.121563960113525Z"
                                    fillRule="evenodd"
                                    fill="#14C9BB"
                                    fillOpacity="1"
                                />
                            </g>
                        </g>
                        <g transform="matrix(-1,0,0,-1,18.727272987365723,25.39995765686035)">
                            <g>
                                <path
                                    d="M11.814086493682861,13.823348828430175Q11.823516493682861,13.834138828430175,11.831316493682861,13.846168828430176Q11.839116493682862,13.858188828430176,11.84511649368286,13.871208828430175Q11.851116493682861,13.884218828430175,11.85520649368286,13.897958828430175Q11.85928649368286,13.911698828430175,11.861376493682862,13.925868828430175Q11.86346649368286,13.940048828430175,11.863506493682861,13.954378828430176Q11.863546493682861,13.968718828430175,11.861546493682862,13.982908828430176Q11.859546493682862,13.997098828430175,11.85553649368286,14.010858828430177Q11.851536493682861,14.024618828430176,11.845606493682862,14.037668828430176Q11.839686493682862,14.050718828430176,11.831956493682862,14.062788828430175Q11.82423649368286,14.074858828430177,11.81486649368286,14.085698828430175Q11.805496493682861,14.096548828430176,11.79467649368286,14.105948828430176Q11.783866493682861,14.115348828430175,11.771816493682861,14.123108828430176Q11.75976649368286,14.130868828430176,11.746736493682862,14.136838828430175Q11.733706493682861,14.142798828430175,11.719956493682862,14.146838828430177Q11.706206493682862,14.150888828430176,11.69201649368286,14.152928828430175Q11.677836493682861,14.154978828430176,11.663506493682862,14.154978828430176L10.000831493682862,14.154978828430176Q9.986500493682861,14.154978828430176,9.972315493682862,14.152928828430175Q9.95813049368286,14.150888828430176,9.944381493682862,14.146838828430177Q9.930632493682861,14.142798828430175,9.917601493682861,14.136838828430175Q9.904569493682862,14.130868828430176,9.892522493682861,14.123108828430176Q9.880474493682861,14.115348828430175,9.869655493682862,14.105948828430176Q9.858837493682861,14.096548828430176,9.849470493682862,14.085698828430175Q9.840102493682862,14.074858828430177,9.832377493682861,14.062788828430175Q9.824651493682861,14.050718828430176,9.818726493682862,14.037668828430176Q9.812801493682862,14.024618828430176,9.80879649368286,14.010858828430177Q9.80479249368286,13.997098828430175,9.80279149368286,13.982908828430176Q9.800789493682862,13.968718828430175,9.800832493682861,13.954378828430176Q9.800874493682862,13.940048828430175,9.80296049368286,13.925868828430175Q9.805045493682861,13.911698828430175,9.80913149368286,13.897958828430175Q9.813217493682862,13.884218828430175,9.819219493682862,13.871208828430175Q9.825222493682862,13.858188828430176,9.833019493682862,13.846168828430176Q9.840815493682861,13.834138828430175,9.850247493682861,13.823348828430175L10.681586493682861,12.872255828430175Q10.695666493682861,12.856147828430176,10.712826493682861,12.843382828430176Q10.729996493682862,12.830617828430176,10.749476493682861,12.821772828430175Q10.768956493682861,12.812927828430176,10.789866493682862,12.808402828430175Q10.810776493682862,12.803877828430176,10.83216649368286,12.803877828430176Q10.853556493682861,12.803877828430176,10.874466493682862,12.808402828430175Q10.89537649368286,12.812927828430176,10.914856493682862,12.821772828430175Q10.934336493682862,12.830617828430176,10.951506493682862,12.843382828430176Q10.968676493682862,12.856147828430176,10.982756493682862,12.872255828430175L11.814086493682861,13.823348828430175Z"
                                    fill="#14C9BB"
                                    fillOpacity="1"
                                />
                                <path
                                    d="M10.461966493682862,14.154978828430176L10.478126493682861,14.154978828430176Q10.469586493682861,14.152458828430175,10.461096493682861,14.149808828430176Q10.406696493682862,13.820398828430175,10.570876493682862,13.458831828430176Q10.676676493682862,13.225827828430177,10.83216649368286,13.069957828430177Q10.987656493682861,13.225827828430177,11.093466493682861,13.458831828430176Q11.257646493682861,13.820398828430175,11.203236493682862,14.149808828430176Q11.194756493682862,14.152458828430175,11.186216493682862,14.154978828430176L11.663506493682862,14.154978828430176Q11.722346493682862,14.154978828430176,11.771816493682861,14.123108828430176Q11.821276493682861,14.091238828430175,11.845606493682862,14.037668828430176Q11.86993649368286,13.984088828430176,11.861376493682862,13.925868828430175Q11.852816493682862,13.867658828430176,11.814086493682861,13.823348828430175L11.023336493682862,12.918688828430176L10.982756493682862,12.872255828430175Q10.922986493682862,12.803877828430176,10.83216649368286,12.803877828430176Q10.741356493682861,12.803877828430176,10.681586493682861,12.872255828430175L10.640996493682861,12.918688828430176L9.850247493682861,13.823348828430175Q9.811522493682862,13.867658828430176,9.80296049368286,13.925868828430175Q9.794397493682862,13.984088828430176,9.818726493682862,14.037668828430176Q9.843054493682862,14.091248828430176,9.892521493682862,14.123108828430176Q9.941989493682861,14.154978828430176,10.000831493682862,14.154978828430176L10.461966493682862,14.154978828430176Z"
                                    fillRule="evenodd"
                                    fill="#14C9BB"
                                    fillOpacity="1"
                                />
                            </g>
                            <g>
                                <path
                                    d="M9.837090761779786,15.394975652694702L9.608391761779785,15.394975652694702L9.608391761779785,16.794975652694703L9.837090761779786,16.794975652694703Q10.542673761779785,16.794975652694703,11.039281761779785,16.293175652694703Q11.532171761779786,15.795135652694702,11.532171761779786,15.092381652694701L11.532171761779786,14.154975652694702L10.132167761779785,14.154975652694702L10.132167761779785,15.092381652694701Q10.132167761779785,15.394975652694702,9.837090761779786,15.394975652694702Z"
                                    fillRule="evenodd"
                                    fill="#14C9BB"
                                    fillOpacity="1"
                                />
                            </g>
                        </g>
                        <g transform="matrix(0,-1,1,0,-6.304986000061035,12.304986000061035)">
                            <g>
                                <path
                                    d="M5.4317,10.443276000061035Q5.441,10.454116000061035,5.44866,10.466156000061035Q5.45632,10.478196000061036,5.4622,10.491206000061036Q5.46807,10.504216000061035,5.47204,10.517926000061035Q5.4760100000000005,10.531636000061035,5.47799,10.545776000061036Q5.47997,10.559916000061035,5.47992,10.574186000061035Q5.47987,10.588456000061035,5.4778,10.602576000061035Q5.47572,10.616706000061034,5.47166,10.630386000061035Q5.4676,10.644076000061036,5.46164,10.657046000061035Q5.45568,10.670016000061036,5.44794,10.682006000061035Q5.4402,10.693996000061036,5.43083,10.704766000061035Q5.42146,10.715536000061036,5.41066,10.724866000061034Q5.39986,10.734196000061035,5.387840000000001,10.741896000061034Q5.3758300000000006,10.749606000061036,5.36284,10.755516000061036Q5.34985,10.761436000061035,5.33615,10.765446000061035Q5.32245,10.769466000061035,5.30832,10.771486000061035Q5.29419,10.773516000061035,5.279920000000001,10.773516000061035L3.630074,10.773516000061035Q3.6158,10.773516000061035,3.601671,10.771486000061035Q3.587542,10.769466000061035,3.5738440000000002,10.765446000061035Q3.560146,10.761436000061035,3.547157,10.755516000061036Q3.534167,10.749606000061036,3.52215,10.741896000061034Q3.5101329999999997,10.734196000061035,3.499331,10.724866000061034Q3.48853,10.715536000061036,3.479163,10.704766000061035Q3.469796,10.693996000061036,3.462053,10.682006000061035Q3.454311,10.670016000061036,3.44835,10.657046000061035Q3.442389,10.644076000061036,3.43833,10.630386000061035Q3.434271,10.616706000061034,3.432197,10.602576000061035Q3.430123,10.588456000061035,3.430075,10.574186000061035Q3.430028,10.559916000061035,3.432008,10.545776000061036Q3.433988,10.531636000061035,3.437955,10.517926000061035Q3.441922,10.504216000061035,3.447797,10.491206000061036Q3.453671,10.478196000061036,3.4613329999999998,10.466156000061035Q3.468996,10.454116000061035,3.47829,10.443276000061035L4.30321,9.481881000061035Q4.3172999999999995,9.465470000061035,4.33456,9.452447000061035Q4.35182,9.439425000061036,4.37147,9.430394000061035Q4.39112,9.421363000061035,4.41225,9.416741000061036Q4.43337,9.412118000061035,4.455,9.412118000061035Q4.4766200000000005,9.412118000061035,4.49775,9.416741000061036Q4.51887,9.421363000061035,4.53852,9.430394000061035Q4.5581700000000005,9.439425000061036,4.57543,9.452447000061035Q4.5927,9.465470000061035,4.6067800000000005,9.481881000061035L5.4317,10.443276000061035Z"
                                    fill="#14C9BB"
                                    fillOpacity="1"
                                />
                                <path
                                    d="M4.07873,10.773516000061035L4.13776,10.773516000061035Q4.1064,10.765436000061035,4.07582,10.755656000061036Q4.02566,10.429006000061035,4.18957,10.072372000061035Q4.29716,9.838297000061035,4.455,9.682623000061035Q4.61283,9.838297000061035,4.72042,10.072373000061035Q4.88434,10.429006000061035,4.83417,10.755656000061036Q4.80359,10.765436000061035,4.77224,10.773516000061035L5.279920000000001,10.773516000061035Q5.338509999999999,10.773516000061035,5.387840000000001,10.741896000061034Q5.43717,10.710286000061036,5.46164,10.657046000061035Q5.48611,10.603806000061034,5.47799,10.545776000061036Q5.469860000000001,10.487746000061035,5.4317,10.443276000061035L4.6495,9.531674000061034L4.6067800000000005,9.481881000061035Q4.54692,9.412118000061035,4.455,9.412118000061035Q4.3630700000000004,9.412118000061035,4.30321,9.481881000061035L4.26049,9.531674000061034L3.47829,10.443276000061035Q3.440135,10.487746000061035,3.432008,10.545776000061036Q3.42388,10.603806000061034,3.44835,10.657046000061035Q3.47282,10.710286000061036,3.52215,10.741896000061034Q3.571481,10.773516000061035,3.630074,10.773516000061035L4.07873,10.773516000061035Z"
                                    fillRule="evenodd"
                                    fill="#14C9BB"
                                    fillOpacity="1"
                                />
                            </g>
                            <g>
                                <path
                                    d="M3.455292500513077,12.031557608642578L3.242499500513077,12.031557608642578L3.242499500513077,13.431557608642578L3.455292500513077,13.431557608642578Q4.161747500513076,13.431557608642578,4.661349500513077,12.923527608642578Q5.154999500513076,12.421547608642578,5.154999500513076,11.719643608642578L5.154999500513076,10.773517608642578L3.7549965005130765,10.773517608642578L3.7549975005130767,11.719643608642578Q3.7549975005130767,12.031557608642578,3.455292500513077,12.031557608642578Z"
                                    fillRule="evenodd"
                                    fill="#14C9BB"
                                    fillOpacity="1"
                                />
                            </g>
                        </g>
                    </g>
                </g>
            </g>
        </g>
    </svg>
);

export const switchGroupIcon = () => (
    <svg xmlns="http://www.w3.org/2000/svg" fill="none" version="1.1" width="16" height="16" viewBox="0 0 16 16">
        <g>
            <g />
            <g>
                <rect
                    x="1.7727290987968445"
                    y="1.699999988079071"
                    width="12.600000023841858"
                    height="12.600000023841858"
                    rx="1.300000011920929"
                    fillOpacity="0"
                    strokeOpacity="1"
                    stroke="#14C9BB"
                    fill="none"
                    strokeWidth="1.399999976158142"
                />
            </g>
            <g transform="matrix(0.7071067690849304,0.7071067690849304,-0.7071067690849304,0.7071067690849304,3.071067896907607,-5.4142134513208475)">
                <g>
                    <g>
                        <rect
                            x="12.65822458267212"
                            y="2.0091745853424072"
                            width="0.8256882429122925"
                            height="2.56880784034729"
                            rx="0"
                            fill="#14C9BB"
                            fillOpacity="1"
                        />
                        <rect
                            x="12.65822458267212"
                            y="2.0091745853424072"
                            width="0.8256882429122925"
                            height="2.56880784034729"
                            rx="0"
                            fillOpacity="0"
                            strokeOpacity="1"
                            stroke="#14C9BB"
                            fill="none"
                            strokeWidth="0.5"
                        />
                    </g>
                    <g>
                        <path
                            d="M14.037179997329712,2.18755Q14.046089997329712,2.19849,14.053369997329712,2.21058Q14.060649997329712,2.2226600000000003,14.066159997329713,2.2356499999999997Q14.071669997329712,2.24864,14.075299997329711,2.26227Q14.078929997329713,2.27591,14.080609997329713,2.28992Q14.082299997329713,2.3039300000000003,14.081989997329712,2.3180300000000003Q14.081689997329711,2.33214,14.079409997329712,2.34606Q14.077139997329713,2.35999,14.072919997329713,2.37345Q14.068709997329712,2.38692,14.062649997329713,2.39966Q14.056589997329713,2.4124,14.048809997329712,2.42417Q14.041019997329713,2.43593,14.031649997329712,2.44649Q14.022289997329711,2.45704,14.011539997329713,2.46618Q14.000789997329711,2.47531,13.988859997329712,2.48285Q13.976929997329712,2.49038,13.964059997329713,2.49617Q13.951189997329712,2.50196,13.937639997329711,2.5058800000000003Q13.924089997329713,2.5098000000000003,13.910119997329712,2.51178Q13.896149997329712,2.51376,13.882039997329713,2.51376L12.260096997329711,2.51376Q12.245987997329712,2.51376,12.232018997329712,2.51178Q12.218048997329712,2.5098000000000003,12.204495997329712,2.5058800000000003Q12.190942997329712,2.50196,12.178074997329713,2.49617Q12.165206997329712,2.49038,12.153278997329712,2.48285Q12.141350997329711,2.47531,12.130597997329712,2.46618Q12.119845997329712,2.45704,12.110482997329711,2.44649Q12.101119997329713,2.43593,12.093330997329712,2.42417Q12.085542997329712,2.4124,12.079482997329713,2.39966Q12.073422997329711,2.38692,12.069211997329711,2.37345Q12.065000997329712,2.35999,12.062722997329711,2.34606Q12.060443997329712,2.33214,12.060142997329711,2.3180300000000003Q12.059841997329713,2.3039300000000003,12.061523997329711,2.28992Q12.063205997329712,2.27591,12.066837997329712,2.26227Q12.070469997329711,2.24864,12.075980997329712,2.2356499999999997Q12.081490997329713,2.2226600000000003,12.088769997329711,2.21058Q12.096048997329712,2.19849,12.104952997329711,2.18755L12.915919997329713,1.190702Q12.929989997329711,1.173413,12.947519997329712,1.159645Q12.965039997329711,1.145876,12.985169997329711,1.136304Q13.005299997329711,1.126731,13.027039997329712,1.121825Q13.048779997329712,1.116918,13.071069997329712,1.116918Q13.093359997329712,1.116918,13.115099997329711,1.121825Q13.136839997329712,1.126731,13.156969997329712,1.136304Q13.177089997329713,1.145876,13.194619997329712,1.159645Q13.212149997329712,1.173413,13.226209997329711,1.190702L14.037179997329712,2.18755Z"
                            fill="#14C9BB"
                            fillOpacity="1"
                        />
                        <path
                            d="M14.231109997329712,2.0297799999999997Q14.410519997329711,2.2503,14.288419997329711,2.5070300000000003Q14.166319997329712,2.76376,13.882039997329713,2.76376L12.260096997329711,2.76376Q11.975811997329712,2.76376,11.853714997329712,2.5070300000000003Q11.731617697329712,2.2503,11.911023997329712,2.0297799999999997L12.721989997329711,1.032933Q12.857049997329712,0.866918,13.071069997329712,0.866918Q13.285079997329712,0.866918,13.420139997329713,1.032933L14.231109997329712,2.0297799999999997L14.231109997329712,2.0297799999999997ZM13.843249997329712,2.34532L13.032279997329711,1.3484720000000001Q13.047289997329711,1.366918,13.071069997329712,1.366918Q13.094849997329712,1.366918,13.109849997329713,1.3484720000000001L12.298882997329711,2.34532Q12.318816997329712,2.32081,12.305250997329711,2.29229Q12.291684997329712,2.26376,12.260096997329711,2.26376L13.882039997329713,2.26376Q13.850449997329711,2.26376,13.836889997329711,2.29229Q13.823319997329712,2.32081,13.843249997329712,2.34532L13.843249997329712,2.34532Z"
                            fill="#14C9BB"
                            fillOpacity="1"
                        />
                    </g>
                </g>
                <g transform="matrix(1,0,0,-1,0,22)">
                    <g>
                        <rect
                            x="12.65822458267212"
                            y="12.009174585342407"
                            width="0.8256882429122925"
                            height="2.56880784034729"
                            rx="0"
                            fill="#14C9BB"
                            fillOpacity="1"
                        />
                        <rect
                            x="12.65822458267212"
                            y="12.009174585342407"
                            width="0.8256882429122925"
                            height="2.56880784034729"
                            rx="0"
                            fillOpacity="0"
                            strokeOpacity="1"
                            stroke="#14C9BB"
                            fill="none"
                            strokeWidth="0.5"
                        />
                    </g>
                    <g>
                        <path
                            d="M14.037179997329712,12.18755Q14.046089997329712,12.19849,14.053369997329712,12.21058Q14.060649997329712,12.22266,14.066159997329713,12.23565Q14.071669997329712,12.24864,14.075299997329711,12.262270000000001Q14.078929997329713,12.27591,14.080609997329713,12.28992Q14.082299997329713,12.30393,14.081989997329712,12.31803Q14.081689997329711,12.33214,14.079409997329712,12.34606Q14.077139997329713,12.35999,14.072919997329713,12.37345Q14.068709997329712,12.38692,14.062649997329713,12.39966Q14.056589997329713,12.4124,14.048809997329712,12.42417Q14.041019997329713,12.435929999999999,14.031649997329712,12.44649Q14.022289997329711,12.45704,14.011539997329713,12.46618Q14.000789997329711,12.47531,13.988859997329712,12.48285Q13.976929997329712,12.49038,13.964059997329713,12.49617Q13.951189997329712,12.50196,13.937639997329711,12.50588Q13.924089997329713,12.5098,13.910119997329712,12.51178Q13.896149997329712,12.51376,13.882039997329713,12.51376L12.260096997329711,12.51376Q12.245987997329712,12.51376,12.232018997329712,12.51178Q12.218048997329712,12.5098,12.204495997329712,12.50588Q12.190942997329712,12.50196,12.178074997329713,12.49617Q12.165206997329712,12.49038,12.153278997329712,12.48285Q12.141350997329711,12.47531,12.130597997329712,12.46618Q12.119845997329712,12.45704,12.110482997329711,12.44649Q12.101119997329713,12.435929999999999,12.093330997329712,12.42417Q12.085542997329712,12.4124,12.079482997329713,12.39966Q12.073422997329711,12.38692,12.069211997329711,12.37345Q12.065000997329712,12.35999,12.062722997329711,12.34606Q12.060443997329712,12.33214,12.060142997329711,12.31803Q12.059841997329713,12.30393,12.061523997329711,12.28992Q12.063205997329712,12.27591,12.066837997329712,12.262270000000001Q12.070469997329711,12.24864,12.075980997329712,12.23565Q12.081490997329713,12.22266,12.088769997329711,12.21058Q12.096048997329712,12.19849,12.104952997329711,12.18755L12.915919997329713,11.190702Q12.929989997329711,11.173413,12.947519997329712,11.159645Q12.965039997329711,11.145876,12.985169997329711,11.136304Q13.005299997329711,11.126731,13.027039997329712,11.121825Q13.048779997329712,11.116918,13.071069997329712,11.116918Q13.093359997329712,11.116918,13.115099997329711,11.121825Q13.136839997329712,11.126731,13.156969997329712,11.136304Q13.177089997329713,11.145876,13.194619997329712,11.159645Q13.212149997329712,11.173413,13.226209997329711,11.190702L14.037179997329712,12.18755Z"
                            fill="#14C9BB"
                            fillOpacity="1"
                        />
                        <path
                            d="M14.231109997329712,12.02978Q14.410519997329711,12.2503,14.288419997329711,12.50703Q14.166319997329712,12.76376,13.882039997329713,12.76376L12.260096997329711,12.76376Q11.975811997329712,12.76376,11.853714997329712,12.50703Q11.731617697329712,12.2503,11.911023997329712,12.02978L12.721989997329711,11.032933Q12.857049997329712,10.866918,13.071069997329712,10.866918Q13.285079997329712,10.866918,13.420139997329713,11.032933L14.231109997329712,12.02978L14.231109997329712,12.02978ZM13.843249997329712,12.345320000000001L13.032279997329711,11.348472Q13.047289997329711,11.366918,13.071069997329712,11.366918Q13.094849997329712,11.366918,13.109849997329713,11.348472L12.298882997329711,12.345320000000001Q12.318816997329712,12.32081,12.305250997329711,12.29229Q12.291684997329712,12.26376,12.260096997329711,12.26376L13.882039997329713,12.26376Q13.850449997329711,12.26376,13.836889997329711,12.29229Q13.823319997329712,12.32081,13.843249997329712,12.345320000000001L13.843249997329712,12.345320000000001Z"
                            fill="#14C9BB"
                            fillOpacity="1"
                        />
                    </g>
                </g>
                <g transform="matrix(0,-1,-1,0,25.49308729171753,25.49308729171753)">
                    <g>
                        <rect
                            x="19.080242395401"
                            y="8.431194067001343"
                            width="0.8256882429122925"
                            height="2.56880784034729"
                            rx="0"
                            fill="#14C9BB"
                            fillOpacity="1"
                        />
                        <rect
                            x="19.080242395401"
                            y="8.431194067001343"
                            width="0.8256882429122925"
                            height="2.56880784034729"
                            rx="0"
                            fillOpacity="0"
                            strokeOpacity="1"
                            stroke="#14C9BB"
                            fill="none"
                            strokeWidth="0.5"
                        />
                    </g>
                    <g>
                        <path
                            d="M20.459197810058594,8.609569481658935Q20.468107810058594,8.620509481658935,20.475387810058592,8.632599481658936Q20.482667810058594,8.644679481658935,20.488177810058595,8.657669481658935Q20.493687810058596,8.670659481658936,20.497317810058593,8.684289481658936Q20.500947810058594,8.697929481658935,20.502627810058595,8.711939481658936Q20.504317810058595,8.725949481658935,20.504007810058592,8.740049481658936Q20.503707810058593,8.754159481658936,20.501427810058594,8.768079481658935Q20.499157810058595,8.782009481658935,20.494937810058595,8.795469481658936Q20.490727810058594,8.808939481658935,20.484667810058593,8.821679481658936Q20.478607810058595,8.834419481658935,20.470827810058594,8.846189481658936Q20.463037810058594,8.857949481658935,20.453667810058594,8.868509481658936Q20.444307810058593,8.879059481658935,20.433557810058595,8.888199481658935Q20.422807810058593,8.897329481658936,20.410877810058594,8.904869481658935Q20.398947810058594,8.912399481658936,20.386077810058595,8.918189481658935Q20.373207810058595,8.923979481658936,20.359657810058593,8.927899481658935Q20.346107810058594,8.931819481658936,20.332137810058594,8.933799481658935Q20.318167810058593,8.935779481658935,20.304057810058595,8.935779481658935L18.682114810058593,8.935779481658935Q18.668005810058595,8.935779481658935,18.654036810058592,8.933799481658935Q18.640066810058595,8.931819481658936,18.626513810058594,8.927899481658935Q18.612960810058595,8.923979481658936,18.600092810058594,8.918189481658935Q18.587224810058594,8.912399481658936,18.575296810058592,8.904869481658935Q18.563368810058595,8.897329481658936,18.552615810058594,8.888199481658935Q18.541863810058594,8.879059481658935,18.532500810058593,8.868509481658936Q18.523137810058593,8.857949481658935,18.515348810058594,8.846189481658936Q18.507560810058592,8.834419481658935,18.501500810058594,8.821679481658936Q18.495440810058593,8.808939481658935,18.491229810058595,8.795469481658936Q18.487018810058593,8.782009481658935,18.484740810058593,8.768079481658935Q18.482461810058595,8.754159481658936,18.482160810058595,8.740049481658936Q18.481859810058594,8.725949481658935,18.483541810058593,8.711939481658936Q18.485223810058592,8.697929481658935,18.488855810058595,8.684289481658936Q18.492487810058595,8.670659481658936,18.497998810058593,8.657669481658935Q18.503508810058594,8.644679481658935,18.510787810058595,8.632599481658936Q18.518066810058595,8.620509481658935,18.526970810058593,8.609569481658935L19.337937810058595,7.6127214816589355Q19.352007810058595,7.595432481658936,19.369537810058596,7.581664481658936Q19.387057810058593,7.567895481658936,19.407187810058595,7.5583234816589355Q19.427317810058593,7.548750481658936,19.449057810058594,7.543844481658936Q19.470797810058592,7.538937481658936,19.493087810058594,7.538937481658936Q19.515377810058595,7.538937481658936,19.537117810058593,7.543844481658936Q19.558857810058594,7.548750481658936,19.578987810058592,7.5583234816589355Q19.599107810058594,7.567895481658936,19.616637810058595,7.581664481658936Q19.634167810058592,7.595432481658936,19.648227810058593,7.6127214816589355L20.459197810058594,8.609569481658935Z"
                            fill="#14C9BB"
                            fillOpacity="1"
                        />
                        <path
                            d="M20.653127810058592,8.451799481658936Q20.832537810058593,8.672319481658935,20.710437810058593,8.929049481658936Q20.588337810058594,9.185779481658935,20.304057810058595,9.185779481658935L18.682114810058593,9.185779481658935Q18.397829810058592,9.185779481658935,18.275732810058592,8.929049481658936Q18.153635510058592,8.672319481658935,18.333041810058592,8.451799481658936L19.144007810058593,7.454952481658935Q19.279067810058592,7.288937481658936,19.493087810058594,7.288937481658936Q19.707097810058595,7.288937481658936,19.842157810058595,7.454952481658935L20.653127810058592,8.451799481658936L20.653127810058592,8.451799481658936ZM20.265267810058592,8.767339481658937L19.454297810058595,7.770491481658936Q19.469307810058595,7.788937481658936,19.493087810058594,7.788937481658936Q19.516867810058592,7.788937481658936,19.531867810058593,7.770491481658936L18.720900810058595,8.767339481658937Q18.740834810058594,8.742829481658935,18.727268810058593,8.714309481658935Q18.713702810058592,8.685779481658935,18.682114810058593,8.685779481658935L20.304057810058595,8.685779481658935Q20.272467810058593,8.685779481658935,20.258907810058595,8.714309481658935Q20.245337810058594,8.742829481658935,20.265267810058592,8.767339481658937L20.265267810058592,8.767339481658937Z"
                            fill="#14C9BB"
                            fillOpacity="1"
                        />
                    </g>
                </g>
                <g transform="matrix(0,1,1,0,3.4930856227874756,-3.4930856227874756)">
                    <g>
                        <rect
                            x="9.080242395401001"
                            y="5.587156772613525"
                            width="0.8256882429122925"
                            height="2.56880784034729"
                            rx="0"
                            fill="#14C9BB"
                            fillOpacity="1"
                        />
                        <rect
                            x="9.080242395401001"
                            y="5.587156772613525"
                            width="0.8256882429122925"
                            height="2.56880784034729"
                            rx="0"
                            fillOpacity="0"
                            strokeOpacity="1"
                            stroke="#14C9BB"
                            fill="none"
                            strokeWidth="0.5"
                        />
                    </g>
                    <g>
                        <path
                            d="M10.459197810058594,5.765532187271118Q10.468107810058594,5.776472187271118,10.475387810058594,5.788562187271118Q10.482667810058594,5.8006421872711185,10.488177810058595,5.813632187271118Q10.493687810058594,5.826622187271118,10.497317810058593,5.840252187271118Q10.500947810058594,5.853892187271118,10.502627810058595,5.867902187271119Q10.504317810058595,5.881912187271118,10.504007810058594,5.896012187271118Q10.503707810058593,5.910122187271118,10.501427810058594,5.924042187271118Q10.499157810058595,5.937972187271118,10.494937810058595,5.951432187271118Q10.490727810058594,5.964902187271118,10.484667810058594,5.977642187271118Q10.478607810058595,5.990382187271118,10.470827810058594,6.002152187271118Q10.463037810058594,6.013912187271118,10.453667810058594,6.024472187271118Q10.444307810058593,6.035022187271118,10.433557810058595,6.044162187271118Q10.422807810058593,6.0532921872711185,10.410877810058594,6.060832187271118Q10.398947810058594,6.068362187271118,10.386077810058595,6.074152187271118Q10.373207810058593,6.079942187271119,10.359657810058593,6.0838621872711185Q10.346107810058594,6.087782187271118,10.332137810058594,6.089762187271118Q10.318167810058593,6.091742187271118,10.304057810058595,6.091742187271118L8.682114810058593,6.091742187271118Q8.668005810058593,6.091742187271118,8.654036810058594,6.089762187271118Q8.640066810058594,6.087782187271118,8.626513810058594,6.0838621872711185Q8.612960810058594,6.079942187271119,8.600092810058594,6.074152187271118Q8.587224810058594,6.068362187271118,8.575296810058594,6.060832187271118Q8.563368810058593,6.0532921872711185,8.552615810058594,6.044162187271118Q8.541863810058594,6.035022187271118,8.532500810058593,6.024472187271118Q8.523137810058595,6.013912187271118,8.515348810058594,6.002152187271118Q8.507560810058594,5.990382187271118,8.501500810058594,5.977642187271118Q8.495440810058593,5.964902187271118,8.491229810058593,5.951432187271118Q8.487018810058593,5.937972187271118,8.484740810058593,5.924042187271118Q8.482461810058593,5.910122187271118,8.482160810058593,5.896012187271118Q8.481859810058594,5.881912187271118,8.483541810058593,5.867902187271119Q8.485223810058594,5.853892187271118,8.488855810058594,5.840252187271118Q8.492487810058593,5.826622187271118,8.497998810058593,5.813632187271118Q8.503508810058594,5.8006421872711185,8.510787810058593,5.788562187271118Q8.518066810058594,5.776472187271118,8.526970810058593,5.765532187271118L9.337937810058595,4.768684187271118Q9.352007810058593,4.751395187271118,9.369537810058594,4.737627187271118Q9.387057810058593,4.7238581872711185,9.407187810058593,4.714286187271118Q9.427317810058593,4.7047131872711185,9.449057810058594,4.6998071872711185Q9.470797810058594,4.694900187271118,9.493087810058594,4.694900187271118Q9.515377810058594,4.694900187271118,9.537117810058593,4.6998071872711185Q9.558857810058594,4.7047131872711185,9.578987810058594,4.714286187271118Q9.599107810058594,4.7238581872711185,9.616637810058593,4.737627187271118Q9.634167810058594,4.751395187271118,9.648227810058593,4.768684187271118L10.459197810058594,5.765532187271118Z"
                            fill="#14C9BB"
                            fillOpacity="1"
                        />
                        <path
                            d="M10.653127810058594,5.607762187271118Q10.832537810058593,5.828282187271118,10.710437810058593,6.0850121872711185Q10.588337810058594,6.341742187271118,10.304057810058595,6.341742187271118L8.682114810058593,6.341742187271118Q8.397829810058594,6.341742187271118,8.275732810058594,6.0850121872711185Q8.153635510058594,5.828282187271118,8.333041810058594,5.607762187271118L9.144007810058593,4.610915187271118Q9.279067810058594,4.444900187271118,9.493087810058594,4.444900187271118Q9.707097810058594,4.444900187271118,9.842157810058595,4.610915187271118L10.653127810058594,5.607762187271118L10.653127810058594,5.607762187271118ZM10.265267810058594,5.923302187271118L9.454297810058593,4.926454187271118Q9.469307810058593,4.944900187271118,9.493087810058594,4.944900187271118Q9.516867810058594,4.944900187271118,9.531867810058595,4.926454187271118L8.720900810058593,5.923302187271118Q8.740834810058594,5.898792187271118,8.727268810058593,5.870272187271118Q8.713702810058594,5.841742187271118,8.682114810058593,5.841742187271118L10.304057810058595,5.841742187271118Q10.272467810058593,5.841742187271118,10.258907810058593,5.870272187271118Q10.245337810058594,5.898792187271118,10.265267810058594,5.923302187271118L10.265267810058594,5.923302187271118Z"
                            fill="#14C9BB"
                            fillOpacity="1"
                        />
                    </g>
                </g>
                <g>
                    <path
                        d="M14.649150543289185,6.624752733230591Q14.768320543289185,6.32374273323059,14.768320543289185,6.000002733230591Q14.768320543289185,5.67626273323059,14.649150543289185,5.375252733230591Q14.519760543289184,5.048420733230591,14.271200543289185,4.79986473323059Q14.022650543289185,4.551308733230591,13.695820543289184,4.421919733230591Q13.394810543289184,4.302752733230591,13.071070543289185,4.302752733230591Q12.747330543289184,4.302752733230591,12.446320543289184,4.421919733230591Q12.119488543289185,4.551308733230591,11.870932543289184,4.79986473323059Q11.622376543289185,5.048421733230591,11.492987543289184,5.375252733230591Q11.373820543289185,5.67626273323059,11.373820543289185,6.000002733230591Q11.373820543289185,6.32374273323059,11.492987543289184,6.624752733230591Q11.622376543289185,6.951582733230591,11.870932543289184,7.200132733230591Q12.119489543289184,7.44869273323059,12.446320543289184,7.578082733230591Q12.747330543289184,7.697252733230591,13.071070543289185,7.697252733230591Q13.394810543289184,7.697252733230591,13.695820543289184,7.578082733230591Q14.022650543289185,7.44869273323059,14.271200543289185,7.200132733230591Q14.519760543289184,6.951582733230591,14.649150543289185,6.624752733230591ZM13.966730543289184,5.645412733230591Q14.034370543289185,5.816252733230591,14.034370543289185,6.000002733230591Q14.034370543289185,6.1837427332305905,13.966730543289184,6.354582733230591Q13.893300543289184,6.540082733230591,13.752230543289185,6.681162733230591Q13.611150543289185,6.8222327332305905,13.425650543289184,6.895662733230591Q13.254810543289185,6.9633027332305915,13.071070543289185,6.9633027332305915Q12.887320543289185,6.9633027332305915,12.716480543289185,6.895662733230591Q12.530980543289184,6.8222327332305905,12.389910543289185,6.681162733230591Q12.248838543289185,6.540082733230591,12.175400543289184,6.354582733230591Q12.107765543289185,6.1837427332305905,12.107765543289185,6.000002733230591Q12.107765543289185,5.816252733230591,12.175400543289184,5.645412733230591Q12.248837543289184,5.459912733230591,12.389910543289185,5.318842733230591Q12.530980543289184,5.1777697332305905,12.716480543289185,5.10433273323059Q12.887320543289185,5.036697733230591,13.071070543289185,5.036697733230591Q13.254810543289185,5.036697733230591,13.425650543289184,5.10433273323059Q13.611150543289185,5.177770733230591,13.752230543289185,5.318842733230591Q13.893300543289184,5.459912733230591,13.966730543289184,5.645412733230591Z"
                        fillRule="evenodd"
                        fill="#14C9BB"
                        fillOpacity="1"
                    />
                    <path
                        d="M13.787840543289185,7.810532733230591Q14.162810543289185,7.662082733230591,14.447980543289184,7.376912733230591Q14.733150543289184,7.091742733230591,14.881600543289185,6.716772733230591Q15.018320543289185,6.371422733230591,15.018320543289185,6.000002733230591Q15.018320543289185,5.628572733230591,14.881600543289185,5.283228733230591Q14.733150543289184,4.9082557332305905,14.447980543289184,4.623088733230591Q14.162810543289185,4.337920333230591,13.787840543289185,4.189472733230591Q13.442490543289185,4.052752733230591,13.071070543289185,4.052752733230591Q12.699640543289185,4.052752733230591,12.354296543289184,4.189472733230591Q11.979323543289185,4.337920633230591,11.694156543289184,4.623088733230591Q11.408988343289185,4.9082557332305905,11.260540543289185,5.283228733230591Q11.123820543289185,5.628572733230591,11.123820543289185,6.000002733230591Q11.123820543289185,6.371422733230591,11.260540543289185,6.716772733230591Q11.408988443289184,7.091742733230591,11.694156543289184,7.376912733230591Q11.979324543289184,7.662082733230591,12.354296543289184,7.810532733230591Q12.699640543289185,7.947252733230591,13.071070543289185,7.947252733230591Q13.442490543289185,7.947252733230591,13.787840543289185,7.810532733230591ZM14.416700543289185,6.532722733230591Q14.306370543289184,6.811412733230591,14.094430543289185,7.0233627332305915Q13.882480543289184,7.235302733230591,13.603790543289184,7.345632733230591Q13.347120543289185,7.447252733230591,13.071070543289185,7.447252733230591Q12.795010543289184,7.447252733230591,12.538340543289184,7.345632733230591Q12.259654543289184,7.235302733230591,12.047709543289184,7.0233627332305915Q11.835765543289185,6.811412733230591,11.725434543289184,6.532722733230591Q11.623820543289185,6.276052733230591,11.623820543289185,6.000002733230591Q11.623820543289185,5.723942733230591,11.725434543289184,5.46727273323059Q11.835764543289185,5.188586733230591,12.047709543289184,4.976641733230591Q12.259653543289184,4.764697733230591,12.538340543289184,4.6543667332305905Q12.795010543289184,4.552752733230591,13.071070543289185,4.552752733230591Q13.347120543289185,4.552752733230591,13.603790543289184,4.6543667332305905Q13.882480543289184,4.764697733230591,14.094430543289185,4.976641733230591Q14.306370543289184,5.188586733230591,14.416700543289185,5.46727273323059Q14.518320543289185,5.723942733230591,14.518320543289185,6.000002733230591Q14.518320543289185,6.276052733230591,14.416700543289185,6.532722733230591ZM14.199180543289184,5.553392733230591Q14.106690543289185,5.31975273323059,13.929000543289185,5.142065733230591Q13.751320543289184,4.96438173323059,13.517680543289185,4.871885733230591Q13.302500543289185,4.786697733230591,13.071070543289185,4.786697733230591Q12.839640543289185,4.786697733230591,12.624460543289185,4.871885733230591Q12.390820543289184,4.96438173323059,12.213133543289185,5.142065733230591Q12.035449543289184,5.31975273323059,11.942953543289185,5.553392733230591Q11.857765543289185,5.768572733230591,11.857765543289185,6.000002733230591Q11.857765543289185,6.231432733230591,11.942953543289185,6.446612733230591Q12.035449543289184,6.68025273323059,12.213133543289185,6.857932733230591Q12.390820543289184,7.035622733230591,12.624460543289185,7.128112733230591Q12.839640543289185,7.2133027332305915,13.071070543289185,7.2133027332305915Q13.302500543289185,7.2133027332305915,13.517680543289185,7.128112733230591Q13.751320543289184,7.035622733230591,13.929000543289185,6.857932733230591Q14.106690543289185,6.68025273323059,14.199180543289184,6.446612733230591Q14.284370543289185,6.231432733230591,14.284370543289185,6.000002733230591Q14.284370543289185,5.768572733230591,14.199180543289184,5.553392733230591ZM13.333630543289186,5.33678273323059Q13.470990543289185,5.3911627332305905,13.575450543289184,5.495622733230591Q13.679910543289186,5.600082733230591,13.734290543289184,5.737432733230591Q13.784370543289185,5.863942733230591,13.784370543289185,6.000002733230591Q13.784370543289185,6.136062733230591,13.734290543289184,6.262562733230591Q13.679910543289186,6.399922733230591,13.575450543289184,6.504382733230591Q13.470990543289185,6.608842733230591,13.333630543289186,6.66322273323059Q13.207130543289185,6.7133027332305915,13.071070543289185,6.7133027332305915Q12.935010543289184,6.7133027332305915,12.808510543289184,6.66322273323059Q12.671150543289185,6.608842733230591,12.566690543289184,6.504382733230591Q12.462230543289184,6.399922733230591,12.407850543289184,6.262562733230591Q12.357765543289185,6.136062733230591,12.357765543289185,6.000002733230591Q12.357765543289185,5.863942733230591,12.407850543289184,5.737432733230591Q12.462230543289184,5.600082733230591,12.566690543289184,5.495622733230591Q12.671150543289185,5.3911627332305905,12.808500543289185,5.33678273323059Q12.935010543289184,5.286697733230591,13.071070543289185,5.286697733230591Q13.207130543289185,5.286697733230591,13.333630543289186,5.33678273323059Z"
                        fillRule="evenodd"
                        fill="#14C9BB"
                        fillOpacity="1"
                    />
                </g>
            </g>
        </g>
    </svg>
);

export const otnDeviceIcon = () => (
    <svg xmlns="http://www.w3.org/2000/svg" fill="none" version="1.1" width="16" height="16" viewBox="0 0 16 16">
        <g>
            <g />
            <g>
                <g>
                    <path
                        d="M13.11350011920929,2.7499876022338867C13.187000119209289,2.7499876022338867,13.25000011920929,2.8129876022338864,13.25000011920929,2.8864876022338866L13.25000011920929,5.403487602233887C13.25000011920929,5.476987602233887,13.187000119209289,5.539987602233887,13.11350011920929,5.539987602233887L2.8865001192092894,5.539987602233887C2.8130001192092893,5.539987602233887,2.7500001192092896,5.476987602233887,2.7500001192092896,5.403487602233887L2.7500001192092896,2.8864876022338866C2.7500001192092896,2.8129876022338864,2.8130001192092893,2.7499876022338867,2.8865001192092894,2.7499876022338867L13.11350011920929,2.7499876022338867ZM13.11350011920929,1.9999876022338867L2.8865001192092894,1.9999876022338867C2.3990001192092896,1.9999876022338867,2.0000001192092896,2.3989876022338867,2.0000001192092896,2.8864876022338866L2.0000001192092896,5.403487602233887C2.0000001192092896,5.890987602233887,2.3990001192092896,6.289987602233887,2.8865001192092894,6.289987602233887L13.11350011920929,6.289987602233887C13.60100011920929,6.289987602233887,14.00000011920929,5.890987602233887,14.00000011920929,5.403487602233887L14.00000011920929,2.8864876022338866C14.00000011920929,2.3989876022338867,13.60100011920929,1.9999877166748867,13.11350011920929,1.9999876022338867Z"
                        fill="#14C9BB"
                        fillOpacity="1"
                    />
                    <path
                        d="M14.15000011920929,5.403487602233887L14.15000011920929,2.8864876022338866Q14.15000011920929,2.458730602233887,13.845600119209289,2.1543596022338867Q13.54130011920929,1.8499876022338868,13.11350011920929,1.8499876022338868L2.8865001192092894,1.8499876022338868Q2.4587431192092897,1.8499876022338868,2.1543711192092894,2.1543596022338867Q1.8500001192092896,2.458730602233887,1.8500001192092896,2.8864876022338866L1.8500001192092896,5.403487602233887Q1.8500001192092896,5.831247602233887,2.1543711192092894,6.135617602233887Q2.4587421192092895,6.439987602233887,2.8865001192092894,6.439987602233887L13.11350011920929,6.439987602233887Q13.54130011920929,6.439987602233887,13.845600119209289,6.135617602233887Q14.15000011920929,5.831247602233887,14.15000011920929,5.403487602233887ZM13.63350011920929,2.3664916022338867Q13.85000011920929,2.582994602233887,13.85000011920929,2.8864876022338866L13.85000011920929,5.403487602233887Q13.85000011920929,5.706977602233886,13.63350011920929,5.923487602233887Q13.41700011920929,6.139987602233886,13.11350011920929,6.139987602233886L2.8865001192092894,6.139987602233886Q2.58300711920929,6.139987602233886,2.3665041192092895,5.923487602233887Q2.1500001192092895,5.706977602233886,2.1500001192092895,5.403487602233887L2.1500001192092895,2.8864876022338866Q2.1500001192092895,2.582994602233887,2.3665041192092895,2.3664916022338867Q2.58300711920929,2.1499876022338866,2.8865001192092894,2.1499876022338866L13.11350011920929,2.1499876022338866Q13.41700011920929,2.1499876022338866,13.63350011920929,2.3664916022338867ZM13.11350011920929,2.599987602233887L2.8865001192092894,2.599987602233887Q2.6000001192092896,2.599987602233887,2.6000001192092896,2.8864876022338866L2.6000001192092896,5.403487602233887Q2.6000001192092896,5.689987602233886,2.8865001192092894,5.689987602233886L13.11350011920929,5.689987602233886Q13.40000011920929,5.689987602233886,13.40000011920929,5.403487602233887L13.40000011920929,2.8864876022338866Q13.40000011920929,2.599987602233887,13.11350011920929,2.599987602233887ZM2.9000001192092895,2.8999876022338866L2.9000001192092895,5.389987602233887L13.10000011920929,5.389987602233887L13.10000011920929,2.8999876022338866L2.9000001192092895,2.8999876022338866Z"
                        fillRule="evenodd"
                        fill="#14C9BB"
                        fillOpacity="1"
                    />
                </g>
                <g>
                    <path
                        d="M4.67750011920929,3.742988348007202L3.7775001192092894,3.742988348007202L3.7775001192092894,4.6429883480072025L4.67750011920929,4.6429883480072025L4.67750011920929,3.742988348007202ZM6.22250011920929,3.742988348007202L5.322500119209289,3.742988348007202L5.322500119209289,4.6429883480072025L6.22250011920929,4.6429883480072025L6.22250011920929,3.742988348007202ZM12.41000011920929,3.742988348007202L9.185000119209288,3.742988348007202L9.185000119209288,4.6429883480072025L12.41000011920929,4.6429883480072025L12.41000011920929,3.742988348007202ZM13.11350011920929,6.7999883480072025C13.187000119209289,6.7999883480072025,13.25000011920929,6.862988348007202,13.25000011920929,6.936488348007202L13.25000011920929,9.453488348007202C13.25000011920929,9.526988348007201,13.187000119209289,9.589988348007203,13.11350011920929,9.589988348007203L2.8865001192092894,9.589988348007203C2.8130001192092893,9.589988348007203,2.7500001192092896,9.526988348007201,2.7500001192092896,9.453488348007202L2.7500001192092896,6.936488348007202C2.7500001192092896,6.862988348007202,2.8130001192092893,6.7999883480072025,2.8865001192092894,6.7999883480072025L13.11350011920929,6.7999883480072025ZM13.11350011920929,6.0499883480072025L2.8865001192092894,6.0499883480072025C2.3990001192092896,6.0499883480072025,2.0000001192092896,6.448988348007202,2.0000001192092896,6.936488348007202L2.0000001192092896,9.453488348007202C2.0000001192092896,9.940988348007203,2.3990001192092896,10.339988348007203,2.8865001192092894,10.339988348007203L13.11350011920929,10.339988348007203C13.60100011920929,10.339988348007203,14.00000011920929,9.940988348007203,14.00000011920929,9.453488348007202L14.00000011920929,6.936488348007202C14.00000011920929,6.448988348007202,13.60100011920929,6.0499883480072025,13.11350011920929,6.0499883480072025Z"
                        fill="#14C9BB"
                        fillOpacity="1"
                    />
                    <path
                        d="M3.6275001192092895,4.792988348007202L4.82750011920929,4.792988348007202L4.82750011920929,3.5929883480072022L3.6275001192092895,3.5929883480072022L3.6275001192092895,4.792988348007202ZM5.172500119209289,4.792988348007202L6.372500119209289,4.792988348007202L6.372500119209289,3.5929883480072022L5.172500119209289,3.5929883480072022L5.172500119209289,4.792988348007202ZM9.03500011920929,4.792988348007202L12.56000011920929,4.792988348007202L12.56000011920929,3.5929883480072022L9.03500011920929,3.5929883480072022L9.03500011920929,4.792988348007202ZM4.527500119209289,4.492988348007202L3.9275001192092898,4.492988348007202L3.9275001192092898,3.892988348007202L4.527500119209289,3.892988348007202L4.527500119209289,4.492988348007202ZM6.072500119209289,4.492988348007202L5.47250011920929,4.492988348007202L5.47250011920929,3.892988348007202L6.072500119209289,3.892988348007202L6.072500119209289,4.492988348007202ZM12.26000011920929,4.492988348007202L9.33500011920929,4.492988348007202L9.33500011920929,3.892988348007202L12.26000011920929,3.892988348007202L12.26000011920929,4.492988348007202ZM14.15000011920929,9.453488348007202L14.15000011920929,6.936488348007202Q14.15000011920929,6.508728348007202,13.845600119209289,6.204358348007203Q13.54130011920929,5.899988348007202,13.11350011920929,5.899988348007202L2.8865001192092894,5.899988348007202Q2.4587431192092897,5.899988348007202,2.1543711192092894,6.204358348007203Q1.8500001192092896,6.508728348007202,1.8500001192092896,6.936488348007202L1.8500001192092896,9.453488348007202Q1.8500001192092896,9.881248348007201,2.1543711192092894,10.185618348007203Q2.4587421192092895,10.489988348007202,2.8865001192092894,10.489988348007202L13.11350011920929,10.489988348007202Q13.54130011920929,10.489988348007202,13.845600119209289,10.185618348007203Q14.15000011920929,9.881248348007201,14.15000011920929,9.453488348007202ZM13.63350011920929,6.416488348007203Q13.85000011920929,6.632998348007202,13.85000011920929,6.936488348007202L13.85000011920929,9.453488348007202Q13.85000011920929,9.756978348007202,13.63350011920929,9.973488348007201Q13.41700011920929,10.189988348007201,13.11350011920929,10.189988348007201L2.8865001192092894,10.189988348007201Q2.58300711920929,10.189988348007201,2.3665031192092894,9.973488348007201Q2.1500001192092895,9.756978348007202,2.1500001192092895,9.453488348007202L2.1500001192092895,6.936488348007202Q2.1500001192092895,6.632998348007202,2.3665041192092895,6.416488348007203Q2.58300711920929,6.199988348007202,2.8865001192092894,6.199988348007202L13.11350011920929,6.199988348007202Q13.41700011920929,6.199988348007202,13.63350011920929,6.416488348007203ZM13.11350011920929,6.649988348007202L2.8865001192092894,6.649988348007202Q2.6000001192092896,6.649988348007202,2.6000001192092896,6.936488348007202L2.6000001192092896,9.453488348007202Q2.6000001192092896,9.570748348007202,2.6846211192092895,9.655368348007201Q2.7692431192092894,9.739988348007202,2.8865001192092894,9.739988348007202L13.11350011920929,9.739988348007202Q13.40000011920929,9.739988348007202,13.40000011920929,9.453488348007202L13.40000011920929,6.936488348007202Q13.40000011920929,6.649988348007202,13.11350011920929,6.649988348007202ZM2.9000001192092895,6.949988348007202L2.9000001192092895,9.439988348007201L13.10000011920929,9.439988348007201L13.10000011920929,6.949988348007202L2.9000001192092895,6.949988348007202Z"
                        fillRule="evenodd"
                        fill="#14C9BB"
                        fillOpacity="1"
                    />
                </g>
                <g>
                    <path
                        d="M4.67750011920929,7.792988300323486L3.7775001192092894,7.792988300323486L3.7775001192092894,8.692988300323487L4.67750011920929,8.692988300323487L4.67750011920929,7.792988300323486ZM6.22250011920929,7.792988300323486L5.322500119209289,7.792988300323486L5.322500119209289,8.692988300323487L6.22250011920929,8.692988300323487L6.22250011920929,7.792988300323486ZM12.41000011920929,7.792988300323486L9.185000119209288,7.792988300323486L9.185000119209288,8.692988300323487L12.41000011920929,8.692988300323487L12.41000011920929,7.792988300323486ZM13.11350011920929,10.849988300323487C13.187000119209289,10.849988300323487,13.25000011920929,10.912988300323487,13.25000011920929,10.986488300323487L13.25000011920929,13.503488300323486C13.25000011920929,13.576988300323485,13.187000119209289,13.639988300323488,13.11350011920929,13.639988300323488L2.8865001192092894,13.639988300323488C2.8130001192092893,13.639988300323488,2.7500001192092896,13.576988300323485,2.7500001192092896,13.503488300323486L2.7500001192092896,10.986488300323487C2.7500001192092896,10.912988300323487,2.8130001192092893,10.849988300323487,2.8865001192092894,10.849988300323487L13.11350011920929,10.849988300323487ZM13.11350011920929,10.099988300323487L2.8865001192092894,10.099988300323487C2.3990001192092896,10.099988300323487,2.0000001192092896,10.498988300323486,2.0000001192092896,10.986488300323487L2.0000001192092896,13.503488300323486C2.0000001192092896,13.990988300323487,2.3990001192092896,14.389988300323488,2.8865001192092894,14.389988300323488L13.11350011920929,14.389988300323488C13.60100011920929,14.389988300323488,14.00000011920929,13.990988300323487,14.00000011920929,13.503488300323486L14.00000011920929,10.986488300323487C14.00000011920929,10.498988300323486,13.60100011920929,10.099988300323487,13.11350011920929,10.099988300323487Z"
                        fill="#14C9BB"
                        fillOpacity="1"
                    />
                    <path
                        d="M3.6275001192092895,8.842988300323487L4.82750011920929,8.842988300323487L4.82750011920929,7.642988300323486L3.6275001192092895,7.642988300323486L3.6275001192092895,8.842988300323487ZM5.172500119209289,8.842988300323487L6.372500119209289,8.842988300323487L6.372500119209289,7.642988300323486L5.172500119209289,7.642988300323486L5.172500119209289,8.842988300323487ZM9.03500011920929,8.842988300323487L12.56000011920929,8.842988300323487L12.56000011920929,7.642988300323486L9.03500011920929,7.642988300323486L9.03500011920929,8.842988300323487ZM4.527500119209289,8.542988300323486L3.9275001192092898,8.542988300323486L3.9275001192092898,7.942988300323487L4.527500119209289,7.942988300323487L4.527500119209289,8.542988300323486ZM6.072500119209289,8.542988300323486L5.47250011920929,8.542988300323486L5.47250011920929,7.942988300323487L6.072500119209289,7.942988300323487L6.072500119209289,8.542988300323486ZM12.26000011920929,8.542988300323486L9.33500011920929,8.542988300323486L9.33500011920929,7.942988300323487L12.26000011920929,7.942988300323487L12.26000011920929,8.542988300323486ZM14.15000011920929,13.503488300323486L14.15000011920929,10.986488300323487Q14.15000011920929,10.558728300323487,13.845600119209289,10.254358300323487Q13.54130011920929,9.949988300323486,13.11350011920929,9.949988300323486L2.8865001192092894,9.949988300323486Q2.4587431192092897,9.949988300323486,2.1543711192092894,10.254358300323487Q1.8500001192092896,10.558728300323487,1.8500001192092896,10.986488300323487L1.8500001192092896,13.503488300323486Q1.8500001192092896,13.931248300323485,2.1543711192092894,14.235618300323488Q2.4587421192092895,14.539988300323486,2.8865001192092894,14.539988300323486L13.11350011920929,14.539988300323486Q13.54130011920929,14.539988300323486,13.845600119209289,14.235618300323488Q14.15000011920929,13.931248300323485,14.15000011920929,13.503488300323486ZM13.63350011920929,10.466488300323487Q13.85000011920929,10.682998300323487,13.85000011920929,10.986488300323487L13.85000011920929,13.503488300323486Q13.85000011920929,13.806978300323486,13.63350011920929,14.023488300323486Q13.41700011920929,14.239988300323486,13.11350011920929,14.239988300323486L2.8865001192092894,14.239988300323486Q2.58300711920929,14.239988300323486,2.3665031192092894,14.023488300323486Q2.1500001192092895,13.806978300323486,2.1500001192092895,13.503488300323486L2.1500001192092895,10.986488300323487Q2.1500001192092895,10.682998300323487,2.3665041192092895,10.466488300323487Q2.58300711920929,10.249988300323487,2.8865001192092894,10.249988300323487L13.11350011920929,10.249988300323487Q13.41700011920929,10.249988300323487,13.63350011920929,10.466488300323487ZM13.11350011920929,10.699988300323486L2.8865001192092894,10.699988300323486Q2.6000001192092896,10.699988300323486,2.6000001192092896,10.986488300323487L2.6000001192092896,13.503488300323486Q2.6000001192092896,13.620748300323486,2.6846211192092895,13.705368300323485Q2.7692431192092894,13.789988300323486,2.8865001192092894,13.789988300323486L13.11350011920929,13.789988300323486Q13.40000011920929,13.789988300323486,13.40000011920929,13.503488300323486L13.40000011920929,10.986488300323487Q13.40000011920929,10.699988300323486,13.11350011920929,10.699988300323486ZM2.9000001192092895,10.999988300323487L2.9000001192092895,13.489988300323486L13.10000011920929,13.489988300323486L13.10000011920929,10.999988300323487L2.9000001192092895,10.999988300323487Z"
                        fillRule="evenodd"
                        fill="#14C9BB"
                        fillOpacity="1"
                    />
                </g>
                <g>
                    <path
                        d="M4.677500033378601,11.842988014221191L3.777500033378601,11.842988014221191L3.777500033378601,12.742988014221192L4.677500033378601,12.742988014221192L4.677500033378601,11.842988014221191ZM6.222500033378601,11.842988014221191L5.322500033378601,11.842988014221191L5.322500033378601,12.742988014221192L6.222500033378601,12.742988014221192L6.222500033378601,11.842988014221191ZM12.410000033378601,11.842988014221191L9.1850000333786,11.842988014221191L9.1850000333786,12.742988014221192L12.410000033378601,12.742988014221192L12.410000033378601,11.842988014221191Z"
                        fill="#14C9BB"
                        fillOpacity="1"
                    />
                    <path
                        d="M3.627500033378601,12.892988014221192L4.827500033378601,12.892988014221192L4.827500033378601,11.692988014221191L3.627500033378601,11.692988014221191L3.627500033378601,12.892988014221192ZM5.172500033378601,12.892988014221192L6.372500033378602,12.892988014221192L6.372500033378602,11.692988014221191L5.172500033378601,11.692988014221191L5.172500033378601,12.892988014221192ZM9.035000033378601,12.892988014221192L12.560000033378602,12.892988014221192L12.560000033378602,11.692988014221191L9.035000033378601,11.692988014221191L9.035000033378601,12.892988014221192ZM4.527500033378601,12.592988014221191L3.927500033378601,12.592988014221191L3.927500033378601,11.992988014221192L4.527500033378601,11.992988014221192L4.527500033378601,12.592988014221191ZM6.072500033378601,12.592988014221191L5.472500033378601,12.592988014221191L5.472500033378601,11.992988014221192L6.072500033378601,11.992988014221192L6.072500033378601,12.592988014221191ZM12.260000033378601,12.592988014221191L9.335000033378602,12.592988014221191L9.335000033378602,11.992988014221192L12.260000033378601,11.992988014221192L12.260000033378601,12.592988014221191Z"
                        fillRule="evenodd"
                        fill="#14C9BB"
                        fillOpacity="1"
                    />
                </g>
            </g>
        </g>
    </svg>
);

export const otnDeviceDisabledIcon = () => (
    <svg xmlns="http://www.w3.org/2000/svg" fill="none" version="1.1" width="16" height="16" viewBox="0 0 16 16">
        <g>
            <g />
            <g>
                <g>
                    <path
                        d="M13.11350011920929,2.7499876022338867C13.187000119209289,2.7499876022338867,13.25000011920929,2.8129876022338864,13.25000011920929,2.8864876022338866L13.25000011920929,5.403487602233887C13.25000011920929,5.476987602233887,13.187000119209289,5.539987602233887,13.11350011920929,5.539987602233887L2.8865001192092894,5.539987602233887C2.8130001192092893,5.539987602233887,2.7500001192092896,5.476987602233887,2.7500001192092896,5.403487602233887L2.7500001192092896,2.8864876022338866C2.7500001192092896,2.8129876022338864,2.8130001192092893,2.7499876022338867,2.8865001192092894,2.7499876022338867L13.11350011920929,2.7499876022338867ZM13.11350011920929,1.9999876022338867L2.8865001192092894,1.9999876022338867C2.3990001192092896,1.9999876022338867,2.0000001192092896,2.3989876022338867,2.0000001192092896,2.8864876022338866L2.0000001192092896,5.403487602233887C2.0000001192092896,5.890987602233887,2.3990001192092896,6.289987602233887,2.8865001192092894,6.289987602233887L13.11350011920929,6.289987602233887C13.60100011920929,6.289987602233887,14.00000011920929,5.890987602233887,14.00000011920929,5.403487602233887L14.00000011920929,2.8864876022338866C14.00000011920929,2.3989876022338867,13.60100011920929,1.9999877166748867,13.11350011920929,1.9999876022338867Z"
                        fill="#A2ACB2"
                        fillOpacity="1"
                    />
                    <path
                        d="M14.15000011920929,5.403487602233887L14.15000011920929,2.8864876022338866Q14.15000011920929,2.458730602233887,13.845600119209289,2.1543596022338867Q13.54130011920929,1.8499876022338868,13.11350011920929,1.8499876022338868L2.8865001192092894,1.8499876022338868Q2.4587431192092897,1.8499876022338868,2.1543711192092894,2.1543596022338867Q1.8500001192092896,2.458730602233887,1.8500001192092896,2.8864876022338866L1.8500001192092896,5.403487602233887Q1.8500001192092896,5.831247602233887,2.1543711192092894,6.135617602233887Q2.4587421192092895,6.439987602233887,2.8865001192092894,6.439987602233887L13.11350011920929,6.439987602233887Q13.54130011920929,6.439987602233887,13.845600119209289,6.135617602233887Q14.15000011920929,5.831247602233887,14.15000011920929,5.403487602233887ZM13.63350011920929,2.3664916022338867Q13.85000011920929,2.582994602233887,13.85000011920929,2.8864876022338866L13.85000011920929,5.403487602233887Q13.85000011920929,5.706977602233886,13.63350011920929,5.923487602233887Q13.41700011920929,6.139987602233886,13.11350011920929,6.139987602233886L2.8865001192092894,6.139987602233886Q2.58300711920929,6.139987602233886,2.3665041192092895,5.923487602233887Q2.1500001192092895,5.706977602233886,2.1500001192092895,5.403487602233887L2.1500001192092895,2.8864876022338866Q2.1500001192092895,2.582994602233887,2.3665041192092895,2.3664916022338867Q2.58300711920929,2.1499876022338866,2.8865001192092894,2.1499876022338866L13.11350011920929,2.1499876022338866Q13.41700011920929,2.1499876022338866,13.63350011920929,2.3664916022338867ZM13.11350011920929,2.599987602233887L2.8865001192092894,2.599987602233887Q2.6000001192092896,2.599987602233887,2.6000001192092896,2.8864876022338866L2.6000001192092896,5.403487602233887Q2.6000001192092896,5.689987602233886,2.8865001192092894,5.689987602233886L13.11350011920929,5.689987602233886Q13.40000011920929,5.689987602233886,13.40000011920929,5.403487602233887L13.40000011920929,2.8864876022338866Q13.40000011920929,2.599987602233887,13.11350011920929,2.599987602233887ZM2.9000001192092895,2.8999876022338866L2.9000001192092895,5.389987602233887L13.10000011920929,5.389987602233887L13.10000011920929,2.8999876022338866L2.9000001192092895,2.8999876022338866Z"
                        fillRule="evenodd"
                        fill="#A2ACB2"
                        fillOpacity="1"
                    />
                </g>
                <g>
                    <path
                        d="M4.67750011920929,3.742988348007202L3.7775001192092894,3.742988348007202L3.7775001192092894,4.6429883480072025L4.67750011920929,4.6429883480072025L4.67750011920929,3.742988348007202ZM6.22250011920929,3.742988348007202L5.322500119209289,3.742988348007202L5.322500119209289,4.6429883480072025L6.22250011920929,4.6429883480072025L6.22250011920929,3.742988348007202ZM12.41000011920929,3.742988348007202L9.185000119209288,3.742988348007202L9.185000119209288,4.6429883480072025L12.41000011920929,4.6429883480072025L12.41000011920929,3.742988348007202ZM13.11350011920929,6.7999883480072025C13.187000119209289,6.7999883480072025,13.25000011920929,6.862988348007202,13.25000011920929,6.936488348007202L13.25000011920929,9.453488348007202C13.25000011920929,9.526988348007201,13.187000119209289,9.589988348007203,13.11350011920929,9.589988348007203L2.8865001192092894,9.589988348007203C2.8130001192092893,9.589988348007203,2.7500001192092896,9.526988348007201,2.7500001192092896,9.453488348007202L2.7500001192092896,6.936488348007202C2.7500001192092896,6.862988348007202,2.8130001192092893,6.7999883480072025,2.8865001192092894,6.7999883480072025L13.11350011920929,6.7999883480072025ZM13.11350011920929,6.0499883480072025L2.8865001192092894,6.0499883480072025C2.3990001192092896,6.0499883480072025,2.0000001192092896,6.448988348007202,2.0000001192092896,6.936488348007202L2.0000001192092896,9.453488348007202C2.0000001192092896,9.940988348007203,2.3990001192092896,10.339988348007203,2.8865001192092894,10.339988348007203L13.11350011920929,10.339988348007203C13.60100011920929,10.339988348007203,14.00000011920929,9.940988348007203,14.00000011920929,9.453488348007202L14.00000011920929,6.936488348007202C14.00000011920929,6.448988348007202,13.60100011920929,6.0499883480072025,13.11350011920929,6.0499883480072025Z"
                        fill="#A2ACB2"
                        fillOpacity="1"
                    />
                    <path
                        d="M3.6275001192092895,4.792988348007202L4.82750011920929,4.792988348007202L4.82750011920929,3.5929883480072022L3.6275001192092895,3.5929883480072022L3.6275001192092895,4.792988348007202ZM5.172500119209289,4.792988348007202L6.372500119209289,4.792988348007202L6.372500119209289,3.5929883480072022L5.172500119209289,3.5929883480072022L5.172500119209289,4.792988348007202ZM9.03500011920929,4.792988348007202L12.56000011920929,4.792988348007202L12.56000011920929,3.5929883480072022L9.03500011920929,3.5929883480072022L9.03500011920929,4.792988348007202ZM4.527500119209289,4.492988348007202L3.9275001192092898,4.492988348007202L3.9275001192092898,3.892988348007202L4.527500119209289,3.892988348007202L4.527500119209289,4.492988348007202ZM6.072500119209289,4.492988348007202L5.47250011920929,4.492988348007202L5.47250011920929,3.892988348007202L6.072500119209289,3.892988348007202L6.072500119209289,4.492988348007202ZM12.26000011920929,4.492988348007202L9.33500011920929,4.492988348007202L9.33500011920929,3.892988348007202L12.26000011920929,3.892988348007202L12.26000011920929,4.492988348007202ZM14.15000011920929,9.453488348007202L14.15000011920929,6.936488348007202Q14.15000011920929,6.508728348007202,13.845600119209289,6.204358348007203Q13.54130011920929,5.899988348007202,13.11350011920929,5.899988348007202L2.8865001192092894,5.899988348007202Q2.4587431192092897,5.899988348007202,2.1543711192092894,6.204358348007203Q1.8500001192092896,6.508728348007202,1.8500001192092896,6.936488348007202L1.8500001192092896,9.453488348007202Q1.8500001192092896,9.881248348007201,2.1543711192092894,10.185618348007203Q2.4587421192092895,10.489988348007202,2.8865001192092894,10.489988348007202L13.11350011920929,10.489988348007202Q13.54130011920929,10.489988348007202,13.845600119209289,10.185618348007203Q14.15000011920929,9.881248348007201,14.15000011920929,9.453488348007202ZM13.63350011920929,6.416488348007203Q13.85000011920929,6.632998348007202,13.85000011920929,6.936488348007202L13.85000011920929,9.453488348007202Q13.85000011920929,9.756978348007202,13.63350011920929,9.973488348007201Q13.41700011920929,10.189988348007201,13.11350011920929,10.189988348007201L2.8865001192092894,10.189988348007201Q2.58300711920929,10.189988348007201,2.3665031192092894,9.973488348007201Q2.1500001192092895,9.756978348007202,2.1500001192092895,9.453488348007202L2.1500001192092895,6.936488348007202Q2.1500001192092895,6.632998348007202,2.3665041192092895,6.416488348007203Q2.58300711920929,6.199988348007202,2.8865001192092894,6.199988348007202L13.11350011920929,6.199988348007202Q13.41700011920929,6.199988348007202,13.63350011920929,6.416488348007203ZM13.11350011920929,6.649988348007202L2.8865001192092894,6.649988348007202Q2.6000001192092896,6.649988348007202,2.6000001192092896,6.936488348007202L2.6000001192092896,9.453488348007202Q2.6000001192092896,9.570748348007202,2.6846211192092895,9.655368348007201Q2.7692431192092894,9.739988348007202,2.8865001192092894,9.739988348007202L13.11350011920929,9.739988348007202Q13.40000011920929,9.739988348007202,13.40000011920929,9.453488348007202L13.40000011920929,6.936488348007202Q13.40000011920929,6.649988348007202,13.11350011920929,6.649988348007202ZM2.9000001192092895,6.949988348007202L2.9000001192092895,9.439988348007201L13.10000011920929,9.439988348007201L13.10000011920929,6.949988348007202L2.9000001192092895,6.949988348007202Z"
                        fillRule="evenodd"
                        fill="#A2ACB2"
                        fillOpacity="1"
                    />
                </g>
                <g>
                    <path
                        d="M4.67750011920929,7.792988300323486L3.7775001192092894,7.792988300323486L3.7775001192092894,8.692988300323487L4.67750011920929,8.692988300323487L4.67750011920929,7.792988300323486ZM6.22250011920929,7.792988300323486L5.322500119209289,7.792988300323486L5.322500119209289,8.692988300323487L6.22250011920929,8.692988300323487L6.22250011920929,7.792988300323486ZM12.41000011920929,7.792988300323486L9.185000119209288,7.792988300323486L9.185000119209288,8.692988300323487L12.41000011920929,8.692988300323487L12.41000011920929,7.792988300323486ZM13.11350011920929,10.849988300323487C13.187000119209289,10.849988300323487,13.25000011920929,10.912988300323487,13.25000011920929,10.986488300323487L13.25000011920929,13.503488300323486C13.25000011920929,13.576988300323485,13.187000119209289,13.639988300323488,13.11350011920929,13.639988300323488L2.8865001192092894,13.639988300323488C2.8130001192092893,13.639988300323488,2.7500001192092896,13.576988300323485,2.7500001192092896,13.503488300323486L2.7500001192092896,10.986488300323487C2.7500001192092896,10.912988300323487,2.8130001192092893,10.849988300323487,2.8865001192092894,10.849988300323487L13.11350011920929,10.849988300323487ZM13.11350011920929,10.099988300323487L2.8865001192092894,10.099988300323487C2.3990001192092896,10.099988300323487,2.0000001192092896,10.498988300323486,2.0000001192092896,10.986488300323487L2.0000001192092896,13.503488300323486C2.0000001192092896,13.990988300323487,2.3990001192092896,14.389988300323488,2.8865001192092894,14.389988300323488L13.11350011920929,14.389988300323488C13.60100011920929,14.389988300323488,14.00000011920929,13.990988300323487,14.00000011920929,13.503488300323486L14.00000011920929,10.986488300323487C14.00000011920929,10.498988300323486,13.60100011920929,10.099988300323487,13.11350011920929,10.099988300323487Z"
                        fill="#A2ACB2"
                        fillOpacity="1"
                    />
                    <path
                        d="M3.6275001192092895,8.842988300323487L4.82750011920929,8.842988300323487L4.82750011920929,7.642988300323486L3.6275001192092895,7.642988300323486L3.6275001192092895,8.842988300323487ZM5.172500119209289,8.842988300323487L6.372500119209289,8.842988300323487L6.372500119209289,7.642988300323486L5.172500119209289,7.642988300323486L5.172500119209289,8.842988300323487ZM9.03500011920929,8.842988300323487L12.56000011920929,8.842988300323487L12.56000011920929,7.642988300323486L9.03500011920929,7.642988300323486L9.03500011920929,8.842988300323487ZM4.527500119209289,8.542988300323486L3.9275001192092898,8.542988300323486L3.9275001192092898,7.942988300323487L4.527500119209289,7.942988300323487L4.527500119209289,8.542988300323486ZM6.072500119209289,8.542988300323486L5.47250011920929,8.542988300323486L5.47250011920929,7.942988300323487L6.072500119209289,7.942988300323487L6.072500119209289,8.542988300323486ZM12.26000011920929,8.542988300323486L9.33500011920929,8.542988300323486L9.33500011920929,7.942988300323487L12.26000011920929,7.942988300323487L12.26000011920929,8.542988300323486ZM14.15000011920929,13.503488300323486L14.15000011920929,10.986488300323487Q14.15000011920929,10.558728300323487,13.845600119209289,10.254358300323487Q13.54130011920929,9.949988300323486,13.11350011920929,9.949988300323486L2.8865001192092894,9.949988300323486Q2.4587431192092897,9.949988300323486,2.1543711192092894,10.254358300323487Q1.8500001192092896,10.558728300323487,1.8500001192092896,10.986488300323487L1.8500001192092896,13.503488300323486Q1.8500001192092896,13.931248300323485,2.1543711192092894,14.235618300323488Q2.4587421192092895,14.539988300323486,2.8865001192092894,14.539988300323486L13.11350011920929,14.539988300323486Q13.54130011920929,14.539988300323486,13.845600119209289,14.235618300323488Q14.15000011920929,13.931248300323485,14.15000011920929,13.503488300323486ZM13.63350011920929,10.466488300323487Q13.85000011920929,10.682998300323487,13.85000011920929,10.986488300323487L13.85000011920929,13.503488300323486Q13.85000011920929,13.806978300323486,13.63350011920929,14.023488300323486Q13.41700011920929,14.239988300323486,13.11350011920929,14.239988300323486L2.8865001192092894,14.239988300323486Q2.58300711920929,14.239988300323486,2.3665031192092894,14.023488300323486Q2.1500001192092895,13.806978300323486,2.1500001192092895,13.503488300323486L2.1500001192092895,10.986488300323487Q2.1500001192092895,10.682998300323487,2.3665041192092895,10.466488300323487Q2.58300711920929,10.249988300323487,2.8865001192092894,10.249988300323487L13.11350011920929,10.249988300323487Q13.41700011920929,10.249988300323487,13.63350011920929,10.466488300323487ZM13.11350011920929,10.699988300323486L2.8865001192092894,10.699988300323486Q2.6000001192092896,10.699988300323486,2.6000001192092896,10.986488300323487L2.6000001192092896,13.503488300323486Q2.6000001192092896,13.620748300323486,2.6846211192092895,13.705368300323485Q2.7692431192092894,13.789988300323486,2.8865001192092894,13.789988300323486L13.11350011920929,13.789988300323486Q13.40000011920929,13.789988300323486,13.40000011920929,13.503488300323486L13.40000011920929,10.986488300323487Q13.40000011920929,10.699988300323486,13.11350011920929,10.699988300323486ZM2.9000001192092895,10.999988300323487L2.9000001192092895,13.489988300323486L13.10000011920929,13.489988300323486L13.10000011920929,10.999988300323487L2.9000001192092895,10.999988300323487Z"
                        fillRule="evenodd"
                        fill="#A2ACB2"
                        fillOpacity="1"
                    />
                </g>
                <g>
                    <path
                        d="M4.677500033378601,11.842988014221191L3.777500033378601,11.842988014221191L3.777500033378601,12.742988014221192L4.677500033378601,12.742988014221192L4.677500033378601,11.842988014221191ZM6.222500033378601,11.842988014221191L5.322500033378601,11.842988014221191L5.322500033378601,12.742988014221192L6.222500033378601,12.742988014221192L6.222500033378601,11.842988014221191ZM12.410000033378601,11.842988014221191L9.1850000333786,11.842988014221191L9.1850000333786,12.742988014221192L12.410000033378601,12.742988014221192L12.410000033378601,11.842988014221191Z"
                        fill="#A2ACB2"
                        fillOpacity="1"
                    />
                    <path
                        d="M3.627500033378601,12.892988014221192L4.827500033378601,12.892988014221192L4.827500033378601,11.692988014221191L3.627500033378601,11.692988014221191L3.627500033378601,12.892988014221192ZM5.172500033378601,12.892988014221192L6.372500033378602,12.892988014221192L6.372500033378602,11.692988014221191L5.172500033378601,11.692988014221191L5.172500033378601,12.892988014221192ZM9.035000033378601,12.892988014221192L12.560000033378602,12.892988014221192L12.560000033378602,11.692988014221191L9.035000033378601,11.692988014221191L9.035000033378601,12.892988014221192ZM4.527500033378601,12.592988014221191L3.927500033378601,12.592988014221191L3.927500033378601,11.992988014221192L4.527500033378601,11.992988014221192L4.527500033378601,12.592988014221191ZM6.072500033378601,12.592988014221191L5.472500033378601,12.592988014221191L5.472500033378601,11.992988014221192L6.072500033378601,11.992988014221192L6.072500033378601,12.592988014221191ZM12.260000033378601,12.592988014221191L9.335000033378602,12.592988014221191L9.335000033378602,11.992988014221192L12.260000033378601,11.992988014221192L12.260000033378601,12.592988014221191Z"
                        fillRule="evenodd"
                        fill="#A2ACB2"
                        fillOpacity="1"
                    />
                </g>
            </g>
        </g>
    </svg>
);

export const switchDeviceIcon = () => (
    <svg xmlns="http://www.w3.org/2000/svg" fill="none" version="1.1" width="16" height="16" viewBox="0 0 16 16">
        <g>
            <g />
            <g>
                <g>
                    <path
                        d="M14,9.3146C14,9.28857,14,9.262540000000001,13.9937,9.24301L12.9588,2.911128C12.9197,2.670347,12.8093,2.436073,12.6529,2.279891C12.475,2.0981743,12.2305,1.99708362,11.97614,2.0000645437L4.03037,2.0000645437C3.7678000000000003,2.000591595,3.51573,2.103283,3.32755,2.286399C3.15326,2.466662,3.05335,2.706005,3.04772,2.95668L2.0130152,9.20397C2.00650766,9.24952,2,9.282060000000001,2,9.321110000000001L2,11.98922C2.032538,12.4706,2.3383950000000002,12.8873,2.839479,13.0174L2.845987,13.0239L2.872017,13.0239L2.885032,13.0302L2.898047,13.0369L2.911063,13.0369L2.91757,13.0432L2.937093,13.0432L3.00868,13.0498L3.04121,13.0563L3.05423,13.0563L3.06724,13.0626L12.8937,13.0626C13.5054,13.0626,13.9998,12.5617,13.9998,11.94977L14,9.3146ZM3.75054,2.807006C3.75705,2.780976,3.75705,2.754945,3.75705,2.7289149999999998C3.75705,2.7093920000000002,3.76356,2.696377,3.77657,2.683362C3.78959,2.670347,3.80911,2.6573320000000002,3.82863,2.6573320000000002L12.2299,2.6573320000000002C12.2364,2.6573320000000002,12.2427,2.6573320000000002,12.2497,2.670347C12.2822,2.702885,12.3017,2.748438,12.308,2.807006L13.1672,8.4979C13.1347,8.491389999999999,13.1087,8.491389999999999,13.0826,8.491389999999999L3.04772,8.491389999999999C3.02169,8.491389999999999,2.989154,8.491389999999999,2.963124,8.4979L3.75054,2.807006ZM13.3425,12.0673C13.3425,12.1716,13.2581,12.2561,13.1538,12.2562L2.839479,12.2562C2.748373,12.2495,2.6702820000000003,12.1912,2.657267,12.0933L2.657267,12.087L2.650759,12.087L2.650759,9.353850000000001C2.65663,9.25067,2.742652,9.170390000000001,2.845987,9.17163L13.1475,9.17163C13.1975,9.17037,13.2454,9.19192,13.2777,9.2302C13.31,9.26274,13.3362,9.3085,13.3362,9.353850000000001L13.3362,12.0675L13.3425,12.0673Z"
                        fill="#14C9BB"
                        fillOpacity="1"
                    />
                    <path
                        d="M13.7826,12.8413Q14.1498,12.4711,14.1498,11.94977L14.15,9.31461Q14.15,9.251660000000001,14.1406,9.21201L13.1068,2.886932Q13.0361,2.450522,12.7589,2.173756Q12.4366,1.844657,11.97438,1.850075L4.03037,1.850065Q3.5599,1.851009,3.22294,2.178894L3.2213000000000003,2.180491L3.21971,2.182136Q2.9116400000000002,2.500775,2.898051,2.942355L1.864759,9.18111L1.864523,9.18276Q1.863259,9.19161,1.860701,9.20844Q1.85,9.278880000000001,1.85,9.321110000000001L1.85,11.99428L1.850342,11.99933Q1.9119362,12.9106,2.761617,13.1517L2.783846,13.1739L2.836415,13.1739L2.837407,13.1744L2.856817,13.1932L2.930284,13.1932L2.987117,13.1984L3.0152,13.204L3.0328299999999997,13.2126L12.8937,13.2126Q13.4144,13.2126,13.7826,12.8413ZM13.85,9.314589999999999L13.8498,11.94976Q13.8498,12.3475,13.5696,12.63Q13.2894,12.9126,12.8937,12.9126L3.10166,12.9126L3.08865,12.9063L3.05607,12.9063L3.03024,12.9011L2.9818860000000003,12.8967L2.971815,12.8869L2.933302,12.8869L2.90644,12.8739L2.883702,12.8739L2.877189,12.8722Q2.200449,12.6965,2.15,11.98404L2.15,9.321110000000001Q2.15,9.30154,2.157298,9.253499999999999Q2.159831,9.236830000000001,2.161264,9.226880000000001L3.19745,2.970688L3.19768,2.960044Q3.20509,2.629979,3.4337999999999997,2.392306Q3.68317,2.150762,4.03037,2.150065L11.9779,2.150054Q12.312,2.146138,12.5469,2.386025Q12.7554,2.594266,12.8108,2.935323L13.8475,9.27836L13.851,9.289100000000001L13.8495,9.28603Q13.85,9.2934,13.85,9.314589999999999ZM3.60306,2.778455L2.784108,8.69727L2.9995060000000002,8.643419999999999Q3.00762,8.641390000000001,3.04772,8.641390000000001L13.0826,8.641390000000001Q13.1198,8.641390000000001,13.1378,8.64498L13.3475,8.68692L12.4568,2.7875069999999997Q12.4423,2.6608169999999998,12.3692,2.578561Q12.3202,2.507332,12.2299,2.507332L3.82863,2.507332Q3.74047,2.507332,3.67051,2.577296Q3.60705,2.640753,3.60705,2.7289149999999998Q3.60705,2.76251,3.6050199999999997,2.770627L3.60306,2.778455ZM3.13621,8.34139L12.9919,8.34139L12.1592,2.82624L12.1589,2.823063Q12.158,2.814492,12.1566,2.807332L3.90268,2.807332Q3.90085,2.821481,3.89827,2.833789L3.13621,8.34139ZM13.1862,12.0923L13.1862,9.353850000000001Q13.1862,9.350999999999999,13.1713,9.33594L13.1669,9.33155L13.1629,9.32682Q13.1584,9.32141,13.1513,9.32159L13.1494,9.321629999999999L2.8441739999999998,9.32162Q2.804475,9.32114,2.800759,9.35922L2.800759,11.93704L2.807267,11.93704L2.807267,12.0798Q2.813953,12.1025,2.846199,12.1062L13.1537,12.1062Q13.1769,12.1062,13.1862,12.0923ZM2.543891,12.237L2.500759,12.237L2.500759,9.34958L2.501001,9.34533Q2.519646,9.01768,2.847799,9.021650000000001L13.1458,9.02163Q13.2925,9.018740000000001,13.3883,9.12876Q13.4862,9.22991,13.4862,9.353850000000001L13.4862,11.9126L13.4925,11.9124L13.4925,12.0673Q13.4925,12.4059,13.154,12.4062L2.833964,12.4062L2.828463,12.4058Q2.6181900000000002,12.3903,2.543891,12.237Z"
                        fillRule="evenodd"
                        fill="#14C9BB"
                        fillOpacity="1"
                    />
                </g>
                <g>
                    <path
                        d="M11.447689937362671,10.882949202957153L12.65646993736267,10.882949202957153L12.65646993736267,11.501179202957154L11.447689937362671,11.501179202957154L11.447689937362671,10.882949202957153ZM11.447689937362671,9.926339202957152L12.65646993736267,9.926339202957152L12.65646993736267,10.544759202957152L11.447689937362671,10.544759202957152L11.447689937362671,9.926339202957152ZM9.425449937362671,10.882949202957153L10.63422993736267,10.882949202957153L10.63422993736267,11.501179202957154L9.425449937362671,11.501179202957154L9.425449937362671,10.882949202957153ZM9.425449937362671,9.926339202957152L10.63422993736267,9.926339202957152L10.63422993736267,10.544759202957152L9.425449937362671,10.544759202957152L9.425449937362671,9.926339202957152ZM7.319439937362671,10.882949202957153L8.52841993736267,10.882949202957153L8.52841993736267,11.501179202957154L7.319449937362671,11.501179202957154L7.319439937362671,10.882949202957153ZM7.319439937362671,9.926339202957152L8.52841993736267,9.926339202957152L8.52841993736267,10.544759202957152L7.319449937362671,10.544759202957152L7.319439937362671,9.926339202957152ZM5.297179937362671,10.882949202957153L6.505969937362671,10.882949202957153L6.505969937362671,11.501179202957154L5.297179937362671,11.501179202957154L5.297179937362671,10.882949202957153ZM5.297179937362671,9.926339202957152L6.505969937362671,9.926339202957152L6.505969937362671,10.544759202957152L5.297179937362671,10.544759202957152L5.297179937362671,9.926339202957152ZM6.965569937362671,7.440439202957153C7.069689937362671,7.440439202957153,7.173809937362671,7.251719202957153,7.238889937362671,7.167119202957153L7.7920299373626705,6.340654202957153L7.238889937362671,5.787509202957153L6.692249937362671,6.613973202957153L5.592479937362671,6.613973202957153C5.397251937362671,6.613973202957153,5.319161437362671,6.802689202957153,5.319161437362671,7.004429202957153C5.319161437362671,7.206169202957153,5.397251937362671,7.440439202957153,5.592479937362671,7.440439202957153L6.965569937362671,7.440439202957153Z"
                        fill="#14C9BB"
                        fillOpacity="1"
                    />
                    <path
                        d="M5.219161437362671,7.004429202957153Q5.219161437362671,7.540439202957153,5.592479937362671,7.540439202957153L6.965569937362671,7.540439202957153Q7.101169937362671,7.540439202957153,7.277399937362671,7.2853692029571535Q7.306599937362671,7.243109202957154,7.318149937362671,7.228089202957153L7.320159937362671,7.225479202957153L7.920819937362671,6.328020202957154L7.223019937362671,5.630224202957153L6.6384999373626705,6.513973202957153L5.592479937362671,6.513973202957153Q5.3988699373626705,6.513973202957153,5.2971164062626706,6.681677202957153Q5.219161437362671,6.810159202957154,5.219161437362671,7.004429202957153ZM6.965569937362671,7.340439202957153L5.592479937362671,7.340439202957153Q5.419160937362671,7.340439202957153,5.419160937362671,7.004429202957153Q5.419160937362671,6.7139732029571535,5.592479937362671,6.7139732029571535L6.7459999373626705,6.7139732029571535L7.254749937362671,5.944794202957153L7.663239937362671,6.3532892029571535L7.157499937362671,7.108939202957154Q7.142719937362671,7.128449202957153,7.112849937362671,7.171689202957153Q6.996259937362671,7.340439202957153,6.965569937362671,7.340439202957153ZM5.197179937362671,9.826339202957154L5.197179937362671,10.644759202957154L6.605969937362671,10.644759202957154L6.605969937362671,9.826339202957154L5.197179937362671,9.826339202957154ZM7.219429937362671,9.826339202957154L7.219449937362671,10.544759202957152L7.219449937362671,10.644759202957154L8.628419937362672,10.644759202957154L8.628419937362672,9.826339202957154L7.219429937362671,9.826339202957154ZM9.32544993736267,9.826339202957154L9.32544993736267,10.644759202957154L10.73422993736267,10.644759202957154L10.73422993736267,9.826339202957154L9.32544993736267,9.826339202957154ZM11.34768993736267,9.826339202957154L11.34768993736267,10.644759202957154L12.75646993736267,10.644759202957154L12.75646993736267,9.826339202957154L11.34768993736267,9.826339202957154ZM7.419449937362671,10.444759202957155L7.419439937362671,10.026339202957153L8.428419937362671,10.026339202957153L8.428419937362671,10.444759202957155L7.419449937362671,10.444759202957155ZM5.3971799373626705,10.444759202957155L5.3971799373626705,10.026339202957153L6.405969937362671,10.026339202957153L6.405969937362671,10.444759202957155L5.3971799373626705,10.444759202957155ZM9.525449937362671,10.444759202957155L9.525449937362671,10.026339202957153L10.53422993736267,10.026339202957153L10.53422993736267,10.444759202957155L9.525449937362671,10.444759202957155ZM11.547689937362671,10.444759202957155L12.556469937362671,10.444759202957155L12.556469937362671,10.026339202957153L11.547689937362671,10.026339202957153L11.547689937362671,10.444759202957155ZM5.197179937362671,10.782949202957154L5.197179937362671,11.601179202957153L6.605969937362671,11.601179202957153L6.605969937362671,10.782949202957154L5.197179937362671,10.782949202957154ZM7.219429937362671,10.782949202957154L7.219449937362671,11.501179202957154L7.219449937362671,11.601179202957153L8.628419937362672,11.601179202957153L8.628419937362672,10.782949202957154L7.219429937362671,10.782949202957154ZM9.32544993736267,10.782949202957154L9.32544993736267,11.601179202957153L10.73422993736267,11.601179202957153L10.73422993736267,10.782949202957154L9.32544993736267,10.782949202957154ZM11.34768993736267,10.782949202957154L11.34768993736267,11.601179202957153L12.75646993736267,11.601179202957153L12.75646993736267,10.782949202957154L11.34768993736267,10.782949202957154ZM7.419449937362671,11.401179202957152L7.419439937362671,10.982949202957153L8.428419937362671,10.982949202957153L8.428419937362671,11.401179202957152L7.419449937362671,11.401179202957152ZM5.3971799373626705,11.401179202957152L5.3971799373626705,10.982949202957153L6.405969937362671,10.982949202957153L6.405969937362671,11.401179202957152L5.3971799373626705,11.401179202957152ZM9.525449937362671,11.401179202957152L9.525449937362671,10.982949202957153L10.53422993736267,10.982949202957153L10.53422993736267,11.401179202957152L9.525449937362671,11.401179202957152ZM11.547689937362671,11.401179202957152L12.556469937362671,11.401179202957152L12.556469937362671,10.982949202957153L11.547689937362671,10.982949202957153L11.547689937362671,11.401179202957152Z"
                        fillRule="evenodd"
                        fill="#14C9BB"
                        fillOpacity="1"
                    />
                </g>
                <g>
                    <path
                        d="M10.543261598815917,6.613931255683899L9.170161598815918,6.613931255683899L7.243911598815918,3.8612162556838987C7.178841598815918,3.8547092556838987,7.074711598815918,3.8612162556838987,6.970591598815918,3.8612162556838987L5.864301598815918,3.8612162556838987L5.864301598815918,3.308071255683899L4.764521598815918,4.4143612556838985L5.864301598815918,5.240831255683899L5.864301598815918,4.6876812556838985L6.944561598815918,4.6876812556838985L8.617011598815917,7.167071255683899C8.681891598815918,7.251671255683899,8.786211598815918,7.440391255683899,8.89033159881592,7.440391255683899L10.458661598815919,7.440391255683899C10.654091598815917,7.440391255683899,10.816581598815919,7.271191255683899,10.816581598815919,7.062951255683899C10.823291598815917,6.854711255683899,10.745001598815918,6.613931255683899,10.543261598815917,6.613931255683899Z"
                        fill="#14C9BB"
                        fillOpacity="1"
                    />
                    <path
                        d="M10.916581598815917,7.064591255683899Q10.922751598815918,6.863201255683899,10.847181598815919,6.714931255683899Q10.744721598815918,6.513931255683898,10.543261598815917,6.513931255683898L9.222241598815918,6.513931255683898L7.299531598815918,3.766279255683899L7.253861598815918,3.761713255683899Q7.205421598815918,3.756868255683899,7.075631598815918,3.7596952556838987Q7.005791598815918,3.761216255683899,6.970591598815918,3.761216255683899L5.9643015988159185,3.761216255683899L5.9643015988159185,3.065639255683899L4.612632598815918,4.425311255683899L5.9643015988159185,5.441061255683898L5.9643015988159185,4.787681255683899L6.891391598815918,4.787681255683899L8.535811598815918,7.225511255683899L8.537661598815918,7.227931255683899Q8.549091598815918,7.242821255683899,8.577961598815918,7.2846412556838995Q8.754511598815919,7.540391255683899,8.89033159881592,7.540391255683899L10.458661598815919,7.540391255683899Q10.648461598815917,7.540391255683899,10.783331598815918,7.3987112556838985Q10.916021598815918,7.259341255683899,10.916581598815917,7.064591255683899ZM10.668991598815918,6.805761255683899Q10.721501598815918,6.908761255683899,10.716631598815919,7.059731255683899L10.716581598815917,7.0613412556838995L10.716581598815917,7.062951255683899Q10.716581598815917,7.340391255683899,10.458661598815919,7.340391255683899L8.89033159881592,7.340391255683899Q8.859471598815919,7.340391255683899,8.742541598815919,7.171021255683899Q8.712901598815918,7.128081255683899,8.698331598815917,7.108791255683899L6.997731598815918,4.587681255683899L5.764304598815918,4.587681255683899L5.764304598815918,5.040591255683899L4.916410598815918,4.403411255683899L5.764304598815918,3.550503255683899L5.764304598815918,3.961216255683899L6.970591598815918,3.961216255683899Q7.007971598815918,3.961216255683899,7.079981598815918,3.959648255683899Q7.149081598815918,3.9581432556838987,7.190131598815918,3.958776255683899L9.118081598815918,6.7139312556838995L10.543261598815917,6.7139312556838995Q10.622181598815917,6.7139312556838995,10.668991598815918,6.805761255683899Z"
                        fillRule="evenodd"
                        fill="#14C9BB"
                        fillOpacity="1"
                    />
                </g>
                <g>
                    <path
                        d="M10.269245546684264,3.8612620711212156L9.169465546684265,3.8612620711212156C9.058835546684264,3.8612620711212156,8.811545546684265,3.978399071121216,8.740165546684265,4.062997071121216L8.063175546684265,4.687724071121216L8.616325546684266,5.240874071121215L9.169465546684265,4.687724071121216L10.269245546684264,4.687724071121216L10.269245546684264,5.240874071121215L11.369035546684266,4.414404071121216L10.269245546684264,3.314624071121216L10.269245546684264,3.8612620711212156ZM3.600385546684265,10.642174071121214C3.600385546684265,10.866804071121216,3.7824825466842653,11.048894071121216,4.007110546684265,11.048894071121216C4.231737546684265,11.048894071121216,4.4138345466842654,10.866804071121216,4.4138345466842654,10.642174071121214C4.4138345466842654,10.417544071121217,4.231738546684265,10.235454071121215,4.007110546684265,10.235454071121215C3.7824825466842653,10.235454071121215,3.600385988008265,10.417544071121217,3.600385546684265,10.642174071121214Z"
                        fill="#14C9BB"
                        fillOpacity="1"
                    />
                    <path
                        d="M11.521195546684265,4.425144071121216L10.169245546684266,3.073203071121216L10.169245546684266,3.761262071121216L9.169465546684265,3.761262071121216Q8.867095546684265,3.761262071121216,8.667915546684265,3.9936020711212157L7.918855546684265,4.684834071121216L8.616325546684266,5.3822940711212155L9.210885546684265,4.787724071121216L10.169245546684266,4.787724071121216L10.169245546684266,5.441104071121216L11.521195546684265,4.425144071121216ZM10.369245546684265,3.5560450711212157L10.369245546684265,3.961262071121216L9.169465546684265,3.961262071121216Q8.956845546684265,3.961262071121216,8.816595546684265,4.1274840711212155L8.812575546684265,4.132255071121216L8.207495546684264,4.690624071121215L8.616325546684266,5.099454071121215L9.128045546684266,4.587724071121215L10.369245546684265,4.587724071121215L10.369245546684265,5.040634071121216L11.216865546684264,4.403664071121216L10.369245546684265,3.5560450711212157ZM4.365418546684265,10.283864071121215Q4.217002546684265,10.135454071121217,4.007110546684265,10.135454071121217Q3.797218546684265,10.135454071121217,3.648802046684265,10.283864071121215Q3.5003859466842653,10.432284071121217,3.500385546684265,10.642174071121214Q3.500385546684265,10.852064071121216,3.6488017466842653,11.000484071121216Q3.7972175466842653,11.148894071121216,4.007110546684265,11.148894071121216Q4.217002546684265,11.148894071121216,4.365418546684265,11.000484071121216Q4.513834546684265,10.852064071121216,4.513834546684265,10.642174071121214Q4.513834546684265,10.432284071121217,4.365418546684265,10.283864071121215ZM3.7003855466842652,10.642174071121214Q3.700386546684265,10.335454071121216,4.007110546684265,10.335454071121216Q4.134159546684265,10.335454071121216,4.223997546684265,10.425284071121215Q4.313834546684265,10.515124071121216,4.313834546684265,10.642174071121214Q4.313834546684265,10.948894071121217,4.007110546684265,10.948894071121217Q3.7003855466842652,10.948894071121217,3.7003855466842652,10.642174071121214Z"
                        fillRule="evenodd"
                        fill="#14C9BB"
                        fillOpacity="1"
                    />
                </g>
            </g>
        </g>
    </svg>
);

export const switchDeviceDisabledIcon = () => (
    <svg xmlns="http://www.w3.org/2000/svg" fill="none" version="1.1" width="16" height="16" viewBox="0 0 16 16">
        <g>
            <g />
            <g>
                <g>
                    <path
                        d="M14,9.3146C14,9.28857,14,9.262540000000001,13.9937,9.24301L12.9588,2.911128C12.9197,2.670347,12.8093,2.436073,12.6529,2.279891C12.475,2.0981743,12.2305,1.99708362,11.97614,2.0000645437L4.03037,2.0000645437C3.7678000000000003,2.000591595,3.51573,2.103283,3.32755,2.286399C3.15326,2.466662,3.05335,2.706005,3.04772,2.95668L2.0130152,9.20397C2.00650766,9.24952,2,9.282060000000001,2,9.321110000000001L2,11.98922C2.032538,12.4706,2.3383950000000002,12.8873,2.839479,13.0174L2.845987,13.0239L2.872017,13.0239L2.885032,13.0302L2.898047,13.0369L2.911063,13.0369L2.91757,13.0432L2.937093,13.0432L3.00868,13.0498L3.04121,13.0563L3.05423,13.0563L3.06724,13.0626L12.8937,13.0626C13.5054,13.0626,13.9998,12.5617,13.9998,11.94977L14,9.3146ZM3.75054,2.807006C3.75705,2.780976,3.75705,2.754945,3.75705,2.7289149999999998C3.75705,2.7093920000000002,3.76356,2.696377,3.77657,2.683362C3.78959,2.670347,3.80911,2.6573320000000002,3.82863,2.6573320000000002L12.2299,2.6573320000000002C12.2364,2.6573320000000002,12.2427,2.6573320000000002,12.2497,2.670347C12.2822,2.702885,12.3017,2.748438,12.308,2.807006L13.1672,8.4979C13.1347,8.491389999999999,13.1087,8.491389999999999,13.0826,8.491389999999999L3.04772,8.491389999999999C3.02169,8.491389999999999,2.989154,8.491389999999999,2.963124,8.4979L3.75054,2.807006ZM13.3425,12.0673C13.3425,12.1716,13.2581,12.2561,13.1538,12.2562L2.839479,12.2562C2.748373,12.2495,2.6702820000000003,12.1912,2.657267,12.0933L2.657267,12.087L2.650759,12.087L2.650759,9.353850000000001C2.65663,9.25067,2.742652,9.170390000000001,2.845987,9.17163L13.1475,9.17163C13.1975,9.17037,13.2454,9.19192,13.2777,9.2302C13.31,9.26274,13.3362,9.3085,13.3362,9.353850000000001L13.3362,12.0675L13.3425,12.0673Z"
                        fill="#A2ACB2"
                        fillOpacity="1"
                    />
                    <path
                        d="M13.7826,12.8413Q14.1498,12.4711,14.1498,11.94977L14.15,9.31461Q14.15,9.251660000000001,14.1406,9.21201L13.1068,2.886932Q13.0361,2.450522,12.7589,2.173756Q12.4366,1.844657,11.97438,1.850075L4.03037,1.850065Q3.5599,1.851009,3.22294,2.178894L3.2213000000000003,2.180491L3.21971,2.182136Q2.9116400000000002,2.500775,2.898051,2.942355L1.864759,9.18111L1.864523,9.18276Q1.863259,9.19161,1.860701,9.20844Q1.85,9.278880000000001,1.85,9.321110000000001L1.85,11.99428L1.850342,11.99933Q1.9119362,12.9106,2.761617,13.1517L2.783846,13.1739L2.836415,13.1739L2.837407,13.1744L2.856817,13.1932L2.930284,13.1932L2.987117,13.1984L3.0152,13.204L3.0328299999999997,13.2126L12.8937,13.2126Q13.4144,13.2126,13.7826,12.8413ZM13.85,9.314589999999999L13.8498,11.94976Q13.8498,12.3475,13.5696,12.63Q13.2894,12.9126,12.8937,12.9126L3.10166,12.9126L3.08865,12.9063L3.05607,12.9063L3.03024,12.9011L2.9818860000000003,12.8967L2.971815,12.8869L2.933302,12.8869L2.90644,12.8739L2.883702,12.8739L2.877189,12.8722Q2.200449,12.6965,2.15,11.98404L2.15,9.321110000000001Q2.15,9.30154,2.157298,9.253499999999999Q2.159831,9.236830000000001,2.161264,9.226880000000001L3.19745,2.970688L3.19768,2.960044Q3.20509,2.629979,3.4337999999999997,2.392306Q3.68317,2.150762,4.03037,2.150065L11.9779,2.150054Q12.312,2.146138,12.5469,2.386025Q12.7554,2.594266,12.8108,2.935323L13.8475,9.27836L13.851,9.289100000000001L13.8495,9.28603Q13.85,9.2934,13.85,9.314589999999999ZM3.60306,2.778455L2.784108,8.69727L2.9995060000000002,8.643419999999999Q3.00762,8.641390000000001,3.04772,8.641390000000001L13.0826,8.641390000000001Q13.1198,8.641390000000001,13.1378,8.64498L13.3475,8.68692L12.4568,2.7875069999999997Q12.4423,2.6608169999999998,12.3692,2.578561Q12.3202,2.507332,12.2299,2.507332L3.82863,2.507332Q3.74047,2.507332,3.67051,2.577296Q3.60705,2.640753,3.60705,2.7289149999999998Q3.60705,2.76251,3.6050199999999997,2.770627L3.60306,2.778455ZM3.13621,8.34139L12.9919,8.34139L12.1592,2.82624L12.1589,2.823063Q12.158,2.814492,12.1566,2.807332L3.90268,2.807332Q3.90085,2.821481,3.89827,2.833789L3.13621,8.34139ZM13.1862,12.0923L13.1862,9.353850000000001Q13.1862,9.350999999999999,13.1713,9.33594L13.1669,9.33155L13.1629,9.32682Q13.1584,9.32141,13.1513,9.32159L13.1494,9.321629999999999L2.8441739999999998,9.32162Q2.804475,9.32114,2.800759,9.35922L2.800759,11.93704L2.807267,11.93704L2.807267,12.0798Q2.813953,12.1025,2.846199,12.1062L13.1537,12.1062Q13.1769,12.1062,13.1862,12.0923ZM2.543891,12.237L2.500759,12.237L2.500759,9.34958L2.501001,9.34533Q2.519646,9.01768,2.847799,9.021650000000001L13.1458,9.02163Q13.2925,9.018740000000001,13.3883,9.12876Q13.4862,9.22991,13.4862,9.353850000000001L13.4862,11.9126L13.4925,11.9124L13.4925,12.0673Q13.4925,12.4059,13.154,12.4062L2.833964,12.4062L2.828463,12.4058Q2.6181900000000002,12.3903,2.543891,12.237Z"
                        fillRule="evenodd"
                        fill="#A2ACB2"
                        fillOpacity="1"
                    />
                </g>
                <g>
                    <path
                        d="M11.447689937362671,10.882949202957153L12.65646993736267,10.882949202957153L12.65646993736267,11.501179202957154L11.447689937362671,11.501179202957154L11.447689937362671,10.882949202957153ZM11.447689937362671,9.926339202957152L12.65646993736267,9.926339202957152L12.65646993736267,10.544759202957152L11.447689937362671,10.544759202957152L11.447689937362671,9.926339202957152ZM9.425449937362671,10.882949202957153L10.63422993736267,10.882949202957153L10.63422993736267,11.501179202957154L9.425449937362671,11.501179202957154L9.425449937362671,10.882949202957153ZM9.425449937362671,9.926339202957152L10.63422993736267,9.926339202957152L10.63422993736267,10.544759202957152L9.425449937362671,10.544759202957152L9.425449937362671,9.926339202957152ZM7.319439937362671,10.882949202957153L8.52841993736267,10.882949202957153L8.52841993736267,11.501179202957154L7.319449937362671,11.501179202957154L7.319439937362671,10.882949202957153ZM7.319439937362671,9.926339202957152L8.52841993736267,9.926339202957152L8.52841993736267,10.544759202957152L7.319449937362671,10.544759202957152L7.319439937362671,9.926339202957152ZM5.297179937362671,10.882949202957153L6.505969937362671,10.882949202957153L6.505969937362671,11.501179202957154L5.297179937362671,11.501179202957154L5.297179937362671,10.882949202957153ZM5.297179937362671,9.926339202957152L6.505969937362671,9.926339202957152L6.505969937362671,10.544759202957152L5.297179937362671,10.544759202957152L5.297179937362671,9.926339202957152ZM6.965569937362671,7.440439202957153C7.069689937362671,7.440439202957153,7.173809937362671,7.251719202957153,7.238889937362671,7.167119202957153L7.7920299373626705,6.340654202957153L7.238889937362671,5.787509202957153L6.692249937362671,6.613973202957153L5.592479937362671,6.613973202957153C5.397251937362671,6.613973202957153,5.319161437362671,6.802689202957153,5.319161437362671,7.004429202957153C5.319161437362671,7.206169202957153,5.397251937362671,7.440439202957153,5.592479937362671,7.440439202957153L6.965569937362671,7.440439202957153Z"
                        fill="#A2ACB2"
                        fillOpacity="1"
                    />
                    <path
                        d="M5.219161437362671,7.004429202957153Q5.219161437362671,7.540439202957153,5.592479937362671,7.540439202957153L6.965569937362671,7.540439202957153Q7.101169937362671,7.540439202957153,7.277399937362671,7.2853692029571535Q7.306599937362671,7.243109202957154,7.318149937362671,7.228089202957153L7.320159937362671,7.225479202957153L7.920819937362671,6.328020202957154L7.223019937362671,5.630224202957153L6.6384999373626705,6.513973202957153L5.592479937362671,6.513973202957153Q5.3988699373626705,6.513973202957153,5.2971164062626706,6.681677202957153Q5.219161437362671,6.810159202957154,5.219161437362671,7.004429202957153ZM6.965569937362671,7.340439202957153L5.592479937362671,7.340439202957153Q5.419160937362671,7.340439202957153,5.419160937362671,7.004429202957153Q5.419160937362671,6.7139732029571535,5.592479937362671,6.7139732029571535L6.7459999373626705,6.7139732029571535L7.254749937362671,5.944794202957153L7.663239937362671,6.3532892029571535L7.157499937362671,7.108939202957154Q7.142719937362671,7.128449202957153,7.112849937362671,7.171689202957153Q6.996259937362671,7.340439202957153,6.965569937362671,7.340439202957153ZM5.197179937362671,9.826339202957154L5.197179937362671,10.644759202957154L6.605969937362671,10.644759202957154L6.605969937362671,9.826339202957154L5.197179937362671,9.826339202957154ZM7.219429937362671,9.826339202957154L7.219449937362671,10.544759202957152L7.219449937362671,10.644759202957154L8.628419937362672,10.644759202957154L8.628419937362672,9.826339202957154L7.219429937362671,9.826339202957154ZM9.32544993736267,9.826339202957154L9.32544993736267,10.644759202957154L10.73422993736267,10.644759202957154L10.73422993736267,9.826339202957154L9.32544993736267,9.826339202957154ZM11.34768993736267,9.826339202957154L11.34768993736267,10.644759202957154L12.75646993736267,10.644759202957154L12.75646993736267,9.826339202957154L11.34768993736267,9.826339202957154ZM7.419449937362671,10.444759202957155L7.419439937362671,10.026339202957153L8.428419937362671,10.026339202957153L8.428419937362671,10.444759202957155L7.419449937362671,10.444759202957155ZM5.3971799373626705,10.444759202957155L5.3971799373626705,10.026339202957153L6.405969937362671,10.026339202957153L6.405969937362671,10.444759202957155L5.3971799373626705,10.444759202957155ZM9.525449937362671,10.444759202957155L9.525449937362671,10.026339202957153L10.53422993736267,10.026339202957153L10.53422993736267,10.444759202957155L9.525449937362671,10.444759202957155ZM11.547689937362671,10.444759202957155L12.556469937362671,10.444759202957155L12.556469937362671,10.026339202957153L11.547689937362671,10.026339202957153L11.547689937362671,10.444759202957155ZM5.197179937362671,10.782949202957154L5.197179937362671,11.601179202957153L6.605969937362671,11.601179202957153L6.605969937362671,10.782949202957154L5.197179937362671,10.782949202957154ZM7.219429937362671,10.782949202957154L7.219449937362671,11.501179202957154L7.219449937362671,11.601179202957153L8.628419937362672,11.601179202957153L8.628419937362672,10.782949202957154L7.219429937362671,10.782949202957154ZM9.32544993736267,10.782949202957154L9.32544993736267,11.601179202957153L10.73422993736267,11.601179202957153L10.73422993736267,10.782949202957154L9.32544993736267,10.782949202957154ZM11.34768993736267,10.782949202957154L11.34768993736267,11.601179202957153L12.75646993736267,11.601179202957153L12.75646993736267,10.782949202957154L11.34768993736267,10.782949202957154ZM7.419449937362671,11.401179202957152L7.419439937362671,10.982949202957153L8.428419937362671,10.982949202957153L8.428419937362671,11.401179202957152L7.419449937362671,11.401179202957152ZM5.3971799373626705,11.401179202957152L5.3971799373626705,10.982949202957153L6.405969937362671,10.982949202957153L6.405969937362671,11.401179202957152L5.3971799373626705,11.401179202957152ZM9.525449937362671,11.401179202957152L9.525449937362671,10.982949202957153L10.53422993736267,10.982949202957153L10.53422993736267,11.401179202957152L9.525449937362671,11.401179202957152ZM11.547689937362671,11.401179202957152L12.556469937362671,11.401179202957152L12.556469937362671,10.982949202957153L11.547689937362671,10.982949202957153L11.547689937362671,11.401179202957152Z"
                        fillRule="evenodd"
                        fill="#A2ACB2"
                        fillOpacity="1"
                    />
                </g>
                <g>
                    <path
                        d="M10.543261598815917,6.613931255683899L9.170161598815918,6.613931255683899L7.243911598815918,3.8612162556838987C7.178841598815918,3.8547092556838987,7.074711598815918,3.8612162556838987,6.970591598815918,3.8612162556838987L5.864301598815918,3.8612162556838987L5.864301598815918,3.308071255683899L4.764521598815918,4.4143612556838985L5.864301598815918,5.240831255683899L5.864301598815918,4.6876812556838985L6.944561598815918,4.6876812556838985L8.617011598815917,7.167071255683899C8.681891598815918,7.251671255683899,8.786211598815918,7.440391255683899,8.89033159881592,7.440391255683899L10.458661598815919,7.440391255683899C10.654091598815917,7.440391255683899,10.816581598815919,7.271191255683899,10.816581598815919,7.062951255683899C10.823291598815917,6.854711255683899,10.745001598815918,6.613931255683899,10.543261598815917,6.613931255683899Z"
                        fill="#A2ACB2"
                        fillOpacity="1"
                    />
                    <path
                        d="M10.916581598815917,7.064591255683899Q10.922751598815918,6.863201255683899,10.847181598815919,6.714931255683899Q10.744721598815918,6.513931255683898,10.543261598815917,6.513931255683898L9.222241598815918,6.513931255683898L7.299531598815918,3.766279255683899L7.253861598815918,3.761713255683899Q7.205421598815918,3.756868255683899,7.075631598815918,3.7596952556838987Q7.005791598815918,3.761216255683899,6.970591598815918,3.761216255683899L5.9643015988159185,3.761216255683899L5.9643015988159185,3.065639255683899L4.612632598815918,4.425311255683899L5.9643015988159185,5.441061255683898L5.9643015988159185,4.787681255683899L6.891391598815918,4.787681255683899L8.535811598815918,7.225511255683899L8.537661598815918,7.227931255683899Q8.549091598815918,7.242821255683899,8.577961598815918,7.2846412556838995Q8.754511598815919,7.540391255683899,8.89033159881592,7.540391255683899L10.458661598815919,7.540391255683899Q10.648461598815917,7.540391255683899,10.783331598815918,7.3987112556838985Q10.916021598815918,7.259341255683899,10.916581598815917,7.064591255683899ZM10.668991598815918,6.805761255683899Q10.721501598815918,6.908761255683899,10.716631598815919,7.059731255683899L10.716581598815917,7.0613412556838995L10.716581598815917,7.062951255683899Q10.716581598815917,7.340391255683899,10.458661598815919,7.340391255683899L8.89033159881592,7.340391255683899Q8.859471598815919,7.340391255683899,8.742541598815919,7.171021255683899Q8.712901598815918,7.128081255683899,8.698331598815917,7.108791255683899L6.997731598815918,4.587681255683899L5.764304598815918,4.587681255683899L5.764304598815918,5.040591255683899L4.916410598815918,4.403411255683899L5.764304598815918,3.550503255683899L5.764304598815918,3.961216255683899L6.970591598815918,3.961216255683899Q7.007971598815918,3.961216255683899,7.079981598815918,3.959648255683899Q7.149081598815918,3.9581432556838987,7.190131598815918,3.958776255683899L9.118081598815918,6.7139312556838995L10.543261598815917,6.7139312556838995Q10.622181598815917,6.7139312556838995,10.668991598815918,6.805761255683899Z"
                        fillRule="evenodd"
                        fill="#A2ACB2"
                        fillOpacity="1"
                    />
                </g>
                <g>
                    <path
                        d="M10.269245546684264,3.8612620711212156L9.169465546684265,3.8612620711212156C9.058835546684264,3.8612620711212156,8.811545546684265,3.978399071121216,8.740165546684265,4.062997071121216L8.063175546684265,4.687724071121216L8.616325546684266,5.240874071121215L9.169465546684265,4.687724071121216L10.269245546684264,4.687724071121216L10.269245546684264,5.240874071121215L11.369035546684266,4.414404071121216L10.269245546684264,3.314624071121216L10.269245546684264,3.8612620711212156ZM3.600385546684265,10.642174071121214C3.600385546684265,10.866804071121216,3.7824825466842653,11.048894071121216,4.007110546684265,11.048894071121216C4.231737546684265,11.048894071121216,4.4138345466842654,10.866804071121216,4.4138345466842654,10.642174071121214C4.4138345466842654,10.417544071121217,4.231738546684265,10.235454071121215,4.007110546684265,10.235454071121215C3.7824825466842653,10.235454071121215,3.600385988008265,10.417544071121217,3.600385546684265,10.642174071121214Z"
                        fill="#A2ACB2"
                        fillOpacity="1"
                    />
                    <path
                        d="M11.521195546684265,4.425144071121216L10.169245546684266,3.073203071121216L10.169245546684266,3.761262071121216L9.169465546684265,3.761262071121216Q8.867095546684265,3.761262071121216,8.667915546684265,3.9936020711212157L7.918855546684265,4.684834071121216L8.616325546684266,5.3822940711212155L9.210885546684265,4.787724071121216L10.169245546684266,4.787724071121216L10.169245546684266,5.441104071121216L11.521195546684265,4.425144071121216ZM10.369245546684265,3.5560450711212157L10.369245546684265,3.961262071121216L9.169465546684265,3.961262071121216Q8.956845546684265,3.961262071121216,8.816595546684265,4.1274840711212155L8.812575546684265,4.132255071121216L8.207495546684264,4.690624071121215L8.616325546684266,5.099454071121215L9.128045546684266,4.587724071121215L10.369245546684265,4.587724071121215L10.369245546684265,5.040634071121216L11.216865546684264,4.403664071121216L10.369245546684265,3.5560450711212157ZM4.365418546684265,10.283864071121215Q4.217002546684265,10.135454071121217,4.007110546684265,10.135454071121217Q3.797218546684265,10.135454071121217,3.648802046684265,10.283864071121215Q3.5003859466842653,10.432284071121217,3.500385546684265,10.642174071121214Q3.500385546684265,10.852064071121216,3.6488017466842653,11.000484071121216Q3.7972175466842653,11.148894071121216,4.007110546684265,11.148894071121216Q4.217002546684265,11.148894071121216,4.365418546684265,11.000484071121216Q4.513834546684265,10.852064071121216,4.513834546684265,10.642174071121214Q4.513834546684265,10.432284071121217,4.365418546684265,10.283864071121215ZM3.7003855466842652,10.642174071121214Q3.700386546684265,10.335454071121216,4.007110546684265,10.335454071121216Q4.134159546684265,10.335454071121216,4.223997546684265,10.425284071121215Q4.313834546684265,10.515124071121216,4.313834546684265,10.642174071121214Q4.313834546684265,10.948894071121217,4.007110546684265,10.948894071121217Q3.7003855466842652,10.948894071121217,3.7003855466842652,10.642174071121214Z"
                        fillRule="evenodd"
                        fill="#A2ACB2"
                        fillOpacity="1"
                    />
                </g>
            </g>
        </g>
    </svg>
);

export const backupLogEnabledIcon = () => (
    <svg xmlns="http://www.w3.org/2000/svg" fill="none" version="1.1" width="16" height="16" viewBox="0 0 16 16">
        <g>
            <g />
            <g transform="matrix(-1,0,0,-1,30,30)">
                <path
                    d="M19.97368,15L27.7105,15C28.4227,15.000000843249,29,15.577318,29,16.28947L29,24.02632C29,24.73847,28.4227,25.3158,27.7105,25.3158L19.97368,25.3158C19.26153,25.3158,18.68421,24.73847,18.68421,24.026310000000002L18.68421,16.28947C18.68421,15.577317,19.26153,15.0000003373,19.97368,15ZM19.97368,16.10526C19.87195,16.10526,19.78947,16.18774,19.78947,16.28947L19.78947,24.02632C19.78947,24.12805,19.87195,24.21053,19.97368,24.21053L27.7105,24.21053C27.8123,24.21053,27.8947,24.12805,27.8947,24.02632L27.8947,16.28947C27.8947,16.18774,27.8123,16.10526,27.7105,16.10526L19.97368,16.10526ZM24.21053,26.4211C24.21053,26.1158,24.45795,25.8684,24.76316,25.8684C25.0684,25.8684,25.3158,26.1158,25.3158,26.4211L25.3158,27.7105C25.3158,28.4227,24.73847,29,24.026310000000002,29L16.28947,29C15.577317,29,15.00000016865,28.4227,15,27.7105L15,19.97368C15,19.26153,15.577317,18.68421,16.28947,18.68421L17.57895,18.68421C17.88416,18.68421,18.13158,18.93163,18.13158,19.23684C18.13158,19.54205,17.88416,19.78947,17.57895,19.78947L16.28947,19.78947C16.18774,19.78947,16.10526,19.87195,16.10526,19.97368L16.10526,27.7105C16.10526,27.8123,16.18774,27.8947,16.28947,27.8947L24.02632,27.8947C24.12805,27.8947,24.21053,27.8123,24.21053,27.7105L24.21053,26.4211Z"
                    fill="#FFFFFF"
                    fillOpacity="1"
                />
                <path
                    d="M18.92047,15.236257Q18.48421,15.672514,18.48421,16.28947L18.48421,24.026310000000002Q18.48421,24.64327,18.92047,25.0795Q19.35672,25.5158,19.97368,25.5158L27.7105,25.5158Q28.3275,25.5158,28.7637,25.0795Q29.2,24.64327,29.2,24.02632L29.2,16.28947Q29.2,15.672514,28.7637,15.236257Q28.3275,14.800001,27.7105,14.8L19.97368,14.8Q19.35672,14.8,18.92047,15.236257ZM18.88421,24.026310000000002L18.88421,16.28947Q18.88421,15.838199,19.203310000000002,15.5191Q19.52241,15.2,19.97368,15.2L27.7105,15.2Q28.1618,15.200001,28.4809,15.5191Q28.8,15.838199,28.8,16.28947L28.8,24.02632Q28.8,24.47759,28.4809,24.796689999999998Q28.1618,25.1158,27.7105,25.1158L19.97368,25.1158Q19.52241,25.1158,19.203310000000002,24.796689999999998Q18.88421,24.47759,18.88421,24.026310000000002ZM27.9822,16.0178Q27.8697,15.905263,27.7105,15.905263L19.97368,15.905263Q19.589480000000002,15.905263,19.58947,16.28947L19.58947,24.02632Q19.58947,24.18546,19.70201,24.29799Q19.81454,24.41053,19.97368,24.41053L27.7105,24.41053Q27.8697,24.41053,27.9822,24.29799Q28.0947,24.18546,28.0947,24.02632L28.0947,16.28947Q28.0947,16.13033,27.9822,16.0178ZM27.694699999999997,16.30526L19.98947,16.30526L19.98947,24.01053L27.694699999999997,24.01053L27.694699999999997,16.30526ZM24.01053,27.694699999999997L24.01053,26.4211Q24.01053,26.109299999999998,24.23097,25.8889Q24.45141,25.6684,24.76316,25.6684Q25.0749,25.6684,25.295299999999997,25.8889Q25.5158,26.109299999999998,25.5158,26.4211L25.5158,27.7105Q25.5158,28.3275,25.0795,28.7637Q24.64327,29.2,24.026310000000002,29.2L16.28947,29.2Q15.672515,29.2,15.236257,28.7637Q14.8,28.3275,14.8,27.7105L14.8,19.97368Q14.8,19.35672,15.236257,18.92047Q15.672513,18.48421,16.28947,18.48421L17.57895,18.48421Q17.8907,18.48421,18.11114,18.70465Q18.33158,18.92509,18.33158,19.23684Q18.33158,19.54859,18.11114,19.76903Q17.8907,19.98947,17.57895,19.98947L16.30526,19.98947L16.30526,27.694699999999997L24.01053,27.694699999999997ZM24.29799,27.9822Q24.41053,27.8697,24.41053,27.7105L24.41053,26.4211Q24.41053,26.275,24.51381,26.1717Q24.617089999999997,26.0684,24.76316,26.0684Q24.909219999999998,26.0684,25.0125,26.1717Q25.1158,26.275,25.1158,26.4211L25.1158,27.7105Q25.1158,28.1618,24.796689999999998,28.4809Q24.47759,28.8,24.02632,28.8L16.28947,28.8Q15.8382,28.8,15.519099,28.4809Q15.2,28.1618,15.2,27.7105L15.2,19.97368Q15.2,19.52241,15.519099,19.203310000000002Q15.838199,18.88421,16.28947,18.88421L17.57895,18.88421Q17.72501,18.88421,17.8283,18.98749Q17.93158,19.09078,17.93158,19.23684Q17.93158,19.38291,17.8283,19.48619Q17.72501,19.58947,17.57895,19.58947L16.28947,19.58947Q15.905263,19.589480000000002,15.905263,19.97368L15.905263,27.7105Q15.905263,27.8697,16.0178,27.9822Q16.13033,28.0947,16.28948,28.0947L24.02632,28.0947Q24.18546,28.0947,24.29799,27.9822Z"
                    fillRule="evenodd"
                    fill="#FFFFFF"
                    fillOpacity="1"
                />
            </g>
        </g>
    </svg>
);

export const backupLogDisabledIcon = () => (
    <svg xmlns="http://www.w3.org/2000/svg" fill="none" version="1.1" width="16" height="16" viewBox="0 0 16 16">
        <g>
            <g />
            <g transform="matrix(-1,0,0,-1,30,30)">
                <path
                    d="M19.97368,15L27.7105,15C28.4227,15.000000843249,29,15.577318,29,16.28947L29,24.02632C29,24.73847,28.4227,25.3158,27.7105,25.3158L19.97368,25.3158C19.26153,25.3158,18.68421,24.73847,18.68421,24.026310000000002L18.68421,16.28947C18.68421,15.577317,19.26153,15.0000003373,19.97368,15ZM19.97368,16.10526C19.87195,16.10526,19.78947,16.18774,19.78947,16.28947L19.78947,24.02632C19.78947,24.12805,19.87195,24.21053,19.97368,24.21053L27.7105,24.21053C27.8123,24.21053,27.8947,24.12805,27.8947,24.02632L27.8947,16.28947C27.8947,16.18774,27.8123,16.10526,27.7105,16.10526L19.97368,16.10526ZM24.21053,26.4211C24.21053,26.1158,24.45795,25.8684,24.76316,25.8684C25.0684,25.8684,25.3158,26.1158,25.3158,26.4211L25.3158,27.7105C25.3158,28.4227,24.73847,29,24.026310000000002,29L16.28947,29C15.577317,29,15.00000016865,28.4227,15,27.7105L15,19.97368C15,19.26153,15.577317,18.68421,16.28947,18.68421L17.57895,18.68421C17.88416,18.68421,18.13158,18.93163,18.13158,19.23684C18.13158,19.54205,17.88416,19.78947,17.57895,19.78947L16.28947,19.78947C16.18774,19.78947,16.10526,19.87195,16.10526,19.97368L16.10526,27.7105C16.10526,27.8123,16.18774,27.8947,16.28947,27.8947L24.02632,27.8947C24.12805,27.8947,24.21053,27.8123,24.21053,27.7105L24.21053,26.4211Z"
                    fill="#B3BBC8"
                    fillOpacity="1"
                />
                <path
                    d="M18.92047,15.236257Q18.48421,15.672514,18.48421,16.28947L18.48421,24.026310000000002Q18.48421,24.64327,18.92047,25.0795Q19.35672,25.5158,19.97368,25.5158L27.7105,25.5158Q28.3275,25.5158,28.7637,25.0795Q29.2,24.64327,29.2,24.02632L29.2,16.28947Q29.2,15.672514,28.7637,15.236257Q28.3275,14.800001,27.7105,14.8L19.97368,14.8Q19.35672,14.8,18.92047,15.236257ZM18.88421,24.026310000000002L18.88421,16.28947Q18.88421,15.838199,19.203310000000002,15.5191Q19.52241,15.2,19.97368,15.2L27.7105,15.2Q28.1618,15.200001,28.4809,15.5191Q28.8,15.838199,28.8,16.28947L28.8,24.02632Q28.8,24.47759,28.4809,24.796689999999998Q28.1618,25.1158,27.7105,25.1158L19.97368,25.1158Q19.52241,25.1158,19.203310000000002,24.796689999999998Q18.88421,24.47759,18.88421,24.026310000000002ZM27.9822,16.0178Q27.8697,15.905263,27.7105,15.905263L19.97368,15.905263Q19.589480000000002,15.905263,19.58947,16.28947L19.58947,24.02632Q19.58947,24.18546,19.70201,24.29799Q19.81454,24.41053,19.97368,24.41053L27.7105,24.41053Q27.8697,24.41053,27.9822,24.29799Q28.0947,24.18546,28.0947,24.02632L28.0947,16.28947Q28.0947,16.13033,27.9822,16.0178ZM27.694699999999997,16.30526L19.98947,16.30526L19.98947,24.01053L27.694699999999997,24.01053L27.694699999999997,16.30526ZM24.01053,27.694699999999997L24.01053,26.4211Q24.01053,26.109299999999998,24.23097,25.8889Q24.45141,25.6684,24.76316,25.6684Q25.0749,25.6684,25.295299999999997,25.8889Q25.5158,26.109299999999998,25.5158,26.4211L25.5158,27.7105Q25.5158,28.3275,25.0795,28.7637Q24.64327,29.2,24.026310000000002,29.2L16.28947,29.2Q15.672515,29.2,15.236257,28.7637Q14.8,28.3275,14.8,27.7105L14.8,19.97368Q14.8,19.35672,15.236257,18.92047Q15.672513,18.48421,16.28947,18.48421L17.57895,18.48421Q17.8907,18.48421,18.11114,18.70465Q18.33158,18.92509,18.33158,19.23684Q18.33158,19.54859,18.11114,19.76903Q17.8907,19.98947,17.57895,19.98947L16.30526,19.98947L16.30526,27.694699999999997L24.01053,27.694699999999997ZM24.29799,27.9822Q24.41053,27.8697,24.41053,27.7105L24.41053,26.4211Q24.41053,26.275,24.51381,26.1717Q24.617089999999997,26.0684,24.76316,26.0684Q24.909219999999998,26.0684,25.0125,26.1717Q25.1158,26.275,25.1158,26.4211L25.1158,27.7105Q25.1158,28.1618,24.796689999999998,28.4809Q24.47759,28.8,24.02632,28.8L16.28947,28.8Q15.8382,28.8,15.519099,28.4809Q15.2,28.1618,15.2,27.7105L15.2,19.97368Q15.2,19.52241,15.519099,19.203310000000002Q15.838199,18.88421,16.28947,18.88421L17.57895,18.88421Q17.72501,18.88421,17.8283,18.98749Q17.93158,19.09078,17.93158,19.23684Q17.93158,19.38291,17.8283,19.48619Q17.72501,19.58947,17.57895,19.58947L16.28947,19.58947Q15.905263,19.589480000000002,15.905263,19.97368L15.905263,27.7105Q15.905263,27.8697,16.0178,27.9822Q16.13033,28.0947,16.28948,28.0947L24.02632,28.0947Q24.18546,28.0947,24.29799,27.9822Z"
                    fillRule="evenodd"
                    fill="#B3BBC8"
                    fillOpacity="1"
                />
            </g>
        </g>
    </svg>
);

export const uploadDisabledIcon = () => (
    <svg
        xmlns="http://www.w3.org/2000/svg"
        fill="none"
        version="1.1"
        width="16.203125"
        height="16"
        viewBox="0 0 16.203125 16"
    >
        <g>
            <g />
            <g>
                <g>
                    <path
                        d="M8.111708114624022,8.407145977020264C7.939838114624024,8.407145977020264,7.769888114624024,8.472073077020264,7.640028114624023,8.603836977020263L5.772422114624024,10.471445977020263C5.510803614624024,10.733065977020264,5.510803614624024,11.155085977020263,5.772422114624024,11.416705977020264C6.034040114624023,11.678325977020265,6.456066114624024,11.678325977020265,6.717688114624023,11.416705977020264L8.113618114624023,10.020775977020264L9.507638114624024,11.416705977020264C9.769258114624023,11.678325977020265,10.191288114624022,11.678325977020265,10.452908114624023,11.416705977020264C10.714518114624024,11.155085977020263,10.714518114624024,10.733065977020264,10.452908114624023,10.471445977020263L8.585298114624024,8.603836977020263C8.453528114624024,8.473982677020263,8.281668114624024,8.407145977020264,8.111708114624022,8.407145977020264Z"
                        fill="#B3BBC8"
                        fillOpacity="1"
                    />
                    <path
                        d="M10.523618114624023,10.400735977020263L8.656008114624024,8.533125977020264L8.655488114624024,8.532611977020263Q8.426708114624024,8.307145977020264,8.111708114624022,8.307145977020264Q7.792018114624023,8.307145977020264,7.568808114624023,8.533643977020263L5.701711114624024,10.400735977020263Q5.476208114624024,10.626235977020263,5.476208114624024,10.944075977020264Q5.476208114624024,11.261915977020264,5.701711114624024,11.487415977020264Q5.927214114624023,11.712925977020264,6.245053114624024,11.712925977020264Q6.562892114624024,11.712925977020264,6.788398114624023,11.487415977020264L8.113568114624023,10.162245977020264L9.436878114624022,11.487375977020264Q9.662428114624024,11.712925977020264,9.980268114624025,11.712925977020264Q10.298108114624023,11.712925977020264,10.523618114624023,11.487415977020264Q10.749118114624023,11.261915977020264,10.749118114624023,10.944075977020264Q10.749118114624023,10.626235977020263,10.523618114624023,10.400735977020263ZM8.514578114624022,8.674549977020263L8.515098114624024,8.675061977020263L10.382188114624023,10.542155977020263Q10.549118114624022,10.709085977020264,10.549118114624022,10.944075977020264Q10.549118114624022,11.179075977020263,10.382188114624023,11.345995977020264Q10.215268114624024,11.512925977020263,9.980268114624025,11.512925977020263Q9.745278114624023,11.512925977020263,9.578398114624022,11.346045977020264L8.113668114624023,9.879305977020264L6.646978114624023,11.345995977020264Q6.480049114624023,11.512925977020263,6.245053114624024,11.512925977020263Q6.010057114624024,11.512925977020263,5.843132114624024,11.345995977020264Q5.676208114624023,11.179075977020263,5.676208114624023,10.944075977020264Q5.676208114624023,10.709085977020264,5.843132114624024,10.542155977020263L7.711258114624023,8.674029977020263Q7.875718114624023,8.507145977020263,8.111708114624022,8.507145977020263Q8.344358114624024,8.507145977020263,8.514578114624022,8.674549977020263Z"
                        fillRule="evenodd"
                        fill="#B3BBC8"
                        fillOpacity="1"
                    />
                </g>
                <g>
                    <path
                        d="M8.112423034088135,8.407145977020264C7.743867034088135,8.407145977020264,7.444056034088135,8.706956977020264,7.444056034088135,9.075512977020264L7.444056034088135,14.011885977020263C7.444056034088135,14.380445977020264,7.743867034088135,14.680255977020263,8.112423034088135,14.680255977020263C8.480976034088135,14.680255977020263,8.780786034088134,14.380445977020264,8.780786034088134,14.011885977020263L8.780786034088134,9.075512977020264C8.780786034088134,8.706956977020264,8.480976034088135,8.407145977020264,8.112423034088135,8.407145977020264Z"
                        fill="#B3BBC8"
                        fillOpacity="1"
                    />
                    <path
                        d="M8.880786034088135,14.011885977020263L8.880786034088135,9.075512977020264Q8.880786034088135,8.757673977020264,8.655526034088135,8.532409977020263Q8.430263034088135,8.307145977020264,8.112424034088134,8.307146077020263Q7.7945840340881345,8.307145977020264,7.569320034088134,8.532409977020263Q7.3440561340881345,8.757673977020264,7.3440561340881345,9.075512977020264L7.344056034088135,14.011885977020263Q7.344056034088135,14.329725977020264,7.569320034088134,14.554985977020264Q7.7945840340881345,14.780255977020264,8.112423034088135,14.780255977020264Q8.430262034088134,14.780255977020264,8.655526034088135,14.554985977020264Q8.880786034088135,14.329725977020264,8.880786034088135,14.011885977020263ZM8.514106034088135,8.673831977020264Q8.680786034088134,8.840516977020263,8.680786034088134,9.075512977020264L8.680786034088134,14.011885977020263Q8.680786034088134,14.246875977020263,8.514106034088135,14.413565977020264Q8.347420034088135,14.580255977020265,8.112423034088135,14.580255977020265Q7.877427034088135,14.580255977020265,7.710742034088135,14.413565977020264Q7.544056034088134,14.246875977020263,7.544056034088134,14.011885977020263L7.544056034088134,9.075512977020264Q7.544056034088134,8.840516977020263,7.710742034088135,8.673831977020264Q7.877427034088135,8.507145977020263,8.112424034088134,8.507145977020263Q8.347420034088135,8.507145977020263,8.514106034088135,8.673831977020264Z"
                        fillRule="evenodd"
                        fill="#B3BBC8"
                        fillOpacity="1"
                    />
                </g>
                <g>
                    <path
                        d="M3.5732525,13.238604189758302C1.6579025,13.238604189758302,0.1015625,11.680354189758301,0.1015625,9.766914189758301C0.1015625,7.851564189758301,1.6598125,6.295224189758301,3.5732525,6.295224189758301C3.9418125,6.295224189758301,4.2416225,6.595035189758301,4.2416225,6.963591189758301C4.2416225,7.332144189758301,3.9418125,7.631954189758301,3.5732525,7.631954189758301C2.3950225,7.631954189758301,1.4382925,8.590584189758301,1.4382925,9.766914189758301C1.4382925,10.9432441897583,2.3969225,11.901874189758301,3.5732525,11.901874189758301C3.9418125,11.901874189758301,4.2416225,12.2016841897583,4.2416225,12.5702441897583C4.2416225,12.938794189758301,3.9418125,13.238604189758302,3.5732525,13.238604189758302Z"
                        fill="#B3BBC8"
                        fillOpacity="1"
                    />
                    <path
                        d="M4.1163525,13.113344189758301Q4.3416225,12.888074189758301,4.3416225,12.5702441897583Q4.3416225,12.252404189758302,4.1163525,12.0271341897583Q3.8910925,11.801874189758301,3.5732525,11.801874189758301Q2.7324325,11.801874189758301,2.1353625,11.2048041897583Q1.5383025,10.6077441897583,1.5382925,9.766914189758301Q1.5382925,8.925744189758301,2.1346725,8.329004189758301Q2.7313425,7.731954189758301,3.5732525,7.731954189758301Q3.8910925,7.731954189758301,4.1163525,7.506694189758301Q4.3416225,7.281430189758301,4.3416225,6.963591189758301Q4.3416225,6.6457521897583005,4.1163525,6.4204881897583Q3.8910925,6.195224189758301,3.5732525,6.195224189758301Q2.0964324999999997,6.195224189758301,1.0491705,7.242089189758301Q0.0015624999999999944,8.289304189758301,0.0015624999999999944,9.766914189758301Q0.0015624999999999944,11.2437341897583,1.0484274999999998,12.290994189758301Q2.0956425000000003,13.3386041897583,3.5732525,13.3386041897583Q3.8910925,13.3386041897583,4.1163525,13.113344189758301ZM3.9749325,12.1685541897583Q4.1416225,12.335244189758301,4.1416225,12.5702441897583Q4.1416225,12.805234189758302,3.9749325,12.971924189758301Q3.8082525,13.1386041897583,3.5732525,13.1386041897583Q2.1785025,13.1386041897583,1.1898725,12.1496041897583Q0.2015625,11.160914189758302,0.2015625,9.766914189758301Q0.2015625,8.372164189758301,1.1905625,7.383534189758301Q2.1792525,6.3952241897583,3.5732525,6.3952241897583Q3.8082525,6.3952241897583,3.9749325,6.561910189758301Q4.1416225,6.728595189758301,4.1416225,6.963591189758301Q4.1416225,7.198588189758301,3.9749325,7.365274189758301Q3.8082525,7.5319541897583004,3.5732525,7.5319541897583004Q2.6484625,7.5319541897583004,1.9932025,8.187624189758301Q1.3382925,8.842934189758301,1.3382925,9.766914189758301Q1.3382925,10.690584189758301,1.9939425,11.346224189758301Q2.6495825,12.0018741897583,3.5732525,12.0018741897583Q3.8082525,12.0018741897583,3.9749325,12.1685541897583Z"
                        fillRule="evenodd"
                        fill="#B3BBC8"
                        fillOpacity="1"
                    />
                </g>
                <g>
                    <path
                        d="M12.635597422027589,7.61298515625C12.267047422027588,7.61298515625,11.967227422027587,7.31317515625,11.967227422027587,6.94461515625C11.967227422027587,4.81729515625,10.237117422027588,3.08717515625,8.111707422027589,3.08717515625C5.984387422027588,3.08717515625,4.256177422027588,4.81729515625,4.256177422027588,6.94270515625C4.256177422027588,7.31126515625,3.956367422027588,7.61107515625,3.587814422027588,7.61107515625C3.219258422027588,7.61107515625,2.919447422027588,7.31126515625,2.919447422027588,6.94270515625C2.919447422027588,5.55631515625,3.459870422027588,4.25204515625,4.4414174220275875,3.27050515625C5.422957422027588,2.28895815625,6.727227422027588,1.74853515625,8.113617422027588,1.74853515625C9.499997422027587,1.74853515625,10.804277422027589,2.28895815625,11.785817422027588,3.27050515625C12.763547422027587,4.25204515625,13.303947422027587,5.55631515625,13.303947422027587,6.94461515625C13.303947422027587,7.31317515625,13.004147422027588,7.61298515625,12.635597422027589,7.61298515625Z"
                        fill="#B3BBC8"
                        fillOpacity="1"
                    />
                    <path
                        d="M13.178747422027588,7.48771515625Q13.403947422027588,7.26245515625,13.403947422027588,6.94461515625Q13.403947422027588,4.75327515625,11.856527422027588,3.19979515625Q10.305257422027587,1.64853525625,8.113617422027588,1.64853525625Q5.921957422027588,1.64853515625,4.370707422027587,3.19979515625Q2.819447422027588,4.7510551562500005,2.819447522027588,6.94270515625Q2.819447522027588,7.26054515625,3.044711422027588,7.48580515625Q3.269975422027588,7.71107515625,3.587815422027588,7.71107515625Q3.9056544220275877,7.71107515625,4.130917422027588,7.48580515625Q4.356177422027588,7.26054515625,4.356177422027588,6.94270515625Q4.356177422027588,5.3897351562499995,5.456927422027588,4.28861515625Q6.557977422027587,3.18717515625,8.111707422027589,3.18717515625Q9.664477422027588,3.18717515625,10.765777422027588,4.2888551562499995Q11.867227422027588,5.39068515625,11.867227422027588,6.94461515625Q11.867227422027588,7.26245515625,12.092497422027588,7.48771515625Q12.317767422027588,7.71298515625,12.635597422027589,7.71298515625Q12.953447422027589,7.71298515625,13.178747422027588,7.48771515625ZM11.715107422027588,3.34121515625Q13.203947422027587,4.83588515625,13.203947422027587,6.94461515625Q13.203947422027587,7.17961515625,13.037247422027589,7.34629515625Q12.870597422027588,7.51298515625,12.635597422027589,7.51298515625Q12.400607422027587,7.51298515625,12.233917422027588,7.34629515625Q12.067237422027588,7.17961515625,12.067237422027588,6.94461515625Q12.067247422027588,5.30786515625,10.907217422027589,4.14745515625Q9.747337422027588,2.98718515625,8.111707422027589,2.98717515625Q6.4751074220275875,2.98717515625,5.3154774220275876,4.147215156250001Q4.156187422027588,5.30690515625,4.1561774220275876,6.94270515625Q4.1561774220275876,7.17770515625,3.989497422027588,7.34438515625Q3.8228114220275877,7.51107515625,3.587815422027588,7.51107515625Q3.352818422027588,7.51107515625,3.186133422027588,7.34438515625Q3.019447422027588,7.17770515625,3.019447422027588,6.94270515625Q3.0194473220275877,4.83389515625,4.5121274220275875,3.34121515625Q6.0048074220275875,1.84853365625,8.113617422027588,1.84853515625Q10.222417422027588,1.84853515625,11.715107422027588,3.34121515625Z"
                        fillRule="evenodd"
                        fill="#B3BBC8"
                        fillOpacity="1"
                    />
                </g>
                <g>
                    <path
                        d="M12.629870982543945,13.238604189758302C12.261314982543945,13.238604189758302,11.961503982543945,12.938794189758301,11.961503982543945,12.5702441897583C11.961503982543945,12.2016841897583,12.261314982543945,11.901874189758301,12.629870982543945,11.901874189758301C13.808103982543946,11.901874189758301,14.764823982543945,10.9432441897583,14.764823982543945,9.766914189758301C14.764823982543945,8.590584189758301,13.806193982543945,7.631954189758301,12.629870982543945,7.631954189758301C12.261314982543945,7.631954189758301,11.961503982543945,7.332144189758301,11.961503982543945,6.963591189758301C11.961503982543945,6.595035189758301,12.261314982543945,6.295224189758301,12.629870982543945,6.295224189758301C14.545223982543945,6.295224189758301,16.101563982543944,7.853474189758301,16.101563982543944,9.766914189758301C16.101563982543944,11.680354189758301,14.543313982543946,13.238604189758302,12.629870982543945,13.238604189758302Z"
                        fill="#B3BBC8"
                        fillOpacity="1"
                    />
                    <path
                        d="M15.153963982543946,12.2910141897583Q16.201563982543945,11.243414189758301,16.201563982543945,9.766914189758301Q16.201563982543945,8.290094189758301,15.154693982543945,7.242832189758301Q14.107483982543945,6.195224189758301,12.629870982543945,6.1952242897583005Q12.312031982543946,6.1952242897583005,12.086767982543945,6.4204881897583Q11.861503982543946,6.6457521897583005,11.861504082543945,6.963592189758301Q11.861503982543946,7.281431189758301,12.086767982543945,7.506694189758301Q12.312031982543946,7.731954189758301,12.629870982543945,7.731954189758301Q13.470693982543946,7.731954189758301,14.067763982543944,8.3290241897583Q14.664823982543945,8.9260941897583,14.664823982543945,9.766914189758301Q14.664823982543945,10.608094189758301,14.068453982543945,11.204824189758302Q13.471783982543945,11.801874189758301,12.629870982543945,11.801874189758301Q12.312031982543946,11.801874189758301,12.086767982543945,12.0271341897583Q11.861503982543946,12.252404189758302,11.861504082543945,12.5702441897583Q11.861503982543946,12.8880841897583,12.086767982543945,13.113344189758301Q12.312031982543946,13.3386041897583,12.629871982543944,13.3386041897583Q14.106373982543946,13.3386041897583,15.153963982543946,12.2910141897583ZM15.013253982543945,7.384224189758301Q16.001563982543946,8.372914189758301,16.001563982543946,9.766914189758301Q16.001563982543946,11.1605741897583,15.012543982543946,12.1495941897583Q14.023533982543945,13.1386041897583,12.629871982543944,13.1386041897583Q12.394874982543945,13.1386041897583,12.228189982543945,12.971924189758301Q12.061503982543945,12.805234189758302,12.061503982543945,12.5702441897583Q12.061503982543945,12.335244189758301,12.228189982543945,12.1685541897583Q12.394874982543945,12.0018741897583,12.629870982543945,12.0018741897583Q13.554653982543945,12.0018741897583,14.209923982543945,11.3462041897583Q14.864823982543946,10.6908941897583,14.864823982543946,9.766914189758301Q14.864823982543946,8.843254189758301,14.209183982543944,8.1876041897583Q13.553533982543945,7.5319541897583004,12.629870982543945,7.5319541897583004Q12.394874982543945,7.5319541897583004,12.228189982543945,7.365274189758301Q12.061503982543945,7.198588189758301,12.061503982543945,6.963592189758301Q12.061503982543945,6.728595189758301,12.228189982543945,6.561910189758301Q12.394874982543945,6.3952241897583,12.629870982543945,6.3952241897583Q14.024623982543945,6.3952241897583,15.013253982543945,7.384224189758301Z"
                        fillRule="evenodd"
                        fill="#B3BBC8"
                        fillOpacity="1"
                    />
                </g>
            </g>
        </g>
    </svg>
);

export const uploadEnabledIcon = () => (
    <svg
        xmlns="http://www.w3.org/2000/svg"
        fill="none"
        version="1.1"
        width="16.203125"
        height="16"
        viewBox="0 0 16.203125 16"
    >
        <g>
            <g />
            <g>
                <g>
                    <path
                        d="M8.111708114624022,8.407145977020264C7.939838114624024,8.407145977020264,7.769888114624024,8.472073077020264,7.640028114624023,8.603836977020263L5.772422114624024,10.471445977020263C5.510803614624024,10.733065977020264,5.510803614624024,11.155085977020263,5.772422114624024,11.416705977020264C6.034040114624023,11.678325977020265,6.456066114624024,11.678325977020265,6.717688114624023,11.416705977020264L8.113618114624023,10.020775977020264L9.507638114624024,11.416705977020264C9.769258114624023,11.678325977020265,10.191288114624022,11.678325977020265,10.452908114624023,11.416705977020264C10.714518114624024,11.155085977020263,10.714518114624024,10.733065977020264,10.452908114624023,10.471445977020263L8.585298114624024,8.603836977020263C8.453528114624024,8.473982677020263,8.281668114624024,8.407145977020264,8.111708114624022,8.407145977020264Z"
                        fill="#FFFFFF"
                        fillOpacity="1"
                    />
                    <path
                        d="M10.523618114624023,10.400735977020263L8.656008114624024,8.533125977020264L8.655488114624024,8.532611977020263Q8.426708114624024,8.307145977020264,8.111708114624022,8.307145977020264Q7.792018114624023,8.307145977020264,7.568808114624023,8.533643977020263L5.701711114624024,10.400735977020263Q5.476208114624024,10.626235977020263,5.476208114624024,10.944075977020264Q5.476208114624024,11.261915977020264,5.701711114624024,11.487415977020264Q5.927214114624023,11.712925977020264,6.245053114624024,11.712925977020264Q6.562892114624024,11.712925977020264,6.788398114624023,11.487415977020264L8.113568114624023,10.162245977020264L9.436878114624022,11.487375977020264Q9.662428114624024,11.712925977020264,9.980268114624025,11.712925977020264Q10.298108114624023,11.712925977020264,10.523618114624023,11.487415977020264Q10.749118114624023,11.261915977020264,10.749118114624023,10.944075977020264Q10.749118114624023,10.626235977020263,10.523618114624023,10.400735977020263ZM8.514578114624022,8.674549977020263L8.515098114624024,8.675061977020263L10.382188114624023,10.542155977020263Q10.549118114624022,10.709085977020264,10.549118114624022,10.944075977020264Q10.549118114624022,11.179075977020263,10.382188114624023,11.345995977020264Q10.215268114624024,11.512925977020263,9.980268114624025,11.512925977020263Q9.745278114624023,11.512925977020263,9.578398114624022,11.346045977020264L8.113668114624023,9.879305977020264L6.646978114624023,11.345995977020264Q6.480049114624023,11.512925977020263,6.245053114624024,11.512925977020263Q6.010057114624024,11.512925977020263,5.843132114624024,11.345995977020264Q5.676208114624023,11.179075977020263,5.676208114624023,10.944075977020264Q5.676208114624023,10.709085977020264,5.843132114624024,10.542155977020263L7.711258114624023,8.674029977020263Q7.875718114624023,8.507145977020263,8.111708114624022,8.507145977020263Q8.344358114624024,8.507145977020263,8.514578114624022,8.674549977020263Z"
                        fillRule="evenodd"
                        fill="#FFFFFF"
                        fillOpacity="1"
                    />
                </g>
                <g>
                    <path
                        d="M8.112423034088135,8.407145977020264C7.743867034088135,8.407145977020264,7.444056034088135,8.706956977020264,7.444056034088135,9.075512977020264L7.444056034088135,14.011885977020263C7.444056034088135,14.380445977020264,7.743867034088135,14.680255977020263,8.112423034088135,14.680255977020263C8.480976034088135,14.680255977020263,8.780786034088134,14.380445977020264,8.780786034088134,14.011885977020263L8.780786034088134,9.075512977020264C8.780786034088134,8.706956977020264,8.480976034088135,8.407145977020264,8.112423034088135,8.407145977020264Z"
                        fill="#FFFFFF"
                        fillOpacity="1"
                    />
                    <path
                        d="M8.880786034088135,14.011885977020263L8.880786034088135,9.075512977020264Q8.880786034088135,8.757673977020264,8.655526034088135,8.532409977020263Q8.430263034088135,8.307145977020264,8.112424034088134,8.307146077020263Q7.7945840340881345,8.307145977020264,7.569320034088134,8.532409977020263Q7.3440561340881345,8.757673977020264,7.3440561340881345,9.075512977020264L7.344056034088135,14.011885977020263Q7.344056034088135,14.329725977020264,7.569320034088134,14.554985977020264Q7.7945840340881345,14.780255977020264,8.112423034088135,14.780255977020264Q8.430262034088134,14.780255977020264,8.655526034088135,14.554985977020264Q8.880786034088135,14.329725977020264,8.880786034088135,14.011885977020263ZM8.514106034088135,8.673831977020264Q8.680786034088134,8.840516977020263,8.680786034088134,9.075512977020264L8.680786034088134,14.011885977020263Q8.680786034088134,14.246875977020263,8.514106034088135,14.413565977020264Q8.347420034088135,14.580255977020265,8.112423034088135,14.580255977020265Q7.877427034088135,14.580255977020265,7.710742034088135,14.413565977020264Q7.544056034088134,14.246875977020263,7.544056034088134,14.011885977020263L7.544056034088134,9.075512977020264Q7.544056034088134,8.840516977020263,7.710742034088135,8.673831977020264Q7.877427034088135,8.507145977020263,8.112424034088134,8.507145977020263Q8.347420034088135,8.507145977020263,8.514106034088135,8.673831977020264Z"
                        fillRule="evenodd"
                        fill="#FFFFFF"
                        fillOpacity="1"
                    />
                </g>
                <g>
                    <path
                        d="M3.5732525,13.238604189758302C1.6579025,13.238604189758302,0.1015625,11.680354189758301,0.1015625,9.766914189758301C0.1015625,7.851564189758301,1.6598125,6.295224189758301,3.5732525,6.295224189758301C3.9418125,6.295224189758301,4.2416225,6.595035189758301,4.2416225,6.963591189758301C4.2416225,7.332144189758301,3.9418125,7.631954189758301,3.5732525,7.631954189758301C2.3950225,7.631954189758301,1.4382925,8.590584189758301,1.4382925,9.766914189758301C1.4382925,10.9432441897583,2.3969225,11.901874189758301,3.5732525,11.901874189758301C3.9418125,11.901874189758301,4.2416225,12.2016841897583,4.2416225,12.5702441897583C4.2416225,12.938794189758301,3.9418125,13.238604189758302,3.5732525,13.238604189758302Z"
                        fill="#FFFFFF"
                        fillOpacity="1"
                    />
                    <path
                        d="M4.1163525,13.113344189758301Q4.3416225,12.888074189758301,4.3416225,12.5702441897583Q4.3416225,12.252404189758302,4.1163525,12.0271341897583Q3.8910925,11.801874189758301,3.5732525,11.801874189758301Q2.7324325,11.801874189758301,2.1353625,11.2048041897583Q1.5383025,10.6077441897583,1.5382925,9.766914189758301Q1.5382925,8.925744189758301,2.1346725,8.329004189758301Q2.7313425,7.731954189758301,3.5732525,7.731954189758301Q3.8910925,7.731954189758301,4.1163525,7.506694189758301Q4.3416225,7.281430189758301,4.3416225,6.963591189758301Q4.3416225,6.6457521897583005,4.1163525,6.4204881897583Q3.8910925,6.195224189758301,3.5732525,6.195224189758301Q2.0964324999999997,6.195224189758301,1.0491705,7.242089189758301Q0.0015624999999999944,8.289304189758301,0.0015624999999999944,9.766914189758301Q0.0015624999999999944,11.2437341897583,1.0484274999999998,12.290994189758301Q2.0956425000000003,13.3386041897583,3.5732525,13.3386041897583Q3.8910925,13.3386041897583,4.1163525,13.113344189758301ZM3.9749325,12.1685541897583Q4.1416225,12.335244189758301,4.1416225,12.5702441897583Q4.1416225,12.805234189758302,3.9749325,12.971924189758301Q3.8082525,13.1386041897583,3.5732525,13.1386041897583Q2.1785025,13.1386041897583,1.1898725,12.1496041897583Q0.2015625,11.160914189758302,0.2015625,9.766914189758301Q0.2015625,8.372164189758301,1.1905625,7.383534189758301Q2.1792525,6.3952241897583,3.5732525,6.3952241897583Q3.8082525,6.3952241897583,3.9749325,6.561910189758301Q4.1416225,6.728595189758301,4.1416225,6.963591189758301Q4.1416225,7.198588189758301,3.9749325,7.365274189758301Q3.8082525,7.5319541897583004,3.5732525,7.5319541897583004Q2.6484625,7.5319541897583004,1.9932025,8.187624189758301Q1.3382925,8.842934189758301,1.3382925,9.766914189758301Q1.3382925,10.690584189758301,1.9939425,11.346224189758301Q2.6495825,12.0018741897583,3.5732525,12.0018741897583Q3.8082525,12.0018741897583,3.9749325,12.1685541897583Z"
                        fillRule="evenodd"
                        fill="#FFFFFF"
                        fillOpacity="1"
                    />
                </g>
                <g>
                    <path
                        d="M12.635597422027589,7.61298515625C12.267047422027588,7.61298515625,11.967227422027587,7.31317515625,11.967227422027587,6.94461515625C11.967227422027587,4.81729515625,10.237117422027588,3.08717515625,8.111707422027589,3.08717515625C5.984387422027588,3.08717515625,4.256177422027588,4.81729515625,4.256177422027588,6.94270515625C4.256177422027588,7.31126515625,3.956367422027588,7.61107515625,3.587814422027588,7.61107515625C3.219258422027588,7.61107515625,2.919447422027588,7.31126515625,2.919447422027588,6.94270515625C2.919447422027588,5.55631515625,3.459870422027588,4.25204515625,4.4414174220275875,3.27050515625C5.422957422027588,2.28895815625,6.727227422027588,1.74853515625,8.113617422027588,1.74853515625C9.499997422027587,1.74853515625,10.804277422027589,2.28895815625,11.785817422027588,3.27050515625C12.763547422027587,4.25204515625,13.303947422027587,5.55631515625,13.303947422027587,6.94461515625C13.303947422027587,7.31317515625,13.004147422027588,7.61298515625,12.635597422027589,7.61298515625Z"
                        fill="#FFFFFF"
                        fillOpacity="1"
                    />
                    <path
                        d="M13.178747422027588,7.48771515625Q13.403947422027588,7.26245515625,13.403947422027588,6.94461515625Q13.403947422027588,4.75327515625,11.856527422027588,3.19979515625Q10.305257422027587,1.64853525625,8.113617422027588,1.64853525625Q5.921957422027588,1.64853515625,4.370707422027587,3.19979515625Q2.819447422027588,4.7510551562500005,2.819447522027588,6.94270515625Q2.819447522027588,7.26054515625,3.044711422027588,7.48580515625Q3.269975422027588,7.71107515625,3.587815422027588,7.71107515625Q3.9056544220275877,7.71107515625,4.130917422027588,7.48580515625Q4.356177422027588,7.26054515625,4.356177422027588,6.94270515625Q4.356177422027588,5.3897351562499995,5.456927422027588,4.28861515625Q6.557977422027587,3.18717515625,8.111707422027589,3.18717515625Q9.664477422027588,3.18717515625,10.765777422027588,4.2888551562499995Q11.867227422027588,5.39068515625,11.867227422027588,6.94461515625Q11.867227422027588,7.26245515625,12.092497422027588,7.48771515625Q12.317767422027588,7.71298515625,12.635597422027589,7.71298515625Q12.953447422027589,7.71298515625,13.178747422027588,7.48771515625ZM11.715107422027588,3.34121515625Q13.203947422027587,4.83588515625,13.203947422027587,6.94461515625Q13.203947422027587,7.17961515625,13.037247422027589,7.34629515625Q12.870597422027588,7.51298515625,12.635597422027589,7.51298515625Q12.400607422027587,7.51298515625,12.233917422027588,7.34629515625Q12.067237422027588,7.17961515625,12.067237422027588,6.94461515625Q12.067247422027588,5.30786515625,10.907217422027589,4.14745515625Q9.747337422027588,2.98718515625,8.111707422027589,2.98717515625Q6.4751074220275875,2.98717515625,5.3154774220275876,4.147215156250001Q4.156187422027588,5.30690515625,4.1561774220275876,6.94270515625Q4.1561774220275876,7.17770515625,3.989497422027588,7.34438515625Q3.8228114220275877,7.51107515625,3.587815422027588,7.51107515625Q3.352818422027588,7.51107515625,3.186133422027588,7.34438515625Q3.019447422027588,7.17770515625,3.019447422027588,6.94270515625Q3.0194473220275877,4.83389515625,4.5121274220275875,3.34121515625Q6.0048074220275875,1.84853365625,8.113617422027588,1.84853515625Q10.222417422027588,1.84853515625,11.715107422027588,3.34121515625Z"
                        fillRule="evenodd"
                        fill="#FFFFFF"
                        fillOpacity="1"
                    />
                </g>
                <g>
                    <path
                        d="M12.629870982543945,13.238604189758302C12.261314982543945,13.238604189758302,11.961503982543945,12.938794189758301,11.961503982543945,12.5702441897583C11.961503982543945,12.2016841897583,12.261314982543945,11.901874189758301,12.629870982543945,11.901874189758301C13.808103982543946,11.901874189758301,14.764823982543945,10.9432441897583,14.764823982543945,9.766914189758301C14.764823982543945,8.590584189758301,13.806193982543945,7.631954189758301,12.629870982543945,7.631954189758301C12.261314982543945,7.631954189758301,11.961503982543945,7.332144189758301,11.961503982543945,6.963591189758301C11.961503982543945,6.595035189758301,12.261314982543945,6.295224189758301,12.629870982543945,6.295224189758301C14.545223982543945,6.295224189758301,16.101563982543944,7.853474189758301,16.101563982543944,9.766914189758301C16.101563982543944,11.680354189758301,14.543313982543946,13.238604189758302,12.629870982543945,13.238604189758302Z"
                        fill="#FFFFFF"
                        fillOpacity="1"
                    />
                    <path
                        d="M15.153963982543946,12.2910141897583Q16.201563982543945,11.243414189758301,16.201563982543945,9.766914189758301Q16.201563982543945,8.290094189758301,15.154693982543945,7.242832189758301Q14.107483982543945,6.195224189758301,12.629870982543945,6.1952242897583005Q12.312031982543946,6.1952242897583005,12.086767982543945,6.4204881897583Q11.861503982543946,6.6457521897583005,11.861504082543945,6.963592189758301Q11.861503982543946,7.281431189758301,12.086767982543945,7.506694189758301Q12.312031982543946,7.731954189758301,12.629870982543945,7.731954189758301Q13.470693982543946,7.731954189758301,14.067763982543944,8.3290241897583Q14.664823982543945,8.9260941897583,14.664823982543945,9.766914189758301Q14.664823982543945,10.608094189758301,14.068453982543945,11.204824189758302Q13.471783982543945,11.801874189758301,12.629870982543945,11.801874189758301Q12.312031982543946,11.801874189758301,12.086767982543945,12.0271341897583Q11.861503982543946,12.252404189758302,11.861504082543945,12.5702441897583Q11.861503982543946,12.8880841897583,12.086767982543945,13.113344189758301Q12.312031982543946,13.3386041897583,12.629871982543944,13.3386041897583Q14.106373982543946,13.3386041897583,15.153963982543946,12.2910141897583ZM15.013253982543945,7.384224189758301Q16.001563982543946,8.372914189758301,16.001563982543946,9.766914189758301Q16.001563982543946,11.1605741897583,15.012543982543946,12.1495941897583Q14.023533982543945,13.1386041897583,12.629871982543944,13.1386041897583Q12.394874982543945,13.1386041897583,12.228189982543945,12.971924189758301Q12.061503982543945,12.805234189758302,12.061503982543945,12.5702441897583Q12.061503982543945,12.335244189758301,12.228189982543945,12.1685541897583Q12.394874982543945,12.0018741897583,12.629870982543945,12.0018741897583Q13.554653982543945,12.0018741897583,14.209923982543945,11.3462041897583Q14.864823982543946,10.6908941897583,14.864823982543946,9.766914189758301Q14.864823982543946,8.843254189758301,14.209183982543944,8.1876041897583Q13.553533982543945,7.5319541897583004,12.629870982543945,7.5319541897583004Q12.394874982543945,7.5319541897583004,12.228189982543945,7.365274189758301Q12.061503982543945,7.198588189758301,12.061503982543945,6.963592189758301Q12.061503982543945,6.728595189758301,12.228189982543945,6.561910189758301Q12.394874982543945,6.3952241897583,12.629870982543945,6.3952241897583Q14.024623982543945,6.3952241897583,15.013253982543945,7.384224189758301Z"
                        fillRule="evenodd"
                        fill="#FFFFFF"
                        fillOpacity="1"
                    />
                </g>
            </g>
        </g>
    </svg>
);

export const refreshEnabledIcon = () => (
    <svg xmlns="http://www.w3.org/2000/svg" fill="none" version="1.1" width="16" height="16" viewBox="0 0 16 16">
        <g>
            <g />
            <g>
                <g>
                    <path
                        d="M13.3901,6.67192L10.74975,6.67192C10.41312,6.67192,10.14023,6.39903,10.14023,6.0624C10.14023,5.72577,10.41312,5.45287,10.74975,5.45287L11.8102,5.45287C10.82248,4.051270000000001,9.21451,3.21795,7.49983,3.21905C4.58366,3.21917,2.2197,5.58342,2.2197,8.49983C2.21982,11.41637,4.5840700000000005,13.7806,7.50048,13.7806C10.41702,13.7805,12.7813,11.41625,12.7813,8.49983C12.77,8.15558,13.046,7.87038,13.3905,7.87038C13.7349,7.87038,14.0109,8.15558,13.9997,8.49983C13.9997,12.0891,11.0891,14.9997,7.49983,14.9997C3.91057,14.9997,1,12.0891,1,8.49983C1,4.91057,3.91057,2.000000746834,7.49983,2.000000746834C9.59664,1.999013581,11.5644,3.01243,12.7813,4.72001L12.7813,3.6249599999999997C12.77,3.2807,13.046,2.995502,13.3905,2.995502C13.7349,2.995502,14.0109,3.2807,13.9997,3.6249599999999997L13.9997,6.0624C13.9997,6.39903,13.7268,6.67192,13.3901,6.67192Z"
                        fill="#14C9BB"
                        fillOpacity="1"
                    />
                    <path
                        d="M13.9272,6.59946Q14.1497,6.377,14.1497,6.0624L14.1497,3.62731Q14.1592,3.30721,13.9362,3.07682Q13.7124,2.8455019999999998,13.3905,2.8455019999999998Q13.0686,2.8455019999999998,12.8447,3.07682Q12.6217,3.30721,12.6313,3.62731L12.6313,4.27613Q11.7687,3.21915,10.53921,2.58594Q9.10877,1.849243,7.49983,1.850001Q4.74575,1.850002,2.79788,3.79788Q0.85,5.74575,0.85,8.49983Q0.85,11.25392,2.79788,13.2018Q4.745760000000001,15.1497,7.49983,15.1497Q10.25391,15.1497,12.2018,13.2018Q14.1488,11.25475,14.1497,8.502189999999999Q14.1592,8.182089999999999,13.9362,7.95169Q13.7124,7.72038,13.3905,7.72038Q13.0686,7.72038,12.8447,7.95169Q12.6217,8.18207,12.6313,8.502130000000001Q12.6305,10.62583,11.1285,12.1278Q9.62575,13.6305,7.50049,13.6306Q5.37531,13.6306,3.87255,12.1279Q2.36979,10.6251,2.3697,8.49983Q2.3697,6.37465,3.87227,4.8718900000000005Q5.37484,3.36913,7.49993,3.36905Q8.74933,3.3682499999999997,9.85861,3.94312Q10.83608,4.4497,11.5111,5.30287L10.74975,5.30287Q10.43515,5.30287,10.21269,5.52533Q9.99023,5.74779,9.99023,6.0624Q9.99023,6.377,10.21269,6.59946Q10.43514,6.82192,10.74975,6.82192L13.3901,6.82192Q13.7047,6.82192,13.9272,6.59946ZM13.8497,3.62251L13.8497,6.0624Q13.8497,6.25274,13.7151,6.38733Q13.5805,6.52192,13.3901,6.52192L10.74975,6.52192Q10.55941,6.52192,10.42482,6.38733Q10.29023,6.25274,10.29023,6.0624Q10.29023,5.872059999999999,10.42482,5.73747Q10.55941,5.602869999999999,10.74975,5.602869999999999L12.0994,5.602869999999999L11.9328,5.36647Q11.1709,4.28534,9.99665,3.6767700000000003Q8.82236,3.0682,7.49974,3.06905Q5.25056,3.06914,3.66013,4.65977Q2.06969,6.2504,2.0697,8.49983Q2.0697900000000002,10.74937,3.66041,12.34Q5.25105,13.9306,7.50049,13.9306Q9.75002,13.9305,11.3406,12.3399Q12.9313,10.74928,12.9313,8.49983L12.9313,8.49738L12.9312,8.49493Q12.9248,8.300270000000001,13.0603,8.16033Q13.1957,8.02038,13.3905,8.02038Q13.5852,8.02038,13.7207,8.16033Q13.8561,8.300270000000001,13.8497,8.49493L13.8497,8.49738L13.8497,8.49983Q13.8497,11.12965,11.9897,12.9897Q10.12965,14.8497,7.49983,14.8497Q4.87002,14.8497,3.01001,12.9897Q1.150002,11.12965,1.15,8.49983Q1.15,5.87002,3.01001,4.010009999999999Q4.87002,2.150002,7.49983,2.150001Q9.03613,2.149278,10.40185,2.852647Q11.7676,3.55602,12.6591,4.80706L12.9313,5.188969999999999L12.9313,3.62251L12.9312,3.62005Q12.9248,3.4254,13.0603,3.28545Q13.1957,3.1455,13.3905,3.1455Q13.5852,3.1455,13.7207,3.28545Q13.8561,3.4254,13.8497,3.62005L13.8497,3.62251Z"
                        fillRule="evenodd"
                        fill="#14C9BB"
                        fillOpacity="1"
                    />
                </g>
            </g>
        </g>
    </svg>
);

export const refreshDisabledIcon = () => (
    <svg xmlns="http://www.w3.org/2000/svg" fill="none" version="1.1" width="16" height="16" viewBox="0 0 16 16">
        <g>
            <g />
            <g>
                <g>
                    <path
                        d="M13.3901,6.67192L10.74975,6.67192C10.41312,6.67192,10.14023,6.39903,10.14023,6.0624C10.14023,5.72577,10.41312,5.45287,10.74975,5.45287L11.8102,5.45287C10.82248,4.051270000000001,9.21451,3.21795,7.49983,3.21905C4.58366,3.21917,2.2197,5.58342,2.2197,8.49983C2.21982,11.41637,4.5840700000000005,13.7806,7.50048,13.7806C10.41702,13.7805,12.7813,11.41625,12.7813,8.49983C12.77,8.15558,13.046,7.87038,13.3905,7.87038C13.7349,7.87038,14.0109,8.15558,13.9997,8.49983C13.9997,12.0891,11.0891,14.9997,7.49983,14.9997C3.91057,14.9997,1,12.0891,1,8.49983C1,4.91057,3.91057,2.000000746834,7.49983,2.000000746834C9.59664,1.999013581,11.5644,3.01243,12.7813,4.72001L12.7813,3.6249599999999997C12.77,3.2807,13.046,2.995502,13.3905,2.995502C13.7349,2.995502,14.0109,3.2807,13.9997,3.6249599999999997L13.9997,6.0624C13.9997,6.39903,13.7268,6.67192,13.3901,6.67192Z"
                        fill="#B3BBC8"
                        fillOpacity="1"
                    />
                    <path
                        d="M13.9272,6.59946Q14.1497,6.377,14.1497,6.0624L14.1497,3.62731Q14.1592,3.30721,13.9362,3.07682Q13.7124,2.8455019999999998,13.3905,2.8455019999999998Q13.0686,2.8455019999999998,12.8447,3.07682Q12.6217,3.30721,12.6313,3.62731L12.6313,4.27613Q11.7687,3.21915,10.53921,2.58594Q9.10877,1.849243,7.49983,1.850001Q4.74575,1.850002,2.79788,3.79788Q0.85,5.74575,0.85,8.49983Q0.85,11.25392,2.79788,13.2018Q4.745760000000001,15.1497,7.49983,15.1497Q10.25391,15.1497,12.2018,13.2018Q14.1488,11.25475,14.1497,8.502189999999999Q14.1592,8.182089999999999,13.9362,7.95169Q13.7124,7.72038,13.3905,7.72038Q13.0686,7.72038,12.8447,7.95169Q12.6217,8.18207,12.6313,8.502130000000001Q12.6305,10.62583,11.1285,12.1278Q9.62575,13.6305,7.50049,13.6306Q5.37531,13.6306,3.87255,12.1279Q2.36979,10.6251,2.3697,8.49983Q2.3697,6.37465,3.87227,4.8718900000000005Q5.37484,3.36913,7.49993,3.36905Q8.74933,3.3682499999999997,9.85861,3.94312Q10.83608,4.4497,11.5111,5.30287L10.74975,5.30287Q10.43515,5.30287,10.21269,5.52533Q9.99023,5.74779,9.99023,6.0624Q9.99023,6.377,10.21269,6.59946Q10.43514,6.82192,10.74975,6.82192L13.3901,6.82192Q13.7047,6.82192,13.9272,6.59946ZM13.8497,3.62251L13.8497,6.0624Q13.8497,6.25274,13.7151,6.38733Q13.5805,6.52192,13.3901,6.52192L10.74975,6.52192Q10.55941,6.52192,10.42482,6.38733Q10.29023,6.25274,10.29023,6.0624Q10.29023,5.872059999999999,10.42482,5.73747Q10.55941,5.602869999999999,10.74975,5.602869999999999L12.0994,5.602869999999999L11.9328,5.36647Q11.1709,4.28534,9.99665,3.6767700000000003Q8.82236,3.0682,7.49974,3.06905Q5.25056,3.06914,3.66013,4.65977Q2.06969,6.2504,2.0697,8.49983Q2.0697900000000002,10.74937,3.66041,12.34Q5.25105,13.9306,7.50049,13.9306Q9.75002,13.9305,11.3406,12.3399Q12.9313,10.74928,12.9313,8.49983L12.9313,8.49738L12.9312,8.49493Q12.9248,8.300270000000001,13.0603,8.16033Q13.1957,8.02038,13.3905,8.02038Q13.5852,8.02038,13.7207,8.16033Q13.8561,8.300270000000001,13.8497,8.49493L13.8497,8.49738L13.8497,8.49983Q13.8497,11.12965,11.9897,12.9897Q10.12965,14.8497,7.49983,14.8497Q4.87002,14.8497,3.01001,12.9897Q1.150002,11.12965,1.15,8.49983Q1.15,5.87002,3.01001,4.010009999999999Q4.87002,2.150002,7.49983,2.150001Q9.03613,2.149278,10.40185,2.852647Q11.7676,3.55602,12.6591,4.80706L12.9313,5.188969999999999L12.9313,3.62251L12.9312,3.62005Q12.9248,3.4254,13.0603,3.28545Q13.1957,3.1455,13.3905,3.1455Q13.5852,3.1455,13.7207,3.28545Q13.8561,3.4254,13.8497,3.62005L13.8497,3.62251Z"
                        fillRule="evenodd"
                        fill="#B3BBC8"
                        fillOpacity="1"
                    />
                </g>
            </g>
        </g>
    </svg>
);

export const exportEnabledIcon = () => (
    <svg xmlns="http://www.w3.org/2000/svg" fill="none" version="1.1" width="16" height="16" viewBox="0 0 16 16">
        <g>
            <g />
            <g>
                <path
                    d="M15,9.19162L15,13.3906C15,13.7272,14.7272,14,14.3906,14L2.609375,14C2.2728260000000002,14,2,13.7272,2,13.3906L2,9.19162C2,8.855080000000001,2.2728260000000002,8.58225,2.609375,8.58225C2.9459239999999998,8.58225,3.21875,8.855080000000001,3.21875,9.19162L3.21875,12.7812L13.7812,12.7812L13.7812,9.19162C13.7812,8.855080000000001,14.0541,8.58225,14.3906,8.58225C14.7272,8.58225,15,8.855080000000001,15,9.19162ZM5.13462,5.84656L7.90037,3.08L7.90037,10.61431C7.90037,10.95086,8.1732,11.2237,8.50975,11.2237C8.8463,11.2237,9.119119999999999,10.95086,9.119119999999999,10.61431L9.119119999999999,3.08081L11.88487,5.84737C12.1229,6.08563,12.509,6.08572,12.7471,5.84759C12.9853,5.60945,12.9852,5.22333,12.7469,4.98531L8.941189999999999,1.17875C8.70337,0.9401402,8.31695,0.9401402,8.07913,1.17875L4.27337,4.984500000000001C4.04232,5.2235,4.045450000000001,5.60357,4.280390000000001,5.83874C4.51534,6.07391,4.89541,6.07739,5.13462,5.84656Z"
                    fill="#14C9BB"
                    fillOpacity="1"
                />
                <path
                    d="M3.95214,5.41429Q3.9546900000000003,5.72496,4.1742799999999995,5.94476Q4.39386,6.16455,4.70454,6.16739Q5.01521,6.17024,5.23878,5.9545L7.75037,3.4422L7.75037,10.61431Q7.75037,10.92886,7.97279,11.1513Q8.19521,11.3737,8.50975,11.3737Q8.824290000000001,11.3737,9.046710000000001,11.1513Q9.26913,10.92886,9.269120000000001,10.61431L9.269120000000001,3.44302L11.77879,5.95343Q12.0012,6.17605,12.3159,6.17612Q12.6307,6.1762,12.8532,5.95365Q13.0758,5.7311,13.0757,5.41637Q13.0756,5.10164,12.853,4.8791899999999995L9.047270000000001,1.0726953Q8.825099999999999,0.849793,8.510159999999999,0.849793Q8.19521,0.849793,7.97288,1.0728598L4.1673100000000005,4.87843Q3.9495899999999997,5.10361,3.95214,5.41429ZM4.70728,5.86741Q4.51934,5.86568,4.3865099999999995,5.73272Q4.25367,5.59976,4.25213,5.41182Q4.25059,5.22388,4.379440000000001,5.09057L8.185369999999999,1.28464Q8.31977,1.149793,8.510159999999999,1.149793Q8.70055,1.149793,8.83511,1.284805L12.6409,5.09143Q12.7756,5.22602,12.7757,5.41644Q12.7757,5.60687,12.6411,5.74152Q12.5064,5.87617,12.316,5.87612Q12.1256,5.87608,11.99096,5.74132L8.96912,2.71861L8.96912,10.61431Q8.96913,10.80459,8.834579999999999,10.93914Q8.70003,11.0737,8.50975,11.0737Q8.319469999999999,11.0737,8.18492,10.93914Q8.050370000000001,10.80459,8.050370000000001,10.61431L8.050370000000001,2.71779L5.03047,5.73862Q4.89522,5.86913,4.70728,5.86741ZM14.9276,8.65467Q14.7052,8.43225,14.3906,8.43225Q14.0761,8.43225,13.8537,8.65467Q13.6312,8.87708,13.6313,9.19162L13.6313,12.6313L3.36875,12.6313L3.36875,9.19162Q3.36875,8.87708,3.14633,8.65467Q2.923918,8.43225,2.609375,8.43225Q2.294832,8.43225,2.0724157,8.65467Q1.85,8.87708,1.85,9.19162L1.85,13.3906Q1.85,13.7052,2.0724158,13.9276Q2.294832,14.15,2.609375,14.15L14.3906,14.15Q14.7052,14.15,14.9276,13.9276Q15.15,13.7052,15.15,13.3906L15.15,9.19162Q15.15,8.87708,14.9276,8.65467ZM14.0658,8.8668Q14.2003,8.73225,14.3906,8.73225Q14.5809,8.73225,14.7155,8.8668Q14.85,9.00135,14.85,9.19162L14.85,13.3906Q14.85,13.5809,14.7155,13.7155Q14.5809,13.85,14.3906,13.85L2.609375,13.85Q2.419096,13.85,2.284548,13.7155Q2.15,13.5809,2.15,13.3906L2.15,9.19162Q2.15,9.00135,2.284548,8.8668Q2.419096,8.73225,2.609375,8.73225Q2.799655,8.73225,2.934202,8.8668Q3.06875,9.00135,3.06875,9.19162L3.06875,12.9312L13.9312,12.9312L13.9312,9.19162Q13.9312,9.00135,14.0658,8.8668Z"
                    fillRule="evenodd"
                    fill="#14C9BB"
                    fillOpacity="1"
                />
            </g>
        </g>
    </svg>
);

export const exportDisabledIcon = () => (
    <svg xmlns="http://www.w3.org/2000/svg" fill="none" version="1.1" width="16" height="16" viewBox="0 0 16 16">
        <g>
            <g />
            <g>
                <path
                    d="M15,9.19162L15,13.3906C15,13.7272,14.7272,14,14.3906,14L2.609375,14C2.2728260000000002,14,2,13.7272,2,13.3906L2,9.19162C2,8.855080000000001,2.2728260000000002,8.58225,2.609375,8.58225C2.9459239999999998,8.58225,3.21875,8.855080000000001,3.21875,9.19162L3.21875,12.7812L13.7812,12.7812L13.7812,9.19162C13.7812,8.855080000000001,14.0541,8.58225,14.3906,8.58225C14.7272,8.58225,15,8.855080000000001,15,9.19162ZM5.13462,5.84656L7.90037,3.08L7.90037,10.61431C7.90037,10.95086,8.1732,11.2237,8.50975,11.2237C8.8463,11.2237,9.119119999999999,10.95086,9.119119999999999,10.61431L9.119119999999999,3.08081L11.88487,5.84737C12.1229,6.08563,12.509,6.08572,12.7471,5.84759C12.9853,5.60945,12.9852,5.22333,12.7469,4.98531L8.941189999999999,1.17875C8.70337,0.9401402,8.31695,0.9401402,8.07913,1.17875L4.27337,4.984500000000001C4.04232,5.2235,4.045450000000001,5.60357,4.280390000000001,5.83874C4.51534,6.07391,4.89541,6.07739,5.13462,5.84656Z"
                    fill="#B3BBC8"
                    fillOpacity="1"
                />
                <path
                    d="M3.95214,5.41429Q3.9546900000000003,5.72496,4.1742799999999995,5.94476Q4.39386,6.16455,4.70454,6.16739Q5.01521,6.17024,5.23878,5.9545L7.75037,3.4422L7.75037,10.61431Q7.75037,10.92886,7.97279,11.1513Q8.19521,11.3737,8.50975,11.3737Q8.824290000000001,11.3737,9.046710000000001,11.1513Q9.26913,10.92886,9.269120000000001,10.61431L9.269120000000001,3.44302L11.77879,5.95343Q12.0012,6.17605,12.3159,6.17612Q12.6307,6.1762,12.8532,5.95365Q13.0758,5.7311,13.0757,5.41637Q13.0756,5.10164,12.853,4.8791899999999995L9.047270000000001,1.0726953Q8.825099999999999,0.849793,8.510159999999999,0.849793Q8.19521,0.849793,7.97288,1.0728598L4.1673100000000005,4.87843Q3.9495899999999997,5.10361,3.95214,5.41429ZM4.70728,5.86741Q4.51934,5.86568,4.3865099999999995,5.73272Q4.25367,5.59976,4.25213,5.41182Q4.25059,5.22388,4.379440000000001,5.09057L8.185369999999999,1.28464Q8.31977,1.149793,8.510159999999999,1.149793Q8.70055,1.149793,8.83511,1.284805L12.6409,5.09143Q12.7756,5.22602,12.7757,5.41644Q12.7757,5.60687,12.6411,5.74152Q12.5064,5.87617,12.316,5.87612Q12.1256,5.87608,11.99096,5.74132L8.96912,2.71861L8.96912,10.61431Q8.96913,10.80459,8.834579999999999,10.93914Q8.70003,11.0737,8.50975,11.0737Q8.319469999999999,11.0737,8.18492,10.93914Q8.050370000000001,10.80459,8.050370000000001,10.61431L8.050370000000001,2.71779L5.03047,5.73862Q4.89522,5.86913,4.70728,5.86741ZM14.9276,8.65467Q14.7052,8.43225,14.3906,8.43225Q14.0761,8.43225,13.8537,8.65467Q13.6312,8.87708,13.6313,9.19162L13.6313,12.6313L3.36875,12.6313L3.36875,9.19162Q3.36875,8.87708,3.14633,8.65467Q2.923918,8.43225,2.609375,8.43225Q2.294832,8.43225,2.0724157,8.65467Q1.85,8.87708,1.85,9.19162L1.85,13.3906Q1.85,13.7052,2.0724158,13.9276Q2.294832,14.15,2.609375,14.15L14.3906,14.15Q14.7052,14.15,14.9276,13.9276Q15.15,13.7052,15.15,13.3906L15.15,9.19162Q15.15,8.87708,14.9276,8.65467ZM14.0658,8.8668Q14.2003,8.73225,14.3906,8.73225Q14.5809,8.73225,14.7155,8.8668Q14.85,9.00135,14.85,9.19162L14.85,13.3906Q14.85,13.5809,14.7155,13.7155Q14.5809,13.85,14.3906,13.85L2.609375,13.85Q2.419096,13.85,2.284548,13.7155Q2.15,13.5809,2.15,13.3906L2.15,9.19162Q2.15,9.00135,2.284548,8.8668Q2.419096,8.73225,2.609375,8.73225Q2.799655,8.73225,2.934202,8.8668Q3.06875,9.00135,3.06875,9.19162L3.06875,12.9312L13.9312,12.9312L13.9312,9.19162Q13.9312,9.00135,14.0658,8.8668Z"
                    fillRule="evenodd"
                    fill="#B3BBC8"
                    fillOpacity="1"
                />
            </g>
        </g>
    </svg>
);

export const activateEnabledIcon = () => (
    <svg xmlns="http://www.w3.org/2000/svg" fill="none" version="1.1" width="16" height="16" viewBox="0 0 16 16">
        <g>
            <g />
            <g>
                <g>
                    <path
                        d="M8,1C4.134650000000001,1,1,4.134650000000001,1,8C1,11.8653,4.134650000000001,15,8,15C11.8653,15,15,11.8653,15,8C15,4.134650000000001,11.8653,1,8,1ZM12.0134,12.0134C11.4914,12.5353,10.88538,12.945,10.20921,13.2301C9.50968,13.5262,8.768080000000001,13.6757,8,13.6757C7.23192,13.6757,6.49032,13.5262,5.79079,13.2301C5.11462,12.945,4.508570000000001,12.5353,3.98665,12.0134C3.46472,11.4914,3.05497,10.88538,2.76986,10.20921C2.47385,9.50968,2.32428,8.768080000000001,2.32428,8C2.32428,7.23192,2.47385,6.49032,2.76986,5.79079C3.05497,5.11462,3.46472,4.508570000000001,3.98665,3.98665C4.508570000000001,3.46472,5.11462,3.05497,5.79079,2.76986C6.49032,2.47385,7.23192,2.32428,8,2.32428C8.768080000000001,2.32428,9.50968,2.47385,10.20921,2.76986C10.88538,3.05497,11.4914,3.46472,12.0134,3.98665C12.5353,4.508570000000001,12.945,5.11462,13.2301,5.79079C13.5262,6.49032,13.6757,7.23192,13.6757,8C13.6757,8.768080000000001,13.5262,9.50968,13.2301,10.20921C12.9435,10.88382,12.5337,11.4914,12.0134,12.0134Z"
                        fill="#14C9BB"
                        fillOpacity="1"
                    />
                </g>
                <g>
                    <path
                        d="M10.416430660171509,5.669224992584229L7.221010660171508,8.864640992584228L5.5835706601715085,7.227200992584228C5.324949660171509,6.968580992584228,4.905853660171509,6.968580992584228,4.647228660171509,7.227200992584228C4.388604560171509,7.485830992584228,4.388604560171509,7.904920992584229,4.647228660171509,8.163550992584229L6.753620660171508,10.269940992584228C6.878250660171509,10.394580992584228,7.046520660171509,10.46313099258423,7.222570660171509,10.46313099258423C7.398620660171509,10.46313099258423,7.566880660171509,10.39302099258423,7.691520660171509,10.269940992584228L11.35433066017151,6.607130992584229C11.61295066017151,6.348503992584228,11.61295066017151,5.9294079925842285,11.35433066017151,5.670782992584228C11.094150660171508,5.410600692584229,10.67505066017151,5.410600692584229,10.416430660171509,5.669224992584229Z"
                        fill="#14C9BB"
                        fillOpacity="1"
                    />
                </g>
            </g>
        </g>
    </svg>
);

export const activateEnabledDisabledIcon = () => (
    <svg xmlns="http://www.w3.org/2000/svg" fill="none" version="1.1" width="16" height="16" viewBox="0 0 16 16">
        <g>
            <g />
            <g>
                <g>
                    <path
                        d="M8,1C4.134650000000001,1,1,4.134650000000001,1,8C1,11.8653,4.134650000000001,15,8,15C11.8653,15,15,11.8653,15,8C15,4.134650000000001,11.8653,1,8,1ZM12.0134,12.0134C11.4914,12.5353,10.88538,12.945,10.20921,13.2301C9.50968,13.5262,8.768080000000001,13.6757,8,13.6757C7.23192,13.6757,6.49032,13.5262,5.79079,13.2301C5.11462,12.945,4.508570000000001,12.5353,3.98665,12.0134C3.46472,11.4914,3.05497,10.88538,2.76986,10.20921C2.47385,9.50968,2.32428,8.768080000000001,2.32428,8C2.32428,7.23192,2.47385,6.49032,2.76986,5.79079C3.05497,5.11462,3.46472,4.508570000000001,3.98665,3.98665C4.508570000000001,3.46472,5.11462,3.05497,5.79079,2.76986C6.49032,2.47385,7.23192,2.32428,8,2.32428C8.768080000000001,2.32428,9.50968,2.47385,10.20921,2.76986C10.88538,3.05497,11.4914,3.46472,12.0134,3.98665C12.5353,4.508570000000001,12.945,5.11462,13.2301,5.79079C13.5262,6.49032,13.6757,7.23192,13.6757,8C13.6757,8.768080000000001,13.5262,9.50968,13.2301,10.20921C12.9435,10.88382,12.5337,11.4914,12.0134,12.0134Z"
                        fill="#B3BBC8"
                        fillOpacity="1"
                    />
                </g>
                <g>
                    <path
                        d="M10.416430660171509,5.669224992584229L7.221010660171508,8.864640992584228L5.5835706601715085,7.227200992584228C5.324949660171509,6.968580992584228,4.905853660171509,6.968580992584228,4.647228660171509,7.227200992584228C4.388604560171509,7.485830992584228,4.388604560171509,7.904920992584229,4.647228660171509,8.163550992584229L6.753620660171508,10.269940992584228C6.878250660171509,10.394580992584228,7.046520660171509,10.46313099258423,7.222570660171509,10.46313099258423C7.398620660171509,10.46313099258423,7.566880660171509,10.39302099258423,7.691520660171509,10.269940992584228L11.35433066017151,6.607130992584229C11.61295066017151,6.348503992584228,11.61295066017151,5.9294079925842285,11.35433066017151,5.670782992584228C11.094150660171508,5.410600692584229,10.67505066017151,5.410600692584229,10.416430660171509,5.669224992584229Z"
                        fill="#B3BBC8"
                        fillOpacity="1"
                    />
                </g>
            </g>
        </g>
    </svg>
);

export const upgradeDisabledIcon = () => (
    <svg xmlns="http://www.w3.org/2000/svg" fill="none" version="1.1" width="16" height="16" viewBox="0 0 16 16">
        <g>
            <g />
            <g>
                <g>
                    <path
                        d="M13.8549,7.68704C13.8189,7.48893,13.9702,7.3062,14.1716,7.30465C14.3586,7.30457,14.5064,7.46309,14.4933,7.64964C14.6026,10.05928,13.3593,12.3288,11.26977,13.5338C8.18548,15.3149,4.24161,14.258,2.461201,11.17371C2.43193,11.12294,2.403339,11.07178,2.375437,11.02024L2.1129860000000003,12.1004C2.0741709999999998,12.2682,1.901186,12.3681,1.736397,12.3177C1.568452,12.2666,1.4689077,12.094,1.50876725,11.923L1.954354,10.08586C1.99317,9.91796,2.166155,9.81814,2.330943,9.86855L4.14489,10.40119C4.3119,10.4502,4.4137900000000005,10.62688,4.37252,10.79583C4.3337,10.96373,4.1607199999999995,11.06355,3.99593,11.01315L2.93645,10.70233L3.01964,10.85129C4.6223,13.627,8.1716,14.5779,10.94735,12.9754C12.8279,11.894,13.9491,9.85491,13.8549,7.68769L13.8549,7.68704ZM2.241955,7.6303C2.239645,7.81892,2.076603,7.96528,1.888821,7.9473C1.701039,7.92932,1.5687376,7.75467,1.60227,7.56904C1.737687,5.49071,2.8822799999999997,3.4846,4.82133,2.365148C6.54071,1.370553,8.622959999999999,1.2253910000000001,10.46371,1.971793C11.6768,2.460157,12.7118,3.30746,13.43,4.40027L13.6944,3.3079099999999997C13.7332,3.1396699999999997,13.9066,3.03974,14.0716,3.0906000000000002C14.238,3.1395999999999997,14.3405,3.31629,14.2992,3.48524L13.8537,5.3224C13.8148,5.4902999999999995,13.6419,5.59012,13.4771,5.53971L11.6631,5.007070000000001C11.49492,4.95624,11.39505,4.783580000000001,11.43485,4.61243C11.47366,4.44453,11.6466,4.34471,11.8114,4.39512L12.8554,4.701420000000001C12.2109,3.74333,11.29373,3.00069,10.22254,2.56956C8.56573,1.897224,6.69126,2.027885,5.14375,2.9235800000000003C3.40267,3.92825,2.367699,5.76476,2.241955,7.6303Z"
                        fill="#B3BBC8"
                        fillOpacity="1"
                    />
                    <path
                        d="M3.51554,5.21188Q4.22656,4.0301100000000005,5.39365,3.35665L5.39422,3.35632Q6.45465,2.74255,7.67693,2.65735Q8.8992,2.5721499999999997,10.03586,3.0334000000000003Q10.85547,3.36328,11.49687,3.91326Q11.4102,3.94025,11.326,3.98884Q11.02577,4.16209,10.94784,4.4991699999999994Q10.87322,4.82005,11.03817,5.10522Q11.20312,5.39039,11.5223,5.48682L13.3308,6.01785Q13.6623,6.11923,13.9625,5.94599Q14.2627,5.77274,14.3396,5.44025L14.785,3.60389Q14.8635,3.2823,14.6962,2.99277Q14.5295,2.70423,14.2189,2.61278Q13.887,2.51049,13.5861,2.68384Q13.2852,2.85719,13.2084,3.19029L13.1893,3.26928Q12.1477,2.110736,10.6516,1.50843647Q9.1638,0.905155,7.56223,1.016807Q5.96067,1.1284589999999999,4.57134,1.932129Q3.03253,2.82052,2.107681,4.34862Q1.221118,5.81348,1.1049120000000001,7.51279Q1.056139,7.84857,1.263999,8.122959999999999Q1.4819159,8.41062,1.841157,8.44503Q2.200397,8.47943,2.468947,8.23835Q2.73133,8.00281,2.74158,7.65292Q2.83209,6.34783,3.51554,5.21188ZM11.6704,4.87479L11.6652,4.8732500000000005Q11.6678,4.87405,11.6704,4.87479ZM13.3565,7.73595Q13.3076,7.38292,13.5391,7.10326Q13.7839,6.80763,14.1677,6.80467Q14.5291,6.80449,14.7731,7.06612Q15.0056,7.31538,14.9939,7.6511Q15.0754,9.5848,14.1446,11.2837Q13.2081,12.9932,11.5196,13.967Q9.02725,15.4062,6.24712,14.6612Q3.96584,14.05,2.58766,12.2619Q2.5021199999999997,12.5622,2.221837,12.7239Q1.921612,12.8972,1.5901462,12.7958Q1.275981,12.7002,1.111539,12.4151Q0.947097,12.13,1.021827,11.8095L1.4684421,9.96801Q1.5452792,9.63552,1.845505,9.46227Q2.145731,9.28902,2.477199,9.390419999999999L4.28576,9.92144Q4.60317,10.01459,4.76994,10.30373Q4.93677,10.59299,4.85824,10.91448Q4.78159,11.24618,4.48137,11.41942Q4.304399999999999,11.5215,4.11658,11.5282Q5.1195699999999995,12.656,6.67299,13.0722Q8.7949,13.6408,10.69735,12.5424Q11.9871,11.8007,12.7035,10.49782Q13.4136,9.20655,13.3565,7.73595ZM4.1369299999999996,10.53347L4.14218,10.53502Q4.1395599999999995,10.53421,4.1369299999999996,10.53347Z"
                        fillRule="evenodd"
                        fill="#B3BBC8"
                        fillOpacity="1"
                    />
                </g>
            </g>
        </g>
    </svg>
);

export const upgradeEnabledIcon = () => (
    <svg xmlns="http://www.w3.org/2000/svg" fill="none" version="1.1" width="16" height="16" viewBox="0 0 16 16">
        <g>
            <g />
            <g>
                <g>
                    <path
                        d="M13.8549,7.68704C13.8189,7.48893,13.9702,7.3062,14.1716,7.30465C14.3586,7.30457,14.5064,7.46309,14.4933,7.64964C14.6026,10.05928,13.3593,12.3288,11.26977,13.5338C8.18548,15.3149,4.24161,14.258,2.461201,11.17371C2.43193,11.12294,2.403339,11.07178,2.375437,11.02024L2.1129860000000003,12.1004C2.0741709999999998,12.2682,1.901186,12.3681,1.736397,12.3177C1.568452,12.2666,1.4689077,12.094,1.50876725,11.923L1.954354,10.08586C1.99317,9.91796,2.166155,9.81814,2.330943,9.86855L4.14489,10.40119C4.3119,10.4502,4.4137900000000005,10.62688,4.37252,10.79583C4.3337,10.96373,4.1607199999999995,11.06355,3.99593,11.01315L2.93645,10.70233L3.01964,10.85129C4.6223,13.627,8.1716,14.5779,10.94735,12.9754C12.8279,11.894,13.9491,9.85491,13.8549,7.68769L13.8549,7.68704ZM2.241955,7.6303C2.239645,7.81892,2.076603,7.96528,1.888821,7.9473C1.701039,7.92932,1.5687376,7.75467,1.60227,7.56904C1.737687,5.49071,2.8822799999999997,3.4846,4.82133,2.365148C6.54071,1.370553,8.622959999999999,1.2253910000000001,10.46371,1.971793C11.6768,2.460157,12.7118,3.30746,13.43,4.40027L13.6944,3.3079099999999997C13.7332,3.1396699999999997,13.9066,3.03974,14.0716,3.0906000000000002C14.238,3.1395999999999997,14.3405,3.31629,14.2992,3.48524L13.8537,5.3224C13.8148,5.4902999999999995,13.6419,5.59012,13.4771,5.53971L11.6631,5.007070000000001C11.49492,4.95624,11.39505,4.783580000000001,11.43485,4.61243C11.47366,4.44453,11.6466,4.34471,11.8114,4.39512L12.8554,4.701420000000001C12.2109,3.74333,11.29373,3.00069,10.22254,2.56956C8.56573,1.897224,6.69126,2.027885,5.14375,2.9235800000000003C3.40267,3.92825,2.367699,5.76476,2.241955,7.6303Z"
                        fill="#007D71"
                        fillOpacity="1"
                    />
                    <path
                        d="M3.51554,5.21188Q4.22656,4.0301100000000005,5.39365,3.35665L5.39422,3.35632Q6.45465,2.74255,7.67693,2.65735Q8.8992,2.5721499999999997,10.03586,3.0334000000000003Q10.85547,3.36328,11.49687,3.91326Q11.4102,3.94025,11.326,3.98884Q11.02577,4.16209,10.94784,4.4991699999999994Q10.87322,4.82005,11.03817,5.10522Q11.20312,5.39039,11.5223,5.48682L13.3308,6.01785Q13.6623,6.11923,13.9625,5.94599Q14.2627,5.77274,14.3396,5.44025L14.785,3.60389Q14.8635,3.2823,14.6962,2.99277Q14.5295,2.70423,14.2189,2.61278Q13.887,2.51049,13.5861,2.68384Q13.2852,2.85719,13.2084,3.19029L13.1893,3.26928Q12.1477,2.110736,10.6516,1.50843647Q9.1638,0.905155,7.56223,1.016807Q5.96067,1.1284589999999999,4.57134,1.932129Q3.03253,2.82052,2.107681,4.34862Q1.221118,5.81348,1.1049120000000001,7.51279Q1.056139,7.84857,1.263999,8.122959999999999Q1.4819159,8.41062,1.841157,8.44503Q2.200397,8.47943,2.468947,8.23835Q2.73133,8.00281,2.74158,7.65292Q2.83209,6.34783,3.51554,5.21188ZM11.6704,4.87479L11.6652,4.8732500000000005Q11.6678,4.87405,11.6704,4.87479ZM13.3565,7.73595Q13.3076,7.38292,13.5391,7.10326Q13.7839,6.80763,14.1677,6.80467Q14.5291,6.80449,14.7731,7.06612Q15.0056,7.31538,14.9939,7.6511Q15.0754,9.5848,14.1446,11.2837Q13.2081,12.9932,11.5196,13.967Q9.02725,15.4062,6.24712,14.6612Q3.96584,14.05,2.58766,12.2619Q2.5021199999999997,12.5622,2.221837,12.7239Q1.921612,12.8972,1.5901462,12.7958Q1.275981,12.7002,1.111539,12.4151Q0.947097,12.13,1.021827,11.8095L1.4684421,9.96801Q1.5452792,9.63552,1.845505,9.46227Q2.145731,9.28902,2.477199,9.390419999999999L4.28576,9.92144Q4.60317,10.01459,4.76994,10.30373Q4.93677,10.59299,4.85824,10.91448Q4.78159,11.24618,4.48137,11.41942Q4.304399999999999,11.5215,4.11658,11.5282Q5.1195699999999995,12.656,6.67299,13.0722Q8.7949,13.6408,10.69735,12.5424Q11.9871,11.8007,12.7035,10.49782Q13.4136,9.20655,13.3565,7.73595ZM4.1369299999999996,10.53347L4.14218,10.53502Q4.1395599999999995,10.53421,4.1369299999999996,10.53347Z"
                        fillRule="evenodd"
                        fill="#14C9BB"
                        fillOpacity="1"
                    />
                </g>
            </g>
        </g>
    </svg>
);

export const searchNoDataIcon = () => (
    <svg xmlns="http://www.w3.org/2000/svg" fill="none" version="1.1" width="96" height="96" viewBox="0 0 96 96">
        <defs>
            <pattern
                x="0"
                y="7"
                width="95"
                height="82.2680435180664"
                patternUnits="userSpaceOnUse"
                id="master_svg0_20_23599"
            >
                <image
                    x="-0.0000013169788104505642"
                    y="0"
                    width="95.00000263395762"
                    height="82.2680435180664"
                    xlinkHref="data:image/png;base64,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"
                />
            </pattern>
        </defs>
        <g>
            <g />
            <g>
                <rect
                    x="0"
                    y="7"
                    width="95"
                    height="82.2680435180664"
                    rx="0"
                    fill="url(#master_svg0_20_23599)"
                    fillOpacity="1"
                />
            </g>
        </g>
    </svg>
);

export const searchIcon = () => (
    <svg xmlns="http://www.w3.org/2000/svg" fill="none" version="1.1" width="16" height="16" viewBox="0 0 16 16">
        <g>
            <g />
            <g>
                <g>
                    <path
                        d="M11.4189,10.6022C11.4483,10.62277,11.4771,10.64568,11.503,10.67271L14.831,14.0002C15.0549,14.224,15.0584,14.5995,14.8292,14.8286C14.6013,15.0591,14.2294,15.0602,14.0002,14.831L10.67212,11.503C10.64638,11.4772,10.62317,11.4491,10.60279,11.4189C9.55296,12.2826,8.23519,12.7538,6.87578,12.7516C3.63059,12.7516,1,10.12097,1,6.87578C1,3.63059,3.63059,1,6.87578,1C10.12097,1,12.7516,3.63059,12.7516,6.87578C12.7516,8.29008,12.2521,9.58804,11.4189,10.6022ZM6.87578,11.5764C9.4725,11.5773,11.578,9.4725,11.578,6.87578C11.578,4.279059999999999,9.4725,2.1742600000000003,6.87578,2.17516C4.28033,2.17605,2.17678,4.28033,2.17678,6.87578C2.17678,9.47123,4.28033,11.5755,6.87578,11.5764Z"
                        fill="#B8BFBF"
                        fillOpacity="1"
                    />
                    <path
                        d="M11.5593,10.58767Q12.8515,8.965499999999999,12.8516,6.87578Q12.8516,4.40045,11.1013,2.65023Q9.35109,0.9000001,6.87578,0.9000005Q4.40046,0.9000015,2.65023,2.65023Q0.9,4.40047,0.9000005,6.87578Q0.9000015,9.35109,2.65023,11.1013Q4.40045,12.8516,6.87562,12.8516Q8.96389,12.8549,10.58786,11.5597Q10.59453,11.5668,10.60141,11.5737L13.9295,14.9017Q14.1308,15.103,14.4155,15.1022Q14.7002,15.1014,14.8999,14.8994Q15.1004,14.6989,15.1,14.4123Q15.0996,14.1273,14.9017,13.9294L11.5744,10.60273Q11.5671,10.59517,11.5593,10.58767ZM10.95991,2.7916499999999997Q12.6516,4.4833,12.6516,6.87578Q12.6516,8.94431,11.3417,10.53872L11.2731,10.62218L11.3616,10.68412Q11.4037,10.71358,11.4307,10.74183L11.4315,10.74264L14.7603,14.0709Q14.8997,14.2103,14.9,14.4126Q14.9003,14.6162,14.7585,14.7579Q14.6164,14.9016,14.4149,14.9022Q14.2134,14.9028,14.0709,14.7603L10.74283,11.4322Q10.71091,11.4003,10.68564,11.3629L10.62412,11.2719L10.53926,11.3417Q8.94296,12.6549,6.87594,12.6516Q4.48329,12.6516,2.7916499999999997,10.95991Q1.100002,9.26823,1.1,6.87578Q1.0999999,4.4833099999999995,2.7916499999999997,2.7916499999999997Q4.4833099999999995,1.100002,6.87578,1.1Q9.26823,1.1,10.95991,2.7916499999999997ZM3.48226,3.48181Q2.0767800000000003,4.887779999999999,2.0767800000000003,6.87578Q2.0767800000000003,8.86378,3.48226,10.26975Q4.88776,11.6757,6.87575,11.6764Q8.86472,11.6771,10.27137,10.27092Q11.678,8.86473,11.678,6.87578Q11.678,4.88682,10.27137,3.48064Q8.86472,2.07447,6.87575,2.07516Q4.8877500000000005,2.07584,3.48226,3.48181ZM3.62371,10.12835Q2.27678,8.78097,2.27678,6.87578Q2.27678,4.9706,3.62371,3.62321Q4.9706399999999995,2.27581,6.87581,2.27516Q8.78192,2.2744999999999997,10.12997,3.62209Q11.478,4.96968,11.478,6.87578Q11.478,8.781880000000001,10.12997,10.12947Q8.781939999999999,11.4771,6.87581,11.4764Q4.97065,11.4758,3.62371,10.12835Z"
                        fillRule="evenodd"
                        fill="#B8BFBF"
                        fillOpacity="1"
                    />
                </g>
            </g>
        </g>
    </svg>
);

export const deleteCommonIcon = () => (
    <svg xmlns="http://www.w3.org/2000/svg" fill="none" version="1.1" width="16" height="16" viewBox="0 0 16 16">
        <defs>
            <clipPath id="master_svg0_121_28408">
                <rect x="0" y="0" width="16" height="16" rx="0" />
            </clipPath>
        </defs>
        <g clipPath="url(#master_svg0_121_28408)">
            <g>
                <path
                    d="M9.49994,12C9.22346,12,9.00002,11.7765,9.00002,11.5L9.00002,5.99998C9.00002,5.72349,9.22346,5.49994,9.49994,5.49994C9.77643,5.49994,9.99998,5.72351,9.99998,5.99998L9.99998,11.5C9.99998,11.7765,9.77644,12,9.49994,12L9.49994,12ZM6.50001,12C6.22343,12,5.99998,11.7765,5.99998,11.5L5.99998,5.99998C5.99998,5.72349,6.22343,5.49994,6.50001,5.49994C6.77649,5.49994,6.99993,5.72351,6.99993,5.99998L6.99993,11.5C6.99993,11.7765,6.77649,12,6.50001,12L6.50001,12ZM14.4999,3.50001L11.9999,3.50001L11.9999,2.50001C11.9999,1.672981,11.3324,1.000000359596,10.51145,1.000000359596L5.49993,1.000000359596C4.67299,1.000000359596,4,1.672996,4,2.50001L4,3.50001L1.5000339999999999,3.50001C1.223458,3.50001,1,3.72345,1,4.000030000000001C1,4.27651,1.223457,4.49995,1.5000339999999999,4.49995L14.5,4.49995C14.7764,4.49995,15,4.27651,15,4.000030000000001C15,3.72345,14.7764,3.5,14.5,3.5L14.4999,3.50001ZM4.99999,2.50001C4.99999,2.2245,5.22445,1.999989,5.49993,1.999989L10.51146,1.999989C10.78553,1.999989,10.99998,2.2195,10.99998,2.50001L10.99998,3.50001L4.99999,3.50001L4.99999,2.50001ZM11.502,15L4.50094,15C3.67399,15,3.00101,14.327,3.00101,13.5L3.00101,5.99251C3.00101,5.71649,3.22501,5.4925,3.50097,5.4925C3.77701,5.4925,4.00099,5.71649,4.00099,5.99251L4.00099,13.5C4.00099,13.776,4.22547,14,4.50096,14L11.502,14C11.7779,14,12.002,13.776,12.002,13.5L12.002,6.01297C12.002,5.73701,12.2255,5.513,12.502,5.513C12.7785,5.513,13.002,5.73701,13.002,6.01297L13.002,13.5C13.002,14.327,12.3289,15,11.502,15Z"
                    fill="#14C9BB"
                    fillOpacity="1"
                />
                <path
                    d="M15.0304,3.46951Q14.8109,3.25,14.5,3.25L14.5,3.75Q14.75,3.75,14.75,4.000030000000001Q14.75,4.24995,14.5,4.24995L1.5000339999999999,4.24995Q1.25,4.24995,1.25,4.000030000000001Q1.25,3.75001,1.5000339999999999,3.75001L4.25,3.75001L4.25,2.50001Q4.25,1.983306,4.61664,1.616648Q4.983280000000001,1.25,5.49993,1.25L10.51145,1.25Q11.023,1.25,11.3861,1.61595Q11.7499,1.982661,11.7499,2.50001L11.7499,3.75001L14.4999,3.75001L14.4999,3.25001L12.2499,3.25001L12.2499,2.50001Q12.2499,1.776703,11.741,1.263788Q11.2313,0.75,10.51145,0.75L5.49993,0.75Q4.77616,0.75,4.26308,1.263101Q3.75,1.776204,3.75,2.50001L3.75,3.25001L1.5000339999999999,3.25001Q1.18905,3.25001,0.9695279,3.46952Q0.75,3.68904,0.75,4.000030000000001Q0.75,4.31096,0.9695394,4.53046Q1.189066,4.74995,1.5000339999999999,4.74995L14.5,4.74995Q14.8109,4.74995,15.0304,4.53048Q15.25,4.31096,15.25,4.000030000000001Q15.25,3.68905,15.0304,3.46951ZM4.74999,2.50001L4.74999,3.75001L11.25,3.75001L11.25,2.50001Q11.25,2.18777,11.0373,1.970102Q10.82227,1.749989,10.51146,1.749989L5.49993,1.749989Q5.18975,1.749989,4.969860000000001,1.969921Q4.74999,2.1898299999999997,4.74999,2.50001ZM5.24999,3.25001L10.74998,3.25001L10.74998,2.50001Q10.74998,2.24999,10.51146,2.24999L5.49993,2.24999Q5.24999,2.24999,5.24999,2.50001L5.24999,3.25001ZM12.7389,14.7369Q13.252,14.2238,13.252,13.5L13.252,6.01297Q13.252,5.7024,13.0325,5.48277Q12.8128,5.263,12.502,5.263Q12.1911,5.263,11.9714,5.48277Q11.752,5.7024,11.752,6.01297L11.752,13.5Q11.752,13.75,11.502,13.75L4.50096,13.75Q4.25099,13.75,4.25099,13.5L4.25099,5.99251Q4.25099,5.68194,4.03127,5.46222Q3.81155,5.2425,3.50097,5.2425Q3.19043,5.2425,2.97072,5.46223Q2.75101,5.68195,2.75101,5.99251L2.75101,13.5Q2.75101,14.2238,3.26409,14.7369Q3.77717,15.25,4.50094,15.25L11.502,15.25Q12.2258,15.25,12.7389,14.7369ZM7.03043,12.0304Q7.24993,11.8109,7.24993,11.5L7.24993,5.99998Q7.24993,5.68908,7.03046,5.46954Q6.81094,5.24994,6.50001,5.24994Q6.18903,5.24994,5.96949,5.46951Q5.74998,5.68906,5.74998,5.99998L5.74998,11.5Q5.74998,11.811,5.96951,12.0305Q6.18904,12.25,6.50002,12.25Q6.81094,12.25,7.03043,12.0304ZM10.03041,12.0305Q10.24998,11.8109,10.24998,11.5L10.24998,5.99998Q10.24998,5.68907,10.03042,5.46951Q9.81086,5.24994,9.49994,5.24994Q9.18902,5.24994,8.9695,5.46953Q8.75002,5.68907,8.75002,5.99998L8.75002,11.5Q8.75002,11.811,8.96952,12.0305Q9.18903,12.25,9.49996,12.25Q9.81087,12.25,10.03041,12.0305ZM12.752,6.01297L12.752,13.5Q12.752,14.0167,12.3853,14.3834Q12.0187,14.75,11.502,14.75L4.50094,14.75Q3.98429,14.75,3.61765,14.3834Q3.25101,14.0167,3.25101,13.5L3.25101,5.99251Q3.25101,5.7425,3.50097,5.7425Q3.75099,5.7425,3.75099,5.99251L3.75099,13.5Q3.75099,13.8105,3.97094,14.0304Q4.19074,14.25,4.50096,14.25L11.502,14.25Q11.8125,14.25,12.0322,14.0303Q12.252,13.8106,12.252,13.5L12.252,6.01297Q12.252,5.763,12.502,5.763Q12.752,5.763,12.752,6.01297ZM6.74993,5.99998L6.74993,11.5Q6.74993,11.75,6.49999,11.75Q6.24998,11.75,6.24998,11.5L6.24998,5.99998Q6.24998,5.74994,6.50001,5.74994Q6.74993,5.74994,6.74993,5.99998ZM9.74998,5.99998L9.74998,11.5Q9.74998,11.75,9.49993,11.75Q9.25002,11.75,9.25002,11.5L9.25002,5.99998Q9.25002,5.74994,9.49994,5.74994Q9.74998,5.74994,9.74998,5.99998Z"
                    fillRule="evenodd"
                    fill="#14C9BB"
                    fillOpacity="1"
                />
            </g>
        </g>
    </svg>
);

export const deleteCommonDisabledIcon = () => (
    <svg xmlns="http://www.w3.org/2000/svg" fill="none" version="1.1" width="16" height="16" viewBox="0 0 16 16">
        <defs>
            <clipPath id="master_svg0_121_28408">
                <rect x="0" y="0" width="16" height="16" rx="0" />
            </clipPath>
        </defs>
        <g clipPath="url(#master_svg0_121_28408)">
            <g>
                <path
                    d="M9.49994,12C9.22346,12,9.00002,11.7765,9.00002,11.5L9.00002,5.99998C9.00002,5.72349,9.22346,5.49994,9.49994,5.49994C9.77643,5.49994,9.99998,5.72351,9.99998,5.99998L9.99998,11.5C9.99998,11.7765,9.77644,12,9.49994,12L9.49994,12ZM6.50001,12C6.22343,12,5.99998,11.7765,5.99998,11.5L5.99998,5.99998C5.99998,5.72349,6.22343,5.49994,6.50001,5.49994C6.77649,5.49994,6.99993,5.72351,6.99993,5.99998L6.99993,11.5C6.99993,11.7765,6.77649,12,6.50001,12L6.50001,12ZM14.4999,3.50001L11.9999,3.50001L11.9999,2.50001C11.9999,1.672981,11.3324,1.000000359596,10.51145,1.000000359596L5.49993,1.000000359596C4.67299,1.000000359596,4,1.672996,4,2.50001L4,3.50001L1.5000339999999999,3.50001C1.223458,3.50001,1,3.72345,1,4.000030000000001C1,4.27651,1.223457,4.49995,1.5000339999999999,4.49995L14.5,4.49995C14.7764,4.49995,15,4.27651,15,4.000030000000001C15,3.72345,14.7764,3.5,14.5,3.5L14.4999,3.50001ZM4.99999,2.50001C4.99999,2.2245,5.22445,1.999989,5.49993,1.999989L10.51146,1.999989C10.78553,1.999989,10.99998,2.2195,10.99998,2.50001L10.99998,3.50001L4.99999,3.50001L4.99999,2.50001ZM11.502,15L4.50094,15C3.67399,15,3.00101,14.327,3.00101,13.5L3.00101,5.99251C3.00101,5.71649,3.22501,5.4925,3.50097,5.4925C3.77701,5.4925,4.00099,5.71649,4.00099,5.99251L4.00099,13.5C4.00099,13.776,4.22547,14,4.50096,14L11.502,14C11.7779,14,12.002,13.776,12.002,13.5L12.002,6.01297C12.002,5.73701,12.2255,5.513,12.502,5.513C12.7785,5.513,13.002,5.73701,13.002,6.01297L13.002,13.5C13.002,14.327,12.3289,15,11.502,15Z"
                    fill="#B3BBC8"
                    fillOpacity="1"
                />
                <path
                    d="M15.0304,3.46951Q14.8109,3.25,14.5,3.25L14.5,3.75Q14.75,3.75,14.75,4.000030000000001Q14.75,4.24995,14.5,4.24995L1.5000339999999999,4.24995Q1.25,4.24995,1.25,4.000030000000001Q1.25,3.75001,1.5000339999999999,3.75001L4.25,3.75001L4.25,2.50001Q4.25,1.983306,4.61664,1.616648Q4.983280000000001,1.25,5.49993,1.25L10.51145,1.25Q11.023,1.25,11.3861,1.61595Q11.7499,1.982661,11.7499,2.50001L11.7499,3.75001L14.4999,3.75001L14.4999,3.25001L12.2499,3.25001L12.2499,2.50001Q12.2499,1.776703,11.741,1.263788Q11.2313,0.75,10.51145,0.75L5.49993,0.75Q4.77616,0.75,4.26308,1.263101Q3.75,1.776204,3.75,2.50001L3.75,3.25001L1.5000339999999999,3.25001Q1.18905,3.25001,0.9695279,3.46952Q0.75,3.68904,0.75,4.000030000000001Q0.75,4.31096,0.9695394,4.53046Q1.189066,4.74995,1.5000339999999999,4.74995L14.5,4.74995Q14.8109,4.74995,15.0304,4.53048Q15.25,4.31096,15.25,4.000030000000001Q15.25,3.68905,15.0304,3.46951ZM4.74999,2.50001L4.74999,3.75001L11.25,3.75001L11.25,2.50001Q11.25,2.18777,11.0373,1.970102Q10.82227,1.749989,10.51146,1.749989L5.49993,1.749989Q5.18975,1.749989,4.969860000000001,1.969921Q4.74999,2.1898299999999997,4.74999,2.50001ZM5.24999,3.25001L10.74998,3.25001L10.74998,2.50001Q10.74998,2.24999,10.51146,2.24999L5.49993,2.24999Q5.24999,2.24999,5.24999,2.50001L5.24999,3.25001ZM12.7389,14.7369Q13.252,14.2238,13.252,13.5L13.252,6.01297Q13.252,5.7024,13.0325,5.48277Q12.8128,5.263,12.502,5.263Q12.1911,5.263,11.9714,5.48277Q11.752,5.7024,11.752,6.01297L11.752,13.5Q11.752,13.75,11.502,13.75L4.50096,13.75Q4.25099,13.75,4.25099,13.5L4.25099,5.99251Q4.25099,5.68194,4.03127,5.46222Q3.81155,5.2425,3.50097,5.2425Q3.19043,5.2425,2.97072,5.46223Q2.75101,5.68195,2.75101,5.99251L2.75101,13.5Q2.75101,14.2238,3.26409,14.7369Q3.77717,15.25,4.50094,15.25L11.502,15.25Q12.2258,15.25,12.7389,14.7369ZM7.03043,12.0304Q7.24993,11.8109,7.24993,11.5L7.24993,5.99998Q7.24993,5.68908,7.03046,5.46954Q6.81094,5.24994,6.50001,5.24994Q6.18903,5.24994,5.96949,5.46951Q5.74998,5.68906,5.74998,5.99998L5.74998,11.5Q5.74998,11.811,5.96951,12.0305Q6.18904,12.25,6.50002,12.25Q6.81094,12.25,7.03043,12.0304ZM10.03041,12.0305Q10.24998,11.8109,10.24998,11.5L10.24998,5.99998Q10.24998,5.68907,10.03042,5.46951Q9.81086,5.24994,9.49994,5.24994Q9.18902,5.24994,8.9695,5.46953Q8.75002,5.68907,8.75002,5.99998L8.75002,11.5Q8.75002,11.811,8.96952,12.0305Q9.18903,12.25,9.49996,12.25Q9.81087,12.25,10.03041,12.0305ZM12.752,6.01297L12.752,13.5Q12.752,14.0167,12.3853,14.3834Q12.0187,14.75,11.502,14.75L4.50094,14.75Q3.98429,14.75,3.61765,14.3834Q3.25101,14.0167,3.25101,13.5L3.25101,5.99251Q3.25101,5.7425,3.50097,5.7425Q3.75099,5.7425,3.75099,5.99251L3.75099,13.5Q3.75099,13.8105,3.97094,14.0304Q4.19074,14.25,4.50096,14.25L11.502,14.25Q11.8125,14.25,12.0322,14.0303Q12.252,13.8106,12.252,13.5L12.252,6.01297Q12.252,5.763,12.502,5.763Q12.752,5.763,12.752,6.01297ZM6.74993,5.99998L6.74993,11.5Q6.74993,11.75,6.49999,11.75Q6.24998,11.75,6.24998,11.5L6.24998,5.99998Q6.24998,5.74994,6.50001,5.74994Q6.74993,5.74994,6.74993,5.99998ZM9.74998,5.99998L9.74998,11.5Q9.74998,11.75,9.49993,11.75Q9.25002,11.75,9.25002,11.5L9.25002,5.99998Q9.25002,5.74994,9.49994,5.74994Q9.74998,5.74994,9.74998,5.99998Z"
                    fillRule="evenodd"
                    fill="#B3BBC8"
                    fillOpacity="1"
                />
            </g>
        </g>
    </svg>
);

export const deleteCommonWhiteIcon = () => (
    <svg xmlns="http://www.w3.org/2000/svg" fill="none" version="1.1" width="16" height="16" viewBox="0 0 16 16">
        <defs>
            <clipPath id="master_svg0_121_28408">
                <rect x="0" y="0" width="16" height="16" rx="0" />
            </clipPath>
        </defs>
        <g clipPath="url(#master_svg0_121_28408)">
            <g>
                <path
                    d="M9.49994,12C9.22346,12,9.00002,11.7765,9.00002,11.5L9.00002,5.99998C9.00002,5.72349,9.22346,5.49994,9.49994,5.49994C9.77643,5.49994,9.99998,5.72351,9.99998,5.99998L9.99998,11.5C9.99998,11.7765,9.77644,12,9.49994,12L9.49994,12ZM6.50001,12C6.22343,12,5.99998,11.7765,5.99998,11.5L5.99998,5.99998C5.99998,5.72349,6.22343,5.49994,6.50001,5.49994C6.77649,5.49994,6.99993,5.72351,6.99993,5.99998L6.99993,11.5C6.99993,11.7765,6.77649,12,6.50001,12L6.50001,12ZM14.4999,3.50001L11.9999,3.50001L11.9999,2.50001C11.9999,1.672981,11.3324,1.000000359596,10.51145,1.000000359596L5.49993,1.000000359596C4.67299,1.000000359596,4,1.672996,4,2.50001L4,3.50001L1.5000339999999999,3.50001C1.223458,3.50001,1,3.72345,1,4.000030000000001C1,4.27651,1.223457,4.49995,1.5000339999999999,4.49995L14.5,4.49995C14.7764,4.49995,15,4.27651,15,4.000030000000001C15,3.72345,14.7764,3.5,14.5,3.5L14.4999,3.50001ZM4.99999,2.50001C4.99999,2.2245,5.22445,1.999989,5.49993,1.999989L10.51146,1.999989C10.78553,1.999989,10.99998,2.2195,10.99998,2.50001L10.99998,3.50001L4.99999,3.50001L4.99999,2.50001ZM11.502,15L4.50094,15C3.67399,15,3.00101,14.327,3.00101,13.5L3.00101,5.99251C3.00101,5.71649,3.22501,5.4925,3.50097,5.4925C3.77701,5.4925,4.00099,5.71649,4.00099,5.99251L4.00099,13.5C4.00099,13.776,4.22547,14,4.50096,14L11.502,14C11.7779,14,12.002,13.776,12.002,13.5L12.002,6.01297C12.002,5.73701,12.2255,5.513,12.502,5.513C12.7785,5.513,13.002,5.73701,13.002,6.01297L13.002,13.5C13.002,14.327,12.3289,15,11.502,15Z"
                    fill="#FFFFFF"
                    fillOpacity="1"
                />
                <path
                    d="M15.0304,3.46951Q14.8109,3.25,14.5,3.25L14.5,3.75Q14.75,3.75,14.75,4.000030000000001Q14.75,4.24995,14.5,4.24995L1.5000339999999999,4.24995Q1.25,4.24995,1.25,4.000030000000001Q1.25,3.75001,1.5000339999999999,3.75001L4.25,3.75001L4.25,2.50001Q4.25,1.983306,4.61664,1.616648Q4.983280000000001,1.25,5.49993,1.25L10.51145,1.25Q11.023,1.25,11.3861,1.61595Q11.7499,1.982661,11.7499,2.50001L11.7499,3.75001L14.4999,3.75001L14.4999,3.25001L12.2499,3.25001L12.2499,2.50001Q12.2499,1.776703,11.741,1.263788Q11.2313,0.75,10.51145,0.75L5.49993,0.75Q4.77616,0.75,4.26308,1.263101Q3.75,1.776204,3.75,2.50001L3.75,3.25001L1.5000339999999999,3.25001Q1.18905,3.25001,0.9695279,3.46952Q0.75,3.68904,0.75,4.000030000000001Q0.75,4.31096,0.9695394,4.53046Q1.189066,4.74995,1.5000339999999999,4.74995L14.5,4.74995Q14.8109,4.74995,15.0304,4.53048Q15.25,4.31096,15.25,4.000030000000001Q15.25,3.68905,15.0304,3.46951ZM4.74999,2.50001L4.74999,3.75001L11.25,3.75001L11.25,2.50001Q11.25,2.18777,11.0373,1.970102Q10.82227,1.749989,10.51146,1.749989L5.49993,1.749989Q5.18975,1.749989,4.969860000000001,1.969921Q4.74999,2.1898299999999997,4.74999,2.50001ZM5.24999,3.25001L10.74998,3.25001L10.74998,2.50001Q10.74998,2.24999,10.51146,2.24999L5.49993,2.24999Q5.24999,2.24999,5.24999,2.50001L5.24999,3.25001ZM12.7389,14.7369Q13.252,14.2238,13.252,13.5L13.252,6.01297Q13.252,5.7024,13.0325,5.48277Q12.8128,5.263,12.502,5.263Q12.1911,5.263,11.9714,5.48277Q11.752,5.7024,11.752,6.01297L11.752,13.5Q11.752,13.75,11.502,13.75L4.50096,13.75Q4.25099,13.75,4.25099,13.5L4.25099,5.99251Q4.25099,5.68194,4.03127,5.46222Q3.81155,5.2425,3.50097,5.2425Q3.19043,5.2425,2.97072,5.46223Q2.75101,5.68195,2.75101,5.99251L2.75101,13.5Q2.75101,14.2238,3.26409,14.7369Q3.77717,15.25,4.50094,15.25L11.502,15.25Q12.2258,15.25,12.7389,14.7369ZM7.03043,12.0304Q7.24993,11.8109,7.24993,11.5L7.24993,5.99998Q7.24993,5.68908,7.03046,5.46954Q6.81094,5.24994,6.50001,5.24994Q6.18903,5.24994,5.96949,5.46951Q5.74998,5.68906,5.74998,5.99998L5.74998,11.5Q5.74998,11.811,5.96951,12.0305Q6.18904,12.25,6.50002,12.25Q6.81094,12.25,7.03043,12.0304ZM10.03041,12.0305Q10.24998,11.8109,10.24998,11.5L10.24998,5.99998Q10.24998,5.68907,10.03042,5.46951Q9.81086,5.24994,9.49994,5.24994Q9.18902,5.24994,8.9695,5.46953Q8.75002,5.68907,8.75002,5.99998L8.75002,11.5Q8.75002,11.811,8.96952,12.0305Q9.18903,12.25,9.49996,12.25Q9.81087,12.25,10.03041,12.0305ZM12.752,6.01297L12.752,13.5Q12.752,14.0167,12.3853,14.3834Q12.0187,14.75,11.502,14.75L4.50094,14.75Q3.98429,14.75,3.61765,14.3834Q3.25101,14.0167,3.25101,13.5L3.25101,5.99251Q3.25101,5.7425,3.50097,5.7425Q3.75099,5.7425,3.75099,5.99251L3.75099,13.5Q3.75099,13.8105,3.97094,14.0304Q4.19074,14.25,4.50096,14.25L11.502,14.25Q11.8125,14.25,12.0322,14.0303Q12.252,13.8106,12.252,13.5L12.252,6.01297Q12.252,5.763,12.502,5.763Q12.752,5.763,12.752,6.01297ZM6.74993,5.99998L6.74993,11.5Q6.74993,11.75,6.49999,11.75Q6.24998,11.75,6.24998,11.5L6.24998,5.99998Q6.24998,5.74994,6.50001,5.74994Q6.74993,5.74994,6.74993,5.99998ZM9.74998,5.99998L9.74998,11.5Q9.74998,11.75,9.49993,11.75Q9.25002,11.75,9.25002,11.5L9.25002,5.99998Q9.25002,5.74994,9.49994,5.74994Q9.74998,5.74994,9.74998,5.99998Z"
                    fillRule="evenodd"
                    fill="#FFFFFF"
                    fillOpacity="1"
                />
            </g>
        </g>
    </svg>
);

export const addCommonIcon = () => (
    <svg xmlns="http://www.w3.org/2000/svg" fill="none" version="1.1" width="16" height="16" viewBox="0 0 16 16">
        <g>
            <g />
            <g>
                <rect x="2" y="7.25" width="12" height="1.5" rx="0.75" fill="#14C9BB" fillOpacity="1" />
            </g>
            <g transform="matrix(0,1,-1,0,10.75,-6.75)">
                <path
                    d="M8.75,2.75C8.75,3.1642099999999997,9.085786,3.5,9.5,3.5L20,3.5C20.4142,3.5,20.75,3.1642099999999997,20.75,2.75C20.75,2.335786,20.4142,2,20,2L9.5,2C9.085786,2,8.75,2.335786,8.75,2.75Z"
                    fill="#14C9BB"
                    fillOpacity="1"
                />
            </g>
        </g>
    </svg>
);

export const addCommonPrimaryIcon = () => (
    <svg xmlns="http://www.w3.org/2000/svg" fill="none" version="1.1" width="16" height="16" viewBox="0 0 16 16">
        <g>
            <g />
            <g>
                <rect x="1" y="7" width="14" height="2" rx="1" fill="#FFFFFF" fillOpacity="1" />
            </g>
            <g transform="matrix(0,1,-1,0,10,-8)">
                <path
                    d="M9,2C9,2.55228,9.447715,3,10,3L22,3C22.552300000000002,3,23,2.55228,23,2C23,1.447715,22.552300000000002,1,22,1L10,1C9.447715,1,9,1.447715,9,2Z"
                    fill="#FFFFFF"
                    fillOpacity="1"
                />
            </g>
        </g>
    </svg>
);

export const addCommonDisabledIcon = () => (
    <svg xmlns="http://www.w3.org/2000/svg" fill="none" version="1.1" width="16" height="16" viewBox="0 0 16 16">
        <g>
            <g />
            <g>
                <rect x="1" y="7" width="14" height="2" rx="1" fill="#B3BBC8" fillOpacity="1" />
            </g>
            <g transform="matrix(0,1,-1,0,10,-8)">
                <path
                    d="M9,2C9,2.55228,9.447715,3,10,3L22,3C22.552300000000002,3,23,2.55228,23,2C23,1.447715,22.552300000000002,1,22,1L10,1C9.447715,1,9,1.447715,9,2Z"
                    fill="#B3BBC8"
                    fillOpacity="1"
                />
            </g>
        </g>
    </svg>
);

export const moreInfo = () => (
    <svg xmlns="http://www.w3.org/2000/svg" fill="none" version="1.1" width="20" height="20" viewBox="0 0 20 20">
        <g>
            <g />
            <g>
                <path
                    d="M17.3333,3C17.7015,3,18,3.28491,18,3.636364C18,3.962714,17.7426,4.23169,17.411099999999998,4.26845L17.3333,4.27273L2.666667,4.27273C2.298477,4.27273,2,3.987818,2,3.636364C2,3.310014,2.25736,3.0410409,2.5889189999999997,3.00428126L2.666667,3L17.3333,3Z"
                    fill="#14C9BB"
                    fillOpacity="1"
                />
                <path
                    d="M17.3333,2.5L2.65291,2.5L2.547593,2.505799L2.533822,2.507326Q2.100647,2.555352,1.805667,2.869424Q1.5,3.194876,1.5,3.636364Q1.5,4.1139,1.850024,4.44802Q2.190195,4.77273,2.666667,4.77273L17.347099999999998,4.77273L17.4524,4.76693L17.4662,4.7654Q17.8994,4.71738,18.1943,4.4033Q18.5,4.07785,18.5,3.636364Q18.5,3.158822,18.15,2.824709Q17.8098,2.5,17.3333,2.5ZM2.6804230000000002,3.5L2.633312,3.502594Q2.5,3.5217169999999998,2.5,3.636364Q2.5,3.772727,2.666667,3.772727L17.3196,3.772727L17.3667,3.770133Q17.5,3.751009,17.5,3.636364Q17.5,3.5,17.3333,3.5L2.6804230000000002,3.5Z"
                    fillRule="evenodd"
                    fill="#14C9BB"
                    fillOpacity="1"
                />
            </g>
            <g>
                <path
                    d="M17.3333,9.36376953125C17.7015,9.36376953125,18,9.64867953125,18,10.00013353125C18,10.32648353125,17.7426,10.59545953125,17.411099999999998,10.63221953125L17.3333,10.63649953125L2.666667,10.63649953125C2.298477,10.63649953125,2,10.35158753125,2,10.00013353125C2,9.67378353125,2.25736,9.40481043125,2.5889189999999997,9.36805079125L2.666667,9.36376953125L17.3333,9.36376953125Z"
                    fill="#14C9BB"
                    fillOpacity="1"
                />
                <path
                    d="M17.3333,8.86376953125L2.65291,8.86376953125L2.547593,8.86956853125L2.533822,8.87109553125Q2.100647,8.91912153125,1.805667,9.23319353125Q1.5,9.55864553125,1.5,10.00013353125Q1.5,10.47766953125,1.850024,10.81178953125Q2.190195,11.13649953125,2.666667,11.13649953125L17.347099999999998,11.13649953125L17.4524,11.13069953125L17.4662,11.12916953125Q17.8994,11.08114953125,18.1943,10.76706953125Q18.5,10.44161953125,18.5,10.00013353125Q18.5,9.52259153125,18.15,9.18847853125Q17.8098,8.86376953125,17.3333,8.86376953125ZM2.6804230000000002,9.86376953125L2.633312,9.86636353125Q2.5,9.88548653125,2.5,10.00013353125Q2.5,10.13649653125,2.666667,10.13649653125L17.3196,10.13649653125L17.3667,10.13390253125Q17.5,10.11477853125,17.5,10.00013353125Q17.5,9.86376953125,17.3333,9.86376953125L2.6804230000000002,9.86376953125Z"
                    fillRule="evenodd"
                    fill="#14C9BB"
                    fillOpacity="1"
                />
            </g>
            <g>
                <path
                    d="M17.3333,15.72705078125C17.7015,15.72705078125,18,16.01196078125,18,16.36341478125C18,16.68976478125,17.7426,16.95874078125,17.411099999999998,16.99550078125L17.3333,16.99978078125L2.666667,16.99978078125C2.298477,16.99978078125,2,16.71486878125,2,16.36341478125C2,16.03706478125,2.25736,15.76809168125,2.5889189999999997,15.73133204125L2.666667,15.72705078125L17.3333,15.72705078125Z"
                    fill="#14C9BB"
                    fillOpacity="1"
                />
                <path
                    d="M17.3333,15.22705078125L2.65291,15.22705078125L2.547593,15.23284978125L2.533822,15.23437678125Q2.100647,15.28240278125,1.805667,15.59647478125Q1.5,15.92192678125,1.5,16.36341478125Q1.5,16.84095078125,1.850024,17.17507078125Q2.190195,17.49978078125,2.666667,17.49978078125L17.347099999999998,17.49978078125L17.4524,17.49398078125L17.4662,17.49245078125Q17.8994,17.44443078125,18.1943,17.13035078125Q18.5,16.80490078125,18.5,16.36341478125Q18.5,15.88587278125,18.15,15.55175978125Q17.8098,15.22705078125,17.3333,15.22705078125ZM2.6804230000000002,16.22705078125L2.633312,16.22964478125Q2.5,16.24876778125,2.5,16.36341478125Q2.5,16.49977778125,2.666667,16.49977778125L17.3196,16.49977778125L17.3667,16.49718378125Q17.5,16.47805978125,17.5,16.36341478125Q17.5,16.22705078125,17.3333,16.22705078125L2.6804230000000002,16.22705078125Z"
                    fillRule="evenodd"
                    fill="#14C9BB"
                    fillOpacity="1"
                />
            </g>
        </g>
    </svg>
);

export const editCommonIcon = () => (
    <svg xmlns="http://www.w3.org/2000/svg" fill="none" version="1.1" width="20" height="20" viewBox="0 0 20 20">
        <g>
            <g />
            <g>
                <g>
                    <path
                        d="M5,4L12.28571,4Q12.38421,4,12.4808,3.980785Q12.5774,3.96157,12.6684,3.923879Q12.75939,3.8861879999999998,12.84128,3.83147Q12.92318,3.776751,12.99282,3.707107Q13.0625,3.637463,13.1172,3.55557Q13.1719,3.473678,13.2096,3.382683Q13.2473,3.291689,13.2665,3.19509Q13.2857,3.0984914,13.2857,3Q13.2857,2.9015086,13.2665,2.80491Q13.2473,2.708311,13.2096,2.617317Q13.1719,2.526322,13.1172,2.44443Q13.0625,2.362537,12.99282,2.292893Q12.92318,2.223249,12.84128,2.16853Q12.75939,2.1138120000000002,12.6684,2.076121Q12.5774,2.03843,12.4808,2.019215Q12.38421,2,12.28571,2L5,2Q3.7573600000000003,2,2.87868,2.87868Q2,3.757359,2,5L2,16Q2,17.2426,2.87868,18.121299999999998Q3.7573600000000003,19,5,19L16,19Q17.2426,19,18.121299999999998,18.121299999999998Q19,17.2426,19,16L19,8Q19,7.90151,18.980800000000002,7.80491Q18.9616,7.70831,18.9239,7.61732Q18.886200000000002,7.52632,18.8315,7.44443Q18.776699999999998,7.36254,18.7071,7.29289Q18.6375,7.22325,18.5556,7.16853Q18.4737,7.11381,18.3827,7.07612Q18.2917,7.03843,18.1951,7.01921Q18.0985,7,18,7Q17.9015,7,17.8049,7.01921Q17.7083,7.03843,17.6173,7.07612Q17.5263,7.11381,17.4444,7.16853Q17.3625,7.22325,17.2929,7.29289Q17.2232,7.36254,17.1685,7.44443Q17.113799999999998,7.52632,17.0761,7.61732Q17.0384,7.70831,17.019199999999998,7.80491Q17,7.90151,17,8L17,16Q17,16.4142,16.7071,16.7071Q16.4142,17,16,17L5,17Q4.58579,17,4.29289,16.7071Q4,16.4142,4,16L4,5Q4,4.58579,4.29289,4.29289Q4.58579,4,5,4Z"
                        fillRule="evenodd"
                        fill="#14C9BB"
                        fillOpacity="1"
                    />
                </g>
                <g>
                    <path
                        d="M18.7071,3.707107Q18.8478,3.566455,18.9239,3.382683Q19,3.198912,19,3Q19,2.9015086,18.980800000000002,2.80491Q18.9616,2.708311,18.9239,2.617317Q18.886200000000002,2.526322,18.8315,2.44443Q18.776699999999998,2.362537,18.7071,2.292893Q18.6375,2.223249,18.5556,2.16853Q18.4737,2.1138120000000002,18.3827,2.076121Q18.2917,2.03843,18.1951,2.019215Q18.0985,2,18,2Q17.801090000000002,2,17.61732,2.07612Q17.43355,2.152241,17.29289,2.292893L17.29252,2.293268L7.793268,11.79252L7.792893,11.79289Q7.652241,11.93354,7.57612,12.11732Q7.5,12.30109,7.5,12.5Q7.5,12.59849,7.519215,12.69509Q7.538429,12.79169,7.57612,12.88268Q7.613812,12.97368,7.66853,13.0556Q7.723249,13.1375,7.792893,13.2071Q7.862537,13.2767,7.9444298,13.3315Q8.0263224,13.3862,8.117317,13.4239Q8.208311,13.4616,8.30491,13.4808Q8.401509,13.5,8.5,13.5Q8.698912,13.5,8.882684,13.4239Q9.06645,13.3478,9.20711,13.2071L9.207419999999999,13.2068L18.706699999999998,3.707482L18.7071,3.707107L18.7071,3.707107Z"
                        fillRule="evenodd"
                        fill="#14C9BB"
                        fillOpacity="1"
                    />
                </g>
            </g>
        </g>
    </svg>
);

export const editTableIcon = () => (
    <svg xmlns="http://www.w3.org/2000/svg" fill="none" version="1.1" width="16" height="16" viewBox="0 0 16 16">
        <g>
            <g />
            <g>
                <g>
                    <path
                        d="M3.066748046875,2.6L3.066748046875,13.4Q3.066748046875,13.8971,3.418221046875,14.2485Q3.769693046875,14.6,4.266748046875,14.6L12.666748046875,14.6Q13.163808046875,14.6,13.515278046875,14.2485Q13.866748046875,13.8971,13.866748046875,13.4L13.866748046875,5Q13.866748046875,4.8888300000000005,13.826948046875,4.78504Q13.787048046875,4.68125,13.712748046875,4.59862L11.012728046875,1.598621Q10.927388046875,1.5038,10.810848046875,1.4519Q10.694318046875,1.4,10.566748046875,1.4L4.266748046875,1.4Q3.769691046875,1.4,3.418220046875,1.751472Q3.066748046875,2.102944,3.066748046875,2.6ZM12.666748046875,13.4L4.266748046875,13.4L4.266748046875,2.6L10.299528046875,2.6L12.666748046875,5.23024L12.666748046875,13.4Z"
                        fillRule="evenodd"
                        fill="#14C9BB"
                        fillOpacity="1"
                    />
                </g>
                <g>
                    <path
                        d="M5.766747856140137,10.099852561950684L5.766747856140137,11.299852561950683Q5.766747856140137,11.358952561950684,5.7782768561401365,11.416902561950684Q5.789805856140137,11.474862561950683,5.812419856140137,11.529462561950684Q5.835034856140137,11.584062561950685,5.867865856140137,11.633192561950683Q5.900697856140137,11.682332561950684,5.942483856140137,11.724112561950683Q5.984269856140137,11.765902561950684,6.033405856140137,11.798732561950683Q6.082540856140136,11.831562561950683,6.137137856140137,11.854182561950683Q6.191733856140137,11.876792561950683,6.249693856140136,11.888322561950684Q6.307653056140136,11.899852561950684,6.366747856140137,11.899852561950684L7.566747856140137,11.899852561950684Q7.686097856140137,11.899852561950684,7.796357856140137,11.854182561950683Q7.906617856140137,11.808512561950684,7.991007856140136,11.724112561950683L10.991007856140136,8.724112561950683Q11.032797856140137,8.682332561950684,11.065627856140136,8.633192561950683Q11.098457856140136,8.584062561950685,11.121077856140136,8.529462561950684Q11.143687856140136,8.474862561950683,11.155217856140137,8.416902561950684Q11.166747856140137,8.358942561950684,11.166747856140137,8.299852561950683Q11.166747856140137,8.240762561950683,11.155217856140137,8.182802561950684Q11.143687856140136,8.124842561950684,11.121077856140136,8.070242561950684Q11.098457856140136,8.015645561950684,11.065627856140136,7.966510561950684Q11.032797856140137,7.917374561950684,10.991007856140136,7.875588561950684L9.791007856140137,6.675588561950684Q9.749227856140138,6.6338025619506835,9.700087856140136,6.6009705619506835Q9.650957856140137,6.568139561950684,9.596357856140138,6.545524561950684Q9.541757856140137,6.522910561950684,9.483797856140136,6.511381561950683Q9.425847856140138,6.499852561950684,9.366747856140137,6.499852561950684Q9.307657856140137,6.499852561950684,9.249697856140138,6.511381561950683Q9.191737856140136,6.522910561950684,9.137137856140136,6.545524561950684Q9.082537856140137,6.568139561950684,9.033407856140137,6.6009705619506835Q8.984267856140136,6.6338025619506835,8.942487856140136,6.675588561950684L5.942483856140137,9.675592561950683Q5.858092856140137,9.759982561950684,5.812419856140137,9.870242561950683Q5.766747856140137,9.980502561950683,5.766747856140137,10.099852561950684ZM6.966747856140136,10.699852561950683L7.318219856140137,10.699852561950683L9.718217856140136,8.299852561950683L9.366747856140137,7.9483805619506835L6.966747856140136,10.348382561950684L6.966747856140136,10.699852561950683Z"
                        fillRule="evenodd"
                        fill="#14C9BB"
                        fillOpacity="1"
                    />
                </g>
                <g>
                    <path
                        d="M10.266674518585205,5.5L13.266674518585205,5.5Q13.315924518585206,5.5,13.364214518585205,5.49039Q13.412514518585205,5.48078,13.458014518585205,5.46194Q13.503514518585206,5.44309,13.544454518585205,5.41573Q13.585404518585205,5.38837,13.620224518585205,5.35355Q13.655044518585205,5.31873,13.682404518585205,5.27778Q13.709764518585205,5.23684,13.728614518585205,5.19134Q13.747454518585204,5.14584,13.757064518585205,5.09754Q13.766674518585205,5.04925,13.766674518585205,5Q13.766674518585205,4.95075,13.757064518585205,4.90245Q13.747454518585204,4.854150000000001,13.728614518585205,4.80866Q13.709764518585205,4.76316,13.682404518585205,4.7222100000000005Q13.655044518585205,4.68127,13.620224518585205,4.64645Q13.585404518585205,4.61162,13.544454518585205,4.5842600000000004Q13.503514518585206,4.55691,13.458014518585205,4.53806Q13.412514518585205,4.51921,13.364214518585205,4.50961Q13.315924518585206,4.5,13.266674518585205,4.5L10.766674518585205,4.5L10.766674518585205,2Q10.766674518585205,1.9507543,10.757067518585204,1.9024549Q10.747459518585206,1.854155,10.728614518585205,1.8086579999999999Q10.709768518585205,1.763161,10.682409518585205,1.722215Q10.655049518585205,1.681269,10.620227518585205,1.646447Q10.585405518585205,1.611625,10.544459518585205,1.584265Q10.503513518585205,1.5569060000000001,10.458016518585206,1.53806Q10.412519518585205,1.519215,10.364219618585205,1.509607Q10.315920218585205,1.5,10.266674518585205,1.5Q10.217428818585205,1.5,10.169129418585205,1.509607Q10.120829518585206,1.519215,10.075332518585205,1.53806Q10.029835518585205,1.5569060000000001,9.988889518585205,1.584265Q9.947943518585205,1.611625,9.913121518585205,1.646447Q9.878299518585205,1.681269,9.850939518585205,1.722215Q9.823580518585205,1.763161,9.804734518585205,1.8086579999999999Q9.785889518585204,1.854155,9.776281518585206,1.9024549Q9.766674518585205,1.9507543,9.766674518585205,2L9.766674518585205,5Q9.766674518585205,5.04925,9.776281518585206,5.09754Q9.785889518585204,5.14584,9.804734518585205,5.19134Q9.823580518585205,5.23684,9.850939518585205,5.27778Q9.878299518585205,5.31873,9.913121518585205,5.35355Q9.947943518585205,5.38837,9.988889518585205,5.41573Q10.029835518585205,5.44309,10.075332518585205,5.46194Q10.120829518585206,5.48078,10.169129418585205,5.49039Q10.217428818585205,5.5,10.266674518585205,5.5Z"
                        fillRule="evenodd"
                        fill="#14C9BB"
                        fillOpacity="1"
                    />
                </g>
            </g>
        </g>
    </svg>
);
export const editTableWriteIcon = () => (
    <svg xmlns="http://www.w3.org/2000/svg" fill="none" version="1.1" width="16" height="16" viewBox="0 0 16 16">
        <g>
            <g />
            <g>
                <g>
                    <path
                        d="M3.066748046875,2.6L3.066748046875,13.4Q3.066748046875,13.8971,3.418221046875,14.2485Q3.769693046875,14.6,4.266748046875,14.6L12.666748046875,14.6Q13.163808046875,14.6,13.515278046875,14.2485Q13.866748046875,13.8971,13.866748046875,13.4L13.866748046875,5Q13.866748046875,4.8888300000000005,13.826948046875,4.78504Q13.787048046875,4.68125,13.712748046875,4.59862L11.012728046875,1.598621Q10.927388046875,1.5038,10.810848046875,1.4519Q10.694318046875,1.4,10.566748046875,1.4L4.266748046875,1.4Q3.769691046875,1.4,3.418220046875,1.751472Q3.066748046875,2.102944,3.066748046875,2.6ZM12.666748046875,13.4L4.266748046875,13.4L4.266748046875,2.6L10.299528046875,2.6L12.666748046875,5.23024L12.666748046875,13.4Z"
                        fillRule="evenodd"
                        fill="#ffffff"
                        fillOpacity="1"
                    />
                </g>
                <g>
                    <path
                        d="M5.766747856140137,10.099852561950684L5.766747856140137,11.299852561950683Q5.766747856140137,11.358952561950684,5.7782768561401365,11.416902561950684Q5.789805856140137,11.474862561950683,5.812419856140137,11.529462561950684Q5.835034856140137,11.584062561950685,5.867865856140137,11.633192561950683Q5.900697856140137,11.682332561950684,5.942483856140137,11.724112561950683Q5.984269856140137,11.765902561950684,6.033405856140137,11.798732561950683Q6.082540856140136,11.831562561950683,6.137137856140137,11.854182561950683Q6.191733856140137,11.876792561950683,6.249693856140136,11.888322561950684Q6.307653056140136,11.899852561950684,6.366747856140137,11.899852561950684L7.566747856140137,11.899852561950684Q7.686097856140137,11.899852561950684,7.796357856140137,11.854182561950683Q7.906617856140137,11.808512561950684,7.991007856140136,11.724112561950683L10.991007856140136,8.724112561950683Q11.032797856140137,8.682332561950684,11.065627856140136,8.633192561950683Q11.098457856140136,8.584062561950685,11.121077856140136,8.529462561950684Q11.143687856140136,8.474862561950683,11.155217856140137,8.416902561950684Q11.166747856140137,8.358942561950684,11.166747856140137,8.299852561950683Q11.166747856140137,8.240762561950683,11.155217856140137,8.182802561950684Q11.143687856140136,8.124842561950684,11.121077856140136,8.070242561950684Q11.098457856140136,8.015645561950684,11.065627856140136,7.966510561950684Q11.032797856140137,7.917374561950684,10.991007856140136,7.875588561950684L9.791007856140137,6.675588561950684Q9.749227856140138,6.6338025619506835,9.700087856140136,6.6009705619506835Q9.650957856140137,6.568139561950684,9.596357856140138,6.545524561950684Q9.541757856140137,6.522910561950684,9.483797856140136,6.511381561950683Q9.425847856140138,6.499852561950684,9.366747856140137,6.499852561950684Q9.307657856140137,6.499852561950684,9.249697856140138,6.511381561950683Q9.191737856140136,6.522910561950684,9.137137856140136,6.545524561950684Q9.082537856140137,6.568139561950684,9.033407856140137,6.6009705619506835Q8.984267856140136,6.6338025619506835,8.942487856140136,6.675588561950684L5.942483856140137,9.675592561950683Q5.858092856140137,9.759982561950684,5.812419856140137,9.870242561950683Q5.766747856140137,9.980502561950683,5.766747856140137,10.099852561950684ZM6.966747856140136,10.699852561950683L7.318219856140137,10.699852561950683L9.718217856140136,8.299852561950683L9.366747856140137,7.9483805619506835L6.966747856140136,10.348382561950684L6.966747856140136,10.699852561950683Z"
                        fillRule="evenodd"
                        fill="#ffffff"
                        fillOpacity="1"
                    />
                </g>
                <g>
                    <path
                        d="M10.266674518585205,5.5L13.266674518585205,5.5Q13.315924518585206,5.5,13.364214518585205,5.49039Q13.412514518585205,5.48078,13.458014518585205,5.46194Q13.503514518585206,5.44309,13.544454518585205,5.41573Q13.585404518585205,5.38837,13.620224518585205,5.35355Q13.655044518585205,5.31873,13.682404518585205,5.27778Q13.709764518585205,5.23684,13.728614518585205,5.19134Q13.747454518585204,5.14584,13.757064518585205,5.09754Q13.766674518585205,5.04925,13.766674518585205,5Q13.766674518585205,4.95075,13.757064518585205,4.90245Q13.747454518585204,4.854150000000001,13.728614518585205,4.80866Q13.709764518585205,4.76316,13.682404518585205,4.7222100000000005Q13.655044518585205,4.68127,13.620224518585205,4.64645Q13.585404518585205,4.61162,13.544454518585205,4.5842600000000004Q13.503514518585206,4.55691,13.458014518585205,4.53806Q13.412514518585205,4.51921,13.364214518585205,4.50961Q13.315924518585206,4.5,13.266674518585205,4.5L10.766674518585205,4.5L10.766674518585205,2Q10.766674518585205,1.9507543,10.757067518585204,1.9024549Q10.747459518585206,1.854155,10.728614518585205,1.8086579999999999Q10.709768518585205,1.763161,10.682409518585205,1.722215Q10.655049518585205,1.681269,10.620227518585205,1.646447Q10.585405518585205,1.611625,10.544459518585205,1.584265Q10.503513518585205,1.5569060000000001,10.458016518585206,1.53806Q10.412519518585205,1.519215,10.364219618585205,1.509607Q10.315920218585205,1.5,10.266674518585205,1.5Q10.217428818585205,1.5,10.169129418585205,1.509607Q10.120829518585206,1.519215,10.075332518585205,1.53806Q10.029835518585205,1.5569060000000001,9.988889518585205,1.584265Q9.947943518585205,1.611625,9.913121518585205,1.646447Q9.878299518585205,1.681269,9.850939518585205,1.722215Q9.823580518585205,1.763161,9.804734518585205,1.8086579999999999Q9.785889518585204,1.854155,9.776281518585206,1.9024549Q9.766674518585205,1.9507543,9.766674518585205,2L9.766674518585205,5Q9.766674518585205,5.04925,9.776281518585206,5.09754Q9.785889518585204,5.14584,9.804734518585205,5.19134Q9.823580518585205,5.23684,9.850939518585205,5.27778Q9.878299518585205,5.31873,9.913121518585205,5.35355Q9.947943518585205,5.38837,9.988889518585205,5.41573Q10.029835518585205,5.44309,10.075332518585205,5.46194Q10.120829518585206,5.48078,10.169129418585205,5.49039Q10.217428818585205,5.5,10.266674518585205,5.5Z"
                        fillRule="evenodd"
                        fill="#ffffff"
                        fillOpacity="1"
                    />
                </g>
            </g>
        </g>
    </svg>
);

export const configureIcon = () => (
    <svg xmlns="http://www.w3.org/2000/svg" fill="none" version="1.1" width="16" height="16" viewBox="0 0 16 16">
        <g>
            <g />
            <g>
                <g />
                <g>
                    <path
                        d="M14.8504,9.06948L11.9316,14.3622C11.7247,14.7397,11.3287,14.9747,10.89817,14.9756L5.10183,14.9756C4.6713000000000005,14.9747,4.27532,14.7397,4.0684000000000005,14.3622L1.14957,9.06948C0.9501434,8.707329999999999,0.9501434,8.26826,1.14957,7.90612L4.0684000000000005,2.613438C4.27532,2.235889,4.6713000000000005,2.000844071,5.10183,2L10.89817,2C11.3287,2.000844071,11.7247,2.235889,11.9316,2.613438L14.8504,7.90612C15.0499,8.26826,15.0499,8.707329999999999,14.8504,9.06948ZM13.9543,8.57094C13.9827,8.51922,13.9827,8.45655,13.9543,8.40482L11.0406,3.1121499999999997C11.0111,3.0581899999999997,10.95467,3.02456,10.89322,3.02439L5.10678,3.02439C5.04533,3.02456,4.98886,3.0581899999999997,4.95944,3.1121499999999997L2.04574,8.40482C2.01731,8.45655,2.01731,8.51922,2.04574,8.57094L4.95944,13.8636C4.9889,13.9175,5.04537,13.9511,5.10678,13.9512L10.89322,13.9512C10.95463,13.9511,11.0111,13.9175,11.0406,13.8636L13.9543,8.57094ZM7.95595,11.1536C6.4472,11.1536,5.22425,9.93065,5.22425,8.421890000000001C5.22425,6.91314,6.4472,5.690189999999999,7.95595,5.690189999999999C9.46454,5.690189999999999,10.68766,6.91314,10.68766,8.421890000000001C10.68766,9.93065,9.46453,11.1536,7.95595,11.1536ZM7.95595,10.12921C8.898900000000001,10.12921,9.66327,9.364840000000001,9.66327,8.421890000000001C9.66327,7.47894,8.898900000000001,6.71458,7.95595,6.71458C7.013,6.71458,6.24864,7.47894,6.24864,8.421890000000001C6.24864,9.364840000000001,7.013,10.12921,7.95595,10.12921Z"
                        fill="#14C9BB"
                        fillOpacity="1"
                    />
                    <path
                        d="M14.9818,9.14184Q15.342,8.4878,14.9818,7.83376L14.9818,7.83368L12.0631,2.541344Q11.6851,1.851542,10.89817,1.85L5.10154,1.85Q4.31493,1.851542,3.93705,2.541001L1.0181749,7.83376Q0.658012,8.48779,1.0182198,9.141919999999999L3.93686,14.4342Q4.31493,15.1241,5.10183,15.1256L10.89846,15.1256Q11.6851,15.1241,12.063,14.4346L14.9818,9.14184ZM14.719,7.97847L14.7191,7.97855Q14.9995,8.48784,14.719,8.997119999999999L11.8003,14.2897Q11.5072,14.8244,10.89787,14.8256L5.10183,14.8256Q4.4928,14.8244,4.19993,14.2901L1.28092,8.99704Q1.000491083,8.4878,1.2809650000000001,7.97847L4.19975,2.685876Q4.4928,2.151194,5.10213,2.15L10.89817,2.15Q11.5072,2.151194,11.8001,2.6855320000000003L14.719,7.97847ZM11.172,13.936L11.1722,13.9356L14.0857,8.64328Q14.1711,8.48788,14.0857,8.33257L11.172,3.03981Q11.0821,2.8749029999999998,10.89363,2.87439L5.10678,2.874389Q4.917949999999999,2.874904,4.82775,3.04034L1.914335,8.33248Q1.82892,8.48788,1.9142869999999998,8.6432L4.82804,13.936Q4.91816,14.1008,5.10646,14.1012L10.89322,14.1012Q11.0816,14.1008,11.172,13.936ZM13.8229,8.4986L10.90915,13.7913L10.90894,13.7917Q10.90374,13.8012,10.89322,13.8012L5.10711,13.8012Q5.09626,13.8012,5.09085,13.7913L2.17719,8.49869Q2.1712499999999997,8.48788,2.17714,8.47716L5.09114,3.1839500000000003Q5.09634,3.17442,5.10678,3.17439L10.89281,3.17439Q10.90367,3.17442,10.90915,3.1844900000000003L13.8228,8.47708Q13.8288,8.48788,13.8229,8.4986ZM9.99359,10.4596Q10.83765,9.61557,10.83766,8.421890000000001Q10.83765,7.22822,9.99359,6.38419Q9.14955,5.54019,7.95595,5.54019Q6.76225,5.54019,5.91825,6.38419Q5.07425,7.2282,5.07425,8.421890000000001Q5.07425,9.615590000000001,5.91825,10.45959Q6.76226,11.3036,7.95595,11.3036Q9.14955,11.3036,9.99359,10.4596ZM9.78146,6.59633Q10.53766,7.35249,10.53766,8.421890000000001Q10.53765,9.49129,9.78146,10.24746Q9.02528,11.0036,7.95595,11.0036Q6.88652,11.0036,6.13038,10.24746Q5.37425,9.49133,5.37425,8.421890000000001Q5.37425,7.35246,6.13038,6.59633Q6.88652,5.84019,7.95595,5.84019Q9.02529,5.84019,9.78146,6.59633ZM6.64262,7.10856Q6.09863,7.65255,6.09864,8.421890000000001Q6.09864,9.19124,6.64262,9.73522Q7.18661,10.27921,7.95595,10.27921Q8.7253,10.27921,9.26928,9.73522Q9.81327,9.191230000000001,9.81327,8.421890000000001Q9.81327,7.65255,9.26928,7.10856Q8.7253,6.56458,7.95595,6.56458Q7.18661,6.56458,6.64262,7.10856ZM6.85475,9.52309Q6.39864,9.066970000000001,6.39864,8.421890000000001Q6.39864,7.77681,6.85475,7.3207Q7.31087,6.86458,7.95595,6.86458Q8.60103,6.86458,9.05715,7.3207Q9.51327,7.77681,9.51327,8.421890000000001Q9.51327,9.066970000000001,9.05715,9.52309Q8.60103,9.97921,7.95595,9.97921Q7.31087,9.97921,6.85475,9.52309Z"
                        fillRule="evenodd"
                        fill="#14C9BB"
                        fillOpacity="1"
                    />
                </g>
            </g>
        </g>
    </svg>
);

export const alarmMaskButtonIcon = () => (
    <svg
        xmlns="http://www.w3.org/2000/svg"
        fill="none"
        version="1.1"
        width="16.2998046875"
        height="16"
        viewBox="0 0 16.2998046875 16"
    >
        <g>
            <g />
            <g>
                <path
                    d="M12.68240234375,14.733L12.68240234375,9.39997C12.68240234375,6.88014,10.68250234375,4.60029,8.149372343749999,4.60029C5.61620234375,4.60029,3.61634234375,6.88014,3.61634234375,9.39997L3.61634234375,14.733L2.54974234375,14.733C2.23856234375,14.733,2.01644234375,14.9569,2.01644234375,15.2663C2.01644234375,15.5756,2.23856234375,15.7996,2.54974234375,15.7996L13.74900234375,15.8137C14.06020234375,15.8137,14.28230234375,15.5332,14.28230234375,15.2663C14.28230234375,14.9993,14.10440234375,14.733,13.74900234375,14.733L12.68240234375,14.733ZM4.68293234375,14.733L4.68293234375,9.66662C4.68293234375,8.69416,4.99411234375,7.76782,5.66073234375,7.0604C6.28283234375,6.35324,7.17157234375,5.66689,8.149372343749999,5.66689C9.08264234375,5.66689,9.97138234375,6.35324,10.63800234375,7.0604C11.30460234375,7.72355,11.61580234375,8.738150000000001,11.61580234375,9.66662L11.61580234375,14.733L4.68293234375,14.733ZM8.32722234375,10.42017L9.08264234375,9.71302C9.21596234375,9.58023,9.21596234375,9.4477,9.21596234375,9.27092C9.21596234375,9.13813,9.17143234375,8.96161,9.08264234375,8.82881C8.86182234375,8.60772,8.50351234375,8.60772,8.28269234375,8.82881L6.50494234375,10.46444C6.28283234375,10.68576,6.28283234375,11.0391,6.50494234375,11.2161C6.63827234375,11.3486,6.86065234375,11.4372,7.17157234375,11.4372L8.41602234375,11.4372L7.39395234375,12.5422C7.26063234375,12.675,7.26063234375,12.8075,7.26063234375,12.9843C7.26063234375,13.1171,7.30489234375,13.2936,7.39395234375,13.4264C7.52728234375,13.5589,7.70486234375,13.5589,7.83819234375,13.5589C8.01604234375,13.5589,8.149372343749999,13.5149,8.28269234375,13.4264L10.06044234375,11.7465C10.28260234375,11.5697,10.37140234375,11.3044,10.37140234375,11.0391C10.32710234375,10.72975,10.10470234375,10.42044,9.66047234375,10.42044L8.32722234375,10.42044L8.32722234375,10.42017ZM2.80999234375,10.19992L0.81652434375,10.19992C0.50534534375,10.19992,0.14990234375,9.89061,0.14990234375,9.53677C0.14990234375,9.18319,0.50534534375,8.866679999999999,0.81652434375,8.866679999999999L2.81746234375,8.866679999999999L2.80999234375,10.19992ZM13.49010234375,10.19992L15.48330234375,10.19992C15.79450234375,10.19992,16.14990234375,9.89061,16.14990234375,9.53677C16.14990234375,9.18319,15.79450234375,8.866679999999999,15.48330234375,8.866679999999999L13.48230234375,8.866679999999999L13.49010234375,10.19992ZM3.54967234375,6.14286L2.1401723437499998,4.73308C1.92018234375,4.5131,1.88765234375,4.043,2.13750234375,3.79288C2.38788234375,3.54276,2.86279234375,3.5705,3.08304234375,3.79021L4.49788234375,5.20532L3.54967234375,6.14286ZM7.48301234375,3.66009L7.48301234375,1.666622C7.48301234375,1.355443,7.79233234375,0.9999999403993,8.146172343749999,1C8.49974234375,1,8.81626234375,1.355443,8.81626234375,1.666622L8.81626234375,3.66756L7.48301234375,3.66009ZM13.01620234375,6.14259L14.42580234375,4.73308C14.64570234375,4.5131,14.67850234375,4.043,14.42840234375,3.79288C14.17830234375,3.54276,13.70310234375,3.5705,13.48310234375,3.79021L12.06800234375,5.20532L13.01620234375,6.14286L13.01620234375,6.14259Z"
                    fill="#FFFFFF"
                    fillOpacity="1"
                />
                <path
                    d="M7.33301234375,3.80925L8.81542234375,3.81755L8.96626234375,3.8184L8.96626234375,1.666622Q8.96626234375,1.365762,8.72173234375,1.112442Q8.46839234375,0.85,8.146172343749999,0.85Q7.82368234375,0.85,7.57323234375,1.113222Q7.33301234375,1.365691,7.33301234375,1.666622L7.33301234375,3.80925ZM8.66626234375,3.51671L8.66626234375,1.666622Q8.66626234375,1.486936,8.50588234375,1.320796Q8.34101234375,1.15,8.146172343749999,1.15Q7.95233234375,1.15,7.79057234375,1.320015Q7.63301234375,1.485608,7.63301234375,1.666622L7.63301234375,3.51093L8.66626234375,3.51671ZM3.54907234375,6.3544L4.60334234375,5.31198L4.71060234375,5.20593L3.18898234375,3.68402Q2.97603234375,3.47159,2.62422234375,3.46538Q2.25955234375,3.45895,2.03138234375,3.68687Q1.80363234375,3.91486,1.81269234375,4.27802Q1.82139234375,4.6264400000000006,2.0340923437500003,4.83914L3.54907234375,6.3544ZM11.85530234375,5.20592L13.16620234375,6.50211L13.16620234375,6.20472L14.53180234375,4.83915Q14.74460234375,4.6264,14.75330234375,4.27805Q14.76250234375,3.91479,14.53450234375,3.68681Q14.30660234375,3.45895,13.94180234375,3.46538Q13.58990234375,3.47159,13.37710234375,3.68408L11.85530234375,5.20592ZM13.01550234375,5.93119L14.31970234375,4.62702Q14.44770234375,4.499029999999999,14.45340234375,4.27051Q14.45930234375,4.035909999999999,14.32240234375,3.89895Q14.18460234375,3.76115,13.94710234375,3.76534Q13.71620234375,3.76941,13.58910234375,3.89634L12.28080234375,5.20472L13.01550234375,5.93119ZM4.28515234375,5.20471L2.97710234375,3.89641Q2.84979234375,3.76941,2.61892234375,3.76534Q2.38151234375,3.76115,2.24362234375,3.89889Q2.1067423437499997,4.03592,2.11260234375,4.2705400000000004Q2.11830234375,4.49909,2.24624234375,4.6270299999999995L3.55028234375,5.93131L4.28515234375,5.20471ZM14.26100234375,14.7998Q14.06820234375,14.583,13.74900234375,14.583L12.83240234375,14.583L12.83240234375,9.39997Q12.83240234375,7.42331,11.47610234375,5.95347Q10.08894234375,4.45029,8.149372343749999,4.45029Q6.20978234375,4.45029,4.82268234375,5.95347Q3.46634234375,7.42331,3.46634234375,9.39997L3.46634234375,14.583L2.54974234375,14.583Q2.25444234375,14.583,2.0602023437500003,14.7777Q1.86644234375,14.9719,1.86644234375,15.2663Q1.86644234375,15.5606,2.0602023437500003,15.7548Q2.25444234375,15.9496,2.54974234375,15.9495L13.74880234375,15.9637Q14.04010234375,15.9637,14.24310234375,15.7412Q14.43230234375,15.5339,14.43230234375,15.2663Q14.43230234375,14.9924,14.26100234375,14.7998ZM12.53240234375,14.883L13.74900234375,14.883Q14.13230234375,14.883,14.13230234375,15.2663Q14.13230234375,15.4176,14.02150234375,15.539Q13.90770234375,15.6637,13.74920234375,15.6637L2.54974234375,15.6496Q2.16644234375,15.6495,2.16644234375,15.2663Q2.16644234375,14.883,2.54974234375,14.883L3.76634234375,14.883L3.76634234375,9.39997Q3.76634234375,7.54058,5.04315234375,6.15692Q6.34116234375,4.75029,8.149372343749999,4.75029Q9.95758234375,4.7503,11.25560234375,6.15692Q12.53240234375,7.54057,12.53240234375,9.39997L12.53240234375,14.883ZM4.53293234375,14.883L11.76580234375,14.883L11.76580234375,9.66662Q11.76580234375,7.97217,10.74550234375,6.95576Q9.38835234375,5.51689,8.149372343749999,5.51689Q6.81968234375,5.51688,5.54980234375,6.9594Q4.53293234375,8.039909999999999,4.53293234375,9.66662L4.53293234375,14.883ZM11.46580234375,14.583L11.46580234375,9.66662Q11.46580234375,8.095469999999999,10.53220234375,7.16674L10.53050234375,7.16504L10.52890234375,7.16329Q9.25962234375,5.81689,8.149372343749999,5.81689Q6.95444234375,5.81689,5.77336234375,7.15947L5.77166234375,7.1614L5.76990234375,7.16327Q4.83293234375,8.15758,4.83293234375,9.66662L4.83293234375,14.583L11.46580234375,14.583ZM8.70667234375,10.27044L9.18685234375,9.82094L9.18849234375,9.8193Q9.36596234375,9.64254,9.36596234375,9.27092Q9.36596234375,8.982669999999999,9.20733234375,8.74544L9.19916234375,8.73322L9.18877234375,8.722809999999999Q8.97921234375,8.51299,8.68267234375,8.51299Q8.38778234375,8.51299,8.17891234375,8.720469999999999L6.40118234375,10.35607L6.39907234375,10.35818Q6.18836234375,10.56814,6.18836234375,10.85688Q6.18836234375,11.1512,6.40500234375,11.3282Q6.67095234375,11.5872,7.17157234375,11.5872L8.07295234375,11.5872L7.28572234375,12.4383Q7.11063234375,12.6151,7.11063234375,12.9843Q7.11063234375,13.2732,7.26937234375,13.5099L7.27766234375,13.5223L7.28821234375,13.5328Q7.46541234375,13.7089,7.83819234375,13.7089Q8.128382343750001,13.7089,8.36567234375,13.5513L8.37637234375,13.5442L10.15900234375,11.8597Q10.52140234375,11.5676,10.52140234375,11.0391L10.52140234375,11.0284L10.51980234375,11.0178Q10.41290234375,10.27044,9.66047234375,10.27044L8.70667234375,10.27044ZM2.95915234375,10.34992L2.96745234375,8.867519999999999L2.96830234375,8.71668L0.81652434375,8.71668Q0.5156653437500001,8.71668,0.26234434375,8.961210000000001Q-0.00009765624999999445,9.21455,-0.00009765624999999445,9.53677Q-0.00009765624999999445,9.85926,0.26312434375,10.10971Q0.5155923437500001,10.34992,0.81652434375,10.34992L2.95915234375,10.34992ZM15.48330234375,8.71668L13.33150234375,8.71668L13.33230234375,8.86755L13.34090234375,10.34992L15.48330234375,10.34992Q15.78420234375,10.34992,16.03670234375,10.1097Q16.29990234375,9.85925,16.29990234375,9.53677Q16.29990234375,9.21454,16.03750234375,8.961210000000001Q15.78410234375,8.71668,15.48330234375,8.71668ZM8.17746234375,10.3549L8.17722234375,10.4199L8.17669234375,10.57044L9.66047234375,10.57044Q10.14745234375,10.57044,10.22130234375,11.0498Q10.21780234375,11.4295,9.96703234375,11.6291L9.96205234375,11.6331L8.18948234375,13.3081Q8.03155234375,13.4089,7.83819234375,13.4089Q7.60228234375,13.4089,7.50957234375,13.3291Q7.41063234375,13.1736,7.41063234375,12.9843Q7.41063234375,12.7373,7.49981234375,12.6484L7.50198234375,12.6463L8.75909234375,11.2872L7.17157234375,11.2872Q6.78920234375,11.2872,6.61069234375,11.1097L6.60486234375,11.1039L6.59844234375,11.0988Q6.48836234375,11.0111,6.48836234375,10.85688Q6.48836234375,10.69405,6.60885234375,10.57267L8.38659234375,8.937059999999999L8.38882234375,8.934809999999999Q8.51049234375,8.81299,8.68267234375,8.81299Q8.84757234375,8.81299,8.96614234375,8.92475Q9.06596234375,9.08136,9.06596234375,9.27092Q9.06596234375,9.51597,8.97819234375,9.60533L8.17746234375,10.3549ZM13.63320234375,9.01668L15.48330234375,9.01667Q15.66300234375,9.01667,15.82910234375,9.17705Q15.99990234375,9.34192,15.99990234375,9.53677Q15.99990234375,9.7306,15.82990234375,9.89237Q15.66430234375,10.04992,15.48330234375,10.04992L13.63920234375,10.04992L13.63320234375,9.01668ZM2.66661234375,9.01668L0.81652534375,9.01667Q0.63683934375,9.01667,0.47069834375,9.17705Q0.29990234375,9.34192,0.29990234375,9.53677Q0.29990234375,9.7306,0.46991834375,9.89237Q0.63551134375,10.04992,0.81652434375,10.04992L2.66083234375,10.04992L2.66661234375,9.01668Z"
                    fillRule="evenodd"
                    fill="#FFFFFF"
                    fillOpacity="1"
                />
            </g>
        </g>
    </svg>
);

export const UpOneLevelIcon = () => (
    <svg xmlns="http://www.w3.org/2000/svg" version="1.1" width="24" height="24" viewBox="0 0 24 24">
        <g transform="matrix(1,0,0,-1,0,48)">
            <g />
            <g>
                <g transform="matrix(1,0,0,-1,0,91.728515625)">
                    <path
                        d="M12.06776,46.7572348125C11.596260000000001,46.7572348125,11.21402,47.1394578125,11.21402,47.6109778125L11.21402,59.874657812500004C11.21402,60.346157812499996,11.59624,60.7283578125,12.06776,60.7283578125C12.53925,60.7283578125,12.9215,60.346157812499996,12.9215,59.874657812500004L12.92149,47.6109578125C12.921479999999999,47.1394578125,12.53925,46.7572348125,12.06776,46.7572348125ZM12.673639999999999,46.1273828125C12.34024,45.7939820125,11.81881,45.7748480125,11.50899,46.0846718125L5.220403,52.3732378125C4.9105997,52.683037812500004,4.9297129,53.2044678125,5.263115,53.5378678125C5.596516,53.8712678125,6.11794,53.8903978125,6.42775,53.580577812499996L12.71635,47.2920178125C13.02616,46.9822178125,13.00704,46.4607848125,12.673639999999999,46.1273828125ZM11.462,46.1273828125C11.128599999999999,46.4607848125,11.10946,46.9822078125,11.41928,47.2920178125L17.707900000000002,53.5805978125C18.017699999999998,53.8903978125,18.539099999999998,53.8712878125,18.872500000000002,53.5378878125C19.2059,53.2044878125,19.225,52.6830578125,18.9152,52.3732578125L12.626629999999999,46.0846508125C12.31682,45.7748480125,11.7954,45.7939820125,11.462,46.1273828125Z"
                        fillOpacity="1"
                    />
                    <path
                        d="M12.74672,45.9224698125Q12.78157,45.9524722125,12.815059999999999,45.9859618125Q12.84651,46.0174068125,12.874880000000001,46.0500558125L19.0566,52.2318278125Q19.3506,52.5258178125,19.334899999999998,52.9540578125Q19.319499999999998,53.3736878125,19.0139,53.6793078125Q18.7083,53.984927812500004,18.2887,54.0003078125Q17.8604,54.016017812499996,17.5664,53.7220178125L13.12149,49.2770678125L13.1215,59.874657812500004Q13.1215,60.3111578125,12.81286,60.6197578125Q12.50422,60.928357812499996,12.06776,60.928357812499996Q11.63128,60.928357812499996,11.32265,60.6197578125Q11.01402,60.311057812499996,11.01402,59.874657812500004L11.01402,49.2771778125L6.56917,53.7219978125Q6.27518,54.0160078125,5.846949,54.0002978125Q5.427312,53.9849078125,5.121694,53.6792878125Q4.816079,53.3736778125,4.800691,52.9540278125Q4.784989,52.5258078125,5.0789818,52.2318178125L11.26077,46.0500478125Q11.28914,46.0174008125,11.32058,45.9859618125Q11.35407,45.9524704125,11.38893,45.9224619125Q11.66928,45.6571268125,12.06774,45.6643468125Q12.46632,45.6570688125,12.74672,45.9224698125ZM11.00503,46.8714678125L5.361824,52.5146578125Q5.191109,52.6853678125,5.200423,52.9393778125Q5.210051,53.2019578125,5.404536,53.3964478125Q5.599025,53.5909378125,5.861611,53.6005678125Q6.11561,53.6098878125,6.28632,53.4391578125L11.01402,48.7114978125L11.01402,47.6109778125Q11.01402,47.3826478125,11.098469999999999,47.1893078125Q11.02376,47.0425178125,11.00503,46.8714678125ZM11.415099999999999,47.5706778125Q11.41402,47.5905978125,11.41402,47.6109778125L11.41402,48.3114978125L11.784970000000001,47.9405478125L11.415099999999999,47.5706778125ZM12.067820000000001,47.6577078125L11.58267,47.1725578125Q11.593720000000001,47.1604878125,11.6055,47.1487078125Q11.79698,46.9572378125,12.06776,46.9572378125Q12.33853,46.9572378125,12.53001,47.1487078125Q12.541820000000001,47.1605178125,12.552900000000001,47.1726278125L12.067820000000001,47.6577078125ZM12.067820000000001,48.2233878125L11.41402,48.8771778125L11.41402,59.874657812500004Q11.41402,60.1454578125,11.60549,60.3368578125Q11.79697,60.5283578125,12.06776,60.5283578125Q12.33854,60.5283578125,12.53002,60.3368578125Q12.721499999999999,60.1454578125,12.721499999999999,59.874657812500004L12.72149,48.8770678125L12.067820000000001,48.2233878125ZM12.72149,48.3113778125L12.350660000000001,47.9405478125L12.72042,47.5707978125Q12.72149,47.5906578125,12.72149,47.6109578125L12.72149,48.3113778125ZM13.12149,48.7113778125L17.8493,53.4391778125Q18.02,53.6098978125,18.274,53.6005778125Q18.5366,53.5909478125,18.731099999999998,53.3964678125Q18.9256,53.2019778125,18.935200000000002,52.9393878125Q18.944499999999998,52.6853978125,18.7738,52.5146878125L13.13059,46.8714578125Q13.11186,47.0425978125,13.03709,47.1894378125Q13.12149,47.3827178125,13.12149,47.6109578125L13.12149,48.7113778125ZM12.73544,46.7949798125Q12.45083,46.5572348125,12.06776,46.5572348125Q11.684750000000001,46.5572348125,11.40019,46.7948858125Q11.398,46.7611888125,11.3993,46.7258798125Q11.40768,46.4974408125,11.555959999999999,46.3205398125L11.65041,46.2260928125Q11.65295,46.2235538125,11.65552,46.2210438125Q11.83229,46.0730548125,12.060500000000001,46.0646808125Q12.06414,46.0645478125,12.06777,46.0644508125L12.075140000000001,46.0646838125Q12.30335,46.0730538125,12.48012,46.2210408125Q12.482669999999999,46.2235298125,12.48521,46.2260728125L12.57968,46.3205488125Q12.72796,46.4974428125,12.736329999999999,46.7258778125Q12.73763,46.7612368125,12.73544,46.7949798125Z"
                        fillRule="evenodd"
                        fillOpacity="1"
                    />
                </g>
                <g>
                    <path
                        d="M18.986725,27.853739C18.986725,28.32523,18.604525000000002,28.70748,18.133025,28.70748L5.869363,28.70748C5.397869,28.70748,5.015625,28.32525,5.015625,27.853738C5.015625,27.382244,5.397849,27,5.869363,27L18.133025,27C18.604525000000002,27.000000153392,18.986725,27.382224,18.986725,27.853739Z"
                        fillOpacity="1"
                    />
                    <path
                        d="M18.878125,27.10863Q18.569525,26.8,18.133025,26.8L5.869363,26.8Q5.432887,26.8,5.124254,27.108639Q4.815625,27.417272,4.815625,27.853739Q4.815625,28.290210000000002,5.124264,28.59885Q5.432898,28.90748,5.869363,28.90748L18.133025,28.90748Q18.569525,28.90748,18.878125,28.59884Q19.186725,28.2902,19.186725,27.853739Q19.186725,27.41726,18.878125,27.10863ZM5.869363,27.2L18.133025,27.2Q18.403824999999998,27.2,18.595325000000003,27.391473Q18.786725,27.582946,18.786725,27.853739Q18.786725,28.12452,18.595325000000003,28.316Q18.403824999999998,28.50748,18.133025,28.50748L5.869363,28.50748Q5.598581,28.50748,5.407104,28.316Q5.215625,28.12453,5.215625,27.853739Q5.215625,27.582956,5.407099,27.391479Q5.598575,27.2,5.869363,27.2Z"
                        fillRule="evenodd"
                        fillOpacity="1"
                    />
                </g>
            </g>
        </g>
    </svg>
);
export const UpOneLevelDisabledIcon = () => (
    <svg xmlns="http://www.w3.org/2000/svg" fill="none" version="1.1" width="24" height="24" viewBox="0 0 24 24">
        <g transform="matrix(1,0,0,-1,0,48)">
            <g />
            <g>
                <g transform="matrix(1,0,0,-1,0,91.728515625)">
                    <path
                        d="M12.06776,46.7572348125C11.596260000000001,46.7572348125,11.21402,47.1394578125,11.21402,47.6109778125L11.21402,59.874657812500004C11.21402,60.346157812499996,11.59624,60.7283578125,12.06776,60.7283578125C12.53925,60.7283578125,12.9215,60.346157812499996,12.9215,59.874657812500004L12.92149,47.6109578125C12.921479999999999,47.1394578125,12.53925,46.7572348125,12.06776,46.7572348125ZM12.673639999999999,46.1273828125C12.34024,45.7939820125,11.81881,45.7748480125,11.50899,46.0846718125L5.220403,52.3732378125C4.9105997,52.683037812500004,4.9297129,53.2044678125,5.263115,53.5378678125C5.596516,53.8712678125,6.11794,53.8903978125,6.42775,53.580577812499996L12.71635,47.2920178125C13.02616,46.9822178125,13.00704,46.4607848125,12.673639999999999,46.1273828125ZM11.462,46.1273828125C11.128599999999999,46.4607848125,11.10946,46.9822078125,11.41928,47.2920178125L17.707900000000002,53.5805978125C18.017699999999998,53.8903978125,18.539099999999998,53.8712878125,18.872500000000002,53.5378878125C19.2059,53.2044878125,19.225,52.6830578125,18.9152,52.3732578125L12.626629999999999,46.0846508125C12.31682,45.7748480125,11.7954,45.7939820125,11.462,46.1273828125Z"
                        fill="#B3BBC8"
                        fillOpacity="1"
                    />
                    <path
                        d="M12.74672,45.9224698125Q12.78157,45.9524722125,12.815059999999999,45.9859618125Q12.84651,46.0174068125,12.874880000000001,46.0500558125L19.0566,52.2318278125Q19.3506,52.5258178125,19.334899999999998,52.9540578125Q19.319499999999998,53.3736878125,19.0139,53.6793078125Q18.7083,53.984927812500004,18.2887,54.0003078125Q17.8604,54.016017812499996,17.5664,53.7220178125L13.12149,49.2770678125L13.1215,59.874657812500004Q13.1215,60.3111578125,12.81286,60.6197578125Q12.50422,60.928357812499996,12.06776,60.928357812499996Q11.63128,60.928357812499996,11.32265,60.6197578125Q11.01402,60.311057812499996,11.01402,59.874657812500004L11.01402,49.2771778125L6.56917,53.7219978125Q6.27518,54.0160078125,5.846949,54.0002978125Q5.427312,53.9849078125,5.121694,53.6792878125Q4.816079,53.3736778125,4.800691,52.9540278125Q4.784989,52.5258078125,5.0789818,52.2318178125L11.26077,46.0500478125Q11.28914,46.0174008125,11.32058,45.9859618125Q11.35407,45.9524704125,11.38893,45.9224619125Q11.66928,45.6571268125,12.06774,45.6643468125Q12.46632,45.6570688125,12.74672,45.9224698125ZM11.00503,46.8714678125L5.361824,52.5146578125Q5.191109,52.6853678125,5.200423,52.9393778125Q5.210051,53.2019578125,5.404536,53.3964478125Q5.599025,53.5909378125,5.861611,53.6005678125Q6.11561,53.6098878125,6.28632,53.4391578125L11.01402,48.7114978125L11.01402,47.6109778125Q11.01402,47.3826478125,11.098469999999999,47.1893078125Q11.02376,47.0425178125,11.00503,46.8714678125ZM11.415099999999999,47.5706778125Q11.41402,47.5905978125,11.41402,47.6109778125L11.41402,48.3114978125L11.784970000000001,47.9405478125L11.415099999999999,47.5706778125ZM12.067820000000001,47.6577078125L11.58267,47.1725578125Q11.593720000000001,47.1604878125,11.6055,47.1487078125Q11.79698,46.9572378125,12.06776,46.9572378125Q12.33853,46.9572378125,12.53001,47.1487078125Q12.541820000000001,47.1605178125,12.552900000000001,47.1726278125L12.067820000000001,47.6577078125ZM12.067820000000001,48.2233878125L11.41402,48.8771778125L11.41402,59.874657812500004Q11.41402,60.1454578125,11.60549,60.3368578125Q11.79697,60.5283578125,12.06776,60.5283578125Q12.33854,60.5283578125,12.53002,60.3368578125Q12.721499999999999,60.1454578125,12.721499999999999,59.874657812500004L12.72149,48.8770678125L12.067820000000001,48.2233878125ZM12.72149,48.3113778125L12.350660000000001,47.9405478125L12.72042,47.5707978125Q12.72149,47.5906578125,12.72149,47.6109578125L12.72149,48.3113778125ZM13.12149,48.7113778125L17.8493,53.4391778125Q18.02,53.6098978125,18.274,53.6005778125Q18.5366,53.5909478125,18.731099999999998,53.3964678125Q18.9256,53.2019778125,18.935200000000002,52.9393878125Q18.944499999999998,52.6853978125,18.7738,52.5146878125L13.13059,46.8714578125Q13.11186,47.0425978125,13.03709,47.1894378125Q13.12149,47.3827178125,13.12149,47.6109578125L13.12149,48.7113778125ZM12.73544,46.7949798125Q12.45083,46.5572348125,12.06776,46.5572348125Q11.684750000000001,46.5572348125,11.40019,46.7948858125Q11.398,46.7611888125,11.3993,46.7258798125Q11.40768,46.4974408125,11.555959999999999,46.3205398125L11.65041,46.2260928125Q11.65295,46.2235538125,11.65552,46.2210438125Q11.83229,46.0730548125,12.060500000000001,46.0646808125Q12.06414,46.0645478125,12.06777,46.0644508125L12.075140000000001,46.0646838125Q12.30335,46.0730538125,12.48012,46.2210408125Q12.482669999999999,46.2235298125,12.48521,46.2260728125L12.57968,46.3205488125Q12.72796,46.4974428125,12.736329999999999,46.7258778125Q12.73763,46.7612368125,12.73544,46.7949798125Z"
                        fillRule="evenodd"
                        fill="#B3BBC8"
                        fillOpacity="1"
                    />
                </g>
                <g>
                    <path
                        d="M18.986725,27.853739C18.986725,28.32523,18.604525000000002,28.70748,18.133025,28.70748L5.869363,28.70748C5.397869,28.70748,5.015625,28.32525,5.015625,27.853738C5.015625,27.382244,5.397849,27,5.869363,27L18.133025,27C18.604525000000002,27.000000153392,18.986725,27.382224,18.986725,27.853739Z"
                        fill="#B3BBC8"
                        fillOpacity="1"
                    />
                    <path
                        d="M18.878125,27.10863Q18.569525,26.8,18.133025,26.8L5.869363,26.8Q5.432887,26.8,5.124254,27.108639Q4.815625,27.417272,4.815625,27.853739Q4.815625,28.290210000000002,5.124264,28.59885Q5.432898,28.90748,5.869363,28.90748L18.133025,28.90748Q18.569525,28.90748,18.878125,28.59884Q19.186725,28.2902,19.186725,27.853739Q19.186725,27.41726,18.878125,27.10863ZM5.869363,27.2L18.133025,27.2Q18.403824999999998,27.2,18.595325000000003,27.391473Q18.786725,27.582946,18.786725,27.853739Q18.786725,28.12452,18.595325000000003,28.316Q18.403824999999998,28.50748,18.133025,28.50748L5.869363,28.50748Q5.598581,28.50748,5.407104,28.316Q5.215625,28.12453,5.215625,27.853739Q5.215625,27.582956,5.407099,27.391479Q5.598575,27.2,5.869363,27.2Z"
                        fillRule="evenodd"
                        fill="#B3BBC8"
                        fillOpacity="1"
                    />
                </g>
            </g>
        </g>
    </svg>
);

export const rootTopologyIcon = () => (
    <svg xmlns="http://www.w3.org/2000/svg" version="1.1" width="24" height="24" viewBox="0 0 24 24">
        <g>
            <g />
            <g>
                <path
                    d="M18.4848,4.853494859375C18.4848,5.3249858593749995,18.102600000000002,5.707235859375,17.6311,5.707235859375L5.367435,5.707235859375C4.89594,5.707235859375,4.5136961,5.3250058593750005,4.5136961,4.853493859375C4.5136961,4.381999859375,4.89592,3.999755859375,5.367435,3.999755859375L17.6311,3.999755859375C18.102600000000002,3.999756012767,18.4848,4.381979859375,18.4848,4.853494859375ZM11.56776,8.028615859375C11.096260000000001,8.028615859375,10.71402,8.410835859375,10.71402,8.882355859375L10.71402,21.146055859375C10.71402,21.617555859375,11.09624,21.999755859375,11.56776,21.999755859375C12.03925,21.999755859375,12.4215,21.617555859375,12.4215,21.146055859375L12.42149,8.882335859375C12.421479999999999,8.410835859375,12.03925,8.028615859375,11.56776,8.028615859375ZM12.173639999999999,7.3987658593750005C11.84024,7.0653658593749995,11.31881,7.046225859374999,11.00899,7.356055859375L4.720403,13.644615859375C4.4105997,13.954415859375,4.429713,14.475855859375,4.763115,14.809255859375C5.096516,15.142655859375,5.61794,15.161755859375,5.92775,14.851955859375L12.21635,8.563395859375C12.52616,8.253595859375,12.50704,7.732165859375,12.173639999999999,7.3987658593750005ZM10.962,7.3987658593750005C10.628599999999999,7.732165859375,10.60946,8.253595859375,10.91928,8.563395859375L17.207900000000002,14.851955859375C17.517699999999998,15.161755859375,18.039099999999998,15.142655859375,18.372500000000002,14.809255859375C18.7059,14.475855859375,18.725,13.954445859375,18.4152,13.644635859375L12.126629999999999,7.356035859375C11.81682,7.046225859374999,11.2954,7.0653658593749995,10.962,7.3987658593750005Z"
                    fillOpacity="1"
                />
                <path
                    d="M18.3762,4.108385859375Q18.0676,3.799755859375,17.6311,3.799755859375L5.367435,3.799755859375Q4.930958,3.799755859375,4.622325,4.108394859375Q4.313696,4.417027859375,4.313696,4.853494859375Q4.313696,5.289965859375,4.622335,5.5986058593750005Q4.930969,5.907235859375,5.367435,5.907235859375L17.6311,5.907235859375Q18.0676,5.907235859375,18.3762,5.598595859375Q18.6848,5.2899558593750005,18.6848,4.853494859375Q18.6848,4.417015859375,18.3762,4.108385859375ZM5.367435,4.199755859375L17.6311,4.199755859375Q17.901899999999998,4.199755859375,18.093400000000003,4.391228859375Q18.2848,4.582701859375,18.2848,4.853494859375Q18.2848,5.124275859375,18.093400000000003,5.315755859375Q17.901899999999998,5.507235859375,17.6311,5.507235859375L5.367435,5.507235859375Q5.096652,5.507235859375,4.905175,5.315755859375Q4.713696,5.124285859375,4.713696,4.853494859375Q4.713696,4.582711859375,4.90517,4.391234859375Q5.096646,4.199755859375,5.367435,4.199755859375ZM12.24672,7.193855859375001Q12.28157,7.223855859375,12.315059999999999,7.257345859375Q12.34651,7.288785859375,12.374880000000001,7.321435859375001L18.5566,13.503215859375Q18.8506,13.797205859375,18.834899999999998,14.225455859375Q18.819499999999998,14.645055859375,18.5139,14.950655859375Q18.2083,15.256255859375,17.7887,15.271655859375Q17.3604,15.287355859375,17.0664,14.993355859375L12.62149,10.548445859375L12.6215,21.146055859375Q12.6215,21.582455859375,12.31286,21.891155859375Q12.00422,22.199755859375,11.56776,22.199755859375Q11.13128,22.199755859375,10.82265,21.891155859375Q10.51402,21.582455859375,10.51402,21.146055859375L10.51402,10.548555859375L6.06917,14.993355859375Q5.77518,15.287355859375,5.346949,15.271655859375Q4.9273109999999996,15.256255859375,4.621694,14.950655859375Q4.316079,14.645055859375,4.300691,14.225455859375Q4.284989,13.797185859375,4.5789819,13.503195859375L10.76077,7.321425859375Q10.78914,7.288785859375,10.82058,7.257345859375Q10.85407,7.223855859375,10.88893,7.193845859375Q11.169270000000001,6.928505859375,11.56774,6.935725859375Q11.96632,6.928445859375,12.24672,7.193855859375001ZM10.50503,8.142845859375L4.861824,13.786035859375Q4.691109,13.956755859375,4.700423,14.210755859375Q4.710051,14.473355859375,4.904536,14.667855859375Q5.099025,14.862355859375,5.361611,14.871955859375Q5.61561,14.881255859375,5.78632,14.710555859375L10.51402,9.982875859375L10.51402,8.882355859375Q10.51402,8.654025859375,10.59848,8.460685859375001Q10.52376,8.313905859375,10.50503,8.142845859375ZM10.915099999999999,8.842055859375Q10.91402,8.861985859375,10.91402,8.882355859375L10.91402,9.582875859375001L11.284970000000001,9.211925859375L10.915099999999999,8.842055859375ZM11.567820000000001,8.929085859375L11.08267,8.443945859374999Q11.093720000000001,8.431875859375001,11.1055,8.420085859375Q11.29698,8.228615859375001,11.56776,8.228615859375001Q11.83853,8.228615859375001,12.03001,8.420085859375Q12.041820000000001,8.431895859375,12.052900000000001,8.444005859375L11.567820000000001,8.929085859375ZM11.567820000000001,9.494775859375L10.91402,10.148565859375001L10.91402,21.146055859375Q10.91402,21.416755859375,11.10549,21.608255859375Q11.29697,21.799755859375,11.56776,21.799755859375Q11.83854,21.799755859375,12.03002,21.608255859375Q12.221499999999999,21.416855859375,12.221499999999999,21.146055859375L12.22149,10.148445859375L11.567820000000001,9.494775859375ZM12.22149,9.582765859375L11.850660000000001,9.211925859375L12.22042,8.842175859375Q12.22149,8.862035859375,12.22149,8.882335859375L12.22149,9.582765859375ZM12.62149,9.982765859375L17.3493,14.710555859375Q17.52,14.881255859375,17.774,14.871955859375Q18.0366,14.862355859375,18.231099999999998,14.667855859375Q18.4256,14.473355859375,18.435200000000002,14.210755859375Q18.444499999999998,13.956775859375,18.2738,13.786065859375L12.63059,8.142845859375Q12.61186,8.313975859374999,12.53709,8.460825859375Q12.62149,8.654095859375001,12.62149,8.882335859375L12.62149,9.982765859375ZM12.23544,8.066365859375Q11.95083,7.828615859375001,11.56776,7.828615859375001Q11.184750000000001,7.828615859375001,10.90019,8.066265859375001Q10.898,8.032565859375,10.8993,7.997265859375Q10.90768,7.768825859375,11.055959999999999,7.591915859375L11.15041,7.497475859375Q11.15296,7.494925859375,11.15552,7.492425859375Q11.33229,7.344435859375,11.560500000000001,7.336065859375Q11.564129999999999,7.335925859375,11.56777,7.335835859375L11.575140000000001,7.336065859375Q11.80335,7.344435859375,11.98012,7.492425859375Q11.982669999999999,7.494915859375,11.98521,7.497455859375L12.07968,7.591925859374999Q12.22796,7.768825859375,12.236329999999999,7.9972558593750005Q12.23763,8.032615859375,12.23544,8.066365859375Z"
                    fillRule="evenodd"
                    fillOpacity="1"
                />
            </g>
        </g>
    </svg>
);

export const editLocationIcon = () => (
    <svg xmlns="http://www.w3.org/2000/svg" version="1.1" width="24" height="24" viewBox="0 0 24 24">
        <g>
            <g />
            <g>
                <g>
                    <path
                        d="M11.757003610229493,3.0000825238C8.803703610229492,2.9838744,6.398707790229492,5.36919,6.390653610229492,8.32253C6.390653610229492,10.27393,7.990803610229492,12.56683,11.137433610229493,15.3671C11.491473610229491,15.6793,12.022543610229492,15.6793,12.376573610229492,15.3671C15.523213610229492,12.56683,17.123353610229493,10.25442,17.123353610229493,8.32253C17.11535361022949,5.36919,14.710313610229493,2.9838733,11.757003610229493,3.0000825238ZM11.757003610229493,13.2449C9.468993610229493,11.15694,8.342053610229492,9.488489999999999,8.342053610229492,8.32253C8.342053610229492,6.4365000000000006,9.870983610229493,4.90758,11.757003610229493,4.90758C13.643033610229491,4.90758,15.171963610229492,6.4365000000000006,15.171963610229492,8.32253C15.171963610229492,9.503129999999999,14.049903610229492,11.17158,11.757003610229493,13.2449Z"
                        fillOpacity="1"
                    />
                    <path
                        d="M12.442723610229493,15.4421Q17.223353610229495,11.18768,17.223353610229495,8.32253L17.223353610229495,8.32226Q17.21725361022949,6.0657700000000006,15.615073610229492,4.47673Q14.012953610229491,2.887699,11.756463610229492,2.9000845Q9.501083610229493,2.8877,7.898943610229492,4.47673Q6.296808210229492,6.06576,6.290654410229492,8.32226Q6.2906536102294925,11.18768,11.070953610229491,15.4418Q11.365163610229493,15.7013,11.757003610229493,15.7013Q12.148843610229491,15.7013,12.442723610229493,15.4421ZM15.474233610229492,4.61873Q17.017353610229492,6.14922,17.023353610229492,8.32253L17.023353610229492,8.3228Q17.023153610229492,11.09811,12.310433610229492,15.2921Q12.073253610229493,15.5013,11.757003610229493,15.5013Q11.440763610229492,15.5013,11.203913610229492,15.2924Q6.490653510229492,11.09795,6.490653610229492,8.3228Q6.4965806102294925,6.1493,8.039783610229492,4.61873Q9.582993610229492,3.0881526,11.757553610229493,3.100081Q13.931043610229493,3.0881538,15.474233610229492,4.61873ZM11.824083610229494,13.3191Q15.271953610229492,10.20134,15.271963610229491,8.32253Q15.271963610229491,6.86659,14.242453610229493,5.83708Q13.212953610229492,4.80758,11.757003610229493,4.80758Q10.301063610229493,4.80758,9.271563610229492,5.83708Q8.242053610229492,6.86658,8.242053610229492,8.32253Q8.242043610229493,10.172609999999999,11.689603610229492,13.3188L11.756703610229494,13.38L11.824083610229494,13.3191ZM14.101033610229493,5.9785Q15.071953610229492,6.9494299999999996,15.071963610229492,8.32253Q15.071973610229492,10.09464,11.757313610229492,13.1097Q8.442043610229492,10.06682,8.442053610229493,8.32253Q8.442053610229493,6.9494299999999996,9.412983610229492,5.9785Q10.383913610229492,5.00758,11.757003610229493,5.00758Q13.130113610229493,5.00758,14.101033610229493,5.9785Z"
                        fillRule="evenodd"
                        fillOpacity="1"
                    />
                </g>
                <g>
                    <path
                        d="M21.3986,19.06979703125L21.4425,18.98197703125L21.4718,18.89416703125C21.4718,18.86489703125,21.4718,18.83562703125,21.4962,18.801477031250002C21.5013,18.751187031249998,21.5013,18.70052703125,21.4962,18.65023703125L21.4962,12.26915803125C21.4955,11.73081903125,21.0588,11.29480463125,20.5205,11.29480463125C19.9822,11.29480463125,19.5455,11.73082003125,19.5448,12.26915803125L19.5448,17.97212703125L13.7931,20.445537031249998L7.8803,16.80616703125L7.84615,16.80616703125C7.8178,16.78667703125,7.78651,16.77184703125,7.75346,16.76225703125L7.66565,16.72323703125L7.1095,16.72323703125L7.02657,16.75250703125C6.99199,16.76207703125,6.95909,16.77687703125,6.929,16.79640703125L6.89485,16.79640703125L3.9531099999999997,18.42094703125L3.9531099999999997,12.26915803125C3.9531099999999997,11.73029303125,3.51627,11.29345703125,2.977407,11.29345703125C2.438542,11.29345703125,2.00170596,11.73029303125,2.00170596,12.26915803125L2.00170596,20.12354703125C1.999517768,20.15929703125,1.999517768,20.19513703125,2.00170596,20.23087703125C1.999431406,20.26010703125,1.999431406,20.28946703125,2.00170596,20.31868703125L2.030977,20.41138703125C2.0418732,20.444707031249997,2.0549097,20.47730703125,2.070005,20.50895703125C2.0718418,20.523527031249998,2.0718418,20.53827703125,2.070005,20.55285703125C2.107175,20.61891703125,2.153126,20.67963703125,2.206603,20.733367031249998C2.290905,20.805427031249998,2.386502,20.86311703125,2.489556,20.90410703125L2.54322,20.93825703125C2.596467,20.96439703125,2.651987,20.98562703125,2.709089,21.00167703125L2.7676309999999997,21.00167703125C2.8353450000000002,21.02630703125,2.905777,21.04268703125,2.977407,21.05046703125C3.0504100000000003,21.05764703125,3.12394,21.05764703125,3.1969399999999997,21.05046703125L3.25548,21.05046703125C3.30944,21.034407031249998,3.36172,21.01316703125,3.41159,20.98704703125L3.44574,20.98704703125L7.34855,18.82586703125L13.2028,22.416457031249998C13.2467,22.416457031249998,13.2906,22.45055703125,13.3296,22.47015703125C13.3668,22.49045703125,13.406,22.50685703125,13.4467,22.51885703125C13.5244,22.54175703125,13.6047,22.554857031250002,13.6857,22.557957031249998L13.7101,22.557957031249998C13.7841,22.55535703125,13.8576,22.54555703125,13.9296,22.52865703125L13.9979,22.50425703125L14.0955,22.47495703125L20.9254,19.54788703125C20.9785,19.52339703125,21.0291,19.49399703125,21.0767,19.46007703125L21.145,19.406407031249998L21.2181,19.34298703125C21.2407,19.318177031250002,21.2619,19.29211703125,21.2816,19.26493703125L21.3401,19.19175703125L21.3986,19.06979703125Z"
                        fillOpacity="1"
                    />
                    <path
                        d="M21.3611,19.32554703125L21.4253,19.24531703125L21.4302,19.23502703125L21.4888,19.11306703125L21.5352,19.02034703125L21.5718,18.91039703125L21.5718,18.89416703125Q21.5718,18.86766703125,21.5776,18.85959703125L21.593,18.83794703125L21.5957,18.81147703125Q21.6041,18.72842703125,21.5962,18.64538703125L21.5962,12.26915803125Q21.5956,11.82389303125,21.2806,11.50934503125Q20.9656,11.19480473125,20.5205,11.19480473125Q20.0754,11.19480463125,19.7604,11.50934503125Q19.4454,11.82389203125,19.4448,12.26902003125L19.4448,17.90627703125L13.8012,20.33315703125L7.90861,16.70616703125L7.87498,16.70616703125Q7.83393,16.682387031250002,7.78827,16.66829703125L7.68687,16.62323703125L7.09237,16.62323703125L6.99636,16.65712703125Q6.94642,16.67136703125,6.90135,16.69641703125L6.86907,16.69641703125L4.05311,18.25149703125L4.05311,12.26915803125Q4.05311,11.82358803125,3.73804,11.50852203125Q3.42298,11.19345703125,2.977407,11.19345703125Q2.531837,11.19345703125,2.216771,11.50852203125Q1.9017059,11.82358803125,1.9017059,12.26915803125L1.901706,20.12057703125Q1.898528,20.17535703125,1.9014978,20.23014703125Q1.89826,20.27829703125,1.9020074,20.326447031249998L1.9028966,20.337877031250002L1.9356186,20.44149703125Q1.9511363,20.488977031250002,1.9713276,20.533837031250002Q1.9712002,20.53709703125,1.9707895,20.54035703125L1.9666615,20.57311703125L1.9828529,20.601897031249997Q2.0455404,20.71330703125,2.135732,20.803917031250002L2.138568,20.80675703125L2.141623,20.80937703125Q2.27788,20.92585703125,2.443646,20.99342703125L2.494195,21.02559703125L2.499156,21.028027031249998Q2.587393,21.07134703125,2.682022,21.09794703125L2.695298,21.10167703125L2.750488,21.10167703125Q2.855741,21.13783703125,2.966603,21.14987703125Q3.08466,21.16149703125,3.20173,21.150467031250002L3.27004,21.150467031250002L3.284,21.14631703125Q3.36233,21.123007031249998,3.4355,21.087047031250002L3.4715800000000003,21.087047031250002L7.34597,18.941597031249998L13.1745,22.51645703125L13.2028,22.51645703125Q13.2086,22.51645703125,13.2486,22.53955703125Q13.2705,22.55225703125,13.283,22.55865703125Q13.3477,22.59385703125,13.4183,22.61475703125Q13.5475,22.65285703125,13.682,22.65785703125L13.6839,22.65795703125L13.7118,22.65795703125L13.7136,22.65785703125Q13.8346,22.65365703125,13.9525,22.62605703125L13.958,22.62475703125L14.0292,22.599257031249998L14.1297,22.56915703125L20.9661,19.63925703125L20.9673,19.638677031249998Q21.0556,19.59791703125,21.1347,19.54148703125L21.1366,19.540137031249998L21.2086,19.48355703125L21.2881,19.41468703125L21.2921,19.410317031250003Q21.3289,19.36981703125,21.3611,19.32554703125ZM21.2549,19.13819703125L21.2019,19.20436703125L21.2005,19.20634703125Q21.176,19.24025703125,21.148,19.27140703125L21.0813,19.329267031249998L21.0168,19.37996703125Q20.9543,19.42428703125,20.8847,19.45653703125L14.0613,22.380857031250002L13.9667,22.40925703125L13.9014,22.43255703125Q13.8061,22.454357031249998,13.7084,22.45795703125L13.6876,22.45795703125Q13.5791,22.45365703125,13.4751,22.42305703125Q13.4242,22.40795703125,13.3777,22.38245703125L13.376,22.38155703125L13.3743,22.38065703125Q13.367,22.37695703125,13.3486,22.36635703125Q13.2856,22.32995703125,13.237,22.320157031249998L7.35112,18.71013703125L3.41991,20.88704703125L3.38699,20.88704703125L3.36519,20.89846703125Q3.3050699999999997,20.929957031249998,3.24047,20.95046703125L3.19204,20.95046703125L3.18715,20.95094703125Q3.08717,20.96077703125,2.98821,20.95104703125Q2.892385,20.94063703125,2.8018039999999997,20.907697031250002L2.785248,20.90167703125L2.723258,20.90167703125Q2.6556610000000003,20.88155703125,2.59212,20.850847031249998L2.535284,20.81467703125L2.526517,20.81118703125Q2.388173,20.75616703125,2.274665,20.65997703125Q2.215377,20.59977703125,2.171376,20.528017031250002Q2.171209,20.512237031250002,2.16922,20.49645703125L2.167205,20.480457031249998L2.160265,20.465907031249998Q2.14038,20.42420703125,2.126335,20.381267031249998L2.100685,20.30003703125Q2.0990151,20.26933703125,2.101405,20.23863703125L2.101944,20.23170703125L2.101519,20.22476703125Q2.0986079,20.177217031250002,2.101519,20.129657031249998L2.101706,20.12660703125L2.101706,12.26915803125Q2.101706,11.90643103125,2.358193,11.64994403125Q2.61468,11.39345703125,2.977407,11.39345703125Q3.3401300000000003,11.39345703125,3.5966199999999997,11.64994403125Q3.85311,11.90643103125,3.85311,12.26915803125L3.85311,18.59040703125L6.92062,16.89640703125L6.9586,16.89640703125L6.98344,16.88029703125Q7.01592,16.85920703125,7.05324,16.84888703125L7.05658,16.84795703125L7.12663,16.82323703125L7.64443,16.82323703125L7.71906,16.85640703125L7.72559,16.85829703125Q7.75999,16.86827703125,7.78949,16.88856703125L7.81509,16.90616703125L7.85199,16.90616703125L13.7849,20.55791703125L19.6448,18.03797703125L19.6448,12.26929503125Q19.6453,11.90692703125,19.9017,11.65086403125Q20.1581,11.39480503125,20.5205,11.39480503125Q20.8829,11.39480503125,21.1393,11.65086403125Q21.3957,11.90692903125,21.3962,12.26915803125L21.3962,18.65525703125L21.3967,18.66024703125Q21.4022,18.71527703125,21.3985,18.770317031250002Q21.3751,18.816297031250002,21.3722,18.87669703125L21.3499,18.94361703125L21.3085,19.026517031250002L21.2549,19.13819703125Z"
                        fillRule="evenodd"
                        fillOpacity="1"
                    />
                </g>
                <g>
                    <path
                        d="M13.22055703125,8.366353421020507Q13.22055703125,8.438253421020509,13.21351703125,8.509803421020507Q13.20646703125,8.581363421020509,13.19243703125,8.651883421020507Q13.17840703125,8.722393421020508,13.15753703125,8.791203421020509Q13.13666703125,8.860003421020508,13.10915703125,8.926433421020509Q13.08163703125,8.992853421020508,13.047747031250001,9.056263421020507Q13.01384703125,9.119673421020508,12.97390703125,9.179463421020507Q12.93395703125,9.239243421020507,12.88834703125,9.294823421020508Q12.84273703125,9.350403421020507,12.79189703125,9.401243421020508Q12.74105703125,9.452083421020507,12.68547703125,9.497693421020507Q12.62989703125,9.543303421020507,12.57011703125,9.583253421020508Q12.51032703125,9.623193421020508,12.446917031249999,9.657093421020509Q12.38350703125,9.690983421020508,12.31708703125,9.718503421020507Q12.25065703125,9.746013421020507,12.18185703125,9.766883421020507Q12.11304703125,9.787753421020508,12.04253703125,9.801783421020508Q11.972017031250001,9.815813421020508,11.90045703125,9.822863421020507Q11.82890703125,9.829903421020507,11.75700703125,9.829903421020507Q11.68510703125,9.829903421020507,11.61355703125,9.822863421020507Q11.54199703125,9.815813421020508,11.47148703125,9.801783421020508Q11.40096703125,9.787753421020508,11.33215703125,9.766883421020507Q11.26335803125,9.746013421020507,11.19693103125,9.718503421020507Q11.13050503125,9.690983421020508,11.06709503125,9.657093421020509Q11.00368503125,9.623193421020508,10.94390303125,9.583253421020508Q10.88412003125,9.543303421020507,10.82854103125,9.497693421020507Q10.77296203125,9.452083421020507,10.72212103125,9.401243421020508Q10.67128103125,9.350403421020507,10.62566803125,9.294823421020508Q10.58005503125,9.239243421020507,10.54011003125,9.179463421020507Q10.50016503125,9.119673421020508,10.46627103125,9.056263421020507Q10.43237803125,8.992853421020508,10.40486303125,8.926433421020509Q10.37734843125,8.860003421020508,10.35647703125,8.791203421020509Q10.33560563125,8.722393421020508,10.32157873125,8.651883421020507Q10.30755183125,8.581363421020509,10.30050443125,8.509803421020507Q10.29345703125,8.438253421020509,10.29345703125,8.366353421020507Q10.29345703125,8.294453421020508,10.30050443125,8.222903421020508Q10.30755183125,8.151343421020508,10.32157873125,8.080833421020508Q10.33560563125,8.010313421020507,10.35647703125,7.941503421020508Q10.37734843125,7.872704421020508,10.40486303125,7.806277421020508Q10.43237803125,7.739851421020508,10.46627103125,7.676441421020508Q10.50016503125,7.613031421020508,10.54011003125,7.5532494210205074Q10.58005503125,7.493466421020508,10.62566803125,7.437887421020508Q10.67128103125,7.382308421020507,10.72212103125,7.331467421020508Q10.77296203125,7.280627421020508,10.82854103125,7.235014421020508Q10.88412003125,7.1894014210205075,10.94390303125,7.149456421020508Q11.00368503125,7.109511421020508,11.06709503125,7.075617421020508Q11.13050503125,7.041724421020508,11.19693103125,7.0142094210205075Q11.26335803125,6.9866948210205075,11.33215703125,6.965823421020508Q11.40096703125,6.944952021020508,11.47148703125,6.930925121020508Q11.54199703125,6.916898221020507,11.61355703125,6.909850821020508Q11.68510703125,6.902803421020508,11.75700703125,6.902803421020508Q11.82890703125,6.902803421020508,11.90045703125,6.909850821020508Q11.972017031250001,6.916898221020507,12.04253703125,6.930925121020508Q12.11304703125,6.944952021020508,12.18185703125,6.965823421020508Q12.25065703125,6.9866948210205075,12.31708703125,7.0142094210205075Q12.38350703125,7.041724421020508,12.446917031249999,7.075617421020508Q12.51032703125,7.109511421020508,12.57011703125,7.149456421020508Q12.62989703125,7.1894014210205075,12.68547703125,7.235014421020508Q12.74105703125,7.280627421020508,12.79189703125,7.331467421020508Q12.84273703125,7.382308421020507,12.88834703125,7.437887421020508Q12.93395703125,7.493466421020508,12.97390703125,7.5532494210205074Q13.01384703125,7.613031421020508,13.047747031250001,7.676441421020508Q13.08163703125,7.739851421020508,13.10915703125,7.806277421020508Q13.13666703125,7.872704421020508,13.15753703125,7.941503421020508Q13.17840703125,8.010313421020507,13.19243703125,8.080833421020508Q13.20646703125,8.151343421020508,13.21351703125,8.222903421020508Q13.22055703125,8.294453421020508,13.22055703125,8.366353421020507Z"
                        fillOpacity="1"
                    />
                    <path
                        d="M12.86260703125,7.260757421020508Q12.40465703125,6.802803521020508,11.75700703125,6.802803521020508Q11.10936403125,6.802803421020508,10.65141103125,7.260757421020508Q10.19345713125,7.718710421020508,10.19345713125,8.366353421020507Q10.19345703125,9.014003421020508,10.65141103125,9.471953421020508Q11.10936403125,9.929903421020509,11.75700703125,9.929903421020509Q12.40465703125,9.929903421020509,12.86260703125,9.471953421020508Q13.32055703125,9.014003421020508,13.32055703125,8.366353421020507Q13.32055703125,7.718710421020508,12.86260703125,7.260757421020508ZM10.79283203125,7.4021784210205075Q11.19220703125,7.0028034210205075,11.75700703125,7.0028034210205075Q12.32180703125,7.0028034210205075,12.72118703125,7.4021784210205075Q13.12055703125,7.8015534210205075,13.12055703125,8.366353421020507Q13.12055703125,8.931153421020507,12.72118703125,9.330533421020508Q12.32180703125,9.729903421020508,11.75700703125,9.729903421020508Q11.19220703125,9.729903421020508,10.79283203125,9.330533421020508Q10.39345703125,8.931153421020507,10.39345703125,8.366353421020507Q10.39345703125,7.8015534210205075,10.79283203125,7.4021784210205075Z"
                        fillRule="evenodd"
                        fillOpacity="1"
                    />
                </g>
            </g>
        </g>
    </svg>
);

export const editLocationDisabledIcon = () => (
    <svg xmlns="http://www.w3.org/2000/svg" fill="none" version="1.1" width="24" height="24" viewBox="0 0 24 24">
        <g>
            <g />
            <g>
                <g>
                    <path
                        d="M11.757003610229493,3.0000825238C8.803703610229492,2.9838744,6.398707790229492,5.36919,6.390653610229492,8.32253C6.390653610229492,10.27393,7.990803610229492,12.56683,11.137433610229493,15.3671C11.491473610229491,15.6793,12.022543610229492,15.6793,12.376573610229492,15.3671C15.523213610229492,12.56683,17.123353610229493,10.25442,17.123353610229493,8.32253C17.11535361022949,5.36919,14.710313610229493,2.9838733,11.757003610229493,3.0000825238ZM11.757003610229493,13.2449C9.468993610229493,11.15694,8.342053610229492,9.488489999999999,8.342053610229492,8.32253C8.342053610229492,6.4365000000000006,9.870983610229493,4.90758,11.757003610229493,4.90758C13.643033610229491,4.90758,15.171963610229492,6.4365000000000006,15.171963610229492,8.32253C15.171963610229492,9.503129999999999,14.049903610229492,11.17158,11.757003610229493,13.2449Z"
                        fill="#B3BBC8"
                        fillOpacity="1"
                    />
                    <path
                        d="M12.442723610229493,15.4421Q17.223353610229495,11.18768,17.223353610229495,8.32253L17.223353610229495,8.32226Q17.21725361022949,6.0657700000000006,15.615073610229492,4.47673Q14.012953610229491,2.887699,11.756463610229492,2.9000845Q9.501083610229493,2.8877,7.898943610229492,4.47673Q6.296808210229492,6.06576,6.290654410229492,8.32226Q6.2906536102294925,11.18768,11.070953610229491,15.4418Q11.365163610229493,15.7013,11.757003610229493,15.7013Q12.148843610229491,15.7013,12.442723610229493,15.4421ZM15.474233610229492,4.61873Q17.017353610229492,6.14922,17.023353610229492,8.32253L17.023353610229492,8.3228Q17.023153610229492,11.09811,12.310433610229492,15.2921Q12.073253610229493,15.5013,11.757003610229493,15.5013Q11.440763610229492,15.5013,11.203913610229492,15.2924Q6.490653510229492,11.09795,6.490653610229492,8.3228Q6.4965806102294925,6.1493,8.039783610229492,4.61873Q9.582993610229492,3.0881526,11.757553610229493,3.100081Q13.931043610229493,3.0881538,15.474233610229492,4.61873ZM11.824083610229494,13.3191Q15.271953610229492,10.20134,15.271963610229491,8.32253Q15.271963610229491,6.86659,14.242453610229493,5.83708Q13.212953610229492,4.80758,11.757003610229493,4.80758Q10.301063610229493,4.80758,9.271563610229492,5.83708Q8.242053610229492,6.86658,8.242053610229492,8.32253Q8.242043610229493,10.172609999999999,11.689603610229492,13.3188L11.756703610229494,13.38L11.824083610229494,13.3191ZM14.101033610229493,5.9785Q15.071953610229492,6.9494299999999996,15.071963610229492,8.32253Q15.071973610229492,10.09464,11.757313610229492,13.1097Q8.442043610229492,10.06682,8.442053610229493,8.32253Q8.442053610229493,6.9494299999999996,9.412983610229492,5.9785Q10.383913610229492,5.00758,11.757003610229493,5.00758Q13.130113610229493,5.00758,14.101033610229493,5.9785Z"
                        fillRule="evenodd"
                        fill="#B3BBC8"
                        fillOpacity="1"
                    />
                </g>
                <g>
                    <path
                        d="M21.3986,19.06979703125L21.4425,18.98197703125L21.4718,18.89416703125C21.4718,18.86489703125,21.4718,18.83562703125,21.4962,18.801477031250002C21.5013,18.751187031249998,21.5013,18.70052703125,21.4962,18.65023703125L21.4962,12.26915803125C21.4955,11.73081903125,21.0588,11.29480463125,20.5205,11.29480463125C19.9822,11.29480463125,19.5455,11.73082003125,19.5448,12.26915803125L19.5448,17.97212703125L13.7931,20.445537031249998L7.8803,16.80616703125L7.84615,16.80616703125C7.8178,16.78667703125,7.78651,16.77184703125,7.75346,16.76225703125L7.66565,16.72323703125L7.1095,16.72323703125L7.02657,16.75250703125C6.99199,16.76207703125,6.95909,16.77687703125,6.929,16.79640703125L6.89485,16.79640703125L3.9531099999999997,18.42094703125L3.9531099999999997,12.26915803125C3.9531099999999997,11.73029303125,3.51627,11.29345703125,2.977407,11.29345703125C2.438542,11.29345703125,2.00170596,11.73029303125,2.00170596,12.26915803125L2.00170596,20.12354703125C1.999517768,20.15929703125,1.999517768,20.19513703125,2.00170596,20.23087703125C1.999431406,20.26010703125,1.999431406,20.28946703125,2.00170596,20.31868703125L2.030977,20.41138703125C2.0418732,20.444707031249997,2.0549097,20.47730703125,2.070005,20.50895703125C2.0718418,20.523527031249998,2.0718418,20.53827703125,2.070005,20.55285703125C2.107175,20.61891703125,2.153126,20.67963703125,2.206603,20.733367031249998C2.290905,20.805427031249998,2.386502,20.86311703125,2.489556,20.90410703125L2.54322,20.93825703125C2.596467,20.96439703125,2.651987,20.98562703125,2.709089,21.00167703125L2.7676309999999997,21.00167703125C2.8353450000000002,21.02630703125,2.905777,21.04268703125,2.977407,21.05046703125C3.0504100000000003,21.05764703125,3.12394,21.05764703125,3.1969399999999997,21.05046703125L3.25548,21.05046703125C3.30944,21.034407031249998,3.36172,21.01316703125,3.41159,20.98704703125L3.44574,20.98704703125L7.34855,18.82586703125L13.2028,22.416457031249998C13.2467,22.416457031249998,13.2906,22.45055703125,13.3296,22.47015703125C13.3668,22.49045703125,13.406,22.50685703125,13.4467,22.51885703125C13.5244,22.54175703125,13.6047,22.554857031250002,13.6857,22.557957031249998L13.7101,22.557957031249998C13.7841,22.55535703125,13.8576,22.54555703125,13.9296,22.52865703125L13.9979,22.50425703125L14.0955,22.47495703125L20.9254,19.54788703125C20.9785,19.52339703125,21.0291,19.49399703125,21.0767,19.46007703125L21.145,19.406407031249998L21.2181,19.34298703125C21.2407,19.318177031250002,21.2619,19.29211703125,21.2816,19.26493703125L21.3401,19.19175703125L21.3986,19.06979703125Z"
                        fill="#B3BBC8"
                        fillOpacity="1"
                    />
                    <path
                        d="M21.3611,19.32554703125L21.4253,19.24531703125L21.4302,19.23502703125L21.4888,19.11306703125L21.5352,19.02034703125L21.5718,18.91039703125L21.5718,18.89416703125Q21.5718,18.86766703125,21.5776,18.85959703125L21.593,18.83794703125L21.5957,18.81147703125Q21.6041,18.72842703125,21.5962,18.64538703125L21.5962,12.26915803125Q21.5956,11.82389303125,21.2806,11.50934503125Q20.9656,11.19480473125,20.5205,11.19480473125Q20.0754,11.19480463125,19.7604,11.50934503125Q19.4454,11.82389203125,19.4448,12.26902003125L19.4448,17.90627703125L13.8012,20.33315703125L7.90861,16.70616703125L7.87498,16.70616703125Q7.83393,16.682387031250002,7.78827,16.66829703125L7.68687,16.62323703125L7.09237,16.62323703125L6.99636,16.65712703125Q6.94642,16.67136703125,6.90135,16.69641703125L6.86907,16.69641703125L4.05311,18.25149703125L4.05311,12.26915803125Q4.05311,11.82358803125,3.73804,11.50852203125Q3.42298,11.19345703125,2.977407,11.19345703125Q2.531837,11.19345703125,2.216771,11.50852203125Q1.9017059,11.82358803125,1.9017059,12.26915803125L1.901706,20.12057703125Q1.898528,20.17535703125,1.9014978,20.23014703125Q1.89826,20.27829703125,1.9020074,20.326447031249998L1.9028966,20.337877031250002L1.9356186,20.44149703125Q1.9511363,20.488977031250002,1.9713276,20.533837031250002Q1.9712002,20.53709703125,1.9707895,20.54035703125L1.9666615,20.57311703125L1.9828529,20.601897031249997Q2.0455404,20.71330703125,2.135732,20.803917031250002L2.138568,20.80675703125L2.141623,20.80937703125Q2.27788,20.92585703125,2.443646,20.99342703125L2.494195,21.02559703125L2.499156,21.028027031249998Q2.587393,21.07134703125,2.682022,21.09794703125L2.695298,21.10167703125L2.750488,21.10167703125Q2.855741,21.13783703125,2.966603,21.14987703125Q3.08466,21.16149703125,3.20173,21.150467031250002L3.27004,21.150467031250002L3.284,21.14631703125Q3.36233,21.123007031249998,3.4355,21.087047031250002L3.4715800000000003,21.087047031250002L7.34597,18.941597031249998L13.1745,22.51645703125L13.2028,22.51645703125Q13.2086,22.51645703125,13.2486,22.53955703125Q13.2705,22.55225703125,13.283,22.55865703125Q13.3477,22.59385703125,13.4183,22.61475703125Q13.5475,22.65285703125,13.682,22.65785703125L13.6839,22.65795703125L13.7118,22.65795703125L13.7136,22.65785703125Q13.8346,22.65365703125,13.9525,22.62605703125L13.958,22.62475703125L14.0292,22.599257031249998L14.1297,22.56915703125L20.9661,19.63925703125L20.9673,19.638677031249998Q21.0556,19.59791703125,21.1347,19.54148703125L21.1366,19.540137031249998L21.2086,19.48355703125L21.2881,19.41468703125L21.2921,19.410317031250003Q21.3289,19.36981703125,21.3611,19.32554703125ZM21.2549,19.13819703125L21.2019,19.20436703125L21.2005,19.20634703125Q21.176,19.24025703125,21.148,19.27140703125L21.0813,19.329267031249998L21.0168,19.37996703125Q20.9543,19.42428703125,20.8847,19.45653703125L14.0613,22.380857031250002L13.9667,22.40925703125L13.9014,22.43255703125Q13.8061,22.454357031249998,13.7084,22.45795703125L13.6876,22.45795703125Q13.5791,22.45365703125,13.4751,22.42305703125Q13.4242,22.40795703125,13.3777,22.38245703125L13.376,22.38155703125L13.3743,22.38065703125Q13.367,22.37695703125,13.3486,22.36635703125Q13.2856,22.32995703125,13.237,22.320157031249998L7.35112,18.71013703125L3.41991,20.88704703125L3.38699,20.88704703125L3.36519,20.89846703125Q3.3050699999999997,20.929957031249998,3.24047,20.95046703125L3.19204,20.95046703125L3.18715,20.95094703125Q3.08717,20.96077703125,2.98821,20.95104703125Q2.892385,20.94063703125,2.8018039999999997,20.907697031250002L2.785248,20.90167703125L2.723258,20.90167703125Q2.6556610000000003,20.88155703125,2.59212,20.850847031249998L2.535284,20.81467703125L2.526517,20.81118703125Q2.388173,20.75616703125,2.274665,20.65997703125Q2.215377,20.59977703125,2.171376,20.528017031250002Q2.171209,20.512237031250002,2.16922,20.49645703125L2.167205,20.480457031249998L2.160265,20.465907031249998Q2.14038,20.42420703125,2.126335,20.381267031249998L2.100685,20.30003703125Q2.0990151,20.26933703125,2.101405,20.23863703125L2.101944,20.23170703125L2.101519,20.22476703125Q2.0986079,20.177217031250002,2.101519,20.129657031249998L2.101706,20.12660703125L2.101706,12.26915803125Q2.101706,11.90643103125,2.358193,11.64994403125Q2.61468,11.39345703125,2.977407,11.39345703125Q3.3401300000000003,11.39345703125,3.5966199999999997,11.64994403125Q3.85311,11.90643103125,3.85311,12.26915803125L3.85311,18.59040703125L6.92062,16.89640703125L6.9586,16.89640703125L6.98344,16.88029703125Q7.01592,16.85920703125,7.05324,16.84888703125L7.05658,16.84795703125L7.12663,16.82323703125L7.64443,16.82323703125L7.71906,16.85640703125L7.72559,16.85829703125Q7.75999,16.86827703125,7.78949,16.88856703125L7.81509,16.90616703125L7.85199,16.90616703125L13.7849,20.55791703125L19.6448,18.03797703125L19.6448,12.26929503125Q19.6453,11.90692703125,19.9017,11.65086403125Q20.1581,11.39480503125,20.5205,11.39480503125Q20.8829,11.39480503125,21.1393,11.65086403125Q21.3957,11.90692903125,21.3962,12.26915803125L21.3962,18.65525703125L21.3967,18.66024703125Q21.4022,18.71527703125,21.3985,18.770317031250002Q21.3751,18.816297031250002,21.3722,18.87669703125L21.3499,18.94361703125L21.3085,19.026517031250002L21.2549,19.13819703125Z"
                        fillRule="evenodd"
                        fill="#B3BBC8"
                        fillOpacity="1"
                    />
                </g>
                <g>
                    <path
                        d="M13.22055703125,8.366353421020507Q13.22055703125,8.438253421020509,13.21351703125,8.509803421020507Q13.20646703125,8.581363421020509,13.19243703125,8.651883421020507Q13.17840703125,8.722393421020508,13.15753703125,8.791203421020509Q13.13666703125,8.860003421020508,13.10915703125,8.926433421020509Q13.08163703125,8.992853421020508,13.047747031250001,9.056263421020507Q13.01384703125,9.119673421020508,12.97390703125,9.179463421020507Q12.93395703125,9.239243421020507,12.88834703125,9.294823421020508Q12.84273703125,9.350403421020507,12.79189703125,9.401243421020508Q12.74105703125,9.452083421020507,12.68547703125,9.497693421020507Q12.62989703125,9.543303421020507,12.57011703125,9.583253421020508Q12.51032703125,9.623193421020508,12.446917031249999,9.657093421020509Q12.38350703125,9.690983421020508,12.31708703125,9.718503421020507Q12.25065703125,9.746013421020507,12.18185703125,9.766883421020507Q12.11304703125,9.787753421020508,12.04253703125,9.801783421020508Q11.972017031250001,9.815813421020508,11.90045703125,9.822863421020507Q11.82890703125,9.829903421020507,11.75700703125,9.829903421020507Q11.68510703125,9.829903421020507,11.61355703125,9.822863421020507Q11.54199703125,9.815813421020508,11.47148703125,9.801783421020508Q11.40096703125,9.787753421020508,11.33215703125,9.766883421020507Q11.26335803125,9.746013421020507,11.19693103125,9.718503421020507Q11.13050503125,9.690983421020508,11.06709503125,9.657093421020509Q11.00368503125,9.623193421020508,10.94390303125,9.583253421020508Q10.88412003125,9.543303421020507,10.82854103125,9.497693421020507Q10.77296203125,9.452083421020507,10.72212103125,9.401243421020508Q10.67128103125,9.350403421020507,10.62566803125,9.294823421020508Q10.58005503125,9.239243421020507,10.54011003125,9.179463421020507Q10.50016503125,9.119673421020508,10.46627103125,9.056263421020507Q10.43237803125,8.992853421020508,10.40486303125,8.926433421020509Q10.37734843125,8.860003421020508,10.35647703125,8.791203421020509Q10.33560563125,8.722393421020508,10.32157873125,8.651883421020507Q10.30755183125,8.581363421020509,10.30050443125,8.509803421020507Q10.29345703125,8.438253421020509,10.29345703125,8.366353421020507Q10.29345703125,8.294453421020508,10.30050443125,8.222903421020508Q10.30755183125,8.151343421020508,10.32157873125,8.080833421020508Q10.33560563125,8.010313421020507,10.35647703125,7.941503421020508Q10.37734843125,7.872704421020508,10.40486303125,7.806277421020508Q10.43237803125,7.739851421020508,10.46627103125,7.676441421020508Q10.50016503125,7.613031421020508,10.54011003125,7.5532494210205074Q10.58005503125,7.493466421020508,10.62566803125,7.437887421020508Q10.67128103125,7.382308421020507,10.72212103125,7.331467421020508Q10.77296203125,7.280627421020508,10.82854103125,7.235014421020508Q10.88412003125,7.1894014210205075,10.94390303125,7.149456421020508Q11.00368503125,7.109511421020508,11.06709503125,7.075617421020508Q11.13050503125,7.041724421020508,11.19693103125,7.0142094210205075Q11.26335803125,6.9866948210205075,11.33215703125,6.965823421020508Q11.40096703125,6.944952021020508,11.47148703125,6.930925121020508Q11.54199703125,6.916898221020507,11.61355703125,6.909850821020508Q11.68510703125,6.902803421020508,11.75700703125,6.902803421020508Q11.82890703125,6.902803421020508,11.90045703125,6.909850821020508Q11.972017031250001,6.916898221020507,12.04253703125,6.930925121020508Q12.11304703125,6.944952021020508,12.18185703125,6.965823421020508Q12.25065703125,6.9866948210205075,12.31708703125,7.0142094210205075Q12.38350703125,7.041724421020508,12.446917031249999,7.075617421020508Q12.51032703125,7.109511421020508,12.57011703125,7.149456421020508Q12.62989703125,7.1894014210205075,12.68547703125,7.235014421020508Q12.74105703125,7.280627421020508,12.79189703125,7.331467421020508Q12.84273703125,7.382308421020507,12.88834703125,7.437887421020508Q12.93395703125,7.493466421020508,12.97390703125,7.5532494210205074Q13.01384703125,7.613031421020508,13.047747031250001,7.676441421020508Q13.08163703125,7.739851421020508,13.10915703125,7.806277421020508Q13.13666703125,7.872704421020508,13.15753703125,7.941503421020508Q13.17840703125,8.010313421020507,13.19243703125,8.080833421020508Q13.20646703125,8.151343421020508,13.21351703125,8.222903421020508Q13.22055703125,8.294453421020508,13.22055703125,8.366353421020507Z"
                        fill="#B3BBC8"
                        fillOpacity="1"
                    />
                    <path
                        d="M12.86260703125,7.260757421020508Q12.40465703125,6.802803521020508,11.75700703125,6.802803521020508Q11.10936403125,6.802803421020508,10.65141103125,7.260757421020508Q10.19345713125,7.718710421020508,10.19345713125,8.366353421020507Q10.19345703125,9.014003421020508,10.65141103125,9.471953421020508Q11.10936403125,9.929903421020509,11.75700703125,9.929903421020509Q12.40465703125,9.929903421020509,12.86260703125,9.471953421020508Q13.32055703125,9.014003421020508,13.32055703125,8.366353421020507Q13.32055703125,7.718710421020508,12.86260703125,7.260757421020508ZM10.79283203125,7.4021784210205075Q11.19220703125,7.0028034210205075,11.75700703125,7.0028034210205075Q12.32180703125,7.0028034210205075,12.72118703125,7.4021784210205075Q13.12055703125,7.8015534210205075,13.12055703125,8.366353421020507Q13.12055703125,8.931153421020507,12.72118703125,9.330533421020508Q12.32180703125,9.729903421020508,11.75700703125,9.729903421020508Q11.19220703125,9.729903421020508,10.79283203125,9.330533421020508Q10.39345703125,8.931153421020507,10.39345703125,8.366353421020507Q10.39345703125,7.8015534210205075,10.79283203125,7.4021784210205075Z"
                        fillRule="evenodd"
                        fill="#B3BBC8"
                        fillOpacity="1"
                    />
                </g>
            </g>
        </g>
    </svg>
);

export const cancelEditLocationIcon = () => (
    <svg xmlns="http://www.w3.org/2000/svg" version="1.1" width="24" height="24" viewBox="0 0 24 24">
        <g>
            <g />
            <g transform="matrix(1,-4.2146844236867764e-8,1.2644053981603065e-7,1,-5.68982549964403e-7,1.8966598425151915e-7)">
                <g transform="matrix(0.7071067094802856,0.7071067690849304,-0.7071068286895752,0.7071068286895752,4.866459731257521,-2.748367971217192)">
                    <path
                        d="M5.750802993774414,5.383999872634888C5.750802993774414,4.895863872634887,6.146514993774414,4.500151872634888,6.6346509937744145,4.500151872634888L24.311602993774414,4.500151872634888C24.799702993774414,4.500151872634888,25.195502993774415,4.895863872634887,25.195502993774415,5.383999872634888C25.195502993774415,5.8721318726348875,24.799702993774414,6.267851872634887,24.311602993774414,6.267851872634887L6.6346509937744145,6.267851872634887C6.146514993774414,6.267851872634887,5.750802993774414,5.8721318726348875,5.750802993774414,5.383999872634888Z"
                        fillOpacity="1"
                    />
                    <path
                        d="M5.938964993774414,6.079681872634888Q6.227127993774414,6.367851872634888,6.6346509937744145,6.367851872634888L24.311602993774414,6.367851872634888Q24.719102993774413,6.367851872634888,25.007302993774413,6.079681872634888Q25.295502993774413,5.791521872634887,25.295502993774413,5.383999872634888Q25.295502993774413,4.976473872634887,25.007302993774413,4.688313872634888Q24.719102993774413,4.400151872634888,24.311602993774414,4.400151872634888L6.6346509937744145,4.400151972634887Q6.227127993774414,4.400151872634888,5.938964993774414,4.688313872634888Q5.650802993774414,4.976476872634888,5.650802993774414,5.383999872634888Q5.650802993774414,5.791521872634887,5.938964993774414,6.079681872634888ZM24.311602993774414,6.167851872634888L6.6346509937744145,6.167851872634888Q6.3099699937744145,6.167851872634888,6.080386993774414,5.938261872634888Q5.850802893774414,5.708681872634887,5.850802893774414,5.383999872634888Q5.850802993774414,5.059319872634887,6.080386993774414,4.829735872634887Q6.3099699937744145,4.600151872634887,6.6346509937744145,4.600151872634887L24.311602993774414,4.600151872634887Q24.636302993774414,4.600151872634887,24.865902993774416,4.829735872634887Q25.095502993774414,5.059317872634888,25.095502993774414,5.383999872634888Q25.095502993774414,5.708681872634887,24.865902993774416,5.938261872634888Q24.636302993774414,6.167851872634888,24.311602993774414,6.167851872634888Z"
                        fillRule="evenodd"
                        fillOpacity="1"
                    />
                </g>
                <g transform="matrix(-0.7071067690849304,0.7071068286895752,-0.7071067094802856,-0.7071067094802856,37.35381010685603,-3.972554588151695)">
                    <path
                        d="M19.499648094177246,6.6337988857727055C19.499648094177246,6.145662885772705,19.895360094177246,5.749950885772705,20.383496094177247,5.749950885772705L38.06044809417725,5.749950885772705C38.54854809417725,5.749950885772705,38.94434809417724,6.145662885772705,38.94434809417724,6.6337988857727055C38.94434809417724,7.121930885772705,38.54854809417725,7.517650885772705,38.06044809417725,7.517650885772705L20.383496094177247,7.517650885772705C19.895360094177246,7.517650885772705,19.499648094177246,7.121930885772705,19.499648094177246,6.6337988857727055Z"
                        fillOpacity="1"
                    />
                    <path
                        d="M19.687810094177244,7.329480885772705Q19.975973094177245,7.617650885772705,20.383496094177247,7.617650885772705L38.06044809417725,7.617650885772705Q38.467948094177245,7.617650885772705,38.75614809417725,7.329480885772705Q39.044348094177245,7.041320885772705,39.044348094177245,6.6337988857727055Q39.044348094177245,6.226272885772705,38.75614809417725,5.938112885772705Q38.467948094177245,5.649950885772705,38.06044809417725,5.649950885772705L20.383496094177247,5.649950985772705Q19.975973094177245,5.649950885772705,19.687810094177244,5.938112885772705Q19.399648094177245,6.226275885772705,19.399648094177245,6.6337988857727055Q19.399648094177245,7.041320885772705,19.687810094177244,7.329480885772705ZM38.06044809417725,7.417650885772705L20.383496094177247,7.417650885772705Q20.058815094177245,7.417650885772705,19.829232094177247,7.188060885772705Q19.599647994177246,6.958480885772705,19.599647994177246,6.6337988857727055Q19.599648094177248,6.309118885772705,19.829232094177247,6.079534885772705Q20.058815094177245,5.849950885772705,20.383496094177247,5.849950885772705L38.06044809417725,5.849950885772705Q38.38514809417725,5.849950885772705,38.614748094177244,6.079534885772705Q38.84434809417725,6.309116885772705,38.84434809417725,6.6337988857727055Q38.84434809417725,6.958480885772705,38.614748094177244,7.188060885772705Q38.38514809417725,7.417650885772705,38.06044809417725,7.417650885772705Z"
                        fillRule="evenodd"
                        fillOpacity="1"
                    />
                </g>
            </g>
        </g>
    </svg>
);

export const saveViewIcon = () => (
    <svg xmlns="http://www.w3.org/2000/svg" version="1.1" width="24" height="24" viewBox="0 0 24 24">
        <g>
            <g />
            <g>
                <g>
                    <path
                        d="M20.1182,6.190910000000001L16.927300000000002,3.245455Q16.8455,3.163636,16.7636,3.163636L16.681800000000003,3.0818182C16.6,3,16.5182,3,16.3545,3L6.045450000000001,3C4.9,3.000000146302,4,3.9,4,5.045450000000001L4,18.9545C4,20.1,4.9,21,6.045450000000001,21L18.318199999999997,21C19.4636,21,20.3636,20.1,20.3636,18.9545L20.3636,6.7636400000000005C20.3636,6.51818,20.2818,6.35455,20.1182,6.190910000000001ZM8.909089999999999,4.63636L13,4.63636L13,7.09091L8.909089999999999,7.09091L8.909089999999999,4.63636ZM18.7273,18.9545C18.7273,19.2,18.5636,19.3636,18.318199999999997,19.3636L6.04546,19.3636C5.8,19.3636,5.63636,19.2,5.63636,18.9545L5.63636,5.04546C5.63636,4.8,5.8,4.63636,6.04546,4.63636L7.27273,4.63636L7.27273,7.09091C7.27273,7.99091,8.00909,8.72727,8.909089999999999,8.72727L13,8.72727C13.9,8.72727,14.6364,7.99091,14.6364,7.09091L14.6364,4.63636L16.0273,4.63636L18.7273,7.09091L18.7273,18.9545Z"
                        fillOpacity="1"
                    />
                    <path
                        d="M20.4636,18.9545L20.4636,6.7636400000000005Q20.4636,6.39494,20.1889,6.1202000000000005L20.1875,6.118790000000001L16.996499999999997,3.173265Q16.906599999999997,3.0840759,16.8089,3.0674472L16.752499999999998,3.0111073Q16.6414,2.9,16.3545,2.9L6.045450000000001,2.9Q5.14494,2.9000002,4.522471,3.522471Q3.9,4.14494,3.9,5.045450000000001L3.9,18.9545Q3.9,19.8551,4.522471,20.4775Q5.14494,21.1,6.045450000000001,21.1L18.318199999999997,21.1Q19.2187,21.1,19.8412,20.4775Q20.4636,19.8551,20.4636,18.9545ZM20.0488,6.262980000000001Q20.2636,6.478680000000001,20.2636,6.7636400000000005L20.2636,18.9545Q20.2636,19.7722,19.6997,20.3361Q19.1358,20.9,18.318199999999997,20.9L6.045450000000001,20.9Q5.22778,20.9,4.663892,20.3361Q4.1,19.7722,4.1,18.9545L4.0999998,5.045450000000001Q4.0999999,4.22779,4.663892,3.663893Q5.22779,3.1,6.045450000000001,3.1L16.3545,3.1Q16.5586,3.1,16.6111,3.152529L16.7222,3.263636L16.7636,3.263636Q16.804000000000002,3.263636,16.8566,3.316165L16.858,3.317579L20.0488,6.262980000000001ZM18.8273,18.9545L18.8273,7.09091L18.8273,7.04667L16.0659,4.53636L14.5364,4.53636L14.5364,7.09091Q14.5364,7.72449,14.085,8.17588Q13.63358,8.627279999999999,13,8.62727L8.909089999999999,8.62727Q8.27551,8.627279999999999,7.824120000000001,8.17588Q7.37273,7.72449,7.37273,7.09091L7.37273,4.53636L6.04546,4.53636Q5.81994,4.53636,5.6781500000000005,4.6781500000000005Q5.53636,4.81994,5.53636,5.04546L5.53636,18.9545Q5.53636,19.4636,6.04546,19.4636L18.318199999999997,19.4636Q18.8273,19.4636,18.8273,18.9545ZM8.809090000000001,4.53636L8.809090000000001,7.19091L13.1,7.19091L13.1,4.53636L8.809090000000001,4.53636ZM18.627299999999998,7.13515L18.627299999999998,18.9545Q18.627299999999998,19.0972,18.5441,19.1804Q18.460900000000002,19.2636,18.318199999999997,19.2636L6.04546,19.2636Q5.7363599999999995,19.2636,5.7363599999999995,18.9545L5.7363599999999995,5.04546Q5.73637,4.7363599999999995,6.04546,4.7363599999999995L7.17273,4.7363599999999995L7.17273,7.09091Q7.17273,7.80733,7.6827000000000005,8.3173Q8.19267,8.82727,8.909089999999999,8.82727L13,8.82727Q13.71642,8.82727,14.2264,8.3173Q14.7364,7.80734,14.7364,7.09091L14.7364,4.7363599999999995L15.9886,4.7363599999999995L18.627299999999998,7.13515ZM9.00909,6.9909099999999995L12.9,6.9909099999999995L12.9,4.7363599999999995L9.00909,4.7363599999999995L9.00909,6.9909099999999995Z"
                        fillRule="evenodd"
                        fillOpacity="1"
                    />
                </g>
                <g>
                    <path
                        d="M7.6818185394464304,13.636363991577149C7.6818185394464304,14.127271991577148,8.00909124684143,14.454541991577148,8.50000024684143,14.454541991577148L16.68181824684143,14.454541991577148C17.17272824684143,14.454541991577148,17.49999824684143,14.127271991577148,17.49999824684143,13.636363991577149C17.49999824684143,13.145453991577149,17.17272824684143,12.818181991577148,16.68181824684143,12.818181991577148L8.50000024684143,12.818181991577148C8.00909124684143,12.818181991577148,7.681818246841431,13.145453991577149,7.6818185394464304,13.636363991577149ZM12.59090824684143,16.090911991577148L8.50000024684143,16.090911991577148C8.00909124684143,16.090911991577148,7.681818246841431,16.41818199157715,7.681818246841431,16.90909199157715C7.681818246841431,17.40000199157715,8.00909124684143,17.727271991577148,8.50000024684143,17.727271991577148L12.59090824684143,17.727271991577148C13.081818246841431,17.727271991577148,13.409088246841431,17.40000199157715,13.409088246841431,16.90909199157715C13.409088246841431,16.41818199157715,13.081818246841431,16.090911991577148,12.59090824684143,16.090911991577148Z"
                        fillOpacity="1"
                    />
                    <path
                        d="M16.68181824684143,12.718181991577149L8.50000024684143,12.718181991577149Q8.09039724684143,12.718181991577149,7.836107246841431,12.972470991577149Q7.581818246841431,13.226760991577148,7.581818446841431,13.636363991577149Q7.581818546841431,14.045961991577148,7.83610824684143,14.300251991577149Q8.09039724684143,14.554541991577148,8.50000024684143,14.554541991577148L16.68181824684143,14.554541991577148Q17.091418246841428,14.554541991577148,17.345708246841433,14.300261991577148Q17.59999824684143,14.045971991577149,17.59999824684143,13.636363991577149Q17.59999824684143,13.226758991577148,17.345708246841433,12.972470991577149Q17.091418246841428,12.718181991577149,16.68181824684143,12.718181991577149ZM7.9775292468414305,13.11389299157715Q8.173239246841431,12.918181991577148,8.50000024684143,12.918181991577148L16.68181824684143,12.918181991577148Q17.39999824684143,12.918181991577148,17.39999824684143,13.636363991577149Q17.39999824684143,14.354541991577149,16.68181824684143,14.354541991577149L8.50000024684143,14.354541991577149Q8.17324024684143,14.354541991577149,7.9775292468414305,14.158831991577149Q7.78181824684143,13.963121991577149,7.78181824684143,13.636363991577149Q7.78181824684143,13.30960299157715,7.9775292468414305,13.11389299157715ZM13.254798246841432,17.572981991577148Q13.509088246841431,17.318691991577147,13.509088246841431,16.90909199157715Q13.509088246841431,16.49949199157715,13.254798246841432,16.245201991577147Q13.00051824684143,15.990911991577148,12.59090824684143,15.990911991577148L8.50000024684143,15.990911991577148Q8.090396246841431,15.990911991577148,7.836107246841431,16.245201991577147Q7.581818246841431,16.49949199157715,7.581818246841431,16.90909199157715Q7.581818246841431,17.318691991577147,7.836107246841431,17.572981991577148Q8.090396246841431,17.82727199157715,8.50000024684143,17.82727199157715L12.59090824684143,17.82727199157715Q13.00051824684143,17.82727199157715,13.254798246841432,17.572981991577148ZM13.113378246841432,16.386621991577147Q13.30908824684143,16.58233199157715,13.30908824684143,16.90909199157715Q13.30908824684143,17.62727199157715,12.59090824684143,17.62727199157715L8.50000024684143,17.62727199157715Q7.78181824684143,17.62727199157715,7.781818146841431,16.90909199157715Q7.781818146841431,16.19091199157715,8.50000024684143,16.19091199157715L12.59090824684143,16.19091199157715Q12.91766824684143,16.19091199157715,13.113378246841432,16.386621991577147Z"
                        fillRule="evenodd"
                        fillOpacity="1"
                    />
                </g>
            </g>
        </g>
    </svg>
);

export const saveViewDisableIcon = () => (
    <svg xmlns="http://www.w3.org/2000/svg" fill="none" version="1.1" width="24" height="24" viewBox="0 0 24 24">
        <g>
            <g />
            <g>
                <g>
                    <path
                        d="M20.1182,6.190910000000001L16.927300000000002,3.245455Q16.8455,3.163636,16.7636,3.163636L16.681800000000003,3.0818182C16.6,3,16.5182,3,16.3545,3L6.045450000000001,3C4.9,3.000000146302,4,3.9,4,5.045450000000001L4,18.9545C4,20.1,4.9,21,6.045450000000001,21L18.318199999999997,21C19.4636,21,20.3636,20.1,20.3636,18.9545L20.3636,6.7636400000000005C20.3636,6.51818,20.2818,6.35455,20.1182,6.190910000000001ZM8.909089999999999,4.63636L13,4.63636L13,7.09091L8.909089999999999,7.09091L8.909089999999999,4.63636ZM18.7273,18.9545C18.7273,19.2,18.5636,19.3636,18.318199999999997,19.3636L6.04546,19.3636C5.8,19.3636,5.63636,19.2,5.63636,18.9545L5.63636,5.04546C5.63636,4.8,5.8,4.63636,6.04546,4.63636L7.27273,4.63636L7.27273,7.09091C7.27273,7.99091,8.00909,8.72727,8.909089999999999,8.72727L13,8.72727C13.9,8.72727,14.6364,7.99091,14.6364,7.09091L14.6364,4.63636L16.0273,4.63636L18.7273,7.09091L18.7273,18.9545Z"
                        fill="#B3BBC8"
                        fillOpacity="1"
                    />
                    <path
                        d="M20.4636,18.9545L20.4636,6.7636400000000005Q20.4636,6.39494,20.1889,6.1202000000000005L20.1875,6.118790000000001L16.996499999999997,3.173265Q16.906599999999997,3.0840759,16.8089,3.0674472L16.752499999999998,3.0111073Q16.6414,2.9,16.3545,2.9L6.045450000000001,2.9Q5.14494,2.9000002,4.522471,3.522471Q3.9,4.14494,3.9,5.045450000000001L3.9,18.9545Q3.9,19.8551,4.522471,20.4775Q5.14494,21.1,6.045450000000001,21.1L18.318199999999997,21.1Q19.2187,21.1,19.8412,20.4775Q20.4636,19.8551,20.4636,18.9545ZM20.0488,6.262980000000001Q20.2636,6.478680000000001,20.2636,6.7636400000000005L20.2636,18.9545Q20.2636,19.7722,19.6997,20.3361Q19.1358,20.9,18.318199999999997,20.9L6.045450000000001,20.9Q5.22778,20.9,4.663892,20.3361Q4.1,19.7722,4.1,18.9545L4.0999998,5.045450000000001Q4.0999999,4.22779,4.663892,3.663893Q5.22779,3.1,6.045450000000001,3.1L16.3545,3.1Q16.5586,3.1,16.6111,3.152529L16.7222,3.263636L16.7636,3.263636Q16.804000000000002,3.263636,16.8566,3.316165L16.858,3.317579L20.0488,6.262980000000001ZM18.8273,18.9545L18.8273,7.09091L18.8273,7.04667L16.0659,4.53636L14.5364,4.53636L14.5364,7.09091Q14.5364,7.72449,14.085,8.17588Q13.63358,8.627279999999999,13,8.62727L8.909089999999999,8.62727Q8.27551,8.627279999999999,7.824120000000001,8.17588Q7.37273,7.72449,7.37273,7.09091L7.37273,4.53636L6.04546,4.53636Q5.81994,4.53636,5.6781500000000005,4.6781500000000005Q5.53636,4.81994,5.53636,5.04546L5.53636,18.9545Q5.53636,19.4636,6.04546,19.4636L18.318199999999997,19.4636Q18.8273,19.4636,18.8273,18.9545ZM8.809090000000001,4.53636L8.809090000000001,7.19091L13.1,7.19091L13.1,4.53636L8.809090000000001,4.53636ZM18.627299999999998,7.13515L18.627299999999998,18.9545Q18.627299999999998,19.0972,18.5441,19.1804Q18.460900000000002,19.2636,18.318199999999997,19.2636L6.04546,19.2636Q5.7363599999999995,19.2636,5.7363599999999995,18.9545L5.7363599999999995,5.04546Q5.73637,4.7363599999999995,6.04546,4.7363599999999995L7.17273,4.7363599999999995L7.17273,7.09091Q7.17273,7.80733,7.6827000000000005,8.3173Q8.19267,8.82727,8.909089999999999,8.82727L13,8.82727Q13.71642,8.82727,14.2264,8.3173Q14.7364,7.80734,14.7364,7.09091L14.7364,4.7363599999999995L15.9886,4.7363599999999995L18.627299999999998,7.13515ZM9.00909,6.9909099999999995L12.9,6.9909099999999995L12.9,4.7363599999999995L9.00909,4.7363599999999995L9.00909,6.9909099999999995Z"
                        fillRule="evenodd"
                        fill="#B3BBC8"
                        fillOpacity="1"
                    />
                </g>
                <g>
                    <path
                        d="M7.6818185394464304,13.636363991577149C7.6818185394464304,14.127271991577148,8.00909124684143,14.454541991577148,8.50000024684143,14.454541991577148L16.68181824684143,14.454541991577148C17.17272824684143,14.454541991577148,17.49999824684143,14.127271991577148,17.49999824684143,13.636363991577149C17.49999824684143,13.145453991577149,17.17272824684143,12.818181991577148,16.68181824684143,12.818181991577148L8.50000024684143,12.818181991577148C8.00909124684143,12.818181991577148,7.681818246841431,13.145453991577149,7.6818185394464304,13.636363991577149ZM12.59090824684143,16.090911991577148L8.50000024684143,16.090911991577148C8.00909124684143,16.090911991577148,7.681818246841431,16.41818199157715,7.681818246841431,16.90909199157715C7.681818246841431,17.40000199157715,8.00909124684143,17.727271991577148,8.50000024684143,17.727271991577148L12.59090824684143,17.727271991577148C13.081818246841431,17.727271991577148,13.409088246841431,17.40000199157715,13.409088246841431,16.90909199157715C13.409088246841431,16.41818199157715,13.081818246841431,16.090911991577148,12.59090824684143,16.090911991577148Z"
                        fill="#B3BBC8"
                        fillOpacity="1"
                    />
                    <path
                        d="M16.68181824684143,12.718181991577149L8.50000024684143,12.718181991577149Q8.09039724684143,12.718181991577149,7.836107246841431,12.972470991577149Q7.581818246841431,13.226760991577148,7.581818446841431,13.636363991577149Q7.581818546841431,14.045961991577148,7.83610824684143,14.300251991577149Q8.09039724684143,14.554541991577148,8.50000024684143,14.554541991577148L16.68181824684143,14.554541991577148Q17.091418246841428,14.554541991577148,17.345708246841433,14.300261991577148Q17.59999824684143,14.045971991577149,17.59999824684143,13.636363991577149Q17.59999824684143,13.226758991577148,17.345708246841433,12.972470991577149Q17.091418246841428,12.718181991577149,16.68181824684143,12.718181991577149ZM7.9775292468414305,13.11389299157715Q8.173239246841431,12.918181991577148,8.50000024684143,12.918181991577148L16.68181824684143,12.918181991577148Q17.39999824684143,12.918181991577148,17.39999824684143,13.636363991577149Q17.39999824684143,14.354541991577149,16.68181824684143,14.354541991577149L8.50000024684143,14.354541991577149Q8.17324024684143,14.354541991577149,7.9775292468414305,14.158831991577149Q7.78181824684143,13.963121991577149,7.78181824684143,13.636363991577149Q7.78181824684143,13.30960299157715,7.9775292468414305,13.11389299157715ZM13.254798246841432,17.572981991577148Q13.509088246841431,17.318691991577147,13.509088246841431,16.90909199157715Q13.509088246841431,16.49949199157715,13.254798246841432,16.245201991577147Q13.00051824684143,15.990911991577148,12.59090824684143,15.990911991577148L8.50000024684143,15.990911991577148Q8.090396246841431,15.990911991577148,7.836107246841431,16.245201991577147Q7.581818246841431,16.49949199157715,7.581818246841431,16.90909199157715Q7.581818246841431,17.318691991577147,7.836107246841431,17.572981991577148Q8.090396246841431,17.82727199157715,8.50000024684143,17.82727199157715L12.59090824684143,17.82727199157715Q13.00051824684143,17.82727199157715,13.254798246841432,17.572981991577148ZM13.113378246841432,16.386621991577147Q13.30908824684143,16.58233199157715,13.30908824684143,16.90909199157715Q13.30908824684143,17.62727199157715,12.59090824684143,17.62727199157715L8.50000024684143,17.62727199157715Q7.78181824684143,17.62727199157715,7.781818146841431,16.90909199157715Q7.781818146841431,16.19091199157715,8.50000024684143,16.19091199157715L12.59090824684143,16.19091199157715Q12.91766824684143,16.19091199157715,13.113378246841432,16.386621991577147Z"
                        fillRule="evenodd"
                        fill="#B3BBC8"
                        fillOpacity="1"
                    />
                </g>
            </g>
        </g>
    </svg>
);

export const selectMapIcon = () => (
    <svg xmlns="http://www.w3.org/2000/svg" version="1.1" width="24" height="24" viewBox="0 0 24 24">
        <g>
            <g />
            <g>
                <path
                    d="M11.75,22.5C6.3875,22.5,2,18.1125,2,12.75C2,7.3875,6.3875,3,11.75,3C17.1125,3,21.5,7.3875,21.5,12.75C21.5,18.1125,17.1125,22.5,11.75,22.5ZM11.75,21.1071C16.3464,21.1071,20.1071,17.3464,20.1071,12.75C20.1071,8.15357,16.3464,4.39286,11.75,4.39286C7.15357,4.39286,3.3928599999999998,8.15357,3.3928599999999998,12.75C3.3928599999999998,17.3464,7.15357,21.1071,11.75,21.1071ZM15.8589,9.54643L13.8393,14.4911C13.7696,14.7,13.6304,14.8393,13.4214,14.9089L8.476790000000001,16.8589C7.91964,17.0679,7.3625,16.5107,7.57143,15.9536L9.521429999999999,11.00893C9.59107,10.8,9.730360000000001,10.66071,9.93929,10.59107L14.8839,8.64107C15.5107,8.43214,16.0679,8.98929,15.8589,9.54643ZM10.775,11.775L9.521429999999999,14.9089L12.6554,13.6554L13.9089,10.521429999999999L10.775,11.775Z"
                    fillOpacity="1"
                />
                <path
                    d="M20.9608,16.633499999999998Q21.75,14.7757,21.75,12.75Q21.75,10.72425,20.9608,8.8665Q20.1991,7.07367,18.8127,5.68729Q17.426299999999998,4.3009,15.6335,3.53924Q13.7757,2.75,11.75,2.749999Q9.72425,2.749999,7.8665,3.5392390000000002Q6.07367,4.3009,4.68729,5.68729Q3.3009,7.07367,2.53924,8.8665Q1.75,10.724260000000001,1.749999,12.75Q1.749999,14.7757,2.5392390000000002,16.633499999999998Q3.3009,18.426299999999998,4.68729,19.8127Q6.07366,21.1991,7.8665,21.9608Q9.72425,22.75,11.75,22.75Q13.7758,22.75,15.6335,21.9608Q17.426299999999998,21.1991,18.8127,19.8127Q20.1991,18.426299999999998,20.9608,16.633499999999998ZM20.5006,9.06201Q21.25,10.82606,21.25,12.75Q21.25,14.674,20.5006,16.438000000000002Q19.7769,18.1414,18.4592,19.4592Q17.1414,20.7769,15.438,21.5006Q13.6739,22.25,11.75,22.25Q9.82606,22.25,8.06201,21.5006Q6.35855,20.7769,5.04084,19.4592Q3.7231199999999998,18.1414,2.999432,16.438000000000002Q2.249999,14.6739,2.249999,12.75Q2.25,10.82607,2.999432,9.06201Q3.7231199999999998,7.35855,5.04084,6.04084Q6.35855,4.72312,8.06201,3.999432Q9.82607,3.249999,11.75,3.249999Q13.6739,3.25,15.438,3.999432Q17.1414,4.72312,18.4592,6.04084Q19.7769,7.35855,20.5006,9.06201ZM3.82219,9.40732Q3.1428599999999998,11.00637,3.1428599999999998,12.75Q3.1428599999999998,14.4936,3.82219,16.0927Q4.47776,17.6358,5.67099,18.829Q6.86422,20.0222,8.40732,20.6778Q10.00637,21.3571,11.75,21.3571Q13.4936,21.3571,15.0927,20.6778Q16.6358,20.0222,17.829,18.829Q19.0222,17.6358,19.6778,16.0927Q20.3571,14.4936,20.3571,12.75Q20.3571,11.00637,19.6778,9.40732Q19.0222,7.86422,17.829,6.67099Q16.6358,5.47776,15.0927,4.82219Q13.4936,4.14286,11.75,4.14286Q10.00637,4.14286,8.40732,4.82219Q6.86422,5.47776,5.67099,6.67099Q4.47775,7.86423,3.82219,9.40732ZM6.02454,18.4755Q3.6428599999999998,16.0938,3.6428599999999998,12.75Q3.6428599999999998,9.40623,6.02454,7.02454Q8.40623,4.64286,11.75,4.64286Q15.0938,4.64286,17.4755,7.02455Q19.8571,9.40623,19.8571,12.75Q19.8571,16.0938,17.4755,18.4755Q15.0938,20.8571,11.75,20.8571Q8.40623,20.8571,6.02454,18.4755ZM15.8574,8.625630000000001Q15.4128,8.20124,14.8049,8.4039L14.7985,8.406030000000001L9.853439999999999,10.35619Q9.4305,10.50014,9.286539999999999,10.923079999999999L7.33735,15.8658Q7.12413,16.4344,7.56005,16.8703Q7.99598,17.3062,8.5685,17.0915L13.5073,15.1438Q13.9295,15.0001,14.0737,14.5784L16.0917,9.637599999999999L16.093,9.63421Q16.3094,9.05708,15.8574,8.625630000000001ZM14.969,8.87627L10.02473,10.82611L10.01834,10.828240000000001Q9.82353,10.893180000000001,9.758600000000001,11.08799L9.75647,11.09438L7.80551,16.0414Q7.70534,16.308500000000002,7.91361,16.5168Q8.12188,16.725,8.385069999999999,16.6264L13.336,14.6739L13.3424,14.6718Q13.5372,14.6068,13.6021,14.412L13.6047,14.4042L15.626,9.45554Q15.7241,9.18956,15.5122,8.98731Q15.2875,8.77279,14.969,8.87627ZM12.8477,13.8477L14.3577,10.072669999999999L13.8161,10.28931L10.58267,11.58267L9.072659999999999,15.3577L12.8477,13.8477ZM13.4602,10.970189999999999L10.96733,11.96733L9.970189999999999,14.4602L12.463,13.463L13.4602,10.970189999999999Z"
                    fillRule="evenodd"
                    fillOpacity="1"
                />
            </g>
        </g>
    </svg>
);

export const unLocatedIcon = () => (
    <svg xmlns="http://www.w3.org/2000/svg" version="1.1" width="24" height="24" viewBox="0 0 24 24">
        <g>
            <g />
            <g>
                <g>
                    <path
                        d="M11.75,3C6.36493,3,2,7.36493,2,12.75C2,18.1351,6.36493,22.5,11.75,22.5C17.1351,22.5,21.5,18.1325,21.5,12.74739C21.5,7.36232,17.1351,3,11.75,3ZM17.5003,18.497700000000002C16.7541,19.2439,15.8827,19.8309,14.9148,20.2406C14.1686,20.5563,13.3859,20.7545,12.5823,20.838L12.5823,17.6707L10.96467,17.6707L10.96467,20.8433C10.14283,20.765,9.34707,20.5641,8.58523,20.2432C7.61727,19.8336,6.74846,19.2465,5.99966,18.5003C5.25348,17.7541,4.66644,16.8827,4.256819999999999,15.9148C3.9411300000000002,15.1686,3.74284,14.3859,3.65935,13.5823L6.87891,13.5823L6.87891,11.96467L3.65674,11.96468C3.73501,11.14283,3.93591,10.34707,4.256819999999999,9.58523C4.66644,8.617270000000001,5.25348,7.74846,5.99967,6.99967C6.74585,6.25348,7.61727,5.66644,8.58523,5.256819999999999C9.34707,4.93591,10.14283,4.7350200000000005,10.96467,4.65674L10.96467,7.86587L12.5823,7.86587L12.5823,4.65935C13.3859,4.74023,14.1686,4.94113,14.9148,5.256819999999999C15.8827,5.66644,16.7515,6.25348,17.5003,6.99967C18.2465,7.74585,18.8336,8.617270000000001,19.2432,9.58523C19.5641,10.34707,19.765,11.14283,19.8433,11.96468L16.6524,11.96468L16.6524,13.5823L19.8406,13.5823C19.7598,14.3859,19.5589,15.1686,19.2432,15.9148C18.8336,16.8801,18.2491,17.7489,17.5003,18.497700000000002Z"
                        fillOpacity="1"
                    />
                    <path
                        d="M20.872,16.6015Q21.65,14.7616,21.65,12.74739Q21.65,10.73322,20.872,8.89409Q20.1205,7.11782,18.7504,5.74821Q17.3804,4.37868,15.6037,3.627592Q13.7642,2.85,11.75,2.85Q9.73582,2.85,7.89631,3.6280099999999997Q6.11963,4.37944,4.74953,5.74953Q3.3794399999999998,7.11963,2.6280099999999997,8.89631Q1.849999,10.73584,1.85,12.75Q1.85,14.7642,2.6280099999999997,16.6037Q3.3794399999999998,18.3804,4.74953,19.7505Q6.11964,21.1206,7.89631,21.872Q9.73585,22.65,11.75,22.65Q13.7641,22.65,15.6037,21.8716Q17.3803,21.1198,18.7505,19.7491Q20.1205,18.3786,20.872,16.6015ZM20.5957,9.01097Q21.35,10.79407,21.35,12.74739Q21.35,14.7008,20.5957,16.4846Q19.867,18.207900000000002,18.5383,19.537Q17.2095,20.8663,15.4868,21.5953Q13.7032,22.35,11.75,22.35Q9.796669999999999,22.35,8.013169999999999,21.5957Q6.29039,20.8671,4.96166,19.5383Q3.63295,18.209600000000002,2.904314,16.486800000000002Q2.15,14.7033,2.15,12.75Q2.15,10.796669999999999,2.904314,9.013169999999999Q3.63295,7.29039,4.96166,5.96166Q6.29037,4.63295,8.013169999999999,3.904314Q9.796669999999999,3.15,11.75,3.15Q13.7034,3.15,15.4869,3.903917Q17.209699999999998,4.63222,18.5384,5.96038Q19.8671,7.28861,20.5957,9.01097ZM17.6064,18.6038Q18.7506,17.459600000000002,19.3813,15.9734L19.3813,15.9732Q19.8661,14.8274,19.9899,13.5973L20.0065,13.4323L16.8024,13.4323L16.8024,12.11468L20.0082,12.11468L19.9926,11.95045Q19.8729,10.69379,19.3813,9.526769999999999Q18.7517,8.03886,17.6062,6.893409999999999Q16.4542,5.74539,14.9732,5.1186799999999995Q13.8274,4.63392,12.5973,4.51011L12.4323,4.4935L12.4323,7.71587L11.11467,7.71587L11.11467,4.49178L10.95045,4.50742Q9.69378,4.6271,8.526769999999999,5.1186799999999995Q7.03886,5.74834,5.893409999999999,6.89378Q4.74539,8.045819999999999,4.11859,9.527000000000001Q3.6271,10.69377,3.5074199999999998,11.95045L3.4917800000000003,12.11468L6.72891,12.11467L6.72891,13.4323L3.49296,13.4323L3.5101500000000003,13.5978Q3.6392100000000003,14.8399,4.1186799999999995,15.9732Q4.74834,17.461100000000002,5.89378,18.6066Q7.04581,19.7546,8.527000000000001,20.3814Q9.69379,20.8729,10.95045,20.9926L11.11467,21.0082L11.11467,17.820700000000002L12.4323,17.820700000000002L12.4323,21.0044L12.5978,20.9872Q13.8399,20.8582,14.9732,20.3787Q16.461199999999998,19.7491,17.6064,18.6038ZM19.6731,13.7323Q19.5395,14.8292,19.1051,15.8562L19.105,15.8563Q18.4972,17.2887,17.3943,18.3917Q16.290399999999998,19.4956,14.8563,20.1024Q13.84,20.5324,12.7323,20.67L12.7323,17.520699999999998L10.81468,17.520699999999998L10.81468,20.6766Q9.6915,20.5464,8.643460000000001,20.1049Q7.21634,19.501,6.10554,18.3941Q5.00183,17.290399999999998,4.39496,15.8563Q3.9649900000000002,14.84,3.82742,13.7323L7.02891,13.7323L7.02891,11.81468L3.8234,11.81468Q3.95359,10.6915,4.39506,9.643460000000001Q4.99899,8.21635,6.10592,7.10555Q7.20963,6.00183,8.64369,5.39496Q9.691510000000001,4.95359,10.81468,4.8233999999999995L10.81468,8.01587L12.7323,8.01587L12.7323,4.8269400000000005Q13.8293,4.96047,14.8563,5.39497Q16.2837,5.99899,17.3945,7.10592Q18.4982,8.20964,19.105,9.64369Q19.5464,10.69152,19.6766,11.81468L16.5024,11.81468L16.5024,13.7323L19.6731,13.7323Z"
                        fillRule="evenodd"
                        fillOpacity="1"
                    />
                </g>
                <g>
                    <path
                        d="M11.880315731201172,10.3080153465271C10.445335731201173,10.3080153465271,9.310405731201172,11.4168553465271,9.310405731201172,12.8518353465271C9.310405731201172,14.2868053465271,10.445335731201173,15.4478353465271,11.880315731201172,15.4478353465271C13.315285731201172,15.4478353465271,14.450225731201172,14.2868053465271,14.450225731201172,12.8518353465271C14.450225731201172,11.4168553465271,13.315285731201172,10.3080153465271,11.880315731201172,10.3080153465271Z"
                        fillOpacity="1"
                    />
                </g>
            </g>
        </g>
    </svg>
);

export const unLocatedDisabledIcon = () => (
    <svg xmlns="http://www.w3.org/2000/svg" fill="none" version="1.1" width="24" height="24" viewBox="0 0 24 24">
        <g>
            <g />
            <g>
                <g>
                    <path
                        d="M11.75,3C6.36493,3,2,7.36493,2,12.75C2,18.1351,6.36493,22.5,11.75,22.5C17.1351,22.5,21.5,18.1325,21.5,12.74739C21.5,7.36232,17.1351,3,11.75,3ZM17.5003,18.497700000000002C16.7541,19.2439,15.8827,19.8309,14.9148,20.2406C14.1686,20.5563,13.3859,20.7545,12.5823,20.838L12.5823,17.6707L10.96467,17.6707L10.96467,20.8433C10.14283,20.765,9.34707,20.5641,8.58523,20.2432C7.61727,19.8336,6.74846,19.2465,5.99966,18.5003C5.25348,17.7541,4.66644,16.8827,4.256819999999999,15.9148C3.9411300000000002,15.1686,3.74284,14.3859,3.65935,13.5823L6.87891,13.5823L6.87891,11.96467L3.65674,11.96468C3.73501,11.14283,3.93591,10.34707,4.256819999999999,9.58523C4.66644,8.617270000000001,5.25348,7.74846,5.99967,6.99967C6.74585,6.25348,7.61727,5.66644,8.58523,5.256819999999999C9.34707,4.93591,10.14283,4.7350200000000005,10.96467,4.65674L10.96467,7.86587L12.5823,7.86587L12.5823,4.65935C13.3859,4.74023,14.1686,4.94113,14.9148,5.256819999999999C15.8827,5.66644,16.7515,6.25348,17.5003,6.99967C18.2465,7.74585,18.8336,8.617270000000001,19.2432,9.58523C19.5641,10.34707,19.765,11.14283,19.8433,11.96468L16.6524,11.96468L16.6524,13.5823L19.8406,13.5823C19.7598,14.3859,19.5589,15.1686,19.2432,15.9148C18.8336,16.8801,18.2491,17.7489,17.5003,18.497700000000002Z"
                        fill="#B3BBC8"
                        fillOpacity="1"
                    />
                    <path
                        d="M20.872,16.6015Q21.65,14.7616,21.65,12.74739Q21.65,10.73322,20.872,8.89409Q20.1205,7.11782,18.7504,5.74821Q17.3804,4.37868,15.6037,3.627592Q13.7642,2.85,11.75,2.85Q9.73582,2.85,7.89631,3.6280099999999997Q6.11963,4.37944,4.74953,5.74953Q3.3794399999999998,7.11963,2.6280099999999997,8.89631Q1.849999,10.73584,1.85,12.75Q1.85,14.7642,2.6280099999999997,16.6037Q3.3794399999999998,18.3804,4.74953,19.7505Q6.11964,21.1206,7.89631,21.872Q9.73585,22.65,11.75,22.65Q13.7641,22.65,15.6037,21.8716Q17.3803,21.1198,18.7505,19.7491Q20.1205,18.3786,20.872,16.6015ZM20.5957,9.01097Q21.35,10.79407,21.35,12.74739Q21.35,14.7008,20.5957,16.4846Q19.867,18.207900000000002,18.5383,19.537Q17.2095,20.8663,15.4868,21.5953Q13.7032,22.35,11.75,22.35Q9.796669999999999,22.35,8.013169999999999,21.5957Q6.29039,20.8671,4.96166,19.5383Q3.63295,18.209600000000002,2.904314,16.486800000000002Q2.15,14.7033,2.15,12.75Q2.15,10.796669999999999,2.904314,9.013169999999999Q3.63295,7.29039,4.96166,5.96166Q6.29037,4.63295,8.013169999999999,3.904314Q9.796669999999999,3.15,11.75,3.15Q13.7034,3.15,15.4869,3.903917Q17.209699999999998,4.63222,18.5384,5.96038Q19.8671,7.28861,20.5957,9.01097ZM17.6064,18.6038Q18.7506,17.459600000000002,19.3813,15.9734L19.3813,15.9732Q19.8661,14.8274,19.9899,13.5973L20.0065,13.4323L16.8024,13.4323L16.8024,12.11468L20.0082,12.11468L19.9926,11.95045Q19.8729,10.69379,19.3813,9.526769999999999Q18.7517,8.03886,17.6062,6.893409999999999Q16.4542,5.74539,14.9732,5.1186799999999995Q13.8274,4.63392,12.5973,4.51011L12.4323,4.4935L12.4323,7.71587L11.11467,7.71587L11.11467,4.49178L10.95045,4.50742Q9.69378,4.6271,8.526769999999999,5.1186799999999995Q7.03886,5.74834,5.893409999999999,6.89378Q4.74539,8.045819999999999,4.11859,9.527000000000001Q3.6271,10.69377,3.5074199999999998,11.95045L3.4917800000000003,12.11468L6.72891,12.11467L6.72891,13.4323L3.49296,13.4323L3.5101500000000003,13.5978Q3.6392100000000003,14.8399,4.1186799999999995,15.9732Q4.74834,17.461100000000002,5.89378,18.6066Q7.04581,19.7546,8.527000000000001,20.3814Q9.69379,20.8729,10.95045,20.9926L11.11467,21.0082L11.11467,17.820700000000002L12.4323,17.820700000000002L12.4323,21.0044L12.5978,20.9872Q13.8399,20.8582,14.9732,20.3787Q16.461199999999998,19.7491,17.6064,18.6038ZM19.6731,13.7323Q19.5395,14.8292,19.1051,15.8562L19.105,15.8563Q18.4972,17.2887,17.3943,18.3917Q16.290399999999998,19.4956,14.8563,20.1024Q13.84,20.5324,12.7323,20.67L12.7323,17.520699999999998L10.81468,17.520699999999998L10.81468,20.6766Q9.6915,20.5464,8.643460000000001,20.1049Q7.21634,19.501,6.10554,18.3941Q5.00183,17.290399999999998,4.39496,15.8563Q3.9649900000000002,14.84,3.82742,13.7323L7.02891,13.7323L7.02891,11.81468L3.8234,11.81468Q3.95359,10.6915,4.39506,9.643460000000001Q4.99899,8.21635,6.10592,7.10555Q7.20963,6.00183,8.64369,5.39496Q9.691510000000001,4.95359,10.81468,4.8233999999999995L10.81468,8.01587L12.7323,8.01587L12.7323,4.8269400000000005Q13.8293,4.96047,14.8563,5.39497Q16.2837,5.99899,17.3945,7.10592Q18.4982,8.20964,19.105,9.64369Q19.5464,10.69152,19.6766,11.81468L16.5024,11.81468L16.5024,13.7323L19.6731,13.7323Z"
                        fillRule="evenodd"
                        fill="#B3BBC8"
                        fillOpacity="1"
                    />
                </g>
                <g>
                    <path
                        d="M11.880315731201172,10.3080153465271C10.445335731201173,10.3080153465271,9.310405731201172,11.4168553465271,9.310405731201172,12.8518353465271C9.310405731201172,14.2868053465271,10.445335731201173,15.4478353465271,11.880315731201172,15.4478353465271C13.315285731201172,15.4478353465271,14.450225731201172,14.2868053465271,14.450225731201172,12.8518353465271C14.450225731201172,11.4168553465271,13.315285731201172,10.3080153465271,11.880315731201172,10.3080153465271Z"
                        fill="#B3BBC8"
                        fillOpacity="1"
                    />
                </g>
            </g>
        </g>
    </svg>
);

export const editTokenIcon = () => (
    <svg xmlns="http://www.w3.org/2000/svg" version="1.1" width="24" height="24" viewBox="0 0 24 24">
        <g>
            <g />
            <g>
                <path
                    d="M22.2917,12.84678L18.2262,20.2187C17.9379,20.7446,17.386400000000002,21.072,16.7867,21.0731L8.71327,21.0731C8.11359,21.072,7.56206,20.7446,7.27384,20.2187L3.208329,12.84678C2.9305569,12.34235,2.9305569,11.7308,3.208329,11.22637L7.27384,3.854432C7.56206,3.32856,8.11359,3.00117567,8.71327,3L16.7867,3C17.386400000000002,3.00117567,17.9379,3.32856,18.2262,3.854432L22.2917,11.22637C22.5694,11.7308,22.5694,12.34235,22.2917,12.84678ZM21.0434,12.15239C21.083,12.08034,21.083,11.99305,21.0434,11.921L16.9851,4.54906C16.9441,4.47391,16.8654,4.42706,16.7798,4.42683L8.72016,4.42683C8.63457,4.42706,8.55591,4.47391,8.51494,4.54906L4.45657,11.921C4.41697,11.99305,4.41697,12.08034,4.45657,12.15239L8.51494,19.5243C8.55597,19.5994,8.63462,19.6461,8.72016,19.6463L16.7798,19.6463C16.8654,19.6461,16.944000000000003,19.5994,16.9851,19.5243L21.0434,12.15239ZM12.68865,15.7497C10.58717,15.7497,8.88377,14.0463,8.88377,11.94478C8.88377,9.8433,10.58717,8.13991,12.68865,8.13991C14.7899,8.13991,16.493499999999997,9.8433,16.493499999999997,11.94478C16.493499999999997,14.0463,14.7899,15.7497,12.68865,15.7497ZM12.68865,14.3228C14.002,14.3228,15.0667,13.2582,15.0667,11.94478C15.0667,10.63139,14.002,9.56674,12.68865,9.56674C11.37525,9.56674,10.3106,10.63139,10.3106,11.94478C10.3106,13.2582,11.37525,14.3228,12.68865,14.3228Z"
                    fillOpacity="1"
                />
                <path
                    d="M22.4669,12.94325Q22.9662,12.03658,22.4669,11.1299L22.4668,11.12979L18.4015,3.758307Q17.877499999999998,2.802138,16.7867,2.8L8.71288,2.8Q7.62251,2.802138,7.0987,3.757849L3.0331361,11.1299Q2.533852,12.03658,3.0331960000000002,12.94336L7.09845,20.3148Q7.62251,21.271,8.71327,21.2731L16.787100000000002,21.2731Q17.877499999999998,21.271,18.4013,20.3153L22.4669,12.94325ZM22.1165,11.32285L22.1165,11.32296Q22.5095,12.03663,22.1165,12.7503L18.051000000000002,20.1221Q17.6403,20.8715,16.7863,20.8731L8.71327,20.8731Q7.85968,20.8715,7.44922,20.1226L3.383463,12.75019Q2.99049079,12.03658,3.383523,11.32285L7.44897,3.951015Q7.85967,3.201674,8.71366,3.2L16.7867,3.2Q17.6403,3.201674,18.050800000000002,3.950557L22.1165,11.32285ZM17.1603,19.6208L17.1605,19.6203L21.2186,12.24884Q21.3352,12.0367,21.2187,11.82467L17.1603,4.45261Q17.037599999999998,4.22753,16.7804,4.22683L8.72016,4.22683Q8.46245,4.22753,8.33934,4.45332L4.28136,11.82455Q4.16475,12.03669,4.2813,12.24872L8.33973,19.6208Q8.46273,19.8458,8.71973,19.8463L16.7798,19.8463Q17.037,19.8458,17.1603,19.6208ZM20.8682,12.05593L16.8099,19.4279L16.8096,19.4284Q16.799799999999998,19.4463,16.7798,19.4463L8.720600000000001,19.4463Q8.70021,19.4463,8.69014,19.4279L4.63183,12.05605Q4.6212,12.03669,4.6317699999999995,12.01745L8.690529999999999,4.6448Q8.7003,4.62688,8.72016,4.62683L16.7793,4.62683Q16.7997,4.62688,16.8099,4.64551L20.8682,12.01733Q20.8788,12.03669,20.8682,12.05593ZM15.5205,14.7767Q16.6935,13.6037,16.6935,11.94478Q16.6935,10.28587,15.5205,9.112870000000001Q14.3475,7.93991,12.68865,7.93991Q11.02969,7.93991,9.856729999999999,9.112870000000001Q8.683769999999999,10.28583,8.683779999999999,11.94478Q8.683779999999999,13.6037,9.85674,14.7767Q11.02969,15.9497,12.68865,15.9497Q14.3475,15.9497,15.5205,14.7767ZM15.2376,9.39572Q16.2935,10.45156,16.2935,11.94478Q16.2935,13.438,15.2376,14.4938Q14.1818,15.5497,12.68865,15.5497Q11.19538,15.5497,10.139579999999999,14.4939Q9.083770000000001,13.438,9.08378,11.94478Q9.083770000000001,10.45152,10.139579999999999,9.395710000000001Q11.19538,8.33991,12.68865,8.33991Q14.1818,8.33991,15.2376,9.39572ZM10.865680000000001,10.12181Q10.1106,10.87689,10.1106,11.94478Q10.1106,13.0127,10.865680000000001,13.7677Q11.62076,14.5228,12.68865,14.5228Q13.7565,14.5228,14.5116,13.7677Q15.2667,13.0127,15.2667,11.94478Q15.2667,10.87689,14.5116,10.12181Q13.7565,9.36674,12.68865,9.36674Q11.62076,9.36674,10.865680000000001,10.12181ZM11.14852,13.4849Q10.5106,12.84698,10.5106,11.94478Q10.5106,11.04258,11.14852,10.40466Q11.78644,9.76674,12.68865,9.76674Q13.5909,9.76674,14.2288,10.40466Q14.8667,11.04258,14.8667,11.94478Q14.8667,12.84698,14.2288,13.4849Q13.5909,14.1228,12.68865,14.1228Q11.78644,14.1228,11.14852,13.4849Z"
                    fillRule="evenodd"
                    fillOpacity="1"
                />
            </g>
        </g>
    </svg>
);

export const editTokenDisableIcon = () => (
    <svg xmlns="http://www.w3.org/2000/svg" fill="none" version="1.1" width="24" height="24" viewBox="0 0 24 24">
        <g>
            <g />
            <g>
                <path
                    d="M22.2917,12.84678L18.2262,20.2187C17.9379,20.7446,17.386400000000002,21.072,16.7867,21.0731L8.71327,21.0731C8.11359,21.072,7.56206,20.7446,7.27384,20.2187L3.208329,12.84678C2.9305569,12.34235,2.9305569,11.7308,3.208329,11.22637L7.27384,3.854432C7.56206,3.32856,8.11359,3.00117567,8.71327,3L16.7867,3C17.386400000000002,3.00117567,17.9379,3.32856,18.2262,3.854432L22.2917,11.22637C22.5694,11.7308,22.5694,12.34235,22.2917,12.84678ZM21.0434,12.15239C21.083,12.08034,21.083,11.99305,21.0434,11.921L16.9851,4.54906C16.9441,4.47391,16.8654,4.42706,16.7798,4.42683L8.72016,4.42683C8.63457,4.42706,8.55591,4.47391,8.51494,4.54906L4.45657,11.921C4.41697,11.99305,4.41697,12.08034,4.45657,12.15239L8.51494,19.5243C8.55597,19.5994,8.63462,19.6461,8.72016,19.6463L16.7798,19.6463C16.8654,19.6461,16.944000000000003,19.5994,16.9851,19.5243L21.0434,12.15239ZM12.68865,15.7497C10.58717,15.7497,8.88377,14.0463,8.88377,11.94478C8.88377,9.8433,10.58717,8.13991,12.68865,8.13991C14.7899,8.13991,16.493499999999997,9.8433,16.493499999999997,11.94478C16.493499999999997,14.0463,14.7899,15.7497,12.68865,15.7497ZM12.68865,14.3228C14.002,14.3228,15.0667,13.2582,15.0667,11.94478C15.0667,10.63139,14.002,9.56674,12.68865,9.56674C11.37525,9.56674,10.3106,10.63139,10.3106,11.94478C10.3106,13.2582,11.37525,14.3228,12.68865,14.3228Z"
                    fill="#B3BBC8"
                    fillOpacity="1"
                />
                <path
                    d="M22.4669,12.94325Q22.9662,12.03658,22.4669,11.1299L22.4668,11.12979L18.4015,3.758307Q17.877499999999998,2.802138,16.7867,2.8L8.71288,2.8Q7.62251,2.802138,7.0987,3.757849L3.0331361,11.1299Q2.533852,12.03658,3.0331960000000002,12.94336L7.09845,20.3148Q7.62251,21.271,8.71327,21.2731L16.787100000000002,21.2731Q17.877499999999998,21.271,18.4013,20.3153L22.4669,12.94325ZM22.1165,11.32285L22.1165,11.32296Q22.5095,12.03663,22.1165,12.7503L18.051000000000002,20.1221Q17.6403,20.8715,16.7863,20.8731L8.71327,20.8731Q7.85968,20.8715,7.44922,20.1226L3.383463,12.75019Q2.99049079,12.03658,3.383523,11.32285L7.44897,3.951015Q7.85967,3.201674,8.71366,3.2L16.7867,3.2Q17.6403,3.201674,18.050800000000002,3.950557L22.1165,11.32285ZM17.1603,19.6208L17.1605,19.6203L21.2186,12.24884Q21.3352,12.0367,21.2187,11.82467L17.1603,4.45261Q17.037599999999998,4.22753,16.7804,4.22683L8.72016,4.22683Q8.46245,4.22753,8.33934,4.45332L4.28136,11.82455Q4.16475,12.03669,4.2813,12.24872L8.33973,19.6208Q8.46273,19.8458,8.71973,19.8463L16.7798,19.8463Q17.037,19.8458,17.1603,19.6208ZM20.8682,12.05593L16.8099,19.4279L16.8096,19.4284Q16.799799999999998,19.4463,16.7798,19.4463L8.720600000000001,19.4463Q8.70021,19.4463,8.69014,19.4279L4.63183,12.05605Q4.6212,12.03669,4.6317699999999995,12.01745L8.690529999999999,4.6448Q8.7003,4.62688,8.72016,4.62683L16.7793,4.62683Q16.7997,4.62688,16.8099,4.64551L20.8682,12.01733Q20.8788,12.03669,20.8682,12.05593ZM15.5205,14.7767Q16.6935,13.6037,16.6935,11.94478Q16.6935,10.28587,15.5205,9.112870000000001Q14.3475,7.93991,12.68865,7.93991Q11.02969,7.93991,9.856729999999999,9.112870000000001Q8.683769999999999,10.28583,8.683779999999999,11.94478Q8.683779999999999,13.6037,9.85674,14.7767Q11.02969,15.9497,12.68865,15.9497Q14.3475,15.9497,15.5205,14.7767ZM15.2376,9.39572Q16.2935,10.45156,16.2935,11.94478Q16.2935,13.438,15.2376,14.4938Q14.1818,15.5497,12.68865,15.5497Q11.19538,15.5497,10.139579999999999,14.4939Q9.083770000000001,13.438,9.08378,11.94478Q9.083770000000001,10.45152,10.139579999999999,9.395710000000001Q11.19538,8.33991,12.68865,8.33991Q14.1818,8.33991,15.2376,9.39572ZM10.865680000000001,10.12181Q10.1106,10.87689,10.1106,11.94478Q10.1106,13.0127,10.865680000000001,13.7677Q11.62076,14.5228,12.68865,14.5228Q13.7565,14.5228,14.5116,13.7677Q15.2667,13.0127,15.2667,11.94478Q15.2667,10.87689,14.5116,10.12181Q13.7565,9.36674,12.68865,9.36674Q11.62076,9.36674,10.865680000000001,10.12181ZM11.14852,13.4849Q10.5106,12.84698,10.5106,11.94478Q10.5106,11.04258,11.14852,10.40466Q11.78644,9.76674,12.68865,9.76674Q13.5909,9.76674,14.2288,10.40466Q14.8667,11.04258,14.8667,11.94478Q14.8667,12.84698,14.2288,13.4849Q13.5909,14.1228,12.68865,14.1228Q11.78644,14.1228,11.14852,13.4849Z"
                    fillRule="evenodd"
                    fill="#B3BBC8"
                    fillOpacity="1"
                />
            </g>
        </g>
    </svg>
);

export const plusIcon = () => (
    <svg xmlns="http://www.w3.org/2000/svg" fill="none" version="1.1" width="16" height="16" viewBox="0 0 16 16">
        <g>
            <g />
            <g>
                <rect x="1" y="7" width="14" height="2" rx="1" fill="#FFFFFF" fillOpacity="1" />
            </g>
            <g transform="matrix(0,1,-1,0,10,-8)">
                <path
                    d="M9,2C9,2.55228,9.447715,3,10,3L22,3C22.552300000000002,3,23,2.55228,23,2C23,1.447715,22.552300000000002,1,22,1L10,1C9.447715,1,9,1.447715,9,2Z"
                    fill="#FFFFFF"
                    fillOpacity="1"
                />
            </g>
        </g>
    </svg>
);

export const plusDisableIcon = () => (
    <svg xmlns="http://www.w3.org/2000/svg" fill="none" version="1.1" width="16" height="16" viewBox="0 0 16 16">
        <g>
            <g />
            <g>
                <rect x="1" y="7" width="14" height="2" rx="1" fill="#B3BBC8" fillOpacity="1" />
            </g>
            <g transform="matrix(0,1,-1,0,10,-8)">
                <path
                    d="M9,2C9,2.55228,9.447715,3,10,3L22,3C22.552300000000002,3,23,2.55228,23,2C23,1.447715,22.552300000000002,1,22,1L10,1C9.447715,1,9,1.447715,9,2Z"
                    fill="#B3BBC8"
                    fillOpacity="1"
                />
            </g>
        </g>
    </svg>
);

export const confirmIcon = () => (
    <svg xmlns="http://www.w3.org/2000/svg" fill="none" version="1.1" width="16" height="16" viewBox="0 0 16 16">
        <g>
            <path
                d="M8,0C3.58212,0,0,3.58212,0,8C0,12.4179,3.58212,16,8,16C12.4179,16,16,12.4179,16,8C16,3.58212,12.4179,0,8,0ZM8,13.1774C7.48047,13.1774,7.05882,12.7558,7.05882,12.2362C7.05882,11.7167,7.48047,11.2951,8,11.2951C8.51953,11.2951,8.94118,11.7167,8.94118,12.2362C8.94118,12.7558,8.51953,13.1774,8,13.1774ZM8.94118,9.41177C8.94118,9.9313,8.51953,10.3529,8,10.3529C7.48047,10.3529,7.05882,9.9313,7.05882,9.41177L7.05882,4.23529C7.05882,3.71577,7.48047,3.29412,8,3.29412C8.51953,3.29412,8.94118,3.71576,8.94118,4.23529L8.94118,9.41177Z"
                fill="#FF9F30"
                fillOpacity="1"
            />
        </g>
    </svg>
);
