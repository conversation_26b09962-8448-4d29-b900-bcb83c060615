import React, {useEffect, useState} from "react";
import {useSelector} from "react-redux";
import {Collapse, Timeline, Empty} from "antd";
import Icon, {LoadingOutlined} from "@ant-design/icons";
import {CustomTable} from "@/modules-otn/components/common/custom_table";
import {apiNEUpgrade, objectGet} from "@/modules-otn/apis/api";
import {NEStateConfig} from "@/modules-otn/config/state_config";
import useUserRight from "@/modules-otn/hooks/useUserRight";
import {gLabelList} from "@/store/modules/otn/languageOTNSlice";
import {NE_TYPE_CONFIG} from "@/modules-otn/utils/util";
import {uploadDisabledIcon, uploadEnabledIcon} from "@/modules-otn/pages/otn/device/device_icons";
import {middleModal} from "@/modules-otn/components/modal/custom_modal";
import CustomEmpty from "@/modules-otn/components/common/custom_empty";
import upgradeStyles from "./upgrade.module.scss";

const {Panel} = Collapse;

const Upgrade = () => {
    const onlyOTN = import.meta.env.VITE_APP_EXPORT_MODULE === "AmpCon-T";
    const {upgrade} = useSelector(state => state.notification);
    const [commonData, setCommonData] = useState({
        allData: {}
    });

    const [neTableSectionType, setNeTableSectionType] = useState("checkout");
    const [reloadDataList, setReloadDataList] = useState(false);
    const userRight = useUserRight();

    useEffect(() => {
        setNeTableSectionType("checkout");
    }, [commonData]);

    const upgradeDisabled =
        !commonData?.images ||
        !commonData?.upgrade ||
        !commonData?.upgrade?.selectedRows?.some(a => a.state === 1 || a.state < 0) ||
        userRight.disabled;

    const onlyOtnStyle = onlyOTN ? {padding: "32px 24px 0", borderRadius: 5, overflow: "auto"} : {};

    return (
        <div
            style={{
                background: "#FFF",
                flex: 1,
                display: "flex",
                flexDirection: "column",
                gap: "24px", // 设置两个表格之间的间距为 40px,页码下边距是16px
                ...onlyOtnStyle
            }}
            className={upgradeStyles.container}
        >
            <CustomTable
                type="images"
                scroll={false}
                tableID="images_file"
                rootStyle={!commonData?.allData?.images?.length ? {marginBottom: 24} : {}}
                execRefresh={reloadDataList}
                commonData={commonData}
                search
                refreshParent={() => {
                    setReloadDataList(!reloadDataList);
                }}
                setCommonData={setCommonData}
                buttons={[
                    {
                        label: "upload",
                        upload: {
                            name: "upgrade",
                            accept: [".bin"],
                            api: "../../otn/api/file/upload?type=upgrade"
                        }
                    }
                ]}
                clearData={(selectedRows, oldData) => {
                    const filterNEType = selectedRows?.[0]?.type;
                    if (filterNEType) {
                        if (oldData.upgrade?.selectedRows?.find(i => i.type !== filterNEType)) {
                            return true;
                        }
                    }
                    return false;
                }}
                columnFormat={{
                    type: state => {
                        return NE_TYPE_CONFIG[parseInt(state)];
                    }
                }}
            />
            <CustomTable
                type="upgrade"
                scroll={false}
                execRefresh={upgrade}
                sectionTypeProps={neTableSectionType}
                search
                buttons={[
                    {
                        label: "upgrade",
                        confirm: {
                            title: "sure_upgrade"
                        },
                        type: "primary",
                        icon: <Icon component={upgradeDisabled ? uploadDisabledIcon : uploadEnabledIcon} />,
                        disabled: upgradeDisabled,
                        onClick: () => {
                            const neList = [];
                            commonData.upgrade.selectedRows.forEach(item => {
                                neList.push(item.ne_id);
                            });
                            apiNEUpgrade(neList, commonData.images.selectedRows[0].name, "upgrade").then();
                        }
                    }
                ]}
                checkboxProps={record => {
                    const t5 = record?.type === "5" && commonData?.images?.selectedRows?.[0]?.type !== "5";
                    const state = record?.state === 0 || record?.state >= 2;
                    return {disabled: state || (commonData?.images?.selectedRows?.[0] && t5)};
                }}
                columnFormat={{
                    type: state => {
                        return NE_TYPE_CONFIG[parseInt(state)];
                    },
                    state: (state, data) => {
                        try {
                            const {upgradeType} = data;
                            let _state = state < 0 ? "error" : state;
                            if (upgradeType !== "upgrade" && state < 0) {
                                _state = 1;
                            }
                            return (
                                <span>
                                    {NEStateConfig[upgradeType][_state].icon}{" "}
                                    {gLabelList[NEStateConfig[upgradeType][_state].title2]}
                                </span>
                            );
                        } catch (e) {
                            return state;
                        }
                    }
                }}
                initRowOperation={[
                    {
                        label: "history",
                        async onClick() {
                            // eslint-disable-next-line react/no-this-in-sfc
                            openUpgradeHistory(this.ne_id);
                        }
                    }
                ]}
                setCommonData={setCommonData}
                commonData={commonData}
            />
        </div>
    );
};

const UpgradeHistory = ({ne_id}) => {
    const {upgrade} = useSelector(state => state.notification);
    const [upgradeInfo, setUpgradeInfo] = useState();

    useEffect(() => {
        if (upgrade && upgradeInfo && upgrade?.source === upgradeInfo?.value.ne_id) {
            updateUpgradeInfo(upgrade.source).then();
        }
    }, [upgrade]);

    const updateUpgradeInfo = () => {
        objectGet("config:ne", {ne_id}).then(rs => {
            setUpgradeInfo(rs.documents[0]);
        });
    };

    useEffect(() => {
        updateUpgradeInfo();
    }, []);

    const getNEState = rs => {
        return rs.value?.upgrade_type === "upgrade" && rs.value?.state > 1;
    };

    return upgradeInfo ? (
        <div className={upgradeStyles.upgrade}>
            <Collapse
                expandIconPosition="end"
                accordion
                defaultActiveKey={[upgradeInfo?.value.upgrade?.[0]?.startTime]}
            >
                {Array.isArray(upgradeInfo?.value?.upgrade) && upgradeInfo.value.upgrade.length ? (
                    upgradeInfo?.value.upgrade?.map((item, index) => {
                        try {
                            return (
                                <Panel
                                    header={item.startTime}
                                    key={item.startTime}
                                    headerStyle={{backgroundColor: "rgba(0, 0, 0, 0.02)"}}
                                >
                                    <Timeline
                                        mode="left"
                                        pending={
                                            index === 0 && getNEState(upgradeInfo) ? (
                                                <a>{gLabelList.upgrading}</a>
                                            ) : (
                                                false
                                            )
                                        }
                                    >
                                        {item.step.map(item2 => {
                                            const _upgradeType = "upgrade";
                                            return (
                                                <Timeline.Item
                                                    key={item2.time}
                                                    color={item2.state < 0 ? "red" : "blue"}
                                                    label={item2.time}
                                                >
                                                    {
                                                        gLabelList[
                                                            NEStateConfig[_upgradeType][Math.abs(item2.state)]?.title
                                                        ]
                                                    }
                                                    {item2.state < 0 ? ` (${gLabelList.failed})` : ""}
                                                    {item2.desc ? ` ${item2.desc}` : ""}
                                                </Timeline.Item>
                                            );
                                        })}
                                    </Timeline>
                                </Panel>
                            );
                        } catch (e) {
                            return <div>{e.toString()}</div>;
                        }
                    })
                ) : (
                    <CustomEmpty />
                )}
            </Collapse>
        </div>
    ) : (
        <div style={{width: "100%", textAlign: "center"}}>
            <a>
                <LoadingOutlined style={{fontSize: 32, fill: "#14C9BB", color: "#14C9BB"}} />
            </a>
        </div>
    );
};

const openUpgradeHistory = ne_id => {
    middleModal({
        title: ne_id,
        content: <UpgradeHistory ne_id={ne_id} />,
        footer: <div style={{height: 32, borderTop: "none"}} className="ant-modal-confirm-btns" />
    });
};

export default Upgrade;
