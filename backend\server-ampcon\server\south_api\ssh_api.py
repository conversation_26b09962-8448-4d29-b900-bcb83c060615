import json
import logging
import re
import socket

from server import constants as C
from server import util
from server.util import utils, ssh_util, ssh_helper
from server import constants
from server.db.models import inventory
from server.db.models.inventory import Switch

import platform
if platform.system() != 'Windows':
    from server.collect.rma_collect import collect_backup_config_single_group, collect_backup_config_single

inven_db = inventory.inven_db
LOG = logging.getLogger(__name__)

ssh_conn = None
# port status regexes
mac_regex = re.compile('(([0-9a-f]{2}:){5}[0-9a-f]{2})', re.I)
port_name_re = re.compile('interface: (.e-1/1/[0-9]+|ae[0-9]+)', re.I)
link_status_re = re.compile('Physical link is (Down|Up)', re.I)
speed_re = re.compile('Speed: (Auto|[0-9]+[\.]{0,1}[0-9]?M|1Gb|2.5Gb|10Gb|25Gb|40Gb|100Gb|[0-9]+Gb)', re.I)
input_oct_re = re.compile('Input Octets\.+([0-9]+)', re.I)
output_oct_re = re.compile('Output Octets\.+([0-9]+)', re.I)
drop_oct_re = re.compile('Discarded Packets\.+([0-9]+)', re.I)
mtu_re = re.compile('MTU:\s+(\d+)', re.I)
admin_status_re = re.compile('(Enabled|Disabled), error-discard ', re.I)
flow_control_re = re.compile('Flow control: (Disabled|Enabled)', re.I)
description_re = re.compile('Description: (.*)\r\r\n', re.I)

def get_ssh_conn():
    global ssh_conn
    if not ssh_conn:
        ssh_conn = util.get_ssh_conn()
    return ssh_conn


# @utils.update_switch_status1
def get_neighbors(host, user=None, password=None):
    pass


# @utils.update_switch_status1
def apply_configs(host, configs, sn=None, user=None, password=None):
    if not user or not password:
        user, password = utils.get_switch_default_user(sn=sn)

    timeout = 10 if len(configs) < 300 else 240

    # before commit config, commit confirmed config is ok
    res, status = ssh_util.interactive_shell_configure(configs, host, username=user,
                                                       password=password, timeout=timeout, action='commit confirmed')
    if status != C.RMA_ACTIVE:
        return res, status

    # if commit confirmed ok
    res, status = ssh_util.interactive_shell_configure('commit', host, username=user, password=password,
                                                       timeout=timeout)
    # Then, ampcon will retrieve the config to get snapshot, firstly, we need get sn
    switch_entry = inven_db.get_collection(Switch, filters={'mgt_ip': [host]})
    if switch_entry:
        sn = switch_entry[0].sn
        try:
            if collect_backup_config_single(host, sn) == constants.RMA_ACTIVE:
                inven_db.add_switch_log(sn, "Retrieve config success", level='info')
                ssh_util.interactive_shell_linux("type -p save_config >/dev/null 2>&1 && sudo save_config", host,
                                                 username=user, password=password)
                msg = {'status': '200', 'info': 'back-up success'}
            else:
                inven_db.add_switch_log(sn, "Retrieve config failed", level='warn')
                msg = {'status': '400', 'info': 'back-up failed'}
        except:
            inven_db.add_switch_log(sn, "Retrieve config failed", level='warn')
    return res, status


def apply_configs_by_push_conf(host, configs, sn=None, user=None, password=None):
    if not user or not password:
        user, password = utils.get_switch_default_user(sn=sn)

    timeout = 10 if len(configs) < 300 else 240

    # before commit config, commit confirmed config is ok
    res, status = ssh_util.interactive_shell_configure(configs, host, username=user,
                                                       password=password, timeout=timeout, action='commit confirmed')
    if status != C.RMA_ACTIVE:
        return res, "", status

    # if commit confirmed ok
    res2, status = ssh_util.interactive_shell_configure('commit', host, username=user, password=password,
                                                       timeout=timeout)
    # Then, ampcon will retrieve the config to get snapshot, firstly, we need get sn
    switch_entry = inven_db.get_collection(Switch, filters={'mgt_ip': [host]})
    if switch_entry:
        sn = switch_entry[0].sn
        try:
            if collect_backup_config_single(host, sn) == constants.RMA_ACTIVE:
                ssh_util.interactive_shell_linux("type -p save_config >/dev/null 2>&1 && sudo save_config", host,
                                                 username=user, password=password)
                inven_db.add_switch_log(sn, "Retrieve config success", level='info')
            else:
                inven_db.add_switch_log(sn, "Retrieve config failed", level='warn')
        except:
            inven_db.add_switch_log(sn, "Retrieve config failed", level='warn')
    return res, res2, status


# @utils.update_switch_status1
def apply_configs_ignore_error(host, configs, sn=None, user=None, password=None):
    if not user or not password:
        user, password = utils.get_switch_default_user(sn=sn)

    timeout = 10 if len(configs) < 300 else 240
    cmd_lines = configs.split('\n')
    with ssh_util.open_interactive_configure_connection(host, username=user, password=password) as client:
        for cmd in cmd_lines:
            res, status = client.execute(cmd, error_fn=ssh_helper.ignore_stdout_error)
        # before commit config, commit confirmed config is ok
        res, status = client.execute('commit confirmed')
        res, status = client.execute('commit')
    # Then, ampcon will retrieve the config to get snapshot, firstly, we need get sn
    switch_entry = inven_db.get_collection(Switch, filters={'mgt_ip': [host]})
    if switch_entry:
        sn = switch_entry[0].sn
        try:
            if collect_backup_config_single(host, sn) == constants.RMA_ACTIVE:
                ssh_util.interactive_shell_linux("type -p save_config >/dev/null 2>&1 && sudo save_config", host,
                                                 username=user, password=password)
                inven_db.add_switch_log(sn, "Retrieve config success", level='info')
                msg = {'status': '200', 'info': 'back-up success'}
            else:
                inven_db.add_switch_log(sn, "Retrieve config failed", level='warn')
                msg = {'status': '400', 'info': 'back-up failed'}
        except:
            inven_db.add_switch_log(sn, "Retrieve config failed", level='warn')
        return res, status

# @utils.update_switch_status1
def push_file_to_switch(dest_host, user, password, local_file, remote_file):
    if not user or not password:
        user, password = utils.get_switch_default_user()
    return ssh_util.interactive_push_file(dest_host, user, password, local_file, remote_file)


# @utils.update_switch_status1
def get_port_stats(host, user=None, password=None):
    pass


def get_ports_brief(host, user=None, password=None):
    if not user or not password:
        user, password = utils.get_switch_default_user()
    res, status = ssh_util.interactive_shell_configure('run show interface brief | no-more',
                                                       host, username=user, password=password)
    if status != C.RMA_ACTIVE:
        return []
    lines = res.split('\r\n')[3:-1]
    ports = []
    compile_reg = re.compile('\s+')
    for line in lines:
        cols = compile_reg.split(line)
        name = cols[0]
        ports.append({'port_name': cols[0], 'management': cols[1].lower(),
                      'status': cols[2].upper(), 'flow_control': cols[3].lower(),
                      'duplex': cols[4].lower(), 'speed': cols[5],
                      'description': cols[6], 'type': name[:2]})
    return ports


# @utils.update_switch_status1
def get_running_configs(host, user=None, password=None):
    if not user or not password:
        user, password = utils.get_switch_default_user()

    return ssh_util.interactive_shell_linux("cat /pica/config/pica_startup.boot",
                                            host, username=user, password=password)


def get_hardware_id(host, user=None, password=None):
    if not user or not password:
        user, password = utils.get_switch_default_user()

    res, status = ssh_util.interactive_shell_linux("license -s | grep 'Hardware ID'",
                                                   host, username=user, password=password)
    if status != C.RMA_ACTIVE:
        return ""
    else:
        return re.findall("(([0-9A-Z]+-)+[0-9A-Z]+)", res)[0][0]


def get_port_status(host, user=None, password=None):
    if not user or not password:
        user, password = utils.get_switch_default_user()

    res, status = ssh_util.interactive_shell_configure('run show interface detail | no-more',
                                                       host, username=user, password=password,
                                                       error_fn=ssh_helper.ignore_stdout_error)
    if status != C.RMA_ACTIVE:
        return []
    ports_detail = res.split('\r\r\n\r\r\n')
    port_info = []
    for port_detail in ports_detail[:-1]:
        port_name = port_name_re.findall(port_detail)[0]
        link = link_status_re.findall(port_detail)[0]
        speed = speed_re.findall(port_detail)[0]
        input_oct = int(input_oct_re.findall(port_detail)[0])
        output_oct = int(output_oct_re.findall(port_detail)[0])
        drop_oct = drop_oct_re.findall(port_detail)
        input_drop_oct = int(drop_oct[1])
        output_drop_oct = int(drop_oct[0])
        mtu = int(mtu_re.findall(port_detail)[0])
        admin_status = admin_status_re.findall(port_detail)[0]
        flow_control = flow_control_re.findall(port_detail)[0]
        description = description_re.findall(port_detail)[0]
        if speed != 'Auto':
            band = int(speed[:-1]) if 'M' in speed else float(speed[:-2]) * 1000
            band_use_rate = round(float(input_oct + output_oct) / 8 / 1024 / 1024 / band * 100, 2)
        else:
            band_use_rate = 0

        input_drop_rate = round(float(input_drop_oct) / input_oct * 100, 2) if input_oct != 0 else 0
        output_drop_rate = round(float(output_drop_oct) / output_oct * 100, 2) if output_oct != 0 else 0
        drop_rate = round(float(output_drop_oct + input_drop_oct) / (input_oct + output_oct) * 100, 2) \
            if (input_oct + output_oct) != 0 else 0
        port_info.append({'port_name': port_name,
                          'link': link,
                          'speed': speed,
                          'mtu': mtu,
                          'inputOct': input_oct,
                          'outputOct': output_oct,
                          'inputDropOct': input_drop_oct,
                          'outputDropOct': output_drop_oct,
                          'band_use_rate': band_use_rate,
                          'input_drop_rate': input_drop_rate,
                          'output_drop_rate': output_drop_rate,
                          'drop_rate': drop_rate,
                          'admin_status': admin_status,
                          'description': description,
                          'flow_control': flow_control})
    return port_info


def get_vlan_status(host, user=None, password=None):
    if not user or not password:
        user, password = utils.get_switch_default_user()

    res, status = ssh_util.interactive_shell_configure('run show vlans detail | no-more',
                                                       host, username=user, password=password)
    if status != C.RMA_ACTIVE:
        return
    lines = res.split('\r\r\n')
    vlans = []
    cur_vlan = {}
    for line in lines[:-1]:
        value = line.split(':')[1].strip() if ':' in line else ''
        if 'VLAN ID' in line:
            if cur_vlan:
                vlans.append(cur_vlan)
                cur_vlan = {}
            cur_vlan['id'] = value
        elif 'VLAN Name' in line:
            cur_vlan['name'] = value
        elif 'Description' in line:
            cur_vlan['description'] = value
        elif 'vlan-interface' in line:
            cur_vlan['vlan_interface'] = value
        elif 'Number of member ports' in line:
            cur_vlan['port_number'] = int(value)
        elif 'Untagged port' in line:
            cur_vlan['untagged'] = value if value != 'None' else ''
        elif 'Tagged port' in line:
            cur_vlan['tagged'] = value if value != 'None' else ''
        elif 'tagged' in cur_vlan and line != '':
            cur_vlan['tagged'] += ',' + line.strip()
        elif 'untagged' in cur_vlan and line != '':
            cur_vlan['untagged'] += ',' + line.strip()
    if cur_vlan:
        vlans.append(cur_vlan)
    return vlans


def get_switch_system_static(host, user=None, password=None):
    if not user or not password:
        user, password = utils.get_switch_default_user()

    system_info = {}
    with ssh_util.open_interactive_configure_connection(host, username=user, password=password) as client:
        # get cpu_usage
        res, status = client.execute('run show system cpu-usage')
        if status == C.RMA_ACTIVE:
            cpu_usage = res.split('\r\r\n')[1:-1]
            system_info['cpu_usage'] = cpu_usage[0].split(':')[1].strip('%')
        else:
            system_info['cpu_usage'] = '0'

        # get temperature
        res, status = client.execute('run show system temperature')
        if status == C.RMA_ACTIVE:
            temperature = res.split('\r\r\n')[2:-1]
            try:
                system_info['board_temperature'] = float(temperature[0].split(':')[1].strip().split(' ')[0])
            except:
                system_info['board_temperature'] = 0
            try:
                system_info['asic_temperature'] = float(temperature[1].split(':')[1].strip().split(' ')[0])
            except:
                system_info['asic_temperature'] = 0
        else:
            system_info['board_temperature'] = 0
            system_info['asic_temperature'] = 0

        # get system rpsu
        res, status = client.execute('run show system rpsu')
        if status == C.RMA_ACTIVE:
            system_info['rpsu'] = "\n".join(res.split("\n")[1:-1])
        else:
            system_info['rpsu'] = ''

            # get system uptime
        res, status = client.execute('run show system uptime')
        if status == C.RMA_ACTIVE:
            uptime = res.split('\r\r\n')[1:-1]
            system_info['uptime'] = re.findall('up\s+([\s\d\w:]+),', uptime[0])[0]
        else:
            system_info['uptime'] = ''

        # get memory_usage
        res, status = client.execute('run show system memory-usage')
        if status == C.RMA_ACTIVE:
            memory_usage = res.split('\r\r\n')
            mem_info = [i for i in memory_usage if "Mem:" in i][0]
            mem_info_list = [i for i in mem_info.split(' ') if i]
            system_info['memory_total'] = mem_info_list[1]
            system_info['memory_use'] = mem_info_list[2]
        else:
            system_info['memory_total'] = 0
            system_info['memory_use'] = 0

            # get system spanning tree
        res, status = client.execute('run show spanning-tree')
        if status == C.RMA_ACTIVE:
            if 'MSTP' in res:
                # tmp_info_lines = res.split("\n")[3:-1]
                revise_info_list = res.split("\n")[3:-1]
                # for line in tmp_info_lines:
                #     for key_str in ["Hello Time", "Configuration", 'Level', 'Remaining', 'Maximum']:
                #         if key_str in line:
                #             revise_info_list.remove(line)
                #             continue
                system_info['stp'] = "\n".join(revise_info_list)
            else:
                system_info['stp'] = "\n".join(res.split("\n")[3:-4])
        else:
            system_info['stp'] = ''

        # get system logs
        res, status = client.execute('run show log last-rows 30 | no-more', error_fn=ssh_helper.ignore_stdout_error)
        if status == C.RMA_ACTIVE:
            system_info['logs'] = "\n".join(res.split("\n")[1:-1])
        else:
            system_info['logs'] = ''

            # get port poe enable/disable
        res, status = client.execute('show all poe|display set | no-more', error_fn=ssh_helper.ignore_stdout_error)
        if status == C.RMA_ACTIVE:
            system_info['port_poe_enable'] = str(re.findall("set poe interface ([a-z]e-1/1/[0-9]+) enable true",res))

            res, status = client.execute('run show poe interface all | no-more', error_fn=ssh_helper.ignore_stdout_error)
            if status == C.RMA_ACTIVE:
                
                if 'syntax error' in res:
                    system_info['poe_info'] = '[]'
                else:
                    system_info['poe_info'] = []

                    poe_info = res.split('\r\r\n')[3:-1]
                    for line in poe_info:
                        try:
                            line_list = [ i for i in line.split(' ') if i]
                            tmpPOEInfo = {
                                'port':          line_list[0],
                                'status':        line_list[1],
                                'consume':       line_list[2],
                                'reserved':      line_list[3],
                                'pair':          line_list[4],
                                'pd_type':       line_list[5],
                                'pd_class':      line_list[6],
                                'detection_type':' '.join(line_list[7:])
                            }
                            system_info['poe_info'].append(tmpPOEInfo)
                        except Exception as e:
                            LOG.error(e)
                    system_info['poe_info'] = json.dumps(system_info['poe_info'])
            else:
                system_info['poe_info'] = '[]'
        else:
            system_info['port_poe_enable'] = '[]'
            system_info['poe_info'] = '[]'

    return system_info


def get_device_info(host, user=None, password=None):
    if not user or not password:
        user, password = utils.get_switch_default_user()

    try:
        with ssh_util.open_interactive_connection(host, username=user, password=password) as client:
            # sn platform version revision mac hwid

            retry_time = 0

            while retry_time < 3:
                res, code = client.execute('/pica/bin/system/fan_status -s')
                if code == C.RMA_ACTIVE:
                    sn = re.findall("MotherBoard Serial Number : (.*)", res)[0].strip()
                    break
                retry_time += 1
                if retry_time == 3:
                    sn = None

            retry_time = 0
            while retry_time < 3:
                res, code = client.execute('/pica/bin/system/pica_switch_mac')
                if code == C.RMA_ACTIVE:
                    matches = mac_regex.findall(res)
                    mac = matches[0][0] if matches else None
                    break
                retry_time += 1
                if retry_time == 3:
                    mac = None

            retry_time = 0
            while retry_time < 3:
                res, code = client.execute('version')
                if code == C.RMA_ACTIVE:
                    platform = re.findall("Hardware Model.*:\s+(.*)\n", res)[0].strip()
                    if 'as' in platform.lower():
                        platform = platform.lower()
                    current_version = list(map(lambda x: x[0] if x[0] else x[1], re.findall("L2/L3 Version/Revision.*:\s+(.*?)\n|PICOS Release/Commit.*:\s+(.*?)\n", res)))[0].strip()
                    version, _, revision = current_version.partition('/')
                    break

                retry_time += 1
                if retry_time == 3:
                    platform = None
                    version = None
                    revision = None

            retry_time = 0
            while retry_time < 3:
                res, code = client.execute('/usr/sbin/license -s')
                if code == C.RMA_ACTIVE:
                    s = res.find('{')
                    e = res.find('}')

                    if s == -1 or e == -1:
                        LOG.warn('switch %s have no license yet', host)
                        lines = res.split('\n')
                        sw_type, _, sw_type_value = lines[1].strip().partition(':')
                        sw_hwid, _, hwid_value = lines[2].strip().partition(':')
                        license_info = {sw_type: sw_type_value, sw_hwid: hwid_value}
                    else:
                        license_info_str = res[s:e + 1]
                        license_info = json.loads(license_info_str)
                    break
                retry_time += 1
                if retry_time == 3:
                    license_info = None

            return {'sn': sn, 'mac': mac, 'platform': platform, 'version': version,
                    'revision': revision, 'license_info': license_info,
                    'status': C.RMA_ACTIVE}
    except socket.timeout:
        return {'status': C.RMA_UN_REACHABLE}

def get_switch_running_config(host, sn=None, user=None, password=None, format='tree'):
    if not user or not password:
        user, password = utils.get_switch_default_user(sn=sn)

    running_config = 'Null'
    if format == 'tree':
        execute_command = 'show all | no-more'
    elif format == 'all_set':
        execute_command = 'show all | display set | no-more'
    elif format == "set":
        execute_command = 'show | display set | no-more'
    with ssh_util.open_interactive_configure_connection(host, username=user, password=password) as client:
        # get cpu_usage
        res, status = client.execute(execute_command)
        if status == C.RMA_ACTIVE:
            #need remove the blanks in head of lines
            running_config_list = res.split("\r\n")[1:-1]
            running_config_list_strip = []
            for config in running_config_list:
                running_config_list_strip.append(config[4:])
            running_config_strip="\n".join(running_config_list_strip)
        else:
            running_config_strip = 'Can not get config'
    return {'host': host, 'running_config': running_config_strip}


def enable_gnmi(ip, username, password):

    configs = "set protocols grpc enable true\nset protocols lldp enable true\n"
    res, status = ssh_util.interactive_shell_configure(configs, ip, username=username, 
                                                        password=password, timeout=30, action='commit confirmed')
    if status != constants.RMA_ACTIVE:
        LOG.error('Failed to enable grpc & lldp')
        return False
    
    res, status = ssh_util.interactive_shell_configure('commit', ip, username=username, 
                                                        password=password, timeout=30)
    if status != constants.RMA_ACTIVE:
        LOG.error('Failed to enable grpc & lldp')
        return False
    return True

if __name__ == '__main__':
    from server import cfg
    cfg.CONF(default_config_files=['../automation.ini'])
    print(get_port_status('************', user='admin', password='pica8'))
