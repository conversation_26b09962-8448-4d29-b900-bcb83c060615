    ST-OTN-MIB DEFINITIONS ::= BEGIN
 
        IMPORTS
            Integer32, Unsigned32, BITS, OBJECT-TYPE, MODULE-IDENTITY            
                FROM SNMPv2-SMI            
            DisplayString, RowStatus, DateAndTime, TruthValue, TEXTUAL-CONVENTION            
                FROM SNMPv2-TC            
            shelfId, slotNo, subSlotNo, portNo, subPortNo, StAvailabilityState            
                FROM ST-COMMON-MIB            
            enterpriseProducts            
                FROM ST-ROOT-MIB;
    

        stOTN MODULE-IDENTITY 
            LAST-UPDATED "201704191143Z"        -- April 19, 2017 at 11:43 GMT
            ORGANIZATION 
                ""
            CONTACT-INFO 
                ""
            DESCRIPTION 
                "OTN MIB"
            ::= { enterpriseProducts 20 }

-- 
-- Textual conventions
-- 
        SEtyType ::= TEXTUAL-CONVENTION
            STATUS current
            DESCRIPTION 
                " "
            SYNTAX INTEGER
                {
                none(0),
                fe<PERSON><PERSON>r(1),
                gettt(2),
                xgeOpu2e(3),
                xgeGfpOpu2(4),
                xgeGfpOpu2e(5),
                ge(6),
                ge100Gmp(7),
                ge100Gfpf(8),
                ge40Gmp(9),
                ge40Gfpf(10)
                }

--  Textual conventions
-- 
-- Textual conventions
-- 
        SOtnEnabled ::= TEXTUAL-CONVENTION
            STATUS current
            DESCRIPTION 
                " "
            SYNTAX INTEGER
                {
                disabled(0),
                enabled(1)
                }

        SOtnUpDown ::= TEXTUAL-CONVENTION
            STATUS current
            DESCRIPTION 
                " "
            SYNTAX INTEGER
                {
                down(0),
                up(1)
                }

        SLoopBackMode ::= TEXTUAL-CONVENTION
            STATUS current
            DESCRIPTION 
                " "
            SYNTAX INTEGER
                {
                none(0),
                outwardNear(1),
                inwardFar(2),
                outwardFar(3),
                inwardNear(4)
                }

        SMapMode ::= TEXTUAL-CONVENTION
            STATUS current
            DESCRIPTION 
                " "
            SYNTAX INTEGER
                {
                none(255),
                amp(0),
                bmp(1),
                gmp(2),
                gfp(3),
                cbr(4)
                }

        SOtnYesNo ::= TEXTUAL-CONVENTION
            STATUS current
            DESCRIPTION 
                " "
            SYNTAX INTEGER
                {
                no(0),
                yes(1),
                na(2)
                }

        SOcStmType ::= TEXTUAL-CONVENTION
            STATUS current
            DESCRIPTION 
                " "
            SYNTAX INTEGER
                {
                none(0),
                stm1(1),
                stm4(2),
                stm16(3),
                stm64(4),
                stm256(5),
                oc3(6),
                oc12(7),
                oc48(8),
                oc192(9),
                oc768(10),
                fc100(11),
                fc200(12),
                fc400(13),
                fc800(14),
                fc1200(15)
                }

        TraceIdentifierSDH ::= TEXTUAL-CONVENTION
            STATUS    current
            DESCRIPTION    "Trail trace identifier for SDH/SONET, either
                - 15 byte printable characters + CRC byte or
                - 62 byte printable characters + CRC byte."
            SYNTAX    OCTET STRING 
                ( 
                SIZE(16|64) 
                )
                
        TraceIdentifierSDHMsgType ::= TEXTUAL-CONVENTION
            STATUS    current
            DESCRIPTION    "Format of SDH trail trace identifier."
            SYNTAX    INTEGER 
                {
                disabled (0),
                byte16 (1),
                byte64 (2)
                }
                
        SOduType ::= TEXTUAL-CONVENTION
            STATUS current
            DESCRIPTION 
                " "
            SYNTAX INTEGER
                {
                none(0),
                odu0(1),
                odu1(2),
                odu2(3),
                odu3(4),
                odu4(5),
                odu2e(6),
                oduflex(7),
                oduc2(8),
                oduc4(9)
                }

                
        SOduId ::= TEXTUAL-CONVENTION
            STATUS current
            DESCRIPTION 
                " "
            SYNTAX Unsigned32 (1..128)

        SOtuId ::= TEXTUAL-CONVENTION
            STATUS current
            DESCRIPTION 
                " "
            SYNTAX Unsigned32 (1..256)

        SOtuType ::= TEXTUAL-CONVENTION
            STATUS current
            DESCRIPTION 
                " "
            SYNTAX INTEGER
                {
                none(0),
                otu0ll(1),
                otu1(2),
                otu2(3),
                otu3(4),
                otu4(5),
                otu2e(6),
                otu2f(7),
                otu1e(8),
                otu1f(9),
                otuc2(10),
                otuc4(11)            
                }

        OperationalState ::= TEXTUAL-CONVENTION
            STATUS    current
            DESCRIPTION    
                "Indicates whether a resource is able to provide service."
            SYNTAX    INTEGER 
                {
                enabled (0),
                disabled (1)
                }        

               
        SOtnTruthValue ::= TEXTUAL-CONVENTION
            STATUS current
            DESCRIPTION 
                " "
            SYNTAX INTEGER
                {
                false(0),
                true(1)
                }

        SOchOsId ::= TEXTUAL-CONVENTION
            STATUS current
            DESCRIPTION 
                " "
            SYNTAX Unsigned32 (1..256)

             
-- 
-- Node definitions
-- 
        otnConfigMIB OBJECT IDENTIFIER ::= { stOTN 1 }

        
        sETYnTable OBJECT-TYPE
            SYNTAX SEQUENCE OF SETYnEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "ETYn Table"
            ::= { otnConfigMIB 4 }

        
        sETYnEntry OBJECT-TYPE
            SYNTAX SETYnEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                " "
            INDEX { shelfId, slotNo, subSlotNo, portNo, subPortNo
                 }
            ::= { sETYnTable 1 }

        
        SETYnEntry ::=
            SEQUENCE { 
                etynType
                    SEtyType,
                etynAdminState
                    SOtnEnabled,
                etynOperationalState
                    SOtnUpDown,
                etynAvailabilityState
                    StAvailabilityState,
                etynAlarmProfile
                    Integer32,
                etynPMProfile
                    Integer32,
                etynLoopBack
                    SLoopBackMode,
                etynUPIvalue
                    INTEGER,
                etynMapMode
                    SMapMode,
                etynExtChannel
                    DisplayString,
                etynClientShutdown
                    SOtnYesNo,
                etynClientShutdownHoldoffTimer
                    Integer32,
                etynNearEndALS
                    SOtnYesNo,
                etyn8023bmFEC
                    SOtnEnabled,
                etynRowStatus
                    RowStatus,
                etynFectype
                    INTEGER
             }

        etynType OBJECT-TYPE
            SYNTAX SEtyType
            MAX-ACCESS read-create
            STATUS current
            DESCRIPTION
                "Description."
            ::= { sETYnEntry 1 }

        
        etynAdminState OBJECT-TYPE
            SYNTAX SOtnEnabled
            MAX-ACCESS read-create
            STATUS current
            DESCRIPTION
                "Specifies the admin state of the entity.
                As to different object, the consequent aciton may be different, e.g. it may shutdown the port, or insert maintenance signal."
            DEFVAL { enabled }
            ::= { sETYnEntry 2 }

        
        etynOperationalState OBJECT-TYPE
            SYNTAX SOtnUpDown
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The operational state of the NE."
            ::= { sETYnEntry 3 }

        
        etynAvailabilityState OBJECT-TYPE
            SYNTAX StAvailabilityState
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "Avalaibility Status is to qualify the operational, usage and/or administrative state attributes"
            DEFVAL { normal }
            ::= { sETYnEntry 4 }

        
        etynAlarmProfile OBJECT-TYPE
            SYNTAX Integer32
            MAX-ACCESS read-create
            STATUS current
            DESCRIPTION
                "Specifies the alarm profile table for the entity."
            ::= { sETYnEntry 5 }

        
        etynPMProfile OBJECT-TYPE
            SYNTAX Integer32
            MAX-ACCESS read-create
            STATUS current
            DESCRIPTION
                "Specifies the alarm profile table for the entity."
            ::= { sETYnEntry 6 }

        
        etynLoopBack OBJECT-TYPE
            SYNTAX SLoopBackMode
            MAX-ACCESS read-create
            STATUS current
            DESCRIPTION
                " "
            DEFVAL { none }
            ::= { sETYnEntry 7 }

        
        etynUPIvalue OBJECT-TYPE
            SYNTAX INTEGER
                {
                gsupp43(1),
                g709(2)
                }
            MAX-ACCESS read-create
            STATUS current
            DESCRIPTION
                "Specifies the GFP UPI values transmitted for a TGLAN with
                TRANSMPA=PREAMBLE.
                - GSUPP43 - 0xFD for data frames and 0xFE for ordered sets.
                Provides compatibility with ITU-T Supplemental43
                recommendation.
                - G709 - 0x13 for data frames and 0x14 for ordered sets.
                Provides compatibility with ITU-T G.709 recommendation
                values first introduced in Amendment 3."
            DEFVAL { gsupp43 }
            ::= { sETYnEntry 8 }

        
        etynMapMode OBJECT-TYPE
            SYNTAX SMapMode
            MAX-ACCESS read-create
            STATUS current
            DESCRIPTION
                "Specifies the mapping mode from client to OPU/ODU, such as:
                (1) STMn/OCn: AMP, BMP
                (2) 10GE: GFP-F, GFP-Fp, BMP
                (3) 100GE/40GE: GMP, GFP-F
                ..."
            DEFVAL { gfp }
            ::= { sETYnEntry 9 }

        
        etynExtChannel OBJECT-TYPE
            SYNTAX DisplayString
            MAX-ACCESS read-create
            STATUS current
            DESCRIPTION
                "Specifies the DWDM frequency at which the interface will operate when the supporting pluggable for the facility is a tunable DWDM optics. 
                This parameter is used when directly connecting the module to
                a separate network element, rather than connecting to an optical
                multiplexer/demultiplex module within the same network
                element."
            ::= { sETYnEntry 10 }

        
        etynClientShutdown OBJECT-TYPE
            SYNTAX SOtnYesNo
            MAX-ACCESS read-create
            STATUS current
            DESCRIPTION
                "Setting the value to YES will shut down the output client port
                side laser on the transponder instead of sending a maintenance
                signal (e.g. AIS or LF depending on the type of facility
                provisioned) when there is a signal failure detected on the line
                side of a transponder.
                This value is typically used with external client side protection.
                Setting the value to NO will not shut down the laser on the port
                side of the transponder. When this value is set standard
                maintenance signaling will be used.
                Setting this value to NA will disable the protection functionality."
            DEFVAL { no }
            ::= { sETYnEntry 11 }

        
        etynClientShutdownHoldoffTimer OBJECT-TYPE
            SYNTAX Integer32
            MAX-ACCESS read-create
            STATUS current
            DESCRIPTION
                "Specifies a hold-off time between 60 msec and 1000 msec in
                increments of 5 msec before a  client port side laser shutdown upon a
                defect detected at line side. If no additional hold-off time for a port side laser shutdown is desired, then the value shall be set to zero.
                Note: This parameter is only supported when value of parameter 'ClientShutDown' is yes"
            DEFVAL { 0 }
            ::= { sETYnEntry 12 }

        
        etynNearEndALS OBJECT-TYPE
            SYNTAX SOtnYesNo
            MAX-ACCESS read-create
            STATUS current
            DESCRIPTION
                "This parameter, when set to YES, will shut down port side laser
                upon a port side incoming failure (such as LOS/LOF/LOSYNC)."
            DEFVAL { no }
            ::= { sETYnEntry 13 }

        
        etyn8023bmFEC OBJECT-TYPE
            SYNTAX SOtnEnabled
            MAX-ACCESS read-create
            STATUS current
            DESCRIPTION
                "enable/disable 100GE FEC functionality.
                Note: for SR4 100GE, according to 802.3bm standard, it should be always enabled"
            DEFVAL { disabled }
            ::= { sETYnEntry 14 }

        
        etynRowStatus OBJECT-TYPE
            SYNTAX RowStatus
            MAX-ACCESS read-create
            STATUS current
            DESCRIPTION
                "sETYnTable RowStatus"
            ::= { sETYnEntry 15 }

        etynFectype OBJECT-TYPE
            SYNTAX INTEGER
                {
                none(0),
                rs(1)
                }
            MAX-ACCESS read-create
            STATUS current
            DESCRIPTION
                ""
            DEFVAL { none }
            ::= { sETYnEntry 16 }

        
        sOCnSTMnTable OBJECT-TYPE
            SYNTAX SEQUENCE OF SOCnSTMnEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "OCn & STMn Table"
            ::= { otnConfigMIB 5 }

        
        sOCnSTMnEntry OBJECT-TYPE
            SYNTAX SOCnSTMnEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                " "
            INDEX { shelfId, slotNo, subSlotNo, portNo, subPortNo
                 }
            ::= { sOCnSTMnTable 1 }

        
        SOCnSTMnEntry ::=
            SEQUENCE { 
                ocnstmnType
                    SOcStmType,
                ocnstmnAdminState
                    SOtnEnabled,
                ocnstmnOperationalState
                    SOtnUpDown,
                ocnstmnAvailabilityState
                    StAvailabilityState,
                ocnstmnAlarmProfile
                    Integer32,
                ocnstmnPMProfile
                    Integer32,
                ocnstmnLoopBack
                    SLoopBackMode,
                ocnstmnMapMode
                    SMapMode,
                ocnstmnClientShutdown
                    SOtnYesNo,
                ocnstmnClientShutdownHoldoffTimer
                    Integer32,
                ocnstmnNearEndALS
                    SOtnYesNo,
                ocnstmnRowStatus
                    RowStatus,
                ocnstmnTIMDetectionMode
                    TraceIdentifierSDHMsgType,
                ocnstmnTraceIdExpected    
                    TraceIdentifierSDH,
                ocnstmnTraceIdReceived
                    TraceIdentifierSDH
             }

        ocnstmnType OBJECT-TYPE
            SYNTAX SOcStmType
            MAX-ACCESS read-create
            STATUS current
            DESCRIPTION
                "Specifies the admin state of the entity.
                As to different object, the consequent aciton may be different, e.g. it may shutdown the port, or insert maintenance signal."
            ::= { sOCnSTMnEntry 1 }

        
        ocnstmnAdminState OBJECT-TYPE
            SYNTAX SOtnEnabled
            MAX-ACCESS read-create
            STATUS current
            DESCRIPTION
                "Specifies the admin state of the entity.
                As to different object, the consequent aciton may be different, e.g. it may shutdown the port, or insert maintenance signal."
            DEFVAL { enabled }
            ::= { sOCnSTMnEntry 2 }

        
        ocnstmnOperationalState OBJECT-TYPE
            SYNTAX SOtnUpDown
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The operational state of the NE."
            DEFVAL { up }
            ::= { sOCnSTMnEntry 3 }

        
        ocnstmnAvailabilityState OBJECT-TYPE
            SYNTAX StAvailabilityState
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "Avalaibility Status is to qualify the operational, usage and/or administrative state attributes"
            DEFVAL { normal }
            ::= { sOCnSTMnEntry 4 }

        
        ocnstmnAlarmProfile OBJECT-TYPE
            SYNTAX Integer32
            MAX-ACCESS read-create
            STATUS current
            DESCRIPTION
                "Specifies the alarm profile table for the entity."
            ::= { sOCnSTMnEntry 5 }

        
        ocnstmnPMProfile OBJECT-TYPE
            SYNTAX Integer32
            MAX-ACCESS read-create
            STATUS current
            DESCRIPTION
                "Specifies the alarm profile table for the entity."
            ::= { sOCnSTMnEntry 6 }

        
        ocnstmnLoopBack OBJECT-TYPE
            SYNTAX SLoopBackMode
            MAX-ACCESS read-create
            STATUS current
            DESCRIPTION
                " "
            DEFVAL { none }
            ::= { sOCnSTMnEntry 7 }

        
        ocnstmnMapMode OBJECT-TYPE
            SYNTAX SMapMode
            MAX-ACCESS read-create
            STATUS current
            DESCRIPTION
                "Specifies the mapping mode from client to OPU/ODU, such as:
                (1) STMn/OCn: AMP, BMP
                (2) 10GE: GFP-F, GFP-Fp, BMP
                (3) 100GE/40GE: GMP, GFP-F
                ..."
            DEFVAL { amp }
            ::= { sOCnSTMnEntry 8 }

        
        ocnstmnClientShutdown OBJECT-TYPE
            SYNTAX SOtnYesNo
            MAX-ACCESS read-create
            STATUS current
            DESCRIPTION
                "Setting the value to YES will shut down the output client port
                side laser on the transponder instead of sending a maintenance
                signal (e.g. AIS or LF depending on the type of facility
                provisioned) when there is a signal failure detected on the line
                side of a transponder.
                This value is typically used with external client side protection.
                Setting the value to NO will not shut down the laser on the port
                side of the transponder. When this value is set standard
                maintenance signaling will be used.
                Setting this value to NA will disable the protection functionality."
            DEFVAL { no }
            ::= { sOCnSTMnEntry 9 }

        
        ocnstmnClientShutdownHoldoffTimer OBJECT-TYPE
            SYNTAX Integer32
            MAX-ACCESS read-create
            STATUS current
            DESCRIPTION
                "Specifies a hold-off time between 60 msec and 1000 msec in
                increments of 5 msec before a  client port side laser shutdown upon a
                defect detected at line side. If no additional hold-off time for a port side laser shutdown is desired, then the value shall be set to zero.
                Note: This parameter is only supported when value of parameter 'ClientShutDown' is yes"
            DEFVAL { 0 }
            ::= { sOCnSTMnEntry 10 }

        
        ocnstmnNearEndALS OBJECT-TYPE
            SYNTAX SOtnYesNo
            MAX-ACCESS read-create
            STATUS current
            DESCRIPTION
                "This parameter, when set to YES, will shut down port side laser
                upon a port side incoming failure (such as LOS/LOF/LOSYNC)."
            DEFVAL { no }
            ::= { sOCnSTMnEntry 11 }

        
        ocnstmnRowStatus OBJECT-TYPE
            SYNTAX RowStatus
            MAX-ACCESS read-create
            STATUS current
            DESCRIPTION
                "sOCnSTMnTable RowStatus"
            ::= { sOCnSTMnEntry 12 }

            
        ocnstmnTIMDetectionMode OBJECT-TYPE
            SYNTAX    TraceIdentifierSDHMsgType
            MAX-ACCESS    read-write
            STATUS    current
            DESCRIPTION    "Enables 16-bytes, 64-bytes or disables detection of dTIM defect for RS non-intrusive monitoring in ingress direction."
            DEFVAL    { disabled }
            ::=  { sOCnSTMnEntry  13 }


        ocnstmnTraceIdExpected OBJECT-TYPE
            SYNTAX    TraceIdentifierSDH
            MAX-ACCESS    read-write
            STATUS    current
            DESCRIPTION    "Expected RS trace identifier in ingress direction. Not effective if rsIngressTIMDetectionMode is 'off'."
            DEFVAL    { '89000000000000000000000000000000'H }
            ::=  { sOCnSTMnEntry  14 }

            
        ocnstmnTraceIdReceived OBJECT-TYPE
            SYNTAX    TraceIdentifierSDH
            MAX-ACCESS    read-only
            STATUS    current
            DESCRIPTION    "Received RS trace identifier in ingress direction."
            DEFVAL    { '89000000000000000000000000000000'H }
            ::=  { sOCnSTMnEntry  15 }
            
        
        sODUkPTable OBJECT-TYPE
            SYNTAX SEQUENCE OF SODUkPEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "ODUkP Table"
            ::= { otnConfigMIB 7 }

        
        sODUkPEntry OBJECT-TYPE
            SYNTAX SODUkPEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                " "
            INDEX { shelfId, slotNo, subSlotNo, portNo, subPortNo, 
                hoOduType, hoOduId, loOduType, loOduId }
            ::= { sODUkPTable 1 }

        
        SODUkPEntry ::=
            SEQUENCE { 
                hoOduType
                    SOduType,
                hoOduId
                    SOduId,
                loOduType
                    SOduType,
                loOduId
                    SOduId,
                odukpAdminState
                    SOtnEnabled,
                odukpOperationalState
                    SOtnUpDown,
                odukpAvailabilityState
                    StAvailabilityState,
                odukpAlarmProfile
                    Integer32,
                odukpPMProfile
                    Integer32,
                odukpExpSAPI
                    OCTET STRING,
                odukpExpDAPI
                    OCTET STRING,
                odukpExpOperator
                    OCTET STRING,
                odukpTxSAPI
                    OCTET STRING,
                odukpTxDAPI
                    OCTET STRING,
                odukpTxOperator
                    OCTET STRING,
                odukpRxSAPI
                    OCTET STRING,
                odukpRxDAPI
                    OCTET STRING,
                odukpRxOperator
                    OCTET STRING,
                odukpTIMDefectMode
                    INTEGER,
                odukpTIMActDis
                    SOtnTruthValue,
                odukpDegradeInterval
                    Integer32,
                odukpDegradeThreshold
                    Integer32,
                odukpDMSource
                    SOtnEnabled,
                odukpPrbs
                    INTEGER,
                odukpPrbsSync
                    INTEGER,
                odukpPrbsTimerDuration
                    DisplayString,
                odukpPrbsCalcBer
                    Integer32,
                odukpPrbsErrorCount
                    Integer32,
                odukpNull
                    SOtnEnabled,
                odukpNIM
                    SOtnEnabled,
                odukpRxPT
                    Integer32,
                odukpTxPT
                    Integer32,
                odukpExpPT
                    Integer32,
                odukpPLMConsequentActions
                    SOtnEnabled,
                odukpOpuConfig
                    INTEGER,
                odukpOpuConfigActual
                    INTEGER,
                odukpTribSlot
                    OCTET STRING,
                odukpExpClientRate
                    Integer32,
                odukpNumOfGfpTs
                    Integer32,
                odukpOperateRate
                    Integer32,
                odukpRowStatus
                    RowStatus
             }

        hoOduType OBJECT-TYPE
            SYNTAX SOduType
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                " "
            ::= { sODUkPEntry 1 }

        
        hoOduId OBJECT-TYPE
            SYNTAX SOduId
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                " "
            ::= { sODUkPEntry 2 }

        
        loOduType OBJECT-TYPE
            SYNTAX SOduType
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                " "
            ::= { sODUkPEntry 3 }

        
        loOduId OBJECT-TYPE
            SYNTAX SOduId
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                " "
            ::= { sODUkPEntry 4 }

        
        odukpAdminState OBJECT-TYPE
            SYNTAX SOtnEnabled
            MAX-ACCESS read-create
            STATUS current
            DESCRIPTION
                "Specifies the admin state of the entity.
                As to different object, the consequent aciton may be different, e.g. it may shutdown the port, or insert maintenance signal."
            DEFVAL { enabled }
            ::= { sODUkPEntry 5 }

        
        odukpOperationalState OBJECT-TYPE
            SYNTAX SOtnUpDown
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The operational state of the NE."
            DEFVAL { up }
            ::= { sODUkPEntry 6 }

        
        odukpAvailabilityState OBJECT-TYPE
            SYNTAX StAvailabilityState
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "Avalaibility Status is to qualify the operational, usage and/or administrative state attributes"
            DEFVAL { normal }
            ::= { sODUkPEntry 7 }

        
        odukpAlarmProfile OBJECT-TYPE
            SYNTAX Integer32
            MAX-ACCESS read-create
            STATUS current
            DESCRIPTION
                "Specifies the alarm profile table for the entity."
            ::= { sODUkPEntry 8 }

        
        odukpPMProfile OBJECT-TYPE
            SYNTAX Integer32
            MAX-ACCESS read-create
            STATUS current
            DESCRIPTION
                "Specifies the alarm profile table for the entity."
            ::= { sODUkPEntry 9 }

        
        odukpExpSAPI OBJECT-TYPE
            SYNTAX OCTET STRING (SIZE (16))
            MAX-ACCESS read-create
            STATUS current
            DESCRIPTION
                "Specifies 16 characters representing the expected SAPI."
            ::= { sODUkPEntry 10 }

        
        odukpExpDAPI OBJECT-TYPE
            SYNTAX OCTET STRING (SIZE (16))
            MAX-ACCESS read-create
            STATUS current
            DESCRIPTION
                "Specifies 16 characters representing the expected DAPI."
            ::= { sODUkPEntry 11 }

        
        odukpExpOperator OBJECT-TYPE
            SYNTAX OCTET STRING (SIZE (32))
            MAX-ACCESS read-create
            STATUS current
            DESCRIPTION
                "Specifies 32 characters representing the expected value for the
                Operator Specific area of the TTI."
            ::= { sODUkPEntry 12 }

        
        odukpTxSAPI OBJECT-TYPE
            SYNTAX OCTET STRING (SIZE (16))
            MAX-ACCESS read-create
            STATUS current
            DESCRIPTION
                "Specifies 16 characters representing the transmitted SAPI."
            ::= { sODUkPEntry 13 }

        
        odukpTxDAPI OBJECT-TYPE
            SYNTAX OCTET STRING (SIZE (16))
            MAX-ACCESS read-create
            STATUS current
            DESCRIPTION
                "Specifies 16 characters representing the transmitted DAPI."
            ::= { sODUkPEntry 14 }

        
        odukpTxOperator OBJECT-TYPE
            SYNTAX OCTET STRING (SIZE (32))
            MAX-ACCESS read-create
            STATUS current
            DESCRIPTION
                "Specifies 32 characters representing the transmitted value in
                the Operator Specific area of the TTI."
            ::= { sODUkPEntry 15 }

        
        odukpRxSAPI OBJECT-TYPE
            SYNTAX OCTET STRING (SIZE (16))
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "Specifies 16 characters representing the received SAPI."
            ::= { sODUkPEntry 16 }

        
        odukpRxDAPI OBJECT-TYPE
            SYNTAX OCTET STRING (SIZE (16))
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "Specifies 16 characters representing the received DAPI."
            ::= { sODUkPEntry 17 }

        
        odukpRxOperator OBJECT-TYPE
            SYNTAX OCTET STRING (SIZE (32))
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "Specifies 32 characters representing the received value in
                the Operator Specific area of the TTI."
            ::= { sODUkPEntry 18 }

        
        odukpTIMDefectMode OBJECT-TYPE
            SYNTAX INTEGER
                {
                none(0),
                sapi(1),
                dapi(2),
                oper(3),
                sapi-dapi(4),
                sapi-dapi-oper(5),
                sapi-oper(6),
                dapi-oper(7)
                }
            MAX-ACCESS read-create
            STATUS current
            DESCRIPTION
                "Specifies the definition of the trace identifier mismatch alarm by
                identifying which portion(s) of the TTI message are compared
                for trace identifier mismatch purposes.
                - NONE (no trace identifier mismatch defect)
                - OPER (Operator Specific mismatch only),
                - SAPI (SAPI mismatch only),
                - DAPI (DAPI mismatch only),
                - SAPI_DAPI (SAPI + DAPI Mismatches),
                - SAPI_OPER (SAPI + OPER mismatches),
                - DAPI_OPER (DAPI + OPER mismatches),
                - SAPI_DAPI_OPER (SAPI + DAPI + OPER mismatches)."
            DEFVAL { sapi-dapi }
            ::= { sODUkPEntry 19 }

        
        odukpTIMActDis OBJECT-TYPE
            SYNTAX SOtnTruthValue
            MAX-ACCESS read-create
            STATUS current
            DESCRIPTION
                "Specifies the monitored trace identification mode.
                - 'enabled': enable trace monitoring and perform
                consequent actions, including protection switches and AIS
                maintenance signal insertion when there is a trace mismatch.
                - 'disabled': to enable trace monitoring but not
                perform consequent actions, including protection switches and
                AIS maintenance signal insertion when there is a trace
                mismatch.
                - Enter DISABLED to disable trace monitoring.
                Note that AIS insertion is only performed on a terminated entity."
            DEFVAL { true }
            ::= { sODUkPEntry 20 }

        
        odukpDegradeInterval OBJECT-TYPE
            SYNTAX Integer32 (2..8)
            MAX-ACCESS read-create
            STATUS current
            DESCRIPTION
                "Indicates the consecutive number of one second intervals with
                the number of detected block errors exceeding the block error
                threshold for each of those seconds for the purposes of BER of Signal Degrade detection."
            DEFVAL { 2 }
            ::= { sODUkPEntry 21 }

        
        odukpDegradeThreshold OBJECT-TYPE
            SYNTAX Integer32
            MAX-ACCESS read-create
            STATUS current
            DESCRIPTION
                "Indicates the threshold number of block errors at which a one
                second interval will be considered degraded for the purposes of BER signal degrade detection.
                some examples of possible values are:
                1 to 20421, default of 3064 (OTU1)
                1 to 82026, default of 12304 (OTU2)
                1 to 84986, default of 12748 (OTU2e)
                1 to 856388, default of 128459 (OTU4)
                ..."
            DEFVAL { 82026 }
            ::= { sODUkPEntry 22 }

        
        odukpDMSource OBJECT-TYPE
            SYNTAX SOtnEnabled
            MAX-ACCESS read-create
            STATUS current
            DESCRIPTION
                "Specifies whether or not the entity is acting as the Source of the
                Delay Measurement function for performance monitoring."
            DEFVAL { enabled }
            ::= { sODUkPEntry 23 }

        
        odukpPrbs OBJECT-TYPE
            SYNTAX INTEGER
                {
                none(0),
                rx(1),
                tx(2),
                rxtx(3),
                reset(4)
                }
            MAX-ACCESS read-create
            STATUS current
            DESCRIPTION
                "none: disable PRBS
                Tx: enable transmit direction only
                Rx: enable receive direction only
                RxTx: enable both transmssion and receive direction
                Reset: zero PRBS counters and reset timer of Rx, no change for others"
            DEFVAL { none }
            ::= { sODUkPEntry 24 }

        
        odukpPrbsSync OBJECT-TYPE
            SYNTAX INTEGER
                {
                na(0),
                inSync(1),
                outSync(2),
                errSync(3)
                }
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "Indicates whether PRBS pattern is synchronized since start of
                PRBSRX.
                - INSYNC indicates the PRBS pattern is synchronized.
                - OUTSYNC indicates the PRBS pattern is not synchronized.
                - ERRSYNC indicates that the PRBS pattern is synchronized,
                but has lost synchronization and then regained synchronization
                at some point since the PRBS in the Receive direction was
                turned ON.
                - NA indicates that PRBS in receive direction is OFF."
            DEFVAL { inSync }
            ::= { sODUkPEntry 25 }

        
        odukpPrbsTimerDuration OBJECT-TYPE
            SYNTAX DisplayString
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "Indicates the amount of time since start of PRBSRX. Reset of
                PRBSRX will reset the timer."
            DEFVAL { "" }
            ::= { sODUkPEntry 26 }

        
        odukpPrbsCalcBer OBJECT-TYPE
            SYNTAX Integer32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "Indicates the calculated bit error rate (BER). Values for
                CALCBER are restricted to integer values that represent the
                closest negative power of 10. For example, a value of 5
                indicates a BER of 10e-5."
            ::= { sODUkPEntry 27 }

        
        odukpPrbsErrorCount OBJECT-TYPE
            SYNTAX Integer32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "Indicates the bit error count since the start of PRBSRX."
            ::= { sODUkPEntry 28 }

        
        odukpNull OBJECT-TYPE
            SYNTAX SOtnEnabled
            MAX-ACCESS read-create
            STATUS current
            DESCRIPTION
                "enable/disable NULL functionality"
            DEFVAL { disabled }
            ::= { sODUkPEntry 29 }

        
        odukpNIM OBJECT-TYPE
            SYNTAX SOtnEnabled
            MAX-ACCESS read-create
            STATUS current
            DESCRIPTION
                "enable/disable Non intrusive monitoring functionality"
            DEFVAL { enabled }
            ::= { sODUkPEntry 30 }

        
        odukpRxPT OBJECT-TYPE
            SYNTAX Integer32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "Identifies the received Payload Type hexadecimal value."
            DEFVAL { 255 }
            ::= { sODUkPEntry 31 }

        
        odukpTxPT OBJECT-TYPE
            SYNTAX Integer32
            MAX-ACCESS read-create
            STATUS current
            DESCRIPTION
                "Transmitted Payload type"
            DEFVAL { 255 }
            ::= { sODUkPEntry 32 }

        
        odukpExpPT OBJECT-TYPE
            SYNTAX Integer32
            MAX-ACCESS read-create
            STATUS current
            DESCRIPTION
                "same as transmitted payload type"
            DEFVAL { 255 }
            ::= { sODUkPEntry 33 }

        
        odukpPLMConsequentActions OBJECT-TYPE
            SYNTAX SOtnEnabled
            MAX-ACCESS read-create
            STATUS current
            DESCRIPTION
                " "
            DEFVAL { enabled }
            ::= { sODUkPEntry 34 }

        
        odukpOpuConfig OBJECT-TYPE
            SYNTAX INTEGER
                {
                auto(0),
                intact(1),
                client(2),
                mux(3)
                }
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "- AUTO indicates that the system will automatically decide how
                the OPU operates based on additional related provisioning such
                as an ODU cross-connection or an ODUj being multiplexed into
                the ODU. When no additional related provisioning is present, the
                ODU will behave as CTP (connection termination point) that has
                an open connection and thus will be transmitting an ODU-OCI
                maintenance signal.
                - INTACT indicates that the ODU will operate as if there will be
                an ODU intact cross-connect provisioned.
                - MUX indicates that the ODU will operate as a TTP (Trail
                Termination Point) supporting the multiplexing of ODUj entities
                into the ODU.
                - CLIENT indicates tha the ODU will operate as a TTP (Trail
                Termination Point) supporting the client facility into t"
            DEFVAL { auto }
            ::= { sODUkPEntry 35 }

        
        odukpOpuConfigActual OBJECT-TYPE
            SYNTAX INTEGER
                {
                intact(1),
                client(2),
                mux(3),
                na(4)
                }
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "actual OPU configuration"
            DEFVAL { intact }
            ::= { sODUkPEntry 36 }

        
        odukpTribSlot OBJECT-TYPE
            SYNTAX OCTET STRING (SIZE (0..10))
            MAX-ACCESS read-create
            STATUS current
            DESCRIPTION
                "Specifies a group of values, or a range of values identifying
                which tributary slots will be used within the supporting ODUk/
                ODUj."
            ::= { sODUkPEntry 37 }

        
        odukpExpClientRate OBJECT-TYPE
            SYNTAX Integer32
            MAX-ACCESS read-create
            STATUS current
            DESCRIPTION
                "Specifies the expected signal rate of the constant bit rate
                mapped client within the payload of the ODUflex. This value is
                used by the system to determine the ODUflex(CBR) rate. The
                number is specified in Kbps."
            ::= { sODUkPEntry 38 }

        
        odukpNumOfGfpTs OBJECT-TYPE
            SYNTAX Integer32
            MAX-ACCESS read-create
            STATUS current
            DESCRIPTION
                "Specify the ODUflex(GFP) rate by entering the number of 1.25G
                Tributary Slots when the ODUflex supports a GFP client."
            ::= { sODUkPEntry 39 }

        
        odukpOperateRate OBJECT-TYPE
            SYNTAX Integer32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The OPERRATE parameter shows the OPUflex nominal bit
                rate. The number is measured in Kbps."
            ::= { sODUkPEntry 40 }

        
        odukpRowStatus OBJECT-TYPE
            SYNTAX RowStatus
            MAX-ACCESS read-create
            STATUS current
            DESCRIPTION
                "ODUkP Table RowStatus"
            ::= { sODUkPEntry 41 }

            
        sOTUkTable OBJECT-TYPE
            SYNTAX SEQUENCE OF SOTUkEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "OTUk Table"
            ::= { otnConfigMIB 8 }

        
        sOTUkEntry OBJECT-TYPE
            SYNTAX SOTUkEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                " "
            INDEX { shelfId, slotNo, subSlotNo, portNo, subPortNo, 
                otuId }
            ::= { sOTUkTable 1 }

        
        SOTUkEntry ::=
            SEQUENCE { 
                otuId
                    SOtuId,
                otuType
                    SOtuType,
                otukAdminState
                    SOtnEnabled,
                otukOperationalState
                    SOtnUpDown,
                otukAvailabilityState
                    StAvailabilityState,
                otukAlarmProfile
                    Integer32,
                otukPMProfile
                    Integer32,
                otukExpSAPI
                    OCTET STRING,
                otukExpDAPI
                    OCTET STRING,
                otukExpOperator
                    OCTET STRING,
                otukTxSAPI
                    OCTET STRING,
                otukTxDAPI
                    OCTET STRING,
                otukTxOperator
                    OCTET STRING,
                otukRxSAPI
                    OCTET STRING,
                otukRxDAPI
                    OCTET STRING,
                otukRxOperator
                    OCTET STRING,
                otukTIMDefectMode
                    INTEGER,
                otukTIMActDis
                    SOtnTruthValue,
                otukDegradeInterval
                    Integer32,
                otukDegradeThreshold
                    Integer32,
                otukLoopBack
                    SLoopBackMode,
                otukExtChannel
                    DisplayString,
                otukClientShutdown
                    SOtnYesNo,
                otukClientShutdownHoldoffTimer
                    Integer32,
                otukFecType
                    INTEGER,
                otukRowStatus
                    RowStatus,
                otukNearEndALS
                    SOtnYesNo
             }

        otuId OBJECT-TYPE
            SYNTAX SOtuId
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                " "
            ::= { sOTUkEntry 1 }

        
        otuType OBJECT-TYPE
            SYNTAX SOtuType
            MAX-ACCESS read-create
            STATUS current
            DESCRIPTION
                " "
            ::= { sOTUkEntry 2 }

        
        otukAdminState OBJECT-TYPE
            SYNTAX SOtnEnabled
            MAX-ACCESS read-create
            STATUS current
            DESCRIPTION
                "Specifies the admin state of the entity.
                As to different object, the consequent aciton may be different, e.g. it may shutdown the port, or insert maintenance signal."
            DEFVAL { enabled }
            ::= { sOTUkEntry 3 }

        
        otukOperationalState OBJECT-TYPE
            SYNTAX SOtnUpDown
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The operational state of the NE."
            DEFVAL { up }
            ::= { sOTUkEntry 4 }

        
        otukAvailabilityState OBJECT-TYPE
            SYNTAX StAvailabilityState
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "Avalaibility Status is to qualify the operational, usage and/or administrative state attributes"
            DEFVAL { normal }
            ::= { sOTUkEntry 5 }

        
        otukAlarmProfile OBJECT-TYPE
            SYNTAX Integer32
            MAX-ACCESS read-create
            STATUS current
            DESCRIPTION
                "Specifies the alarm profile table for the entity."
            ::= { sOTUkEntry 6 }

        
        otukPMProfile OBJECT-TYPE
            SYNTAX Integer32
            MAX-ACCESS read-create
            STATUS current
            DESCRIPTION
                "Specifies the alarm profile table for the entity."
            ::= { sOTUkEntry 7 }

        
        otukExpSAPI OBJECT-TYPE
            SYNTAX OCTET STRING (SIZE (16))
            MAX-ACCESS read-create
            STATUS current
            DESCRIPTION
                "Specifies 15 characters representing the expected SAPI."
            ::= { sOTUkEntry 8 }

        
        otukExpDAPI OBJECT-TYPE
            SYNTAX OCTET STRING (SIZE (16))
            MAX-ACCESS read-create
            STATUS current
            DESCRIPTION
                "Specifies 15 characters representing the expected DAPI."
            ::= { sOTUkEntry 9 }

        
        otukExpOperator OBJECT-TYPE
            SYNTAX OCTET STRING (SIZE (32))
            MAX-ACCESS read-create
            STATUS current
            DESCRIPTION
                "Specifies 32 characters representing the expected value for the
                Operator Specific area of the TTI."
            ::= { sOTUkEntry 10 }

        
        otukTxSAPI OBJECT-TYPE
            SYNTAX OCTET STRING (SIZE (16))
            MAX-ACCESS read-create
            STATUS current
            DESCRIPTION
                "Specifies 15 characters representing the transmitted SAPI."
            ::= { sOTUkEntry 11 }

        
        otukTxDAPI OBJECT-TYPE
            SYNTAX OCTET STRING (SIZE (16))
            MAX-ACCESS read-create
            STATUS current
            DESCRIPTION
                "Specifies 15 characters representing the transmitted DAPI."
            ::= { sOTUkEntry 12 }

        
        otukTxOperator OBJECT-TYPE
            SYNTAX OCTET STRING (SIZE (32))
            MAX-ACCESS read-create
            STATUS current
            DESCRIPTION
                "Specifies 32 characters representing the transmitted value in
                the Operator Specific area of the TTI."
            ::= { sOTUkEntry 13 }

        
        otukRxSAPI OBJECT-TYPE
            SYNTAX OCTET STRING (SIZE (16))
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "Specifies 15 characters representing the received SAPI."
            ::= { sOTUkEntry 14 }

        
        otukRxDAPI OBJECT-TYPE
            SYNTAX OCTET STRING (SIZE (16))
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "Specifies 15 characters representing the received DAPI."
            ::= { sOTUkEntry 15 }

        
        otukRxOperator OBJECT-TYPE
            SYNTAX OCTET STRING (SIZE (32))
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "Specifies 32 characters representing the received value in
                the Operator Specific area of the TTI."
            ::= { sOTUkEntry 16 }

        
        otukTIMDefectMode OBJECT-TYPE
            SYNTAX INTEGER
                {
                none(0),
                sapi(1),
                dapi(2),
                oper(3),
                sapi-dapi(4),
                sapi-dapi-oper(5),
                sapi-oper(6),
                dapi-oper(7)
                }
            MAX-ACCESS read-create
            STATUS current
            DESCRIPTION
                "Specifies the definition of the trace identifier mismatch alarm by
                identifying which portion(s) of the TTI message are compared
                for trace identifier mismatch purposes.
                - NONE (no trace identifier mismatch defect)
                - OPER (Operator Specific mismatch only),
                - SAPI (SAPI mismatch only),
                - DAPI (DAPI mismatch only),
                - SAPI_DAPI (SAPI + DAPI Mismatches),
                - SAPI_OPER (SAPI + OPER mismatches),
                - DAPI_OPER (DAPI + OPER mismatches),
                - SAPI_DAPI_OPER (SAPI + DAPI + OPER mismatches)."
            DEFVAL { sapi-dapi }
            ::= { sOTUkEntry 17 }

        
        otukTIMActDis OBJECT-TYPE
            SYNTAX SOtnTruthValue
            MAX-ACCESS read-create
            STATUS current
            DESCRIPTION
                "Specifies the monitored trace identification mode.
                - 'false': enable trace monitoring and perform
                consequent actions, including protection switches and AIS
                maintenance signal insertion when there is a trace mismatch.
                - 'true': to enable trace monitoring but not
                perform consequent actions, including protection switches and
                AIS maintenance signal insertion when there is a trace
                mismatch.
                - Enter true to disable trace monitoring.
                Note that AIS insertion is only performed on a terminated entity."
            DEFVAL { true }
            ::= { sOTUkEntry 18 }

        
        otukDegradeInterval OBJECT-TYPE
            SYNTAX Integer32
            MAX-ACCESS read-create
            STATUS current
            DESCRIPTION
                "Indicates the consecutive number of one second intervals with
                the number of detected block errors exceeding the block error
                threshold for each of those seconds for the purposes of BER of Signal Degrade detection."
            DEFVAL { 2 }
            ::= { sOTUkEntry 19 }

        
        otukDegradeThreshold OBJECT-TYPE
            SYNTAX Integer32
            MAX-ACCESS read-create
            STATUS current
            DESCRIPTION
                "Indicates the threshold number of block errors at which a one
                second interval will be considered degraded for the purposes of BER signal degrade detection.
                some examples of possible values are:
                1 to 20421, default of 3064 (OTU1)
                1 to 82026, default of 12304 (OTU2)
                1 to 84986, default of 12748 (OTU2e)
                1 to 856388, default of 128459 (OTU4)
                ..."
            DEFVAL { 82026 }
            ::= { sOTUkEntry 20 }

        
        otukLoopBack OBJECT-TYPE
            SYNTAX SLoopBackMode
            MAX-ACCESS read-create
            STATUS current
            DESCRIPTION
                " "
            DEFVAL { none }
            ::= { sOTUkEntry 21 }

        
        otukExtChannel OBJECT-TYPE
            SYNTAX DisplayString
            MAX-ACCESS read-create
            STATUS current
            DESCRIPTION
                "Specifies the DWDM frequency at which the interface will operate 
                when the supporting pluggable for the facility is a tunable DWDM optics. 
                This parameter is used when directly connecting the module to
                a separate network element, rather than connecting to an optical
                multiplexer/demultiplex module within the same network
                element."
            ::= { sOTUkEntry 22 }

        
        otukClientShutdown OBJECT-TYPE
            SYNTAX SOtnYesNo
            MAX-ACCESS read-create
            STATUS current
            DESCRIPTION
                "Setting the value to YES will shut down the output client port
                side laser on the transponder instead of sending a maintenance
                signal (e.g. AIS or LF depending on the type of facility
                provisioned) when there is a signal failure detected on the line
                side of a transponder.
                This value is typically used with external client side protection.
                Setting the value to NO will not shut down the laser on the port
                side of the transponder. When this value is set standard
                maintenance signaling will be used.
                Setting this value to NA will disable the protection functionality."
            DEFVAL { no }
            ::= { sOTUkEntry 23 }

        
        otukClientShutdownHoldoffTimer OBJECT-TYPE
            SYNTAX Integer32
            MAX-ACCESS read-create
            STATUS current
            DESCRIPTION
                "Specifies a hold-off time between 60 msec and 1000 msec in
                increments of 5 msec before a  client port side laser shutdown upon a
                defect detected at line side. If no additional hold-off time for a port side laser shutdown is desired, then the value shall be set to zero.
                Note: This parameter is only supported when value of parameter 'ClientShutDown' is yes"
            DEFVAL { 0 }
            ::= { sOTUkEntry 24 }

        
        otukFecType OBJECT-TYPE
            SYNTAX INTEGER
                {
                none(0),
                rs(1),
                i4(2),
                i7(3),
                last(4),
                sdfec1(5),
                sdfec2(6),
                sdfec3(7),
                sdfecl(8),
                sdfec1-den(9),
                sdfec2-den(10),
                sdfec3-den(11),
                sdfec1-8qam(12),
                sdfec2-8qam(13),
                sdfec3-8qam(14),
                sdfec1-16qam(15),
                sdfec2-16qam(16),
                sdfec3-16qam(17)
                }
            MAX-ACCESS read-create
            STATUS current
            DESCRIPTION
                "for 10G: NoFEC, G709FEC, I.4EFEC, I.7EFEC
                 for 100G: G709FEC, SDFEC1, SDFEC2, SDFEC3"
            DEFVAL { rs }
            ::= { sOTUkEntry 25 }

        
        otukRowStatus OBJECT-TYPE
            SYNTAX RowStatus
            MAX-ACCESS read-create
            STATUS current
            DESCRIPTION
                "ODUkP Table RowStatus"
            ::= { sOTUkEntry 26 }


        otukNearEndALS OBJECT-TYPE
            SYNTAX SOtnYesNo
            MAX-ACCESS read-create
            STATUS current
            DESCRIPTION
                "This parameter, when set to YES, will shut down port side laser
                upon a port side incoming failure (such as LOS/LOF/LOSYNC)."
            DEFVAL { no }
            ::= { sOTUkEntry 27 }


        sOChOSTable OBJECT-TYPE
            SYNTAX SEQUENCE OF SOChOSEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "OChOS Table"
            ::= { otnConfigMIB 9 }

        
        sOChOSEntry OBJECT-TYPE
            SYNTAX SOChOSEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                " "
            INDEX { shelfId, slotNo, subSlotNo, portNo, subPortNo, 
                ochosId }
            ::= { sOChOSTable 1 }

        
        SOChOSEntry ::=
            SEQUENCE { 
                ochosId
                    SOchOsId,
                ochosAdminState
                    SOtnEnabled,
                ochosOperationalState
                    SOtnUpDown,
                ochosAvailabilityState
                    StAvailabilityState,
                ochosAlarmProfile
                    Integer32,
                ochosPMProfile
                    Integer32,
                ochosSettingFreq
                    Integer32,
                ochosLineLaserEnabled
                    SOtnEnabled,
                ochosLineLaserState
                    SOtnEnabled,
                ochosOEORegen
                    SOtnEnabled,
                ochosRowStatus
                    RowStatus,
                ochosNearEndALS
                    SOtnYesNo,
                ochosEffectiveFreq
                    Integer32,
                ochosFirstSupportedFreq
                    Integer32,
                ochosLastSupportedFreq
                    Integer32,
                ochosFreqGrid
                    INTEGER,
                ochosConfigureTxPower
                    Integer32,
                ochosEffectiveTxPower
                    Integer32,
                ochosFirstSupportedTxPower
                    Integer32,
                ochosLastSupportedTxPower
                    Integer32,
                ochosTxPowerGrid
                    Integer32,    
                ochosDGD
                    Integer32,                    
                ochosCD
                    Integer32,                    
                ochosOSNR
                    Integer32,
                ochosEffectiveCDAutoSearchRangeHighVal
                    Integer32,
                ochosEffectiveCDAutoSearchRangeLowVal
                    Integer32,
                ochosConfiguredCDAutoSearchRangeHighVal
                    Integer32,
                ochosConfiguredCDAutoSearchRangeLowVal
                    Integer32,
                ochosSupportedCDAutoSearchRangeHighValThr
                    Integer32,
                ochosSupportedCDAutoSearchRangeLowValThr
                    Integer32                    
             }

        ochosId OBJECT-TYPE
            SYNTAX SOchOsId
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                " "
            ::= { sOChOSEntry 1 }

        
        ochosAdminState OBJECT-TYPE
            SYNTAX SOtnEnabled
            MAX-ACCESS read-create
            STATUS current
            DESCRIPTION
                "Specifies the admin state of the entity.
                As to different object, the consequent aciton may be different, e.g. it may shutdown the port, or insert maintenance signal."
            DEFVAL { enabled }
            ::= { sOChOSEntry 2 }

        
        ochosOperationalState OBJECT-TYPE
            SYNTAX SOtnUpDown
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The operational state of the NE."
            DEFVAL { up }
            ::= { sOChOSEntry 3 }

        
        ochosAvailabilityState OBJECT-TYPE
            SYNTAX StAvailabilityState
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "Avalaibility Status is to qualify the operational, usage and/or administrative state attributes"
            DEFVAL { normal }
            ::= { sOChOSEntry 4 }

        
        ochosAlarmProfile OBJECT-TYPE
            SYNTAX Integer32
            MAX-ACCESS read-create
            STATUS current
            DESCRIPTION
                "Specifies the alarm profile table for the entity."
            ::= { sOChOSEntry 5 }

        
        ochosPMProfile OBJECT-TYPE
            SYNTAX Integer32
            MAX-ACCESS read-create
            STATUS current
            DESCRIPTION
                "Specifies the alarm profile table for the entity."
            ::= { sOChOSEntry 6 }

        
        ochosSettingFreq OBJECT-TYPE
            SYNTAX Integer32 (19135..19610)
            MAX-ACCESS read-create
            STATUS current
            DESCRIPTION
                "Setting the frequency of line side, AC100 support configuration range: 191.35 THz to 196.10 THz in increments of 0.05 THz"
            ::= { sOChOSEntry 7 }

        
        ochosLineLaserEnabled OBJECT-TYPE
            SYNTAX SOtnEnabled
            MAX-ACCESS read-create
            STATUS current
            DESCRIPTION
                "Through this attribute the laser, normally on, can be switched-off by the operator overriding all ALS related laser control. Switch-on the laser, though, will resume ALS control of the laser, which does not necessarily mean that the laser will be turned on!
                Furthermore switch-on/off of the laser shall be subject to the dispositions stated in laserState."
            DEFVAL { enabled }
            ::= { sOChOSEntry 8 }

        
        ochosLineLaserState OBJECT-TYPE
            SYNTAX SOtnEnabled
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "Line laser state"
            ::= { sOChOSEntry 9 }

        
        ochosOEORegen OBJECT-TYPE
            SYNTAX SOtnEnabled
            MAX-ACCESS read-create
            STATUS current
            DESCRIPTION
                "only applies to transponder card (not for muxponder and OTN switching card)"
            DEFVAL { enabled }
            ::= { sOChOSEntry 10 }

            
        ochosRowStatus OBJECT-TYPE
            SYNTAX RowStatus
            MAX-ACCESS read-create
            STATUS current
            DESCRIPTION
                "ochos RowStatus"
            ::= { sOChOSEntry 11 }

            
        ochosNearEndALS OBJECT-TYPE
            SYNTAX SOtnYesNo
            MAX-ACCESS read-create
            STATUS current
            DESCRIPTION
                "This parameter, when set to YES, will shut down port side laser
                upon a port side incoming failure (such as LOS/LOF/LOSYNC)."
            DEFVAL { no }
            ::= { sOChOSEntry 12 }
        
        
        ochosEffectiveFreq OBJECT-TYPE
            SYNTAX Integer32 (19135..19610)
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The effective frequency of line side"
            ::= { sOChOSEntry 13 }

            
        ochosFirstSupportedFreq OBJECT-TYPE
            SYNTAX Integer32 (19135..19610)
            MAX-ACCESS read-create
            STATUS current
            DESCRIPTION
                "The start frequency of line side for setting"
            ::= { sOChOSEntry 14 }

            
        ochosLastSupportedFreq OBJECT-TYPE
            SYNTAX Integer32 (19135..19610)
            MAX-ACCESS read-create
            STATUS current
            DESCRIPTION
                "The end frequency of line side for setting"
            ::= { sOChOSEntry 15 }

            
        ochosFreqGrid OBJECT-TYPE
            SYNTAX INTEGER
                {
                hZ50G(50),
                hZ100G(100)
                }
            MAX-ACCESS read-create
            STATUS current
            DESCRIPTION
                "Increments of frequency setting"
            DEFVAL { hZ50G }
            ::= { sOChOSEntry 16 }

            
        ochosConfigureTxPower OBJECT-TYPE
            SYNTAX Integer32 
            MAX-ACCESS read-create
            STATUS current
            DESCRIPTION
                "Configure of Output Power, range is between ochosFirstSupportedTxPower and ochosLastSupportedTxPower, step is ochosTxPowerGrid"
            ::= { sOChOSEntry 17 }

            
        ochosEffectiveTxPower OBJECT-TYPE
            SYNTAX Integer32 
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The actual Output Power"
            ::= { sOChOSEntry 18 }

            
        ochosFirstSupportedTxPower OBJECT-TYPE
            SYNTAX Integer32 
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The start point of Output Power"
            ::= { sOChOSEntry 19 }

            
        ochosLastSupportedTxPower OBJECT-TYPE
            SYNTAX Integer32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The end point of Output Power"
            ::= { sOChOSEntry 20 }            


        ochosTxPowerGrid OBJECT-TYPE
            SYNTAX Integer32 
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The step of Output Power"
            ::= { sOChOSEntry 21 }

            
        ochosDGD OBJECT-TYPE
            SYNTAX Integer32 
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "Current Differential Group Delay"
            ::= { sOChOSEntry 22 }

            
        ochosCD OBJECT-TYPE
            SYNTAX Integer32 
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "Current Chromatic Dispersion"
            ::= { sOChOSEntry 23 }

            
        ochosOSNR OBJECT-TYPE
            SYNTAX Integer32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "Current OSNR"
            ::= { sOChOSEntry 24 }                
    
            
        ochosEffectiveCDAutoSearchRangeHighVal OBJECT-TYPE
            SYNTAX Integer32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The actual value in CFP"
            DEFVAL { 0 }
            ::= { sOChOSEntry 25 }

        
        ochosEffectiveCDAutoSearchRangeLowVal OBJECT-TYPE
            SYNTAX Integer32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The actual value in CFP"
            DEFVAL { 0 }
            ::= { sOChOSEntry 26 }

        
        ochosConfiguredCDAutoSearchRangeHighVal OBJECT-TYPE
            SYNTAX Integer32
            MAX-ACCESS read-create
            STATUS current
            DESCRIPTION
                "The value set by user"
            DEFVAL { 0 }
            ::= { sOChOSEntry 27 }

        
        ochosConfiguredCDAutoSearchRangeLowVal OBJECT-TYPE
            SYNTAX Integer32
            MAX-ACCESS read-create
            STATUS current
            DESCRIPTION
                "The value set by user"
            DEFVAL { 0 }
            ::= { sOChOSEntry 28 }

        
        ochosSupportedCDAutoSearchRangeHighValThr OBJECT-TYPE
            SYNTAX Integer32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The value which support by CFP"
            DEFVAL { 0 }
            ::= { sOChOSEntry 29 }

        
        ochosSupportedCDAutoSearchRangeLowValThr OBJECT-TYPE
            SYNTAX Integer32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The value which support by CFP"
            DEFVAL { 0 }
            ::= { sOChOSEntry 30 }

        
    
            
    END

--
-- ST-OTN-MIB.mib
--
