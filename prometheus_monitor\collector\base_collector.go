package collector

import (
	"fmt"
	"log"
	"path/filepath"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/prometheus/client_golang/prometheus"
)

type BaseCollector struct {
	targets  []CollectTarget
	path     string
	interval int
	workers  map[string]*CollectorWorker
	mutex    sync.Mutex
}

func NewBaseCollector(target []CollectTarget, path string, interval int) Collector {
	b := &BaseCollector{
		targets:  target,
		path:     path,
		interval: interval,
		workers:  make(map[string]*CollectorWorker),
	}

	for _, target := range b.targets {
		worker := NewCollectorWorker(target, b.path, b.interval)
		worker.Start()
		b.workers[target.Name] = worker
	}

	return b
}

func (c *BaseCollector) AddTarget(target CollectTarget) {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	if _, ok := c.workers[target.Name]; !ok {
		worker := NewCollectorWorker(target, c.path, c.interval)
		worker.Start()
		c.workers[target.Name] = worker
		log.Printf("%s Worker for target %s started\n", c.path, target.Address)
	}
}

func (c *BaseCollector) RemoveTarget(target CollectTarget) {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	if worker, ok := c.workers[target.Name]; ok {
		worker.Stop()
		delete(c.workers, target.Name)
		log.Printf("%s Worker for target %s stopped\n", c.path, target.Address)
	}
}

func (c *BaseCollector) UpdateTargetInternally(target CollectTarget) {
	c.mutex.Lock()
	defer c.mutex.Unlock()
	if worker, ok := c.workers[target.Name]; ok {
		worker.Stop()
		worker.UpdateTarget(target)
		worker.Restart()
		log.Printf("%s Worker for target %s restarted\n", c.path, worker.target.Address)
	}
}

func (c *BaseCollector) UpdateTarget(newTargets []CollectTarget) {

	// 增加target
	for _, newTarget := range newTargets {
		found := false
		for i, target := range c.targets {
			if target.Name == newTarget.Name {
				found = true
				// 如果有变化 则更新 target
				if target.Address != newTarget.Address || target.Username != newTarget.Username || target.Password != newTarget.Password {
					log.Printf("update target %s restarted\n", newTarget.Name)
					c.UpdateTargetInternally(newTarget)
					c.targets[i] = newTarget
				}
				break
			}
		}
		if !found {
			c.AddTarget(newTarget)
			c.targets = append(c.targets, newTarget)
			log.Printf("Added target with ID %s\n", newTarget.Name)
		}
	}

	// 删除target
	for i := len(c.targets) - 1; i >= 0; i-- {
		found := false
		for _, newTarget := range newTargets {
			if c.targets[i].Name == newTarget.Name {
				found = true
				break
			}
		}
		if !found {
			removeTarget := c.targets[i]
			c.RemoveTarget(removeTarget)
			c.targets = append(c.targets[:i], c.targets[i+1:]...)
			log.Printf("Removed target with ID %s\n", removeTarget.Name)
		}
	}

}

func (c *BaseCollector) Stop() {
	for _, worker := range c.workers {
		worker.Stop()
	}
}

func (c *BaseCollector) Update(ch chan<- prometheus.Metric) error {
	// todo 被删除的target是否需要处理
	for targetName, worker := range c.workers {
		result := worker.GetResult()
		timeout := false
		if len(result) != 0 {
			var timestamp time.Time
			for _, v := range result {
				timestamp = v.Timestamp
				break
			}
			// 超过10分钟没有新数据则超时
			currentTime := time.Now()

			// fmt.Println(timestamp, currentTime)
			diff := currentTime.Sub(timestamp)
			if diff > 10*time.Minute {
				timeout = true
			} else {
				samples := c.ResultToSamples(result, targetName, timestamp)
				for _, sample := range samples {
					ch <- sample
				}
			}

		}
		targetStatus := 1
		if len(result) == 0 || timeout {
			// fmt.Println("timeout")
			targetStatus = 0
		}

		targetMetricName := strings.ReplaceAll(c.path+"_target_status", "-", "_")
		labelNames := []string{"target"}
		labelValues := []string{targetName}
		targetMetric := prometheus.NewMetricWithTimestamp(
			time.Now(),
			prometheus.MustNewConstMetric(
				prometheus.NewDesc(
					targetMetricName,
					targetMetricName,
					labelNames,
					nil),
				prometheus.GaugeValue,
				float64(targetStatus), labelValues...))
		ch <- targetMetric

		workerStatus := worker.GetWorkerStatus()
		workerMetricName := strings.ReplaceAll(c.path+"_worker_status", "-", "_")
		workerMetric := prometheus.NewMetricWithTimestamp(
			time.Now(),
			prometheus.MustNewConstMetric(
				prometheus.NewDesc(
					workerMetricName,
					workerMetricName,
					labelNames,
					nil),
				prometheus.GaugeValue,
				float64(workerStatus), labelValues...))
		ch <- workerMetric
	}
	return nil
}

// TODO 封装到worker中， BaseCollector应该只负责target增减，collector启停和暴露update接口，对于特殊处理的部分后续可以考虑分装在各自的worker中
func (c *BaseCollector) ResultToSamples(result map[string]BaseMetric, targetName string, ts time.Time) []prometheus.Metric {
	samples := []prometheus.Metric{}
	newBaseMetric := make(map[string]BaseMetric)
	for _, base := range result {
		// baseMetric := updateToBaseMetric(ts, update)
		if strings.Contains(base.Name, "counters") || strings.Contains(base.Name, "fsconfig-qos-ai-extensions") ||
			strings.Contains(base.Name, "memory") || strings.Contains(base.Name, "cpus") || strings.Contains(base.Name, "rates") {
			// 过滤不需要的cpu参数
			if strings.Contains(base.Name, "cpus") &&
				(strings.Contains(base.Name, "hardware-interrupt") || strings.Contains(base.Name, "nice") ||
					strings.Contains(base.Name, "idle") || strings.Contains(base.Name, "wait") ||
					strings.Contains(base.Name, "user") || strings.Contains(base.Name, "kernel") || strings.Contains(base.Name, "software-interrupt")) {
				continue
			}
			metric := buildMetric(base.Name, base.Value, targetName, base.Label, base.Timestamp)
			samples = append(samples, metric)
		} else if strings.Contains(base.Name, "output-power") || strings.Contains(base.Name, "input-power") || strings.Contains(base.Name, "laser-temperature") {
			dir := filepath.Dir(base.Name)
			dir = strings.ReplaceAll(dir, "\\", "/")
			metric := buildMetric(dir, base.Value, targetName, base.Label, base.Timestamp)
			samples = append(samples, metric)
		} else {
			// 对于非counters类型的指标，根据base路径和label进行合并，dir作为指标名称 name作为label
			name := filepath.Base(base.Name)
			dir := filepath.Dir(base.Name)
			dir = strings.ReplaceAll(dir, "\\", "/")
			label := fmt.Sprintf("%v", base.Label)

			if metric, ok := newBaseMetric[dir+"_"+label]; ok {
				// 指标已存在，更新label
				metric.AddLabel(name, base.Value)
			} else {
				// 新建采集指标
				// 直接用base.Label构建有bug 可能是因为map不能直接copy
				newLabel := make(map[string]string)
				for key, value := range base.Label {
					newLabel[key] = value
				}
				var value string
				// gnmi采集时间和Prometheus采集时间差距大于采集间隔时置0，用于判断lldp连通性
				if ts.Sub(base.Timestamp) > time.Duration(c.interval)*time.Second {
					value = "0"
				} else {
					value = "1"
				}
				newMetric := BaseMetric{
					Name:      dir,
					Label:     newLabel,
					Value:     value,
					Timestamp: base.Timestamp,
				}
				newMetric.AddLabel(name, base.Value)
				newBaseMetric[dir+"_"+label] = newMetric
			}

			// 由于告警需求增加特殊处理
			if (strings.Contains(base.Name, "port-speed") || strings.Contains(base.Name, "negotiated-port-speed")) && strings.Contains(base.Name, "state") {
				baseName := strings.Replace(base.Name, "negotiated-port-speed", "port-speed", -1)

				speedInBps, err := valueToFloat(base.Value)
				if err != nil {
					log.Println(base.Name, base.Value, err)
					continue
				}
				newLabel := make(map[string]string)
				for key, value := range base.Label {
					newLabel[key] = value
				}

				speedStr := strconv.FormatFloat(speedInBps, 'f', -1, 64)
				metric := buildMetric(baseName, fmt.Sprintf("%v", speedStr), targetName, newLabel, base.Timestamp)
				samples = append(samples, metric)
			}
		}
	}

	for _, baseMetric := range newBaseMetric {
		baseMetric.AddLabel("target", targetName)
		metric := baseMetric.BuildMetric()
		samples = append(samples, metric)
	}

	return samples
}
