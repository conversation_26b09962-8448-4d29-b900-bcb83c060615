.DLB{
    display: grid;
    width: 100%;
    gap: 18px 24px;
    grid-template-columns: repeat(12, 1fr);
    grid-template-rows: 0.5fr 1fr 1.5fr;
    > div {

        &:nth-child(1),
        &:nth-child(2){
            grid-column: span 6;
        }
    
        &:nth-child(3),
        &:nth-child(4){
            grid-column: span 6;
            grid-row: 2;
        }
    
        &:nth-child(5) {
            grid-column: 1 / -1;
            grid-row: 3;
        }
    }

    &_cpuUtilization {
        height: 100%;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
    }
}
