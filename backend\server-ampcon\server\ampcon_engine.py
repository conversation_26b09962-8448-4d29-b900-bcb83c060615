import base64
import logging
import os
import re
import sys
import json
import traceback
from datetime import timedelta, datetime, date
from json import JSONEncoder

import flask_login

from flask import (
    request,
    sessions,
    redirect,
    Response,
    Markup,
    jsonify
)
from flask_login.login_manager import Login<PERSON>anager
from flask_principal import identity_loaded, Identity, RoleNeed, Principal, identity_changed, current_app

BASE_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(BASE_DIR)

# import pydevd_pycharm
# pydevd_pycharm.settrace('**********', port=18875, stdoutToServer=True, stderrToServer=True)


from server import app
from server.api import new_dashboard_api
from server.api import management_api
from server.api import new_user_api
from server.api import new_inventory_api
from server.api import new_automation_api
from server.general_api import general_model
from server.api import new_config_api
from server.api import new_template_api
from server.api import new_lifecycle_api
from server.api import new_monitor_api
from server.api import new_rma_api
from server.api import dcp920_api
from server.api import campus_blueprint_api
from server.api import dc_blueprint_api
from server.api import resource_pool_api
from server.api import fmt_api
from server.api import otn_api
from server.api import m6200_api
from server.util import utils
from server import cfg
from server.user import User, logged_in_users
from server.db.models.inventory import ApplicationConfig, AllowedSourceIPPolicy
from server.auth_jwt import Auth_Handles
from server.db.models.inventory import inven_db
from server.db.models.general import general_db
from server.db.models.user import user_db
from server import random_key
from server.util.tnms_access_control import access_control
from server.util.check_license_util import LicenseChecker


login_manager = LoginManager()
auth_handles = Auth_Handles(random_key)
reg_paths = [re.compile('^/reg', re.I), re.compile('^/management/vpn_reg', re.I)]
no_auth_reg_paths = None

LOG = logging.getLogger(__name__)
license_checker = LicenseChecker()


def get_pro_type(file_path="/usr/share/automation/server/.env"):
    pro_type = None
    try:
        with open(file_path, 'r') as file:
            for line in file:
                match = re.match(r'^\s*PRO_TYPE\s*=\s*(.*)\s*$', line)
                if match:
                    pro_type = match.group(1)
                    break
    except Exception as e:
        LOG.info(f": {e}")
    return pro_type


ampcon_pro_type = get_pro_type()


@app.route('/otn_device_lic', methods=['POST'])
def check_license():
    req_data = request.get_json()
    encrypt_data = req_data.get("data", "")
    if not encrypt_data:
        return "Invalid input", 400
    try:
        req_params = json.loads(license_checker.decrypt(encrypt_data))
        sn = req_params.get("sn", "")
        mac = req_params.get("mac", "")
        if not sn or not mac:
            raise ValueError("Input params invalid")
        req_params.update({"code": 1, "info": "License is valid"})
        resp_data = license_checker.encrypt(json.dumps(req_params))
    except ValueError as v:
        return str(v), 400
    except Exception as e:
        return str(e), 500
    else:
        return resp_data, 200


@app.route('/auth', methods=['GET', 'POST'])
def nginx_auth():
    user = flask_login.current_user
    original_uri = request.headers.get('X-Original-URI')
    roles_map = {
        "superuser": "admin",
        "superadmin": "admin",
        "admin": "operator",
        "readonly": "readonly"
    }
    access_status = access_control.check_access(roles_map.get(user.type, "readonly"), original_uri)
    return Response("nginx-auth", status=200 if access_status else 403, content_type='text/plain')


class AutoRefreshSession(sessions.SecureCookieSession):
    def __init__(self, initial=None):
        super(AutoRefreshSession, self).__init__(initial=initial)
        self.permanent = True


class CustomJSONEncoder(JSONEncoder):
    def default(self, obj):
        if isinstance(obj, datetime):
            return obj.strftime('%Y-%m-%d %H:%M:%S')
        elif isinstance(obj, date):
            return obj.strftime('%Y-%m-%d')
        else:
            return JSONEncoder.default(self, obj)


def init_app():
    # session
    app.config['SECRET_KEY'] = os.urandom(24)
    app.config['PERMANENT_SESSION_LIFETIME'] = timedelta(minutes=30)
    app.config['SCHEDULER_API_ENABLED'] = True
    app.config['SESSION_COOKIE_SECURE'] = True
    app.config['SESSION_COOKIE_HTTPONLY'] = True
    app.config['SESSION_COOKIE_SAMESITE'] = 'Lax'

    # register api
    app.register_blueprint(new_dashboard_api.new_dashboard_mold)
    app.register_blueprint(new_user_api.new_user_mold, url_prefix='/user')
    app.register_blueprint(management_api.management_mold, url_prefix='/management')
    app.register_blueprint(new_automation_api.new_automation_model, url_prefix='/automation')
    app.register_blueprint(general_model, url_prefix='/api')
    app.register_blueprint(new_config_api.new_config_mold, url_prefix='/config')
    app.register_blueprint(new_template_api.new_template_mold, url_prefix='/template')
    app.register_blueprint(new_lifecycle_api.new_lifecycle_model, url_prefix='/lifecycle')
    app.register_blueprint(new_inventory_api.new_inventory_mold, url_prefix='/inventory')
    app.register_blueprint(new_monitor_api.new_monitor_mold, url_prefix='/monitor')
    app.register_blueprint(new_rma_api.new_rma_mold, url_prefix='/rma')
    app.register_blueprint(campus_blueprint_api.campus_blueprint_mold, url_prefix='/campus_blueprint')
    app.register_blueprint(resource_pool_api.resource_pool_blueprint_mold, url_prefix='/resource_pool')
    app.register_blueprint(dc_blueprint_api.dc_blueprint_mold, url_prefix='/dc_blueprint')

    if ampcon_pro_type not in ["ampcon-dc", "ampcon-campus"]:
        app.register_blueprint(dcp920_api.dcp920_module, url_prefix='/dcp920')
        app.register_blueprint(fmt_api.fmt_module, url_prefix='/fmt')
        app.register_blueprint(fmt_api.dcs_module, url_prefix='/dcs')
        app.register_blueprint(otn_api.otn_module, url_prefix='/otn')
        app.register_blueprint(m6200_api.m6200_module, url_prefix='/m6200')

    app.session_interface.session_class = AutoRefreshSession
    app.json_encoder = CustomJSONEncoder

    def ip_to_bin32(ip):
        segs = ip.split('.')
        res = ''
        for seg in segs:
            bin_str = bin(int(seg))[2:]
            res += '0' * (8 - len(bin_str)) + bin_str
        return res

    def ensure_ip_available(ip, allow_source_ips):
        source_bin32 = ip_to_bin32(ip)
        for allow_source_ip in allow_source_ips:
            ip, seg, mask = allow_source_ip.partition('/')
            allow_source_ip_bin32 = ip_to_bin32(ip)
            mask = int(mask) if mask != '' else 32
            if source_bin32[:mask] == allow_source_ip_bin32[:mask]:
                return True
        return False

    @app.before_request
    def auth_filter():
        if request.authorization:
            return

        path = request.path
        for reg in reg_paths:
            if reg.search(path):
                records = inven_db.get_collection(AllowedSourceIPPolicy)
                if not records:
                    break
                source_ip = request.remote_addr
                allow_source_ips = [record.ip for record in records]
                if not ensure_ip_available(source_ip, allow_source_ips):
                    return Response('source ip %s not allowed' % Markup.escape(source_ip), status=400)
                break

        api_reg = re.compile('^/api')
        if api_reg.search(path):
            token = request.headers.get('Authorization')
            if token and auth_api_jwt(token.replace('Bearer ', '')):
                return
            else:
                return 'Token not valid'

        global no_auth_reg_paths
        if not no_auth_reg_paths:
            no_auth_reg_paths = [re.compile(reg) for reg in cfg.CONF.no_auth_urls] + [re.compile('^/token')]
        for reg in no_auth_reg_paths:
            if reg.search(path):
                return
        if not flask_login.current_user.is_authenticated:
            return jsonify({'msg': 'Auth failed'}), 401
        elif flask_login.current_user.id not in logged_in_users:
            LOG.error("user not in logged user list")
            return jsonify({'msg': 'Auth failed'}), 401

    @app.after_request
    def apply_hsts(response):
        response.headers["Strict-Transport-Security"] = "max-age=31536000; includeSubDomains"
        response.headers[
            'Content-Security-Policy'] = "default-src 'self'; style-src-elem 'self' fonts.googleapis.com cdnjs.cloudflare.com cdn.jsdelivr.net api.mapbox.com data: 'unsafe-inline'; style-src 'self' data: 'unsafe-inline'; script-src 'self' fonts.googleapis.com cdn.jsdelivr.net cdnjs.cloudflare.com api.mapbox.com unpkg.com 'unsafe-inline' 'unsafe-eval'; font-src 'self' fonts.gstatic.com data:; img-src 'self' data: *.basemaps.cartocdn.com"
        return response

    @app.context_processor
    def inject_version_feature():
        from server.db.models.monitor import Event, monitor_db
        from server.db.models.user import user_db
        from server.constants import VERSION, REVISION
        user = flask_login.current_user
        if not user.is_authenticated:
            return dict()
        if user.role == 'tacacs':
            tmp_user = User()
            tmp_user.name = user.id
            tmp_user.type = user.type
            user = tmp_user
        else:
            user = user_db.query_user(user.id)
        # user = user_db.query_user(user.id)
        event = monitor_db.get_collection(Event, filters={'status': ['unread'],
                                                          'sn': list(map(lambda x: x.sn, utils.query_switch()))},
                                          sorts=[('modified_time', False)])
        development_version = VERSION
        development_revision = VERSION + '/' + REVISION if REVISION else VERSION
        cookies = request.cookies
        sidebar = True if cookies.get('sidebar', False) == 'true' else False
        return dict(version_feature=cfg.CONF.verizon_feature,
                    event=event, development_version=development_version,
                    development_revision=development_revision, user=user, sidebar=sidebar)

    @app.errorhandler(Exception)
    def db_error_handle(error):
        app.logger.exception(error)
        return Response(Markup.escape(str(error)), status=500)

    @app.teardown_request
    def clear_request_session(exc):
        if request.path.startswith('/static'):
            return
        inven_db.clear_session()

    # @app.teardown_appcontext
    def clear_app_session(exc):
        inven_db.clear_session()


def init_login_manager():
    # init login_manager
    login_manager.init_app(app)

    @login_manager.user_loader
    def user_loader(user_id):
        user = User()
        user.id, user.type, user.role, user.user_type, user.group = user_id.split('@@')
        identity_changed.send(current_app._get_current_object(), identity=Identity(user.id, user.type))
        return user

    @login_manager.request_loader
    def request_loader(request):
        # first, try to login using the api_key url arg
        # api_key = request.args.get('api_key')
        # if api_key:
        #     user = User.query.filter_by(api_key=api_key).first()
        #     if user:
        #         return user

        # next, try to login using Basic Auth

        auth = request.authorization
        if not auth or not utils.check_auth(auth.username, auth.password):
            return

        user = User()
        user.id = auth.username
        return user

    @login_manager.header_loader
    def load_user_from_header(header_val):
        header_val = header_val.replace('Basic ', '', 1)
        try:
            header_val = base64.b64decode(header_val).decode()
            username, seg, passwd = header_val.rpartition(':')
            if not utils.check_auth(username, passwd):
                return

            user = User()
            user.id = username
            return user
        except TypeError:
            pass
        return None


def init_db():
    user_db.create_default_user_if_no_exit()
    user_db.create_tacacs_config_if_not_exit()
    general_db.create_root_config_node_if_no_exit()
    inven_db.clear_session()


def init_map():
    geo_api_entry = inven_db.get_collection(ApplicationConfig, filters={'application_name': ['geocoding_api']})
    if not geo_api_entry:
        geo_api_config = ApplicationConfig(application_name='geocoding_api', configuration=cfg.CONF.map.geocoding_api)
        inven_db.insert_or_update(geo_api_config, primary_key='application_name')
    nominatim_server_entry = inven_db.get_collection(ApplicationConfig,
                                                     filters={'application_name': ['nominatim_server_url']})
    if not nominatim_server_entry:
        nominatim_server_url = ApplicationConfig(application_name='nominatim_server_url',
                                                 configuration=cfg.CONF.map.nominatim_server_url)
        inven_db.insert_or_update(nominatim_server_url, primary_key='application_name')
    map_central_latlng_entry = inven_db.get_collection(ApplicationConfig,
                                                       filters={'application_name': ['map_central_latlng']})
    if not map_central_latlng_entry:
        map_central_latlng = ApplicationConfig(application_name='map_central_latlng',
                                               configuration=",".join(cfg.CONF.map.map_central_latlng))
        inven_db.insert_or_update(map_central_latlng, primary_key='application_name')
    maxZoom_entry = inven_db.get_collection(ApplicationConfig, filters={'application_name': ['maxZoom']})
    if not maxZoom_entry:
        maxZoom = ApplicationConfig(application_name='maxZoom', configuration=cfg.CONF.map.maxZoom)
        inven_db.insert_or_update(maxZoom, primary_key='application_name')
    tileLayer_entry = inven_db.get_collection(ApplicationConfig, filters={'application_name': ['tileLayer']})
    if not tileLayer_entry:
        tileLayer = ApplicationConfig(application_name='tileLayer', configuration=cfg.CONF.map.tileLayer)
        inven_db.insert_or_update(tileLayer, primary_key='application_name')
    inven_db.clear_session()


def auth_api_jwt(token):
    ret, _ = auth_handles.auth_jwt_token(token)
    return ret


@identity_loaded.connect_via(app)
def on_identity_loaded(sender, identity):
    role = identity.auth_type
    if not role:
        return
    identity.provides.add(RoleNeed(role))


try:
    init_db()
    init_map()
    init_app()
except Exception as e:
    traceback.print_exc()
    app.log_exception(e)
    app.logger.error('Fail to connect to database, check the database connection.')
    exit()

init_login_manager()

if __name__ == '__main__':
    app.run(host=cfg.CONF.bind, port=cfg.CONF.port, debug=False)
