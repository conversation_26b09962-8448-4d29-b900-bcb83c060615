import logging

from dictdiffer import diff

from server.db.models.vtep import vtep_db, VtepControlSwitch
from server.vtep_management.ovsdb_manager import Ovsdb_manager
from server import constants as C

LOG = logging.getLogger(__name__)


class VtepSync(Ovsdb_manager):

    def __init__(self, *args):
        super(VtepSync, self).__init__(*args)

    def sync(self):
        from eventlet.timeout import Timeout
        timeout = Timeout(600)
        try:
            # reconnect to prevent switch disconnect or other conditions
            if not self.idl._session.is_connected():
                LOG.error('{0}: fail to connect to ovsdb.'.format(self.remote))
                self.idl.force_reconnect()
            self.sync_switch_config_status()
            self.sync_tunnel_ip()
            del_tunnel_keys = self.sync_vtep()
            self.sync_arp_macs()
            self.sync_binding_ports()
            self.sync_mac(del_tunnel_keys)
            self.sync_vlan()
        except Timeout:
            LOG.warning('this green thread for %s may be hang, reinit ovsdb manager', self.sn)
            # maybe this thread is hang by socket, reinit idl for socket
            self.reinit_idl()
        except Exception as e:
            LOG.exception(e)

    def sync_arp_macs(self):
        macs = self.get_arp_local_macs()
        switch_local_macs = set([mac.src_mac for mac in macs])
        switch_local_mac_dict = dict((mac.src_mac, mac.locator.uuid) for mac in macs)
        db_macs = vtep_db.get_switch_local_arp_macs(self.sn)
        db_local_macs = set([mac.mac for mac in db_macs])

        # sync local macs to db
        new_macs = []
        for new_mac in switch_local_macs - db_local_macs:
            # get mac match ip
            dst_ip = self.get_locator_ip(switch_local_mac_dict[new_mac])
            if dst_ip:
                new_macs.append({'sn': self.sn, 'mac': new_mac, 'local_vtep_ip': dst_ip})

        vtep_db.batch_add_local_macs(new_macs)
        del_macs = list(db_local_macs - switch_local_macs)
        vtep_db.del_age_arp_macs(self.sn, del_macs)

        # sync other macs
        switch_remote_macs = self.get_arp_remote_macs()
        switch_remote_macs_ = set([switch_remote_mac.src_mac for switch_remote_mac in switch_remote_macs])
        db_remote_macs = vtep_db.get_other_arps_macs(self.sn)
        db_remote_macs_ = set([db_remote_mac.mac for db_remote_mac in db_remote_macs])

        # switch add remote_macs
        for db_mac in db_remote_macs:
            if db_mac.mac not in switch_remote_macs_:
                # add remote mac
                self.add_arp_remote_mac(db_mac.mac, db_mac.local_vtep_ip)

        # del age remote mac
        self.del_arp_remote_mac(list(switch_remote_macs_ - db_remote_macs_))

    def sync_vtep(self):
        LOG.debug("%s start sync remote vtep ip from DB", self.sn)
        local_ip = self.get_vtep_tunnel_ip()
        if not local_ip:
            return

        self.vtep_ip = local_ip

        switches = vtep_db.get_all_vteps()
        if not switches:
            return

        logic_switches = self.get_all_logic_switches()
        # db_configed_vlans = self.get_all_configed_vlans_from_db()
        db_bind_vlans = vtep_db.get_all_binding_vlans(self.sn)
        db_bind_vlans = set([int(vlan) for vlan in db_bind_vlans])
        vlans = set([logic_switch.tunnel_key[0] - 10000 for logic_switch in logic_switches])
        add_vlans = db_bind_vlans - vlans
        tunnel_keys = [10000 + vlan for vlan in add_vlans]

        self.add_logic_switches(tunnel_keys)

        del_tunnel_keys = []
        have_tunnel_keys = []
        logic_switches = self.get_all_logic_switches()
        for logic_switch in logic_switches:
            vlan = logic_switch.tunnel_key[0] - 10000
            if vlan in db_bind_vlans:
                have_tunnel_keys.append(logic_switch.uuid)
            if vlan not in db_bind_vlans:
                del_tunnel_keys.append(logic_switch.uuid)
        self.del_mcast_remotes(del_tunnel_keys)

        handled_remote_ips = set()
        added_remote_list = []
        for vtep_switch in switches:
            remote_ip = vtep_switch.local_vtep_ip
            if local_ip == remote_ip or remote_ip in handled_remote_ips:
                continue

            handled_remote_ips.add(remote_ip)

            LOG.debug("%s sync local vtep ip %s remote vtep ip %s", self.sn, local_ip, remote_ip)
            try:
                added_remote_list += self.set_mcast_macs_remote(remote_ip, have_tunnel_keys)
            except Exception as e:
                LOG.exception(e)

        self.add_mcast_macs_remote(added_remote_list)

        # delete unused Physical_Locator & Physical_Locator_Set & Mcast Macs Remote
        switch_dst_ip_list = [i.local_vtep_ip for i in switches]
        self.del_unused_physical_locator(switch_dst_ip_list)

        return del_tunnel_keys

    def sync_mac(self, del_tunnel_keys):
        if self.get_connection_status():
            LOG.debug(":::switch %s is connected, start to get MAC", self.sn)
        else:
            LOG.debug(":::switch %s is NOT connected, get previous MAC", self.sn)
            return

        switch_macs, logic_switches = self.get_all_local_mac()

        db_entries, id_map, db_macs = vtep_db.get_switch_macs()
        new_mac_list = []
        changed_mac_list = []
        have_switch_macs = set()
        for switch_mac, vtep_ip in switch_macs.items():
            if logic_switches[switch_mac] in del_tunnel_keys:
                self.del_local_mac_by_logic_uuid(logic_switches[switch_mac])
                continue
            have_switch_macs.add(switch_mac)
            if (switch_mac, vtep_ip, 'controller') in db_entries:
                # db have exist this mac
                pass
            elif switch_mac in db_macs:
                # switch_mac have exist, need update
                changed_mac_list.append(
                    {'id': id_map[switch_mac], 'mac_vni': switch_mac, 'vtep_ip': vtep_ip})
            else:
                new_mac_list.append({'mac_vni': switch_mac, 'vtep_ip': vtep_ip, 'source': 'controller'})
        aged_mac_list = list(set(db_macs) - have_switch_macs)
        vtep_db.save_mac_vtep_to_db(new_mac_list)
        vtep_db.remove_mac_vtep_to_db(aged_mac_list)
        vtep_db.update_mac_vtep_to_db(changed_mac_list)

    def sync_binding_ports(self):
        db_port_bind = vtep_db.get_vlan_vni_binding(self.sn)
        # need get the VLAN binding and mapping
        port_bind = self.get_port_binding()
        differ_vlans = list(diff(port_bind, db_port_bind))
        if differ_vlans:
            LOG.warn("::::There is diffence between Switch and DB  %s", differ_vlans)
            for delta_item in differ_vlans:
                op, key, values = delta_item
                if op == 'add':
                    if key == '':
                        LOG.debug("::::There is mismatch port config: %s,change the config in switch %s",
                                 values, self.sn)
                        # In this case, db should del switch physical port
                        del_physical_port = []
                        for port in values:
                            del_physical_port.append(port[0])
                        vtep_db.del_physical_port(self.sn, del_physical_port)
                    else:
                        configured_vlans = vtep_db.get_all_configed_vlans()
                        for vlan in values:
                            # in here, vlan is [(300, 10300)], inside for, is (300,10300)
                            if not int(vlan[0]) in configured_vlans:
                                # delete the vlan in port_binding if it is not in configured vlan
                                vtep_db.del_vlan_binding_by_sn(vlan[0], self.sn)
                            else:
                                LOG.debug("::::Need add VLANs %s for switch %s port %s", values, self.sn, key)
                                self.add_port_binding(port=key, vlan=vlan[0])
                elif op == 'remove':
                    if key == '':
                        LOG.debug("::::There is mismatch port config: %s please change the config in switch %s",
                                  values, self.sn)
                        # In this case, db should add new switch physical port
                        new_physical_port = []
                        for port in values:
                            new_physical_port.append(port)
                        vtep_db.add_new_physical_port(self.sn, new_physical_port)
                    else:
                        LOG.debug("::::Need del VLANs %s for switch %s port %s", values, self.sn, key)
                        for vlan in values:
                            # in here, vlan is [(300, 10300)], inside for, is (300,10300)
                            self.del_port_binding(port=key, vlan=vlan[0])

    def sync_vlan(self):
        db_configed_vlans = set(vtep_db.get_all_configed_vlans())
        # need get the VLAN list
        switch_vlans = set(self.get_vlans_list())

        self.add_logical_switches(db_configed_vlans - switch_vlans)
        
        self.del_logical_switches(switch_vlans - db_configed_vlans)

    def sync_tunnel_ip(self):
        local_vtep_ip = self.get_vtep_tunnel_ip()
        if local_vtep_ip:
            vtep_db.update_model(VtepControlSwitch, filters={'sn': [self.sn]},
                                 updates={VtepControlSwitch.local_vtep_ip: local_vtep_ip})

    def sync_switch_config_status(self):
        switch_fault_status = self.get_switch_status()
        for status in switch_fault_status:
            if status == 'configure_failed':
                vtep_db.update_switch_config_status(self.sn, C.OVSDB_CONFIG_ERROR, reason='configure_failed')
                return

        vtep_db.update_switch_config_status(self.sn, C.OVSDB_CONFIG_NORMAL)
