import {request} from "@/utils/common/request";

const baseURL = "/ampcon/monitor";

export function fetchPushConfigTask(page, pageSize, filterFields = [], sortFields = [], searchFields = {}) {
    if (sortFields.length === 0) {
        sortFields.push({field: "create_time", order: "desc"});
    }
    return request({
        url: `${baseURL}/push_config_tasks/data_all`,
        method: "POST",
        data: {
            filterFields,
            sortFields,
            searchFields,
            page,
            pageSize
        }
    });
}

export function readPushConfig(taskId) {
    return request({
        url: `${baseURL}/read_config/${taskId}`,
        method: "Get"
    });
}

export function delPushConfig(taskName) {
    return request({
        url: `${baseURL}/del_push_config/${taskName}`,
        method: "Get"
    });
}

export function getConfigContent(taskName) {
    return request({
        url: `${baseURL}/get_config_content/${taskName}`,
        method: "Get"
    });
}

export function fetchTaskList(taskName) {
    return request({
        url: `${baseURL}/get_push_config_task_items/${taskName}`,
        method: "Get"
    });
}

export function getSwitchPushLog(taskName, sn) {
    return request({
        url: `${baseURL}/show_switch_ret/${taskName}/${sn}`,
        method: "Get"
    });
}

export function getSwitchAlarm(startTime, endTime) {
    return request({
        url: `${baseURL}/get_switch_alarm`,
        method: "POST",
        data: {
            startTime,
            endTime
        }
    });
}

export function getHistorySwitchAlarm(startTime, endTime) {
    return request({
        url: `${baseURL}/get_history_switch_alarm`,
        method: "POST",
        data: {
            startTime,
            endTime
        }
    });
}

export function ackSwitchAlarm(data) {
    return request({
        url: `${baseURL}/ack_switch_alarm`,
        method: "POST",
        data
    });
}
export function clearSwitchHistoryAlarm(data) {
    return request({
        url: `${baseURL}/clear_switch_history_alarm`,
        method: "POST",
        data
    });
}

export function getAllAlarmList(page, pageSize, filterFields = [], sortFields = [], searchFields = {}) {
    return request({
        url: `${baseURL}/get_all_alarm`,
        method: "POST",
        data: {
            filterFields,
            sortFields,
            searchFields,
            page,
            pageSize
        }
    });
}
export function getUnreadAlarmList(page, pageSize, filterFields = [], sortFields = [], searchFields = {}) {
    return request({
        url: `${baseURL}/get_unread_alarm`,
        method: "POST",
        data: {
            filterFields,
            sortFields,
            searchFields,
            page,
            pageSize
        }
    });
}

export function updateAlarmStatus(id) {
    return request({
        url: `${baseURL}/update_alarm_status`,
        method: "POST",
        data: {id}
    });
}

export function removeAlarmsAPI(id) {
    return request({
        url: `${baseURL}/delete_alarm/${id}`,
        method: "POST"
    });
}

export function getDlbTableAPI() {
    return request({
        url: `${baseURL}/get_dlb_table`,
        method: "POST"
    });
}

export function getTopologyList() {
    return request({
        url: `${baseURL}/get_topology_list`,
        method: "Get"
    });
}

export function setDefaultTopology(data) {
    return request({
        url: `${baseURL}/set_default_topology`,
        method: "POST",
        data
    });
}

export function addTopology(data) {
    return request({
        url: `${baseURL}/add_topology`,
        method: "POST",
        data
    });
}

export function editTopology(data) {
    return request({
        url: `${baseURL}/edit_topology`,
        method: "POST",
        data
    });
}

export function delTopology(id) {
    return request({
        url: `${baseURL}/del_topology`,
        method: "POST",
        data: {id}
    });
}

export function getTopology(data) {
    return request({
        url: `${baseURL}/get_topology`,
        method: "POST",
        data
    });
}

export function saveTopologyDetail(data) {
    return request({
        url: `${baseURL}/save_topology`,
        method: "POST",
        data
    });
}

export function fetchAddDeviceModalTableData(
    snList,
    page,
    pageSize,
    filterFields = [],
    sortFields = [],
    searchFields = {}
) {
    return request({
        url: `${baseURL}/get_add_device_modal_table_data`,
        method: "POST",
        data: {
            snList,
            filterFields,
            sortFields,
            searchFields,
            page,
            pageSize
        }
    });
}

export function autoDiscoverTopology(monitorTargetIds) {
    return request({
        url: `${baseURL}/refresh_lldp_info`,
        method: "POST",
        data: {monitorTargetIds}
    });
}

export function fetchTopologyToBeAddedSwitch(switchIdList, otnIpList) {
    return request({
        url: `${baseURL}/get_topology_to_be_added_switch`,
        method: "POST",
        data: {switchIdList, otnIpList}
    });
}

export function fetchInterfaceInfo(target, date) {
    return request({
        url: `${baseURL}/get_interface_info`,
        method: "POST",
        data: {
            target,
            date
        }
    });
}

export function fetchAIInfo(target, date) {
    return request({
        url: `${baseURL}/get_ai_info`,
        method: "POST",
        data: {
            target,
            date
        }
    });
}

export function fetchModulesInfo(target, date) {
    return request({
        url: `${baseURL}/get_modules_info`,
        method: "POST",
        data: {
            target,
            date
        }
    });
}

export function fetchUsageTopK(metricName, topK, target, startTime, endTime) {
    return request({
        url: `${baseURL}/get_usage_topk`,
        method: "POST",
        data: {
            metricName,
            topK,
            target,
            startTime,
            endTime
        }
    });
}

export function fetchInterfaceTopK(metricName, topK, target, startTime, endTime) {
    return request({
        url: `${baseURL}/get_interfaces_topk`,
        method: "POST",
        data: {
            metricName,
            topK,
            target,
            startTime,
            endTime
        }
    });
}

export function fetchModulesTopK(metricName, topK, target, startTime, endTime) {
    return request({
        url: `${baseURL}/get_modules_topk`,
        method: "POST",
        data: {
            metricName,
            topK,
            target,
            startTime,
            endTime
        }
    });
}

export function fetchRateTopK(metricName, topK, target, startTime, endTime) {
    return request({
        url: `${baseURL}/get_rate_topk`,
        method: "POST",
        data: {
            metricName,
            topK,
            target,
            startTime,
            endTime
        }
    });
}

export function fetchOTNPortInfo(otnIp) {
    return request({
        url: `${baseURL}/get_otn_port_info`,
        method: "POST",
        data: {
            otnIp
        }
    });
}

export function fetchNICsInfo(page, pageSize, filterFields = [], sortFields = [], searchFields = {}) {
    return request({
        url: `${baseURL}/get_nic_info`,
        method: "POST",
        data: {
            page,
            pageSize,
            filterFields,
            sortFields,
            searchFields
        }
    });
}

export function fetchNICsTopK(metricName, topK, instance, startTime, endTime, filter) {
    return request({
        url: `${baseURL}/get_nic_topk`,
        method: "POST",
        data: {
            metricName,
            topK,
            instance,
            startTime,
            endTime,
            filter
        }
    });
}

export function getNICsHistoryInfo(startTime, endTime) {
    return request({
        url: `${baseURL}/get_nic_history_info`,
        method: "POST",
        data: {
            startTime,
            endTime
        }
    });
}

export function fetchMacTable(target, date) {
    return request({
        url: `${baseURL}/get_mac_table`,
        method: "POST",
        data: {
            target,
            date
        }
    });
}

export function fetchArpTable(target, date) {
    return request({
        url: `${baseURL}/get_arp_table`,
        method: "POST",
        data: {
            target,
            date
        }
    });
}


export function fetchDLBTopK(metricName, topK, target, startTime, endTime, filter) {
    return request({
        url: `${baseURL}/get_dlb_topk`,
        method: "POST",
        data: {
            metricName,
            topK,
            target,
            startTime,
            endTime,
            filter
        }
    });
} 


export function fetchInterfaceList(target, date) {
    return request({
        url: `${baseURL}/get_target_interface`,
        method: "POST",
        data: {
            target,
            date
        }
    });
}