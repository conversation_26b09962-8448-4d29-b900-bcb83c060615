import {
    AmpConCustomTable,
    createColumnConfig,
    TableFilterDropdown,
    AmpConCustomModalTABTable
} from "@/modules-ampcon/components/custom_table";
import {Space, message, Tag} from "antd";
import {
    fetchTaskResultInfo,
    fetchTaskResultInfoById,
    fetchTaskResultInfoBySn
} from "@/modules-ampcon/apis/automation_api";
import TextArea from "antd/es/input/TextArea";
import {useState} from "react";

const matchFieldsList = [
    {name: "switch_sn", matchMode: "fuzzy"},
    {name: "task_name", matchMode: "fuzzy"},
    {name: "duration", matchMode: "fuzzy"},
    {name: "job_name", matchMode: "fuzzy"}
];

const searchFieldsList = ["switch_sn", "task_name", "duration", "job_name"];

const JobLogView = ({textAreaValue}) => {
    return (
        <div
            style={{
                minHeight: "auto",
                maxHeight: "fit-content",
                overflow: "auto",
                margin: "16px 0"
            }}
        >
            <TextArea autoSize={{minRows: 12, maxRows: 14}} value={textAreaValue} readOnly />
        </div>
    );
};

export const JobResultView = ({
    useJob,
    extraParams,
    textAreaValue,
    isModalOpen,
    setIsModalOpen,
    isModalOpenItems,
    setIsModalOpenItems
}) => {
    const [itemLogInfo, setItemLogInfo] = useState("");
    const [itemLogStandardInfo, setItemLogStandardInfo] = useState("");

    const columnsJob = [
        createColumnConfig("Task Name", "task_name", TableFilterDropdown),
        createColumnConfig("SN", "switch_sn", TableFilterDropdown),
        createColumnConfig("Modified Time", "start_time", TableFilterDropdown),
        createColumnConfig("Duration", "duration", TableFilterDropdown)
    ];

    const columnsSwitch = [
        createColumnConfig("Job Name", "job_name", TableFilterDropdown),
        createColumnConfig("Task Name", "task_name", TableFilterDropdown),
        createColumnConfig("Modified Time", "start_time", TableFilterDropdown),
        createColumnConfig("Duration", "duration", TableFilterDropdown)
    ];

    const columns = [
        ...(useJob ? columnsJob : columnsSwitch),
        {
            ...createColumnConfig("State", "state", TableFilterDropdown),
            render: (_, record) => {
                return (
                    <div>
                        <Space size="small">
                            {record.state ? (
                                <Tag className="successTag">Success</Tag>
                            ) : (
                                <Tag className="failedTag">Failed</Tag>
                            )}
                        </Space>
                    </div>
                );
            }
        },
        {
            title: "Operation",
            render: (_, record) => {
                return (
                    <div>
                        <Space size="large" className="actionLink">
                            <a
                                onClick={() => {
                                    setIsModalOpenItems(true);
                                    fetchTaskResultInfoById(record.id).then(res => {
                                        if (res.status === 200) {
                                            setItemLogInfo(res.info);
                                            const output = JSON.parse(res.info);
                                            const exec_stdout_lines = output["exec_result.stdout_lines"];
                                            const {stdout_lines} = output;
                                            const standard_output =
                                                (exec_stdout_lines && exec_stdout_lines.join("\n")) ||
                                                (stdout_lines && stdout_lines.join("\n")) ||
                                                "";
                                            setItemLogStandardInfo(standard_output);
                                        } else {
                                            message.error("Failed to fetch task output detail info");
                                        }
                                    });
                                }}
                            >
                                Show Result
                            </a>
                        </Space>
                    </div>
                );
            }
        }
    ];

    const items = [
        {
            key: "ret_table",
            label: "Result Table",
            children: (
                <AmpConCustomTable
                    columns={columns}
                    searchFieldsList={searchFieldsList}
                    matchFieldsList={matchFieldsList}
                    fetchAPIInfo={useJob ? fetchTaskResultInfo : fetchTaskResultInfoBySn}
                    fetchAPIParams={[extraParams]}
                />
            )
        },
        {
            key: "ret_output",
            label: "Result Output",
            children: <JobLogView textAreaValue={textAreaValue} />
        }
    ];

    const itemsLog = [
        {
            key: "json",
            label: "JSON",
            children: <JobLogView textAreaValue={itemLogInfo} />
        },
        {
            key: "standard out",
            label: "STANDARD OUT",
            children: <JobLogView textAreaValue={itemLogStandardInfo} />
        }
    ];

    return (
        <>
            <AmpConCustomModalTABTable
                title="Task Results"
                selectModalOpen={isModalOpen}
                onCancel={() => {
                    setIsModalOpen(false);
                }}
                items={items}
                modalClass="ampcon-max-modal"
            />
            <AmpConCustomModalTABTable
                title="Result"
                selectModalOpen={isModalOpenItems}
                onCancel={() => {
                    setIsModalOpenItems(false);
                }}
                items={itemsLog}
                modalClass="ampcon-max-modal"
            />
        </>
    );
};
