import sqlalchemy
import os
from sqlalchemy import pool, event, exc
from sqlalchemy.orm import sessionmaker, scoped_session
from sqlalchemy.engine.url import make_url
import logging
from sqlalchemy.util import ScopedRegistry
import threading

LOG = logging.getLogger(__name__)


def create_engine(sql_connection, idle_timeout=3600,
                  connection_debug=0, max_pool_size=None, max_overflow=None,
                  pool_timeout=None, logging_name=None):
    """Return a new SQLAlchemy engine."""

    url = make_url(sql_connection)

    engine_args = {
        "pool_recycle": idle_timeout,
        'convert_unicode': True,
        'connect_args': {},
        'logging_name': logging_name
        # 'echo': True
    }

    _setup_logging(connection_debug)

    _init_connection_args(
        url, engine_args,
        max_pool_size=max_pool_size,
        max_overflow=max_overflow,
        pool_timeout=pool_timeout
    )

    engine = sqlalchemy.create_engine(url, **engine_args)

    return engine


def _init_connection_args(url, engine_args, max_pool_size=None,
                          max_overflow=None, pool_timeout=None):

    pool_class = url.get_dialect().get_pool_class(url)
    if issubclass(pool_class, pool.QueuePool):
        if max_pool_size is not None:
            engine_args['pool_size'] = max_pool_size
        if max_overflow is not None:
            engine_args['max_overflow'] = max_overflow
        if pool_timeout is not None:
            engine_args['pool_timeout'] = pool_timeout


def _setup_logging(connection_debug=0):
    """setup_logging function maps SQL debug level to Python log level.

    Connection_debug is a verbosity of SQL debugging information.
    0=None(default value),
    1=Processed only messages with WARNING level or higher
    50=Processed only messages with INFO level or higher
    100=Processed only messages with DEBUG level
    """
    if connection_debug >= 0:
        logger = logging.getLogger('sqlalchemy.engine')
        if connection_debug >= 100:
            logger.setLevel(logging.DEBUG)
        elif connection_debug >= 50:
            logger.setLevel(logging.INFO)
        else:
            logger.setLevel(logging.WARNING)


def get_maker(engine, autoflush=True, autocommit=True, expire_on_commit=False):
    """Return a SQLAlchemy sessionmaker using the given engine."""
    return scoped_session(sessionmaker(bind=engine,
                          autoflush=autoflush,
                          autocommit=autocommit,
                          expire_on_commit=expire_on_commit), threading.current_thread)
    # return sessionmaker(bind=engine,
    #                     autoflush=autoflush,
    #                     autocommit=autocommit,
    #                     expire_on_commit=expire_on_commit)


class EngineFacade(object):
    """A helper class for removing of global engine instances from oslo.db.

    As a library, oslo.db can't decide where to store/when to create engine
    and sessionmaker instances, so this must be left for a target application.

    On the other hand, in order to simplify the adoption of oslo.db changes,
    we'll provide a helper class, which creates engine and sessionmaker
    on its instantiation and provides get_engine()/get_session() methods
    that are compatible with corresponding utility functions that currently
    exist in target projects, e.g. in Nova.

    engine/sessionmaker instances will still be global (and they are meant to
    be global), but they will be stored in the app context, rather that in the
    oslo.db context.

    Note: using of this helper is completely optional and you are encouraged to
    integrate engine/sessionmaker instances into your apps any way you like
    (e.g. one might want to bind a session to a request context). Two important
    things to remember:

    1. An Engine instance is effectively a pool of DB connections, so it's
       meant to be shared (and it's thread-safe).
    2. A Session instance is not meant to be shared and represents a DB
       transactional context (i.e. it's not thread-safe). sessionmaker is
       a factory of sessions.

    """

    def __init__(self, sql_connection, autocommit=True,
                 expire_on_commit=False, **kwargs):
        """Initialize engine and sessionmaker instances.

        :param sql_connection: the connection string for the database to use
        :type sql_connection: string

        :param autocommit: use autocommit mode for created Session instances
        :type autocommit: bool

        :param expire_on_commit: expire session objects on commit
        :type expire_on_commit: bool

        Keyword arguments:

        :keyword idle_timeout: timeout before idle sql connections are reaped
                               (defaults to 3600)
        :keyword connection_debug: verbosity of SQL debugging information.
                                   -1=Off, 0=None, 100=Everything (defaults
                                   to 0)
        :keyword max_pool_size: maximum number of SQL connections to keep open
                                in a pool (defaults to SQLAlchemy settings)
        :keyword max_overflow: if set, use this value for max_overflow with
                               sqlalchemy (defaults to SQLAlchemy settings)
        :keyword pool_timeout: if set, use this value for pool_timeout with
                               sqlalchemy (defaults to SQLAlchemy settings)
        :keyword sqlite_synchronous: if True, SQLite uses synchronous mode
                                     (defaults to True)
        :keyword connection_trace: add python stack traces to SQL as comment
                                   strings (defaults to False)
        """

        super(EngineFacade, self).__init__()

        engine_kwargs = {
            'idle_timeout': kwargs.get('idle_timeout', 3600),
            'connection_debug': kwargs.get('connection_debug', 0),
            'max_pool_size': kwargs.get('max_pool_size'),
            'max_overflow': kwargs.get('max_overflow'),
            'pool_timeout': kwargs.get('pool_timeout')
        }
        maker_kwargs = {
            'autocommit': autocommit,
            'expire_on_commit': expire_on_commit
        }

        self._engine = create_engine(sql_connection=sql_connection,
                                     **engine_kwargs)

        self._add_engine_pidguard(self._engine)
        self._session_maker = get_maker(engine=self._engine,
                                        **maker_kwargs)

    def _add_engine_pidguard(self, engine):
        """Add multiprocessing guards.

        Forces a connection to be reconnected if it is detected
        as having been shared to a sub-process.

        """
        @event.listens_for(engine, "connect")
        def connect(dbapi_connection, connection_record):
            connection_record.info['pid'] = os.getpid()

        @event.listens_for(engine, "checkout")
        def checkout(dbapi_connection, connection_record, connection_proxy):
            pid = os.getpid()

            if connection_record.info['pid'] != pid:
                # substitute log.debug() or similar here as desired
                LOG.warning(
                  "Parent process %(orig)s forked (%(newproc)s) with an open "
                  "database connection, "
                  "which is being discarded and recreated." %
                  {"newproc": pid, "orig": connection_record.info['pid']})
                connection_record.connection = connection_proxy.connection = None
                raise exc.DisconnectionError(
                        "Connection record belongs to pid %s, "
                        "attempting to check out in pid %s" %
                        (connection_record.info['pid'], pid))

    def get_engine(self):
        """Get the engine instance (note, that it's shared).

        """
        return self._engine

    def get_session(self, **kwargs):
        """Get a Session instance.

        Keyword arugments will be passed to a sessionmaker instance as is (if
        passed, they will override the ones used when the sessionmaker instance
        was created). See SQLAlchemy Session docs for details.

        """

        return self._session_maker(**kwargs)

    def remove_session(self):
        return self._session_maker.remove()

    @classmethod
    def from_config(cls, conf, autocommit=True, expire_on_commit=False):
        """Initialize EngineFacade using oslo.config config instance options.

        :param conf: oslo.config config instance
        :type conf: oslo.config.cfg.ConfigOpts

        :param autocommit: use autocommit mode for created Session instances
        :type autocommit: bool

        :param expire_on_commit: expire session objects on commit
        :type expire_on_commit: bool

        """

        return cls(sql_connection=conf.database.connection,
                   autocommit=autocommit,
                   expire_on_commit=expire_on_commit,
                   idle_timeout=conf.database.idle_timeout,
                   connection_debug=conf.database.connection_debug,
                   max_pool_size=conf.database.max_pool_size,
                   max_overflow=conf.database.max_overflow,
                   pool_timeout=conf.database.pool_timeout)


