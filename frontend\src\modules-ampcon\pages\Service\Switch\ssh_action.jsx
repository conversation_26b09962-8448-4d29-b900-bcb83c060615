import {useState} from "react";
import {AmpConCustomModalForm} from "@/modules-ampcon/components/custom_table";
import {useForm} from "antd/es/form/Form";
import {Form, Input} from "antd";

const SSHAction = ({record}) => {
    const [sshModal, setSSHModal] = useState(false);
    const [sshForm] = useForm();

    const sshSubmit = values => {
        const url = `/ssh/?${btoa(`${record.mgt_ip};22;${values.username};${values.password}`)}`;
        window.open(url, "_blank", "noopener,noreferrer");
        setSSHModal(false);
        sshForm.resetFields();
    };

    const sshFormRender = () => {
        return (
            <>
                <Form.Item
                    name="username"
                    label="Username"
                    rules={[{required: true, message: "Please input username."}]}
                >
                    <Input style={{width: "280px"}} />
                </Form.Item>
                <Form.Item
                    name="password"
                    label="Password"
                    rules={[{required: true, message: "Please input password."}]}
                >
                    <Input.Password style={{width: "280px"}} />
                </Form.Item>
            </>
        );
    };

    return (
        <>
            {(record.status === "Provisioning Success" || record.status === "Imported") && (
                <a
                    onClick={() => {
                        setSSHModal(true);
                    }}
                >
                    SSH
                </a>
            )}
            <AmpConCustomModalForm
                title="SSH"
                isModalOpen={sshModal}
                formInstance={sshForm}
                layoutProps={{
                    labelCol: {
                        span: 6
                    }
                }}
                CustomFormItems={sshFormRender}
                onCancel={() => {
                    sshForm.resetFields();
                    setSSHModal(false);
                }}
                onSubmit={sshSubmit}
                modalClass="ampcon-middle-modal"
            />
        </>
    );
};

export default SSHAction;
