# -*- coding: utf-8 -*-
import time
from datetime import datetime, timedelta
from sqlalchemy import (
    Column,
    Integer,
    String,
    Text,
    Enum,
    DateTime
)

from server.db.db_common import DBCommon
from server.db.models.base import Base


class OperationLog(Base):
    __tablename__ = 'operation_log'
    id = Column(Integer, autoincrement=True, primary_key=True)
    user = Column(String(32), nullable=False)
    path = Column(String(255), nullable=False)
    method = Column(String(255))
    params_original = Column('params', Text(65535))
    content_original = Column('content', Text(65535), nullable=False)
    status = Column(String(256))

    @property
    def params(self):
        return self.params_original

    @property
    def content(self):
        return self.content_original

    @params.setter
    def params(self, params_original):
        self.params_original = params_original

    @content.setter
    def content(self, content_original):
        self.content_original = content_original


class Event(Base):
    __tablename__ = 'event'
    id = Column(Integer, autoincrement=True, primary_key=True)
    sn = Column(String(64))
    resource_id = Column(String(64))
    resource_name = Column(String(32))
    type = Column(Enum('info', 'warn', 'error'), nullable=False, default='info')
    msg = Column(String(255), nullable=False)
    count = Column(Integer, default=1)
    status = Column(Enum('unread', 'read', 'ignore', 'handled'), default='unread')
    operator_name = Column(String(32))
    operator_text = Column(Text(2048))
    operator_time = Column(DateTime())
    history_time = Column(Text(2048))

    def __init__(self, sn, type, msg, resource_id=None, resource_name=None):
        self.sn = sn
        self.type = type
        self.msg = msg
        self.resource_id = resource_id
        self.resource_name = resource_name

class LicenseCount(Base):
    """
    Deprecated (use LicenseStatisttic)
    """
    __tablename__ = 'license_count'
    id = Column(Integer, autoincrement=True, primary_key=True)
    speed_type = Column(String(32))
    feature_type = Column(String(32))
    remain = Column(Integer, nullable=False, default=0)
    total = Column(Integer, nullable=False, default=0)

class LicenseStatisttic(Base):
    __tablename__ = 'license_statistic'
    id = Column(Integer, autoincrement=True, primary_key=True)
    remain = Column(Integer, nullable=False, default=0)
    total = Column(Integer, nullable=False, default=0)

class MonitorDB(DBCommon):

    def add_event(self, sn, event_type, msg, resource_id=None, resource_name=None, session=None):
        session = session or self.get_session()
        msg = msg if len(msg) < 255 else msg[-254:]
        with session.begin(subtransactions=True):
            event = session.query(Event).filter_by(sn=sn, type=event_type, msg=msg, status='unread').first()
            if event:
                count = event.count + 1
                event.count = count
                if event.history_time:
                    history_time_list = event.history_time.split(';')
                    if len(history_time_list) > 12:
                        history_time_list = [history_time_list[0]] + history_time_list[-11:] + [time.strftime("%Y-%m-%d %H:%M:%S")]
                    else:
                        history_time_list = history_time_list + [time.strftime("%Y-%m-%d %H:%M:%S")]
                    history_time = ';'.join(history_time_list)
                else:
                    history_time = str(event.create_time) + ';' + time.strftime("%Y-%m-%d %H:%M:%S")
                event.history_time = history_time
            else:
                event = Event(sn, event_type, msg, resource_id, resource_name)
                event.history_time = time.strftime("%Y-%m-%d %H:%M:%S")
                session.add(event)

    def update_event_status(self, event_id, status):
        session = self.get_session()
        with session.begin(subtransactions=True):
            event = session.query(Event).filter(Event.id == event_id).first()
            event.status = status

    def handle_event(self, sn, resource_id, msg):
        session = self.get_session()
        with session.begin(subtransactions=True):
            event = session.query(Event).filter(Event.sn == sn, Event.resource_id == resource_id, Event.status == "unread").first()
            event.status = "handled"
            # event.msg = msg

    def update_license_count(self, total, remaining):
        session = self.get_session()
        with session.begin(subtransactions=True):
            license_count = session.query(LicenseCount).first()
            if license_count:
                license_count.total = total
                license_count.remain = remaining
            else:
                license_count = LicenseCount(total=total, remain=remaining)
                session.add(license_count)

    def add_operation_log(self, user, path, method, status, params='', content='', session=None):
        session = session or self.get_session()
        with session.begin(subtransactions=True):
            operation_log = OperationLog(user=user, path=path, method=method, params=params, content=content,
                                         status=status)
            session.add(operation_log)
            
    def record_failed_auth_attempts(self, user, session=None):
        session = session or self.get_session()
        now = datetime.now()
        five_minutes_ago = now - timedelta(minutes=5)
        last_success = session.query(OperationLog).filter(OperationLog.user == user, OperationLog.method == 'login', OperationLog.status == 'success') \
                                                  .order_by(OperationLog.modified_time.desc()) \
                                                  .first()
                                                  
        if last_success and last_success.modified_time >= five_minutes_ago:
            nums = session.query(OperationLog).filter(OperationLog.user == user,
                                                    OperationLog.method == 'login',
                                                    OperationLog.modified_time >= last_success.modified_time,
                                                    OperationLog.status == 'error',
                                                    OperationLog.modified_time <= now).count()
        else:
            nums = session.query(OperationLog).filter(OperationLog.user == user,
                                                    OperationLog.method == 'login',
                                                    OperationLog.modified_time >= five_minutes_ago,
                                                    OperationLog.status == 'error',
                                                    OperationLog.modified_time <= now).count()
        if nums >= 3:
            return True
        return False
    
    def unlock_auth_attempts(self, user, session=None):
        session = session or self.get_session()
        now = datetime.now()
        five_minutes_ago = now - timedelta(minutes=5)
        session.query(OperationLog).filter(OperationLog.user == user,
                                           OperationLog.method == 'login',
                                           OperationLog.modified_time >= five_minutes_ago,
                                           OperationLog.status == 'error',
                                           OperationLog.modified_time <= now).update({OperationLog.status: 'unlock'}, synchronize_session=False)


monitor_db = MonitorDB()
