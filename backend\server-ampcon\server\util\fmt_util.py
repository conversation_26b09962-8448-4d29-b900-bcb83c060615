import json
import logging
import time
import traceback
import redis

from server.db.models import inventory
from server.db.models.otn import OtnTempData, OtnDeviceBasic, FmtDeviceBasic, FmtDeviceCards, DcsDeviceBasic, \
    DcsDeviceCards
from server.util.redis_distributed_lock import DistributedLock
from server.util.socket_client import SocketClient
import socket
import copy
import re
import threading
from concurrent.futures import ThreadPoolExecutor
from server import cfg
from south_api.ssh_api import description_re

invent_db = inventory.inven_db
LOG = logging.getLogger(__name__)

# 包含FMT和DCS设备类型
FMT_CONFIG_MODEL = {
    "FMT": {
        "nmuMapping": {
            "SN": "SN",
            "Model": "DTP",
            "Production date": "MD",
            "Software version": "SV",
            "Hardware version": "HV",
            "IP": "IP",
            "Mask": "MSK",
            "Gateway": "GW",
            "Mac": "MAC",
            "Key": "KEY",
            "BZC": "BZC",
            "BZS": "BZS",
            "FNC": "FNC",
            "FNS": "FNS",
            "PWR": "PWR"
        },
        "boardMapping": {
            "0": {
                "boardType": "NMU",
                "analysisMode": 0,
                "basicInfo": {
                    "Board model": "DTP",
                    "Serial number": "SN",
                    "Production date": "MD",
                    "Hardware version": "HV",
                    "Software version": "SV",
                    "Temperature": "MTV"
                },
                "businessInfo": {
                    "config": {
                        "trap_address": "SNMPMIP"
                    }
                }
            },
            "0101": {
                "boardType": "OLP",
                "analysisMode": 2,
                "basicInfo": {
                    "Board model": "DT",
                    "Serial number": "SN",
                    "Production date": "MD",
                    "Hardware version": "HV",
                    "Software version": "SV",
                    "Temperature": "MTV"
                },
                "businessInfo": {
                    "query": {
                        "Power": {
                            "r1": "R1_P",
                            "r2": "R2_P",
                            "tx": "TX_P",
                            "ls": "LS_P",
                            "alarm": "ALM"
                        }
                    },
                    "config": {
                        "Alarm Limit": {
                            "r1": "R1_AP",
                            "r2": "R2_AP",
                            "tx": "TX_AP",
                            "ls": "LS_AP"
                        },
                        "Switch Parameter": {
                            "r1_switch_limit": "R1_SP",
                            "r2_switch_limit": "R2_SP",
                            "return_delay": "Q",
                            "switch_delay": "R"
                        },
                        "Working Parameter": {
                            "work_mode": "M",
                            "work_route": "S",
                            "return_mode": "ACC"
                        }
                    }
                }
            },
            "0102": {
                "boardType": "OLP",
                "analysisMode": 2,
                "basicInfo": {
                    "Board model": "DT",
                    "Serial number": "SN",
                    "Production date": "MD",
                    "Hardware version": "HV",
                    "Software version": "SV",
                    "Temperature": "MTV"
                },
                "businessInfo": {
                    "query": {
                        "Power": {
                            "r1": "R1_P",
                            "r2": "R2_P",
                            "tx": "TX_P",
                            "ls": "LS_P",
                            "alarm": "ALM"
                        }
                    },
                    "config": {
                        "Alarm Limit": {
                            "r1": "R1_AP",
                            "r2": "R2_AP",
                            "tx": "TX_AP",
                            "ls": "LS_AP"
                        },
                        "Switch Parameter": {
                            "r1_switch_limit": "R1_SP",
                            "r2_switch_limit": "R2_SP",
                            "return_delay": "Q",
                            "switch_delay": "R"
                        },
                        "Working Parameter": {
                            "work_mode": "M",
                            "work_route": "S",
                            "return_mode": "ACC"
                        }
                    }
                }
            },
            # "1801": {
            #     "boardType": "DCM",
            #     "analysisMode": 1,
            #     "basicInfo": {
            #         "Board model": "DT",
            #         "Serial number": "SN",
            #         "Production date": "MD",
            #         "Hardware version": "HV",
            #         "Software version": "SV",
            #         "Temperature": "TMP"
            #     },
            #     "businessInfo": {
            #         "config": {
            #             "temperature": "TMP",
            #             "module_state": "STS",
            #             "tdc_value": "GSD",
            #             "tdc_setting_value": "GDS",
            #             "frequency_inter": "GCH",
            #             "tdc": "SDS",
            #             "frequency": "SCH"
            #         }
            #     }
            # },
            "2101": {
                "boardType": "TDCM",
                "analysisMode": 1,
                "basicInfo": {
                    "Board model": "DT",
                    "Serial number": "SN",
                    "Production date": "MD",
                    "Hardware version": "HV",
                    "Software version": "SV",
                    "Temperature": "TMP"
                },
                "businessInfo": {
                    "config": {
                        "temperature": "TMP",
                        "module_state": "STS",
                        "tdc_value": "GSD",
                        "tdc_setting_value": "GDS",
                        "frequency_inter": "GCH",
                        "tdc": "SDS",
                        "frequency": "SCH"
                    }
                }
            },
            # "2202": {
            #     "boardType": "OEO-100G",
            #     "analysisMode": 2,
            #     "basicInfo": {
            #         "Board model": "DT",
            #         "Serial number": "SN",
            #         "Production date": "MD",
            #         "Hardware version": "HV",
            #         "Software version": "SV",
            #         "Temperature": "MTV"
            #     },
            #     "businessInfo": {
            #         "query": {
            #             "tx1": ["M@_TX1_POWER"],
            #             "tx2": ["M@_TX2_POWER"],
            #             "tx3": ["M@_TX3_POWER"],
            #             "tx4": ["M@_TX4_POWER"],
            #             "rx1": ["M@_RX1_POWER"],
            #             "rx2": ["M@_RX2_POWER"],
            #             "rx3": ["M@_RX3_POWER"],
            #             "rx4": ["M@_RX4_POWER"],
            #             "max_wavelength": ["M@_MAX"],
            #             "min_wavelength": ["M@_MIN"],
            #             "rate": ["M@_RATE"],
            #             "tx_distance": ["M@_TD"],
            #             "module_temp": ["M@_T"]
            #         },
            #         "config": {
            #             "work_model_": ["M@_M"],
            #             "control_mode_": ["M@_CONFIG"],
            #             "alarm_limit_": ["M@_RX_ALARM"]
            #         }
            #     }
            # },
            "0301": {
                "boardType": "EDFA",
                "analysisMode": 1,
                "basicInfo": {
                    "Board model": "DT",
                    "Serial number": "SN",
                    "Production date": "MD",
                    "Hardware version": "HV",
                    "Software version": "SV",
                    "Temperature": "MTV"
                },
                "businessInfo": {
                    "query": {
                        "State": {
                            "gain": "PGV",
                            "module_temperature": "MTV",
                            "supply_voltage": "TEC",
                            "work_model": "AGC",
                            "input_power": "PWI",
                            "output_power": "PWO",
                            "low_input_power": "PIA",
                            "low_output_power": "POA",
                            "input_warning_threshold": "PIA",
                            "output_warning_threshold": "POA",
                            "pin_alarm": "PIN",
                            "pout_alarm": "POU",
                            "mt_alarm": "MT",
                            "pt_alarm": "PT"
                        },
                        "Pump state": {
                            "pump1_work_current": "PPV",
                            "pump2_work_current": "PPV",
                            "pump1_power": "PPV",
                            "pump2_power": "PPV",
                            "pump1_temperature": "PTV",
                            "pump2_temperature": "PTV",
                            "pump1_cooling_electricity": "PIV",
                            "pump2_cooling_electricity": "PIV",
                            "upper_pump1_temperature": "PTU",
                            "upper_pump2_temperature": "PTU",
                            "low_pump1_temperature": "PTD",
                            "low_pump2_temperature": "PTD",
                            "upper_module_temperature": "MTU",
                            "low_module_temperature": "MTD"
                        },
                        "output_adjustment": "POV",
                        "pump2_state": "PSW"
                    },
                    "config": {
                        "Gain": {
                            "gain_adjustment": "PGV"
                        },
                        "Pump": {
                            "pump1_state": "PSW"
                        },
                        "rx_power_alarm_threshold": "PIA",
                        "tx_power_alarm_threshold": "POA"
                    }
                }
            },
            "0303": {
                "boardType": "DEDFA",
                "analysisMode": 1,
                "basicInfo": {
                    "Board model": "DT",
                    "Serial number": "SN",
                    "Production date": "MD",
                    "Hardware version": "HV",
                    "Software version": "SV",
                    "Temperature": "MTV"
                },
                "businessInfo": {
                    "query": {
                        "State": {
                            "gain": "PGV",
                            "module_temperature": "MTV",
                            "supply_voltage": "TEC",
                            "work_model": "AGC",
                            "input_power1": "PWI#0",
                            "output_power1": "PWO#0",
                            "input_power2": "PWI#1",
                            "output_power2": "PWO#1",
                            "low_input_power": "PIA",
                            "low_output_power": "POA",
                            "input_warning_threshold": "PIA",
                            "output_warning_threshold": "POA",
                            "pin_alarm": "PIN",
                            "pout_alarm": "POU",
                            "mt_alarm": "MT",
                            "pt_alarm": "PT"
                        },
                        "Pump state": {
                            "pump1_work_current": "PPV",
                            "pump2_work_current": "PPV",
                            "pump1_power": "PPV",
                            "pump2_power": "PPV",
                            "pump1_temperature": "PTV",
                            "pump2_temperature": "PTV",
                            "pump1_cooling_electricity": "PIV",
                            "pump2_cooling_electricity": "PIV",
                            "upper_pump1_temperature": "PTU",
                            "upper_pump2_temperature": "PTU",
                            "low_pump1_temperature": "PTD",
                            "low_pump2_temperature": "PTD",
                            "upper_module_temperature": "MTU",
                            "low_module_temperature": "MTD"
                        },
                        "output_adjustment": "POV",
                        "pump2_state": "PSW"
                    },
                    "config": {
                        "Gain": {
                            "gain_adjustment": "PGV"
                        },
                        "Pump": {
                            "pump1_state": "PSW"
                        },
                        "rx_power_alarm_threshold": "PIA",
                        "tx_power_alarm_threshold": "POA"
                    }
                }
            },
            "0500": {
                "boardType": "OPD",
                "analysisMode": 2,
                "basicInfo": {
                    "Board model": "DT",
                    "Serial number": "SN",
                    "Production date": "MD",
                    "Hardware version": "HV",
                    "Software version": "SV",
                    "Temperature": "MTV"
                },
                "businessInfo": {
                    "query": {
                        "power": ["CH@_P"],
                        "route_type": ["CH@_A"],
                        "channel_description": ["CH@_P"]
                    },
                    "config": {
                        "threshold_": ["CH@_S"],
                        "wavelength_": ["CH@_W"]
                    }
                }
            },
            "0501": {
                "boardType": "OPD",
                "analysisMode": 2,
                "basicInfo": {
                    "Board model": "DT",
                    "Serial number": "SN",
                    "Production date": "MD",
                    "Hardware version": "HV",
                    "Software version": "SV",
                    "Temperature": "MTV"
                },
                "businessInfo": {
                    "query": {
                        "power": ["CH@_P"],
                        "route_type": ["CH@_S"]
                    },
                    "config": {
                        "wavelength_": ["CH@_W"]
                    }
                }
            },
            "0701": {
                "boardType": "OEO",
                "analysisMode": 2,
                "basicInfo": {
                    "Board model": "DT",
                    "Serial number": "SN",
                    "Production date": "MD",
                    "Hardware version": "HV",
                    "Software version": "SV",
                    "Temperature": "MTV"
                },
                "businessInfo": {
                    "query": {
                        "wavelength": ["M@_W"],
                        "transmission_distance": ["M@_TD"],
                        "input_power": ["M@_RXP"],
                        "output_power": ["M@_TXP"],
                        "module_temperature": ["M@_T"],
                        "rate": ["M@_R"],
                        # "service_notes": ["M@_MS"]
                    },
                    "config": {
                        "control_mode_": ["M@_PC"],
                        "work_model_": ["M@_M"],
                        "input_alarm_threshold_": ["M@_RXA"]
                    }
                }
            },
            # "0801": {
            #     "boardType": "R/B",
            #     "analysisMode": 1,
            #     "basicInfo": {
            #         "Board model": "DT",
            #         "Serial number": "SN",
            #         "Production date": "MD",
            #         "Hardware version": "HV",
            #         "Software version": "SV",
            #         "Temperature": "MTV"
            #     },
            #     "businessInfo": {
            #     }
            # },
            "1605": {
                "boardType": "VOA",
                "analysisMode": 1,
                "basicInfo": {
                    "Board model": "DT",
                    "Serial number": "SN",
                    "Production date": "MD",
                    "Hardware version": "HV",
                    "Software version": "SV",
                    "Temperature": "MTV"
                },
                "businessInfo": {
                    "query": {
                        "voa1_power": "CP1",
                        "voa1_attenuation": "CV1",
                        "voa2_power": "CP2",
                        "voa2_attenuation": "CV2",
                        "voa3_power": "CP3",
                        "voa3_attenuation": "CV3",
                        "voa4_power": "CP4",
                        "voa4_attenuation": "CV4"
                    },
                    "config": {
                        "voa1_power_configuration": "P1",
                        "voa1_attenuation_configuration": "V1",
                        "voa1_threshold": "RX1",
                        "voa1_work_mode": "M1",
                        "voa2_power_configuration": "P2",
                        "voa2_attenuation_configuration": "V2",
                        "voa2_threshold": "RX2",
                        "voa2_work_mode": "M2",
                        "voa3_power_configuration": "P3",
                        "voa3_attenuation_configuration": "V3",
                        "voa3_threshold": "RX3",
                        "voa3_work_mode": "M3",
                        "voa4_power_configuration": "P4",
                        "voa4_attenuation_configuration": "V4",
                        "voa4_threshold": "RX4",
                        "voa4_work_mode": "M4"
                    }
                }
            }
        }
    },
    "D6000": {
        "nmuMapping": {
            "SN": "SN",
            "Model": "DTP",
            "Production date": "MD",
            "Software version": "SV",
            "Hardware version": "HV",
            "IP": "IP",
            "Mask": "MSK",
            "Gateway": "GW",
            "Mac": "MAC",
            "Key": "KEY",
            "BZC": "BZC",
            "BZS": "BZS",
            "FNC": "FNC",
            "FNS": "FNS",
            "PWR": "PWR"
        },
        "boardMapping": {
            "0": {
                "boardType": "NMU",
                "analysisMode": 0,
                "basicInfo": {
                    "Board model": "DT",
                    "Serial number": "SN",
                    "Production date": "MD",
                    "Hardware version": "HV",
                    "Software version": "SV",
                    "Temperature": "MTV"
                },
                "businessInfo": {
                    "config": {
                        "trap_address": "SNMPMIP"
                    }
                }
            },
            "8001": {
                "boardType": "4ME4C",
                "analysisMode": 1,
                "basicInfo": {
                    "Board model": "DT",
                    "Serial number": "SN",
                    "Production date": "MD",
                    "Hardware version": "HV",
                    "Software version": "SV",
                    "Temperature": "MTV"
                },
                "businessInfo": {
                    "query": {
                        "existence": ["S@"],
                        "rate": ["RATE@"],
                        "temperature": ["T@"],
                        "transmission_distance": ["TD@"],
                        "rx_power": ["RX@"],
                        "rx_alarm": ["RXPA@"],
                        "tx_alarm": ["TXPA@"],
                        "temperature_alarm": ["TA@"],
                        "OSNR": ["OSNR@"],
                        "dispersion": ["DIS@"],
                        "module_type": ["TYPE@"],
                        "pre_fec": ["PREBER@"],
                        "post_fec": ["POSTBER@"],
                        "tx_lol_alarm": ["TXLOL@"],
                        "tx_los_alarm": ["TXLOS@"],
                        "rx_lol_alarm": ["RXLOL@"],
                        "rx_los_alarm": ["RXLOS@"],
                        "rx_lof_alarm": ["RXLOF@"],
                        "rx_lom_alarm": ["RXLOM@"],
                        "work_mode_": ["M@"],
                        "pn":["PN@"],
                        "sn":["SN@"]
                    },
                    "config": {
                        "tx_switch_": ["TXC@"],
                        "CH1_": ["M@"],
                        "CH2_": ["M@"],
                        "CH3_": ["M@"],
                        "CH4_": ["M@"],
                        "UNI4M_": ["M@"],
                        "UNI4T_": ["M@"],
                        "NNI4T_": ["M@"],
                        "modulation_": ["PM@"],
                        "overlow_input_threshold_": ["RXAL@"],
                        "overhigh_input_threshold_": ["RXAU@"],
                        "overlow_output_threshold_": ["TXAL@"],
                        "overhigh_output_threshold_": ["TXAU@"],
                        "overlow_input_threshold_value_": ["RXALOW@"],
                        "overhigh_input_threshold_value_": ["RXAUP@"],
                        "overlow_output_threshold_value_": ["TXALOW@"],
                        "overhigh_output_threshold_value_": ["TXAUP@"],
                        "fec_switch_": ["FEC@"],
                        "business_mode_": ["OTGE@"],
                        "als_": ["ALS@"],
                        "wavelength_Cvalue_": ["WA@"],
                        "wavelength_Lvalue_": ["TXCD@"],
                        "wavelength_": ["TCD@"],
                        "tx_power_value_": ["TX@"],
                        "tx_power_": ["TPW@"]
                    }
                }
            },
            "8601": {
                "boardType": "4T4E4C",
                "analysisMode": 1,
                "basicInfo": {
                    "Board model": "DT",
                    "Serial number": "SN",
                    "Production date": "MD",
                    "Hardware version": "HV",
                    "Software version": "SV",
                    "Temperature": "MTV"
                },
                "businessInfo": {
                    "query": {
                        "existence": ["S@"],
                        "rate": ["RATE@"],
                        "temperature": ["T@"],
                        "transmission_distance": ["TD@"],
                        "rx_power": ["RX@"],
                        "rx_alarm": ["RXPA@"],
                        "tx_alarm": ["TXPA@"],
                        "temperature_alarm": ["TA@"],
                        "OSNR": ["OSNR@"],
                        "dispersion": ["DIS@"],
                        "module_type": ["TYPE@"],
                        "pre_fec": ["PREBER@"],
                        "post_fec": ["POSTBER@"],
                        "tx_lol_alarm": ["TXLOL@"],
                        "tx_los_alarm": ["TXLOS@"],
                        "rx_lol_alarm": ["RXLOL@"],
                        "rx_los_alarm": ["RXLOS@"],
                        "rx_lof_alarm": ["RXLOF@"],
                        "rx_lom_alarm": ["RXLOM@"],
                        "work_mode_": ["M@"],
                        "pn":["PN@"],
                        "sn":["SN@"]
                    },
                    "config": {
                        "tx_switch_": ["TXC@"],
                        "CH1_": ["M@"],
                        "CH2_": ["M@"],
                        "CH3_": ["M@"],
                        "CH4_": ["M@"],
                        "UNI4M_": ["M@"],
                        "UNI4T_": ["M@"],
                        "NNI4T_": ["M@"],
                        "modulation_": ["PM@"],
                        "overlow_input_threshold_": ["RXAL@"],
                        "overhigh_input_threshold_": ["RXAU@"],
                        "overlow_output_threshold_": ["TXAL@"],
                        "overhigh_output_threshold_": ["TXAU@"],
                        "overlow_input_threshold_value_": ["RXALOW@"],
                        "overhigh_input_threshold_value_": ["RXAUP@"],
                        "overlow_output_threshold_value_": ["TXALOW@"],
                        "overhigh_output_threshold_value_": ["TXAUP@"],
                        "fec_switch_": ["FEC@"],
                        "business_mode_": ["OTGE@"],
                        "als_": ["ALS@"],
                        "wavelength_Cvalue_": ["WA@"],
                        "wavelength_Lvalue_": ["TXCD@"],
                        "wavelength_": ["TCD@"],
                        "tx_power_value_": ["TX@"],
                        "tx_power_": ["TPW@"]
                    }
                }
            },
            "6601": {
                "boardType": "NMU",
                "analysisMode": 1,
                "basicInfo": {
                    "Board model": "DT",
                    "Serial number": "SN",
                    "Production date": "MD",
                    "Hardware version": "HV",
                    "Software version": "SV",
                    "Temperature": "MTV"
                },
                "businessInfo": {
                    "query": {
                        "power_state1": "PN#0",
                        "power_state2": "PN#1",
                        "supply_mode1": "MP#0",
                        "supply_mode2": "MP#1",
                        "max_power1": "MRXP#0",
                        "max_power2": "MRXP#1",
                        "input_current1": "RXA#0",
                        "input_current2": "RXA#1",
                        "output_current1": "TXA#0",
                        "output_current2": "TXA#1",
                        "input_voltage1": "RXV#0",
                        "input_voltage2": "RXV#1",
                        "output_voltage1": "TXV#0",
                        "output_voltage2": "TXV#1",
                        "power_temperature1": "TE#0",
                        "power_temperature2": "TE#1",
                        "fan_state1": "BN#0",
                        "fan_state2": "BN#1",
                    },
                    "config": {
                        "power_switch1": "PS#0",
                        "power_switch2": "PS#1",
                        "fan_switch": "MF",
                        "fan_speed": "REV",
                    }
                }
            },
            "7601": {
                "boardType": "FAN",
                "analysisMode": 1,
                "basicInfo": {
                    "Board model": "DT",
                    "Serial number": "SN",
                    "Production date": "MD",
                    "Hardware version": "HV",
                    "Software version": "SV",
                    "Temperature": "MTV"
                },
                "businessInfo": {
                    "query": {
                        "state": "STA",
                        "rpm": "RPM",
                        "rpm2": "RPM2",
                    },
                    "config": {
                        "work_speed": "M",
                        "set_speed": "M1",
                    }
                }
            }
        }
    },
    "DCP": {}
}

CONFIG_KEY_PORT_MAP = {
    "a1": "01", "a2": "02", "b1": "03", "b2": "04", "c1": "05", "c2": "06", "d1": "07", "d2": "08",
    "1": "01", "2": "02", "3": "03", "4": "04", "5": "05", "6": "06", "7": "07", "8": "08", "9": "09",
    "10": "10", "11": "11", "12": "12", "13": "13", "14": "14", "15": "15", "16": "16"
}

SERIES2MODEL = {
    2: "FMT",
    3: "D6000"
}


def beat_sync_fmt_device_info_all():
    LOG.info('Start sync all fmt device info.')

    # 获取所有fmt设备，series为2
    db_session = invent_db.get_session()
    device_info_list = db_session.query(OtnDeviceBasic).filter(OtnDeviceBasic.series == 2).all()
    for device_info in device_info_list:
        result, NMU = get_device_info_by_ip(device_info.ip, device_info.id, "FMT")
        fmtTempData = OtnTempData(id=device_info.id, ip=device_info.ip, nmu=json.dumps(NMU),
                                  data=json.dumps(result))
        db_session.merge(fmtTempData)

    LOG.info('End sync all fmt device info.')


def beat_sync_dcs_device_info_all():
    LOG.info('Start sync all D6000 device info.')

    # 获取所有dcs设备，series为3
    db_session = invent_db.get_session()
    device_info_list = db_session.query(OtnDeviceBasic).filter(OtnDeviceBasic.series == 3).all()
    for device_info in device_info_list:
        result, NMU = get_device_info_by_ip(device_info.ip, device_info.id, "D6000")
        dcsTempData = OtnTempData(id=device_info.id, ip=device_info.ip, nmu=json.dumps(NMU),
                                  data=json.dumps(result))
        db_session.merge(dcsTempData)

    LOG.info('End sync all D6000 device info.')


def beat_sync_otn_device_info_single(id, ip):
    result, otnDeviceBasic = get_device_info(id, ip)
    otnData = result[0]
    NMU = result[1]
    if otnData is None:
        return None
    db_session = invent_db.get_session()
    with db_session.begin():
        otnTempData = OtnTempData(id=otnDeviceBasic.id, ip=otnDeviceBasic.ip, nmu=json.dumps(NMU),
                                  data=json.dumps(otnData))
        db_session.merge(otnTempData)

    return get_device_detail(otnDeviceBasic.id, SERIES2MODEL[otnDeviceBasic.series])


def subscribe_trap_message(ip, device_type="FMT"):
    # 从全局配置中获取控制器IP，然后下发设备，设置trap接收地址
    controllerIp = cfg.CONF.global_ip
    data, errorCode, errorMsg = modify_config(ip, "0", "trap_address", controllerIp, device_type)
    LOG.info(
        f"deviceIp:{ip} , controllerIp:{controllerIp} , result:{data} , errorCode:{errorCode}, errorMsg:{errorMsg}.")


def get_device_detail(deviceId, device_type="FMT"):
    # 从otn_temp_data、fmt_device_cards表中取出设备所有数据
    db_session = invent_db.get_session()
    otnTempData = db_session.query(OtnTempData).filter(OtnTempData.id == deviceId).first()
    if device_type == "FMT":
        cardsData = db_session.query(FmtDeviceCards).filter(FmtDeviceCards.device_id == deviceId).all()
    elif device_type == "D6000":
        cardsData = db_session.query(DcsDeviceCards).filter(DcsDeviceCards.device_id == deviceId).all()

    otnTempDataResult = otnTempData.data if otnTempData else None

    if otnTempDataResult is None or len(otnTempDataResult) == 0 or otnTempDataResult == '""':
        if device_type == "D6000":
            basic_d6000_data = {
                "boardInfos": [],
                "Slot number": 4,
                "SN": "",
                "Model": "D6000",
                "Production date": "",
                "Software version": "",
                "Hardware version": "",
                "IP": ""
            }
            # 创建PSU板卡
            return clone_dcs_psu_objects(basic_d6000_data)
        return ""

    resultData = json.loads(otnTempDataResult)

    if resultData.get("boardInfos") is None:
        if device_type == "D6000":
            resultData["boardInfos"] = []
        else:
            return resultData

    if resultData.get("boardInfos") is not None:
        boardInfos = resultData["boardInfos"]
        for item in cardsData:
            slotIndex = item.slot_index
            card = get_card_by_slot_index(boardInfos, slotIndex)
            if card is None:
                LOG.info(f"Slot index:{slotIndex} have no card.")
                continue
            card["cardId"] = item.card_id
            card["Board model"] = item.model
            card["Serial number"] = item.serial_number
            card["Production date"] = item.production_date
            card["Hardware version"] = item.hardware_version
            card["Software version"] = item.software_version
            card["Module temperature"] = item.temperature
            card["ports_data"] = json.loads(item.ports_data)
            # 原始数据结构，删除不需要字段，新增需要的card的port内容
            if card.get("basicInfo") is not None:
                del card["basicInfo"]
            if slotIndex == 0:
                card["description"] = otnTempData.description if otnTempData else ""
            # if card.get("businessInfo") is not None:
            #     del card["businessInfo"]

    # 对于D6000设备，确保PSU板卡始终存在
    if device_type == "D6000":
        existing_slots = [card.get("slotIndex") for card in resultData["boardInfos"]]
        has_psu = any(slot in [9, 10] for slot in existing_slots)

        if not has_psu:
            resultData = clone_dcs_psu_objects(resultData)

    return resultData


def get_card_by_slot_index(boardInfos, slotIndex):
    for item in boardInfos:
        if item["slotIndex"] == slotIndex:
            return item
    return None


def get_device_info(id, ip=None):
    db_session = invent_db.get_session()
    if ip is not None:
        LOG.info("device ip:" + ip)
        otnDeviceBasic = db_session.query(OtnDeviceBasic).filter(OtnDeviceBasic.ip == ip).first()
        if otnDeviceBasic is None:
            return [None, None], None
        return get_device_info_by_ip(ip, otnDeviceBasic.id, SERIES2MODEL[otnDeviceBasic.series]), otnDeviceBasic

    LOG.info("device id:" + id)
    otnDeviceBasic = db_session.query(OtnDeviceBasic).filter(OtnDeviceBasic.id == id).first()
    if otnDeviceBasic is None:
        return [None, None], None

    LOG.info(str(otnDeviceBasic))
    ip = otnDeviceBasic.ip
    return get_device_info_by_ip(ip, id, SERIES2MODEL[otnDeviceBasic.series]), otnDeviceBasic


def get_device_info_by_ip(ip, id, device_type="FMT"):
    # 获取锁并查询设备基本信息
    startTime = time.time()
    distributed_lock = DistributedLock(ip)
    if distributed_lock.acquire():
        socket_client = None
        try:
            LOG.info("get lock total time:" + str(time.time() - startTime))
            socket_client = SocketClient(ip, time_out=5, model=device_type)
            socket_client.create_socket_client()
            recData, NMU = get_all_board_info(socket_client)
            LOG.info("get data total time:" + str(time.time() - startTime))
            if recData["boardInfos"] != []:  # 确保 boardInfos 不为空
                update_reachable_status(ip, 1)
            else:
                update_reachable_status(ip, 0)
            if device_type == "FMT":
                update_fmt_device_info(id, recData)
            elif device_type == "D6000":
                update_dcs_device_info(id, recData)
            return recData, NMU
        except socket.timeout:
            LOG.error("socket timed out!")
            LOG.error(traceback.format_exc())
        except Exception as e:
            LOG.error(f"Exception:{e}")
            LOG.error(traceback.format_exc())
        finally:
            if socket_client is not None:
                socket_client.close_socket_client()
            distributed_lock.release()
            LOG.info("end work, release lock!")
    else:
        LOG.error("get lock fail!")
    update_reachable_status(ip, 0)
    if device_type == "FMT":
        update_fmt_device_info(id, None)
    elif device_type == "D6000":
        update_dcs_device_info(id, None)
    # 返回默认值或空数据
    return {"boardInfos": [], "Slot number": 0}, ""


def update_fmt_device_info(id, recData):
    db_session = invent_db.get_session()
    with db_session.begin():
        if recData is not None:
            # 更新fmt设备表、单板表
            fmtDeviceBasic = FmtDeviceBasic(device_id=id, serial_number=recData.get("SN"),
                                            slot_number=recData.get("Slot number"),
                                            mask=recData.get("Mask"),
                                            gateway=recData.get("Gateway"),
                                            mac=recData.get("Mac"),
                                            key_lock_status=recData.get("Key"),
                                            bzc_status=recData.get("BZC"),
                                            bzs_status=recData.get("BZS"),
                                            fnc_status=recData.get("FNC"),
                                            fns_status=recData.get("FNS"),
                                            pwr_status=recData.get("PWR"),
                                            production_date=recData.get("Production date"),
                                            hardware_version=recData.get("Hardware version"),
                                            software_version=recData.get("Software version"),
                                            firmware_version=recData.get("Hardware version"),
                                            temperature="")
            fmtDeviceCardList = []
            lengthCards = len(recData.get("boardInfos"))
            if lengthCards > 0:
                # 取历史同步数据，获得各槽位上单板数据
                fmtCardsList = db_session.query(FmtDeviceCards).filter(FmtDeviceCards.device_id == id).all()
                for index in range(lengthCards):
                    cardInfo = recData.get("boardInfos")[index]
                    cardDetail = cardInfo.get("basicInfo")
                    boardType = cardInfo.get("boardType")
                    temperature = cardDetail.get("Temperature")
                    if temperature is None:
                        temperature = ""
                    if boardType == "DCM" or boardType == "TDCM":
                        if temperature:
                            temperature = f"{float(temperature.lstrip('0')) / 10:.1f}"
                        else:
                            temperature = "0.00"
                    portsData = json.dumps(build_fmt_ports_data(boardType, fmtCardsList, recData, index))
                    slotIndex = cardInfo.get("slotIndex")
                    fmtDeviceCard = FmtDeviceCards(card_id=f"{id}_{slotIndex}", device_id=id,
                                                   slot_index=slotIndex,
                                                   type=boardType, model=cardDetail.get("Board model"),
                                                   serial_number=cardDetail.get("Serial number"),
                                                   production_date=cardDetail.get("Production date"),
                                                   hardware_version=cardDetail.get("Hardware version"),
                                                   software_version=cardDetail.get("Software version"),
                                                   firmware_version=cardDetail.get("Hardware version"),
                                                   temperature=temperature,
                                                   ports_data=portsData)
                    fmtDeviceCardList.append(fmtDeviceCard)

            # 根据id清空fmt设备表数据，会级联删除单板表中设备对应数据
            db_session.query(FmtDeviceBasic).filter(FmtDeviceBasic.device_id == id).delete()
            db_session.add(fmtDeviceBasic)
            if len(fmtDeviceCardList) > 0:
                for item in fmtDeviceCardList:
                    db_session.add(item)


def clone_dcs_psu_objects(data):
    board_infos = data['boardInfos']
    nmu_object = next((x for x in board_infos if x['slotIndex'] == 16), None)

    def safe_get(obj, path, default=None):
        try:
            keys = path.split('.')
            result = obj
            for key in keys:
                result = result[key]
            return result if result is not None else default
        except (KeyError, TypeError):
            return default

    if nmu_object:
        basic_info = {
            'Board model': 'PSU',
            'Serial number': safe_get(nmu_object, 'basicInfo.Serial number', ''),
            'Production date': safe_get(nmu_object, 'basicInfo.Production date', ''),
            'Hardware version': safe_get(nmu_object, 'basicInfo.Hardware version', ''),
            'Software version': safe_get(nmu_object, 'basicInfo.Software version', ''),
            'Temperature': safe_get(nmu_object, 'basicInfo.Temperature', '')
        }
    else:
        basic_info = {
            'Board model': 'PSU',
            'Serial number': '',
            'Production date': '',
            'Hardware version': '',
            'Software version': '',
            'Temperature': ''
        }

    psu1 = {
        'boardType': 'PSU',
        'analysisMode': 1,
        'basicInfo': basic_info.copy(),
        'businessInfo': {
            'query': {
                'power_state': safe_get(nmu_object, 'businessInfo.query.power_state1', '0') if nmu_object else '0',
                'supply_mode': safe_get(nmu_object, 'businessInfo.query.supply_mode1', '') if nmu_object else '',
                'max_power': safe_get(nmu_object, 'businessInfo.query.max_power1', '') if nmu_object else '',
                'input_current': safe_get(nmu_object, 'businessInfo.query.input_current1', '') if nmu_object else '',
                'output_current': safe_get(nmu_object, 'businessInfo.query.output_current1', '') if nmu_object else '',
                'input_voltage': safe_get(nmu_object, 'businessInfo.query.input_voltage1', '') if nmu_object else '',
                'output_voltage': safe_get(nmu_object, 'businessInfo.query.output_voltage1', '') if nmu_object else '',
                'power_temperature': safe_get(nmu_object, 'businessInfo.query.power_temperature1',
                                              '') if nmu_object else '',
                'fan_state': safe_get(nmu_object, 'businessInfo.query.fan_state1', '') if nmu_object else ''
            },
            'config': {
                'power_switch': safe_get(nmu_object, 'businessInfo.config.power_switch1', '0') if nmu_object else '0',
                'fan_switch': safe_get(nmu_object, 'businessInfo.config.fan_switch', '') if nmu_object else '',
                'fan_speed': safe_get(nmu_object, 'businessInfo.config.fan_speed', '') if nmu_object else ''
            }
        },
        'slotIndex': 9
    }

    psu2 = {
        'boardType': 'PSU',
        'analysisMode': 1,
        'basicInfo': basic_info.copy(),
        'businessInfo': {
            'query': {
                'power_state': safe_get(nmu_object, 'businessInfo.query.power_state2', '0') if nmu_object else '0',
                'supply_mode': safe_get(nmu_object, 'businessInfo.query.supply_mode2', '') if nmu_object else '',
                'max_power': safe_get(nmu_object, 'businessInfo.query.max_power2', '') if nmu_object else '',
                'input_current': safe_get(nmu_object, 'businessInfo.query.input_current2', '') if nmu_object else '',
                'output_current': safe_get(nmu_object, 'businessInfo.query.output_current2', '') if nmu_object else '',
                'input_voltage': safe_get(nmu_object, 'businessInfo.query.input_voltage2', '') if nmu_object else '',
                'output_voltage': safe_get(nmu_object, 'businessInfo.query.output_voltage2', '') if nmu_object else '',
                'power_temperature': safe_get(nmu_object, 'businessInfo.query.power_temperature2',
                                              '') if nmu_object else '',
                'fan_state': safe_get(nmu_object, 'businessInfo.query.fan_state2', '') if nmu_object else ''
            },
            'config': {
                'power_switch': safe_get(nmu_object, 'businessInfo.config.power_switch2', '0') if nmu_object else '0',
                'fan_switch': safe_get(nmu_object, 'businessInfo.config.fan_switch', '') if nmu_object else '',
                'fan_speed': safe_get(nmu_object, 'businessInfo.config.fan_speed', '') if nmu_object else ''
            }
        },
        'slotIndex': 10
    }

    board_infos.append(psu1)
    board_infos.append(psu2)

    return data


def update_dcs_device_info(id, recData):
    clone_dcs_psu_objects(recData)
    db_session = invent_db.get_session()
    with db_session.begin():
        if recData is not None:
            dcsDeviceBasic = DcsDeviceBasic(device_id=id, serial_number=recData.get("SN"),
                                            slot_number=recData.get("Slot number"),
                                            mask=recData.get("Mask"),
                                            gateway=recData.get("Gateway"),
                                            mac=recData.get("Mac"),
                                            key_lock_status=recData.get("Key"),
                                            bzc_status=recData.get("BZC"),
                                            bzs_status=recData.get("BZS"),
                                            fnc_status=recData.get("FNC"),
                                            fns_status=recData.get("FNS"),
                                            pwr_status=recData.get("PWR"),
                                            production_date=recData.get("Production date"),
                                            hardware_version=recData.get("Hardware version"),
                                            software_version=recData.get("Software version"),
                                            firmware_version=recData.get("Hardware version"),
                                            temperature="")
            dcsDeviceCardList = []
            lengthCards = len(recData.get("boardInfos"))
            if lengthCards > 0:
                # 取历史同步数据，获得各槽位上单板数据
                dcsCardsList = db_session.query(DcsDeviceCards).filter(DcsDeviceCards.device_id == id).all()
                for index in range(lengthCards):
                    cardInfo = recData.get("boardInfos")[index]
                    cardDetail = cardInfo.get("basicInfo")
                    boardType = cardInfo.get("boardType")
                    temperature = cardDetail.get("Temperature")
                    portsData = json.dumps(build_dcs_ports_data(boardType, dcsCardsList, recData, index))
                    slotIndex = cardInfo.get("slotIndex")
                    dcsDeviceCard = DcsDeviceCards(card_id=f"{id}_{slotIndex}", device_id=id,
                                                   slot_index=slotIndex,
                                                   type=boardType, model=cardDetail.get("Board model"),
                                                   serial_number=cardDetail.get("Serial number"),
                                                   production_date=cardDetail.get("Production date"),
                                                   hardware_version=cardDetail.get("Hardware version"),
                                                   software_version=cardDetail.get("Software version"),
                                                   firmware_version=cardDetail.get("Hardware version"),
                                                   temperature=temperature,
                                                   ports_data=portsData)
                    dcsDeviceCardList.append(dcsDeviceCard)

            # 根据id清空dcs设备表数据，会级联删除单板表中设备对应数据
            db_session.query(DcsDeviceBasic).filter(DcsDeviceBasic.device_id == id).delete()
            db_session.add(dcsDeviceBasic)
            if len(dcsDeviceCardList) > 0:
                for item in dcsDeviceCardList:
                    db_session.add(item)


def build_fmt_ports_data(boardType, fmtCardsList, recData, index):
    portsData = dict()
    cardInfo = recData.get("boardInfos")[index]
    slot_index = cardInfo.get("slotIndex")
    businessInfo = cardInfo.get("businessInfo")
    oldPortsData = get_old_card_ports_data(fmtCardsList, slot_index)

    queryData = None
    configData = None
    if businessInfo.get("query") is not None:
        queryData = businessInfo.get("query")
    if businessInfo.get("config") is not None:
        configData = businessInfo["config"]

    # 不同单板端口数据类型不一致
    if boardType == "NMU":
        slot_number = recData.get("Slot number")
        for item in range(slot_number):
            indexNo = str(item + 1)
            portsData[indexNo] = {"slot_no": indexNo, "equipment_mismatch": "", "card_name": "",
                                  "empty": "TRUE", "Slot Note": handle_note(indexNo, "Slot Note", oldPortsData)}

        # 赋值-当前单板类型
        lengthCards = len(recData.get("boardInfos"))
        if lengthCards > 0:
            for index in range(lengthCards):
                cardInfo = recData.get("boardInfos")[index]
                slotIndex = cardInfo.get("slotIndex")
                if slotIndex == 0:
                    continue
                boardType = cardInfo.get("boardType")
                portsData[str(slotIndex)]["card_name"] = boardType
                if boardType:
                    portsData[str(slotIndex)]["empty"] = "FALSE"

        # 赋值-历史单板类型
        for item in fmtCardsList:
            slotIndex = item.slot_index
            if slotIndex == 0:
                continue
            if portsData[str(slotIndex)]["empty"] == "FALSE":
                portsData[str(slotIndex)]["equipment_mismatch"] = "FALSE"

    elif boardType == "EDFA":
        stateData = queryData["State"]
        portsData["1"] = {"No": "1", "Name": "PORT-1-PIN", "EDFA Gain Value": stateData["gain"],
                          "Expected EDFA Gain": configData["Gain"]["gain_adjustment"],
                          "VOA Attenuation Value": "",
                          "VOA Attenuation Expected": "",
                          "Gain Slope": "",
                          "Input Optical Power": stateData["input_power"],
                          "Output Optical Power": "",
                          "Input Warning Threshold": stateData["input_warning_threshold"],
                          "Output Warning Threshold": "",
                          "Port Note": handle_note("1", "Port Note", oldPortsData)}

        portsData["2"] = {"No": "2", "Name": "PORT-2-POUT", "EDFA Gain Value": stateData["gain"],
                          "Expected EDFA Gain": configData["Gain"]["gain_adjustment"],
                          "VOA Attenuation Value": "",
                          "VOA Attenuation Expected": "",
                          "Gain Slope": "",
                          "Input Optical Power": "",
                          "Output Optical Power": stateData["output_power"],
                          "Input Warning Threshold": "",
                          "Output Warning Threshold": stateData["output_warning_threshold"],
                          "Port Note": handle_note("2", "Port Note", oldPortsData)}
    elif boardType == "DEDFA":
        stateData = queryData["State"]
        portsData["1"] = {"No": "1", "Name": "PORT-1-PIN1",
                          "Input Optical Power": stateData["input_power1"],
                          "Output Optical Power": "",
                          "Port Note": handle_note("1", "Port Note", oldPortsData)}
        portsData["2"] = {"No": "2", "Name": "PORT-2-POUT1",
                          "Input Optical Power": "",
                          "Output Optical Power": stateData["output_power1"],
                          "Port Note": handle_note("2", "Port Note", oldPortsData)}
        portsData["3"] = {"No": "3", "Name": "PORT-3-PIN2",
                          "Input Optical Power": stateData["input_power2"],
                          "Output Optical Power": "",
                          "Port Note": handle_note("3", "Port Note", oldPortsData)}
        portsData["4"] = {"No": "4", "Name": "PORT-4-POUT2",
                          "Input Optical Power": "",
                          "Output Optical Power": stateData["output_power2"],
                          "Port Note": handle_note("4", "Port Note", oldPortsData)}
    elif boardType == "OLP":
        workStatus = configData["Working Parameter"]["work_route"]
        if workStatus == "1":
            workStatus = "PRIMARY"
        else:
            workStatus = "SECONDARY"
        powerData = queryData["Power"]
        thresholdData = configData["Alarm Limit"]
        portsData["1"] = {"No": "1", "Name": "PORT-1-APSP-IN", "Work Status": workStatus,
                          "Optical Power": powerData["r1"], "Optical Power Threshold": thresholdData["r1"],
                          "Protection Group": "APS"}
        portsData["2"] = {"No": "2", "Name": "PORT-2-APSS-IN", "Work Status": workStatus,
                          "Optical Power": powerData["r2"], "Optical Power Threshold": thresholdData["r2"],
                          "Protection Group": "APS"}
        portsData["3"] = {"No": "3", "Name": "PORT-3-APSC-OUT", "Work Status": workStatus,
                          "Optical Power": powerData["tx"], "Optical Power Threshold": thresholdData["tx"],
                          "Protection Group": "APS"}
    elif boardType == "OEO":
        portsName = ["A1", "A2", "B1", "B2", "C1", "C2", "D1", "D2"]
        for index in range(8):
            indexNo = str(index + 1)
            portsData[indexNo] = {"No": indexNo, "Name": portsName[index],
                                  "Module Wavelength": (get_list_value(queryData["wavelength"], index)
                                                        if get_list_value(queryData["wavelength"], index) and float(
                                      get_list_value(queryData["wavelength"], index)) > 0 else ""),
                                  "Input Optical Power": (get_list_value(queryData["input_power"], index)
                                                          if get_list_value(queryData["wavelength"], index) and float(
                                      get_list_value(queryData["wavelength"], index)) > 0 else ""),
                                  "Output Optical Power": (get_list_value(queryData["output_power"], index)
                                                           if get_list_value(queryData["wavelength"], index) and float(
                                      get_list_value(queryData["wavelength"], index)) > 0 else ""),
                                  "Input Alarm Threshold": (get_list_value(configData["input_alarm_threshold_"], index)
                                                            if get_list_value(queryData["wavelength"], index) and float(
                                      get_list_value(queryData["wavelength"], index)) > 0 else ""),
                                  "Transmission Distance": (get_list_value(queryData["transmission_distance"], index)
                                                            if get_list_value(queryData["wavelength"], index) and float(
                                      get_list_value(queryData["wavelength"], index)) > 0 else ""),
                                  "Work Mode": (get_list_value(configData["control_mode_"], index)
                                                if get_list_value(queryData["wavelength"], index) and float(
                                      get_list_value(queryData["wavelength"], index)) > 0 else ""),
                                  "Module Temperature": (get_list_value(queryData["module_temperature"], index)
                                                         if get_list_value(queryData["wavelength"], index) and float(
                                      get_list_value(queryData["wavelength"], index)) > 0 else ""),
                                  "Rate": (get_list_value(queryData["rate"], index)
                                           if get_list_value(queryData["wavelength"], index) and float(
                                      get_list_value(queryData["wavelength"], index)) > 0 else ""),
                                  # "Module State": "Present",
                                  "Service Notes": handle_note(indexNo, "Service Notes", oldPortsData)}
    elif boardType == "VOA":
        for index in range(4):
            indexNo = str(index + 1)
            portsData[indexNo] = {"No": indexNo, "Name": f"VOA{indexNo}",
                                  "Work Mode": configData[f"voa{indexNo}_work_mode"],
                                  "Actual Power": queryData[f"voa{indexNo}_power"], "Expected Power"
                                  : configData[f"voa{indexNo}_power_configuration"],
                                  "Actual Attenuation": queryData[f"voa{indexNo}_attenuation"],
                                  "Expected Attenuation": configData[f"voa{indexNo}_attenuation_configuration"],
                                  "Threshold": configData[f"voa{indexNo}_threshold"]}
    elif boardType == "OPD":
        for index in range(16):
            indexNo = str(index + 1)
            portsData[indexNo] = {"No": indexNo, "Name": f"OPD-PORT{indexNo}",
                                  "Power": get_list_value(queryData["power"], index),
                                  "Route Type": get_list_value(queryData["route_type"], index),
                                  "Wavelength": get_list_value(configData["wavelength_"], index),
                                  "Port Note": handle_note(indexNo, "Port Note", oldPortsData)}
    else:
        pass
    return portsData


def build_dcs_ports_data(boardType, dcsCardsList, recData, index):
    portsData = dict()
    cardInfo = recData.get("boardInfos")[index]
    slot_index = cardInfo.get("slotIndex")
    businessInfo = cardInfo.get("businessInfo")
    basicInfo = cardInfo.get("basicInfo")
    oldPortsData = get_old_card_ports_data(dcsCardsList, slot_index)

    queryData = None
    configData = None
    if businessInfo.get("query") is not None:
        queryData = businessInfo.get("query")
    if businessInfo.get("config") is not None:
        configData = businessInfo["config"]

    # 不同单板端口数据类型不一致
    if boardType == "NMU":
        slot_number = recData.get("Slot number")
        if slot_number is None:
            # 处理 slot_number 为 None 的情况，例如记录日志或设置默认值
            LOG.error("Slot number is None in recData")
            slot_number = 0  # 设置默认值
        for item in range(slot_number):
            indexNo = str(item + 1)
            portsData[indexNo] = {"slot_no": indexNo, "equipment_mismatch": "", "card_name": "",
                                  "empty": "TRUE", "slot_note": handle_note(indexNo, "slot_note", oldPortsData)}

        # 赋值-当前单板类型
        lengthCards = len(recData.get("boardInfos"))
        if lengthCards > 0:
            for index in range(lengthCards):
                cardInfo = recData.get("boardInfos")[index]
                slotIndex = cardInfo.get("slotIndex")
                if slotIndex == 0:
                    continue
                boardType = cardInfo.get("boardType")
                if str(slotIndex) not in portsData:
                    portsData[str(slotIndex)] = {}
                portsData[str(slotIndex)]["card_name"] = boardType
                if boardType:
                    portsData[str(slotIndex)]["empty"] = "FALSE"

        # 赋值-历史单板类型
        for item in dcsCardsList:
            slotIndex = item.slot_index
            if slotIndex == 0:
                continue
            if portsData[str(slotIndex)]["empty"] == "FALSE":
                portsData[str(slotIndex)]["equipment_mismatch"] = "FALSE"

    elif boardType == "4ME4C":
        portsName = ["L1", "C1", "C2", "C3", "C4"]
        for index, port in enumerate(portsName):
            indexNo = str(index + 1)
            portsData[indexNo] = {"no": indexNo, "name": portsName[index],
                                  "module_wavelength": get_list_value(configData["wavelength_Cvalue_"], index),
                                  "wavelength": get_list_value(configData["wavelength_Lvalue_"], index),
                                  "input_optical_power": get_list_value(queryData["rx_power"], index),
                                  "output_optical_power": get_list_value(configData["tx_power_value_"], index),
                                  "target_output_power": get_list_value(configData["tx_power_"], index),
                                  "input_alarm_threshold": get_list_value(configData["overlow_input_threshold_"],
                                                                          index),
                                  "transmission_distance": get_list_value(queryData["transmission_distance"], index),
                                  "module_state": get_list_value(queryData["existence"], index),
                                  "work_mode": get_list_value(queryData["work_mode_"], index),
                                  "module_type": get_list_value(queryData["module_type"], index),
                                  "temperature": get_list_value(queryData["temperature"], index),
                                  "business_mode": get_list_value(configData["business_mode_"], index),
                                  "modulation": get_list_value(configData["modulation_"], index),
                                  "laser_switch": get_list_value(configData["tx_switch_"], index),
                                  "fec": get_list_value(configData["fec_switch_"], index),
                                  "pre_fec": get_list_value(queryData["pre_fec"], index),
                                  "post_fec": get_list_value(queryData["post_fec"], index),
                                  "als": get_list_value(configData["als_"], index),
                                  "pn" : get_list_value(queryData["pn"], index),
                                  "sn" : get_list_value(queryData["sn"], index),
                                  "overlow_input_threshold": get_list_value(
                                      configData["overlow_input_threshold_value_"],
                                      index),
                                  "overhigh_input_threshold": get_list_value(
                                      configData["overhigh_input_threshold_value_"],
                                      index),
                                  "overlow_output_threshold": get_list_value(
                                      configData["overlow_output_threshold_value_"],
                                      index),
                                  "overhigh_output_threshold": get_list_value(
                                      configData["overhigh_output_threshold_value_"],
                                      index),
                                  "serial_no": basicInfo.get("Serial number", ""),
                                  "board_model": basicInfo.get("Board model", ""),
                                  "port_note": handle_note(indexNo, "port_note", oldPortsData)}
    elif boardType == "4T4E4C":
        portsName = ["L1", "L2", "L3", "L4", "C1", "C2", "C3", "C4"]
        for index, port in enumerate(portsName):
            indexNo = str(index + 1)
            portsData[indexNo] = {"no": indexNo, "name": portsName[index],
                                  "module_wavelength": get_list_value(configData["wavelength_Cvalue_"], index),
                                  "wavelength": get_list_value(configData["wavelength_Lvalue_"], index),
                                  "input_optical_power": get_list_value(queryData["rx_power"], index),
                                  "output_optical_power": get_list_value(configData["tx_power_value_"], index),
                                  "target_output_power": get_list_value(configData["tx_power_"], index),
                                  "input_alarm_threshold": get_list_value(configData["overlow_input_threshold_"],
                                                                          index),
                                  "transmission_distance": get_list_value(queryData["transmission_distance"], index),
                                  "module_state": get_list_value(queryData["existence"], index),
                                  "work_mode": get_list_value(queryData["work_mode_"], index),
                                  "module_type": get_list_value(queryData["module_type"], index),
                                  "temperature": get_list_value(queryData["temperature"], index),
                                  "business_mode": get_list_value(configData["business_mode_"], index),
                                  "modulation": get_list_value(configData["modulation_"], index),
                                  "laser_switch": get_list_value(configData["tx_switch_"], index),
                                  "fec": get_list_value(configData["fec_switch_"], index),
                                  "pre_fec": get_list_value(queryData["pre_fec"], index),
                                  "post_fec": get_list_value(queryData["post_fec"], index),
                                   "pn" : get_list_value(queryData["pn"], index),
                                  "sn" : get_list_value(queryData["sn"], index),
                                  "als": get_list_value(configData["als_"], index),
                                  "overlow_input_threshold": get_list_value(
                                      configData["overlow_input_threshold_value_"],
                                      index),
                                  "overhigh_input_threshold": get_list_value(
                                      configData["overhigh_input_threshold_value_"],
                                      index),
                                  "overlow_output_threshold": get_list_value(
                                      configData["overlow_output_threshold_value_"],
                                      index),
                                  "overhigh_output_threshold": get_list_value(
                                      configData["overhigh_output_threshold_value_"],
                                      index),
                                  "serial_no": basicInfo.get("Serial number", ""),
                                  "board_model": basicInfo.get("Board model", ""),
                                  "port_note": handle_note(indexNo, "port_note", oldPortsData)}
    else:
        pass
    return portsData


def get_list_value(data_list, index):
    if data_list is not None and len(data_list) > index:
        return data_list[index]
    return ""


def get_old_card_ports_data(deviceCardList, slotIndex):
    for item in deviceCardList:
        if item.slot_index == slotIndex:
            return json.loads(item.ports_data)
    return dict()


def handle_note(outKey, innerKey, oldNote):
    paramTemp = oldNote.get(outKey)
    if paramTemp is not None:
        return paramTemp.get(innerKey)


def set_note(outKey, innerKey, newNote, jsonData):
    _data = json.loads(jsonData)
    if _data is not None:
        _data[outKey][innerKey] = newNote
        return json.dumps(_data)
    else:
        return jsonData

def update_otntempdata(db_session, device_id, port, note):
    try:
        otntempdata = db_session.query(OtnTempData).filter(OtnTempData.id == device_id).first()
        if not otntempdata:
            return None
        data_json = json.loads(otntempdata.data)
        local_info = data_json.get("local_info", {})
        port_key = f"Port{port}"
        port_desc_key = f"PortDesc{port}"
        if port_key in local_info and port_desc_key in local_info[port_key]:
            local_info[port_key][port_desc_key] = "interface " + note
        else:
            print(f"Warning: Port {port_key} or PortDesc {port_desc_key} not found in local_info.")
            return None
        updated_data = json.dumps(data_json)
        db_session.query(OtnTempData).filter(OtnTempData.id == device_id).update({
            OtnTempData.data: updated_data
        })
        return local_info
    except Exception as e:
        print(f"Error updating otntempdata: {e}")
        raise e

def update_reachable_status(ip, status):
    db_session = invent_db.get_session()

    db_session.query(OtnDeviceBasic).filter(OtnDeviceBasic.ip == ip).update(
        {OtnDeviceBasic.reachable_status: status})


def normalize_number(input, digit=2):
    if input in ["0", "1", "2", "3"]:
        return input

    if isinstance(input, str):
        if len(input) > 10:
            return input

        cleaned_str = input.replace('.', '', 1).replace('-', '', 1).replace('+', '')
        if cleaned_str.isdigit():
            try:
                return format(float(input), f'.{digit}f')
            except ValueError:
                return ''
    elif isinstance(input, (int, float)):
        return format(input, f'.{digit}f')

    return input


# 查询数据指令
def get_all_board_info(socket_client):
    socket_client.result = {"boardInfos": []}
    strSend = f"<C00_[PSWD_{socket_client.password}][CS_?][SN_?][DTP_?][MD_?][SV_?][HV_?][IP_?][MSK_?][GW_?]\
    [MAC_?][KEY_?][BZC_?][BZS_?][FNC_?][FNS_?][PWR_?][SNMPMIP_?][showlocal_?]>"
    str_back = socket_client.send_command_by_long_connection(strSend)

    if len(str_back) > 0:
        index = 0
        fromIndex = 0
        deviceBasic = {}
        local_info = {}
        while index < len(str_back):
            if str_back[index] == '[':
                fromIndex = index
            if str_back[index] == ']':
                toIndex = index
                strData = str_back[fromIndex + 1: toIndex]
                update_device_info(socket_client, strData, deviceBasic)
            index += 1

        # 处理showlocal字段和Port相关字段填充到local_info
        import re
        if 'showlocal' in deviceBasic:
            showlocal_value = deviceBasic['showlocal']
            parts = showlocal_value.split('_', 1)
            if len(parts) == 2:
                port_part, status = parts
                port_number = re.search(r'\d+$', port_part)
                if port_number:
                    port_key = f'Port{port_number.group()}'
                    local_info.setdefault(port_key, {})[status] = ""

        for key, value in deviceBasic.items():
            if key.startswith('Port'):
                if len(value) == 1 and value in ['Y', 'N']:
                    port_number = re.search(r'\d+$', key)
                    if port_number:
                        port_key = f'Port{port_number.group()}'
                        local_info.setdefault(port_key, {})[value] = ""
                else:
                    port_number = re.search(r'\d+$', key)
                    if port_number:
                        port_key = f'Port{port_number.group()}'
                        local_info.setdefault(port_key, {})[key] = value

        # 确保Port1到Port6都存在
        for port_num in range(1, 7):
            port_key = f'Port{port_num}'
            if port_key not in local_info:
                local_info[port_key] = {'N': ''}

        result = socket_client.result
        for key, value in FMT_CONFIG_MODEL.get(socket_client.model).get("nmuMapping").items():
            result[key] = deviceBasic.get(value)

        # 填充0号槽位NMU单板信息
        update_nmu(socket_client, deviceBasic)
        socket_client.NMU["0"] = "0"

        socket_client.result["local_info"] = local_info

    return socket_client.result, socket_client.NMU

# 指令解析
def update_device_info(socket_client, str_order, device_basic):
    OrderArray = str_order.split('_')
    orderKey = OrderArray[0]
    orderValue = OrderArray[1]
    if orderKey == 'CS':
        # 设备槽位号总数特殊处理
        socket_client.result["Slot number"] = int(orderValue)
        HasInfo = ""
        if len(OrderArray) > 2:
            HasInfo = OrderArray[2]
        index = 3
        while index < len(OrderArray) and index - 3 < len(HasInfo):
            if HasInfo[index - 3] == '1':
                strTemp = OrderArray[index]
                if strTemp != '0000':
                    update_board(socket_client, index - 2, strTemp)
            index += 1
    # 设备基本信息解析
    else:
        device_basic[orderKey] = orderValue


def update_nmu(socket_client, device_basic):
    board_config_temp = copy.deepcopy(FMT_CONFIG_MODEL.get(socket_client.model).get("boardMapping").get("0"))
    if board_config_temp is None:
        LOG.error("Error, nmu have no config!")
        return
    boardType = board_config_temp.get("boardType")
    analysisMode = board_config_temp.get("analysisMode")
    scan_board_config(board_config_temp, device_basic, analysisMode)
    board_config_temp["slotIndex"] = 0
    board_config_temp["boardType"] = boardType
    result = socket_client.result.get("boardInfos")
    result.append(board_config_temp)


def update_board(socket_client, slot_index, board_type):
    socket_client.NMU[str(slot_index)] = board_type
    board_config_temp = copy.deepcopy(FMT_CONFIG_MODEL.get(socket_client.model).get("boardMapping").get(board_type))
    if board_config_temp is None:
        LOG.error("Error, board type:{} have no config!".format(board_type))
        return
    boardType = board_config_temp.get("boardType")
    strSend = f"<C{str(slot_index).zfill(2)}_[PSWD_{socket_client.password}][STA_?][B_?]>"
    str_back = socket_client.send_command_by_long_connection(strSend)
    if len(str_back) > 0 and str_back != f"<C{str(slot_index).zfill(2)}_[PSWD_ERROR]>":
        result = socket_client.result.get("boardInfos")
        analysisMode = board_config_temp.get("analysisMode")
        if analysisMode == 1:
            dictionary = initial_analysis_ext(str_back)
        elif analysisMode == 2:
            dictionary = initial_analysis_special(str_back)
        else:
            dictionary = {}

        scan_board_config(board_config_temp, dictionary, analysisMode)

        board_config_temp["slotIndex"] = slot_index
        board_config_temp["boardType"] = boardType
        result.append(board_config_temp)


def scan_board_config(dict_data, mapping_data, analysisMode):
    for key, value in dict_data.items():
        if isinstance(value, str):
            if "#" not in value:
                result = mapping_data.get(value)
                if result is not None:
                    if analysisMode == 0:
                        dict_data[key] = normalize_number(result)
                    elif analysisMode == 1:
                        dict_data[key] = normalize_number(result[0])
                    elif analysisMode == 2:
                        dict_data[key] = normalize_number(result)
                    else:
                        pass
                else:
                    dict_data[key] = ""
            else:
                dict_array = value.split("#")
                result = mapping_data.get(dict_array[0])
                if result is not None:
                    dict_data[key] = normalize_number(result[int(dict_array[1])])
                else:
                    dict_data[key] = ""
        elif isinstance(value, list):
            # 包含@，则需要按槽位号从1开始递增拼接key来获取值
            if "@" in value[0]:
                index = 1
                getResult = []
                if analysisMode == 2:
                    # 目前单板最多16个端口
                    while index < 17:
                        getKey = value[0].replace("@", str(index).zfill(2))
                        getValue = mapping_data.get(getKey)
                        if getValue is None:
                            break
                        else:
                            getResult.append(getValue)
                        index += 1
                elif analysisMode == 1:
                    # 目前单板最多16个端口
                    while index < 17:
                        getKey = value[0].replace("@", str(index))
                        getValue = mapping_data.get(getKey)
                        if getValue is None:
                            break
                        if len(getValue) == 1:
                            getResult.append(getValue[0])
                        else:
                            getResult.append(getValue)
                        index += 1
                dict_data[key] = getResult
            else:
                pass
        elif isinstance(value, dict):
            scan_board_config(value, mapping_data, analysisMode)
        else:
            pass


# def get_config(ip, slotIndex):
#     recData = {}
#     allBoardInfo = beat_sync_otn_device_info_single(None, ip)
#     if allBoardInfo == "" or allBoardInfo.get("boardInfos") is None:
#         return recData, 1, "Failed to synchronize device data!"
#     card = get_card_by_slot_index(allBoardInfo.get("boardInfos"), slotIndex)
#     if card is None:
#         LOG.error(f"Slot index:{slotIndex} have no card.")
#         return recData, 1, f"Slot index:{slotIndex} have no card!"
#     config = card["businessInfo"].get("config")
#     if config is None:
#         LOG.error(f"Slot index:{slotIndex} have no config data.")
#         return recData, 1, f"Slot index:{slotIndex} have no config data!"
#
#     get_config_data(config, recData)
#
#     return {"configData": recData, "boardInfo": {"type": card["boardType"], "model": card["Board model"]}}, 0, "success"


def get_config(ip, slotIndex):
    recData = {}
    board_info = get_single_board_info(ip, slotIndex)
    if board_info is None:
        return recData, 1, f"Failed to get data for slot index: {slotIndex}!"
    card = board_info
    if card is None:
        LOG.error(f"Slot index:{slotIndex} have no card.")
        return recData, 1, f"Slot index:{slotIndex} have no card!"
    config = card["businessInfo"].get("config")
    if config is None:
        LOG.error(f"Slot index:{slotIndex} have no config data.")
        return recData, 1, f"Slot index:{slotIndex} have no config data!"
    LOG.info("get lock total time:" + str(recData))
    get_config_data(config, recData)
    board_type = card.get("boardType")
    board_model = card.get("basicInfo", {}).get("Board model")
    return {"configData": recData, "boardInfo": {"type": board_type, "model": board_model}}, 0, "success"


def get_single_board_info(ip, slotIndex):
    startTime = time.time()
    distributed_lock = DistributedLock(ip)
    if distributed_lock.acquire():
        socket_client = None
        db_session = invent_db.get_session()
        try:
            LOG.info("get lock total time:" + str(time.time() - startTime))
            otnDeviceBasic = db_session.query(OtnDeviceBasic).filter(OtnDeviceBasic.ip == ip).first()
            if otnDeviceBasic is None:
                return None
            device_type = SERIES2MODEL[otnDeviceBasic.series]
            socket_client = SocketClient(ip, time_out=5, model=device_type)
            socket_client.create_socket_client()
            nmu = get_nmu(ip)
            boardIndex = nmu.get(str(slotIndex))
            if boardIndex is None:
                return None
            boardConfig = FMT_CONFIG_MODEL.get(device_type).get("boardMapping").get(boardIndex)
            board_config_temp = copy.deepcopy(boardConfig)
            if board_config_temp is None:
                LOG.error("Error, board type:{} have no config!".format(boardIndex))
                return None
            boardType = board_config_temp.get("boardType")
            strSend = f"<C{str(slotIndex).zfill(2)}_[PSWD_{socket_client.password}][STA_?][B_?]>"
            str_back = socket_client.send_command_by_long_connection(strSend)
            if len(str_back) > 0 and str_back != f"<C{str(slotIndex).zfill(2)}_[PSWD_ERROR]>":
                analysisMode = board_config_temp.get("analysisMode")
                if analysisMode == 1:
                    dictionary = initial_analysis_ext(str_back)
                elif analysisMode == 2:
                    dictionary = initial_analysis_special(str_back)
                else:
                    dictionary = {}

                scan_board_config(board_config_temp, dictionary, analysisMode)

                board_config_temp["slotIndex"] = slotIndex
                board_config_temp["boardType"] = boardType
                build_port_func = lambda: None

                with db_session.begin():
                    card_id = f"{otnDeviceBasic.id}_{slotIndex}"
                    if device_type == "FMT":
                        otn_device_card = db_session.query(FmtDeviceCards).filter(
                            FmtDeviceCards.card_id == card_id).first()
                        build_port_func = build_fmt_ports_data
                    elif device_type == "D6000":
                        otn_device_card = db_session.query(DcsDeviceCards).filter(
                            DcsDeviceCards.card_id == card_id).first()
                        build_port_func = build_dcs_ports_data
                    if not otn_device_card:
                        raise ValueError("can not find device card")

                    card_detail = board_config_temp.get("basicInfo")
                    board_type = board_config_temp.get("boardType")
                    temperature = card_detail.get("Temperature")
                    ports_data = json.dumps(build_port_func(board_type, [], {"boardInfos": [board_config_temp]}, 0))

                    otn_device_card.type = board_type
                    otn_device_card.temperature = temperature
                    otn_device_card.ports_data = ports_data

                    db_session.merge(otn_device_card)

                return board_config_temp
        except socket.timeout:
            LOG.error("socket timed out!")
            LOG.error(traceback.format_exc())
        except Exception as e:
            LOG.error(f"Exception:{e}")
            LOG.error(traceback.format_exc())
        finally:
            if socket_client is not None:
                socket_client.close_socket_client()
            distributed_lock.release()
            LOG.info("end work, release lock!")
    else:
        LOG.error("get lock fail!")
    return None


def get_config_data(dictData, matchData):
    for key, value in dictData.items():
        if isinstance(value, dict):
            get_config_data(value, matchData)
        # 多端口的配置，需要从CONFIG_KEY_PORT_MAP拿到对应的端口index填充value中的@位置
        elif isinstance(value, list):
            # TODO 列表的特殊处理
            matchData[key] = value
        else:
            matchData[key] = value


def modify_config(ip, slotIndex, key, value, device_type):
    if device_type not in ["FMT", "D6000"]:
        raise ValueError(f"Invalid device_type: {device_type}, must be 'FMT' or 'D6000'")

    recData = {}
    nmu = get_nmu(ip)
    startTime = time.time()
    distributed_lock = DistributedLock(ip)
    if distributed_lock.acquire():
        socket_client = None
        try:
            LOG.info("get lock total time:" + str(time.time() - startTime))
            socket_client = SocketClient(ip, time_out=5, model=device_type)
            socket_client.create_socket_client()
            recData = handle_modify_config(socket_client, slotIndex, nmu, key, value)
            LOG.info("set config total time:" + str(time.time() - startTime))
            if recData == "":
                return recData, 1, "Failed, configuration result not obtained!"
            db_session = invent_db.get_session()
            otnDeviceBasic = db_session.query(OtnDeviceBasic).filter(OtnDeviceBasic.ip == ip).first()
            if not otnDeviceBasic:
                return recData, 1, "Device not found in database!"
            device_type = SERIES2MODEL[otnDeviceBasic.series]

            nmu = get_nmu(ip)
            if not nmu:
                return recData, 1, "NMU info not found!"
            boardIndex = nmu.get(slotIndex)
            if not boardIndex:
                return recData, 1, "Slot is empty!"

            boardConfig = FMT_CONFIG_MODEL.get(device_type).get("boardMapping").get(boardIndex)
            if not boardConfig:
                return recData, 1, "Board config not found!"

            strSend = f"<C{str(slotIndex).zfill(2)}_[PSWD_{socket_client.password}][STA_?][B_?]>"
            str_back = socket_client.send_command_by_long_connection(strSend)
            if not str_back or "_ERR" in str_back:
                return recData, 1, "Failed to get updated board info!"

            analysisMode = boardConfig.get("analysisMode")
            if analysisMode == 1:
                dictionary = initial_analysis_ext(str_back)
            elif analysisMode == 2:
                dictionary = initial_analysis_special(str_back)
            else:
                dictionary = {}

            board_config_temp = copy.deepcopy(boardConfig)
            scan_board_config(board_config_temp, dictionary, analysisMode)
            board_config_temp["slotIndex"] = slotIndex
            board_config_temp["boardType"] = boardConfig.get("boardType")
            build_port_func = lambda: None

            with db_session.begin():
                card_id = f"{otnDeviceBasic.id}_{slotIndex}"
                if device_type == "FMT":
                    otn_device_card = db_session.query(FmtDeviceCards).filter(FmtDeviceCards.card_id == card_id).first()
                    build_port_func = build_fmt_ports_data
                elif device_type == "D6000":
                    otn_device_card = db_session.query(DcsDeviceCards).filter(DcsDeviceCards.card_id == card_id).first()
                    build_port_func = build_dcs_ports_data
                if not otn_device_card:
                    raise ValueError("can not find device card")
                card_detail = board_config_temp.get("basicInfo")
                board_type = board_config_temp.get("boardType")
                temperature = card_detail.get("Temperature")
                ports_data = json.dumps(build_port_func(board_type, [], {"boardInfos": [board_config_temp]}, 0))

                otn_device_card.type = board_type
                otn_device_card.temperature = temperature
                otn_device_card.ports_data = ports_data

                db_session.merge(otn_device_card)

            db_session.close()
        except socket.timeout:
            LOG.error("socket timed out!")
            LOG.error(traceback.format_exc())
            return recData, 1, "Failed, device connection timed out!"
        except Exception as e:
            LOG.error(f"Exception:{e}")
            LOG.error(traceback.format_exc())
            return recData, 1, "Failed, configuration failed!"
        finally:
            if socket_client is not None:
                socket_client.close_socket_client()
            distributed_lock.release()
            LOG.info("end modify config, release lock!")
    else:
        LOG.error("get lock fail!")
    return recData, 0, "success"


# 这个是串行的进行修改 且复用socket_client
# def batchModify_config(ip, slotIndex, updates, device_type):
#     if device_type not in ["FMT", "D6000"]:
#         raise ValueError(f"Invalid device_type: {device_type}, must be 'FMT' or 'D6000'")
#
#     recData_list = []  # 存储每个配置项的结果
#     nmu = get_nmu(ip)
#     startTime = time.time()
#     distributed_lock = DistributedLock(ip)
#     if distributed_lock.acquire():
#         socket_client = None
#         try:
#             LOG.info("get lock total time:" + str(time.time() - startTime))
#             socket_client = SocketClient(ip, time_out=5, model=device_type)
#             socket_client.create_socket_client()
#
#             str_back, errorCode, errorMsg = handle_batchModify_config(
#                 socket_client, slotIndex, nmu, updates
#             )
#             recData_list = str_back.split(";") if str_back else []
#
#
#             LOG.info("set config total time:" + str(time.time() - startTime))
#             if not recData_list:
#                 return recData_list, 1, "Failed, configuration result not obtained!"
#
#             db_session = invent_db.get_session()
#             otnDeviceBasic = db_session.query(OtnDeviceBasic).filter(OtnDeviceBasic.ip == ip).first()
#             if not otnDeviceBasic:
#                 return recData_list, 1, "Device not found in database!"
#             device_type = SERIES2MODEL[otnDeviceBasic.series]
#
#
#             nmu = get_nmu(ip)
#             boardIndex = nmu.get(slotIndex)
#             if not boardIndex:
#                 return recData_list, 1, "Slot is empty!"
#
#             boardConfig = FMT_CONFIG_MODEL.get(device_type).get("boardMapping").get(boardIndex)
#             if not boardConfig:
#                 return recData_list, 1, "Board config not found!"
#
#             strSend = f"<C{str(slotIndex).zfill(2)}_[PSWD_{socket_client.password}][STA_?][B_?]>"
#             str_back_status = socket_client.send_command_by_long_connection(strSend)
#             if not str_back_status or "_ERR" in str_back_status:
#                 return recData_list, 1, "Failed to get updated board info!"
#
#             analysisMode = boardConfig.get("analysisMode")
#             if analysisMode == 1:
#                 dictionary = initial_analysis_ext(str_back)
#             elif analysisMode == 2:
#                 dictionary = initial_analysis_special(str_back)
#             else:
#                 dictionary = {}
#
#             board_config_temp = copy.deepcopy(boardConfig)
#             scan_board_config(board_config_temp, dictionary, analysisMode)
#             board_config_temp["slotIndex"] = slotIndex
#             board_config_temp["boardType"] = boardConfig.get("boardType")
#             build_port_func = lambda: None
#
#             with db_session.begin():
#                 card_id = f"{otnDeviceBasic.id}_{slotIndex}"
#                 if device_type == "FMT":
#                     otn_device_card = db_session.query(FmtDeviceCards).filter(FmtDeviceCards.card_id == card_id).first()
#                     build_port_func = build_fmt_ports_data
#                 elif device_type == "D6000":
#                     otn_device_card = db_session.query(DcsDeviceCards).filter(DcsDeviceCards.card_id == card_id).first()
#                     build_port_func = build_dcs_ports_data
#                 if not otn_device_card:
#                     raise ValueError("can not find device card")
#                 card_detail = board_config_temp.get("basicInfo")
#                 board_type = board_config_temp.get("boardType")
#                 temperature = card_detail.get("Temperature")
#                 ports_data = json.dumps(build_port_func(board_type, [], {"boardInfos": [board_config_temp]}, 0))
#
#                 otn_device_card.type = board_type
#                 otn_device_card.temperature = temperature
#                 otn_device_card.ports_data = ports_data
#
#                 db_session.merge(otn_device_card)
#
#             db_session.close()
#
#             return recData_list, errorCode, errorMsg
#         except socket.timeout:
#             LOG.error("socket timed out!")
#             return recData_list, 1, "Device connection timed out!"
#         except Exception as e:
#             LOG.error(f"Exception:{e}")
#             return recData_list, 1, f"Configuration failed: {str(e)}"
#         finally:
#             if socket_client is not None:
#                 socket_client.close_socket_client()
#             distributed_lock.release()
#             LOG.info("end modify config, release lock!")
#     else:
#         LOG.error("get lock fail!")
#     return recData_list, 1, "Failed to acquire lock"

# 并行下发多个配置项
def batchModify_config(ip, slotIndex, updates, device_type):
    if device_type not in ["FMT", "D6000"]:
        raise ValueError(f"Invalid device_type: {device_type}, must be 'FMT' or 'D6000'")

    recData_list = []  # 存储每个配置项的结果
    nmu = get_nmu(ip)

    # 特殊处理：为槽位16（NMU）提供默认的boardIndex
    if str(slotIndex) == "16" and (not nmu or slotIndex not in nmu):
        LOG.info(f"NMU slot 16 not found in NMU mapping, using default boardIndex")
        if not nmu:
            nmu = {}
        # 对于D6000设备，NMU使用boardIndex "6601"，包含电源配置
        nmu[slotIndex] = "6601"  # 设置D6000 NMU的正确boardIndex
        LOG.info(f"Updated NMU mapping for D6000: {nmu}")

    startTime = time.time()
    distributed_lock = DistributedLock(ip)
    if distributed_lock.acquire():
        socket_client = None
        try:
            LOG.info("get lock total time:" + str(time.time() - startTime))

            # 检查是否包含电源操作，设置合适的超时时间
            has_power_operation = any(
                update.get("key", "").startswith("power_switch")
                for update in updates
            )

            # 为电源操作使用更长的超时时间
            timeout = 8 if has_power_operation else 5
            socket_client = SocketClient(ip, time_out=timeout, model=device_type)
            socket_client.create_socket_client()

            str_back, errorCode, errorMsg = handle_batchModify_config(
                ip, socket_client, slotIndex, nmu, updates
            )
            recData_list = str_back.split(";") if str_back else []

            LOG.info("set config total time:" + str(time.time() - startTime))
            if not recData_list:
                return recData_list, 1, "Failed, configuration result not obtained!"

            # 只有在配置成功且需要更新数据库时再进行数据库操作
            if errorCode == 0 or (has_power_operation and any("OK" in response for response in recData_list)):
                LOG.info("Configuration successful, updating database...")

                db_session = invent_db.get_session()
                otnDeviceBasic = db_session.query(OtnDeviceBasic).filter(OtnDeviceBasic.ip == ip).first()
                if not otnDeviceBasic:
                    return recData_list, 1, "Device not found in database!"
                device_type = SERIES2MODEL[otnDeviceBasic.series]

                nmu = get_nmu(ip)
                boardIndex = nmu.get(slotIndex)
                if not boardIndex:
                    return recData_list, 1, "Slot is empty!"

                boardConfig = FMT_CONFIG_MODEL.get(device_type).get("boardMapping").get(boardIndex)
                if not boardConfig:
                    return recData_list, 1, "Board config not found!"

                try:
                    strSend = f"<C{str(slotIndex).zfill(2)}_[PSWD_{socket_client.password}][STA_?][B_?]>"
                    str_back_status = socket_client.send_command_by_long_connection(strSend)

                    if str_back_status and "_ERR" not in str_back_status:
                        analysisMode = boardConfig.get("analysisMode")
                        if analysisMode == 1:
                            dictionary = initial_analysis_ext(str_back_status)
                        elif analysisMode == 2:
                            dictionary = initial_analysis_special(str_back_status)
                        else:
                            dictionary = {}

                        board_config_temp = copy.deepcopy(boardConfig)
                        scan_board_config(board_config_temp, dictionary, analysisMode)
                        board_config_temp["slotIndex"] = slotIndex
                        board_config_temp["boardType"] = boardConfig.get("boardType")
                        build_port_func = lambda: None

                        with db_session.begin():
                            card_id = f"{otnDeviceBasic.id}_{slotIndex}"
                            if device_type == "FMT":
                                otn_device_card = db_session.query(FmtDeviceCards).filter(
                                    FmtDeviceCards.card_id == card_id).first()
                                build_port_func = build_fmt_ports_data
                            elif device_type == "D6000":
                                otn_device_card = db_session.query(DcsDeviceCards).filter(
                                    DcsDeviceCards.card_id == card_id).first()
                                build_port_func = build_dcs_ports_data
                            if otn_device_card:
                                card_detail = board_config_temp.get("basicInfo")
                                board_type = board_config_temp.get("boardType")
                                temperature = card_detail.get("Temperature")
                                ports_data = json.dumps(
                                    build_port_func(board_type, [], {"boardInfos": [board_config_temp]}, 0))

                                otn_device_card.type = board_type
                                otn_device_card.temperature = temperature
                                otn_device_card.ports_data = ports_data

                                db_session.merge(otn_device_card)
                    else:
                        LOG.warning("Failed to get updated board info, skipping database update")

                except Exception as db_error:
                    LOG.warning(f"Database update failed, but configuration was successful: {db_error}")
                finally:
                    if 'db_session' in locals():
                        db_session.close()
            else:
                LOG.info("Configuration had errors, skipping database update")

            return recData_list, errorCode, errorMsg
        except socket.timeout:
            LOG.error("socket timed out!")
            return recData_list, 1, "Device connection timed out!"
        except Exception as e:
            LOG.error(f"Exception:{e}")
            return recData_list, 1, f"Configuration failed: {str(e)}"
        finally:
            if socket_client is not None:
                socket_client.close_socket_client()
            distributed_lock.release()
            LOG.info("end modify config, release lock!")
    else:
        LOG.error("get lock fail!")
    return recData_list, 1, "Failed to acquire lock"


def get_nmu(ip):
    db_session = invent_db.get_session()
    data = db_session.query(OtnTempData).filter(OtnTempData.ip == ip).first()
    return json.loads(data.nmu) if data else {}


def handle_modify_config(socket_client, slot_index, nmu, key, value):
    if not isinstance(nmu, dict):
        raise Exception("nmu info is empty!")
    boardIndex = nmu.get(slot_index)
    if boardIndex is None:
        raise Exception("this slot is empty!")
    # 通过FMT单板配置文件校验key是否合法，并获取真实下发配置的key
    boardConfig = FMT_CONFIG_MODEL.get(socket_client.model).get("boardMapping").get(boardIndex)
    LOG.info(f"boardConfig:{boardConfig}")
    if boardConfig is None:
        raise Exception("get board config failed!")

    boardConfig = boardConfig.get("businessInfo").get("config")
    if boardConfig is None:
        raise Exception("board config is empty!")

    matchValue = []
    get_config_key(boardConfig, key, matchValue, socket_client)
    if len(matchValue) == 0:
        raise Exception("board config not have this key!")

    command = f"<C{str(slot_index).zfill(2)}_[PSWD_{socket_client.password}][{matchValue[0]}_{value}]>"
    LOG.info("config command:%s" % command)
    str_back = socket_client.send_command_by_long_connection(command)
    LOG.info("config result:%s" % str_back)
    if len(str_back) > 0 and "_ERR" not in str_back:
        return str_back
    return ''

# 这个是串行的进行修改 且复用socket_client
# def handle_batchModify_config(socket_client, slot_index, nmu, updates):
#     if not isinstance(nmu, dict):
#         raise Exception("nmu信息为空！")
#     boardIndex = nmu.get(slot_index)
#     if boardIndex is None:
#         raise Exception("该插槽为空！")
#
#     boardConfig = FMT_CONFIG_MODEL.get(socket_client.model).get("boardMapping").get(boardIndex)
#     if boardConfig is None:
#         raise Exception("获取板卡配置失败！")
#     boardConfig = boardConfig.get("businessInfo").get("config")
#     if boardConfig is None:
#         raise Exception("板卡配置为空！")
#
#     responses = []
#     error_flag = 0
#     error_msgs = []
#
#     start_time = time.time()
#
#     for update in updates:
#         key = update.get("key")
#         value = update.get("value")
#         if not key or value is None:
#             responses.append("")
#             error_msgs.append(f"Invalid key-value: {key}={value}")
#             error_flag = 1
#             continue
#
#         matchValue = []
#         try:
#             get_config_key(boardConfig, key, matchValue, socket_client)
#             if not matchValue:
#                 raise Exception(f"配置项 {key} 不存在")
#
#             command = f"<C{str(slot_index).zfill(2)}_[PSWD_{socket_client.password}][{matchValue[0]}_{value}]>"
#             LOG.info(f"发送命令: {command}")
#             cmd_start = time.time()
#             str_back = socket_client.send_command_by_long_connection(command)
#             cmd_time = time.time() - cmd_start
#             LOG.debug(f"命令 {key} 执行耗时: {cmd_time:.2f}s")
#
#             responses.append(str_back)
#
#             if "_ERR" in str_back:
#                 error_flag = 1
#                 error_msgs.append(f"{key}: {str_back}")
#         except Exception as e:
#             responses.append("")
#             error_flag = 1
#             error_msgs.append(f"{key}: {str(e)}")
#
#     total_time = time.time() - start_time
#     LOG.info(f"所有命令执行完成，总耗时: {total_time:.2f} 秒")
#
#     combined_response = ";".join(responses)
#     error_msg = "; ".join(error_msgs) if error_msgs else ""
#     return combined_response, error_flag, error_msg


# 并行下发多个配置项
def handle_batchModify_config(ip, socket_client, slot_index, nmu, updates):
    if not isinstance(nmu, dict):
        raise Exception("NMU info is empty")
    boardIndex = nmu.get(slot_index)
    if boardIndex is None:
        raise Exception("Slot is empty")

    boardConfig = FMT_CONFIG_MODEL.get(socket_client.model).get("boardMapping").get(boardIndex)
    if boardConfig is None:
        raise Exception("Failed to get board config")
    boardConfig = boardConfig.get("businessInfo").get("config")
    if boardConfig is None:
        raise Exception("Board config is empty")

    socket_lock = threading.Lock()
    responses = [""] * len(updates)
    errors = [""] * len(updates)

    error_flag = [0]

    start_time = time.time()

    has_power_operation = any(
        update.get("key", "").startswith("power_switch")
        for update in updates
    )

    LOG.info(f"Operation timeout set to {socket_client.time_out}s (power operation: {has_power_operation})")

    def process_command(index, update):
        key = update.get("key")
        value = update.get("value")

        if not key or value is None:
            errors[index] = "Invalid parameters"
            error_flag[0] = 1
            return

        try:
            matchValue = []
            get_config_key(boardConfig, key, matchValue, socket_client)
            if not matchValue:
                errors[index] = f"Config item not found: {key}"
                error_flag[0] = 1
                return

            command = f"<C{str(slot_index).zfill(2)}_[PSWD_{socket_client.password}][{matchValue[0]}_{value}]>"

            with socket_lock:
                LOG.info(f"Thread {index} sending command: {command}")
                response = socket_client.send_command_by_long_connection(command)
                LOG.info(f"Thread {index} received response: {response}")

            if response and "_ERR" in response:
                errors[index] = f"Device error: {response}"
                error_flag[0] = 1
            elif not response or not response.strip():
                if key.startswith("power_switch"):
                    LOG.info(f"Power switch command completed, empty response is acceptable")
                    responses[index] = "OK"
                else:
                    errors[index] = "No response from device"
                    error_flag[0] = 1
            else:
                if any(success_indicator in response.upper() for success_indicator in
                       ["OK", "SUCCESS", "COMPLETE"]):
                    LOG.info(f"Command {key} executed successfully")
                    responses[index] = response.strip()
                elif key.startswith("power_switch") and len(response.strip()) > 0:
                    LOG.info(f"Power operation {key} appears successful: {response}")
                    responses[index] = "OK"
                else:
                    responses[index] = response.strip()

        except Exception as e:
            errors[index] = str(e)
            error_flag[0] = 1
            LOG.error(f"Error executing command {key}: {e}")

    threads = []
    for i, update in enumerate(updates):
        t = threading.Thread(target=process_command, args=(i, update))
        threads.append(t)
        t.start()

    for t in threads:
        t.join()

    for i in range(len(responses)):
        if errors[i] and not responses[i]:
            responses[i] = f"ERROR:{errors[i]}"

    end_time = time.time()
    total_time = end_time - start_time
    LOG.info(f"All commands executed in parallel in {total_time:.2f} seconds")

    combined_response = ";".join(responses)
    error_msgs = [f"{updates[i].get('key')}: {err}" for i, err in enumerate(errors) if err]
    error_msg = "; ".join(error_msgs) if error_msgs else ""

    return combined_response, error_flag[0], error_msg


def get_config_key(dictData, matchKey, matchValue, socket_client):
    for key, value in dictData.items():
        if key == matchKey:
            if "#" in value:
                parts = value.split("#")
                prefix = parts[0]
                if parts[1] and re.match(r'^\d+$', parts[1]):
                    num = int(parts[1])
                    value = f"{prefix}{num + 1}"
                else:
                    value = value.replace("#", "")
            matchValue.append(value)
            return
        # 多端口的配置，需要从CONFIG_KEY_PORT_MAP拿到对应的端口index填充value中的@位置
        elif key in matchKey and isinstance(value, list):
            matchValue.append(value[0].replace("@", CONFIG_KEY_PORT_MAP.get(
                matchKey.replace(key, "")) if socket_client.model == "FMT" else matchKey.replace(key, "")))
            return
        elif isinstance(value, dict):
            get_config_key(value, matchKey, matchValue, socket_client)
        else:
            pass


def initial_analysis_special(str_back):
    result = {}
    index = 0
    strStart = 0
    while index < len(str_back):
        if str_back[index] == '[':
            strStart = index
        if str_back[index] == ']':
            strEnd = index
            strData = str_back[strStart + 1: strEnd]
            if strData.startswith('B_'):
                OrderArray = strData.split('_')
                if len(OrderArray) >= 6:
                    SV = OrderArray[4]
                    HV = OrderArray[5]
                    result['SV'] = SV
                    result['HV'] = HV
            elif re.search(r"CH\d+_P_([-+]?\d+\.\d+)_\d+", strData):
                result[strData.split("_")[0] + "_" + strData.split("_")[1]] = strData.split("_")[2]
            else:
                strData = str_back[strStart + 1: strEnd]
                # 取最后一个_进行切割
                last_index = strData.rfind('_')
                key = strData[0:last_index]
                value = strData[last_index + 1:]
                result[key] = value
        index += 1
    return result


def initial_analysis_ext(str_back):
    result = {}
    index = 0
    strStart = 0
    while index < len(str_back):
        if str_back[index] == '[':
            strStart = index
        if str_back[index] == ']':
            strEnd = index
            strData = str_back[strStart + 1: strEnd]
            if strData.startswith('B_'):
                OrderArray = strData.split('_')
                if len(OrderArray) >= 6:
                    SV = OrderArray[4]
                    HV = OrderArray[5]
                    result['SV'] = [SV]
                    result['HV'] = [HV]
            else:
                OrderArray = strData.split('_')
                temp = []
                for i in range(1, len(OrderArray)):
                    temp.append(OrderArray[i])
                if OrderArray[0] in result:
                    array = [result[OrderArray[0]], temp]
                    result[OrderArray[0]] = array
                else:
                    result[OrderArray[0]] = temp
        index += 1
    return result


def parse_lldp_data(input_string):
    content = input_string.strip('<>').replace('C00_', '')
    result = {
        "Port1": {},
        "Port2": {},
        "Port3": {},
        "Port4": {},
        "Port5": {},
        "Port6": {}
    }
    items = re.findall(r'\[(.*?)\]', content)
    port_status = {}
    for item in items:
        port_match = re.match(r'^Port([1-6])_(N|Y)$', item)
        if port_match:
            port_num = port_match.group(1)
            status = port_match.group(2)
            port_status[f"Port{port_num}"] = status
    port_fields = ["PortSysName", "PortSysIP", "PortMac", "PortID", "PortDesc", "PortInterfaceNum"]
    for port_num in ["1", "2", "3", "4", "5", "6"]:
        port_key = f"Port{port_num}"
        for field in port_fields:
            pattern1 = f"{field}{port_num}_(.+)"
            pattern2 = f"{field}_{port_num}_(.+)"
            pattern3 = f"{port_num}{field}_(.+)"

            for item in items:
                for pattern in [pattern1, pattern2, pattern3]:
                    match = re.match(pattern, item)
                    if match:
                        field_name = field.replace("Port", "")
                        value = match.group(1)
                        result[port_key][field_name] = value
    for item in items:
        if "_" in item:
            parts = item.split("_", 1)
            if len(parts) == 2:
                key, value = parts
                for port_num in ["1", "2", "3", "4", "5", "6"]:
                    if port_num in key:
                        port_key = f"Port{port_num}"
                        field_name = key.replace(port_num, "").replace("Port", "")

                        if field_name and field_name not in result[port_key]:
                            result[port_key][field_name] = value

    port5_patterns = [
        r'PortSysName5_(.+)', r'PortSysName_5_(.+)', r'5PortSysName_(.+)',
        r'PortSysIP5_(.+)', r'PortSysIP_5_(.+)', r'5PortSysIP_(.+)',
        r'PortMac5_(.+)', r'PortMac_5_(.+)', r'5PortMac_(.+)',
        r'PortID5_(.+)', r'PortID_5_(.+)', r'5PortID_(.+)'
    ]

    port6_patterns = [
        r'PortSysName6_(.+)', r'PortSysName_6_(.+)', r'6PortSysName_(.+)',
        r'PortSysIP6_(.+)', r'PortSysIP_6_(.+)', r'6PortSysIP_(.+)',
        r'PortMac6_(.+)', r'PortMac_6_(.+)', r'6PortMac_(.+)',
        r'PortID6_(.+)', r'PortID_6_(.+)', r'6PortID_(.+)'
    ]

    for item in items:
        for pattern in port5_patterns:
            match = re.match(pattern, item)
            if match:
                field_name = re.search(r'Port(Sys\w+|Mac|ID)', pattern).group(1)
                value = match.group(1)
                result["Port5"][field_name] = value

        for pattern in port6_patterns:
            match = re.match(pattern, item)
            if match:
                field_name = re.search(r'Port(Sys\w+|Mac|ID)', pattern).group(1)
                value = match.group(1)
                result["Port6"][field_name] = value

    return result


def get_dcs_lldp_info(ip, device_type="FMT"):
    lldpData = {}
    distributed_lock = DistributedLock(ip)
    if distributed_lock.acquire():
        socket_client = None
        try:
            socket_client = SocketClient(ip, time_out=5, model=device_type)
            socket_client.create_socket_client()
            command = f"<C00_[PSWD_{socket_client.password}][showlldp_?]>"
            str_back = socket_client.send_command_by_long_connection(command)

            if len(str_back) > 0:
                lldpData = parse_lldp_data(str_back)

                items = re.findall(r'\[(.*?)\]', str_back)

                for port_num in ["5", "6"]:
                    port_key = f"Port{port_num}"
                    port_related = [item for item in items if port_num in item]

                    if not lldpData[port_key] and port_related:
                        for item in port_related:
                            if "_" in item:
                                parts = item.split("_", 1)
                                if len(parts) == 2:
                                    key, value = parts
                                    if port_num in key:
                                        field_name = key.replace(port_num, "").strip()
                                        if field_name:
                                            lldpData[port_key][field_name] = value
        except Exception:
            pass
        finally:
            if socket_client is not None:
                socket_client.close_socket_client()
            distributed_lock.release()

    return lldpData


def beat_sync_dcs_lldp_info():
    db_session = invent_db.get_session()
    devices = db_session.query(DcsDeviceBasic).all()
    if not devices:
        return

    for device in devices:
        try:
            lldpData = get_dcs_lldp_info(device.ip)
            if lldpData:
                ports_with_data = [port for port, data in lldpData.items() if data]
                if ports_with_data:
                    redis_client.set(f"dcs_lldp_info:{device.ip}", json.dumps(lldpData))
        except Exception:
            pass

    db_session.close()
    return


redis_client = redis.StrictRedis(host='redis-service', port=6379, db=0, decode_responses=True)

def parse_local_data(input_string):
    result = {
        "SystemName": "D6000",
        "SystemDescription": "D6000"
    }

    if not input_string:
        return result

    sys_name_match = re.search(r'\[SysName_(.*?)\]', input_string)
    if sys_name_match:
        result["SystemName"] = sys_name_match.group(1)

    sys_desc_match = re.search(r'\[SysDesc_(.*?)\]', input_string)
    if sys_desc_match:
        result["SystemDescription"] = sys_desc_match.group(1)

    items = re.findall(r'\[(.*?)\]', input_string)
    for item in items:
        if "_" in item:
            parts = item.split("_", 1)
            if len(parts) == 2:
                key, value = parts
                if key == "SysName":
                    result["SystemName"] = value
                elif key == "SysDesc":
                    result["SystemDescription"] = value

    return result


def get_dcs_local_info(ip, device_type="D6000"):
    local_data = {
        "SystemName": "D6000",
        "SystemDescription": "D6000"
    }
    
    distributed_lock = DistributedLock(ip)
    if distributed_lock.acquire():
        socket_client = None
        try:
            socket_client = SocketClient(ip, time_out=5, model=device_type)
            socket_client.create_socket_client()
            command = f"<C00_[PSWD_{socket_client.password}][showlocal_?]>"
            str_back = socket_client.send_command_by_long_connection(command)

            if len(str_back) > 0:
                local_data = parse_local_data(str_back)
        except Exception as e:
            LOG.error(f"Error getting local info for {ip}: {str(e)}")
        finally:
            if socket_client:
                socket_client.close_socket_client()
            distributed_lock.release()
    
    return local_data