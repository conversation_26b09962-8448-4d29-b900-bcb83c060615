import {But<PERSON>, Di<PERSON>r, Flex, Form, Input, message, Modal} from "antd";
import {forwardRef, useImperativeHandle, useState} from "react";
import {copyTemplate} from "@/modules-ampcon/apis/template_api";

const TemplateCopyModal = forwardRef((props, ref) => {
    useImperativeHandle(ref, () => ({
        showTemplateCopyModal: templateName => {
            setSelectedTemplateName(templateName);
            templateCopyForm.setFieldValue("originTemplateName", templateName);
            setIsShowModal(true);
        },
        hideTemplateCopyModal: () => {}
    }));

    const title = "Copy Template";
    const originTemplateNameLabel = "Origin Name";
    const templateNameLabel = "New Name";
    const templateDescriptionLabel = "Description";
    const {saveCallback} = props;

    const [templateCopyForm] = Form.useForm();

    const [isShowModal, setIsShowModal] = useState(false);
    const [selectedTemplateName, setSelectedTemplateName] = useState("");
    const [isNewTemplateNameValid, setIsNewTemplateNameValid] = useState(true);
    const [newTemplateNameInvalidMessage, setNewTemplateNameInvalidMessage] = useState("");
    const [isNewTemplateNameInvalidBefore, setIsNewTemplateNameInvalidBefore] = useState(false);

    const isInputNewTemplateNameValid = newTemplateName => {
        if (newTemplateName === "") {
            setIsNewTemplateNameValid(false);
            setNewTemplateNameInvalidMessage("New Name is required.");
            return false;
        }
        if (newTemplateName === selectedTemplateName) {
            setIsNewTemplateNameValid(false);
            setNewTemplateNameInvalidMessage("New Template Name cannot be the same as Origin Template Name.");
            return false;
        }
        setIsNewTemplateNameValid(true);
        setNewTemplateNameInvalidMessage("");
        return true;
    };

    return isShowModal ? (
        <Modal
            className="ampcon-middle-modal"
            title={
                <div>
                    {title}
                    <Divider style={{marginTop: 8, marginBottom: 0}} />
                </div>
            }
            open={isShowModal}
            onOk={() => {}}
            onCancel={() => {
                setIsShowModal(false);
                templateCopyForm.resetFields();
                setSelectedTemplateName("");
                setIsNewTemplateNameValid(true);
                setNewTemplateNameInvalidMessage("");
                setIsNewTemplateNameInvalidBefore(false);
            }}
            footer={
                <Flex vertical>
                    <Divider style={{marginTop: 0, marginBottom: 20}} />
                    <Flex justify="flex-end">
                        <Button
                            onClick={() => {
                                setIsShowModal(false);
                                templateCopyForm.resetFields();
                                setSelectedTemplateName("");
                                setIsNewTemplateNameValid(true);
                                setNewTemplateNameInvalidMessage("");
                                setIsNewTemplateNameInvalidBefore(false);
                            }}
                        >
                            Cancel
                        </Button>
                        <Button
                            type="primary"
                            onClick={() => {
                                if (!isInputNewTemplateNameValid(templateCopyForm.getFieldValue("newTemplateName"))) {
                                    setIsNewTemplateNameInvalidBefore(true);
                                    return;
                                }
                                copyTemplate(
                                    selectedTemplateName,
                                    templateCopyForm.getFieldValue("newTemplateName"),
                                    templateCopyForm.getFieldValue("newTemplateDescription")
                                ).then(response => {
                                    if (response.status !== 200) {
                                        message.error(response.info);
                                    } else {
                                        setIsShowModal(false);
                                        templateCopyForm.resetFields();
                                        message.success(response.info);
                                        if (saveCallback) {
                                            saveCallback();
                                        }
                                    }
                                });
                            }}
                        >
                            Save
                        </Button>
                    </Flex>
                </Flex>
            }
        >
            <Form
                layout="horizontal"
                labelAlign="left"
                labelCol={{flex: "120px"}}
                wrapperCol={{flex: "280px"}}
                labelWrap
                className="label-wrap"
                form={templateCopyForm}
                style={{minHeight: "260.23px"}}
            >
                <Form.Item name="originTemplateName" label={originTemplateNameLabel}>
                    <Input style={{backgroundColor: "#f0f0f0"}} readOnly />
                </Form.Item>
                <Form.Item
                    name="newTemplateName"
                    label={templateNameLabel}
                    validateStatus={isNewTemplateNameValid ? "success" : "error"}
                    help={isNewTemplateNameValid ? "" : newTemplateNameInvalidMessage}
                    initialValue=""
                    required
                >
                    <Input
                        onChange={() => {
                            if (isNewTemplateNameInvalidBefore) {
                                isInputNewTemplateNameValid(templateCopyForm.getFieldValue("newTemplateName"));
                            }
                        }}
                        maxLength={32}
                    />
                </Form.Item>
                <Form.Item name="newTemplateDescription" label={templateDescriptionLabel} initialValue="">
                    <Input maxLength={255} />
                </Form.Item>
            </Form>
        </Modal>
    ) : null;
});

export default TemplateCopyModal;
