import React, {useEffect, useRef, useState, forwardRef, useImper<PERSON><PERSON><PERSON>le} from "react";
import {message, Tree, Space, Button} from "antd";
import {Provider, useDispatch, useSelector} from "react-redux";
import {useRequest} from "ahooks";
import {
    apiGetProvision,
    netconfByXML,
    netconfChange,
    objectDel,
    objectGet,
    objectEdit,
    apiConnectionByGroup
} from "@/modules-otn/apis/api";
import useUserRight from "@/modules-otn/hooks/useUserRight";
import Provision from "@/modules-otn/pages/otn/service/provision";
import {addObjectDisabledIcon, addObjectFocusIcon, addObjectIcon} from "@/modules-otn/pages/otn/device/device_icons";
import {gLabelList} from "@/store/modules/otn/languageOTNSlice";
import {
    deleteDisabledIcon,
    deleteEnabledIcon,
    deleteFocusIcon,
    editServiceDisabledIcon,
    editServiceEnabledIcon,
    editServiceFocusIcon
} from "@/modules-otn/pages/otn/service/service_icon";
import {openDBModalEdit} from "@/modules-otn/components/form/edit_form_db";
import {bigModal, smallModal} from "@/modules-otn/components/modal/custom_modal";
import {convertToArray, DebounceButton} from "@/modules-otn/utils/util";
import {setTableFilter, setConnections} from "@/store/modules/otn/mapSlice";
import store from "@/store/store";

const dataConfigs5 = ["client", "och", "oms", "ots"];

const ServiceTree = forwardRef(({serviceType}, ref) => {
    const {labelList} = useSelector(state => state.languageOTN);
    const [treeData, setTreeData] = useState([]);
    const [selectOMS, setSelectOMS] = useState(null);
    const [selectedKeys, setSelectedKeys] = useState([]);
    const [selectedData, setSelectedData] = useState([]);
    const [expandedKeys, setExpandedKeys] = useState([]);
    const [addProvisionWating, setAddProvisionWating] = useState(false);
    const addProvisionRef = useRef();
    const [autoExpandParent, setAutoExpandParent] = useState(true);
    const {runAsync} = useRequest(apiGetProvision, {manual: true});
    const {runAsync: connectionRunAsync} = useRequest(apiConnectionByGroup, {manual: true});
    const userRight = useUserRight();
    const dispatch = useDispatch();
    useImperativeHandle(ref, () => ({
        selectTransfer
    }));
    useEffect(() => {
        loadOMSTree().then();
    }, []);

    const setFindInfo = obj => {
        dispatch(setTableFilter(obj));
    };

    const loadOMSTree = async () => {
        try {
            const data = {};
            const rs = (await objectGet("nms:provision", {})).documents;
            rs.map(item => {
                if (!data[item.value.type]) data[item.value.type] = [];

                item.value.id = item.id;
                data[item.value.type].push({
                    title: item.value.name,
                    key: item.id,
                    data: item.value
                });
            });
            const treeData = [];

            if (serviceType === "optics") {
                dataConfigs5.forEach(item => {
                    const treeDataItem = {
                        title: item.toUpperCase(),
                        key: item
                    };
                    if (item === "client") {
                        treeDataItem.children = data[item]?.map(clientItem => {
                            return {
                                ...clientItem,
                                key: clientItem.key
                            };
                        });
                    }
                    if (item === "och") {
                        const children = data[item]?.map(ochItem => {
                            const {title, key, data} = ochItem;
                            const clientTreeData = treeData.find(item => item.key === "client")?.children;

                            const children = clientTreeData?.reduce((res, clientItem) => {
                                const isExist = res.find(resItem => resItem.title === clientItem.title);
                                const isMatchClient = clientItem?.data?.och?.includes(key);
                                if (!isExist && isMatchClient) res.push(structuredClone(clientItem));
                                return res;
                            }, []);
                            return {title, key, children, data};
                        });

                        treeDataItem.children = children;
                    }

                    if (item === "oms") {
                        const children = data[item]?.map(ochItem => {
                            const {title, key, data} = ochItem;
                            const children = treeData
                                .find(item => item.key === "och")
                                ?.children?.reduce((res, item) => {
                                    if (item?.data?.oms?.includes(key)) res.push(structuredClone(item));
                                    return res;
                                }, []);
                            return {title, key, children, data};
                        });
                        treeDataItem.children = children;
                    }
                    if (item === "ots") {
                        const children = data[item]?.map(ochItem => {
                            const {title, key, data} = ochItem;
                            const children = treeData
                                .find(item => item.key === "oms")
                                ?.children?.reduce((res, item) => {
                                    if (
                                        item?.data?.ots?.find(otsItem =>
                                            otsItem.find(
                                                otsItemItem => otsItemItem.dire === 1 && otsItemItem.ots === key
                                            )
                                        )
                                    )
                                        res.push(structuredClone(item));
                                    return res;
                                }, []);
                            return {title, key, children, data};
                        });
                        treeDataItem.children = children;
                    }

                    treeData.unshift(treeDataItem);
                });
            }

            updateKeys(treeData);
            setTreeData(treeData);
        } catch (e) {
            setTreeData([]);
        }
    };

    const selectTransfer = obj => {
        try {
            loadOMSTree().then(() => {
                setExpandedKeys([obj.type]);
                setSelectedKeys([`${obj.type}&${obj.dbKey}`]);
                objectGet("nms:provision", {DBKey: obj.dbKey}).then(rs => {
                    const v = rs.documents[0];
                    const selectObj = {...v.value, id: v.id};
                    setSelectOMS(selectObj);
                    setSelectedData([selectObj]);
                    selectService(obj.dbKey, selectObj, "add").then();
                });
            });
        } catch (e) {
            // console.log(e);
        }
    };

    const addProvision = () => {
        const saveSuccess = obj => {
            modal.destroy();
            selectTransfer(obj);
        };

        const modal = bigModal({
            width: "100%",
            title: labelList.create_service,
            content: (
                <Provider store={store}>
                    <Provision
                        ref={addProvisionRef}
                        serviceType={selectOMS}
                        saveSuccess={saveSuccess}
                        setAddProvisionWating={setAddProvisionWating}
                    />
                </Provider>
            ),
            okText: labelList.save,
            onOk: _ => {
                modal.update({okButtonProps: {loading: true}});
                addProvisionRef.current?.onOk().then(r => {
                    if (!r) {
                        modal.update({okButtonProps: {loading: false}});
                    }
                });
            },
            cancelText: labelList.reset,
            onCancel: _ => {
                addProvisionRef.current?.onReset();
            }
            // footer: (a) => {
            //     console.log()
            //     return (
            //         <Space
            //             style={{display: "flex", flex: 1, justifyContent: "flex-end"}}
            //             className="ant-modal-confirm-btns"
            //         >
            //             <Button
            //                 type="primary"
            //                 disabled={addProvisionWating || userRight.disabled}
            //                 loading={addProvisionWating}
            //                 onClick={() => {
            //                     addProvisionRef.current?.onOk();
            //                 }}
            //             >
            //                 {labelList.save}
            //             </Button>
            //             <Button
            //                 disabled={addProvisionWating}
            //                 onClick={() => {
            //                     addProvisionRef.current?.onReset();
            //                 }}
            //             >
            //                 {labelList.reset}
            //             </Button>
            //         </Space>
            //     );
            // }
        });
    };

    const onExpand = newExpandedKeys => {
        setExpandedKeys(newExpandedKeys);
        setAutoExpandParent(false);
    };

    const execDeleteService = async selectOMS => {
        try {
            if (dataConfigs5.includes(selectOMS.type)) {
                const _neList = [];
                const hasDeleteNeList = [];
                for (let m = 0; m < selectOMS.fiber.length - 1; m++) {
                    const neID = selectOMS.fiber[m].ne;
                    if (_neList.includes(neID)) {
                        continue;
                    }
                    _neList.push(neID);
                    try {
                        const _ne = await objectGet("", {DBKey: `config:ne:${neID}`}, null, false);
                        if (_ne?.total === 0) {
                            hasDeleteNeList.push(neID);
                        } else {
                            if (_ne?.documents?.[0]?.value?.runState === 0) {
                                message.error(labelList.ne_is_lost.format(neID));
                                return false;
                            }
                            if (_ne?.documents?.[0]?.value?.state > 1) {
                                message.error(labelList.ne_is_upgrade.format(neID));
                                return false;
                            }
                        }
                    } catch (er) {
                        //
                    }
                }
                if (selectOMS.type === "ots") {
                    const omss = (await objectGet("nms:provision", {type: "oms"}, null, false)).documents;
                    let found = false;
                    for (let i = 0; i < omss.length; i++) {
                        const {ots} = omss[i].value;
                        for (let j = 0; j < ots.length; j++) {
                            if (ots[j].filter(s => s.ots === selectOMS.id).length > 0) {
                                found = true;
                                break;
                            }
                        }
                    }
                    if (found) {
                        message.error(labelList.delete_ots_confirm).then();
                        return false;
                    }
                }
                let allProvision;
                if (selectOMS.type === "oms") {
                    allProvision = (await objectGet("nms:provision", {}, null, false)).documents;
                    const och = await objectGet(
                        "nms:provision",
                        {
                            type: "och",
                            oms: selectOMS.id
                        },
                        null,
                        false
                    );
                    if (och.total > 0) {
                        message.error(labelList.delete_oms_confirm).then();
                        return false;
                    }
                } else if (selectOMS.type === "och") {
                    allProvision = (await objectGet("nms:provision", {}, null, false)).documents;
                    const och = (
                        await objectGet(
                            "nms:provision",
                            {
                                type: "client"
                            },
                            null,
                            false
                        )
                    ).documents;
                    if (och.filter(i => i.value.och.includes(selectOMS.id)).length > 0) {
                        message.error(labelList.delete_och_confirm).then();
                        return false;
                    }
                    const omsList = convertToArray(selectOMS.oms);
                    for (let m = 0; m < omsList.length; m++) {
                        const omsID = omsList[m];
                        const frequencyIndex = selectOMS.frequencyIndex[omsID];
                        const wss = (
                            await objectGet("nms:provision", {DBKey: omsID}, null, false)
                        ).documents[0].value.ne.filter(item => item.card.startsWith("WSS"));
                        for (let i = 0; i < wss.length; i++) {
                            const item = wss[i];
                            if (!hasDeleteNeList.includes(item.ne)) {
                                await netconfByXML({
                                    ne_id: item.ne,
                                    msg: false,
                                    xml: {
                                        components: {
                                            $: {
                                                xmlns: "http://openconfig.net/yang/platform"
                                            },
                                            component: {
                                                name: item.card,
                                                wss: {
                                                    config: {
                                                        "frequency-channel": {
                                                            $: {
                                                                xmlns: "http://openconfig.net/yang/platform/wss",
                                                                "nc:operation": "delete"
                                                            },
                                                            index: frequencyIndex
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }).then(rs => {
                                    if (rs?.apiResult === "fail") {
                                        // eslint-disable-next-line no-console
                                        console.error(rs.apiMessage);
                                    }
                                });
                            }
                            const ch = (
                                await objectGet(
                                    "ne:5:component",
                                    {
                                        ne_id: item.ne,
                                        name: item.card
                                    },
                                    null,
                                    false
                                )
                            ).documents[0];
                            const newCh = [];
                            if (ch?.value?.data?.wss?.config?.["frequency-channel"]) {
                                convertToArray(ch.value.data.wss.config["frequency-channel"]).map(c => {
                                    if (c.index !== frequencyIndex) {
                                        newCh.push(c);
                                    }
                                });
                                const v = {...ch.value};
                                v.data.wss.config["frequency-channel"] = newCh;
                                // eslint-disable-next-line no-console
                                const r = await objectEdit({
                                    key: ch.id,
                                    data: v,
                                    msg: false
                                });
                                if (r.apiResult === "fail") {
                                    // eslint-disable-next-line no-console
                                    console.log("save wss channel fail!");
                                }
                            }
                        }
                    }
                }

                const fiberList = [...selectOMS.fiber];

                while (fiberList.length > 0) {
                    const fiber = fiberList.shift();
                    if (selectOMS.type === "oms" || selectOMS.type === "och") {
                        let found = false;
                        for (let i = 0; i < allProvision.length; i++) {
                            if (allProvision[i].id === selectOMS.id) {
                                continue;
                            }
                            const fbs = allProvision[i].value.fiber;

                            for (let j = 0; j < fbs.length; j++) {
                                if (fbs[j].ne === fiber.ne && fbs[j].index === fiber.index) {
                                    found = true;
                                    break;
                                }
                            }
                            if (found) {
                                break;
                            }
                        }
                        if (found) {
                            continue;
                        }
                    }
                    if (!hasDeleteNeList.includes(fiber.ne)) {
                        try {
                            const rs = await netconfChange({
                                ne_id: fiber.ne,
                                msg: false,
                                operation: "delete",
                                entity: "connection",
                                keys: [fiber.index.toString()],
                                values: {}
                            });

                            if (rs.apiResult === "fail") {
                                if (
                                    rs.apiMessage.indexOf("not exist") > -1 ||
                                    rs.apiMessage.indexOf("it is undefined")
                                ) {
                                    // not exist, continue;
                                } else {
                                    message.error(labelList.delete_failed);
                                    return false;
                                }
                            }
                        } catch (e3) {
                            // eslint-disable-next-line no-console
                            console.log(e3);
                        }
                    }
                }
                const rs = await objectDel({
                    key: selectOMS.id,
                    msg: "error",
                    success: () => {
                        //
                    }
                });
                if (rs.apiResult === -1 || rs.apiResult === "fail") {
                    return false;
                }
                return true;
            }
        } catch (e) {
            // console.log(e);
        }
    };

    const deleteService = async () => {
        try {
            let delSuccess = false;
            let delFail = false;
            for (let i = 0; i < selectedData.length; i++) {
                const selectOMS = selectedData[i];
                const rs = await execDeleteService(selectOMS);
                if (rs) {
                    delSuccess = true;
                }
                if (!rs) {
                    delFail = true;
                }
            }
            if (!delFail) {
                message.success(labelList.delete_success);
            }
            if (delSuccess) {
                loadOMSTree().then();
                setSelectOMS(null);
                setFindInfo({type: "UN_SELECTED"});
                if (serviceType === "optics") {
                    connectionRunAsync("ne:5:connection", {}).then(res => {
                        const portConfig = {
                            BAIN: "PAOUT",
                            BAOUT: "PAIN",
                            PAOUT: "BAIN",
                            PAIN: "BAOUT",
                            LA1IN: "LA2OUT",
                            LA2OUT: "LA1IN",
                            LA1OUT: "LA2IN",
                            LA2IN: "LA1OUT"
                        };
                        const {connectionInfo} = res;

                        try {
                            const connectionData = connectionInfo?.map(item => ({
                                ne_id: item.value.ne_id,
                                ...item.value.data.config
                            }));
                            const flagArr = [];
                            const countArr = [];
                            let res = [];
                            connectionData.forEach(item => {
                                if (item.source && item.dest) {
                                    let sourcePort = "";
                                    let destPort = "";
                                    const sourceIP =
                                        item.source.split("|")[1] === undefined
                                            ? item.ne_id
                                            : item.source.split("|")[0];
                                    const destIP =
                                        item.dest.split("|")[1] === undefined ? item.ne_id : item.dest.split("|")[0];
                                    const sportname =
                                        item.source.split("-", 3).join("-").split("|")[1] ??
                                        item.source.split("-", 3).join("-");
                                    const dportname =
                                        item.dest.split("-", 3).join("-").split("|")[1] ??
                                        item.dest.split("-", 3).join("-");
                                    const sp = item.source.split("-").pop();
                                    const dp = item.dest.split("-").pop();
                                    const sport_1 = `${sportname}-${sp}`;
                                    const sport_2 = `${sportname}-${portConfig[sp] ?? sp}`;
                                    const dport_1 = `${dportname}-${dp}`;
                                    const dport_2 = `${dportname}-${portConfig[dp] ?? dp}`;
                                    const source = `${sourceIP}|${sport_1}`;
                                    const dest = `${destIP}|${dport_1}`;
                                    countArr.push({...item, connection: `${source}&${dest}`});
                                    if (flagArr.includes(source) && flagArr.includes(dest)) {
                                        return true;
                                    }
                                    if (sport_1 === sport_2) {
                                        flagArr.push(`${sourceIP}|${sport_1}`);
                                        sourcePort = sport_1;
                                    } else {
                                        flagArr.push(`${sourceIP}|${sport_1}`);
                                        flagArr.push(`${sourceIP}|${sport_2}`);
                                        sourcePort = `${sport_1}/${sport_2}`;
                                    }
                                    if (dport_1 === dport_2) {
                                        flagArr.push(`${destIP}|${dport_1}`);
                                        destPort = dport_1;
                                    } else {
                                        flagArr.push(`${destIP}|${dport_1}`);
                                        flagArr.push(`${destIP}|${dport_2}`);
                                        destPort = `${dport_1}/${dport_2}`;
                                    }
                                    res.push({
                                        sourceIP,
                                        sourcePort,
                                        destIP,
                                        destPort
                                    });
                                }
                            });
                            res = res.map(item => {
                                let refArr = [];
                                const resArr = [];
                                let state;
                                if (item.sourceIP === item.destIP) {
                                    refArr = [
                                        `${item.sourceIP}|${item.sourcePort.split("/")[0]}&${item.destIP}|${
                                            item.destPort.split("/")[0]
                                        }`,
                                        `${item.destIP}|${item.destPort.split("/")[1] ?? item.destPort.split("/")[0]}&${
                                            item.sourceIP
                                        }|${item.sourcePort.split("/")[1] ?? item.sourcePort.split("/")[0]}`
                                    ];
                                } else {
                                    refArr = [
                                        `${item.sourceIP}|${item.sourcePort.split("/")[0]}&${item.destIP}|${
                                            item.destPort.split("/")[0]
                                        }`,
                                        `${item.sourceIP}|${item.sourcePort.split("/")[0]}&${item.destIP}|${
                                            item.destPort.split("/")[0]
                                        }`,
                                        `${item.destIP}|${item.destPort.split("/")[1] ?? item.destPort.split("/")[0]}&${
                                            item.sourceIP
                                        }|${item.sourcePort.split("/")[1] ?? item.sourcePort.split("/")[0]}`,
                                        `${item.destIP}|${item.destPort.split("/")[1] ?? item.destPort.split("/")[0]}&${
                                            item.sourceIP
                                        }|${item.sourcePort.split("/")[1] ?? item.sourcePort.split("/")[0]}`
                                    ];
                                }
                                countArr.forEach(element => {
                                    if (refArr.includes(element.connection)) {
                                        resArr.push(element);
                                    }
                                });
                                if (resArr.length !== refArr.length) {
                                    state = labelList.partial;
                                } else {
                                    state = labelList.complete;
                                }
                                return {
                                    ...item,
                                    state,
                                    originValue: resArr
                                };
                            });
                            // 处理后的fiber conncetion信息
                            dispatch(setConnections(res));
                        } catch (e) {
                            // eslint-disable-next-line no-console
                            console.log(e);
                        }
                    });
                }
            }
            return !delFail;
        } catch (e) {
            // eslint-disable-next-line no-console
            console.log(e);
            message.error(labelList.delete_failed);
            return false;
        }
    };

    const refSelect = useRef(false);
    refSelect.current = typeof selectOMS === "object";

    const selectService = async (db_key, nodeData, action) => {
        try {
            if (nodeData.type === "client" && nodeData?.och.length !== 2) {
                const provision = await runAsync({db_key});
                // eslint-disable-next-line no-unsafe-optional-chaining
                const servicePortList = [...provision?.servicePortList];
                const portList = {};
                if (provision?.apiResult !== "fail") {
                    const _nodeList = provision.nodeList.flat(Infinity);
                    _nodeList.forEach(node => {
                        const [ne_id, card, port] = node.id.split("/");
                        if (!portList[ne_id]) {
                            portList[ne_id] = [];
                        }
                        if (node.param.type === "OLP") {
                            const apsNo = ["OP2", "OPB2-I"].includes(node.param.vendorType) ? port.split("-")[3] : "1";
                            portList[ne_id].push({card, vendorType: node.param.vendorType, apsNo});
                        }
                        if (node.type === "component") {
                            node.param.port.data.forEach(lineCard => {
                                if (!portList[lineCard.ne]) {
                                    portList[lineCard.ne] = [];
                                }
                                if (!portList[lineCard.ne].find(f => f.card === lineCard.card)) {
                                    portList[lineCard.ne].push({
                                        card: lineCard.card,
                                        vendorType: node.param.vendorType
                                    });
                                }
                            });
                        }
                        const ports = [];
                        const {left, right} = node.param.port;
                        // 存放端口
                        if (left) {
                            left.forEach(l => ports.push(l.split("/")[1]));
                        }
                        if (right) {
                            right.forEach(r => ports.push(r.split("/")[1]));
                        }
                        // 获取光功率,保护
                        if (node.type === "protection") {
                            const _port = right[0].split("-")?.splice(1, 3).join("-");
                            const _left = left.map(item => item.split("/")[1]);
                            const _right = right.map(item => item.split("/")[1]);
                            servicePortList.push({
                                card,
                                ne_id,
                                ports: [..._left, ..._right, `APS-${_port}`]
                            });
                        }
                    });
                }
                provision.portList = portList;
                setFindInfo({
                    type: nodeData.type,
                    key: db_key,
                    name: nodeData.name,
                    provision,
                    servicePortList,
                    action
                });
                return;
            }
            const servicePortList = [];
            const addPort = node => {
                const {ne_id, card} = node;
                const ports = Object.entries(node)
                    .filter(([k]) => k.startsWith("port"))
                    // eslint-disable-next-line no-unused-vars
                    .reduce((prev, [_, v]) => (Array.isArray(v) ? [...prev, ...v] : [...prev, v]), []);
                servicePortList.push({ne_id, card, ports});
            };
            let _type = nodeData.type;
            let typeClient = false;
            if (nodeData.type === "client" && nodeData?.och.length === 2) {
                db_key = nodeData?.och[0];
                _type = "och";
                typeClient = true;
            }

            const provision = await runAsync({db_key});
            if (provision?.apiResult === "fail") {
                setFindInfo({
                    provision: null
                });
                message.error(labelList.get_data_fail).then();
                return;
            }
            let node = provision.startNode;
            addPort(node);
            const path = [];
            let next2;
            while (true) {
                const next = node.next ?? node.next1;
                if (node.next2) {
                    next2 = node.next2;
                    if (node.ne_id !== next2.ne_id) path.push([node.ne_id, next2.ne_id].sort());
                }
                if (next) {
                    addPort(next);
                    if (node.ne_id !== next.ne_id) path.push([node.ne_id, next.ne_id].sort());
                    node = next;
                } else {
                    break;
                }
            }
            if (next2) {
                while (next2.type !== "OLP" && next2.next) {
                    addPort(next2);
                    if (next2.ne_id !== next2.next.ne_id) path.push([next2.ne_id, next2.next.ne_id].sort());
                    next2 = next2.next;
                }
            }
            const connection = path.filter(([a, z], idx) => path.findIndex(([pa, pz]) => pa === a && pz === z) === idx);
            const _servicePortList = servicePortList.map(item => {
                if (item.card?.startsWith("WSS")) {
                    const port = item.card.replace("WSS", "PORT");
                    item.ports.push(`${port}-PAOUT`);
                    item.ports.push(`${port}-BAIN`);
                }
                return item;
            });
            setFindInfo({
                type: _type,
                typeClient,
                key: db_key,
                name: nodeData.name,
                nodeData,
                provision,
                connection,
                servicePortList: _servicePortList,
                action
            });
        } catch (e) {
            // console.log(e);
        }
    };

    return (
        <div style={{display: "flex", flexDirection: "column"}}>
            <div style={{height: 25}}>
                <DebounceButton
                    containerType="ToolButton"
                    enabledIcon={addObjectIcon}
                    onFocusIcon={addObjectFocusIcon}
                    disabledIcon={addObjectDisabledIcon}
                    title={labelList.add}
                    disabled={
                        selectedKeys?.length > 1 ||
                        selectOMS === null ||
                        typeof selectOMS !== "string" ||
                        userRight.disabled ||
                        !(dataConfigs5.includes(selectOMS) || selectOMS?.indexOf("_") > -1)
                    }
                    onClick={() => {
                        const _selectOMS = selectOMS.split("_");
                        if (dataConfigs5.includes(_selectOMS[0])) {
                            addProvision();
                        }
                    }}
                />
                <DebounceButton
                    containerType="ToolButton"
                    title={labelList.edit}
                    enabledIcon={editServiceEnabledIcon}
                    disabledIcon={editServiceDisabledIcon}
                    onFocusIcon={editServiceFocusIcon}
                    disabled={
                        selectedKeys?.length > 1 ||
                        selectOMS === null ||
                        typeof selectOMS === "string" ||
                        userRight.disabled
                    }
                    onClick={async () => {
                        openDBModalEdit({
                            type: "nms:provision",
                            keys: {DBKey: selectOMS.id},
                            success: () => {
                                loadOMSTree().then();
                            }
                        });
                    }}
                    style={{marginLeft: 8}}
                />
                <DebounceButton
                    containerType="ToolButton"
                    title={gLabelList.del}
                    enabledIcon={deleteEnabledIcon}
                    disabledIcon={deleteDisabledIcon}
                    onFocusIcon={deleteFocusIcon}
                    disabled={selectOMS === null || typeof selectOMS === "string" || userRight.disabled}
                    onClick={async event => {
                        const modal = smallModal({
                            content: gLabelList.delete_confirm_msg,
                            // eslint-disable-next-line no-unused-vars
                            onOk: _ => {
                                modal.update({okButtonProps: {loading: true}});
                                deleteService(event).then(r => {
                                    if (!r) {
                                        modal.update({okButtonProps: {loading: false}});
                                    } else {
                                        modal.destroy();
                                    }
                                });
                            }
                        });
                    }}
                    style={{marginLeft: 8}}
                />
            </div>
            <div style={{overflow: "auto", flex: 1, marginLeft: -1, paddingTop: 16}}>
                <Tree
                    showLine
                    multiple
                    blockNode
                    selectedKeys={selectedKeys}
                    expandedKeys={expandedKeys}
                    onExpand={onExpand}
                    autoExpandParent={autoExpandParent}
                    onSelect={async (value, event) => {
                        const key = event?.node?.key;
                        if (!event.selected) {
                            setSelectOMS(null);
                            setSelectedKeys([]);
                            setFindInfo({type: "UN_SELECTED"});
                            return;
                        }

                        if (event.node.type === "ne_id") {
                            setSelectedKeys([key]);
                            setSelectOMS(key);
                            setFindInfo({
                                type: "NODE_NE",
                                id: key.split("_")[1]
                            });
                            return;
                        }
                        if (!event.node.data) {
                            setSelectedKeys([key]);
                            setSelectOMS(key);
                            setFindInfo({type: "UN_SELECTED"});
                            return;
                        }
                        if (event.nativeEvent.ctrlKey) {
                            if (selectedKeys.length === 0) {
                                setSelectOMS(event.node.data);
                            }
                            setSelectedKeys([...selectedKeys, key]);
                            setSelectedData([...selectedData, event.node.data]);
                        } else {
                            setSelectedKeys([key]);
                            setSelectOMS(event.node.data);
                            setSelectedData([event.node.data]);
                            await selectService(key.split("&")[1], event.node.data);
                        }
                    }}
                    treeData={treeData}
                />
            </div>
        </div>
    );
});

export default ServiceTree;

const updateKeys = (data, parentKey = "") => {
    data.forEach(item => {
        const currentKey = parentKey ? `${parentKey}&${item.key}` : item.key;
        item.key = currentKey;
        if (item.children) {
            updateKeys(item.children, currentKey);
        }
    });
};
