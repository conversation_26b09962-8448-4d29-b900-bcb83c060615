import asyncio
import itertools
import logging
import os
import traceback
from collections import defaultdict
import time
from typing import Dict, Any, Tuple, Optional, List, Union, Literal

from pyasn1.type import univ
from pysnmp.error import PySnmpError
from pysnmp.hlapi.v3arch.asyncio import *
from pysnmp.smi import builder, view, compiler, exval, rfc1902
from pysnmp.smi.error import SmiError, NoSuchObjectError, NoSuchInstanceError, MibNotFoundError

# from pysnmp import debug
# debug.set_logger(debug.Debug('all'))

LOG = logging.getLogger(__name__)


SNMP_TABLE_MAP = {
    "M6200": {
        "pluggableTable": {
            "oid": "*******.4.1.52642.1.10.15.2.1",
            "module": "ST-6200-MIB",
            "index": ["shelfId", "slotNo", "subSlotNo", "portNo", "subPortNo"],
            3: "plVendorSN",
            4: "plVendorPN",
            12: "plWaveLength",
            15: "plPresenceState",
            17: "plDateCode"
        },
        "otu10PortTable": {
            "oid": "*******.4.1.52642.1.21.4.1.1",
            "module": "ST-6200-MIB",
            "index": ["shelfId", "slotNo", "subSlotNo", "portNo", "subPortNo"],
            1: "otu10PortlaserShoutdownControl",
            2: "otu10PortlaserState",
            3: "otu10PortSourceSlect",
            4: "otu10PortRxPowHighThd",
            5: "otu10PortRxPowLowThd",
            6: "otu10PortTxPowHighThd",
            7: "otu10PortTxPowLowThd"
        },
        "otu10ConfigTable": {
            "oid": "*******.4.1.52642.1.21.4.2.1",
            "module": "ST-6200-MIB",
            "index": ["shelfId", "slotNo", "subSlotNo"],
            1: "otu10WorkMode",
            2: "otu10SwitchMode",
            3: "otu10SwitchState",
            4: "otu10SwitchHoldoffTime",
            5: "otu10RevertiveOfSwitchMode",
            7: "otu10AutoBackMode"
        },
        "oaPortTable": {
            "oid": "*******.4.1.52642.1.2*******",
            "module": "ST-6200-MIB",
            "index": ["shelfId", "slotNo", "subSlotNo", "portNo", "subPortNo"],
            1: "oaPortType",
            2: "oaPortOpticalPower",
            3: "oaPortAlmThsop"
        },
        "cardInventoryTable": {
            "oid": "*******.4.1.52642.1.10.15.1.1",
            "module": "ST-COMMON-MIB",
            "index": ["shelfId", "slotNo", "subSlotNo"],
            2: "sn",
            3: "pn",
            4: "hardware_version",
            6: "software_version",
            10: "temperature"
        },
        #  One byte for each led, define the index as below:
        #  fault,run,critical,major,minor,active,reserve,reserve,port1~port24 0: unknown, 1: off  2: green slow blink 3: green fast blink 4: green always 5: red slow blink 6: red fast blink 7: red always
        "cardLedTable": {
            "oid": "*******.4.1.52642.1.10.15.4.1",
            "module": "ST-COMMON-MIB",
            "index": ["shelfId", "slotNo", "subSlotNo"],
            1: "cardLedStatus",
        },
    }
}

SNMP_SET_INDICES = {}
for device_type, tables in SNMP_TABLE_MAP.items():
    for table_name, table_config in tables.items():
        module_name = table_config.get("module")
        index_list = table_config.get("index")
        if module_name and index_list:
            for key, mib_object_name in table_config.items():
                if isinstance(key, int) and isinstance(mib_object_name, str):
                    if mib_object_name not in SNMP_SET_INDICES:
                        SNMP_SET_INDICES[mib_object_name] = {
                            "module": module_name,
                            "index": index_list
                        }

# --- Helper function to convert ASN.1 value to Python type ---
def convert_asn1_value(value):
    """Converts pyasn1 value types to basic Python types."""
    # Check for specific SNMP exception/null types first
    if exval.noSuchObject.isSameTypeWith(value) or \
       exval.noSuchInstance.isSameTypeWith(value) or \
       exval.endOfMibView.isSameTypeWith(value) or \
       isinstance(value, univ.Null):
        return None # Represent SNMP null/noSuch* as Python None

    if 'IpAddress' in value.__class__.__name__:
        return value.prettyPrint()

    elif isinstance(value, univ.Integer):
        # This includes Integer32, potentially enums if not resolved
        # return int(value)
        return value.prettyPrint()

    elif isinstance(value, univ.OctetString):
        if 'MacAddress' in value.__class__.__name__:
             return value.prettyPrint()

        # Otherwise, assume it might be text
        try:
            # Try decoding common text encodings, remove trailing nulls
            decoded_value = value.asOctets().decode('utf-8', errors='strict').rstrip('\x00')
            # Return if it looks like printable text
            if decoded_value.isprintable() or decoded_value == '': # Allow empty strings
                 return decoded_value
            else:
                 # If not printable UTF-8, return hex representation for clarity
                 return value.prettyPrint() # e.g., 0x....
        except UnicodeDecodeError:
            try:
                # Try Latin-1 as another common encoding
                decoded_value = value.asOctets().decode('latin-1').rstrip('\x00')
                if decoded_value.isprintable() or decoded_value == '':
                    return decoded_value
                else:
                    return value.prettyPrint() # Fallback to hex
            except UnicodeDecodeError:
                 # If it's not decodable as common text, return hex representation
                 return value.prettyPrint()
        except Exception as e:
             # General fallback for OctetString decoding errors
             LOG.warning(f"Error decoding OctetString: {e}, returning hex.")
             return value.prettyPrint()

    # Handle standard ASN.1 ObjectIdentifier
    elif isinstance(value, univ.ObjectIdentifier):
        # Return OID in dotted decimal string format
        return str(value)
    # Handle SNMP specific numeric types (Counter, Gauge, TimeTicks)
    # elif isinstance(value, (rfc1902.Counter32, rfc1902.Counter64, rfc1902.Gauge32, rfc1902.Unsigned32)):
    #     return int(value)
    # elif isinstance(value, rfc1902.TimeTicks):
    #     # TimeTicks are usually represented in hundredths of a second
    #     return int(value) # Return as integer ticks

    else:
        try:
            return value.prettyPrint()
        except Exception:
             # Absolute last resort if prettyPrint fails
             return str(value)


class SNMPClient:
    """SNMP client for communicating with network devices"""

    def __init__(self, host: str, community_read: str = "public",
                 community_write: str = "private", port: int = 161,
                 timeout: int = 5, retries: int = 3, device_type: str = "M6200"):
        """
        Initialize SNMP client

        Args:
            host: Target device IP address
            community_read: SNMP read community string
            community_write: SNMP write community string
            port: SNMP port (default 161)
            timeout: Timeout in seconds
            retries: Number of retries for failed requests
            device_type: Device type ('FMT' or 'D6000')
        """
        self.host = host
        self.port = port
        self.community_read = community_read
        self.community_write = community_write
        self.version = 1  # SNMP version (0 for SNMPv1, 1 for SNMPv2c)
        self.timeout = timeout
        self.retries = retries
        self.device_type = device_type

        self.mib_path = os.path.join(os.path.dirname(__file__), "mibs")
        self.mib_builder = builder.MibBuilder()
        self.mib_builder.add_mib_sources(
            builder.DirMibSource(self.mib_path)
        )
        # compiler.add_mib_compiler(self.mib_builder, source="file://"+self.mib_path)
        self.mib_view_controller = view.MibViewController(self.mib_builder)
        self._load_mibs(['SNMPv2-MIB', 'SNMPv2-TC', 'ST-COMMON-MIB', 'ST-6200-MIB'])
        self.result = {"boardInfos": []}
        self.nmu = {}

    def _load_mibs(self, mib_names: List[str]) -> None:
        """
        Preload MIB modules

        Args:
            mib_names: List of MIB module names to load
        """
        for mib_name in mib_names:
            try:
                self.mib_builder.load_modules(mib_name)
                LOG.info(f"Loaded MIB module: {mib_name}")
            except Exception as e:
                LOG.error(f"Failed to load MIB module {mib_name}: {str(e)}")

    def _resolve_oid(self, mib_name, *indices):
        """Resolves a MIB object name and indices into an ObjectIdentity."""
            # Construct ObjectIdentity with MIB name and indices
            # The *indices ensures they are passed as separate args after the name
            # identity = ObjectIdentity(mib_name, *indices).resolve_with_mib(self.mib_view_controller)
        try:
            if "::" in mib_name:
                mib_module, mib_object = mib_name.split("::")
                identity = ObjectIdentity(mib_module, mib_object, *indices).resolve_with_mib(self.mib_view_controller)
            else:
                oid_parts = [mib_name] + [str(i) for i in indices]
                full_oid_str = '.'.join(oid_parts)
                identity = ObjectIdentity(full_oid_str)
            return identity
        except SmiError as e:
            # Log specific MIB resolution errors
            if isinstance(e, NoSuchObjectError):
                LOG.error(f"MIB Error: Object '{mib_name}' not found in loaded MIBs.")
            elif isinstance(e, NoSuchInstanceError):
                # This error during resolution usually means incorrect indices *types* based on MIB
                LOG.error(
                    f"MIB Error: Instance resolution failed for '{mib_name}' with indices {indices}. Check index types/constraints in MIB. Error: {e}")
            elif isinstance(e, MibNotFoundError):
                # Extract MIB name if possible
                mib_not_found = getattr(e, 'mibName', mib_name.split("::")[0] if "::" in mib_name else "Unknown")
                LOG.error(
                    f"MIB Error: MIB module '{mib_not_found}' not found or failed to load. Check MIB_DIRS and MIB files.")
            else:
                LOG.error(f"MIB Error resolving {mib_name}{indices}: {e}")
            return None
        except Exception as e:
            LOG.error(f"Unexpected error resolving {mib_name}{indices}: {e}")
            return None

    async def get(self, mib_vars):
        """
        Performs SNMP GET using MIB symbolic names and optional indices.

        Args:
            mib_vars (list): A list of tuples, where each tuple is
                             (mib_name_str, index_tuple) or just mib_name_str
                             e.g., [('SNMPv2-MIB::sysDescr', '0'), ('ST-6200-MIB::olpSwitchMode', (1, 2, 0))]

        Returns:
            dict: A dictionary mapping the resolved OID string to its value object (pyasn1 type),
                  or None for OIDs that failed. Returns None for the entire operation on major errors.
        """
        object_types = []
        oid_map = {}  # Map resolved OID back to original request key
        start_time = time.time()
        for item in mib_vars:
            indices = ()
            if isinstance(item, tuple):
                mib_name = item[0]
                # Ensure indices are passed correctly, handle single index case
                indices = item[1] if isinstance(item[1], tuple) else (item[1],)
            else:
                mib_name = item  # Assuming scalar object, index is typically '0'
                # indices = ('0',)  # Default index for scalar objects
            identity = self._resolve_oid(mib_name, *indices)
            # if identity:
            object_types.append(ObjectType(identity))
            if "::" in mib_name:
                oid_map[str(identity)] = item  # Map resolved OID str to original request item
            else:
                oid_map[mib_name] = item
            # else:
            #     LOG.warning(f"Skipping GET for unresolved MIB variable: {item}")
                # Optionally add a placeholder for failed resolutions if needed downstream
                # results[item] = None

        if not object_types:
            LOG.error(f"SNMP GET: No valid MIB variables to query for {self.host}.")
            return None  # Or {} ?

        results = {}  # Store results keyed by original request item
        try:
            error_indication, error_status, error_index, var_binds = await get_cmd(
                    SnmpEngine(),
                    CommunityData(self.community_read, mpModel=self.version),
                    await UdpTransportTarget.create((self.host, self.port), timeout=self.timeout, retries=self.retries),
                    ContextData(),
                    *object_types
                )

            if error_indication:
                LOG.error(f"SNMP GET failed for {self.host}: {error_indication}")
                return None
            elif error_status:
                # Map error back to the MIB variable name if possible
                failed_req_item = None
                if error_index and error_index <= len(object_types):
                    failed_oid_str = str(object_types[error_index - 1][0])
                    failed_req_item = oid_map.get(failed_oid_str, f"Unknown (Index {error_index})")
                else:
                    failed_req_item = '?'
                LOG.error(f"SNMP GET error for {self.host}: {error_status.prettyPrint()} at {failed_req_item}")
                # Mark the specific failed item as None in results
                if failed_req_item and failed_req_item != '?':
                    results[failed_req_item] = None
                # Decide whether to return partial results or None for all
                # return None # Fail entire operation on any errorStatus
            # Process successful results and potentially partial errors if not returning None above
            for var_bind in var_binds:
                oid_str = str(var_bind[0])
                value = var_bind[1]
                # Find the original request item using the resolved OID map
                req_item = oid_map.get(oid_str)
                if req_item:
                    # Check for noSuchObject/noSuchInstance specifically
                    if exval.noSuchObject.isSameTypeWith(value) or exval.noSuchInstance.isSameTypeWith(value):
                        LOG.warning(f"SNMP GET for {self.host}: Variable {req_item} reported as {value.__class__.__name__}")
                        results[req_item] = None  # Treat as not found
                    else:
                        results[req_item] = value  # Store the pyasn1 value object
                else:
                    LOG.warning(f"Received SNMP GET response for unknown OID {oid_str} from {self.host}")
        except StopIteration:
            LOG.error(f"SNMP GET: No response received from {self.host} for variables: {mib_vars}")
            return None  # Indicate timeout/no response
        except PySnmpError as e:
            LOG.error(f"PySNMP GET Error for {self.host}: {e}")
            return None
        except Exception as e:
            LOG.error(f"SNMP GET exception for {self.host}, Variables {mib_vars}: {e}")
            LOG.error(traceback.format_exc())
            return None
        finally:
            # self.engine.close_dispatcher()
            pass

        # Ensure all originally requested items have an entry (even if None)
        final_results = {}
        for item in mib_vars:
            final_results[item] = results.get(item)  # Get value or None if resolution/fetch failed

        return final_results

    async def set(self, mib_name, indices, value):
        """
        Performs SNMP SET operation using MIB symbolic name and indices.

        Args:
            mib_name (str): The MIB object name (e.g., 'ST-6200-MIB::olpSwitchMode').
            indices (tuple): Tuple of index values (e.g., (1, 2, 0)).
            value: The value to set (Python type).

        Returns:
            bool: True on success, False on failure.
        """
        identity = self._resolve_oid(mib_name, *indices)
        if "::" in mib_name:
            if not identity:
                LOG.error(f"SNMP SET failed for {self.host}: Could not resolve {mib_name}{indices}")
                return False
            mib_var = identity.get_mib_node()
            asn1_type = mib_var.syntax
        # if not asn1_type:
        #     LOG.error(f"SNMP SET failed for {self.host}: Could not determine ASN.1 syntax for {mib_name}")
        #     return False
        else:
            asn1_type = None

        try:
            # Create the value object with the correct ASN.1 type
            # Handle potential type mismatches (e.g., string for Integer)
            asn1_type_name = asn1_type.__class__.__name__
            LOG.debug(f"ASN.1 type for {mib_name}{indices}: {asn1_type_name}")
            if isinstance(asn1_type, univ.Integer) or 'Integer' in asn1_type_name:
                value_obj = univ.Integer(int(value))
            elif isinstance(asn1_type, univ.OctetString) or 'String' in asn1_type_name:
                if value.startswith('hex_'):
                    hex_str = value[4:].strip()
                    if all(c in '0123456789abcdefABCDEF' for c in hex_str):
                        value_obj = univ.OctetString(hexValue=hex_str)
                    else:
                        raise ValueError(f"Invalid hex string: {hex_str}")
                else:
                    value_obj = univ.OctetString(value)
            elif isinstance(asn1_type, univ.ObjectIdentifier) or 'ObjectIdentifier' in asn1_type_name:
                if isinstance(value, str) and all(part.isdigit() for part in value.split('.')):
                    value_obj = univ.ObjectIdentifier(value)
                elif isinstance(value, (list, tuple)) and all(isinstance(x, int) for x in value):
                    oid_str = '.'.join(str(x) for x in value)
                    value_obj = univ.ObjectIdentifier(oid_str)
                else:
                    raise ValueError(
                        f"Invalid OID format: {value}. Expected dotted decimal notation or numeric sequence.")
            else:
                # 对于其他ASN.1类型，使用通用方法
                LOG.debug(f"Using generic approach for type: {asn1_type_name}")
                try:
                    # 尝试根据Python类型映射到适当的ASN.1类型
                    if isinstance(value, int):
                        value_obj = univ.Integer(value)
                    elif isinstance(value, str):
                        if value.startswith('hex_'):
                            hex_str = value[4:].strip()
                            if all(c in '0123456789abcdefABCDEF' for c in hex_str):
                                value_obj = univ.OctetString(hexValue=hex_str)
                            else:
                                raise ValueError(f"Invalid hex string: {hex_str}")
                        else:
                            value_obj = univ.OctetString(value)
                    elif isinstance(value, bool):
                        value_obj = univ.Integer(int(value))
                    else:
                        # 最后的尝试，可能会失败
                        LOG.warning(f"Using fallback type conversion for {type(value)} to {asn1_type_name}")
                        value_obj = univ.OctetString(str(value))
                except Exception as e:
                    LOG.error(f"Failed to convert value {value} to appropriate ASN.1 type: {e}")
                    raise ValueError(f"Cannot convert {value} to appropriate ASN.1 type: {e}")

            LOG.debug(f"Created value object: {value_obj}, type: {type(value_obj)}")
            var_bind = ObjectType(identity, value_obj)

            iterator = set_cmd(
                SnmpEngine(),
                CommunityData(self.community_read, mpModel=self.version),
                await UdpTransportTarget.create((self.host, self.port), timeout=self.timeout, retries=self.retries),
                ContextData(),
                var_bind
            )
            error_indication, error_status, error_index, var_binds = await iterator

            if error_indication:
                LOG.error(f"SNMP SET failed for {self.host}, {mib_name}{indices}: {error_indication}")
                return False
            elif error_status:
                failed_mib_var = var_binds[int(error_index) - 1][0].get_mib_symbol() if error_index and var_binds else (
                mib_name, indices)
                LOG.error(
                    f"SNMP SET error for {self.host}, {mib_name}{indices}: {error_status.prettyPrint()} at {failed_mib_var}")
                return False
            else:
                LOG.info(f"SNMP SET successful for {self.host}, {mib_name}{indices}, Value: {value}")
                return True

        except StopIteration:
            LOG.error(f"SNMP SET: No response received from {self.host} for {mib_name}{indices}")
            return False
        except (ValueError, TypeError) as e:
            LOG.error(
                f"SNMP SET type error for {self.host}, {mib_name}{indices}: Value '{value}' likely incompatible with MIB type {asn1_type.__class__.__name__}. Error: {e}")
            return False
        except PySnmpError as e:
            LOG.error(f"PySNMP SET Error for {self.host}, {mib_name}{indices}: {e}")
            return False
        except Exception as e:
            LOG.error(f"SNMP SET exception for {self.host}, {mib_name}{indices}: {e}")
            LOG.error(traceback.format_exc())
            return False
        finally:
            pass

    async def walk(self, start_mib_name, indices, non_repeaters=0, max_repetitions=25):
        """
        Performs SNMP WALK using a MIB symbolic name (usually a table).

        Args:
            start_mib_name (str): The MIB object/table name to start walking from
                                  (e.g., 'ST-6200-MIB::olpCardTable').
            indices (tuple): Tuple of index values (e.g., (1, 2, 0)).
        Yields:
            tuple: (OID object, value object) for each variable found in the walk.
                   Yields nothing if an error occurs.
        """
        # identity = self._resolve_oid(start_mib_name, *indices)  # Resolve base name (index '0' is implicit for walk start)
        # if not identity:
        #     LOG.error(f"SNMP WALK failed for {self.host}: Could not resolve starting MIB name {start_mib_name}")
        #     return  # Stop yielding
        start_time = time.time()
        mib_module, mib_object = start_mib_name.split("::")
        if not(mib_module and mib_object):
            LOG.error(f"SNMP WALK failed for {self.host}: Could not resolve starting MIB name {start_mib_name}")
            return  # Stop yielding
        try:
            engine = SnmpEngine()
            async for error_indication, error_status, error_index, var_binds in bulk_walk_cmd(
                    engine,
                    CommunityData(self.community_read, mpModel=self.version),
                    await UdpTransportTarget.create((self.host, self.port), timeout=self.timeout, retries=self.retries),
                    ContextData(),
                    non_repeaters, max_repetitions,
                    ObjectType(ObjectIdentity(mib_module, mib_object, *indices)).add_asn1_mib_source("file://"+self.mib_path),  # Start walk from the resolved base OID
                    lexicographicMode=False,
                    # ignoreNonIncreasingOid=True
            ):
                if error_indication:
                    LOG.error(f"SNMP WALK failed for {self.host}, starting {start_mib_name}: {error_indication}")
                    return  # Stop yielding
                elif error_status:
                    failed_mib_var = var_binds[int(error_index) - 1][
                        0].get_mib_symbol() if error_index and var_binds else '?'
                    LOG.error(
                        f"SNMP WALK error for {self.host}, starting {start_mib_name}: {error_status.prettyPrint()} at {failed_mib_var}")
                    return  # Stop yielding
                else:
                    for var_bind in var_binds:
                        current_identity = var_bind[0]
                        # Check for endOfMibView or other indicators if needed
                        value = var_bind[1]
                        if exval.endOfMibView.isSameTypeWith(value):
                            LOG.debug(f"SNMP WALK for {self.host} hit endOfMibView for subtree {start_mib_name}")
                            return  # Stop yielding

                        yield var_bind  # Yield (OID object, value object)

        except StopAsyncIteration:
            LOG.error(f"SNMP WALK: No response received from {self.host} for {start_mib_name}")
        except PySnmpError as e:
            LOG.error(f"PySNMP WALK Error for {self.host}, {start_mib_name}: {e}")
        except Exception as e:
            LOG.error(f"SNMP WALK exception for {self.host}, {start_mib_name}: {e}")
            LOG.error(traceback.format_exc())
        finally:
            if 'engine' in locals():
                engine.close_dispatcher()
            LOG.info("bulk walk time:" + str(time.time() - start_time))

    def get_slot_ports_data(self,
                       table_name: str,
                       slot_index: int,
                       other_fixed_indices: Dict[str, int],  # e.g., {"shelfId": 1, "subSlotNo": 0}
                       # This config MUST contain exactly ONE entry.
                       # The key is the MIB index name (e.g., "portNo") whose values will be the
                       # keys of the returned dictionary. The value is the range for this index.
                       iterating_indices_config: Dict[str, list]
                       ) -> Dict[int, Dict[str, Any]]:  # MODIFIED return type
        """
        Fetches and formats table data. Keys of the returned dictionary are from iterating_indices_config.
        'subPortNo' is fixed at 0 for tables that include it as an index.

        Args:
            table_name (str): Key for the table in SNMP_TABLE_MAP.
            slot_index (int): The fixed value for the 'slotNo' (or similar) index.
            other_fixed_indices (Dict[str, int]): Fixed values for other indices (e.g., 'shelfId').
            iterating_indices_config (Dict[str, list]): Must contain exactly one entry.
                e.g., {"portNo": range(6, 16)}. Values from this range become output keys.

        Returns:
            Dict[int, Dict[str, Any]]: Formatted table data.
                e.g., {6: {"col1": val1, "col2": val2}, 7: {...}}
        """
        start_time = time.time()
        final_output_dict = defaultdict(dict)
        oids_to_fetch = []
        # oid_to_info_map: maps OID string to (key_for_output_dict, column_name_str)
        oid_to_info_map = {}

        if not (iterating_indices_config and len(iterating_indices_config) == 1):
            LOG.error("iterating_indices_config must contain exactly one entry (e.g., {'portNo': range(1,5)}).")
            return {}

        # Name of the index whose values will become keys in the output dict (e.g., "portNo")
        key_generating_index_name = list(iterating_indices_config.keys())[0]
        # Range of values for this key-generating index (e.g., range(6, 16))
        key_generating_index_list = iterating_indices_config[key_generating_index_name]

        if self.device_type not in SNMP_TABLE_MAP or \
                table_name not in SNMP_TABLE_MAP[self.device_type]:
            LOG.error(f"Table configuration for '{table_name}' not found for device type '{self.device_type}'.")
            return {}

        table_config = SNMP_TABLE_MAP[self.device_type][table_name]
        table_entry_oid = table_config.get("oid")
        all_index_names_from_map = table_config.get("index", [])
        column_details = {k: v for k, v in table_config.items() if isinstance(k, int)}

        if not all([table_entry_oid, all_index_names_from_map, column_details]):
            LOG.error(f"Incomplete table configuration for '{table_name}'. Missing oid, index list, or columns.")
            return {}

        # --- Construct the set of all fixed indices for OID building ---
        current_all_fixed_indices = {**other_fixed_indices}
        current_all_fixed_indices["slotNo"] = slot_index
        current_all_fixed_indices["subPortNo"] = 0

        for iter_val_for_output_key in key_generating_index_list:
            # iter_val_for_output_key (e.g., a port number like 6, 7) becomes a key in final_output_dict.
            # Combine all fixed indices with the current value of the key-generating iterated index
            instance_full_indices = {**current_all_fixed_indices}
            instance_full_indices[key_generating_index_name] = iter_val_for_output_key
            # Construct the OID index part based on the order in SNMP_TABLE_MAP
            index_oid_parts = []
            valid_indices_for_oid = True
            for idx_name_in_order in all_index_names_from_map:
                if idx_name_in_order in instance_full_indices:
                    index_oid_parts.append(str(instance_full_indices[idx_name_in_order]))
                else:
                    LOG.error(
                        f"Critical: MIB index '{idx_name_in_order}' (defined in SNMP_TABLE_MAP for '{table_name}') "
                        f"was not found in constructed instance_full_indices: {instance_full_indices}. "
                        f"Cannot build OID for output key {iter_val_for_output_key}. Skipping this key.")
                    valid_indices_for_oid = False
                    break
            if not valid_indices_for_oid:
                continue  # Skip to the next value in key_generating_index_list
            index_suffix_str = ".".join(index_oid_parts)
            # For each MIB column defined for this table, create the full OID
            for col_suffix_num, col_name_str in column_details.items():
                columnar_object_oid = f"{table_entry_oid}.{col_suffix_num}"  # e.g., ...pluggableEntry.3
                instance_oid_str = f"{columnar_object_oid}.{index_suffix_str}"  # e.g., ...pluggableEntry.*******.6.0

                oids_to_fetch.append(instance_oid_str)
                # Map this OID back to the output key (iter_val_for_output_key) and MIB column name
                oid_to_info_map[instance_oid_str] = (iter_val_for_output_key, col_name_str)

        if not oids_to_fetch:
            LOG.info(f"No OIDs constructed for get_slot_ports_data on '{table_name}' with given parameters. "
                     f"Fixed: {current_all_fixed_indices}, Iterating '{key_generating_index_name}': {key_generating_index_list}")
            return {}

        LOG.debug(
            f"Fetching {len(oids_to_fetch)} OIDs for table '{table_name}'. Example OID: {oids_to_fetch[0] if oids_to_fetch else 'N/A'}")

        raw_results_map = self.process('get', oids_to_fetch)  # Pass the list of OID strings

        if raw_results_map:
            for oid_str_request, val_asn1 in raw_results_map.items():  # oid_str_request is the key from oids_to_fetch
                if oid_str_request in oid_to_info_map:
                    output_key, col_name = oid_to_info_map[oid_str_request]
                    if val_asn1 is not None:  # Check for SNMP errors like noSuchInstance
                        converted_value = convert_asn1_value(val_asn1)
                        if converted_value is not None:  # Ensure conversion didn't result in None (e.g. for empty strings if desired)
                            final_output_dict[output_key][col_name] = converted_value
                        # else: # converted_value is None (e.g., empty string that became None)
                #                         # final_output_dict[output_key][col_name] = None # Or simply skip adding it
                #                     # else: # val_asn1 is None (SNMP noSuchInstance, noSuchObject, SNMP NULL)
                #                     # final_output_dict[output_key][col_name] = None # Or skip
                #                 # else: # Should not happen if oid_to_info_map was built correctly from oids_to_fetch
                # LOG.warning(f"Received result for OID '{oid_str_request}' not in oid_to_info_map.")

        duration = time.time() - start_time
        LOG.debug(
            f"get_slot_ports_data for '{table_name}' (slot: {slot_index}) took {duration:.4f} seconds. "
            f"Processed {len(oids_to_fetch)} OID requests, resulted in {len(final_output_dict)} primary keys in output.")
        return dict(final_output_dict)

    def get_slot_data(self,
                      table_name: str,
                      slot_list: list,
                      other_fixed_indices: Dict[str, int]  # e.g., {"shelfId": 1, "subSlotNo": 0}
                      ) -> Dict[int, Dict[str, Any]]:
        """
        Fetches and formats table data, iterating over a range of slot numbers.
        Keys of the returned dictionary are the slot numbers.
        'subPortNo' is fixed at 0 if it's an index for this table and not otherwise specified.

        Args:
            table_name (str): Key for the table in SNMP_TABLE_MAP.
            slot_list (list): The range of slot numbers to iterate over.
            other_fixed_indices (Dict[str, int]): Fixed values for other indices (e.g., 'shelfId', 'subSlotNo').

        Returns:
            Dict[int, Dict[str, Any]]: Formatted table data, keyed by slot number.
                e.g., {1: {"col1": val1, "col2": val2}, 2: {...}}
        """
        start_time = time.time()
        final_output_dict = defaultdict(dict)
        oids_to_fetch = []
        # oid_to_info_map: maps OID string to (slot_number_for_output_key, column_name_str)
        oid_to_info_map = {}

        if self.device_type not in SNMP_TABLE_MAP or \
                table_name not in SNMP_TABLE_MAP[self.device_type]:
            LOG.error(f"Table configuration for '{table_name}' not found for device type '{self.device_type}'.")
            return {}

        table_config = SNMP_TABLE_MAP[self.device_type][table_name]
        table_entry_oid = table_config.get("oid")
        all_index_names_from_map = table_config.get("index", [])
        column_details = {k: v for k, v in table_config.items() if isinstance(k, int)}

        if not all([table_entry_oid, all_index_names_from_map, column_details]):
            LOG.error(f"Incomplete table configuration for '{table_name}'. Missing oid, index list, or columns.")
            return {}

        # --- Handle 'subPortNo' if it's an index for this table ---
        # This logic is similar to get_slot_ports_data, ensuring subPortNo is 0 if present and not overridden
        current_fixed_indices_with_defaults = {**other_fixed_indices}

        for slot_val in slot_list:
            output_key_for_dict = slot_val

            # Combine all fixed indices with the current slot value
            instance_full_indices = {**current_fixed_indices_with_defaults}
            instance_full_indices["slotNo"] = slot_val

            # Construct the OID index part based on the order in SNMP_TABLE_MAP
            index_oid_parts = []
            valid_indices_for_oid = True
            for idx_name_in_order in all_index_names_from_map:
                if idx_name_in_order in instance_full_indices:
                    index_oid_parts.append(str(instance_full_indices[idx_name_in_order]))
                else:
                    LOG.error(
                        f"Critical: MIB index '{idx_name_in_order}' (defined in SNMP_TABLE_MAP for '{table_name}') "
                        f"was not found in constructed instance_full_indices: {instance_full_indices}. "
                        f"Cannot build OID for slot {slot_val}. Skipping this slot.")
                    valid_indices_for_oid = False
                    break
            if not valid_indices_for_oid:
                continue  # Skip to the next slot in slot_range

            index_suffix_str = ".".join(index_oid_parts)

            # For each MIB column defined for this table, create the full OID
            for col_suffix_num, col_name_str in column_details.items():
                columnar_object_oid = f"{table_entry_oid}.{col_suffix_num}"
                instance_oid_str = f"{columnar_object_oid}.{index_suffix_str}"

                oids_to_fetch.append(instance_oid_str)
                # Map this OID back to the output key (slot_val) and MIB column name
                oid_to_info_map[instance_oid_str] = (output_key_for_dict, col_name_str)

        if not oids_to_fetch:
            LOG.info(f"No OIDs constructed for get_slot_data on '{table_name}' with given parameters. "
                     f"Fixed: {current_fixed_indices_with_defaults}, Slot Range: {slot_list}")
            return {}

        LOG.debug(
            f"Fetching {len(oids_to_fetch)} OIDs for table '{table_name}' across slots {slot_list}. Example OID: {oids_to_fetch[0] if oids_to_fetch else 'N/A'}")

        raw_results_map = self.process('get', oids_to_fetch)

        if raw_results_map:
            for oid_str_request, val_asn1 in raw_results_map.items():
                if oid_str_request in oid_to_info_map:
                    output_key, col_name = oid_to_info_map[oid_str_request]
                    if val_asn1 is not None:
                        converted_value = convert_asn1_value(val_asn1)
                        if converted_value is not None:
                            final_output_dict[output_key][col_name] = converted_value
                        # else: # converted_value is None (e.g. for empty strings if desired)
                        # final_output_dict[output_key][col_name] = None # Or simply skip adding it
                    # else: # val_asn1 is None (SNMP noSuchInstance, noSuchObject, SNMP NULL)
                    # final_output_dict[output_key][col_name] = None # Or skip
                # else: # Should not happen if oid_to_info_map was built correctly
                # LOG.warning(f"Received result for OID '{oid_str_request}' not in oid_to_info_map.")

        duration = time.time() - start_time
        LOG.debug(
            f"get_slot_data for '{table_name}' (slots: {slot_list}) took {duration:.4f} seconds. "
            f"Processed {len(oids_to_fetch)} OID requests, resulted in {len(final_output_dict)} slot keys in output.")
        return dict(final_output_dict)

    def process(self, operation: Literal['get', 'set', 'walk'], *args, **kwargs) -> Any:
        """
        Synchronous wrapper to call async SNMP methods using asyncio.run().

        Args:
            operation: The name of the async method to call ('get', 'set', 'walk').
            *args: Positional arguments for the target async method.
            **kwargs: Keyword arguments for the target async method.

        Returns:
            The result of the async method call. For 'walk', returns a list of results.
            Returns None on error during execution.
        """
        try:
            if operation == 'get':
                return asyncio.run(self.get(*args, **kwargs))
            elif operation == 'set':
                return asyncio.run(self.set(*args, **kwargs))
            elif operation == 'walk':
                async def _collect_walk():
                    results = []
                    async for item in self.walk(*args, **kwargs):
                        results.append(item)
                    return results
                return asyncio.run(_collect_walk())
            else:
                LOG.error(f"Unsupported operation for process: {operation}")
                return None
        except RuntimeError as e:
            LOG.error(f"RuntimeError during asyncio.run() for operation '{operation}': {e}. Ensure no nested event loops.")
            try:
                loop = asyncio.get_running_loop()
                LOG.warning("Attempting to run task in existing loop (experimental).")
                if operation == 'get':
                    task = loop.create_task(self.get(*args, **kwargs))
                elif operation == 'set':
                    task = loop.create_task(self.set(*args, **kwargs))
                elif operation == 'walk':
                     async def _collect_walk_in_loop():
                        results = []
                        async for item in self.walk(*args, **kwargs):
                            results.append(item)
                        return results
                     task = loop.create_task(_collect_walk_in_loop())
                else:
                    return None # Should not happen
                LOG.error("Cannot reliably run in existing loop from synchronous context without blocking or threading.")
                return None

            except RuntimeError:
                 LOG.error(f"No running event loop found for operation '{operation}'.")
                 return None
            except Exception as e_inner:
                 LOG.error(f"Error trying to run in existing loop: {e_inner}")
                 return None

        except Exception as e:
            LOG.error(f"Error executing process for operation '{operation}': {e}")
            LOG.error(traceback.format_exc())
            return None

    def close(self):
        """Clean up resources"""
        pass  # Nothing to clean up for SNMP


if __name__ == "__main__":
    instance = SNMPClient("10.28.63.99", community_read="private", community_write="private")
    voa_mib_name = 'ST-6200-MIB::voaPluggableAttenuation'
    port_mode_mib_name = 'ST-COMMON-MIB::portMode'
    ntp_time_mib_name = 'ST-COMMON-MIB::sntpSyncedTime'
    voa_indices = (1, 8, 0, 3, 0)
    ntp_indices = (0,)
    # success = await instance.set(voa_mib_name, voa_indices, -1)
    # success = instance.process('set', voa_mib_name, voa_indices, 10)
    # success = instance.process('set', "SNMP-TARGET-MIB::snmpTargetSpinLock", (0,), 7)
    # origin_str = "ampcon111111"
    # success = instance.process('set', "SNMP-TARGET-MIB::snmpTargetAddrRowStatus", (origin_str,), 4)
    # success = instance.process('set', "SNMP-TARGET-MIB::snmpTargetAddrTDomain", (origin_str,), "*******.6.1.1")
    # success = instance.process('set', "SNMP-TARGET-MIB::snmpTargetAddrTAddress", (origin_str,), "hex_0403030300a2")
    # success = instance.process('set', "SNMP-TARGET-MIB::snmpTargetAddrTagList", (origin_str,), "internal1")
    # success = instance.process('set', "SNMP-TARGET-MIB::snmpTargetAddrParams", (origin_str,), "internal1")
    # success = instance.process('set', "SNMP-TARGET-MIB::snmpTargetAddrRowStatus", (origin_str,), 1)
    # success = instance.process('set', "*******.4.1.52642.********.1.8", (1, 3, 0, 1, 0), "test111")
    get_args = [[(voa_mib_name, voa_indices), (port_mode_mib_name, voa_indices), (ntp_time_mib_name, ntp_indices)]]
    result = instance.process('get', *get_args) # Pass args list as positional arguments

    rec_data = {}
    if result:
        voa_val_asn1 = result.get((voa_mib_name, voa_indices))
        port_val_asn1 = result.get((port_mode_mib_name, voa_indices))
        ntp_val_asn1 = result.get((ntp_time_mib_name, ntp_indices))

        if voa_val_asn1 is not None:
            rec_data['voaPluggableAttenuation'] = convert_asn1_value(voa_val_asn1)
        if port_val_asn1 is not None:
            rec_data['portMode'] = convert_asn1_value(port_val_asn1)
        if ntp_val_asn1 is not None:
            rec_data['sntpSyncedTime'] = convert_asn1_value(ntp_val_asn1)
    print("GET Result:", rec_data)

    card_table_mib_name = 'ST-COMMON-MIB::slotTable'
    port_table_mib_name = 'ST-COMMON-MIB::portTable'
    mgmt_table_mib_name = 'ST-COMMON-MIB::mgmtEthIfTable'
    system_mib_name = 'ST-COMMON-MIB::sysInfoScalars'
    cards_data = defaultdict(dict)  # {slotIndex: {mib_object_name: value}}
    ports_data = defaultdict(dict)
    mgmt_data = defaultdict(dict)
    system_data = defaultdict(str)
    walk_args_card = (card_table_mib_name, ())
    card_walk_results = instance.process('walk', *walk_args_card)
    if card_walk_results is not None:
        for oid_obj, value_obj in card_walk_results:
            try:
                mib_module, mib_object, indices = oid_obj.get_mib_symbol()
                if len(indices) == 3:
                    shelf, slot, subslot = map(int, indices)
                    slot_key = slot
                    if slot_key not in cards_data:
                        cards_data[slot_key]['slotIndex'] = slot_key
                        cards_data[slot_key]['shelfId'] = shelf
                        cards_data[slot_key]['subSlotNo'] = subslot

                    value = convert_asn1_value(value_obj)
                    cards_data[slot_key][mib_object] = value
            except Exception as e:
                 LOG.warning(f"Error processing card walk result {oid_obj}: {e}")
    else:
        LOG.error(f"Walk failed for {card_table_mib_name}")

    walk_args_port = (port_table_mib_name, ())
    port_walk_results = instance.process('walk', *walk_args_port)
    if port_walk_results is not None:
        for oid_obj, value_obj in port_walk_results:
            try:
                mib_module, mib_object, indices = oid_obj.get_mib_symbol()
                if len(indices) == 5:
                    shelf, slot, subslot, port, subport = map(int, indices)
                    slot_key = slot
                    port_key = port
                    if slot_key not in ports_data:
                        ports_data[slot_key] = defaultdict(dict)
                    if port_key not in ports_data[slot_key]:
                        ports_data[slot_key][port_key] = {}
                    value = convert_asn1_value(value_obj)
                    ports_data[slot_key][port_key][mib_object] = value
            except Exception as e:
                LOG.warning(f"Error processing port walk result {oid_obj}: {e}")
    else:
        LOG.error(f"Walk failed for {port_table_mib_name}")

    walk_args_mgmt = (mgmt_table_mib_name, ())
    mgmt_walk_results = instance.process('walk', *walk_args_mgmt)
    if mgmt_walk_results is not None:
        for oid_obj, value_obj in mgmt_walk_results:
            try:
                mib_module, mib_object, indices = oid_obj.get_mib_symbol()
                if len(indices) == 1:
                    port, = map(int, indices)
                    port_key = port
                    value = convert_asn1_value(value_obj)
                    mgmt_data[port_key][mib_object] = value
            except Exception as e:
                 LOG.warning(f"Error processing mgmt walk result {oid_obj}: {e}")
    else:
        LOG.error(f"Walk failed for {mgmt_table_mib_name}")

    walk_args_system = (system_mib_name, ())
    system_walk_results = instance.process('walk', *walk_args_system)
    if system_walk_results is not None:
        for oid_obj, value_obj in system_walk_results:
            try:
                mib_module, mib_object, indices = oid_obj.get_mib_symbol()
                value = convert_asn1_value(value_obj)
                system_data[mib_object] = value
            except Exception as e:
                LOG.warning(f"Error processing system walk result {oid_obj}: {e}")
    else:
        LOG.error(f"Walk failed for {system_mib_name}")

    target_slot_for_pluggable = 6
    pluggable_fixed_indices = {"shelfId": 1, "subSlotNo": 0, "subPortNo": 0}
    pluggable_iterating_indices = {
        "portNo": list(range(1, 6)),
    }

    # Call the new synchronous method directly
    pluggable_data = instance.get_slot_ports_data(
        table_name="pluggableTable",
        slot_index=target_slot_for_pluggable,
        other_fixed_indices=pluggable_fixed_indices,
        iterating_indices_config=pluggable_iterating_indices
    )

    print(pluggable_data)
    # 经测试，没bulk walk快。采用snmpget命令行方式仅需0.4s左右，采用pysnmp库需要近3s
    # base_oid_prefix = "*******.4.1.52642.1.21.4.1.1"
    # aggregated_get_results = {}
    # iterations = 10
    # oids_per_iteration = 10
    # start_time_get_loop = time.time()
    # for i in range(1, iterations + 1):
    #     oids_to_get_this_iter = []
    #     for x in range(1, oids_per_iteration + 1):
    #         current_oid = f"{base_oid_prefix}.{x}.1.3.0.{i}.0"
    #         oids_to_get_this_iter.append(current_oid)
    #     LOG.info(f"Iteration {i}: Getting {len(oids_to_get_this_iter)} OIDs...")
    #     iter_results = instance.process('get', oids_to_get_this_iter)
    #     if iter_results:
    #         for req_oid, val_asn1 in iter_results.items():
    #             if val_asn1 is not None:
    #                 aggregated_get_results[req_oid] = convert_asn1_value(val_asn1)
    #             else:
    #                 aggregated_get_results[req_oid] = None
    #                 LOG.warning(f"Iteration {i}: No value received for OID {req_oid}")
    #     else:
    #         LOG.error(f"Iteration {i}: GET operation failed.")
    #         for req_oid in oids_to_get_this_iter:
    #             aggregated_get_results[req_oid] = None  # Indicate failure
    # end_time_get_loop = time.time()
    # duration_get_loop = end_time_get_loop - start_time_get_loop
    # print("\n--- Aggregated Iterative GET Results ---")
    # for oid, value in aggregated_get_results.items():
    #     print(f"  {oid}: {value}")
    # print(f"\nTotal time for {iterations} iterative GET operations: {duration_get_loop:.4f} seconds")

    print(cards_data)
    print(ports_data)
    print(mgmt_data)
    print(system_data)

    # if "plVendorSN" in SNMP_SET_INDICES:
    #     print(f"Info for plVendorSN: {SNMP_SET_INDICES['plVendorSN']}")

    card_inventory_table_name = "cardInventoryTable"
    slot_iteration_range = list(range(1, 12))
    fixed_card_indices = {"shelfId": 1, "subSlotNo": 0}

    card_data_by_slot = instance.get_slot_data(
        table_name=card_inventory_table_name,
        slot_list=slot_iteration_range,
        other_fixed_indices=fixed_card_indices
    )
    print(card_data_by_slot)