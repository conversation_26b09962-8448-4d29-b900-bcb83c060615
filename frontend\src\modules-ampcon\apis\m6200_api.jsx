import {request} from "@/utils/common/request";

const baseURL = "/ampcon";
const m6200Url = `${baseURL}/m6200`;

// bypass database in backend
export function queryM6200Info(ip) {
    return request({
        url: `${m6200Url}/info/query`,
        method: "GET",
        params: {
            ip
        }
    });
}

// query database in backend
export function getM6200Info(ip) {
    return request({
        url: `${m6200Url}/info/get`,
        method: "GET",
        params: {
            ip
        }
    });
}

export function getM6200DeviceCard(filterCard, {ip}) {
    return request({
        url: `${m6200Url}/get_m6200_device_card`,
        method: "POST",
        data: {
            ip,
            filterCard
        }
    });
}

export function getM6200DevicePort(id, type = "") {
    return request({
        url: `${m6200Url}/get_m6200_device_port`,
        method: "POST",
        data: {
            id,
            type
        }
    });
}

export function getTimeManagement() {
    return request({
        url: `${m6200Url}/get_m6200_time_management`,
        method: "GET"
    });
}

export function batchModifyDCSConfig(data) {
    return request({
        url: `${m6200Url}/config/batch_modify`,
        method: "PUT",
        data
    });
}

export function queryM6200Config(ip, slotIndex = null, cardId = null) {
    return request({
        url: `${m6200Url}/config/query`,
        method: "GET",
        params: {
            ip,
            slotIndex,
            cardId
        }
    });
}
