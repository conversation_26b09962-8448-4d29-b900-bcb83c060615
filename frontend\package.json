{"name": "ampcon", "version": "1.0.0", "private": true, "scripts": {"start:ampcon-super": "cross-env VITE_APP_EXPORT_MODULE=AmpCon-SUPER vite", "start:ampcon-t": "cross-env VITE_APP_EXPORT_MODULE=AmpCon-T vite", "start:ampcon-dc": "cross-env VITE_APP_EXPORT_MODULE=AmpCon-DC vite", "start:ampcon-campus": "cross-env VITE_APP_EXPORT_MODULE=AmpCon-CAMPUS vite", "build:ampcon-super": "cross-env VITE_APP_EXPORT_MODULE=AmpCon-SUPER NODE_OPTIONS=--max-old-space-size=4096 vite build", "build:ampcon-t": "cross-env VITE_APP_EXPORT_MODULE=AmpCon-T NODE_OPTIONS=--max-old-space-size=4096 vite build", "build:ampcon-dc": "cross-env VITE_APP_EXPORT_MODULE=AmpCon-DC NODE_OPTIONS=--max-old-space-size=4096 vite build", "build:ampcon-campus": "cross-env VITE_APP_EXPORT_MODULE=AmpCon-CAMPUS NODE_OPTIONS=--max-old-space-size=4096 vite build", "serve": "vite preview"}, "devDependencies": {"@originjs/vite-plugin-commonjs": "^1.0.3", "@vitejs/plugin-react": "^4.3.1", "@vitejs/plugin-react-swc": "^3.7.1", "cross-env": "^7.0.3", "eslint": "^8.57.1", "eslint-config-airbnb": "^19.0.4", "eslint-config-prettier": "^9.1.0", "eslint-import-resolver-alias": "^1.1.2", "eslint-plugin-babel": "^5.3.1", "eslint-plugin-import": "^2.29.1", "eslint-plugin-jsx-a11y": "^6.8.0", "eslint-plugin-prettier": "^5.1.2", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-unicorn": "^50.0.1", "http-proxy-middleware": "^2.0.6", "prettier": "^3.1.1", "sass": "^1.69.7", "sass-loader": "^13.3.3", "vite": "^5.4.14", "vite-plugin-babel": "^1.2.0", "vite-plugin-commonjs": "^0.10.3", "vite-plugin-require": "^1.2.14", "vite-plugin-sass": "^0.1.0"}, "dependencies": {"@ant-design/icons": "^5.2.6", "@antv/hierarchy": "^0.6.12", "@antv/l7": "2.20.5", "@antv/l7-draw": "^3.1.5", "@antv/layout": "^0.3.25", "@antv/x6": "^2.16.1", "@antv/x6-plugin-export": "^2.1.6", "@antv/x6-plugin-history": "^2.2.4", "@antv/x6-plugin-keyboard": "^2.2.3", "@antv/x6-plugin-minimap": "^2.0.7", "@antv/x6-plugin-selection": "^2.2.2", "@antv/x6-plugin-snapline": "^2.1.7", "@antv/x6-react-components": "^2.0.8", "@antv/x6-react-shape": "^2.2.3", "@babel/eslint-parser": "^7.25.9", "@fullcalendar/core": "^6.1.11", "@fullcalendar/daygrid": "^6.1.11", "@fullcalendar/interaction": "^6.1.11", "@fullcalendar/list": "^6.1.11", "@fullcalendar/react": "^6.1.11", "@fullcalendar/timegrid": "^6.1.11", "@popperjs/core": "^2.11.8", "@reduxjs/toolkit": "^2.0.1", "ahooks": "^3.7.8", "antd": "5.12.8", "axios": "^1.6.5", "dayjs": "^1.11.10", "diff": "^5.2.0", "echarts": "^5.4.3", "lodash": "^4.17.21", "monaco-editor": "^0.46.0", "normalize.css": "^8.0.1", "rc-dock": "^3.2.18", "react": "^18.2.0", "react-dom": "^18.2.0", "react-popper": "^2.3.0", "react-redux": "^9.0.4", "react-router-dom": "^6.21.1", "react-scripts": "^5.0.1", "sass-embedded": "^1.80.4", "swc-loader": "^0.2.6", "vis-network": "^9.1.9", "vite-plugin-compression": "^0.5.1", "vite-plugin-eslint": "^1.8.1", "vite-plugin-node-polyfills": "^0.22.0", "vite-plugin-replace": "^0.1.1", "vite-plugin-svgr": "^4.2.0"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 50 chrome version", "last 50 firefox version", "last 50 safari version"]}}