package collector

import (
	"log"
	"strconv"
	"strings"
	"time"

	"github.com/prometheus/client_golang/prometheus"
)

type BaseMetric struct {
	Name      string
	Label     map[string]string
	Value     string
	Timestamp time.Time
}

func (m *BaseMetric) BuildMetric() prometheus.Metric {
	metricName := strings.ReplaceAll(strings.ReplaceAll(strings.TrimLeft(m.Name, "/"), "/", "_"), "-", "_")
	value, err := strconv.ParseFloat(m.Value, 64)
	if err != nil {
		log.Println("ParseFloat error:", err)
		return nil
	}

	labelNames := make([]string, 0, len(m.Label))
	labelValues := make([]string, 0, len(m.Label))

	for k, v := range m.Label {
		labelName := strings.ReplaceAll(strings.ReplaceAll(k, "-", "_"), ":", "_")
		labelNames = append(labelNames, labelName)
		labelValues = append(labelValues, v)
	}

	// fmt.Println(metricName, labelNames, labelValues, value, m.Timestamp)
	metric := prometheus.NewMetricWithTimestamp(
		m.Timestamp,
		prometheus.MustNewConstMetric(
			prometheus.NewDesc(
				metricName,
				metricName,
				labelNames,
				nil),
			prometheus.GaugeValue,
			value, labelValues...))
	return metric
}

func (m *BaseMetric) AddLabel(key, value string) {
	m.Label[key] = value
}
