#!/bin/bash
apt-get update
apt-get -y install vim
apt-get -y install ssh
pip install pydevd-pycharm~=242.24807.21 -i https://pypi.tuna.tsinghua.edu.cn/simple


echo 'root:pica8' | chpasswd
sed -i 's/#PermitRootLogin prohibit-password/PermitRootLogin yes/' /etc/ssh/sshd_config
sed 's@session\s*required\s*pam_loginuid.so@session optional pam_loginuid.so@g' -i /etc/pam.d/sshd
echo 'export VISIBLE=now' >> /etc/profile
service ssh restart
