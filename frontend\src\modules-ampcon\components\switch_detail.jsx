import {useSelector} from "react-redux";
import {useEffect, useState} from "react";
import {Card, message, Row, Col, Space, Flex} from "antd";
import {getSwitchDetails} from "@/modules-ampcon/apis/inventory_api";
import icon from "@/assets/port.png";

const SwitchDetail = () => {
    const {selectedItem} = useSelector(state => state.map);
    const [switchDetail, setSwitchDetail] = useState({});
    const [portDetails, setPortDetails] = useState({});

    useEffect(() => {
        const fetchData = async () => {
            try {
                const res = await getSwitchDetails(selectedItem.id.split(":")[1]);
                if (res.apiResult === "complete") {
                    const {data} = res;
                    const {port_map, ...switchData} = data;
                    setSwitchDetail({
                        SN: switchData.sn,
                        Model: switchData.platform_model || "--",
                        HostName: switchData.host_name || "--",
                        Status: switchData.status || "--",
                        HardwareID: switchData.hwid || "--",
                        IP: switchData.mgt_ip || "--",
                        Version: switchData.version || "--",
                        Reachable: switchData.reachable_status === 1 ? "Connected" : "unConnected" || "--"
                    });
                    setPortDetails(port_map);
                } else {
                    message.error(res.apiMessage);
                }
            } catch (error) {
                // error
            }
        };

        if (selectedItem?.id?.startsWith("switch:")) {
            fetchData().then();
        }
    }, [selectedItem]);

    const renderPortCards = portType => {
        const portCount = portDetails[`${portType}_end`];
        if (!portCount) return null;
        const iconRange = Array.from({length: portCount}, (_, index) => index + 1);
        return (
            <span>
                <Card title={portType}>
                    <div>
                        {iconRange.slice(0, Math.ceil(portCount / 2)).map(index => (
                            <img src={icon} alt={`Icon ${index}`} key={`icon-${index}`} />
                        ))}
                    </div>
                    <div>
                        {iconRange.slice(0, Math.ceil(portCount / 2)).map(index => (
                            <img src={icon} alt={`Icon ${index}`} key={`icon-${index}`} />
                        ))}
                    </div>
                </Card>
            </span>
        );
    };

    return (
        <div style={{display: "flex", flexDirection: "column", flex: 1, height: "100%"}}>
            <Flex vertical style={{height: "80%", margin: 23, justifyContent: "space-evenly"}} align="center">
                {renderPortCards("ge")}
                {renderPortCards("te")}
                {renderPortCards("qe")}
                {renderPortCards("xe")}
            </Flex>
            <div style={{background: "#f5f5f5", height: "20px"}} />
            <div>
                <Card style={{marginTop: "30px"}}>
                    <Row gutter={24}>
                        {Object.entries(switchDetail).map(([key, value]) => (
                            <Col span={6} key={key}>
                                <div style={{padding: "10px"}}>
                                    <div
                                        style={{
                                            display: "flex",
                                            alignItems: "center",
                                            marginBottom: "5px"
                                        }}
                                    >
                                        <div
                                            style={{
                                                width: 5,
                                                height: 5,
                                                backgroundColor: "#14c9bb",
                                                borderRadius: "50%",
                                                marginRight: 5
                                            }}
                                        />
                                        <Space>
                                            {key}: {value}
                                        </Space>
                                    </div>
                                </div>
                            </Col>
                        ))}
                    </Row>
                </Card>
            </div>
        </div>
    );
};

export default SwitchDetail;
