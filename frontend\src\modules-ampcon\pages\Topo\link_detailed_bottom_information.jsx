import {forwardRef, useImperativeHandle, useState, useEffect, useRef} from "react";
import {AmpConCustomTable} from "@/modules-ampcon/components/custom_table";
import {Space, Tag} from "antd";
import styles from "@/modules-ampcon/pages/Topo/topo.module.scss";
import {fetchInterfaceInfo} from "@/modules-ampcon/apis/monitor_api";
import {convertToUTCString} from "@/utils/topo_layout_utils";
import CloseSvg from "./resource/portInfo_close.svg?react";

const LinkDetailedBottomInformation = forwardRef(({containerHeight}, ref) => {
    const [isLinkBottomInformationVisible, setLinkBottomInformationVisible] = useState(false);
    const [sourceNode, setSourceNode] = useState();
    const [targetNode, setTargetNode] = useState();
    const [formattedData, setFormattedData] = useState([]);
    const [height, setHeight] = useState(420);
    const [isDragging, setIsDragging] = useState(false);
    const dragRef = useRef(null);

    const startDrag = e => {
        e.preventDefault();
        setIsDragging(true);
        dragRef.current.initialY = e.clientY;
        dragRef.current.initialHeight = height;
    };

    const onDrag = e => {
        if (isDragging) {
            const newHeight = dragRef.current.initialHeight - (e.clientY - dragRef.current.initialY);
            setHeight(() => (newHeight > 250 ? Math.min(newHeight, containerHeight * 0.75) : 250));
        }
    };

    const stopDrag = () => {
        setIsDragging(false);
    };

    useEffect(() => {
        const handleMouseMove = e => {
            if (isDragging) {
                onDrag(e);
            }
        };

        const handleMouseUp = () => {
            stopDrag();
        };

        document.addEventListener("mousemove", handleMouseMove);
        document.addEventListener("mouseup", handleMouseUp);

        return () => {
            document.removeEventListener("mousemove", handleMouseMove);
            document.removeEventListener("mouseup", handleMouseUp);
        };
    }, [isDragging]);

    const tagStyle = admin_status => {
        if (admin_status.toLowerCase() === "up") {
            return <Tag className={styles.UpStyle}>Up</Tag>;
        }
        if (admin_status.toLowerCase() === "down") {
            return <Tag className={styles.DownStyle}>Down</Tag>;
        }
    };

    const createColumn = (title, dataIndex, key, sorter) => ({
        title,
        dataIndex,
        key,
        sorter,
        sortDirections: ["ascend", "descend"]
    });

    const columns = [
        sourceNode?.device_type !== 2
            ? {
                  title: `${sourceNode?.label} (${sourceNode?.switch_sn})`,
                  children: [
                      createColumn("Interface", "source_port", "source_port", (a, b) =>
                          a.source_port.localeCompare(b.source_port)
                      ),
                      {
                          title: "Status",
                          dataIndex: "source_status",
                          key: "source_status",
                          render: text => <Space>{tagStyle(text)}</Space>,
                          sorter: (a, b) => a.source_status.localeCompare(b.source_status)
                      },
                      createColumn("Speed", "source_speed", "source_speed", (a, b) =>
                          a.source_speed.localeCompare(b.source_speed)
                      ),
                      createColumn("Flow Control", "source_flow_control", "source_flow_control", (a, b) =>
                          a.source_flow_control.localeCompare(b.source_flow_control)
                      ),
                      createColumn("Duplex Mode", "source_duplex_mode", "source_duplex_mode", (a, b) =>
                          a.source_duplex_mode.localeCompare(b.source_duplex_mode)
                      )
                  ]
              }
            : {},
        targetNode?.device_type !== 2
            ? {
                  title: `${targetNode?.label} (${targetNode?.switch_sn})`,
                  children: [
                      createColumn("Interface", "target_port", "target_port", (a, b) =>
                          a.target_port.localeCompare(b.target_port)
                      ),
                      {
                          title: "Status",
                          dataIndex: "target_status",
                          key: "target_status",
                          render: text => <Space>{tagStyle(text)}</Space>,
                          sorter: (a, b) => a.target_status.localeCompare(b.target_status)
                      },
                      createColumn("Speed", "target_speed", "target_speed", (a, b) =>
                          a.target_speed.localeCompare(b.target_speed)
                      ),
                      createColumn("Flow Control", "target_flow_control", "target_flow_control", (a, b) =>
                          a.target_flow_control.localeCompare(b.target_flow_control)
                      ),
                      createColumn("Duplex Mode", "target_duplex_mode", "target_duplex_mode", (a, b) =>
                          a.target_duplex_mode.localeCompare(b.target_duplex_mode)
                      )
                  ]
              }
            : {}
    ].filter(column => Object.keys(column).length > 0);

    // const handleTableChange = (pagination, filters, sorter) => {
    //     console.log("Table params:", pagination, filters, sorter);
    // };

    const fetchInterfaceData = async (sn, queryDate) => {
        const response = await fetchInterfaceInfo(sn, convertToUTCString(queryDate));
        if (response.status === 200) {
            return Object.fromEntries(
                Object.keys(response.data).map(key => {
                    const port = response.data[key];
                    const port_speed =
                        port?.ethernet_state?.negotiated_port_speed?.match(/SPEED_(.*)/)?.[1] ||
                        port?.ethernet_state?.port_speed?.match(/SPEED_(.*)/)?.[1] ||
                        "-";
                    return [
                        key,
                        {
                            oper_status: port?.state?.oper_status || "-",
                            flow_control: port?.ethernet_config?.enable_flow_control || "-",
                            port_speed: port_speed === "2500MB" ? "2.5GB" : port_speed,
                            duplex_mode:
                                port?.ethernet_state?.negotiated_duplex_mode || port?.ethernet_state?.duplex_mode || "-"
                        }
                    ];
                })
            );
        }
        return null;
    };

    const loadData = async (sourceSN, targetSN, portInfo, queryDate) => {
        const [sourceInterfaces, targetInterfaces] = await Promise.all([
            sourceSN ? fetchInterfaceData(sourceSN, queryDate) : undefined,
            targetSN ? fetchInterfaceData(targetSN, queryDate) : undefined
        ]);

        setFormattedData(
            portInfo.map(item => ({
                ...item,
                source_status: item.status,
                source_speed: sourceSN ? sourceInterfaces[item.source_port]?.port_speed : undefined,
                source_flow_control: sourceSN ? sourceInterfaces[item.source_port]?.flow_control : undefined,
                source_duplex_mode: sourceSN ? sourceInterfaces[item.source_port]?.duplex_mode : undefined,
                target_status: item.status,
                target_speed: targetSN ? targetInterfaces[item.target_port]?.port_speed : undefined,
                target_flow_control: targetSN ? targetInterfaces[item.target_port]?.flow_control : undefined,
                target_duplex_mode: targetSN ? targetInterfaces[item.target_port]?.duplex_mode : undefined
            }))
        );
    };

    useImperativeHandle(ref, () => ({
        showLinkDetailedBottomInformation: linkInfo => {
            setFormattedData([]);
            setSourceNode(linkInfo.sourceNode.store.data);
            setTargetNode(linkInfo.targetNode.store.data);
            loadData(
                linkInfo.sourceNode.store.data.switch_sn,
                linkInfo.targetNode.store.data.switch_sn,
                linkInfo.Status,
                linkInfo.Time
            );
            setHeight(420);
            setLinkBottomInformationVisible(true);
        },
        hideLinkDetailedBottomInformation: () => {
            setLinkBottomInformationVisible(false);
        }
    }));

    return isLinkBottomInformationVisible ? (
        <div
            ref={ref}
            style={{
                position: "absolute",
                display: "block",
                padding: "5px",
                bottom: "0px",
                left: "0px",
                backgroundColor: "white",
                color: "black",
                borderRadius: "8px",
                pointerEvents: "true",
                zIndex: 1000,
                width: "100%",
                height: `${height}px`
            }}
        >
            <div
                ref={dragRef}
                onMouseDown={startDrag}
                style={{
                    height: "5px",
                    backgroundColor: "transparent",
                    cursor: "ns-resize"
                }}
            />
            <div>
                <h2
                    style={{
                        position: "sticky",
                        top: 0,
                        backgroundColor: "white",
                        zIndex: 1,
                        marginTop: "10px",
                        margin: "10px",
                        height: "39px"
                    }}
                >
                    Link Port Info
                    <CloseSvg
                        className={styles.closeIcon}
                        style={{position: "absolute", right: "14px", top: "9px", cursor: "pointer"}}
                        onClick={() => setLinkBottomInformationVisible(false)}
                    />
                </h2>
                <div style={{borderBottom: "1px solid #e0e0e0"}} />
                <AmpConCustomTable
                    columns={columns}
                    dataSource={formattedData}
                    // onChange={handleTableChange}
                    isShowPagination={false}
                    style={{margin: "10px"}}
                    scroll={{y: height - 212}}
                    sticky
                />
            </div>
        </div>
    ) : null;
});

export default LinkDetailedBottomInformation;
