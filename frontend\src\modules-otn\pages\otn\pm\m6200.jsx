import {createColumnConfig, AmpConCustomTable, TableFilterDropdown} from "@/modules-ampcon/components/custom_table";
import {fetchJobInfo, fetchTaskOutputInfo, terminateJob} from "@/modules-ampcon/apis/automation_api";
import {Space, message, Button, Form, Select, InputNumber, DatePicker} from "antd";
import {useRef, useState, useMemo, Option} from "react";
import dayjs from "dayjs";
import {DebounceButton, NULL_VALUE} from "@/modules-ampcon/pages/Resource/DeviceDisplay/utils";
import {
    editTableIcon,
    exportDisabledSvg,
    exportSvg,
    refreshDisabledSvg,
    refreshSvg,
    updateWhiteSvg
} from "@/utils/common/iconSvg";
import Icon from "@ant-design/icons";
import {useSelector} from "react-redux";
import SelectLoading from "@/modules-otn/components/common/select_loading";
import {confirmModalAction} from "@/modules-ampcon/components/custom_modal";

const M6200 = (props, ref) => {
    const {Option} = Select;
    const tableRef = useRef(null);
    const imageTableRef = useRef(null);
    const addImageModalRef = useRef(null);
    const currentUser = useSelector(state => state.user.userInfo);
    const userType = currentUser?.type;
    const [tableData, setTableData] = useState([]);
    const {pmType, buttons} = props;
    const [form] = Form.useForm();

    const matchFieldsList = [
        {name: "pmPoint", matchMode: "fuzzy"},
        {name: "pmPointType", matchMode: "fuzzy"},
        {name: "pmParameter", matchMode: "fuzzy"},
        {name: "pmGranularity", matchMode: "fuzzy"},
        {name: "maxValue", matchMode: "fuzzy"},
        {name: "minValue", matchMode: "fuzzy"},
        {name: "avgValue", matchMode: "fuzzy"},
        {name: "currentValue", matchMode: "fuzzy"},
        {name: "monitoringTime", matchMode: "fuzzy"}
    ];

    const currentAndHistoryColumns = [
        {
            ...createColumnConfig("PM Point", "pmPoint", TableFilterDropdown, "")
        },
        createColumnConfig("PM Point Type", "pmPointType", TableFilterDropdown, ""),
        createColumnConfig("PM Parameter", "pmParameter", TableFilterDropdown, ""),
        createColumnConfig("PM Granularity", "pmGranularity", TableFilterDropdown, ""),
        createColumnConfig("Max Value", "maxValue", TableFilterDropdown, ""),
        createColumnConfig("Min Value", "minValue", TableFilterDropdown, ""),
        createColumnConfig("Average Value", "avgValue", TableFilterDropdown, ""),
        createColumnConfig("Current Value", "currentValue", TableFilterDropdown, ""),
        createColumnConfig("Monitoring Date Time", "monitoringTime", TableFilterDropdown, "")
    ];
    const pmpColumns = [
        createColumnConfig("PM Point", "pmPoint", TableFilterDropdown, ""),
        createColumnConfig("PM Point Enable", "pmPointEnable", TableFilterDropdown, ""),
        createColumnConfig("TCA Enable", "tcaEnable", TableFilterDropdown, "")
    ];
    const [historyType, setHistoryType] = useState("RECORDS");

    const renderHistoryFields = () => {
        if (pmType !== "HISTORY") return null;

        return (
            <>
                <Form.Item
                    name="historyDataType"
                    label="History Data Type"
                    rules={[{required: true, message: "Please select history data type"}]}
                >
                    <Select
                        placeholder="Select Type"
                        style={{width: 280}}
                        onChange={value => {
                            setHistoryType(value);
                            form.setFieldsValue({startTime: undefined, endTime: undefined, recordCount: undefined});
                        }}
                    >
                        <Option value="TIME">TIME</Option>
                        <Option value="RECORDS">RECORDS</Option>
                    </Select>
                </Form.Item>

                {historyType === "TIME" && (
                    <>
                        <Form.Item
                            name="startTime"
                            label="Start Time"
                            rules={[{required: true, message: "Please select start time"}]}
                        >
                            <DatePicker showTime style={{width: 280}} />
                        </Form.Item>
                        <Form.Item
                            name="endTime"
                            label="End Time"
                            rules={[{required: true, message: "Please select end time"}]}
                        >
                            <DatePicker showTime style={{width: 280}} />
                        </Form.Item>
                    </>
                )}

                {historyType === "RECORDS" && (
                    <Form.Item
                        name="recordCount"
                        label="Number of Records"
                        rules={[{required: true, message: "Please input record count"}]}
                    >
                        <InputNumber min={1} defaultValue={10} style={{width: 280}} />
                    </Form.Item>
                )}
            </>
        );
    };
    const renderPmpFields = () => {
        if (pmType !== "PMP") return null;

        return (
            <>
                <Form.Item name="ne" label="NE" rules={[{required: true}]}>
                    <Select style={{width: 280}} allowClear />
                </Form.Item>
                <Form.Item name="slot" label="Slot" rules={[{required: true}]}>
                    <Select style={{width: 280}} allowClear />
                </Form.Item>
                <Form.Item name="port" label="Port" rules={[{required: true}]}>
                    <Select style={{width: 280}} allowClear />
                </Form.Item>
            </>
        );
    };
    const renderCommonFields = () => {
        return (
            <>
                <Form.Item name="ne" label="NE" rules={[{required: true, message: "Please select NE"}]}>
                    <Select
                        mode="multiple"
                        style={{width: 280}}
                        placeholder="Select NE"
                        allowClear
                        showSearch={false}
                    />
                </Form.Item>
                <Form.Item
                    name="pmPointType"
                    label="PM Point Type"
                    rules={[{required: true, message: "Please select PM Point Type"}]}
                >
                    <Select
                        mode="multiple"
                        style={{width: 280}}
                        placeholder="Select PM Point Type"
                        allowClear
                        showSearch={false}
                    />
                </Form.Item>
                <Form.Item
                    name="pmPoint"
                    label="PM Point"
                    rules={[{required: true, message: "Please select PM Point"}]}
                >
                    <Select
                        mode="multiple"
                        style={{width: 280}}
                        placeholder="Select PM Point"
                        allowClear
                        showSearch={false}
                    />
                </Form.Item>
                <Form.Item
                    name="pmParameter"
                    label="PM Parameter"
                    rules={[{required: true, message: "Please select PM Parameter"}]}
                >
                    <Select
                        mode="multiple"
                        style={{width: 280}}
                        placeholder="Select PM Parameter"
                        allowClear
                        showSearch={false}
                    />
                </Form.Item>
                <Form.Item
                    name="pmGranularity"
                    label="PM Granularity"
                    initialValue="24H"
                    rules={[{required: true, message: "Please select PM Granularity"}]}
                >
                    <Select style={{width: 280}} placeholder="Select PM Granularity" allowClear showSearch={false}>
                        <Option value="15MIN">15MIN</Option>
                        <Option value="24H">24H</Option>
                    </Select>
                </Form.Item>
                <Form.Item
                    name="ValueScope"
                    label="Value Scope"
                    initialValue="INSTANT"
                    rules={[{required: true, message: "Please select Value Scope"}]}
                >
                    <Select style={{width: 280}} placeholder="Select Value Scope" allowClear showSearch={false}>
                        <Option value="INSTANT-AVG-MIN-MAX">INSTANT-AVG-MIN-MAX</Option>
                        <Option value="INSTANT">INSTANT</Option>
                    </Select>
                </Form.Item>
            </>
        );
    };

    return (
        <div style={{padding: 8}}>
            <Form
                form={form}
                layout="inline"
                labelAlign="left"
                labelCol={{span: 8}}
                style={{display: "flex", flexWrap: "wrap", columnGap: 80, rowGap: 24}}
            >
                {pmType === "PMP" ? (
                    renderPmpFields()
                ) : (
                    <>
                        {renderCommonFields()}
                        {renderHistoryFields()}
                    </>
                )}

                <Form.Item style={{width: "100%", marginBottom: 24}}>
                    <Button type="primary" htmlType="submit">
                        Search
                    </Button>
                </Form.Item>
            </Form>
            <AmpConCustomTable
                ref={tableRef}
                // columns={currentAndHistoryColumns}
                columns={pmType === "pmp" ? pmpColumns : currentAndHistoryColumns}
                matchFieldsList={matchFieldsList}
                // fetchAPIInfo={fetchJobInfo} 添加api接口
                extraButton={
                    <>
                        {userType === "superuser" ? (
                            <Button
                                type="primary"
                                onClick={() => {
                                    addImageModalRef.current.showAddImageModal();
                                }}
                                icon={<Icon component={!tableData.length ? exportDisabledSvg : exportSvg} />}
                                disabled={!tableData.length}
                            >
                                Export
                            </Button>
                        ) : null}
                        <Button
                            htmlType="button"
                            onClick={() => {
                                imageTableRef.current.refreshTable();
                                message.success("Image table refresh success.");
                            }}
                        >
                            <Icon component={refreshSvg} />
                            Refresh
                        </Button>
                    </>
                }
            />
        </div>
    );
};
export default M6200;
