# SNMP MIB module (ST-OTN-MIB) expressed in pysnmp data model.
#
# This Python module is designed to be imported and executed by the
# pysnmp library.
#
# See https://www.pysnmp.com/pysnmp for further information.
#
# Notes
# -----
# ASN.1 source file://./ST-OTN-MIB.mib
# Produced by pysmi-1.5.11 at Wed May  7 10:08:48 2025
# On host pica8 platform Linux version 5.15.0-122-generic by user root
# Using Python version 3.11.10 (main, Sep  7 2024, 18:35:41) [GCC 11.4.0]

if 'mibBuilder' not in globals():
    import sys

    sys.stderr.write(__doc__)
    sys.exit(1)

# Import base ASN.1 objects even if this MIB does not use it

(Integer,
 OctetString,
 ObjectIdentifier) = mibBuilder.importSymbols(
    "ASN1",
    "Integer",
    "OctetString",
    "ObjectIdentifier")

(NamedValues,) = mibBuilder.importSymbols(
    "ASN1-ENUMERATION",
    "NamedValues")
(ConstraintsIntersection,
 ConstraintsUnion,
 SingleValueConstraint,
 ValueRangeConstraint,
 ValueSizeConstraint) = mibBuilder.importSymbols(
    "ASN1-REFINEMENT",
    "ConstraintsIntersection",
    "ConstraintsUnion",
    "SingleValueConstraint",
    "ValueRangeConstraint",
    "ValueSizeConstraint")

# Import SMI symbols from the MIBs this MIB depends on

(ModuleCompliance,
 NotificationGroup) = mibBuilder.importSymbols(
    "SNMPv2-CONF",
    "ModuleCompliance",
    "NotificationGroup")

(Bits,
 Bits,
 Counter32,
 Counter64,
 Gauge32,
 Integer32,
 IpAddress,
 ModuleIdentity,
 MibIdentifier,
 NotificationType,
 ObjectIdentity,
 MibScalar,
 MibTable,
 MibTableRow,
 MibTableColumn,
 TimeTicks,
 Unsigned32,
 iso) = mibBuilder.importSymbols(
    "SNMPv2-SMI",
    "Bits",
    "Bits",
    "Counter32",
    "Counter64",
    "Gauge32",
    "Integer32",
    "IpAddress",
    "ModuleIdentity",
    "MibIdentifier",
    "NotificationType",
    "ObjectIdentity",
    "MibScalar",
    "MibTable",
    "MibTableRow",
    "MibTableColumn",
    "TimeTicks",
    "Unsigned32",
    "iso")

(DateAndTime,
 DisplayString,
 PhysAddress,
 RowStatus,
 TextualConvention,
 TruthValue) = mibBuilder.importSymbols(
    "SNMPv2-TC",
    "DateAndTime",
    "DisplayString",
    "PhysAddress",
    "RowStatus",
    "TextualConvention",
    "TruthValue")

(StAvailabilityState,
 portNo,
 shelfId,
 slotNo,
 subPortNo,
 subSlotNo) = mibBuilder.importSymbols(
    "ST-COMMON-MIB",
    "StAvailabilityState",
    "portNo",
    "shelfId",
    "slotNo",
    "subPortNo",
    "subSlotNo")

(enterpriseProducts,) = mibBuilder.importSymbols(
    "ST-ROOT-MIB",
    "enterpriseProducts")


# MODULE-IDENTITY

stOTN = ModuleIdentity(
    (1, 3, 6, 1, 4, 1, 52642, 1, 20)
)
if mibBuilder.loadTexts:
    stOTN.setLastUpdated("201704191143Z")
if mibBuilder.loadTexts:
    stOTN.setDescription("OTN MIB")


# Types definitions


# TEXTUAL-CONVENTIONS



class SEtyType(TextualConvention, Integer32):
    status = "current"
    subtypeSpec = Integer32.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        SingleValueConstraint(
            *(0,
              1,
              2,
              3,
              4,
              5,
              6,
              7,
              8,
              9,
              10)
        )
    )
    namedValues = NamedValues(
        *(("none", 0),
          ("feCbr", 1),
          ("gettt", 2),
          ("xgeOpu2e", 3),
          ("xgeGfpOpu2", 4),
          ("xgeGfpOpu2e", 5),
          ("ge", 6),
          ("ge100Gmp", 7),
          ("ge100Gfpf", 8),
          ("ge40Gmp", 9),
          ("ge40Gfpf", 10))
    )

    if mibBuilder.loadTexts:
        description = " "


class SOtnEnabled(TextualConvention, Integer32):
    status = "current"
    subtypeSpec = Integer32.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        SingleValueConstraint(
            *(0,
              1)
        )
    )
    namedValues = NamedValues(
        *(("disabled", 0),
          ("enabled", 1))
    )

    if mibBuilder.loadTexts:
        description = " "


class SOtnUpDown(TextualConvention, Integer32):
    status = "current"
    subtypeSpec = Integer32.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        SingleValueConstraint(
            *(0,
              1)
        )
    )
    namedValues = NamedValues(
        *(("down", 0),
          ("up", 1))
    )

    if mibBuilder.loadTexts:
        description = " "


class SLoopBackMode(TextualConvention, Integer32):
    status = "current"
    subtypeSpec = Integer32.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        SingleValueConstraint(
            *(0,
              1,
              2,
              3,
              4)
        )
    )
    namedValues = NamedValues(
        *(("none", 0),
          ("outwardNear", 1),
          ("inwardFar", 2),
          ("outwardFar", 3),
          ("inwardNear", 4))
    )

    if mibBuilder.loadTexts:
        description = " "


class SMapMode(TextualConvention, Integer32):
    status = "current"
    subtypeSpec = Integer32.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        SingleValueConstraint(
            *(0,
              1,
              2,
              3,
              4,
              255)
        )
    )
    namedValues = NamedValues(
        *(("amp", 0),
          ("bmp", 1),
          ("gmp", 2),
          ("gfp", 3),
          ("cbr", 4),
          ("none", 255))
    )

    if mibBuilder.loadTexts:
        description = " "


class SOtnYesNo(TextualConvention, Integer32):
    status = "current"
    subtypeSpec = Integer32.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        SingleValueConstraint(
            *(0,
              1,
              2)
        )
    )
    namedValues = NamedValues(
        *(("no", 0),
          ("yes", 1),
          ("na", 2))
    )

    if mibBuilder.loadTexts:
        description = " "


class SOcStmType(TextualConvention, Integer32):
    status = "current"
    subtypeSpec = Integer32.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        SingleValueConstraint(
            *(0,
              1,
              2,
              3,
              4,
              5,
              6,
              7,
              8,
              9,
              10,
              11,
              12,
              13,
              14,
              15)
        )
    )
    namedValues = NamedValues(
        *(("none", 0),
          ("stm1", 1),
          ("stm4", 2),
          ("stm16", 3),
          ("stm64", 4),
          ("stm256", 5),
          ("oc3", 6),
          ("oc12", 7),
          ("oc48", 8),
          ("oc192", 9),
          ("oc768", 10),
          ("fc100", 11),
          ("fc200", 12),
          ("fc400", 13),
          ("fc800", 14),
          ("fc1200", 15))
    )

    if mibBuilder.loadTexts:
        description = " "


class TraceIdentifierSDH(TextualConvention, OctetString):
    status = "current"
    subtypeSpec = OctetString.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        ValueSizeConstraint(16, 16),
        ValueSizeConstraint(64, 64),
    )

    if mibBuilder.loadTexts:
        description = "Trail trace identifier for SDH/SONET, either - 15 byte printable characters + CRC byte or - 62 byte printable characters + CRC byte."


class TraceIdentifierSDHMsgType(TextualConvention, Integer32):
    status = "current"
    subtypeSpec = Integer32.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        SingleValueConstraint(
            *(0,
              1,
              2)
        )
    )
    namedValues = NamedValues(
        *(("disabled", 0),
          ("byte16", 1),
          ("byte64", 2))
    )

    if mibBuilder.loadTexts:
        description = "Format of SDH trail trace identifier."


class SOduType(TextualConvention, Integer32):
    status = "current"
    subtypeSpec = Integer32.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        SingleValueConstraint(
            *(0,
              1,
              2,
              3,
              4,
              5,
              6,
              7,
              8,
              9)
        )
    )
    namedValues = NamedValues(
        *(("none", 0),
          ("odu0", 1),
          ("odu1", 2),
          ("odu2", 3),
          ("odu3", 4),
          ("odu4", 5),
          ("odu2e", 6),
          ("oduflex", 7),
          ("oduc2", 8),
          ("oduc4", 9))
    )

    if mibBuilder.loadTexts:
        description = " "


class SOduId(TextualConvention, Unsigned32):
    status = "current"
    subtypeSpec = Unsigned32.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        ValueRangeConstraint(1, 128),
    )

    if mibBuilder.loadTexts:
        description = " "


class SOtuId(TextualConvention, Unsigned32):
    status = "current"
    subtypeSpec = Unsigned32.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        ValueRangeConstraint(1, 256),
    )

    if mibBuilder.loadTexts:
        description = " "


class SOtuType(TextualConvention, Integer32):
    status = "current"
    subtypeSpec = Integer32.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        SingleValueConstraint(
            *(0,
              1,
              2,
              3,
              4,
              5,
              6,
              7,
              8,
              9,
              10,
              11)
        )
    )
    namedValues = NamedValues(
        *(("none", 0),
          ("otu0ll", 1),
          ("otu1", 2),
          ("otu2", 3),
          ("otu3", 4),
          ("otu4", 5),
          ("otu2e", 6),
          ("otu2f", 7),
          ("otu1e", 8),
          ("otu1f", 9),
          ("otuc2", 10),
          ("otuc4", 11))
    )

    if mibBuilder.loadTexts:
        description = " "


class OperationalState(TextualConvention, Integer32):
    status = "current"
    subtypeSpec = Integer32.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        SingleValueConstraint(
            *(0,
              1)
        )
    )
    namedValues = NamedValues(
        *(("enabled", 0),
          ("disabled", 1))
    )

    if mibBuilder.loadTexts:
        description = "Indicates whether a resource is able to provide service."


class SOtnTruthValue(TextualConvention, Integer32):
    status = "current"
    subtypeSpec = Integer32.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        SingleValueConstraint(
            *(0,
              1)
        )
    )
    namedValues = NamedValues(
        *(("false", 0),
          ("true", 1))
    )

    if mibBuilder.loadTexts:
        description = " "


class SOchOsId(TextualConvention, Unsigned32):
    status = "current"
    subtypeSpec = Unsigned32.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        ValueRangeConstraint(1, 256),
    )

    if mibBuilder.loadTexts:
        description = " "


# MIB Managed Objects in the order of their OIDs

_OtnConfigMIB_ObjectIdentity = ObjectIdentity
otnConfigMIB = _OtnConfigMIB_ObjectIdentity(
    (1, 3, 6, 1, 4, 1, 52642, 1, 20, 1)
)
_SETYnTable_Object = MibTable
sETYnTable = _SETYnTable_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 20, 1, 4)
)
if mibBuilder.loadTexts:
    sETYnTable.setStatus("current")
if mibBuilder.loadTexts:
    sETYnTable.setDescription("ETYn Table")
_SETYnEntry_Object = MibTableRow
sETYnEntry = _SETYnEntry_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 20, 1, 4, 1)
)
sETYnEntry.setIndexNames(
    (0, "ST-COMMON-MIB", "shelfId"),
    (0, "ST-COMMON-MIB", "slotNo"),
    (0, "ST-COMMON-MIB", "subSlotNo"),
    (0, "ST-COMMON-MIB", "portNo"),
    (0, "ST-COMMON-MIB", "subPortNo"),
)
if mibBuilder.loadTexts:
    sETYnEntry.setStatus("current")
if mibBuilder.loadTexts:
    sETYnEntry.setDescription(" ")
_EtynType_Type = SEtyType
_EtynType_Object = MibTableColumn
etynType = _EtynType_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 20, 1, 4, 1, 1),
    _EtynType_Type()
)
etynType.setMaxAccess("read-create")
if mibBuilder.loadTexts:
    etynType.setStatus("current")
if mibBuilder.loadTexts:
    etynType.setDescription("Description.")


class _EtynAdminState_Type(SOtnEnabled):
    """Custom type etynAdminState based on SOtnEnabled"""
    defaultValue = 1


_EtynAdminState_Type.__name__ = "SOtnEnabled"
_EtynAdminState_Object = MibTableColumn
etynAdminState = _EtynAdminState_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 20, 1, 4, 1, 2),
    _EtynAdminState_Type()
)
etynAdminState.setMaxAccess("read-create")
if mibBuilder.loadTexts:
    etynAdminState.setStatus("current")
if mibBuilder.loadTexts:
    etynAdminState.setDescription("Specifies the admin state of the entity. As to different object, the consequent aciton may be different, e.g. it may shutdown the port, or insert maintenance signal.")
_EtynOperationalState_Type = SOtnUpDown
_EtynOperationalState_Object = MibTableColumn
etynOperationalState = _EtynOperationalState_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 20, 1, 4, 1, 3),
    _EtynOperationalState_Type()
)
etynOperationalState.setMaxAccess("read-only")
if mibBuilder.loadTexts:
    etynOperationalState.setStatus("current")
if mibBuilder.loadTexts:
    etynOperationalState.setDescription("The operational state of the NE.")


class _EtynAvailabilityState_Type(StAvailabilityState):
    """Custom type etynAvailabilityState based on StAvailabilityState"""
    defaultValue = 0


_EtynAvailabilityState_Type.__name__ = "StAvailabilityState"
_EtynAvailabilityState_Object = MibTableColumn
etynAvailabilityState = _EtynAvailabilityState_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 20, 1, 4, 1, 4),
    _EtynAvailabilityState_Type()
)
etynAvailabilityState.setMaxAccess("read-only")
if mibBuilder.loadTexts:
    etynAvailabilityState.setStatus("current")
if mibBuilder.loadTexts:
    etynAvailabilityState.setDescription("Avalaibility Status is to qualify the operational, usage and/or administrative state attributes")
_EtynAlarmProfile_Type = Integer32
_EtynAlarmProfile_Object = MibTableColumn
etynAlarmProfile = _EtynAlarmProfile_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 20, 1, 4, 1, 5),
    _EtynAlarmProfile_Type()
)
etynAlarmProfile.setMaxAccess("read-create")
if mibBuilder.loadTexts:
    etynAlarmProfile.setStatus("current")
if mibBuilder.loadTexts:
    etynAlarmProfile.setDescription("Specifies the alarm profile table for the entity.")
_EtynPMProfile_Type = Integer32
_EtynPMProfile_Object = MibTableColumn
etynPMProfile = _EtynPMProfile_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 20, 1, 4, 1, 6),
    _EtynPMProfile_Type()
)
etynPMProfile.setMaxAccess("read-create")
if mibBuilder.loadTexts:
    etynPMProfile.setStatus("current")
if mibBuilder.loadTexts:
    etynPMProfile.setDescription("Specifies the alarm profile table for the entity.")


class _EtynLoopBack_Type(SLoopBackMode):
    """Custom type etynLoopBack based on SLoopBackMode"""
    defaultValue = 0


_EtynLoopBack_Type.__name__ = "SLoopBackMode"
_EtynLoopBack_Object = MibTableColumn
etynLoopBack = _EtynLoopBack_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 20, 1, 4, 1, 7),
    _EtynLoopBack_Type()
)
etynLoopBack.setMaxAccess("read-create")
if mibBuilder.loadTexts:
    etynLoopBack.setStatus("current")
if mibBuilder.loadTexts:
    etynLoopBack.setDescription(" ")


class _EtynUPIvalue_Type(Integer32):
    """Custom type etynUPIvalue based on Integer32"""
    defaultValue = 1

    subtypeSpec = Integer32.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        SingleValueConstraint(
            *(1,
              2)
        )
    )
    namedValues = NamedValues(
        *(("gsupp43", 1),
          ("g709", 2))
    )


_EtynUPIvalue_Type.__name__ = "Integer32"
_EtynUPIvalue_Object = MibTableColumn
etynUPIvalue = _EtynUPIvalue_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 20, 1, 4, 1, 8),
    _EtynUPIvalue_Type()
)
etynUPIvalue.setMaxAccess("read-create")
if mibBuilder.loadTexts:
    etynUPIvalue.setStatus("current")
if mibBuilder.loadTexts:
    etynUPIvalue.setDescription("Specifies the GFP UPI values transmitted for a TGLAN with TRANSMPA=PREAMBLE. - GSUPP43 - 0xFD for data frames and 0xFE for ordered sets. Provides compatibility with ITU-T Supplemental43 recommendation. - G709 - 0x13 for data frames and 0x14 for ordered sets. Provides compatibility with ITU-T G.709 recommendation values first introduced in Amendment 3.")


class _EtynMapMode_Type(SMapMode):
    """Custom type etynMapMode based on SMapMode"""
    defaultValue = 3


_EtynMapMode_Type.__name__ = "SMapMode"
_EtynMapMode_Object = MibTableColumn
etynMapMode = _EtynMapMode_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 20, 1, 4, 1, 9),
    _EtynMapMode_Type()
)
etynMapMode.setMaxAccess("read-create")
if mibBuilder.loadTexts:
    etynMapMode.setStatus("current")
if mibBuilder.loadTexts:
    etynMapMode.setDescription("Specifies the mapping mode from client to OPU/ODU, such as: (1) STMn/OCn: AMP, BMP (2) 10GE: GFP-F, GFP-Fp, BMP (3) 100GE/40GE: GMP, GFP-F ...")
_EtynExtChannel_Type = DisplayString
_EtynExtChannel_Object = MibTableColumn
etynExtChannel = _EtynExtChannel_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 20, 1, 4, 1, 10),
    _EtynExtChannel_Type()
)
etynExtChannel.setMaxAccess("read-create")
if mibBuilder.loadTexts:
    etynExtChannel.setStatus("current")
if mibBuilder.loadTexts:
    etynExtChannel.setDescription("Specifies the DWDM frequency at which the interface will operate when the supporting pluggable for the facility is a tunable DWDM optics. This parameter is used when directly connecting the module to a separate network element, rather than connecting to an optical multiplexer/demultiplex module within the same network element.")


class _EtynClientShutdown_Type(SOtnYesNo):
    """Custom type etynClientShutdown based on SOtnYesNo"""
    defaultValue = 0


_EtynClientShutdown_Type.__name__ = "SOtnYesNo"
_EtynClientShutdown_Object = MibTableColumn
etynClientShutdown = _EtynClientShutdown_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 20, 1, 4, 1, 11),
    _EtynClientShutdown_Type()
)
etynClientShutdown.setMaxAccess("read-create")
if mibBuilder.loadTexts:
    etynClientShutdown.setStatus("current")
if mibBuilder.loadTexts:
    etynClientShutdown.setDescription("Setting the value to YES will shut down the output client port side laser on the transponder instead of sending a maintenance signal (e.g. AIS or LF depending on the type of facility provisioned) when there is a signal failure detected on the line side of a transponder. This value is typically used with external client side protection. Setting the value to NO will not shut down the laser on the port side of the transponder. When this value is set standard maintenance signaling will be used. Setting this value to NA will disable the protection functionality.")


class _EtynClientShutdownHoldoffTimer_Type(Integer32):
    """Custom type etynClientShutdownHoldoffTimer based on Integer32"""
    defaultValue = 0


_EtynClientShutdownHoldoffTimer_Type.__name__ = "Integer32"
_EtynClientShutdownHoldoffTimer_Object = MibTableColumn
etynClientShutdownHoldoffTimer = _EtynClientShutdownHoldoffTimer_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 20, 1, 4, 1, 12),
    _EtynClientShutdownHoldoffTimer_Type()
)
etynClientShutdownHoldoffTimer.setMaxAccess("read-create")
if mibBuilder.loadTexts:
    etynClientShutdownHoldoffTimer.setStatus("current")
if mibBuilder.loadTexts:
    etynClientShutdownHoldoffTimer.setDescription("Specifies a hold-off time between 60 msec and 1000 msec in increments of 5 msec before a client port side laser shutdown upon a defect detected at line side. If no additional hold-off time for a port side laser shutdown is desired, then the value shall be set to zero. Note: This parameter is only supported when value of parameter 'ClientShutDown' is yes")


class _EtynNearEndALS_Type(SOtnYesNo):
    """Custom type etynNearEndALS based on SOtnYesNo"""
    defaultValue = 0


_EtynNearEndALS_Type.__name__ = "SOtnYesNo"
_EtynNearEndALS_Object = MibTableColumn
etynNearEndALS = _EtynNearEndALS_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 20, 1, 4, 1, 13),
    _EtynNearEndALS_Type()
)
etynNearEndALS.setMaxAccess("read-create")
if mibBuilder.loadTexts:
    etynNearEndALS.setStatus("current")
if mibBuilder.loadTexts:
    etynNearEndALS.setDescription("This parameter, when set to YES, will shut down port side laser upon a port side incoming failure (such as LOS/LOF/LOSYNC).")


class _Etyn8023bmFEC_Type(SOtnEnabled):
    """Custom type etyn8023bmFEC based on SOtnEnabled"""
    defaultValue = 0


_Etyn8023bmFEC_Type.__name__ = "SOtnEnabled"
_Etyn8023bmFEC_Object = MibTableColumn
etyn8023bmFEC = _Etyn8023bmFEC_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 20, 1, 4, 1, 14),
    _Etyn8023bmFEC_Type()
)
etyn8023bmFEC.setMaxAccess("read-create")
if mibBuilder.loadTexts:
    etyn8023bmFEC.setStatus("current")
if mibBuilder.loadTexts:
    etyn8023bmFEC.setDescription("enable/disable 100GE FEC functionality. Note: for SR4 100GE, according to 802.3bm standard, it should be always enabled")
_EtynRowStatus_Type = RowStatus
_EtynRowStatus_Object = MibTableColumn
etynRowStatus = _EtynRowStatus_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 20, 1, 4, 1, 15),
    _EtynRowStatus_Type()
)
etynRowStatus.setMaxAccess("read-create")
if mibBuilder.loadTexts:
    etynRowStatus.setStatus("current")
if mibBuilder.loadTexts:
    etynRowStatus.setDescription("sETYnTable RowStatus")


class _EtynFectype_Type(Integer32):
    """Custom type etynFectype based on Integer32"""
    defaultValue = 0

    subtypeSpec = Integer32.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        SingleValueConstraint(
            *(0,
              1)
        )
    )
    namedValues = NamedValues(
        *(("none", 0),
          ("rs", 1))
    )


_EtynFectype_Type.__name__ = "Integer32"
_EtynFectype_Object = MibTableColumn
etynFectype = _EtynFectype_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 20, 1, 4, 1, 16),
    _EtynFectype_Type()
)
etynFectype.setMaxAccess("read-create")
if mibBuilder.loadTexts:
    etynFectype.setStatus("current")
_SOCnSTMnTable_Object = MibTable
sOCnSTMnTable = _SOCnSTMnTable_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 20, 1, 5)
)
if mibBuilder.loadTexts:
    sOCnSTMnTable.setStatus("current")
if mibBuilder.loadTexts:
    sOCnSTMnTable.setDescription("OCn & STMn Table")
_SOCnSTMnEntry_Object = MibTableRow
sOCnSTMnEntry = _SOCnSTMnEntry_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 20, 1, 5, 1)
)
sOCnSTMnEntry.setIndexNames(
    (0, "ST-COMMON-MIB", "shelfId"),
    (0, "ST-COMMON-MIB", "slotNo"),
    (0, "ST-COMMON-MIB", "subSlotNo"),
    (0, "ST-COMMON-MIB", "portNo"),
    (0, "ST-COMMON-MIB", "subPortNo"),
)
if mibBuilder.loadTexts:
    sOCnSTMnEntry.setStatus("current")
if mibBuilder.loadTexts:
    sOCnSTMnEntry.setDescription(" ")
_OcnstmnType_Type = SOcStmType
_OcnstmnType_Object = MibTableColumn
ocnstmnType = _OcnstmnType_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 20, 1, 5, 1, 1),
    _OcnstmnType_Type()
)
ocnstmnType.setMaxAccess("read-create")
if mibBuilder.loadTexts:
    ocnstmnType.setStatus("current")
if mibBuilder.loadTexts:
    ocnstmnType.setDescription("Specifies the admin state of the entity. As to different object, the consequent aciton may be different, e.g. it may shutdown the port, or insert maintenance signal.")


class _OcnstmnAdminState_Type(SOtnEnabled):
    """Custom type ocnstmnAdminState based on SOtnEnabled"""
    defaultValue = 1


_OcnstmnAdminState_Type.__name__ = "SOtnEnabled"
_OcnstmnAdminState_Object = MibTableColumn
ocnstmnAdminState = _OcnstmnAdminState_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 20, 1, 5, 1, 2),
    _OcnstmnAdminState_Type()
)
ocnstmnAdminState.setMaxAccess("read-create")
if mibBuilder.loadTexts:
    ocnstmnAdminState.setStatus("current")
if mibBuilder.loadTexts:
    ocnstmnAdminState.setDescription("Specifies the admin state of the entity. As to different object, the consequent aciton may be different, e.g. it may shutdown the port, or insert maintenance signal.")


class _OcnstmnOperationalState_Type(SOtnUpDown):
    """Custom type ocnstmnOperationalState based on SOtnUpDown"""
    defaultValue = 1


_OcnstmnOperationalState_Type.__name__ = "SOtnUpDown"
_OcnstmnOperationalState_Object = MibTableColumn
ocnstmnOperationalState = _OcnstmnOperationalState_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 20, 1, 5, 1, 3),
    _OcnstmnOperationalState_Type()
)
ocnstmnOperationalState.setMaxAccess("read-only")
if mibBuilder.loadTexts:
    ocnstmnOperationalState.setStatus("current")
if mibBuilder.loadTexts:
    ocnstmnOperationalState.setDescription("The operational state of the NE.")


class _OcnstmnAvailabilityState_Type(StAvailabilityState):
    """Custom type ocnstmnAvailabilityState based on StAvailabilityState"""
    defaultValue = 0


_OcnstmnAvailabilityState_Type.__name__ = "StAvailabilityState"
_OcnstmnAvailabilityState_Object = MibTableColumn
ocnstmnAvailabilityState = _OcnstmnAvailabilityState_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 20, 1, 5, 1, 4),
    _OcnstmnAvailabilityState_Type()
)
ocnstmnAvailabilityState.setMaxAccess("read-only")
if mibBuilder.loadTexts:
    ocnstmnAvailabilityState.setStatus("current")
if mibBuilder.loadTexts:
    ocnstmnAvailabilityState.setDescription("Avalaibility Status is to qualify the operational, usage and/or administrative state attributes")
_OcnstmnAlarmProfile_Type = Integer32
_OcnstmnAlarmProfile_Object = MibTableColumn
ocnstmnAlarmProfile = _OcnstmnAlarmProfile_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 20, 1, 5, 1, 5),
    _OcnstmnAlarmProfile_Type()
)
ocnstmnAlarmProfile.setMaxAccess("read-create")
if mibBuilder.loadTexts:
    ocnstmnAlarmProfile.setStatus("current")
if mibBuilder.loadTexts:
    ocnstmnAlarmProfile.setDescription("Specifies the alarm profile table for the entity.")
_OcnstmnPMProfile_Type = Integer32
_OcnstmnPMProfile_Object = MibTableColumn
ocnstmnPMProfile = _OcnstmnPMProfile_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 20, 1, 5, 1, 6),
    _OcnstmnPMProfile_Type()
)
ocnstmnPMProfile.setMaxAccess("read-create")
if mibBuilder.loadTexts:
    ocnstmnPMProfile.setStatus("current")
if mibBuilder.loadTexts:
    ocnstmnPMProfile.setDescription("Specifies the alarm profile table for the entity.")


class _OcnstmnLoopBack_Type(SLoopBackMode):
    """Custom type ocnstmnLoopBack based on SLoopBackMode"""
    defaultValue = 0


_OcnstmnLoopBack_Type.__name__ = "SLoopBackMode"
_OcnstmnLoopBack_Object = MibTableColumn
ocnstmnLoopBack = _OcnstmnLoopBack_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 20, 1, 5, 1, 7),
    _OcnstmnLoopBack_Type()
)
ocnstmnLoopBack.setMaxAccess("read-create")
if mibBuilder.loadTexts:
    ocnstmnLoopBack.setStatus("current")
if mibBuilder.loadTexts:
    ocnstmnLoopBack.setDescription(" ")


class _OcnstmnMapMode_Type(SMapMode):
    """Custom type ocnstmnMapMode based on SMapMode"""
    defaultValue = 0


_OcnstmnMapMode_Type.__name__ = "SMapMode"
_OcnstmnMapMode_Object = MibTableColumn
ocnstmnMapMode = _OcnstmnMapMode_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 20, 1, 5, 1, 8),
    _OcnstmnMapMode_Type()
)
ocnstmnMapMode.setMaxAccess("read-create")
if mibBuilder.loadTexts:
    ocnstmnMapMode.setStatus("current")
if mibBuilder.loadTexts:
    ocnstmnMapMode.setDescription("Specifies the mapping mode from client to OPU/ODU, such as: (1) STMn/OCn: AMP, BMP (2) 10GE: GFP-F, GFP-Fp, BMP (3) 100GE/40GE: GMP, GFP-F ...")


class _OcnstmnClientShutdown_Type(SOtnYesNo):
    """Custom type ocnstmnClientShutdown based on SOtnYesNo"""
    defaultValue = 0


_OcnstmnClientShutdown_Type.__name__ = "SOtnYesNo"
_OcnstmnClientShutdown_Object = MibTableColumn
ocnstmnClientShutdown = _OcnstmnClientShutdown_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 20, 1, 5, 1, 9),
    _OcnstmnClientShutdown_Type()
)
ocnstmnClientShutdown.setMaxAccess("read-create")
if mibBuilder.loadTexts:
    ocnstmnClientShutdown.setStatus("current")
if mibBuilder.loadTexts:
    ocnstmnClientShutdown.setDescription("Setting the value to YES will shut down the output client port side laser on the transponder instead of sending a maintenance signal (e.g. AIS or LF depending on the type of facility provisioned) when there is a signal failure detected on the line side of a transponder. This value is typically used with external client side protection. Setting the value to NO will not shut down the laser on the port side of the transponder. When this value is set standard maintenance signaling will be used. Setting this value to NA will disable the protection functionality.")


class _OcnstmnClientShutdownHoldoffTimer_Type(Integer32):
    """Custom type ocnstmnClientShutdownHoldoffTimer based on Integer32"""
    defaultValue = 0


_OcnstmnClientShutdownHoldoffTimer_Type.__name__ = "Integer32"
_OcnstmnClientShutdownHoldoffTimer_Object = MibTableColumn
ocnstmnClientShutdownHoldoffTimer = _OcnstmnClientShutdownHoldoffTimer_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 20, 1, 5, 1, 10),
    _OcnstmnClientShutdownHoldoffTimer_Type()
)
ocnstmnClientShutdownHoldoffTimer.setMaxAccess("read-create")
if mibBuilder.loadTexts:
    ocnstmnClientShutdownHoldoffTimer.setStatus("current")
if mibBuilder.loadTexts:
    ocnstmnClientShutdownHoldoffTimer.setDescription("Specifies a hold-off time between 60 msec and 1000 msec in increments of 5 msec before a client port side laser shutdown upon a defect detected at line side. If no additional hold-off time for a port side laser shutdown is desired, then the value shall be set to zero. Note: This parameter is only supported when value of parameter 'ClientShutDown' is yes")


class _OcnstmnNearEndALS_Type(SOtnYesNo):
    """Custom type ocnstmnNearEndALS based on SOtnYesNo"""
    defaultValue = 0


_OcnstmnNearEndALS_Type.__name__ = "SOtnYesNo"
_OcnstmnNearEndALS_Object = MibTableColumn
ocnstmnNearEndALS = _OcnstmnNearEndALS_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 20, 1, 5, 1, 11),
    _OcnstmnNearEndALS_Type()
)
ocnstmnNearEndALS.setMaxAccess("read-create")
if mibBuilder.loadTexts:
    ocnstmnNearEndALS.setStatus("current")
if mibBuilder.loadTexts:
    ocnstmnNearEndALS.setDescription("This parameter, when set to YES, will shut down port side laser upon a port side incoming failure (such as LOS/LOF/LOSYNC).")
_OcnstmnRowStatus_Type = RowStatus
_OcnstmnRowStatus_Object = MibTableColumn
ocnstmnRowStatus = _OcnstmnRowStatus_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 20, 1, 5, 1, 12),
    _OcnstmnRowStatus_Type()
)
ocnstmnRowStatus.setMaxAccess("read-create")
if mibBuilder.loadTexts:
    ocnstmnRowStatus.setStatus("current")
if mibBuilder.loadTexts:
    ocnstmnRowStatus.setDescription("sOCnSTMnTable RowStatus")


class _OcnstmnTIMDetectionMode_Type(TraceIdentifierSDHMsgType):
    """Custom type ocnstmnTIMDetectionMode based on TraceIdentifierSDHMsgType"""
    defaultValue = 0


_OcnstmnTIMDetectionMode_Type.__name__ = "TraceIdentifierSDHMsgType"
_OcnstmnTIMDetectionMode_Object = MibTableColumn
ocnstmnTIMDetectionMode = _OcnstmnTIMDetectionMode_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 20, 1, 5, 1, 13),
    _OcnstmnTIMDetectionMode_Type()
)
ocnstmnTIMDetectionMode.setMaxAccess("read-write")
if mibBuilder.loadTexts:
    ocnstmnTIMDetectionMode.setStatus("current")
if mibBuilder.loadTexts:
    ocnstmnTIMDetectionMode.setDescription("Enables 16-bytes, 64-bytes or disables detection of dTIM defect for RS non-intrusive monitoring in ingress direction.")


class _OcnstmnTraceIdExpected_Type(TraceIdentifierSDH):
    """Custom type ocnstmnTraceIdExpected based on TraceIdentifierSDH"""
    defaultHexValue = "89000000000000000000000000000000"


_OcnstmnTraceIdExpected_Type.__name__ = "TraceIdentifierSDH"
_OcnstmnTraceIdExpected_Object = MibTableColumn
ocnstmnTraceIdExpected = _OcnstmnTraceIdExpected_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 20, 1, 5, 1, 14),
    _OcnstmnTraceIdExpected_Type()
)
ocnstmnTraceIdExpected.setMaxAccess("read-write")
if mibBuilder.loadTexts:
    ocnstmnTraceIdExpected.setStatus("current")
if mibBuilder.loadTexts:
    ocnstmnTraceIdExpected.setDescription("Expected RS trace identifier in ingress direction. Not effective if rsIngressTIMDetectionMode is 'off'.")


class _OcnstmnTraceIdReceived_Type(TraceIdentifierSDH):
    """Custom type ocnstmnTraceIdReceived based on TraceIdentifierSDH"""
    defaultHexValue = "89000000000000000000000000000000"


_OcnstmnTraceIdReceived_Type.__name__ = "TraceIdentifierSDH"
_OcnstmnTraceIdReceived_Object = MibTableColumn
ocnstmnTraceIdReceived = _OcnstmnTraceIdReceived_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 20, 1, 5, 1, 15),
    _OcnstmnTraceIdReceived_Type()
)
ocnstmnTraceIdReceived.setMaxAccess("read-only")
if mibBuilder.loadTexts:
    ocnstmnTraceIdReceived.setStatus("current")
if mibBuilder.loadTexts:
    ocnstmnTraceIdReceived.setDescription("Received RS trace identifier in ingress direction.")
_SODUkPTable_Object = MibTable
sODUkPTable = _SODUkPTable_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 20, 1, 7)
)
if mibBuilder.loadTexts:
    sODUkPTable.setStatus("current")
if mibBuilder.loadTexts:
    sODUkPTable.setDescription("ODUkP Table")
_SODUkPEntry_Object = MibTableRow
sODUkPEntry = _SODUkPEntry_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 20, 1, 7, 1)
)
sODUkPEntry.setIndexNames(
    (0, "ST-COMMON-MIB", "shelfId"),
    (0, "ST-COMMON-MIB", "slotNo"),
    (0, "ST-COMMON-MIB", "subSlotNo"),
    (0, "ST-COMMON-MIB", "portNo"),
    (0, "ST-COMMON-MIB", "subPortNo"),
    (0, "ST-OTN-MIB", "hoOduType"),
    (0, "ST-OTN-MIB", "hoOduId"),
    (0, "ST-OTN-MIB", "loOduType"),
    (0, "ST-OTN-MIB", "loOduId"),
)
if mibBuilder.loadTexts:
    sODUkPEntry.setStatus("current")
if mibBuilder.loadTexts:
    sODUkPEntry.setDescription(" ")
_HoOduType_Type = SOduType
_HoOduType_Object = MibTableColumn
hoOduType = _HoOduType_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 20, 1, 7, 1, 1),
    _HoOduType_Type()
)
hoOduType.setMaxAccess("not-accessible")
if mibBuilder.loadTexts:
    hoOduType.setStatus("current")
if mibBuilder.loadTexts:
    hoOduType.setDescription(" ")
_HoOduId_Type = SOduId
_HoOduId_Object = MibTableColumn
hoOduId = _HoOduId_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 20, 1, 7, 1, 2),
    _HoOduId_Type()
)
hoOduId.setMaxAccess("not-accessible")
if mibBuilder.loadTexts:
    hoOduId.setStatus("current")
if mibBuilder.loadTexts:
    hoOduId.setDescription(" ")
_LoOduType_Type = SOduType
_LoOduType_Object = MibTableColumn
loOduType = _LoOduType_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 20, 1, 7, 1, 3),
    _LoOduType_Type()
)
loOduType.setMaxAccess("not-accessible")
if mibBuilder.loadTexts:
    loOduType.setStatus("current")
if mibBuilder.loadTexts:
    loOduType.setDescription(" ")
_LoOduId_Type = SOduId
_LoOduId_Object = MibTableColumn
loOduId = _LoOduId_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 20, 1, 7, 1, 4),
    _LoOduId_Type()
)
loOduId.setMaxAccess("not-accessible")
if mibBuilder.loadTexts:
    loOduId.setStatus("current")
if mibBuilder.loadTexts:
    loOduId.setDescription(" ")


class _OdukpAdminState_Type(SOtnEnabled):
    """Custom type odukpAdminState based on SOtnEnabled"""
    defaultValue = 1


_OdukpAdminState_Type.__name__ = "SOtnEnabled"
_OdukpAdminState_Object = MibTableColumn
odukpAdminState = _OdukpAdminState_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 20, 1, 7, 1, 5),
    _OdukpAdminState_Type()
)
odukpAdminState.setMaxAccess("read-create")
if mibBuilder.loadTexts:
    odukpAdminState.setStatus("current")
if mibBuilder.loadTexts:
    odukpAdminState.setDescription("Specifies the admin state of the entity. As to different object, the consequent aciton may be different, e.g. it may shutdown the port, or insert maintenance signal.")


class _OdukpOperationalState_Type(SOtnUpDown):
    """Custom type odukpOperationalState based on SOtnUpDown"""
    defaultValue = 1


_OdukpOperationalState_Type.__name__ = "SOtnUpDown"
_OdukpOperationalState_Object = MibTableColumn
odukpOperationalState = _OdukpOperationalState_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 20, 1, 7, 1, 6),
    _OdukpOperationalState_Type()
)
odukpOperationalState.setMaxAccess("read-only")
if mibBuilder.loadTexts:
    odukpOperationalState.setStatus("current")
if mibBuilder.loadTexts:
    odukpOperationalState.setDescription("The operational state of the NE.")


class _OdukpAvailabilityState_Type(StAvailabilityState):
    """Custom type odukpAvailabilityState based on StAvailabilityState"""
    defaultValue = 0


_OdukpAvailabilityState_Type.__name__ = "StAvailabilityState"
_OdukpAvailabilityState_Object = MibTableColumn
odukpAvailabilityState = _OdukpAvailabilityState_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 20, 1, 7, 1, 7),
    _OdukpAvailabilityState_Type()
)
odukpAvailabilityState.setMaxAccess("read-only")
if mibBuilder.loadTexts:
    odukpAvailabilityState.setStatus("current")
if mibBuilder.loadTexts:
    odukpAvailabilityState.setDescription("Avalaibility Status is to qualify the operational, usage and/or administrative state attributes")
_OdukpAlarmProfile_Type = Integer32
_OdukpAlarmProfile_Object = MibTableColumn
odukpAlarmProfile = _OdukpAlarmProfile_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 20, 1, 7, 1, 8),
    _OdukpAlarmProfile_Type()
)
odukpAlarmProfile.setMaxAccess("read-create")
if mibBuilder.loadTexts:
    odukpAlarmProfile.setStatus("current")
if mibBuilder.loadTexts:
    odukpAlarmProfile.setDescription("Specifies the alarm profile table for the entity.")
_OdukpPMProfile_Type = Integer32
_OdukpPMProfile_Object = MibTableColumn
odukpPMProfile = _OdukpPMProfile_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 20, 1, 7, 1, 9),
    _OdukpPMProfile_Type()
)
odukpPMProfile.setMaxAccess("read-create")
if mibBuilder.loadTexts:
    odukpPMProfile.setStatus("current")
if mibBuilder.loadTexts:
    odukpPMProfile.setDescription("Specifies the alarm profile table for the entity.")


class _OdukpExpSAPI_Type(OctetString):
    """Custom type odukpExpSAPI based on OctetString"""
    subtypeSpec = OctetString.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        ValueSizeConstraint(16, 16),
    )
    fixedLength = 16


_OdukpExpSAPI_Type.__name__ = "OctetString"
_OdukpExpSAPI_Object = MibTableColumn
odukpExpSAPI = _OdukpExpSAPI_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 20, 1, 7, 1, 10),
    _OdukpExpSAPI_Type()
)
odukpExpSAPI.setMaxAccess("read-create")
if mibBuilder.loadTexts:
    odukpExpSAPI.setStatus("current")
if mibBuilder.loadTexts:
    odukpExpSAPI.setDescription("Specifies 16 characters representing the expected SAPI.")


class _OdukpExpDAPI_Type(OctetString):
    """Custom type odukpExpDAPI based on OctetString"""
    subtypeSpec = OctetString.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        ValueSizeConstraint(16, 16),
    )
    fixedLength = 16


_OdukpExpDAPI_Type.__name__ = "OctetString"
_OdukpExpDAPI_Object = MibTableColumn
odukpExpDAPI = _OdukpExpDAPI_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 20, 1, 7, 1, 11),
    _OdukpExpDAPI_Type()
)
odukpExpDAPI.setMaxAccess("read-create")
if mibBuilder.loadTexts:
    odukpExpDAPI.setStatus("current")
if mibBuilder.loadTexts:
    odukpExpDAPI.setDescription("Specifies 16 characters representing the expected DAPI.")


class _OdukpExpOperator_Type(OctetString):
    """Custom type odukpExpOperator based on OctetString"""
    subtypeSpec = OctetString.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        ValueSizeConstraint(32, 32),
    )
    fixedLength = 32


_OdukpExpOperator_Type.__name__ = "OctetString"
_OdukpExpOperator_Object = MibTableColumn
odukpExpOperator = _OdukpExpOperator_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 20, 1, 7, 1, 12),
    _OdukpExpOperator_Type()
)
odukpExpOperator.setMaxAccess("read-create")
if mibBuilder.loadTexts:
    odukpExpOperator.setStatus("current")
if mibBuilder.loadTexts:
    odukpExpOperator.setDescription("Specifies 32 characters representing the expected value for the Operator Specific area of the TTI.")


class _OdukpTxSAPI_Type(OctetString):
    """Custom type odukpTxSAPI based on OctetString"""
    subtypeSpec = OctetString.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        ValueSizeConstraint(16, 16),
    )
    fixedLength = 16


_OdukpTxSAPI_Type.__name__ = "OctetString"
_OdukpTxSAPI_Object = MibTableColumn
odukpTxSAPI = _OdukpTxSAPI_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 20, 1, 7, 1, 13),
    _OdukpTxSAPI_Type()
)
odukpTxSAPI.setMaxAccess("read-create")
if mibBuilder.loadTexts:
    odukpTxSAPI.setStatus("current")
if mibBuilder.loadTexts:
    odukpTxSAPI.setDescription("Specifies 16 characters representing the transmitted SAPI.")


class _OdukpTxDAPI_Type(OctetString):
    """Custom type odukpTxDAPI based on OctetString"""
    subtypeSpec = OctetString.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        ValueSizeConstraint(16, 16),
    )
    fixedLength = 16


_OdukpTxDAPI_Type.__name__ = "OctetString"
_OdukpTxDAPI_Object = MibTableColumn
odukpTxDAPI = _OdukpTxDAPI_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 20, 1, 7, 1, 14),
    _OdukpTxDAPI_Type()
)
odukpTxDAPI.setMaxAccess("read-create")
if mibBuilder.loadTexts:
    odukpTxDAPI.setStatus("current")
if mibBuilder.loadTexts:
    odukpTxDAPI.setDescription("Specifies 16 characters representing the transmitted DAPI.")


class _OdukpTxOperator_Type(OctetString):
    """Custom type odukpTxOperator based on OctetString"""
    subtypeSpec = OctetString.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        ValueSizeConstraint(32, 32),
    )
    fixedLength = 32


_OdukpTxOperator_Type.__name__ = "OctetString"
_OdukpTxOperator_Object = MibTableColumn
odukpTxOperator = _OdukpTxOperator_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 20, 1, 7, 1, 15),
    _OdukpTxOperator_Type()
)
odukpTxOperator.setMaxAccess("read-create")
if mibBuilder.loadTexts:
    odukpTxOperator.setStatus("current")
if mibBuilder.loadTexts:
    odukpTxOperator.setDescription("Specifies 32 characters representing the transmitted value in the Operator Specific area of the TTI.")


class _OdukpRxSAPI_Type(OctetString):
    """Custom type odukpRxSAPI based on OctetString"""
    subtypeSpec = OctetString.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        ValueSizeConstraint(16, 16),
    )
    fixedLength = 16


_OdukpRxSAPI_Type.__name__ = "OctetString"
_OdukpRxSAPI_Object = MibTableColumn
odukpRxSAPI = _OdukpRxSAPI_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 20, 1, 7, 1, 16),
    _OdukpRxSAPI_Type()
)
odukpRxSAPI.setMaxAccess("read-only")
if mibBuilder.loadTexts:
    odukpRxSAPI.setStatus("current")
if mibBuilder.loadTexts:
    odukpRxSAPI.setDescription("Specifies 16 characters representing the received SAPI.")


class _OdukpRxDAPI_Type(OctetString):
    """Custom type odukpRxDAPI based on OctetString"""
    subtypeSpec = OctetString.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        ValueSizeConstraint(16, 16),
    )
    fixedLength = 16


_OdukpRxDAPI_Type.__name__ = "OctetString"
_OdukpRxDAPI_Object = MibTableColumn
odukpRxDAPI = _OdukpRxDAPI_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 20, 1, 7, 1, 17),
    _OdukpRxDAPI_Type()
)
odukpRxDAPI.setMaxAccess("read-only")
if mibBuilder.loadTexts:
    odukpRxDAPI.setStatus("current")
if mibBuilder.loadTexts:
    odukpRxDAPI.setDescription("Specifies 16 characters representing the received DAPI.")


class _OdukpRxOperator_Type(OctetString):
    """Custom type odukpRxOperator based on OctetString"""
    subtypeSpec = OctetString.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        ValueSizeConstraint(32, 32),
    )
    fixedLength = 32


_OdukpRxOperator_Type.__name__ = "OctetString"
_OdukpRxOperator_Object = MibTableColumn
odukpRxOperator = _OdukpRxOperator_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 20, 1, 7, 1, 18),
    _OdukpRxOperator_Type()
)
odukpRxOperator.setMaxAccess("read-only")
if mibBuilder.loadTexts:
    odukpRxOperator.setStatus("current")
if mibBuilder.loadTexts:
    odukpRxOperator.setDescription("Specifies 32 characters representing the received value in the Operator Specific area of the TTI.")


class _OdukpTIMDefectMode_Type(Integer32):
    """Custom type odukpTIMDefectMode based on Integer32"""
    defaultValue = 4

    subtypeSpec = Integer32.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        SingleValueConstraint(
            *(0,
              1,
              2,
              3,
              4,
              5,
              6,
              7)
        )
    )
    namedValues = NamedValues(
        *(("none", 0),
          ("sapi", 1),
          ("dapi", 2),
          ("oper", 3),
          ("sapi-dapi", 4),
          ("sapi-dapi-oper", 5),
          ("sapi-oper", 6),
          ("dapi-oper", 7))
    )


_OdukpTIMDefectMode_Type.__name__ = "Integer32"
_OdukpTIMDefectMode_Object = MibTableColumn
odukpTIMDefectMode = _OdukpTIMDefectMode_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 20, 1, 7, 1, 19),
    _OdukpTIMDefectMode_Type()
)
odukpTIMDefectMode.setMaxAccess("read-create")
if mibBuilder.loadTexts:
    odukpTIMDefectMode.setStatus("current")
if mibBuilder.loadTexts:
    odukpTIMDefectMode.setDescription("Specifies the definition of the trace identifier mismatch alarm by identifying which portion(s) of the TTI message are compared for trace identifier mismatch purposes. - NONE (no trace identifier mismatch defect) - OPER (Operator Specific mismatch only), - SAPI (SAPI mismatch only), - DAPI (DAPI mismatch only), - SAPI_DAPI (SAPI + DAPI Mismatches), - SAPI_OPER (SAPI + OPER mismatches), - DAPI_OPER (DAPI + OPER mismatches), - SAPI_DAPI_OPER (SAPI + DAPI + OPER mismatches).")


class _OdukpTIMActDis_Type(SOtnTruthValue):
    """Custom type odukpTIMActDis based on SOtnTruthValue"""
    defaultValue = 1


_OdukpTIMActDis_Type.__name__ = "SOtnTruthValue"
_OdukpTIMActDis_Object = MibTableColumn
odukpTIMActDis = _OdukpTIMActDis_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 20, 1, 7, 1, 20),
    _OdukpTIMActDis_Type()
)
odukpTIMActDis.setMaxAccess("read-create")
if mibBuilder.loadTexts:
    odukpTIMActDis.setStatus("current")
if mibBuilder.loadTexts:
    odukpTIMActDis.setDescription("Specifies the monitored trace identification mode. - 'enabled': enable trace monitoring and perform consequent actions, including protection switches and AIS maintenance signal insertion when there is a trace mismatch. - 'disabled': to enable trace monitoring but not perform consequent actions, including protection switches and AIS maintenance signal insertion when there is a trace mismatch. - Enter DISABLED to disable trace monitoring. Note that AIS insertion is only performed on a terminated entity.")


class _OdukpDegradeInterval_Type(Integer32):
    """Custom type odukpDegradeInterval based on Integer32"""
    defaultValue = 2

    subtypeSpec = Integer32.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        ValueRangeConstraint(2, 8),
    )


_OdukpDegradeInterval_Type.__name__ = "Integer32"
_OdukpDegradeInterval_Object = MibTableColumn
odukpDegradeInterval = _OdukpDegradeInterval_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 20, 1, 7, 1, 21),
    _OdukpDegradeInterval_Type()
)
odukpDegradeInterval.setMaxAccess("read-create")
if mibBuilder.loadTexts:
    odukpDegradeInterval.setStatus("current")
if mibBuilder.loadTexts:
    odukpDegradeInterval.setDescription("Indicates the consecutive number of one second intervals with the number of detected block errors exceeding the block error threshold for each of those seconds for the purposes of BER of Signal Degrade detection.")


class _OdukpDegradeThreshold_Type(Integer32):
    """Custom type odukpDegradeThreshold based on Integer32"""
    defaultValue = 82026


_OdukpDegradeThreshold_Type.__name__ = "Integer32"
_OdukpDegradeThreshold_Object = MibTableColumn
odukpDegradeThreshold = _OdukpDegradeThreshold_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 20, 1, 7, 1, 22),
    _OdukpDegradeThreshold_Type()
)
odukpDegradeThreshold.setMaxAccess("read-create")
if mibBuilder.loadTexts:
    odukpDegradeThreshold.setStatus("current")
if mibBuilder.loadTexts:
    odukpDegradeThreshold.setDescription("Indicates the threshold number of block errors at which a one second interval will be considered degraded for the purposes of BER signal degrade detection. some examples of possible values are: 1 to 20421, default of 3064 (OTU1) 1 to 82026, default of 12304 (OTU2) 1 to 84986, default of 12748 (OTU2e) 1 to 856388, default of 128459 (OTU4) ...")


class _OdukpDMSource_Type(SOtnEnabled):
    """Custom type odukpDMSource based on SOtnEnabled"""
    defaultValue = 1


_OdukpDMSource_Type.__name__ = "SOtnEnabled"
_OdukpDMSource_Object = MibTableColumn
odukpDMSource = _OdukpDMSource_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 20, 1, 7, 1, 23),
    _OdukpDMSource_Type()
)
odukpDMSource.setMaxAccess("read-create")
if mibBuilder.loadTexts:
    odukpDMSource.setStatus("current")
if mibBuilder.loadTexts:
    odukpDMSource.setDescription("Specifies whether or not the entity is acting as the Source of the Delay Measurement function for performance monitoring.")


class _OdukpPrbs_Type(Integer32):
    """Custom type odukpPrbs based on Integer32"""
    defaultValue = 0

    subtypeSpec = Integer32.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        SingleValueConstraint(
            *(0,
              1,
              2,
              3,
              4)
        )
    )
    namedValues = NamedValues(
        *(("none", 0),
          ("rx", 1),
          ("tx", 2),
          ("rxtx", 3),
          ("reset", 4))
    )


_OdukpPrbs_Type.__name__ = "Integer32"
_OdukpPrbs_Object = MibTableColumn
odukpPrbs = _OdukpPrbs_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 20, 1, 7, 1, 24),
    _OdukpPrbs_Type()
)
odukpPrbs.setMaxAccess("read-create")
if mibBuilder.loadTexts:
    odukpPrbs.setStatus("current")
if mibBuilder.loadTexts:
    odukpPrbs.setDescription("none: disable PRBS Tx: enable transmit direction only Rx: enable receive direction only RxTx: enable both transmssion and receive direction Reset: zero PRBS counters and reset timer of Rx, no change for others")


class _OdukpPrbsSync_Type(Integer32):
    """Custom type odukpPrbsSync based on Integer32"""
    defaultValue = 1

    subtypeSpec = Integer32.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        SingleValueConstraint(
            *(0,
              1,
              2,
              3)
        )
    )
    namedValues = NamedValues(
        *(("na", 0),
          ("inSync", 1),
          ("outSync", 2),
          ("errSync", 3))
    )


_OdukpPrbsSync_Type.__name__ = "Integer32"
_OdukpPrbsSync_Object = MibTableColumn
odukpPrbsSync = _OdukpPrbsSync_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 20, 1, 7, 1, 25),
    _OdukpPrbsSync_Type()
)
odukpPrbsSync.setMaxAccess("read-only")
if mibBuilder.loadTexts:
    odukpPrbsSync.setStatus("current")
if mibBuilder.loadTexts:
    odukpPrbsSync.setDescription("Indicates whether PRBS pattern is synchronized since start of PRBSRX. - INSYNC indicates the PRBS pattern is synchronized. - OUTSYNC indicates the PRBS pattern is not synchronized. - ERRSYNC indicates that the PRBS pattern is synchronized, but has lost synchronization and then regained synchronization at some point since the PRBS in the Receive direction was turned ON. - NA indicates that PRBS in receive direction is OFF.")


class _OdukpPrbsTimerDuration_Type(DisplayString):
    """Custom type odukpPrbsTimerDuration based on DisplayString"""
    defaultValue = OctetString("")


_OdukpPrbsTimerDuration_Type.__name__ = "DisplayString"
_OdukpPrbsTimerDuration_Object = MibTableColumn
odukpPrbsTimerDuration = _OdukpPrbsTimerDuration_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 20, 1, 7, 1, 26),
    _OdukpPrbsTimerDuration_Type()
)
odukpPrbsTimerDuration.setMaxAccess("read-only")
if mibBuilder.loadTexts:
    odukpPrbsTimerDuration.setStatus("current")
if mibBuilder.loadTexts:
    odukpPrbsTimerDuration.setDescription("Indicates the amount of time since start of PRBSRX. Reset of PRBSRX will reset the timer.")
_OdukpPrbsCalcBer_Type = Integer32
_OdukpPrbsCalcBer_Object = MibTableColumn
odukpPrbsCalcBer = _OdukpPrbsCalcBer_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 20, 1, 7, 1, 27),
    _OdukpPrbsCalcBer_Type()
)
odukpPrbsCalcBer.setMaxAccess("read-only")
if mibBuilder.loadTexts:
    odukpPrbsCalcBer.setStatus("current")
if mibBuilder.loadTexts:
    odukpPrbsCalcBer.setDescription("Indicates the calculated bit error rate (BER). Values for CALCBER are restricted to integer values that represent the closest negative power of 10. For example, a value of 5 indicates a BER of 10e-5.")
_OdukpPrbsErrorCount_Type = Integer32
_OdukpPrbsErrorCount_Object = MibTableColumn
odukpPrbsErrorCount = _OdukpPrbsErrorCount_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 20, 1, 7, 1, 28),
    _OdukpPrbsErrorCount_Type()
)
odukpPrbsErrorCount.setMaxAccess("read-only")
if mibBuilder.loadTexts:
    odukpPrbsErrorCount.setStatus("current")
if mibBuilder.loadTexts:
    odukpPrbsErrorCount.setDescription("Indicates the bit error count since the start of PRBSRX.")


class _OdukpNull_Type(SOtnEnabled):
    """Custom type odukpNull based on SOtnEnabled"""
    defaultValue = 0


_OdukpNull_Type.__name__ = "SOtnEnabled"
_OdukpNull_Object = MibTableColumn
odukpNull = _OdukpNull_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 20, 1, 7, 1, 29),
    _OdukpNull_Type()
)
odukpNull.setMaxAccess("read-create")
if mibBuilder.loadTexts:
    odukpNull.setStatus("current")
if mibBuilder.loadTexts:
    odukpNull.setDescription("enable/disable NULL functionality")


class _OdukpNIM_Type(SOtnEnabled):
    """Custom type odukpNIM based on SOtnEnabled"""
    defaultValue = 1


_OdukpNIM_Type.__name__ = "SOtnEnabled"
_OdukpNIM_Object = MibTableColumn
odukpNIM = _OdukpNIM_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 20, 1, 7, 1, 30),
    _OdukpNIM_Type()
)
odukpNIM.setMaxAccess("read-create")
if mibBuilder.loadTexts:
    odukpNIM.setStatus("current")
if mibBuilder.loadTexts:
    odukpNIM.setDescription("enable/disable Non intrusive monitoring functionality")


class _OdukpRxPT_Type(Integer32):
    """Custom type odukpRxPT based on Integer32"""
    defaultValue = 255


_OdukpRxPT_Type.__name__ = "Integer32"
_OdukpRxPT_Object = MibTableColumn
odukpRxPT = _OdukpRxPT_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 20, 1, 7, 1, 31),
    _OdukpRxPT_Type()
)
odukpRxPT.setMaxAccess("read-only")
if mibBuilder.loadTexts:
    odukpRxPT.setStatus("current")
if mibBuilder.loadTexts:
    odukpRxPT.setDescription("Identifies the received Payload Type hexadecimal value.")


class _OdukpTxPT_Type(Integer32):
    """Custom type odukpTxPT based on Integer32"""
    defaultValue = 255


_OdukpTxPT_Type.__name__ = "Integer32"
_OdukpTxPT_Object = MibTableColumn
odukpTxPT = _OdukpTxPT_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 20, 1, 7, 1, 32),
    _OdukpTxPT_Type()
)
odukpTxPT.setMaxAccess("read-create")
if mibBuilder.loadTexts:
    odukpTxPT.setStatus("current")
if mibBuilder.loadTexts:
    odukpTxPT.setDescription("Transmitted Payload type")


class _OdukpExpPT_Type(Integer32):
    """Custom type odukpExpPT based on Integer32"""
    defaultValue = 255


_OdukpExpPT_Type.__name__ = "Integer32"
_OdukpExpPT_Object = MibTableColumn
odukpExpPT = _OdukpExpPT_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 20, 1, 7, 1, 33),
    _OdukpExpPT_Type()
)
odukpExpPT.setMaxAccess("read-create")
if mibBuilder.loadTexts:
    odukpExpPT.setStatus("current")
if mibBuilder.loadTexts:
    odukpExpPT.setDescription("same as transmitted payload type")


class _OdukpPLMConsequentActions_Type(SOtnEnabled):
    """Custom type odukpPLMConsequentActions based on SOtnEnabled"""
    defaultValue = 1


_OdukpPLMConsequentActions_Type.__name__ = "SOtnEnabled"
_OdukpPLMConsequentActions_Object = MibTableColumn
odukpPLMConsequentActions = _OdukpPLMConsequentActions_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 20, 1, 7, 1, 34),
    _OdukpPLMConsequentActions_Type()
)
odukpPLMConsequentActions.setMaxAccess("read-create")
if mibBuilder.loadTexts:
    odukpPLMConsequentActions.setStatus("current")
if mibBuilder.loadTexts:
    odukpPLMConsequentActions.setDescription(" ")


class _OdukpOpuConfig_Type(Integer32):
    """Custom type odukpOpuConfig based on Integer32"""
    defaultValue = 0

    subtypeSpec = Integer32.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        SingleValueConstraint(
            *(0,
              1,
              2,
              3)
        )
    )
    namedValues = NamedValues(
        *(("auto", 0),
          ("intact", 1),
          ("client", 2),
          ("mux", 3))
    )


_OdukpOpuConfig_Type.__name__ = "Integer32"
_OdukpOpuConfig_Object = MibTableColumn
odukpOpuConfig = _OdukpOpuConfig_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 20, 1, 7, 1, 35),
    _OdukpOpuConfig_Type()
)
odukpOpuConfig.setMaxAccess("read-only")
if mibBuilder.loadTexts:
    odukpOpuConfig.setStatus("current")
if mibBuilder.loadTexts:
    odukpOpuConfig.setDescription("- AUTO indicates that the system will automatically decide how the OPU operates based on additional related provisioning such as an ODU cross-connection or an ODUj being multiplexed into the ODU. When no additional related provisioning is present, the ODU will behave as CTP (connection termination point) that has an open connection and thus will be transmitting an ODU-OCI maintenance signal. - INTACT indicates that the ODU will operate as if there will be an ODU intact cross-connect provisioned. - MUX indicates that the ODU will operate as a TTP (Trail Termination Point) supporting the multiplexing of ODUj entities into the ODU. - CLIENT indicates tha the ODU will operate as a TTP (Trail Termination Point) supporting the client facility into t")


class _OdukpOpuConfigActual_Type(Integer32):
    """Custom type odukpOpuConfigActual based on Integer32"""
    defaultValue = 1

    subtypeSpec = Integer32.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        SingleValueConstraint(
            *(1,
              2,
              3,
              4)
        )
    )
    namedValues = NamedValues(
        *(("intact", 1),
          ("client", 2),
          ("mux", 3),
          ("na", 4))
    )


_OdukpOpuConfigActual_Type.__name__ = "Integer32"
_OdukpOpuConfigActual_Object = MibTableColumn
odukpOpuConfigActual = _OdukpOpuConfigActual_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 20, 1, 7, 1, 36),
    _OdukpOpuConfigActual_Type()
)
odukpOpuConfigActual.setMaxAccess("read-only")
if mibBuilder.loadTexts:
    odukpOpuConfigActual.setStatus("current")
if mibBuilder.loadTexts:
    odukpOpuConfigActual.setDescription("actual OPU configuration")


class _OdukpTribSlot_Type(OctetString):
    """Custom type odukpTribSlot based on OctetString"""
    subtypeSpec = OctetString.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        ValueSizeConstraint(0, 10),
    )


_OdukpTribSlot_Type.__name__ = "OctetString"
_OdukpTribSlot_Object = MibTableColumn
odukpTribSlot = _OdukpTribSlot_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 20, 1, 7, 1, 37),
    _OdukpTribSlot_Type()
)
odukpTribSlot.setMaxAccess("read-create")
if mibBuilder.loadTexts:
    odukpTribSlot.setStatus("current")
if mibBuilder.loadTexts:
    odukpTribSlot.setDescription("Specifies a group of values, or a range of values identifying which tributary slots will be used within the supporting ODUk/ ODUj.")
_OdukpExpClientRate_Type = Integer32
_OdukpExpClientRate_Object = MibTableColumn
odukpExpClientRate = _OdukpExpClientRate_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 20, 1, 7, 1, 38),
    _OdukpExpClientRate_Type()
)
odukpExpClientRate.setMaxAccess("read-create")
if mibBuilder.loadTexts:
    odukpExpClientRate.setStatus("current")
if mibBuilder.loadTexts:
    odukpExpClientRate.setDescription("Specifies the expected signal rate of the constant bit rate mapped client within the payload of the ODUflex. This value is used by the system to determine the ODUflex(CBR) rate. The number is specified in Kbps.")
_OdukpNumOfGfpTs_Type = Integer32
_OdukpNumOfGfpTs_Object = MibTableColumn
odukpNumOfGfpTs = _OdukpNumOfGfpTs_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 20, 1, 7, 1, 39),
    _OdukpNumOfGfpTs_Type()
)
odukpNumOfGfpTs.setMaxAccess("read-create")
if mibBuilder.loadTexts:
    odukpNumOfGfpTs.setStatus("current")
if mibBuilder.loadTexts:
    odukpNumOfGfpTs.setDescription("Specify the ODUflex(GFP) rate by entering the number of 1.25G Tributary Slots when the ODUflex supports a GFP client.")
_OdukpOperateRate_Type = Integer32
_OdukpOperateRate_Object = MibTableColumn
odukpOperateRate = _OdukpOperateRate_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 20, 1, 7, 1, 40),
    _OdukpOperateRate_Type()
)
odukpOperateRate.setMaxAccess("read-only")
if mibBuilder.loadTexts:
    odukpOperateRate.setStatus("current")
if mibBuilder.loadTexts:
    odukpOperateRate.setDescription("The OPERRATE parameter shows the OPUflex nominal bit rate. The number is measured in Kbps.")
_OdukpRowStatus_Type = RowStatus
_OdukpRowStatus_Object = MibTableColumn
odukpRowStatus = _OdukpRowStatus_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 20, 1, 7, 1, 41),
    _OdukpRowStatus_Type()
)
odukpRowStatus.setMaxAccess("read-create")
if mibBuilder.loadTexts:
    odukpRowStatus.setStatus("current")
if mibBuilder.loadTexts:
    odukpRowStatus.setDescription("ODUkP Table RowStatus")
_SOTUkTable_Object = MibTable
sOTUkTable = _SOTUkTable_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 20, 1, 8)
)
if mibBuilder.loadTexts:
    sOTUkTable.setStatus("current")
if mibBuilder.loadTexts:
    sOTUkTable.setDescription("OTUk Table")
_SOTUkEntry_Object = MibTableRow
sOTUkEntry = _SOTUkEntry_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 20, 1, 8, 1)
)
sOTUkEntry.setIndexNames(
    (0, "ST-COMMON-MIB", "shelfId"),
    (0, "ST-COMMON-MIB", "slotNo"),
    (0, "ST-COMMON-MIB", "subSlotNo"),
    (0, "ST-COMMON-MIB", "portNo"),
    (0, "ST-COMMON-MIB", "subPortNo"),
    (0, "ST-OTN-MIB", "otuId"),
)
if mibBuilder.loadTexts:
    sOTUkEntry.setStatus("current")
if mibBuilder.loadTexts:
    sOTUkEntry.setDescription(" ")
_OtuId_Type = SOtuId
_OtuId_Object = MibTableColumn
otuId = _OtuId_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 20, 1, 8, 1, 1),
    _OtuId_Type()
)
otuId.setMaxAccess("not-accessible")
if mibBuilder.loadTexts:
    otuId.setStatus("current")
if mibBuilder.loadTexts:
    otuId.setDescription(" ")
_OtuType_Type = SOtuType
_OtuType_Object = MibTableColumn
otuType = _OtuType_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 20, 1, 8, 1, 2),
    _OtuType_Type()
)
otuType.setMaxAccess("read-create")
if mibBuilder.loadTexts:
    otuType.setStatus("current")
if mibBuilder.loadTexts:
    otuType.setDescription(" ")


class _OtukAdminState_Type(SOtnEnabled):
    """Custom type otukAdminState based on SOtnEnabled"""
    defaultValue = 1


_OtukAdminState_Type.__name__ = "SOtnEnabled"
_OtukAdminState_Object = MibTableColumn
otukAdminState = _OtukAdminState_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 20, 1, 8, 1, 3),
    _OtukAdminState_Type()
)
otukAdminState.setMaxAccess("read-create")
if mibBuilder.loadTexts:
    otukAdminState.setStatus("current")
if mibBuilder.loadTexts:
    otukAdminState.setDescription("Specifies the admin state of the entity. As to different object, the consequent aciton may be different, e.g. it may shutdown the port, or insert maintenance signal.")


class _OtukOperationalState_Type(SOtnUpDown):
    """Custom type otukOperationalState based on SOtnUpDown"""
    defaultValue = 1


_OtukOperationalState_Type.__name__ = "SOtnUpDown"
_OtukOperationalState_Object = MibTableColumn
otukOperationalState = _OtukOperationalState_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 20, 1, 8, 1, 4),
    _OtukOperationalState_Type()
)
otukOperationalState.setMaxAccess("read-only")
if mibBuilder.loadTexts:
    otukOperationalState.setStatus("current")
if mibBuilder.loadTexts:
    otukOperationalState.setDescription("The operational state of the NE.")


class _OtukAvailabilityState_Type(StAvailabilityState):
    """Custom type otukAvailabilityState based on StAvailabilityState"""
    defaultValue = 0


_OtukAvailabilityState_Type.__name__ = "StAvailabilityState"
_OtukAvailabilityState_Object = MibTableColumn
otukAvailabilityState = _OtukAvailabilityState_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 20, 1, 8, 1, 5),
    _OtukAvailabilityState_Type()
)
otukAvailabilityState.setMaxAccess("read-only")
if mibBuilder.loadTexts:
    otukAvailabilityState.setStatus("current")
if mibBuilder.loadTexts:
    otukAvailabilityState.setDescription("Avalaibility Status is to qualify the operational, usage and/or administrative state attributes")
_OtukAlarmProfile_Type = Integer32
_OtukAlarmProfile_Object = MibTableColumn
otukAlarmProfile = _OtukAlarmProfile_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 20, 1, 8, 1, 6),
    _OtukAlarmProfile_Type()
)
otukAlarmProfile.setMaxAccess("read-create")
if mibBuilder.loadTexts:
    otukAlarmProfile.setStatus("current")
if mibBuilder.loadTexts:
    otukAlarmProfile.setDescription("Specifies the alarm profile table for the entity.")
_OtukPMProfile_Type = Integer32
_OtukPMProfile_Object = MibTableColumn
otukPMProfile = _OtukPMProfile_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 20, 1, 8, 1, 7),
    _OtukPMProfile_Type()
)
otukPMProfile.setMaxAccess("read-create")
if mibBuilder.loadTexts:
    otukPMProfile.setStatus("current")
if mibBuilder.loadTexts:
    otukPMProfile.setDescription("Specifies the alarm profile table for the entity.")


class _OtukExpSAPI_Type(OctetString):
    """Custom type otukExpSAPI based on OctetString"""
    subtypeSpec = OctetString.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        ValueSizeConstraint(16, 16),
    )
    fixedLength = 16


_OtukExpSAPI_Type.__name__ = "OctetString"
_OtukExpSAPI_Object = MibTableColumn
otukExpSAPI = _OtukExpSAPI_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 20, 1, 8, 1, 8),
    _OtukExpSAPI_Type()
)
otukExpSAPI.setMaxAccess("read-create")
if mibBuilder.loadTexts:
    otukExpSAPI.setStatus("current")
if mibBuilder.loadTexts:
    otukExpSAPI.setDescription("Specifies 15 characters representing the expected SAPI.")


class _OtukExpDAPI_Type(OctetString):
    """Custom type otukExpDAPI based on OctetString"""
    subtypeSpec = OctetString.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        ValueSizeConstraint(16, 16),
    )
    fixedLength = 16


_OtukExpDAPI_Type.__name__ = "OctetString"
_OtukExpDAPI_Object = MibTableColumn
otukExpDAPI = _OtukExpDAPI_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 20, 1, 8, 1, 9),
    _OtukExpDAPI_Type()
)
otukExpDAPI.setMaxAccess("read-create")
if mibBuilder.loadTexts:
    otukExpDAPI.setStatus("current")
if mibBuilder.loadTexts:
    otukExpDAPI.setDescription("Specifies 15 characters representing the expected DAPI.")


class _OtukExpOperator_Type(OctetString):
    """Custom type otukExpOperator based on OctetString"""
    subtypeSpec = OctetString.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        ValueSizeConstraint(32, 32),
    )
    fixedLength = 32


_OtukExpOperator_Type.__name__ = "OctetString"
_OtukExpOperator_Object = MibTableColumn
otukExpOperator = _OtukExpOperator_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 20, 1, 8, 1, 10),
    _OtukExpOperator_Type()
)
otukExpOperator.setMaxAccess("read-create")
if mibBuilder.loadTexts:
    otukExpOperator.setStatus("current")
if mibBuilder.loadTexts:
    otukExpOperator.setDescription("Specifies 32 characters representing the expected value for the Operator Specific area of the TTI.")


class _OtukTxSAPI_Type(OctetString):
    """Custom type otukTxSAPI based on OctetString"""
    subtypeSpec = OctetString.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        ValueSizeConstraint(16, 16),
    )
    fixedLength = 16


_OtukTxSAPI_Type.__name__ = "OctetString"
_OtukTxSAPI_Object = MibTableColumn
otukTxSAPI = _OtukTxSAPI_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 20, 1, 8, 1, 11),
    _OtukTxSAPI_Type()
)
otukTxSAPI.setMaxAccess("read-create")
if mibBuilder.loadTexts:
    otukTxSAPI.setStatus("current")
if mibBuilder.loadTexts:
    otukTxSAPI.setDescription("Specifies 15 characters representing the transmitted SAPI.")


class _OtukTxDAPI_Type(OctetString):
    """Custom type otukTxDAPI based on OctetString"""
    subtypeSpec = OctetString.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        ValueSizeConstraint(16, 16),
    )
    fixedLength = 16


_OtukTxDAPI_Type.__name__ = "OctetString"
_OtukTxDAPI_Object = MibTableColumn
otukTxDAPI = _OtukTxDAPI_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 20, 1, 8, 1, 12),
    _OtukTxDAPI_Type()
)
otukTxDAPI.setMaxAccess("read-create")
if mibBuilder.loadTexts:
    otukTxDAPI.setStatus("current")
if mibBuilder.loadTexts:
    otukTxDAPI.setDescription("Specifies 15 characters representing the transmitted DAPI.")


class _OtukTxOperator_Type(OctetString):
    """Custom type otukTxOperator based on OctetString"""
    subtypeSpec = OctetString.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        ValueSizeConstraint(32, 32),
    )
    fixedLength = 32


_OtukTxOperator_Type.__name__ = "OctetString"
_OtukTxOperator_Object = MibTableColumn
otukTxOperator = _OtukTxOperator_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 20, 1, 8, 1, 13),
    _OtukTxOperator_Type()
)
otukTxOperator.setMaxAccess("read-create")
if mibBuilder.loadTexts:
    otukTxOperator.setStatus("current")
if mibBuilder.loadTexts:
    otukTxOperator.setDescription("Specifies 32 characters representing the transmitted value in the Operator Specific area of the TTI.")


class _OtukRxSAPI_Type(OctetString):
    """Custom type otukRxSAPI based on OctetString"""
    subtypeSpec = OctetString.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        ValueSizeConstraint(16, 16),
    )
    fixedLength = 16


_OtukRxSAPI_Type.__name__ = "OctetString"
_OtukRxSAPI_Object = MibTableColumn
otukRxSAPI = _OtukRxSAPI_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 20, 1, 8, 1, 14),
    _OtukRxSAPI_Type()
)
otukRxSAPI.setMaxAccess("read-only")
if mibBuilder.loadTexts:
    otukRxSAPI.setStatus("current")
if mibBuilder.loadTexts:
    otukRxSAPI.setDescription("Specifies 15 characters representing the received SAPI.")


class _OtukRxDAPI_Type(OctetString):
    """Custom type otukRxDAPI based on OctetString"""
    subtypeSpec = OctetString.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        ValueSizeConstraint(16, 16),
    )
    fixedLength = 16


_OtukRxDAPI_Type.__name__ = "OctetString"
_OtukRxDAPI_Object = MibTableColumn
otukRxDAPI = _OtukRxDAPI_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 20, 1, 8, 1, 15),
    _OtukRxDAPI_Type()
)
otukRxDAPI.setMaxAccess("read-only")
if mibBuilder.loadTexts:
    otukRxDAPI.setStatus("current")
if mibBuilder.loadTexts:
    otukRxDAPI.setDescription("Specifies 15 characters representing the received DAPI.")


class _OtukRxOperator_Type(OctetString):
    """Custom type otukRxOperator based on OctetString"""
    subtypeSpec = OctetString.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        ValueSizeConstraint(32, 32),
    )
    fixedLength = 32


_OtukRxOperator_Type.__name__ = "OctetString"
_OtukRxOperator_Object = MibTableColumn
otukRxOperator = _OtukRxOperator_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 20, 1, 8, 1, 16),
    _OtukRxOperator_Type()
)
otukRxOperator.setMaxAccess("read-only")
if mibBuilder.loadTexts:
    otukRxOperator.setStatus("current")
if mibBuilder.loadTexts:
    otukRxOperator.setDescription("Specifies 32 characters representing the received value in the Operator Specific area of the TTI.")


class _OtukTIMDefectMode_Type(Integer32):
    """Custom type otukTIMDefectMode based on Integer32"""
    defaultValue = 4

    subtypeSpec = Integer32.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        SingleValueConstraint(
            *(0,
              1,
              2,
              3,
              4,
              5,
              6,
              7)
        )
    )
    namedValues = NamedValues(
        *(("none", 0),
          ("sapi", 1),
          ("dapi", 2),
          ("oper", 3),
          ("sapi-dapi", 4),
          ("sapi-dapi-oper", 5),
          ("sapi-oper", 6),
          ("dapi-oper", 7))
    )


_OtukTIMDefectMode_Type.__name__ = "Integer32"
_OtukTIMDefectMode_Object = MibTableColumn
otukTIMDefectMode = _OtukTIMDefectMode_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 20, 1, 8, 1, 17),
    _OtukTIMDefectMode_Type()
)
otukTIMDefectMode.setMaxAccess("read-create")
if mibBuilder.loadTexts:
    otukTIMDefectMode.setStatus("current")
if mibBuilder.loadTexts:
    otukTIMDefectMode.setDescription("Specifies the definition of the trace identifier mismatch alarm by identifying which portion(s) of the TTI message are compared for trace identifier mismatch purposes. - NONE (no trace identifier mismatch defect) - OPER (Operator Specific mismatch only), - SAPI (SAPI mismatch only), - DAPI (DAPI mismatch only), - SAPI_DAPI (SAPI + DAPI Mismatches), - SAPI_OPER (SAPI + OPER mismatches), - DAPI_OPER (DAPI + OPER mismatches), - SAPI_DAPI_OPER (SAPI + DAPI + OPER mismatches).")


class _OtukTIMActDis_Type(SOtnTruthValue):
    """Custom type otukTIMActDis based on SOtnTruthValue"""
    defaultValue = 1


_OtukTIMActDis_Type.__name__ = "SOtnTruthValue"
_OtukTIMActDis_Object = MibTableColumn
otukTIMActDis = _OtukTIMActDis_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 20, 1, 8, 1, 18),
    _OtukTIMActDis_Type()
)
otukTIMActDis.setMaxAccess("read-create")
if mibBuilder.loadTexts:
    otukTIMActDis.setStatus("current")
if mibBuilder.loadTexts:
    otukTIMActDis.setDescription("Specifies the monitored trace identification mode. - 'false': enable trace monitoring and perform consequent actions, including protection switches and AIS maintenance signal insertion when there is a trace mismatch. - 'true': to enable trace monitoring but not perform consequent actions, including protection switches and AIS maintenance signal insertion when there is a trace mismatch. - Enter true to disable trace monitoring. Note that AIS insertion is only performed on a terminated entity.")


class _OtukDegradeInterval_Type(Integer32):
    """Custom type otukDegradeInterval based on Integer32"""
    defaultValue = 2


_OtukDegradeInterval_Type.__name__ = "Integer32"
_OtukDegradeInterval_Object = MibTableColumn
otukDegradeInterval = _OtukDegradeInterval_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 20, 1, 8, 1, 19),
    _OtukDegradeInterval_Type()
)
otukDegradeInterval.setMaxAccess("read-create")
if mibBuilder.loadTexts:
    otukDegradeInterval.setStatus("current")
if mibBuilder.loadTexts:
    otukDegradeInterval.setDescription("Indicates the consecutive number of one second intervals with the number of detected block errors exceeding the block error threshold for each of those seconds for the purposes of BER of Signal Degrade detection.")


class _OtukDegradeThreshold_Type(Integer32):
    """Custom type otukDegradeThreshold based on Integer32"""
    defaultValue = 82026


_OtukDegradeThreshold_Type.__name__ = "Integer32"
_OtukDegradeThreshold_Object = MibTableColumn
otukDegradeThreshold = _OtukDegradeThreshold_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 20, 1, 8, 1, 20),
    _OtukDegradeThreshold_Type()
)
otukDegradeThreshold.setMaxAccess("read-create")
if mibBuilder.loadTexts:
    otukDegradeThreshold.setStatus("current")
if mibBuilder.loadTexts:
    otukDegradeThreshold.setDescription("Indicates the threshold number of block errors at which a one second interval will be considered degraded for the purposes of BER signal degrade detection. some examples of possible values are: 1 to 20421, default of 3064 (OTU1) 1 to 82026, default of 12304 (OTU2) 1 to 84986, default of 12748 (OTU2e) 1 to 856388, default of 128459 (OTU4) ...")


class _OtukLoopBack_Type(SLoopBackMode):
    """Custom type otukLoopBack based on SLoopBackMode"""
    defaultValue = 0


_OtukLoopBack_Type.__name__ = "SLoopBackMode"
_OtukLoopBack_Object = MibTableColumn
otukLoopBack = _OtukLoopBack_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 20, 1, 8, 1, 21),
    _OtukLoopBack_Type()
)
otukLoopBack.setMaxAccess("read-create")
if mibBuilder.loadTexts:
    otukLoopBack.setStatus("current")
if mibBuilder.loadTexts:
    otukLoopBack.setDescription(" ")
_OtukExtChannel_Type = DisplayString
_OtukExtChannel_Object = MibTableColumn
otukExtChannel = _OtukExtChannel_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 20, 1, 8, 1, 22),
    _OtukExtChannel_Type()
)
otukExtChannel.setMaxAccess("read-create")
if mibBuilder.loadTexts:
    otukExtChannel.setStatus("current")
if mibBuilder.loadTexts:
    otukExtChannel.setDescription("Specifies the DWDM frequency at which the interface will operate when the supporting pluggable for the facility is a tunable DWDM optics. This parameter is used when directly connecting the module to a separate network element, rather than connecting to an optical multiplexer/demultiplex module within the same network element.")


class _OtukClientShutdown_Type(SOtnYesNo):
    """Custom type otukClientShutdown based on SOtnYesNo"""
    defaultValue = 0


_OtukClientShutdown_Type.__name__ = "SOtnYesNo"
_OtukClientShutdown_Object = MibTableColumn
otukClientShutdown = _OtukClientShutdown_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 20, 1, 8, 1, 23),
    _OtukClientShutdown_Type()
)
otukClientShutdown.setMaxAccess("read-create")
if mibBuilder.loadTexts:
    otukClientShutdown.setStatus("current")
if mibBuilder.loadTexts:
    otukClientShutdown.setDescription("Setting the value to YES will shut down the output client port side laser on the transponder instead of sending a maintenance signal (e.g. AIS or LF depending on the type of facility provisioned) when there is a signal failure detected on the line side of a transponder. This value is typically used with external client side protection. Setting the value to NO will not shut down the laser on the port side of the transponder. When this value is set standard maintenance signaling will be used. Setting this value to NA will disable the protection functionality.")


class _OtukClientShutdownHoldoffTimer_Type(Integer32):
    """Custom type otukClientShutdownHoldoffTimer based on Integer32"""
    defaultValue = 0


_OtukClientShutdownHoldoffTimer_Type.__name__ = "Integer32"
_OtukClientShutdownHoldoffTimer_Object = MibTableColumn
otukClientShutdownHoldoffTimer = _OtukClientShutdownHoldoffTimer_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 20, 1, 8, 1, 24),
    _OtukClientShutdownHoldoffTimer_Type()
)
otukClientShutdownHoldoffTimer.setMaxAccess("read-create")
if mibBuilder.loadTexts:
    otukClientShutdownHoldoffTimer.setStatus("current")
if mibBuilder.loadTexts:
    otukClientShutdownHoldoffTimer.setDescription("Specifies a hold-off time between 60 msec and 1000 msec in increments of 5 msec before a client port side laser shutdown upon a defect detected at line side. If no additional hold-off time for a port side laser shutdown is desired, then the value shall be set to zero. Note: This parameter is only supported when value of parameter 'ClientShutDown' is yes")


class _OtukFecType_Type(Integer32):
    """Custom type otukFecType based on Integer32"""
    defaultValue = 1

    subtypeSpec = Integer32.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        SingleValueConstraint(
            *(0,
              1,
              2,
              3,
              4,
              5,
              6,
              7,
              8,
              9,
              10,
              11,
              12,
              13,
              14,
              15,
              16,
              17)
        )
    )
    namedValues = NamedValues(
        *(("none", 0),
          ("rs", 1),
          ("i4", 2),
          ("i7", 3),
          ("last", 4),
          ("sdfec1", 5),
          ("sdfec2", 6),
          ("sdfec3", 7),
          ("sdfecl", 8),
          ("sdfec1-den", 9),
          ("sdfec2-den", 10),
          ("sdfec3-den", 11),
          ("sdfec1-8qam", 12),
          ("sdfec2-8qam", 13),
          ("sdfec3-8qam", 14),
          ("sdfec1-16qam", 15),
          ("sdfec2-16qam", 16),
          ("sdfec3-16qam", 17))
    )


_OtukFecType_Type.__name__ = "Integer32"
_OtukFecType_Object = MibTableColumn
otukFecType = _OtukFecType_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 20, 1, 8, 1, 25),
    _OtukFecType_Type()
)
otukFecType.setMaxAccess("read-create")
if mibBuilder.loadTexts:
    otukFecType.setStatus("current")
if mibBuilder.loadTexts:
    otukFecType.setDescription("for 10G: NoFEC, G709FEC, I.4EFEC, I.7EFEC for 100G: G709FEC, SDFEC1, SDFEC2, SDFEC3")
_OtukRowStatus_Type = RowStatus
_OtukRowStatus_Object = MibTableColumn
otukRowStatus = _OtukRowStatus_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 20, 1, 8, 1, 26),
    _OtukRowStatus_Type()
)
otukRowStatus.setMaxAccess("read-create")
if mibBuilder.loadTexts:
    otukRowStatus.setStatus("current")
if mibBuilder.loadTexts:
    otukRowStatus.setDescription("ODUkP Table RowStatus")


class _OtukNearEndALS_Type(SOtnYesNo):
    """Custom type otukNearEndALS based on SOtnYesNo"""
    defaultValue = 0


_OtukNearEndALS_Type.__name__ = "SOtnYesNo"
_OtukNearEndALS_Object = MibTableColumn
otukNearEndALS = _OtukNearEndALS_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 20, 1, 8, 1, 27),
    _OtukNearEndALS_Type()
)
otukNearEndALS.setMaxAccess("read-create")
if mibBuilder.loadTexts:
    otukNearEndALS.setStatus("current")
if mibBuilder.loadTexts:
    otukNearEndALS.setDescription("This parameter, when set to YES, will shut down port side laser upon a port side incoming failure (such as LOS/LOF/LOSYNC).")
_SOChOSTable_Object = MibTable
sOChOSTable = _SOChOSTable_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 20, 1, 9)
)
if mibBuilder.loadTexts:
    sOChOSTable.setStatus("current")
if mibBuilder.loadTexts:
    sOChOSTable.setDescription("OChOS Table")
_SOChOSEntry_Object = MibTableRow
sOChOSEntry = _SOChOSEntry_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 20, 1, 9, 1)
)
sOChOSEntry.setIndexNames(
    (0, "ST-COMMON-MIB", "shelfId"),
    (0, "ST-COMMON-MIB", "slotNo"),
    (0, "ST-COMMON-MIB", "subSlotNo"),
    (0, "ST-COMMON-MIB", "portNo"),
    (0, "ST-COMMON-MIB", "subPortNo"),
    (0, "ST-OTN-MIB", "ochosId"),
)
if mibBuilder.loadTexts:
    sOChOSEntry.setStatus("current")
if mibBuilder.loadTexts:
    sOChOSEntry.setDescription(" ")
_OchosId_Type = SOchOsId
_OchosId_Object = MibTableColumn
ochosId = _OchosId_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 20, 1, 9, 1, 1),
    _OchosId_Type()
)
ochosId.setMaxAccess("not-accessible")
if mibBuilder.loadTexts:
    ochosId.setStatus("current")
if mibBuilder.loadTexts:
    ochosId.setDescription(" ")


class _OchosAdminState_Type(SOtnEnabled):
    """Custom type ochosAdminState based on SOtnEnabled"""
    defaultValue = 1


_OchosAdminState_Type.__name__ = "SOtnEnabled"
_OchosAdminState_Object = MibTableColumn
ochosAdminState = _OchosAdminState_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 20, 1, 9, 1, 2),
    _OchosAdminState_Type()
)
ochosAdminState.setMaxAccess("read-create")
if mibBuilder.loadTexts:
    ochosAdminState.setStatus("current")
if mibBuilder.loadTexts:
    ochosAdminState.setDescription("Specifies the admin state of the entity. As to different object, the consequent aciton may be different, e.g. it may shutdown the port, or insert maintenance signal.")


class _OchosOperationalState_Type(SOtnUpDown):
    """Custom type ochosOperationalState based on SOtnUpDown"""
    defaultValue = 1


_OchosOperationalState_Type.__name__ = "SOtnUpDown"
_OchosOperationalState_Object = MibTableColumn
ochosOperationalState = _OchosOperationalState_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 20, 1, 9, 1, 3),
    _OchosOperationalState_Type()
)
ochosOperationalState.setMaxAccess("read-only")
if mibBuilder.loadTexts:
    ochosOperationalState.setStatus("current")
if mibBuilder.loadTexts:
    ochosOperationalState.setDescription("The operational state of the NE.")


class _OchosAvailabilityState_Type(StAvailabilityState):
    """Custom type ochosAvailabilityState based on StAvailabilityState"""
    defaultValue = 0


_OchosAvailabilityState_Type.__name__ = "StAvailabilityState"
_OchosAvailabilityState_Object = MibTableColumn
ochosAvailabilityState = _OchosAvailabilityState_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 20, 1, 9, 1, 4),
    _OchosAvailabilityState_Type()
)
ochosAvailabilityState.setMaxAccess("read-only")
if mibBuilder.loadTexts:
    ochosAvailabilityState.setStatus("current")
if mibBuilder.loadTexts:
    ochosAvailabilityState.setDescription("Avalaibility Status is to qualify the operational, usage and/or administrative state attributes")
_OchosAlarmProfile_Type = Integer32
_OchosAlarmProfile_Object = MibTableColumn
ochosAlarmProfile = _OchosAlarmProfile_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 20, 1, 9, 1, 5),
    _OchosAlarmProfile_Type()
)
ochosAlarmProfile.setMaxAccess("read-create")
if mibBuilder.loadTexts:
    ochosAlarmProfile.setStatus("current")
if mibBuilder.loadTexts:
    ochosAlarmProfile.setDescription("Specifies the alarm profile table for the entity.")
_OchosPMProfile_Type = Integer32
_OchosPMProfile_Object = MibTableColumn
ochosPMProfile = _OchosPMProfile_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 20, 1, 9, 1, 6),
    _OchosPMProfile_Type()
)
ochosPMProfile.setMaxAccess("read-create")
if mibBuilder.loadTexts:
    ochosPMProfile.setStatus("current")
if mibBuilder.loadTexts:
    ochosPMProfile.setDescription("Specifies the alarm profile table for the entity.")


class _OchosSettingFreq_Type(Integer32):
    """Custom type ochosSettingFreq based on Integer32"""
    subtypeSpec = Integer32.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        ValueRangeConstraint(19135, 19610),
    )


_OchosSettingFreq_Type.__name__ = "Integer32"
_OchosSettingFreq_Object = MibTableColumn
ochosSettingFreq = _OchosSettingFreq_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 20, 1, 9, 1, 7),
    _OchosSettingFreq_Type()
)
ochosSettingFreq.setMaxAccess("read-create")
if mibBuilder.loadTexts:
    ochosSettingFreq.setStatus("current")
if mibBuilder.loadTexts:
    ochosSettingFreq.setDescription("Setting the frequency of line side, AC100 support configuration range: 191.35 THz to 196.10 THz in increments of 0.05 THz")


class _OchosLineLaserEnabled_Type(SOtnEnabled):
    """Custom type ochosLineLaserEnabled based on SOtnEnabled"""
    defaultValue = 1


_OchosLineLaserEnabled_Type.__name__ = "SOtnEnabled"
_OchosLineLaserEnabled_Object = MibTableColumn
ochosLineLaserEnabled = _OchosLineLaserEnabled_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 20, 1, 9, 1, 8),
    _OchosLineLaserEnabled_Type()
)
ochosLineLaserEnabled.setMaxAccess("read-create")
if mibBuilder.loadTexts:
    ochosLineLaserEnabled.setStatus("current")
if mibBuilder.loadTexts:
    ochosLineLaserEnabled.setDescription("Through this attribute the laser, normally on, can be switched-off by the operator overriding all ALS related laser control. Switch-on the laser, though, will resume ALS control of the laser, which does not necessarily mean that the laser will be turned on! Furthermore switch-on/off of the laser shall be subject to the dispositions stated in laserState.")
_OchosLineLaserState_Type = SOtnEnabled
_OchosLineLaserState_Object = MibTableColumn
ochosLineLaserState = _OchosLineLaserState_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 20, 1, 9, 1, 9),
    _OchosLineLaserState_Type()
)
ochosLineLaserState.setMaxAccess("read-only")
if mibBuilder.loadTexts:
    ochosLineLaserState.setStatus("current")
if mibBuilder.loadTexts:
    ochosLineLaserState.setDescription("Line laser state")


class _OchosOEORegen_Type(SOtnEnabled):
    """Custom type ochosOEORegen based on SOtnEnabled"""
    defaultValue = 1


_OchosOEORegen_Type.__name__ = "SOtnEnabled"
_OchosOEORegen_Object = MibTableColumn
ochosOEORegen = _OchosOEORegen_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 20, 1, 9, 1, 10),
    _OchosOEORegen_Type()
)
ochosOEORegen.setMaxAccess("read-create")
if mibBuilder.loadTexts:
    ochosOEORegen.setStatus("current")
if mibBuilder.loadTexts:
    ochosOEORegen.setDescription("only applies to transponder card (not for muxponder and OTN switching card)")
_OchosRowStatus_Type = RowStatus
_OchosRowStatus_Object = MibTableColumn
ochosRowStatus = _OchosRowStatus_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 20, 1, 9, 1, 11),
    _OchosRowStatus_Type()
)
ochosRowStatus.setMaxAccess("read-create")
if mibBuilder.loadTexts:
    ochosRowStatus.setStatus("current")
if mibBuilder.loadTexts:
    ochosRowStatus.setDescription("ochos RowStatus")


class _OchosNearEndALS_Type(SOtnYesNo):
    """Custom type ochosNearEndALS based on SOtnYesNo"""
    defaultValue = 0


_OchosNearEndALS_Type.__name__ = "SOtnYesNo"
_OchosNearEndALS_Object = MibTableColumn
ochosNearEndALS = _OchosNearEndALS_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 20, 1, 9, 1, 12),
    _OchosNearEndALS_Type()
)
ochosNearEndALS.setMaxAccess("read-create")
if mibBuilder.loadTexts:
    ochosNearEndALS.setStatus("current")
if mibBuilder.loadTexts:
    ochosNearEndALS.setDescription("This parameter, when set to YES, will shut down port side laser upon a port side incoming failure (such as LOS/LOF/LOSYNC).")


class _OchosEffectiveFreq_Type(Integer32):
    """Custom type ochosEffectiveFreq based on Integer32"""
    subtypeSpec = Integer32.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        ValueRangeConstraint(19135, 19610),
    )


_OchosEffectiveFreq_Type.__name__ = "Integer32"
_OchosEffectiveFreq_Object = MibTableColumn
ochosEffectiveFreq = _OchosEffectiveFreq_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 20, 1, 9, 1, 13),
    _OchosEffectiveFreq_Type()
)
ochosEffectiveFreq.setMaxAccess("read-only")
if mibBuilder.loadTexts:
    ochosEffectiveFreq.setStatus("current")
if mibBuilder.loadTexts:
    ochosEffectiveFreq.setDescription("The effective frequency of line side")


class _OchosFirstSupportedFreq_Type(Integer32):
    """Custom type ochosFirstSupportedFreq based on Integer32"""
    subtypeSpec = Integer32.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        ValueRangeConstraint(19135, 19610),
    )


_OchosFirstSupportedFreq_Type.__name__ = "Integer32"
_OchosFirstSupportedFreq_Object = MibTableColumn
ochosFirstSupportedFreq = _OchosFirstSupportedFreq_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 20, 1, 9, 1, 14),
    _OchosFirstSupportedFreq_Type()
)
ochosFirstSupportedFreq.setMaxAccess("read-create")
if mibBuilder.loadTexts:
    ochosFirstSupportedFreq.setStatus("current")
if mibBuilder.loadTexts:
    ochosFirstSupportedFreq.setDescription("The start frequency of line side for setting")


class _OchosLastSupportedFreq_Type(Integer32):
    """Custom type ochosLastSupportedFreq based on Integer32"""
    subtypeSpec = Integer32.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        ValueRangeConstraint(19135, 19610),
    )


_OchosLastSupportedFreq_Type.__name__ = "Integer32"
_OchosLastSupportedFreq_Object = MibTableColumn
ochosLastSupportedFreq = _OchosLastSupportedFreq_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 20, 1, 9, 1, 15),
    _OchosLastSupportedFreq_Type()
)
ochosLastSupportedFreq.setMaxAccess("read-create")
if mibBuilder.loadTexts:
    ochosLastSupportedFreq.setStatus("current")
if mibBuilder.loadTexts:
    ochosLastSupportedFreq.setDescription("The end frequency of line side for setting")


class _OchosFreqGrid_Type(Integer32):
    """Custom type ochosFreqGrid based on Integer32"""
    defaultValue = 50

    subtypeSpec = Integer32.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        SingleValueConstraint(
            *(50,
              100)
        )
    )
    namedValues = NamedValues(
        *(("hZ50G", 50),
          ("hZ100G", 100))
    )


_OchosFreqGrid_Type.__name__ = "Integer32"
_OchosFreqGrid_Object = MibTableColumn
ochosFreqGrid = _OchosFreqGrid_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 20, 1, 9, 1, 16),
    _OchosFreqGrid_Type()
)
ochosFreqGrid.setMaxAccess("read-create")
if mibBuilder.loadTexts:
    ochosFreqGrid.setStatus("current")
if mibBuilder.loadTexts:
    ochosFreqGrid.setDescription("Increments of frequency setting")
_OchosConfigureTxPower_Type = Integer32
_OchosConfigureTxPower_Object = MibTableColumn
ochosConfigureTxPower = _OchosConfigureTxPower_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 20, 1, 9, 1, 17),
    _OchosConfigureTxPower_Type()
)
ochosConfigureTxPower.setMaxAccess("read-create")
if mibBuilder.loadTexts:
    ochosConfigureTxPower.setStatus("current")
if mibBuilder.loadTexts:
    ochosConfigureTxPower.setDescription("Configure of Output Power, range is between ochosFirstSupportedTxPower and ochosLastSupportedTxPower, step is ochosTxPowerGrid")
_OchosEffectiveTxPower_Type = Integer32
_OchosEffectiveTxPower_Object = MibTableColumn
ochosEffectiveTxPower = _OchosEffectiveTxPower_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 20, 1, 9, 1, 18),
    _OchosEffectiveTxPower_Type()
)
ochosEffectiveTxPower.setMaxAccess("read-only")
if mibBuilder.loadTexts:
    ochosEffectiveTxPower.setStatus("current")
if mibBuilder.loadTexts:
    ochosEffectiveTxPower.setDescription("The actual Output Power")
_OchosFirstSupportedTxPower_Type = Integer32
_OchosFirstSupportedTxPower_Object = MibTableColumn
ochosFirstSupportedTxPower = _OchosFirstSupportedTxPower_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 20, 1, 9, 1, 19),
    _OchosFirstSupportedTxPower_Type()
)
ochosFirstSupportedTxPower.setMaxAccess("read-only")
if mibBuilder.loadTexts:
    ochosFirstSupportedTxPower.setStatus("current")
if mibBuilder.loadTexts:
    ochosFirstSupportedTxPower.setDescription("The start point of Output Power")
_OchosLastSupportedTxPower_Type = Integer32
_OchosLastSupportedTxPower_Object = MibTableColumn
ochosLastSupportedTxPower = _OchosLastSupportedTxPower_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 20, 1, 9, 1, 20),
    _OchosLastSupportedTxPower_Type()
)
ochosLastSupportedTxPower.setMaxAccess("read-only")
if mibBuilder.loadTexts:
    ochosLastSupportedTxPower.setStatus("current")
if mibBuilder.loadTexts:
    ochosLastSupportedTxPower.setDescription("The end point of Output Power")
_OchosTxPowerGrid_Type = Integer32
_OchosTxPowerGrid_Object = MibTableColumn
ochosTxPowerGrid = _OchosTxPowerGrid_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 20, 1, 9, 1, 21),
    _OchosTxPowerGrid_Type()
)
ochosTxPowerGrid.setMaxAccess("read-only")
if mibBuilder.loadTexts:
    ochosTxPowerGrid.setStatus("current")
if mibBuilder.loadTexts:
    ochosTxPowerGrid.setDescription("The step of Output Power")
_OchosDGD_Type = Integer32
_OchosDGD_Object = MibTableColumn
ochosDGD = _OchosDGD_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 20, 1, 9, 1, 22),
    _OchosDGD_Type()
)
ochosDGD.setMaxAccess("read-only")
if mibBuilder.loadTexts:
    ochosDGD.setStatus("current")
if mibBuilder.loadTexts:
    ochosDGD.setDescription("Current Differential Group Delay")
_OchosCD_Type = Integer32
_OchosCD_Object = MibTableColumn
ochosCD = _OchosCD_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 20, 1, 9, 1, 23),
    _OchosCD_Type()
)
ochosCD.setMaxAccess("read-only")
if mibBuilder.loadTexts:
    ochosCD.setStatus("current")
if mibBuilder.loadTexts:
    ochosCD.setDescription("Current Chromatic Dispersion")
_OchosOSNR_Type = Integer32
_OchosOSNR_Object = MibTableColumn
ochosOSNR = _OchosOSNR_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 20, 1, 9, 1, 24),
    _OchosOSNR_Type()
)
ochosOSNR.setMaxAccess("read-only")
if mibBuilder.loadTexts:
    ochosOSNR.setStatus("current")
if mibBuilder.loadTexts:
    ochosOSNR.setDescription("Current OSNR")


class _OchosEffectiveCDAutoSearchRangeHighVal_Type(Integer32):
    """Custom type ochosEffectiveCDAutoSearchRangeHighVal based on Integer32"""
    defaultValue = 0


_OchosEffectiveCDAutoSearchRangeHighVal_Type.__name__ = "Integer32"
_OchosEffectiveCDAutoSearchRangeHighVal_Object = MibTableColumn
ochosEffectiveCDAutoSearchRangeHighVal = _OchosEffectiveCDAutoSearchRangeHighVal_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 20, 1, 9, 1, 25),
    _OchosEffectiveCDAutoSearchRangeHighVal_Type()
)
ochosEffectiveCDAutoSearchRangeHighVal.setMaxAccess("read-only")
if mibBuilder.loadTexts:
    ochosEffectiveCDAutoSearchRangeHighVal.setStatus("current")
if mibBuilder.loadTexts:
    ochosEffectiveCDAutoSearchRangeHighVal.setDescription("The actual value in CFP")


class _OchosEffectiveCDAutoSearchRangeLowVal_Type(Integer32):
    """Custom type ochosEffectiveCDAutoSearchRangeLowVal based on Integer32"""
    defaultValue = 0


_OchosEffectiveCDAutoSearchRangeLowVal_Type.__name__ = "Integer32"
_OchosEffectiveCDAutoSearchRangeLowVal_Object = MibTableColumn
ochosEffectiveCDAutoSearchRangeLowVal = _OchosEffectiveCDAutoSearchRangeLowVal_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 20, 1, 9, 1, 26),
    _OchosEffectiveCDAutoSearchRangeLowVal_Type()
)
ochosEffectiveCDAutoSearchRangeLowVal.setMaxAccess("read-only")
if mibBuilder.loadTexts:
    ochosEffectiveCDAutoSearchRangeLowVal.setStatus("current")
if mibBuilder.loadTexts:
    ochosEffectiveCDAutoSearchRangeLowVal.setDescription("The actual value in CFP")


class _OchosConfiguredCDAutoSearchRangeHighVal_Type(Integer32):
    """Custom type ochosConfiguredCDAutoSearchRangeHighVal based on Integer32"""
    defaultValue = 0


_OchosConfiguredCDAutoSearchRangeHighVal_Type.__name__ = "Integer32"
_OchosConfiguredCDAutoSearchRangeHighVal_Object = MibTableColumn
ochosConfiguredCDAutoSearchRangeHighVal = _OchosConfiguredCDAutoSearchRangeHighVal_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 20, 1, 9, 1, 27),
    _OchosConfiguredCDAutoSearchRangeHighVal_Type()
)
ochosConfiguredCDAutoSearchRangeHighVal.setMaxAccess("read-create")
if mibBuilder.loadTexts:
    ochosConfiguredCDAutoSearchRangeHighVal.setStatus("current")
if mibBuilder.loadTexts:
    ochosConfiguredCDAutoSearchRangeHighVal.setDescription("The value set by user")


class _OchosConfiguredCDAutoSearchRangeLowVal_Type(Integer32):
    """Custom type ochosConfiguredCDAutoSearchRangeLowVal based on Integer32"""
    defaultValue = 0


_OchosConfiguredCDAutoSearchRangeLowVal_Type.__name__ = "Integer32"
_OchosConfiguredCDAutoSearchRangeLowVal_Object = MibTableColumn
ochosConfiguredCDAutoSearchRangeLowVal = _OchosConfiguredCDAutoSearchRangeLowVal_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 20, 1, 9, 1, 28),
    _OchosConfiguredCDAutoSearchRangeLowVal_Type()
)
ochosConfiguredCDAutoSearchRangeLowVal.setMaxAccess("read-create")
if mibBuilder.loadTexts:
    ochosConfiguredCDAutoSearchRangeLowVal.setStatus("current")
if mibBuilder.loadTexts:
    ochosConfiguredCDAutoSearchRangeLowVal.setDescription("The value set by user")


class _OchosSupportedCDAutoSearchRangeHighValThr_Type(Integer32):
    """Custom type ochosSupportedCDAutoSearchRangeHighValThr based on Integer32"""
    defaultValue = 0


_OchosSupportedCDAutoSearchRangeHighValThr_Type.__name__ = "Integer32"
_OchosSupportedCDAutoSearchRangeHighValThr_Object = MibTableColumn
ochosSupportedCDAutoSearchRangeHighValThr = _OchosSupportedCDAutoSearchRangeHighValThr_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 20, 1, 9, 1, 29),
    _OchosSupportedCDAutoSearchRangeHighValThr_Type()
)
ochosSupportedCDAutoSearchRangeHighValThr.setMaxAccess("read-only")
if mibBuilder.loadTexts:
    ochosSupportedCDAutoSearchRangeHighValThr.setStatus("current")
if mibBuilder.loadTexts:
    ochosSupportedCDAutoSearchRangeHighValThr.setDescription("The value which support by CFP")


class _OchosSupportedCDAutoSearchRangeLowValThr_Type(Integer32):
    """Custom type ochosSupportedCDAutoSearchRangeLowValThr based on Integer32"""
    defaultValue = 0


_OchosSupportedCDAutoSearchRangeLowValThr_Type.__name__ = "Integer32"
_OchosSupportedCDAutoSearchRangeLowValThr_Object = MibTableColumn
ochosSupportedCDAutoSearchRangeLowValThr = _OchosSupportedCDAutoSearchRangeLowValThr_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 20, 1, 9, 1, 30),
    _OchosSupportedCDAutoSearchRangeLowValThr_Type()
)
ochosSupportedCDAutoSearchRangeLowValThr.setMaxAccess("read-only")
if mibBuilder.loadTexts:
    ochosSupportedCDAutoSearchRangeLowValThr.setStatus("current")
if mibBuilder.loadTexts:
    ochosSupportedCDAutoSearchRangeLowValThr.setDescription("The value which support by CFP")

# Managed Objects groups


# Notification objects


# Notifications groups


# Agent capabilities


# Module compliance


# Export all MIB objects to the MIB builder

mibBuilder.exportSymbols(
    "ST-OTN-MIB",
    **{"SEtyType": SEtyType,
       "SOtnEnabled": SOtnEnabled,
       "SOtnUpDown": SOtnUpDown,
       "SLoopBackMode": SLoopBackMode,
       "SMapMode": SMapMode,
       "SOtnYesNo": SOtnYesNo,
       "SOcStmType": SOcStmType,
       "TraceIdentifierSDH": TraceIdentifierSDH,
       "TraceIdentifierSDHMsgType": TraceIdentifierSDHMsgType,
       "SOduType": SOduType,
       "SOduId": SOduId,
       "SOtuId": SOtuId,
       "SOtuType": SOtuType,
       "OperationalState": OperationalState,
       "SOtnTruthValue": SOtnTruthValue,
       "SOchOsId": SOchOsId,
       "stOTN": stOTN,
       "otnConfigMIB": otnConfigMIB,
       "sETYnTable": sETYnTable,
       "sETYnEntry": sETYnEntry,
       "etynType": etynType,
       "etynAdminState": etynAdminState,
       "etynOperationalState": etynOperationalState,
       "etynAvailabilityState": etynAvailabilityState,
       "etynAlarmProfile": etynAlarmProfile,
       "etynPMProfile": etynPMProfile,
       "etynLoopBack": etynLoopBack,
       "etynUPIvalue": etynUPIvalue,
       "etynMapMode": etynMapMode,
       "etynExtChannel": etynExtChannel,
       "etynClientShutdown": etynClientShutdown,
       "etynClientShutdownHoldoffTimer": etynClientShutdownHoldoffTimer,
       "etynNearEndALS": etynNearEndALS,
       "etyn8023bmFEC": etyn8023bmFEC,
       "etynRowStatus": etynRowStatus,
       "etynFectype": etynFectype,
       "sOCnSTMnTable": sOCnSTMnTable,
       "sOCnSTMnEntry": sOCnSTMnEntry,
       "ocnstmnType": ocnstmnType,
       "ocnstmnAdminState": ocnstmnAdminState,
       "ocnstmnOperationalState": ocnstmnOperationalState,
       "ocnstmnAvailabilityState": ocnstmnAvailabilityState,
       "ocnstmnAlarmProfile": ocnstmnAlarmProfile,
       "ocnstmnPMProfile": ocnstmnPMProfile,
       "ocnstmnLoopBack": ocnstmnLoopBack,
       "ocnstmnMapMode": ocnstmnMapMode,
       "ocnstmnClientShutdown": ocnstmnClientShutdown,
       "ocnstmnClientShutdownHoldoffTimer": ocnstmnClientShutdownHoldoffTimer,
       "ocnstmnNearEndALS": ocnstmnNearEndALS,
       "ocnstmnRowStatus": ocnstmnRowStatus,
       "ocnstmnTIMDetectionMode": ocnstmnTIMDetectionMode,
       "ocnstmnTraceIdExpected": ocnstmnTraceIdExpected,
       "ocnstmnTraceIdReceived": ocnstmnTraceIdReceived,
       "sODUkPTable": sODUkPTable,
       "sODUkPEntry": sODUkPEntry,
       "hoOduType": hoOduType,
       "hoOduId": hoOduId,
       "loOduType": loOduType,
       "loOduId": loOduId,
       "odukpAdminState": odukpAdminState,
       "odukpOperationalState": odukpOperationalState,
       "odukpAvailabilityState": odukpAvailabilityState,
       "odukpAlarmProfile": odukpAlarmProfile,
       "odukpPMProfile": odukpPMProfile,
       "odukpExpSAPI": odukpExpSAPI,
       "odukpExpDAPI": odukpExpDAPI,
       "odukpExpOperator": odukpExpOperator,
       "odukpTxSAPI": odukpTxSAPI,
       "odukpTxDAPI": odukpTxDAPI,
       "odukpTxOperator": odukpTxOperator,
       "odukpRxSAPI": odukpRxSAPI,
       "odukpRxDAPI": odukpRxDAPI,
       "odukpRxOperator": odukpRxOperator,
       "odukpTIMDefectMode": odukpTIMDefectMode,
       "odukpTIMActDis": odukpTIMActDis,
       "odukpDegradeInterval": odukpDegradeInterval,
       "odukpDegradeThreshold": odukpDegradeThreshold,
       "odukpDMSource": odukpDMSource,
       "odukpPrbs": odukpPrbs,
       "odukpPrbsSync": odukpPrbsSync,
       "odukpPrbsTimerDuration": odukpPrbsTimerDuration,
       "odukpPrbsCalcBer": odukpPrbsCalcBer,
       "odukpPrbsErrorCount": odukpPrbsErrorCount,
       "odukpNull": odukpNull,
       "odukpNIM": odukpNIM,
       "odukpRxPT": odukpRxPT,
       "odukpTxPT": odukpTxPT,
       "odukpExpPT": odukpExpPT,
       "odukpPLMConsequentActions": odukpPLMConsequentActions,
       "odukpOpuConfig": odukpOpuConfig,
       "odukpOpuConfigActual": odukpOpuConfigActual,
       "odukpTribSlot": odukpTribSlot,
       "odukpExpClientRate": odukpExpClientRate,
       "odukpNumOfGfpTs": odukpNumOfGfpTs,
       "odukpOperateRate": odukpOperateRate,
       "odukpRowStatus": odukpRowStatus,
       "sOTUkTable": sOTUkTable,
       "sOTUkEntry": sOTUkEntry,
       "otuId": otuId,
       "otuType": otuType,
       "otukAdminState": otukAdminState,
       "otukOperationalState": otukOperationalState,
       "otukAvailabilityState": otukAvailabilityState,
       "otukAlarmProfile": otukAlarmProfile,
       "otukPMProfile": otukPMProfile,
       "otukExpSAPI": otukExpSAPI,
       "otukExpDAPI": otukExpDAPI,
       "otukExpOperator": otukExpOperator,
       "otukTxSAPI": otukTxSAPI,
       "otukTxDAPI": otukTxDAPI,
       "otukTxOperator": otukTxOperator,
       "otukRxSAPI": otukRxSAPI,
       "otukRxDAPI": otukRxDAPI,
       "otukRxOperator": otukRxOperator,
       "otukTIMDefectMode": otukTIMDefectMode,
       "otukTIMActDis": otukTIMActDis,
       "otukDegradeInterval": otukDegradeInterval,
       "otukDegradeThreshold": otukDegradeThreshold,
       "otukLoopBack": otukLoopBack,
       "otukExtChannel": otukExtChannel,
       "otukClientShutdown": otukClientShutdown,
       "otukClientShutdownHoldoffTimer": otukClientShutdownHoldoffTimer,
       "otukFecType": otukFecType,
       "otukRowStatus": otukRowStatus,
       "otukNearEndALS": otukNearEndALS,
       "sOChOSTable": sOChOSTable,
       "sOChOSEntry": sOChOSEntry,
       "ochosId": ochosId,
       "ochosAdminState": ochosAdminState,
       "ochosOperationalState": ochosOperationalState,
       "ochosAvailabilityState": ochosAvailabilityState,
       "ochosAlarmProfile": ochosAlarmProfile,
       "ochosPMProfile": ochosPMProfile,
       "ochosSettingFreq": ochosSettingFreq,
       "ochosLineLaserEnabled": ochosLineLaserEnabled,
       "ochosLineLaserState": ochosLineLaserState,
       "ochosOEORegen": ochosOEORegen,
       "ochosRowStatus": ochosRowStatus,
       "ochosNearEndALS": ochosNearEndALS,
       "ochosEffectiveFreq": ochosEffectiveFreq,
       "ochosFirstSupportedFreq": ochosFirstSupportedFreq,
       "ochosLastSupportedFreq": ochosLastSupportedFreq,
       "ochosFreqGrid": ochosFreqGrid,
       "ochosConfigureTxPower": ochosConfigureTxPower,
       "ochosEffectiveTxPower": ochosEffectiveTxPower,
       "ochosFirstSupportedTxPower": ochosFirstSupportedTxPower,
       "ochosLastSupportedTxPower": ochosLastSupportedTxPower,
       "ochosTxPowerGrid": ochosTxPowerGrid,
       "ochosDGD": ochosDGD,
       "ochosCD": ochosCD,
       "ochosOSNR": ochosOSNR,
       "ochosEffectiveCDAutoSearchRangeHighVal": ochosEffectiveCDAutoSearchRangeHighVal,
       "ochosEffectiveCDAutoSearchRangeLowVal": ochosEffectiveCDAutoSearchRangeLowVal,
       "ochosConfiguredCDAutoSearchRangeHighVal": ochosConfiguredCDAutoSearchRangeHighVal,
       "ochosConfiguredCDAutoSearchRangeLowVal": ochosConfiguredCDAutoSearchRangeLowVal,
       "ochosSupportedCDAutoSearchRangeHighValThr": ochosSupportedCDAutoSearchRangeHighValThr,
       "ochosSupportedCDAutoSearchRangeLowValThr": ochosSupportedCDAutoSearchRangeLowValThr}
)
