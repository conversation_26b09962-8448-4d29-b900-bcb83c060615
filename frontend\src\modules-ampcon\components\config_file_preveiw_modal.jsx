import {Button, Divider, Flex, Input, Modal} from "antd";

const ConfigFilePreviewModal = ({
    title,
    isOpen,
    globalConfigContent,
    siteConfigContent,
    saveCallback,
    cancelButtonCallback,
    exportButtonCallback
}) => {
    const readonlyStyle = {
        minHeight: "240px",
        resize: "vertical",
        border: "2px solid rgb(206, 212, 218)",
        fontSize: "16px",
        borderRadius: "4px",
        boxShadow: "none",
        marginBottom: "30px"
    };

    return (
        <Modal
            className="ampcon-middle-modal"
            title={title}
            open={isOpen}
            onCancel={cancelButtonCallback}
            footer={
                <>
                    <Button key="export" type="primary" onClick={exportButtonCallback}>
                        Export
                    </Button>
                    <Button key="cancel" type="primary" onClick={cancelButtonCallback}>
                        Cancel
                    </Button>
                    <Button key="submit" type="primary" onClick={saveCallback}>
                        Save
                    </Button>
                </>
            }
        >
            <Divider />
            <Flex vertical style={{flex: 1}}>
                <Flex horizontal style={{flex: 1}}>
                    <h4>Global Config</h4>
                </Flex>
                <Input.TextArea style={readonlyStyle} value={globalConfigContent} readOnly />
                <Flex horizontal style={{flex: 1}}>
                    <h4>Generated Template Config</h4>
                </Flex>
                <Input.TextArea style={readonlyStyle} value={siteConfigContent} readOnly />
            </Flex>
        </Modal>
    );
};

export default ConfigFilePreviewModal;
