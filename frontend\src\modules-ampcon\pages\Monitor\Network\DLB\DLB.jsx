import {Flex, Form, Select, Button, Input, Card, DatePicker, Checkbox, Tooltip} from "antd";
import {Linechart, MultiLineChart} from "@/modules-ampcon/components/echarts_common";
import {useEffect, useRef, useState} from "react";
import {getAlarmCount} from "@/store/modules/common/alarm_slice";
import {useDispatch} from "react-redux";
import {fetchStaticHistory, fetchImportedAndProvisioningSuccessSwitch} from "@/modules-ampcon/apis/dashboard_api";
import dayjs from "dayjs";
import {AmpConCustomTable, createColumnConfig} from "@/modules-ampcon/components/custom_table";

import {searchSvg} from "@/utils/common/iconSvg";
import Icon from "@ant-design/icons/lib/components/Icon";
import styles from "./DLB.module.scss";

const PortCheckBox = ({options, isTag, flowOrPort, setFlowOrPort, selectedPort, setSelectedPort}) => {
    // const [portNames, setPortNames] = useState([
    //     {label: "all", value: "all", checked: false},
    //     {label: "te1", value: "te1", checked: false},
    //     {label: "te2", value: "te2", checked: false},
    //     {label: "te3", value: "te3", checked: false}
    // ]);
    // const handleCheckboxChange = value => {
    //     // 使用map函数遍历portNames数组，并根据value找到对应的元素，更新其checked属性
    //     setPortNames(prevportNames =>
    //         prevportNames.map(option => (option.value === value ? {...option, checked: !option.checked} : option))
    //     );
    // };

    const [searchValue, setSearchValue] = useState("");

    const handlePortChange = value => {
        setSelectedPort(value);
        setSearchValue("");
        setFilteredOptions(options);
        if (value === "" || value === undefined) setFlowOrPort(null);
        else setFlowOrPort("port");
    };

    const [filteredOptions, setFilteredOptions] = useState(options);

    useEffect(() => {
        setFilteredOptions(options);
    }, [options]);

    const handleSearch = value => {
        setSearchValue(value);
        const filtered = options.filter(option => option.toLowerCase().includes(value.toLowerCase()));
        setFilteredOptions(filtered);
    };

    const dropDownRender = menu => {
        return (
            <div>
                <div>
                    <Input
                        value={searchValue}
                        onChange={e => handleSearch(e.target.value)}
                        placeholder="Search"
                        prefix={<Icon component={searchSvg} />}
                        allowClear
                        style={{width: "100%", height: "32px", marginBottom: "3px"}}
                    />
                </div>
                {menu}
            </div>
        );
    };

    return (
        <div>
            <Select
                value={selectedPort}
                onChange={value => {
                    handlePortChange(value);
                }}
                placeholder="Select PortName"
                allowClear
                dropdownRender={menu => dropDownRender(menu)}
                disabled={!isTag || flowOrPort === "flow"}
            >
                {filteredOptions.map(option => (
                    <Option key={option} value={option}>
                        {option}
                    </Option>
                ))}
            </Select>
        </div>
        // <div>
        //     <Checkbox>
        //         {portNames.map(option => (
        //             <label key={option.key} htmlFor="option">
        //                 <Input
        //                     type="checkbox"
        //                     checked={option.checked}
        //                     onChange={() => handleCheckboxChange(option.value)}
        //                 />
        //                 {option.label}
        //             </label>
        //         ))}
        //     </Checkbox>
        // </div>
    );
};
const DLBButton = () => {
    const [isTag, setIsTag] = useState(false);
    const [flowOrPort, setFlowOrPort] = useState("Port");
    const defaultTopK = ["Top5", "Top10", "Top15"];
    const [topK, setTopK] = useState(defaultTopK);
    const [portNames, setPortNames] = useState(["all", "te1", "te2"]);
    const [selectedPort, setSelectedPort] = useState();
    const [selectedFlow, setSelectedFlow] = useState();

    const [timeRange, setTimeRange] = useState(["", ""]);

    const handleTagChange = e => {
        if (e.target.value === null || e.target.value === "") {
            clearFilters();
            setIsTag(false);
        } else setIsTag(true);
    };
    const handleFlowChange = value => {
        setSelectedFlow(value);
        if (value === "" || value === undefined || value === null) setFlowOrPort(null);
        else setFlowOrPort("flow");
    };

    const clearFilters = () => {
        setSelectedFlow(null);
        setFlowOrPort(null);
        setSelectedPort(null);
        setTimeRange(["", ""]);
    };
    return (
        <div>
            <Flex gap="large" style={{justifyContent: "flex-start"}}>
                <Form
                    layout="inline"
                    style={{
                        display: "flex",
                        justifyContent: "flex-start",
                        alignItems: "flex-start",
                        flexWrap: "nowrap",
                        marginBottom: "16px"
                    }}
                >
                    <Form.Item
                        label="SN/Service Tag"
                        name="tag"
                        wrapperCol={{style: {width: 180}}}
                        rules={[{required: true, message: "Please enter SN/Service Tag!"}]}
                    >
                        <Input placeholder="SN/Service Tag" onChange={handleTagChange} style={{height: "32px"}} />
                    </Form.Item>
                    <Form.Item label="Top Flow" name="topflow" wrapperCol={{style: {width: 180}}}>
                        <Select
                            placeholder="Select TopFlow"
                            value={selectedFlow}
                            onChange={handleFlowChange}
                            allowClear
                            disabled={!isTag || flowOrPort === "port"}
                        >
                            {topK.map(option => (
                                <option key={option} value={option}>
                                    {option}
                                </option>
                            ))}
                        </Select>
                    </Form.Item>
                    <Form.Item label="Port Name" name="portname" wrapperCol={{style: {width: 180}}}>
                        <PortCheckBox
                            options={portNames}
                            isTag={isTag}
                            flowOrPort={flowOrPort}
                            selectedPort={selectedPort}
                            setSelectedPort={setSelectedPort}
                            setFlowOrPort={setFlowOrPort}
                        />
                    </Form.Item>
                    <Form.Item label="Time" name="time" wrapperCol={{style: {width: 280}}}>
                        <RangePicker
                            showTime={{format: "HH:mm"}}
                            format="YYYY-MM-DD HH:mm"
                            style={{height: "32px", marginLeft: "32px"}}
                            // onFocus={handleFocus}
                            onChange={(_, dateString) => {
                                setTimeRange(dateString);
                            }}
                            disabledDate={current => {
                                const now = new Date();
                                const oneMonthAgo = new Date();
                                oneMonthAgo.setMonth(now.getMonth() - 1);
                                return current && (current > now || current < oneMonthAgo);
                            }}
                        />
                    </Form.Item>
                    <Form.Item>
                        <Button type="primary" onClick={clearFilters} style={{width: 120}}>
                            Clear All Filters
                        </Button>
                    </Form.Item>
                </Form>
            </Flex>
        </div>
    );
};

const {Option} = Select;
const {RangePicker} = DatePicker;
const DLB = () => {
    const [statisticsHistoryData, setStatisticsHistoryData] = useState({
        bandWidthList: [],
        packetLossList: [],
        timeList: [],
        loading: true
    });
    const portsColumns = [
        {
            title: "Port Name",
            render: (_, record) => {
                return `${record.port_name}:${record.sn}`;
            }
        },
        createColumnConfig("Time", "modified_time"),
        {
            ...createColumnConfig("5 Sec Input Rate", "input_rate"),
            render: (_, record) => {
                return `${record.input_rate}:${record.sn}`;
            }
        },
        {
            ...createColumnConfig("5 Sec Output Rate", "output_rate"),
            render: (_, record) => {
                return `${record.output_rate}:${record.sn}`;
            }
        },
        createColumnConfig("Total Packets Without Errors", "platform_model"),
        createColumnConfig("Total Packets With Errors", "platform_model")
    ];
    const portsMatchFieldsList = [{name: "ne_name"}];
    const portsRef = useRef(null);
    useEffect(() => {
        const fetchHistoryData = async () => {
            await fetchStaticHistory().then(res => {
                if (res.status === 200) {
                    setStatisticsHistoryData({
                        timeList: res.data.map(i => dayjs(i.create_time).format("HH:mm")).reverse(),
                        cpuUsageList: res.data.map(i => parseFloat(i.cpu)).reverse(),
                        memUsageList: res.data.map(i => parseFloat(i.mem)).reverse()
                    });
                }
            });
        };
        fetchHistoryData();

        const timer = setInterval(() => {
            fetchHistoryData().then();
            dispatch(getAlarmCount());
            portsRef.current.refreshTable();
        }, 60000);

        return () => clearInterval(timer);
    }, []);
    const dispatch = useDispatch();
    return (
        <Card style={{flex: 1}} className="DLBstyle">
            <h2 style={{margin: "8px 0 20px"}}>DLB</h2>
            <div>
                <DLBButton />
            </div>
            <div className={styles.DLB}>
                <Card
                    title={<div>Banbdwidth Utillzation</div>}
                    bordered={false}
                    style={{
                        height: "100%",
                        width: "100%"
                    }}
                >
                    <MultiLineChart chartXAxis={statisticsHistoryData.bandWidthList} />
                </Card>
                <Card
                    title={<div>Packet Loss Rate</div>}
                    bordered={false}
                    style={{
                        height: "100%",
                        width: "100%"
                    }}
                >
                    <MultiLineChart chartXAxis={statisticsHistoryData.packetLossList} />
                </Card>
                <Card
                    title={<div>Throughput(Output)</div>}
                    bordered={false}
                    style={{
                        height: "100%",
                        width: "100%"
                    }}
                >
                    <MultiLineChart chartXAxis={statisticsHistoryData.bandWidthList} />
                </Card>
                <Card
                    title={<div>Throughput(Input)</div>}
                    bordered={false}
                    style={{
                        height: "100%",
                        width: "100%"
                    }}
                >
                    <MultiLineChart chartXAxis={statisticsHistoryData.packetLossList} />
                </Card>
                <Card
                    title={<div>Port and Rate</div>}
                    bordered={false}
                    style={{
                        height: "100%",
                        width: "100%"
                    }}
                    className={styles.portsCard}
                >
                    <AmpConCustomTable
                        fetchAPIInfo={fetchImportedAndProvisioningSuccessSwitch}
                        columns={portsColumns}
                        matchFieldsList={portsMatchFieldsList}
                        ref={portsRef}
                        isShowPagination
                    />
                </Card>
            </div>
        </Card>
    );
};

export default DLB;
