from flask import Blueprint, request, jsonify
from sqlalchemy import func
from server.util import utils
from server.db.models.user import User, TacacsConfig, user_db
from server.util.permission import super_user_permission
from server.db.models import inventory
from sqlalchemy import or_, asc, desc
from server.util.utils import is_name_valid
from server.util.check_generate_pwd_util import CheckGeneratePasswd
from server.constants import FAKE_PASSWORD
import logging
from flask_login import current_user
from werkzeug.security import generate_password_hash
from server.db.models.monitor import monitor_db
import json
from server.user import logged_in_users

Log = logging.getLogger(__name__)

new_user_mold = Blueprint('user_mold', __name__, template_folder='templates')
cgp = CheckGeneratePasswd()

inven_db = inventory.inven_db

@new_user_mold.route('/management', methods=['POST', 'GET'])
@super_user_permission.require(http_exception=403)
def user_management():
    db_session = user_db.get_session()
    query_user = db_session.query(User).join(inventory.UserGroupMapping, User.id == inventory.UserGroupMapping.user_id, isouter=True) \
        .join(inventory.Group, inventory.UserGroupMapping.group_id == inventory.Group.id, isouter=True) \
        .group_by(User) \
        .add_columns(func.group_concat(inventory.Group.id))
    page_num, page_size, total_count, query_obj = utils.query_helper(User, pre_query=query_user)
    all_group = db_session.query(inventory.Group).all()
    # Format the response
    response = {
        "data": [{
            "id": user.id,
            "name": user.name,
            "ctime": user.ctime.strftime('%Y-%m-%d %H:%M:%S') if user.ctime else "",
            "type": user.type,
            "userType": user.user_type,
            "email": user.email,
            "groupId": group_id,
            "isLocked": True if user.is_lock else False
        } for user, group_id in query_obj],
        "page": page_num,
        "pageSize": page_size,
        "total": total_count,
        "status": 200,
        "allGroups": [{"id": group.id,"name": group.group_name} for group in all_group]
    }

    return jsonify(response)


@new_user_mold.route('/check_pwd', methods=['POST'])
def check_pwd():
    try:
        form = request.get_json()
        first_password = form.get('password')
        ret, ret_info = cgp.check_random_passwd(first_password)
        msg = {'info': ret_info, 'status': 500}
        if not ret:
            return jsonify(msg)
        if first_password == FAKE_PASSWORD:
            return jsonify(msg)
        return jsonify({'info': 'Success', 'status': 200})
    except Exception as e:
        Log.error(str(e))
        msg = {'info': 'Check password failed', 'status': 500}
        return jsonify(msg)


@new_user_mold.route('/add', methods=['POST', 'GET'])
@super_user_permission.require(http_exception=403)
def user_add():
    try:
        form = request.get_json()
        username = form.get('username')
        first_password = form.get('password')
        confirm_password = form.get('confirmPassword')
        user_role = form.get('userRole')
        user_email = form.get('email')
        user_type = form.get('userType')
        group_id_list = form.get('groupIdList', [])
        if not is_name_valid(username) or ' ' in username:
            msg = {'info': 'The username is invalid!', 'status': 500}
            return jsonify(msg)
        if len(user_email) > 128:
            msg = {'info': 'The user_email length exceeds 128!', 'status': 500}
            return jsonify(msg)
        if user_type not in ('global', 'group'):
            msg = {'info': 'The user type is invalid!', 'status': 500}
            return jsonify(msg)
        ret, ret_info = cgp.check_random_passwd(first_password)
        if not ret:
            msg = {'info': ret_info, 'status': 500}
            return jsonify(msg)
        if first_password == confirm_password:
            if first_password == FAKE_PASSWORD:
                msg = {'info': 'Password cannot be {}'.format(FAKE_PASSWORD), 'status': 500}
                return jsonify(msg)
            session_query = user_db.get_session()
            with session_query.begin():
                user_query = session_query.query(User).filter(User.name == username).first()
                if not user_query:
                    obj1 = User(name=username, password=first_password,
                                type=user_role, email=user_email, user_type=user_type)
                    user_db.insert(obj1)
                    user_db.update_user_group_mapping(user_db.query_user(username).id, group_id_list)
                    msg = {'info': 'User Added Successfully', 'status': 200}
                else:
                    msg = {'info': 'The user already exists', 'status': 500}
            return jsonify(msg)
        else:
            msg = {'info': 'The password is different', 'status': 500}
            return jsonify(msg)
    except Exception as e:
        Log.error(str(e))
        msg = {'info': 'The user add fail', 'status': 500}
        return jsonify(msg)


@new_user_mold.route('/del_user/<string:username>', methods=['GET'])
@super_user_permission.require(http_exception=403)
@utils.operation_log(method='delete_user', contents='delete user {username}')
def del_user(username):
    msg = {'info': 'User %s deleted successfully' % username, 'status': 200}
    if username == 'admin':
        msg = {'info': 'The admin user cannot be deleted!', 'status': 500}
        return jsonify(msg)
    try:
        if current_user.id != username:
            user_db.delete_user(username)
        else:
            msg = {'info': 'Can not delete current user ', 'status': 500}
    except Exception as e:
        Log.exception(e)
        msg = {'info': 'The %s user delete is  fail' % username, 'status': 500}
    finally:
        return jsonify(msg)


@new_user_mold.route('/edit', methods=['POST'])
@super_user_permission.require(http_exception=403)
@utils.operation_log(method='edit_user', contents='edit user {username}')
def user_edit():
    form = request.get_json()
    username = form.get('username')
    no_change_pwd = form.get('noChangePwd', "")
    old_password = form.get('oldPassword')
    first_password = form.get('password')
    user_role = form.get('userRole')
    confirm_password = form.get('confirmPassword')
    user_email = form.get('email')
    user_type = form.get('userType')
    group_id_list = form.get('groupIdList', [])
    if username == 'admin':
        msg = {'info': 'The admin user cannot be edited!', 'status': 500}
        return jsonify(msg)
    if user_email and len(user_email) > 128:
        msg = {'info': 'The user_email length exceeds 128!', 'status': 500}
        return jsonify(msg)
    if no_change_pwd != FAKE_PASSWORD:
        if not first_password:
            msg = {'info': 'First_password is empty!', 'status': 500}
            return jsonify(msg)
        if not confirm_password:
            msg = {'info': 'Confirm_password is empty!', 'status': 500}
            return jsonify(msg)
        if first_password != confirm_password:
            msg = {'info': 'First_password and confirm_password are inconsistent ', 'status': 500}
            return jsonify(msg)
        ret, ret_info = cgp.check_random_passwd(confirm_password)
        if not ret:
            msg = {'info': ret_info, 'status': '500'}
            return jsonify(msg)
    if first_password == FAKE_PASSWORD:
        msg = {'info': 'Password cannot be {}'.format(FAKE_PASSWORD), 'status': 500}
        return jsonify(msg)
    if user_type not in ('global', 'group'):
        msg = {'info': 'The user type is invalid!', 'status': 500}
        return jsonify(msg)
    user = user_db.query_user(username)
    updates = {}
    if user:
        if no_change_pwd == FAKE_PASSWORD:
            pass
        elif not user.check_password_hash(old_password):
            msg = {'info': 'The user authentication failed', 'status': 500}
            return jsonify(msg)
        else:
            updates['passwd'] = generate_password_hash(first_password)
        updates['name'] = username
        updates['email'] = user_email
        updates['type'] = user_role
        updates['user_type'] = user_type
        updates['group_id_list'] = group_id_list
        session_query = user_db.get_session()
        with session_query.begin():
            user_db.update_user({'name': [username]}, updates)
        if username in logged_in_users:
            logged_in_users.remove(username)
        msg = {'info': 'The user information has been modified', 'status': 200}
        return jsonify(msg)
    else:
        msg = {'info': 'The user authentication failed', 'status': 500}
        return jsonify(msg)


@new_user_mold.route('/unlock_user/<string:username>', methods=['GET'])
@super_user_permission.require(http_exception=403)
@utils.operation_log(method='unlock_user', contents='unlock user {username}')
def unlock_user(username):
    try:
        user_db.update_user_status(username, 0)
        monitor_db.unlock_auth_attempts(username)
        msg = {'info': 'User %s unlock successfully' % username, 'status': 200}
    except Exception as e:
        Log.exception(e)
        msg = {'info': 'The %s user unlock fail' % username, 'status': 500}
    finally:
        return jsonify(msg)


@new_user_mold.route('/lock_user/<string:username>', methods=['GET'])
@super_user_permission.require(http_exception=403)
@utils.operation_log(method='lock_user', contents='lock user {username}')
def lock_user(username):
    try:
        user_db.update_user_status(username, 1)
        monitor_db.unlock_auth_attempts(username)
        msg = {'info': 'User %s lock successfully' % username, 'status': 200}
    except Exception as e:
        Log.exception(e)
        msg = {'info': 'The %s user lock fail' % username, 'status': 500}
    finally:
        return jsonify(msg)


@new_user_mold.route('/tacacs_settings', methods=['GET'])
@super_user_permission.require(http_exception=403)
def get_tacacs_settings():
    tacacs_settings= user_db.get_tacacs_config()
    if not tacacs_settings:
        return jsonify({})
    tacacs_obj = {
        'enable': tacacs_settings.enable,
        'serverHost': tacacs_settings.server_host,
        'serverHostII': tacacs_settings.server_host_ii,
        'serverSecret': tacacs_settings.server_secret,
        'sessionTimeout': tacacs_settings.session_timeout,
        'authProtocol': tacacs_settings.auth_protocol,
        'userMapping': json.loads(tacacs_settings.user_mapping) if tacacs_settings.user_mapping else {}
    }
    return jsonify(tacacs_obj)


@new_user_mold.route('/tacacs_settings', methods=['POST'])
@super_user_permission.require(http_exception=403)
@utils.operation_log(method='tacacs_settings', contents='configure login tacacs settings')
def set_tacacs_settings():
    try:
        data = request.get_json()
        tacacs_setting = user_db.get_tacacs_config()
        msg = {'info': 'Success to set TACACS+ configurations.', 'status': 200}
        if tacacs_setting:
            tacacs_setting.enable = data['enable']
            tacacs_setting.server_host = data['serverHost']
            tacacs_setting.server_host_ii = data['serverHostII']
            tacacs_setting.server_secret = data['serverSecret']
            tacacs_setting.session_timeout = data['sessionTimeout']
            tacacs_setting.auth_protocol = data['authProtocol']
            tacacs_setting.user_mapping = json.dumps(data['userMapping'])
            user_db.merge(tacacs_setting)
        else:
            tacacs_setting = TacacsConfig()
            tacacs_setting.enable = data['enable']
            tacacs_setting.server_host = data['serverHost']
            tacacs_setting.server_host_ii = data['serverHostII']
            tacacs_setting.server_secret = data['serverSecret']
            tacacs_setting.session_timeout = data['sessionTimeout']
            tacacs_setting.auth_protocol = data['authProtocol']
            tacacs_setting.user_mapping = json.dumps(data['userMapping'])
            user_db.insert(tacacs_setting)
    except Exception as e:
        msg = {'info': 'Fail to set TACACS+ configurations. {0}'.format(e), 'status': 500}
    return jsonify(msg)


@new_user_mold.route('/update_current_user', methods=['POST'])
@utils.operation_log(method='update_current_user', contents='updata password {user_name}', require_methods='POST')
def user_info():
    form = request.get_json()
    old_pwd = form.get('oldPassword')
    new_pwd = form.get('password')
    confirm_pwd = form.get('confirmPassword')
    email = form.get('email')
    if new_pwd == confirm_pwd:
        user = current_user.id
        session_query = user_db.get_session()
        user_query = session_query.query(User).filter(User.name == user).first()

        ret, ret_info = cgp.check_random_passwd(new_pwd)
        if not ret:
            msg = {'info': ret_info, 'status': 500}
            return jsonify(msg)

        if user_query.check_password_hash(old_pwd):
            user_query.password = new_pwd
            user_query.email = email
            user_db.merge(user_query, session_query)
            msg = {'info': 'The current user info change success', 'status': 200}
        else:
            msg = {'info': 'The old password is Wrong', 'status': 500}
        return jsonify(msg)

    else:
        msg = {'info': 'The current user info change failed, password is different', 'status': 500}
        return jsonify(msg)
    

@new_user_mold.route('/group/<string:group_name>', methods=['POST', 'GET'])
@utils.operation_log(method='query_user_group', contents='query group', require_methods='GET')
def user_group_management(group_name):
    group_name = group_name
    session = inven_db.get_session()
    group_id = session.query(inventory.Group.id).filter(inventory.Group.group_name == group_name).first()[0]
    user_id_list = list(map(lambda x: x[0], session.query(inventory.UserGroupMapping.user_id).filter(inventory.UserGroupMapping.group_id == group_id).all()))
    user_query = session.query(User, func.group_concat(inventory.Group.group_name)).join(inventory.UserGroupMapping, User.id == inventory.UserGroupMapping.user_id, isouter=True).join(inventory.Group, inventory.UserGroupMapping.group_id == inventory.Group.id, isouter=True).group_by(User).filter(User.id.in_(user_id_list))

    page_num, page_size, total_count, query_obj = utils.query_helper(User, pre_query=user_query)
    res_data = []
    for user in query_obj:
        res_data.append({
            'name': user[0].name,
            'type': user[0].type,
            'add_on': user[1],
            'group_name': group_name
        })
    return jsonify({'data': res_data, "page": page_num, "pageSize": page_size, "total": total_count, "status": 200})
