import AnsibleJobsList from "@/modules-ampcon/pages/Maintain/NetworkConfiguration/Automation/AnsibleJobsList/ansible_jobs_list";
import OtherDevices from "@/modules-ampcon/pages/Maintain/NetworkConfiguration/Automation/OtherDevices/other_devices";
import Playbook from "@/modules-ampcon/pages/Maintain/NetworkConfiguration/Automation/Playbooks/playbook";
import Schedule from "@/modules-ampcon/pages/Maintain/NetworkConfiguration/Automation/Schedules/schedule";
import UserManagement from "@/modules-ampcon/pages/System/user_management";
import SwitchView from "@/modules-ampcon/pages/Dashboard/SwitchView/switch_view";
import ConfigTemplateIndex from "@/modules-ampcon/pages/Service/Switch/ConfigTemplate/config_template_index";
import SwitchManager from "@/modules-ampcon/pages/Resource/AuthorityManagement/authority_manager";
import GroupManagement from "@/modules-ampcon/pages/Resource/AuthorityManagement/GroupManagement/group_manager";
import FabricManagement from "@/modules-ampcon/pages/Resource/AuthorityManagement/FabricManagement/fabric_management";
import SiteManagement from "@/modules-ampcon/pages/Resource/AuthorityManagement/SiteManagement/site_management";
import CLIConfiguration from "@/modules-ampcon/pages/Maintain/CliConfig/cli_config";
import Switch from "@/modules-ampcon/pages/Service/Switch/switch";
import GlobalConfiguration from "@/modules-ampcon/pages/Service/Switch/GlobalConfiguration/global_configuration";
import SwitchConfiguration from "@/modules-ampcon/pages/Service/Switch/SwitchConfiguration/switch_configuration";
import ConfigFileView from "@/modules-ampcon/pages/Service/Switch/ConfigFileView/configfile_view";
import SwitchModel from "@/modules-ampcon/pages/Service/Switch/SwitchModel/switch_model";
import AmpConSystemConfig from "@/modules-ampcon/pages/Service/Switch/SystemConfig/system_config";
import SystemBackup from "@/modules-ampcon/pages/Maintain/NetworkConfiguration/SystemBackup/system_backup";
import ProtectedRoute from "@/modules-ampcon/utils/util";
import Alarm from "@/modules-ampcon/pages/Monitor/Alarm/alarm";
import LicenseView from "@/modules-ampcon/pages/System/SoftwareLicense/LicenseView/license_view";
import LicenseManagement from "@/modules-ampcon/pages/System/SoftwareLicense/LicenseManagement/license_management";
import LicenseLog from "@/modules-ampcon/pages/System/SoftwareLicense/LicenseLog/license_log";
import TelemetryView from "@/modules-ampcon/pages/Dashboard/Telemetry/telemetry_view";
import TopoEntrance from "@/modules-ampcon/pages/Topo/topo_entrance";
import TopoUnit from "@/modules-ampcon/pages/Topo/unit";
import TopoUnitDetail from "@/modules-ampcon/pages/Topo/unit_detail";
import TopoUnitView from "@/modules-ampcon/pages/Topo/unit_view";
import fabric from "@/modules-ampcon/pages/Topo/fabric";
import FabricAdd from "@/modules-ampcon/pages/Topo/fabric_detail";
import SwitchTelemetry from "@/modules-ampcon/pages/Service/Switch/switch_telemetry";
import IpPool from "@/modules-ampcon/pages/Resource/Pool/IpPool/ip_pool";
import AsnPool from "@/modules-ampcon/pages/Resource/Pool/AsnPool/asn_pool";
import AreaPool from "@/modules-ampcon/pages/Resource/Pool/AreaPool/area_pool";
// import DLB from "@/modules-ampcon/pages/Monitor/Network/DLB/DLB";
import NICsInventory from "../pages/Service/Nics/Inventory/nic_inventory";
import NICsMonitoring from "../pages/Service/Nics/Monitoring/nic_monitoring";
import Rack from "../pages/Topo/rack";
import DLB from "../pages/Monitor/Network/dlb";

export const ampConRoute = [
    {
        path: "dashboard/switch_view",
        element: <ProtectedRoute component={SwitchView} />
    },
    {
        path: "maintain/network_config/automation/playbooks",
        element: <ProtectedRoute component={Playbook} />
    },
    {
        path: "maintain/network_config/automation/other_devices",
        element: <ProtectedRoute component={OtherDevices} />
    },
    {
        path: "maintain/network_config/automation/ansible_jobs_list/:type",
        element: <AnsibleJobsList />
    },
    {
        path: "maintain/network_config/automation/schedule",
        element: <ProtectedRoute component={Schedule} />
    },
    {
        path: "maintain/network_config/system_backup",
        element: <ProtectedRoute component={SystemBackup} />
    },
    {
        path: "maintain/cli_configuration",
        element: <ProtectedRoute component={CLIConfiguration} />
    },
    {
        path: "system/user_management",
        element: <ProtectedRoute component={UserManagement} />
    },
    {
        path: "resource/auth_management/device_license_management/:type",
        element: <SwitchManager />
    },
    {
        path: "resource/auth_management/group_management",
        element: <ProtectedRoute component={GroupManagement} />
    },
    {
        path: "service/switch/config_template/:type",
        element: <ConfigTemplateIndex />
    },
    {
        path: "service/switch/switch",
        element: <ProtectedRoute component={Switch} />
    },
    {
        path: "service/switch/global_configuration",
        element: <ProtectedRoute component={GlobalConfiguration} />
    },
    {
        path: "service/switch/switch_configuration",
        element: <ProtectedRoute component={SwitchConfiguration} />
    },
    {
        path: "service/switch/config_files_view",
        element: <ProtectedRoute component={ConfigFileView} />
    },
    {
        path: "service/switch/switch_model",
        element: <ProtectedRoute component={SwitchModel} />
    },
    {
        path: "service/switch/system_management",
        element: <ProtectedRoute component={AmpConSystemConfig} />
    },
    {
        path: "dashboard/telemetry_dashboard",
        element: <ProtectedRoute component={TelemetryView} />
    },
    {
        path: "service/switch/:sn",
        element: <ProtectedRoute component={SwitchTelemetry} />
    },
    {
        path: "topo/topology",
        element: <ProtectedRoute component={TopoEntrance} />
    },
    {
        path: "topo/unit",
        element: <ProtectedRoute component={TopoUnit} />
    },
    {
        path: "topo/unit/detail",
        element: <ProtectedRoute component={TopoUnitDetail} />
    },
    {
        path: "topo/unit/view",
        element: <ProtectedRoute component={TopoUnitView} />
    },
    {
        path: "topo/fabric",
        element: <ProtectedRoute component={fabric} />
    },
    {
        path: "topo/fabric_detail",
        element: <ProtectedRoute component={FabricAdd} />
    }
];

export const ampConDCRoute = [
    {
        path: "dashboard/switch_view",
        element: <ProtectedRoute component={SwitchView} />
    },
    {
        path: "dashboard/telemetry_dashboard",
        element: <ProtectedRoute component={TelemetryView} />
    },
    {
        path: "maintain/automation/playbooks",
        element: <ProtectedRoute component={Playbook} />
    },
    {
        path: "maintain/automation/other_devices",
        element: <ProtectedRoute component={OtherDevices} />
    },
    {
        path: "maintain/automation/ansible_jobs_list/:type",
        element: <AnsibleJobsList />
    },
    {
        path: "maintain/automation/schedule",
        element: <ProtectedRoute component={Schedule} />
    },
    {
        path: "maintain/system_backup",
        element: <ProtectedRoute component={SystemBackup} />
    },
    {
        path: "maintain/cli_configuration",
        element: <ProtectedRoute component={CLIConfiguration} />
    },
    {
        path: "system/user_management",
        element: <ProtectedRoute component={UserManagement} />
    },
    {
        path: "system/software_license/license_view",
        element: <ProtectedRoute component={LicenseView} />
    },
    {
        path: "system/software_license/license_management",
        element: <ProtectedRoute component={LicenseManagement} />
    },
    {
        path: "system/software_license/license_log",
        element: <ProtectedRoute component={LicenseLog} />
    },
    {
        path: "resource/auth_management/device_license_management/:type",
        element: <SwitchManager />
    },
    {
        path: "resource/auth_management/group_management",
        element: <ProtectedRoute component={GroupManagement} />
    },
    {
        path: "resource/auth_management/fabric_management",
        element: <ProtectedRoute component={FabricManagement} />
    },
    {
        path: "resource/auth_management/site_management",
        element: <ProtectedRoute component={SiteManagement} />
    },
    {
        path: "resource/pool/ip_pool",
        element: <ProtectedRoute component={IpPool} />
    },
    {
        path: "resource/pool/area_pool",
        element: <ProtectedRoute component={AreaPool} />
    },
    {
        path: "resource/pool/asn_pool",
        element: <ProtectedRoute component={AsnPool} />
    },
    {
        path: "service/config_template/:type",
        element: <ConfigTemplateIndex />
    },
    {
        path: "service/switch",
        element: <ProtectedRoute component={Switch} />
    },
    {
        path: "service/global_configuration",
        element: <ProtectedRoute component={GlobalConfiguration} />
    },
    {
        path: "service/switch_configuration",
        element: <ProtectedRoute component={SwitchConfiguration} />
    },
    {
        path: "service/config_files_view",
        element: <ProtectedRoute component={ConfigFileView} />
    },
    {
        path: "service/switch_model",
        element: <ProtectedRoute component={SwitchModel} />
    },
    {
        path: "service/system_config",
        element: <ProtectedRoute component={AmpConSystemConfig} />
    },
    {
        path: "monitor/alarm",
        element: <ProtectedRoute component={Alarm} />
    },
    {
        path: "monitor/network/dlb",
        element: <ProtectedRoute component={DLB} />
    },
    {
        path: "system/software_license/license_view",
        element: <ProtectedRoute component={LicenseView} />
    },
    {
        path: "system/software_license/license_management",
        element: <ProtectedRoute component={LicenseManagement} />
    },
    {
        path: "system/software_license/license_log",
        element: <ProtectedRoute component={LicenseLog} />
    },
    {
        path: "topo/topology",
        element: <ProtectedRoute component={TopoEntrance} />
    },
    {
        path: "topo/unit",
        element: <ProtectedRoute component={TopoUnit} />
    },
    {
        path: "topo/unit/detail",
        element: <ProtectedRoute component={TopoUnitDetail} />
    },
    {
        path: "topo/unit/view",
        element: <ProtectedRoute component={TopoUnitView} />
    },
    {
        path: "topo/rack",
        element: <ProtectedRoute component={Rack} />
    },
    {
        path: "topo/fabric",
        element: <ProtectedRoute component={fabric} />
    },
    {
        path: "topo/fabric_detail",
        element: <ProtectedRoute component={FabricAdd} />
    },
    {
        path: "service/switch/:sn",
        element: <ProtectedRoute component={SwitchTelemetry} />
    },
    {
        path: "service/nics/inventory",
        element: <ProtectedRoute component={NICsInventory} />
    },
    {
        path: "service/nics/monitoring",
        element: <ProtectedRoute component={NICsMonitoring} />
    }
];
