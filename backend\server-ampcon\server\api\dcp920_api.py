import json
import logging
import traceback
import threading
from flask import Blueprint, jsonify, Response, request
from server.db.models import inventory
from server.db.models.inventory import SwitchMenuTreeInfo, SwitchNeInfo, SwitchGis
from server.db.models.otn import OtnTempData, OtnDeviceBasic
from server.util import dcp920_util
from server.util.permission import admin_permission

inven_db = inventory.inven_db
dcp920_module = Blueprint("dcp920_module", __name__, template_folder="templates")
LOG = logging.getLogger(__name__)


@dcp920_module.route("/info/query", methods=["GET"])
@admin_permission.require(http_exception=403)
def query_info():
    id = request.args.get('id')
    ip = request.args.get('ip')
    if id is None and ip is None:
        return Response(json.dumps({"data": "", "errorCode": 1, "errorMsg": "At least one of IP and ID is required!"}),
                        mimetype="application/json")
    # 下发指令查询设备数据
    data = dcp920_util.beat_sync_dcp920_device_info_single(id=id, ip=ip)
    errorCode = 0
    if data is None:
        errorCode = 1
    result = {"data": data, "errorCode": errorCode, "errorMsg": ""}
    return Response(json.dumps(result), mimetype="application/json")


@dcp920_module.route("/info/get", methods=["GET"])
@admin_permission.require(http_exception=403)
def get_info():
    id = request.args.get('id')
    ip = request.args.get('ip')
    if id is None and ip is None:
        return Response(json.dumps({"data": "", "errorCode": 1, "errorMsg": "At least one of IP and ID is required!"}),
                        mimetype="application/json")

    db_session = inven_db.get_session()
    dcp_temp_data = db_session.query(OtnTempData)
    if id is None:
        data = dcp_temp_data.filter(OtnTempData.ip == ip).first()
    elif ip is None:
        data = dcp_temp_data.filter(OtnTempData.id == id).first()
    else:
        data = dcp_temp_data.filter(OtnTempData.id == id, OtnTempData.ip == ip).first()

    if not data:
        return Response(json.dumps({"data": "", "errorCode": 1, "errorMsg": "Data is empty!"}),
                        mimetype="application/json")
    result = {"data": json.loads(data.data), "errorCode": 0, "errorMsg": ""}
    return Response(json.dumps(result), mimetype="application/json")

@dcp920_module.route("/info/test_result/get", methods=["GET"])
@admin_permission.require(http_exception=403)
def get_test_result():
    ip = request.args.get('ip')
    data, errorCode = dcp920_util.get_test_result(ip)
    result = {"data": data, "errorCode": errorCode, "errorMsg": ""}
    return Response(json.dumps(result), mimetype="application/json")


@dcp920_module.route("/config/query", methods=["GET"])
@admin_permission.require(http_exception=403)
def get_config():
    ip = request.args.get('ip')
    config_type = request.args.get('config_type')
    if ip is None or config_type is None:
        return Response(json.dumps({"data": "", "errorCode": 1, "errorMsg": "Ip and config_type is required!"}),
                        mimetype="application/json")
    db_session = inven_db.get_session()
    device = db_session.query(OtnDeviceBasic).filter(OtnDeviceBasic.ip == ip).first()
    if not device:
        return Response(json.dumps({"data": "", "errorCode": 1, "errorMsg": "Device is not exist!"}),
                        mimetype="application/json")
    data, errorCode = dcp920_util.get_config(ip, config_type)
    result = {"data": data, "errorCode": errorCode, "errorMsg": ""}
    return Response(json.dumps(result), mimetype="application/json")


@dcp920_module.route("/config/modify", methods=["PUT"])
@admin_permission.require(http_exception=403)
def modify_config():
    args = request.args
    ip = args.get("ip")
    config_type = args.get("config_type")
    key = args.get("key")
    value = args.get("value")
    if ip is None or config_type is None or key is None or value is None:
        return Response(
            json.dumps({"data": "", "errorCode": 1, "errorMsg": "Ip, config_type, key and value is required!"}),
            mimetype="application/json")
    db_session = inven_db.get_session()
    device = db_session.query(OtnDeviceBasic).filter(OtnDeviceBasic.ip == ip).first()
    if not device:
        return Response(json.dumps({"data": "", "errorCode": 1, "errorMsg": "Device is not exist!"}),
                        mimetype="application/json")

    data, errorCode = dcp920_util.modify_config(ip, config_type, key, value)
    result = {"data": data, "errorCode": errorCode, "errorMsg": ""}
    return Response(json.dumps(result), mimetype="application/json")
