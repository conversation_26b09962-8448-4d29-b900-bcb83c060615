import {useEffect, useState} from "react";
import {useLocation, useNavigate} from "react-router-dom";
import {Tabs} from "antd";
import Service from "@/modules-otn/pages/otn/service/service";
import BusinessList from "@/modules-otn/pages/otn/service/business_list";
import styles from "./business.module.scss";

const Business = () => {
    const items = [
        {
            key: "create-business",
            label: "Create Business",
            style: {flex: 1, display: "flex"},
            children: <Service key="service_optics" serviceType="optics" />
        },
        {
            key: "business-list",
            label: "Business List",
            style: {flex: 1, display: "flex"},
            children: <BusinessList />
        }
    ];

    const navigate = useNavigate();
    const location = useLocation();
    const [currentActiveKey, setCurrentActiveKey] = useState();
    const pathReg = /(create-business|business-list)$/;

    useEffect(() => {
        // e2e scroll render
        const layoutDom = document.querySelector("main.ant-layout-content");
        layoutDom?.classList?.add("overflow-hidden");
        return () => {
            layoutDom?.classList?.remove("overflow-hidden");
        };
    }, []);

    useEffect(() => {
        const currentPath = location.pathname;
        if (pathReg.test(currentPath)) {
            setCurrentActiveKey(currentPath.match(pathReg)[0]);
        }
    }, [location.pathname]);

    const onChange = key => {
        const currentPath = location.pathname;
        if (pathReg.test(currentPath)) {
            const matchLength = currentPath.match(pathReg)[0].length;
            const parentPath = currentPath.slice(0, -matchLength);
            navigate(parentPath + key);
        }
    };

    return (
        <Tabs
            destroyInactiveTabPane
            items={items}
            activeKey={currentActiveKey}
            onChange={onChange}
            rootClassName={styles.tabs}
        />
    );
};

export default Business;
