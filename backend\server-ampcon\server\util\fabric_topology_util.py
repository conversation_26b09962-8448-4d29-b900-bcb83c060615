from server.db.models.dc_blueprint import dc_fabric_db


def update_topology_node_info(topology, fabric_topo_id):
    links = {}
    for edge in topology['edges']:
        source_device = edge['source']
        target_device = edge['target']
        source_sn = edge['source_sn']
        target_sn = edge['target_sn']
        link_info = edge['link_info']
        link_type = edge['type']

        for link in link_info:
            logic_link = link['logic_link']
            if source_device not in links:
                links[source_device] = {}
            links[source_device][logic_link] = {
                "target_sn": target_sn,
                "source_port": link['source_port'],
                "link_type": link_type,
            }

            # 更新目标链接信息
            if target_device not in links:
                links[target_device] = {}
            links[target_device][logic_link] = {
                "target_sn": source_sn,
                "source_port": link['target_port'],
                "link_type": link_type
            }

    for node in topology['nodes']:
        logic_name = node['logic_device']
        node['node_info']['links'] = links[logic_name]
        dc_fabric_db.update_fabric_topology_node_info(logic_name, node['switch_sn'], fabric_topo_id, node['type'], node['node_info'])


# 根据template 生成topo
class TopologyBuilder:
    def __init__(self, template_info, topology_type, underlay_routing_protocol, overlay_control_protocol):
        self.template_info = template_info
        self.topology_type = topology_type
        self.underlay_routing_protocol = underlay_routing_protocol
        self.overlay_control_protocol = overlay_control_protocol
        self.nodes = []
        self.edges = []

    def build_topology(self):
        if self.topology_type == "5-stage":
            self._build_5_stage_topology()
        elif self.topology_type == "3-stage":
            self._build_3_stage_topology()
        return {
            "nodes": self.nodes,
            "edges": self.edges,
        }

    def _build_5_stage_topology(self):
        pods = self.template_info.get("pod", [])
        spines_list = []
        for pod in pods:
            spine_count = pod.get("spine_count", 1)
            units = pod.get("unit", [])
            pod_name = pod.get("name")
            # print(pod, units, pod_name)
            topo = self._build_unit_and_spine(units, spine_count, link_spine_count=1, prefix=pod_name)
            spines_list.extend(topo.get("spines_name"))

        super_spine_count = self.template_info.get("super_spine_count", 1)
        self._build_spine(super_spine_count, spines_list, link_spine_count=1, name_prefix="super_spine", type="super_spine")

    def _build_3_stage_topology(self):
        spine_count = self.template_info.get("spine_count", 1)
        units = self.template_info.get("unit", [])
        # build时默认使用1个link_spine_count
        self._build_unit_and_spine(units, spine_count, link_spine_count=1)

    def _build_unit_and_spine(self, units, spine_count, link_spine_count, prefix=None):
        leaf_list = []
        name_prefix_index = {}
        for unit in units:
            # 相同unit增加序号区分
            base_name = f"{prefix}_{unit.get('name')}" if prefix else unit.get('name')
            if base_name not in name_prefix_index:
                name_prefix = f"{base_name}_001"
                name_prefix_index[base_name] = 2
            else:
                name_prefix = f"{base_name}_00{name_prefix_index[base_name]}"
                name_prefix_index[base_name] += 1
            topo = self._build_topology_by_unit(unit, name_prefix)
            leaf_list.extend(topo.get("leaf_list"))
            
        spine_prefix = f"{prefix}_spine" if prefix else "spine"
        topo = self._build_spine(spine_count, leaf_list, link_spine_count, name_prefix=spine_prefix)

        return {
            "spines_name": topo.get("spines_name"),
        }

    def _build_spine(self, spine_count, leafs, link_spine_count, name_prefix="spine", type="spine"):
        spines_name = []
        for i in range(1, spine_count + 1):
            self.nodes.append(self._build_topology_node(
                logic_device=f"{name_prefix}_{i}",
                unit_name="",
                node_type=type,
            ))
            spines_name.append(f"{name_prefix}_{i}")
        for leaf in leafs:
            self.edges.extend(self._build_edges(spines_name, leaf, link_spine_count, type="spine_link"))

        return {
            "spines_name": spines_name,
        }

    def _build_topology_by_unit(self, unit, name_prefix):
        leaf_list = []
        leaf_info = unit.get("unit_info", {}).get("leaf", [])
        access_info = unit.get("unit_info", {}).get("access", [])

        # 构建leaf
        for leaf in leaf_info:
            if leaf["strategy"] == "mlag":
                self._build_mlag_leafs(leaf, name_prefix, leaf_list)
            else:
                self.nodes.append(self._build_topology_node(
                    logic_device=f"{name_prefix}_{leaf['name']}_1",
                    unit_name=name_prefix,
                    node_type="leaf",
                    node_info=leaf
                ))
                leaf_list.append(f"{name_prefix}_{leaf['name']}_1")

        # 构建access
        for access in access_info:
            self._build_access_nodes(access, name_prefix)

        return {
            "leaf_list": leaf_list,
        }

    def _build_mlag_leafs(self, leaf, name_prefix, leaf_list):
        for i in range(1, 3):  # mlag 创建两个节点
            self.nodes.append(self._build_topology_node(
                logic_device=f"{name_prefix}_{leaf['name']}_{i}",
                unit_name=name_prefix,
                node_type="leaf",
                node_info=leaf
            ))
            leaf_list.append(f"{name_prefix}_{leaf['name']}_{i}")

        # mlag 构建peer link
        sources = [f"{name_prefix}_{leaf['name']}_1"]
        target = f"{name_prefix}_{leaf['name']}_2"
        count = leaf["mlag_peer_count"]
        self.edges.extend(self._build_edges(sources, target, count, type="peer_link"))

    def _build_access_nodes(self, access_info, unit_name):
        access_name = access_info.get("name")
        logic_device = f"{unit_name}_{access_name}"
        self.edges.extend(self._build_access_edges(logic_device, access_info, unit_name))
        self.nodes.append(self._build_topology_node(
            logic_device=logic_device,
            unit_name=unit_name,
            node_type="access",
            node_info=access_info
        ))

    def _build_access_edges(self, logic_device, access_info, unit_name):
        leaf = access_info.get("leaf")
        access_method = access_info.get("access_method", "")
        physic_link_count = access_info['physic_link_count']

        # 根据method 决定source
        if access_method == "dual":
            sources = [
                f"{unit_name}_{leaf}_1",
                f"{unit_name}_{leaf}_2"
            ]
        elif access_method == "single":
            peer_leaf = access_info.get("peer_leaf")
            source = f"{unit_name}_{leaf}_1" if peer_leaf == "first" else f"{unit_name}_{leaf}_2"
            sources = [source]
        else:
            sources = [f"{unit_name}_{leaf}_1"]

        return self._build_edges(sources, logic_device, physic_link_count)

    def _build_topology_node(self, logic_device, unit_name, node_type, node_info={}):
        # todo 根据type, node_type, underlay_routing_protocol, overlay_control_protocol 决定参数
        node = {
            "logic_device": logic_device,
            "group": unit_name,
            "type": node_type,
            "switch_sn": "",
            "node_info": {}
        }
        
        if node_type == "access":
            if node_info.get("access_method", ""):
                info = {
                    "mlag_vlanid": node_info.get("mlag_vlanid", ""),
                    "access_link": [] 
                }
                node["node_info"].update(info)
            return node
        elif node_type == "leaf":
            if node_info.get("strategy", "") == "mlag":
                info = {
                    "mlag_peerlink_vlanid": node_info.get("mlag_peerlink_vlanid", ""), 
                    "mlag_vlanid": node_info.get("mlag_vlanid", ""), 
                    "l3_interface_ip": "",  
                    "target_l3_interface_ip": "", 
                    "domain_id": "", 
                    "peer_link": [],
                    "access_link": []
                }
                node["node_info"].update(info)
        
        if self.underlay_routing_protocol == "BGP":
            info = {
                "vtep_interface": "",
                "loopback0_address": "",
                "asn": "",
                "spine_link": []
            }
        else:
            info = {
                "vtep_interface": "",
                "loopback0_address": "",
                "routed_interface_address": "",
                "area_id": "",
                "spine_link": []
            }
        
        node["node_info"].update(info)
         
        if self.overlay_control_protocol == "Static VXLAN":
            pass

        return node

    def _build_edges(self, sources, target, count, type="physic_link"):
        edges = []
        for source in sources:
            link_info = []
            for i in range(1, count + 1):
                link_info.append({
                    "logic_link": f"{source}<->{target}[{i}]",
                    "source_port": "",
                    "target_port": ""
                })
            edge = {
                "link_info": link_info,
                "source": source,
                "source_sn": "",
                "type": type,
                "target": target,
                "target_sn": ""
            }
            edges.append(edge)
        return edges
    
        
# update 仅更新topologyde edges 节点不会改变
def update_topology_by_template(topology, template_info, type): 
    if type == "5-stage": 
        pods = template_info.get("pod", [])
        edges = topology.get("edges", [])
        super_spine_count = template_info.get("super_spine_count", 1)
        for pod in pods:
            spine_count = pod.get("spine_count", 1)
            units = pod.get("unit", []) 
            pod_name = pod.get("name")
            update_unit_and_spine_edges(units, spine_count, edges, prefix=pod_name)

            link_super_spine_count = pod.get("link_superspine_count", 1)
            target_prefix = f"{pod_name}_spine"
            # print(link_super_spine_count, target_prefix)
            update_spine_edges(super_spine_count, link_super_spine_count, edges, target_prefix=target_prefix, spine_prefix="super_spine")
        topology["edges"] = edges
        
    elif type == "3-stage":
        spine_count = template_info.get("spine_count", 1)
        units = template_info.get("unit", [])
        edges = topology.get("edges", [])
        update_unit_and_spine_edges(units, spine_count, edges)
        topology["edges"] = edges
            

def update_unit_and_spine_edges(units, spine_count, edges, prefix=None):
    name_prefix_index = {}
    for unit in units:
        base_name = f"{prefix}_{unit.get('name')}" if prefix else unit.get('name')
        if base_name not in name_prefix_index:
            name_prefix = f"{base_name}_001"
            name_prefix_index[base_name] = 2
        else:
            name_prefix = f"{base_name}_00{name_prefix_index[base_name]}"
            name_prefix_index[base_name] += 1
 
        update_edges_by_unit(unit, name_prefix, edges)
        
        spine_name_prefix = f"{prefix}_spine" if prefix else "spine"
        link_spine_count = unit.get("unit_info").get('link_spine_count', 1)
        update_spine_edges(spine_count, link_spine_count, edges, target_prefix=name_prefix, spine_prefix=spine_name_prefix)

    
def update_spine_edges(spine_count, link_spine_count, edges, target_prefix, spine_prefix="spine"): 
    for i in range(1, spine_count+1): 
        source=f"{spine_prefix}_{i}"
        for edge in edges:
            # 找到target前缀为指定值的target 用于区分不同unit
            if edge['source'] == source and edge['target'].startswith(target_prefix):
                target = edge['target']
                current_count = len(edge['link_info'])
                # print(source, target, link_spine_count, current_count)
                if link_spine_count > current_count:
                    # 添加新的连接
                    for i in range(current_count + 1, link_spine_count + 1):
                        edge['link_info'].append({
                            "logic_link": f"{source}<->{target}[{i}]",
                            "source_port": "",
                            "target_port": ""
                        })
                elif link_spine_count < current_count:
                    # 只保留前 link_spine_count 个连接
                    edge['link_info'] = edge['link_info'][:link_spine_count]
            

def update_edges_by_unit(unit, name_prefix, edges):
    leaf_info = unit.get("unit_info").get("leaf", [])
    access_info = unit.get("unit_info").get("access", [])
    for leaf in leaf_info:
        if leaf["strategy"] == "mlag":
            # 更新mlag peer link count
            source=f"{name_prefix}_{leaf['name']}_1"
            target=f"{name_prefix}_{leaf['name']}_2"
            count = leaf["mlag_peer_count"]
            update_edges_link_count(source, target, count, edges)
    
    for access in access_info:
        # 更新access link count
        leaf = access.get("leaf")
        access_name = access.get("name")
        access_method = access.get("access_method", "")
        physic_link_count = access.get("physic_link_count")
        logic_device = f"{name_prefix}_{access_name}"
        
        if access_method == "dual":
            sources = [
                f"{name_prefix}_{leaf}_1",
                f"{name_prefix}_{leaf}_2"
            ]
        elif access_method == "single":
            peer_leaf = access.get("peer_leaf")
            source = f"{name_prefix}_{leaf}_1" if peer_leaf == "first" else f"{name_prefix}_{leaf}_2"
            sources = [source]
        else:
            sources = [f"{name_prefix}_{leaf}_1"]

        for source in sources:
            update_edges_link_count(source, logic_device, physic_link_count, edges)


# 用于更新连接数 
def update_edges_link_count(source, target, count, edges):
    for edge in edges:
        if edge['source'] == source and edge['target'] == target:
            current_count = len(edge['link_info'])
            if count > current_count:
                # 添加新的连接
                for i in range(current_count + 1, count + 1):
                    edge['link_info'].append({
                        "logic_link": f"{source}<->{target}[{i}]",
                        "source_port": "",
                        "target_port": ""
                    })
            elif count < current_count:
                # 只保留前 physic_link_count 个连接
                edge['link_info'] = edge['link_info'][:count]
            break