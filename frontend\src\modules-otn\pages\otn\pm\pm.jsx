import {useMemo, useState, useRef} from "react";
import {Button, Dropdown, message, Space} from "antd";
import {useRequest} from "ahooks";
import {useSelector} from "react-redux";
import dayjs from "dayjs";

import {DownOutlined} from "@ant-design/icons";
import {getPmInfo} from "@/modules-otn/apis/api";
import {sortArr} from "@/modules-otn/utils/util";
import {CustomTable} from "@/modules-otn/components/common/custom_table";
import {bigModal} from "@/modules-otn/components/modal/custom_modal";
import PmChart from "./pm_chart";
import PmFilters from "./pm_filters";
import styles from "./pm.module.scss";

export default function PM({pmType}) {
    const {labelList} = useSelector(state => state.languageOTN);
    const [tableSources, setTableSources] = useState([]);
    const filterFormRef = useRef(null);

    const {runAsync, loading} = useRequest(getPmInfo, {manual: true});

    const trendChartOption = useMemo(() => {
        if (tableSources.length === 0) return [];
        const optionSet = new Set();
        tableSources.forEach(item => {
            optionSet.add(item["pm-parameter"]);
        });

        return sortArr(Array.from(optionSet)).map(key => ({key, label: key, value: key}));
    }, [tableSources]);

    const onSearch = async () => {
        try {
            const {form} = filterFormRef.current;
            await form.validateFields();
            const values = form.getFieldsValue();
            const params = {
                ...values,
                "pm-type": pmType
            };
            runAsync(params).then(res => {
                const {apiResult, apiMessage, data} = res;
                if (apiResult === "fail") {
                    message.error(apiMessage);
                    setTableSources([]);
                    return;
                }

                setTableSources(data);
            });
        } catch (e) {
            // console.log(e);
        }
    };

    const onTrendChartOptionsClick = ({key}) => {
        bigModal({
            title: labelList.trend_chart,
            content: <PmChart tableSources={tableSources} checkedChartKey={key} />,
            cancelButtonProps: {style: {display: "none"}}
        });
    };

    return (
        <div className={styles.container}>
            {pmType === "HISTORY" && (
                <div className={styles.title_container}>
                    <Dropdown
                        disabled={!tableSources?.length}
                        menu={{
                            items: trendChartOption,
                            style: trendChartOption?.length > 10 ? {height: 325, overflowY: "auto"} : {},
                            onClick: onTrendChartOptionsClick
                        }}
                    >
                        <Button>
                            <Space>
                                {labelList.trend_chart}
                                <DownOutlined />
                            </Space>
                        </Button>
                    </Dropdown>
                </div>
            )}
            <div
                style={{
                    marginTop: pmType === "HISTORY" ? 40 : 0,
                    marginBottom: 8
                }}
            >
                <PmFilters
                    ref={filterFormRef}
                    pmType={pmType}
                    buttons={{
                        label: "operation",
                        render: (
                            <div style={{width: 280}}>
                                <Button type="primary" onClick={onSearch}>
                                    {labelList.search_title}
                                </Button>
                            </div>
                        )
                    }}
                />
            </div>
            <CustomTable
                type="pm"
                rootStyle={pmType === "HISTORY" ? {paddingBottom: 20} : {}}
                initTitle=""
                initHead
                scroll={false}
                loading={loading}
                initDataSource={tableSources}
                columnFormat={{
                    "monitoring-date-time": value => dayjs(value).format("YYYY-MM-DD HH:mm:ss"),
                    "max-value": value => (["", undefined, null].includes(value) ? "-" : value),
                    "min-value": value => (["", undefined, null].includes(value) ? "-" : value),
                    "average-value": value => (["", undefined, null].includes(value) ? "-" : value),
                    "current-value": value => (["", undefined, null].includes(value) ? "-" : value)
                }}
            />
        </div>
    );
}
