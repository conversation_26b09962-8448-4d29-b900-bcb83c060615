# SNMP MIB module (ST-6200-MIB) expressed in pysnmp data model.
#
# This Python module is designed to be imported and executed by the
# pysnmp library.
#
# See https://www.pysnmp.com/pysnmp for further information.
#
# Notes
# -----
# ASN.1 source file://./ST-6200-MIB.mib
# Produced by pysmi-1.5.11 at Tue Apr 22 03:03:06 2025
# On host pica8 platform Linux version 5.15.0-122-generic by user root
# Using Python version 3.11.10 (main, Sep  7 2024, 18:35:41) [GCC 11.4.0]

if 'mibBuilder' not in globals():
    import sys

    sys.stderr.write(__doc__)
    sys.exit(1)

# Import base ASN.1 objects even if this MIB does not use it

(Integer,
 OctetString,
 ObjectIdentifier) = mibBuilder.importSymbols(
    "ASN1",
    "Integer",
    "OctetString",
    "ObjectIdentifier")

(NamedValues,) = mibBuilder.importSymbols(
    "ASN1-ENUMERATION",
    "NamedValues")
(ConstraintsIntersection,
 ConstraintsUnion,
 SingleValueConstraint,
 ValueRangeConstraint,
 ValueSizeConstraint) = mibBuilder.importSymbols(
    "ASN1-REFINEMENT",
    "ConstraintsIntersection",
    "ConstraintsUnion",
    "SingleValueConstraint",
    "ValueRangeConstraint",
    "ValueSizeConstraint")

# Import SMI symbols from the MIBs this MIB depends on

(usmUserName,) = mibBuilder.importSymbols(
    "SNMP-USER-BASED-SM-MIB",
    "usmUserName")

(ModuleCompliance,
 NotificationGroup) = mibBuilder.importSymbols(
    "SNMPv2-CONF",
    "ModuleCompliance",
    "NotificationGroup")

(Bits,
 Counter32,
 Counter64,
 Gauge32,
 Integer32,
 IpAddress,
 ModuleIdentity,
 MibIdentifier,
 NotificationType,
 ObjectIdentity,
 MibScalar,
 MibTable,
 MibTableRow,
 MibTableColumn,
 TimeTicks,
 Unsigned32,
 iso) = mibBuilder.importSymbols(
    "SNMPv2-SMI",
    "Bits",
    "Counter32",
    "Counter64",
    "Gauge32",
    "Integer32",
    "IpAddress",
    "ModuleIdentity",
    "MibIdentifier",
    "NotificationType",
    "ObjectIdentity",
    "MibScalar",
    "MibTable",
    "MibTableRow",
    "MibTableColumn",
    "TimeTicks",
    "Unsigned32",
    "iso")

(DisplayString,
 PhysAddress,
 RowStatus,
 TextualConvention) = mibBuilder.importSymbols(
    "SNMPv2-TC",
    "DisplayString",
    "PhysAddress",
    "RowStatus",
    "TextualConvention")

(portNo,
 shelfId,
 slotNo,
 subPortNo,
 subSlotNo) = mibBuilder.importSymbols(
    "ST-COMMON-MIB",
    "portNo",
    "shelfId",
    "slotNo",
    "subPortNo",
    "subSlotNo")

(enterpriseProducts,) = mibBuilder.importSymbols(
    "ST-ROOT-MIB",
    "enterpriseProducts")


# MODULE-IDENTITY

st6200 = ModuleIdentity(
    (1, 3, 6, 1, 4, 1, 52642, 1, 21)
)
if mibBuilder.loadTexts:
    st6200.setRevisions(
        ("2018-03-01 11:37",)
    )
if mibBuilder.loadTexts:
    st6200.setLastUpdated("201803011832Z")
if mibBuilder.loadTexts:
    st6200.setDescription("STN6200 MIB")


# Types definitions


# TEXTUAL-CONVENTIONS



# MIB Managed Objects in the order of their OIDs

_OlpConfigMIB_ObjectIdentity = ObjectIdentity
olpConfigMIB = _OlpConfigMIB_ObjectIdentity(
    (1, 3, 6, 1, 4, 1, 52642, 1, 21, 2)
)
_OlpConfigTable_Object = MibTable
olpConfigTable = _OlpConfigTable_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 21, 2, 1)
)
if mibBuilder.loadTexts:
    olpConfigTable.setStatus("current")
if mibBuilder.loadTexts:
    olpConfigTable.setDescription("Description.")
_OlpConfigEntry_Object = MibTableRow
olpConfigEntry = _OlpConfigEntry_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 21, 2, 1, 1)
)
olpConfigEntry.setIndexNames(
    (0, "ST-COMMON-MIB", "shelfId"),
    (0, "ST-COMMON-MIB", "slotNo"),
    (0, "ST-COMMON-MIB", "subSlotNo"),
)
if mibBuilder.loadTexts:
    olpConfigEntry.setStatus("current")
if mibBuilder.loadTexts:
    olpConfigEntry.setDescription("Description.")


class _OlpSwitchMode_Type(Integer32):
    """Custom type olpSwitchMode based on Integer32"""
    defaultValue = 2

    subtypeSpec = Integer32.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        SingleValueConstraint(
            *(1,
              2,
              3,
              4,
              5,
              6,
              7,
              8,
              9,
              10)
        )
    )
    namedValues = NamedValues(
        *(("manual", 1),
          ("auto", 2),
          ("force", 3),
          ("lockL1", 4),
          ("lockL2", 5),
          ("clean", 6),
          ("forceL1", 7),
          ("forceL2", 8),
          ("manualL1", 9),
          ("manualL2", 10))
    )


_OlpSwitchMode_Type.__name__ = "Integer32"
_OlpSwitchMode_Object = MibTableColumn
olpSwitchMode = _OlpSwitchMode_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 21, 2, 1, 1, 1),
    _OlpSwitchMode_Type()
)
olpSwitchMode.setMaxAccess("read-write")
if mibBuilder.loadTexts:
    olpSwitchMode.setStatus("current")
if mibBuilder.loadTexts:
    olpSwitchMode.setDescription("Description.")


class _OlpSwitchState_Type(Integer32):
    """Custom type olpSwitchState based on Integer32"""
    defaultValue = 3

    subtypeSpec = Integer32.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        SingleValueConstraint(
            *(3,
              12)
        )
    )
    namedValues = NamedValues(
        *(("primary", 3),
          ("secondary", 12))
    )


_OlpSwitchState_Type.__name__ = "Integer32"
_OlpSwitchState_Object = MibTableColumn
olpSwitchState = _OlpSwitchState_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 21, 2, 1, 1, 2),
    _OlpSwitchState_Type()
)
olpSwitchState.setMaxAccess("read-only")
if mibBuilder.loadTexts:
    olpSwitchState.setStatus("current")
if mibBuilder.loadTexts:
    olpSwitchState.setDescription("Description.")


class _OlpSwitchHoldoffTime_Type(Unsigned32):
    """Custom type olpSwitchHoldoffTime based on Unsigned32"""
    defaultValue = 0

    subtypeSpec = Unsigned32.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        ValueRangeConstraint(0, 32000),
    )


_OlpSwitchHoldoffTime_Type.__name__ = "Unsigned32"
_OlpSwitchHoldoffTime_Object = MibTableColumn
olpSwitchHoldoffTime = _OlpSwitchHoldoffTime_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 21, 2, 1, 1, 3),
    _OlpSwitchHoldoffTime_Type()
)
olpSwitchHoldoffTime.setMaxAccess("read-write")
if mibBuilder.loadTexts:
    olpSwitchHoldoffTime.setStatus("current")
if mibBuilder.loadTexts:
    olpSwitchHoldoffTime.setDescription("Switch holdoff time of PtoS. (Unit: Millisecond)")


class _OlpRevertiveOfSwitchMode_Type(Integer32):
    """Custom type olpRevertiveOfSwitchMode based on Integer32"""
    defaultValue = 0

    subtypeSpec = Integer32.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        SingleValueConstraint(
            *(0,
              1)
        )
    )
    namedValues = NamedValues(
        *(("nonrevertive", 0),
          ("revertive", 1))
    )


_OlpRevertiveOfSwitchMode_Type.__name__ = "Integer32"
_OlpRevertiveOfSwitchMode_Object = MibTableColumn
olpRevertiveOfSwitchMode = _OlpRevertiveOfSwitchMode_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 21, 2, 1, 1, 4),
    _OlpRevertiveOfSwitchMode_Type()
)
olpRevertiveOfSwitchMode.setMaxAccess("read-write")
if mibBuilder.loadTexts:
    olpRevertiveOfSwitchMode.setStatus("current")
if mibBuilder.loadTexts:
    olpRevertiveOfSwitchMode.setDescription("Revertive switch mode")


class _OlpRSMsht_Type(Unsigned32):
    """Custom type olpRSMsht based on Unsigned32"""
    defaultValue = 1

    subtypeSpec = Unsigned32.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        ValueRangeConstraint(1, 32000),
    )


_OlpRSMsht_Type.__name__ = "Unsigned32"
_OlpRSMsht_Object = MibTableColumn
olpRSMsht = _OlpRSMsht_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 21, 2, 1, 1, 5),
    _OlpRSMsht_Type()
)
olpRSMsht.setMaxAccess("read-write")
if mibBuilder.loadTexts:
    olpRSMsht.setStatus("current")
if mibBuilder.loadTexts:
    olpRSMsht.setDescription("Switch holdoff time for revertive switch mode.(unit: Minute)")


class _OlpAutoBackMode_Type(Integer32):
    """Custom type olpAutoBackMode based on Integer32"""
    defaultValue = 1

    subtypeSpec = Integer32.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        SingleValueConstraint(
            *(0,
              1)
        )
    )
    namedValues = NamedValues(
        *(("notautoback", 0),
          ("autoback", 1))
    )


_OlpAutoBackMode_Type.__name__ = "Integer32"
_OlpAutoBackMode_Object = MibTableColumn
olpAutoBackMode = _OlpAutoBackMode_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 21, 2, 1, 1, 6),
    _OlpAutoBackMode_Type()
)
olpAutoBackMode.setMaxAccess("read-write")
if mibBuilder.loadTexts:
    olpAutoBackMode.setStatus("current")
if mibBuilder.loadTexts:
    olpAutoBackMode.setDescription("Auto-back mode")


class _OlpABsht_Type(Unsigned32):
    """Custom type olpABsht based on Unsigned32"""
    defaultValue = 30

    subtypeSpec = Unsigned32.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        ValueRangeConstraint(0, 32000),
    )


_OlpABsht_Type.__name__ = "Unsigned32"
_OlpABsht_Object = MibTableColumn
olpABsht = _OlpABsht_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 21, 2, 1, 1, 7),
    _OlpABsht_Type()
)
olpABsht.setMaxAccess("read-write")
if mibBuilder.loadTexts:
    olpABsht.setStatus("current")
if mibBuilder.loadTexts:
    olpABsht.setDescription("Holdoff time of auto-back mode(unit: Second)")


class _OlpButtonEnabled_Type(Integer32):
    """Custom type olpButtonEnabled based on Integer32"""
    defaultValue = 1

    subtypeSpec = Integer32.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        SingleValueConstraint(
            *(0,
              1)
        )
    )
    namedValues = NamedValues(
        *(("disable", 0),
          ("enable", 1))
    )


_OlpButtonEnabled_Type.__name__ = "Integer32"
_OlpButtonEnabled_Object = MibTableColumn
olpButtonEnabled = _OlpButtonEnabled_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 21, 2, 1, 1, 8),
    _OlpButtonEnabled_Type()
)
olpButtonEnabled.setMaxAccess("read-write")
if mibBuilder.loadTexts:
    olpButtonEnabled.setStatus("current")
if mibBuilder.loadTexts:
    olpButtonEnabled.setDescription("Button enable.")


class _OlpConsoleEnable_Type(Integer32):
    """Custom type olpConsoleEnable based on Integer32"""
    defaultValue = 1

    subtypeSpec = Integer32.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        SingleValueConstraint(
            *(0,
              1)
        )
    )
    namedValues = NamedValues(
        *(("disable", 0),
          ("enable", 1))
    )


_OlpConsoleEnable_Type.__name__ = "Integer32"
_OlpConsoleEnable_Object = MibTableColumn
olpConsoleEnable = _OlpConsoleEnable_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 21, 2, 1, 1, 9),
    _OlpConsoleEnable_Type()
)
olpConsoleEnable.setMaxAccess("read-write")
if mibBuilder.loadTexts:
    olpConsoleEnable.setStatus("current")
if mibBuilder.loadTexts:
    olpConsoleEnable.setDescription("Console Enable")


class _OlpCardDescription_Type(DisplayString):
    """Custom type olpCardDescription based on DisplayString"""
    defaultValue = OctetString("")

    subtypeSpec = DisplayString.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        ValueSizeConstraint(0, 32),
    )


_OlpCardDescription_Type.__name__ = "DisplayString"
_OlpCardDescription_Object = MibTableColumn
olpCardDescription = _OlpCardDescription_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 21, 2, 1, 1, 10),
    _OlpCardDescription_Type()
)
olpCardDescription.setMaxAccess("read-write")
if mibBuilder.loadTexts:
    olpCardDescription.setStatus("current")
if mibBuilder.loadTexts:
    olpCardDescription.setDescription("Card description")


class _OlpSwitchCounter_Type(Unsigned32):
    """Custom type olpSwitchCounter based on Unsigned32"""
    defaultValue = 0


_OlpSwitchCounter_Type.__name__ = "Unsigned32"
_OlpSwitchCounter_Object = MibTableColumn
olpSwitchCounter = _OlpSwitchCounter_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 21, 2, 1, 1, 11),
    _OlpSwitchCounter_Type()
)
olpSwitchCounter.setMaxAccess("read-only")
if mibBuilder.loadTexts:
    olpSwitchCounter.setStatus("current")
if mibBuilder.loadTexts:
    olpSwitchCounter.setDescription("Switch counter")


class _OlpRemoteState_Type(Integer32):
    """Custom type olpRemoteState based on Integer32"""
    defaultValue = 1

    subtypeSpec = Integer32.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        SingleValueConstraint(
            *(0,
              1)
        )
    )
    namedValues = NamedValues(
        *(("unnormal", 0),
          ("normal", 1))
    )


_OlpRemoteState_Type.__name__ = "Integer32"
_OlpRemoteState_Object = MibTableColumn
olpRemoteState = _OlpRemoteState_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 21, 2, 1, 1, 12),
    _OlpRemoteState_Type()
)
olpRemoteState.setMaxAccess("read-only")
if mibBuilder.loadTexts:
    olpRemoteState.setStatus("current")
if mibBuilder.loadTexts:
    olpRemoteState.setDescription("remote state.")


class _OlpActiveAlmThsOpRx_Type(Integer32):
    """Custom type olpActiveAlmThsOpRx based on Integer32"""
    defaultValue = -2000

    subtypeSpec = Integer32.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        ValueRangeConstraint(-6000, 2500),
    )


_OlpActiveAlmThsOpRx_Type.__name__ = "Integer32"
_OlpActiveAlmThsOpRx_Object = MibTableColumn
olpActiveAlmThsOpRx = _OlpActiveAlmThsOpRx_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 21, 2, 1, 1, 13),
    _OlpActiveAlmThsOpRx_Type()
)
olpActiveAlmThsOpRx.setMaxAccess("read-write")
if mibBuilder.loadTexts:
    olpActiveAlmThsOpRx.setStatus("current")
if mibBuilder.loadTexts:
    olpActiveAlmThsOpRx.setDescription("Active Alarm threshold of optical power")


class _OlpActiveAlmThsOpTx_Type(Integer32):
    """Custom type olpActiveAlmThsOpTx based on Integer32"""
    defaultValue = -2000

    subtypeSpec = Integer32.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        ValueRangeConstraint(-6000, 2500),
    )


_OlpActiveAlmThsOpTx_Type.__name__ = "Integer32"
_OlpActiveAlmThsOpTx_Object = MibTableColumn
olpActiveAlmThsOpTx = _OlpActiveAlmThsOpTx_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 21, 2, 1, 1, 14),
    _OlpActiveAlmThsOpTx_Type()
)
olpActiveAlmThsOpTx.setMaxAccess("read-write")
if mibBuilder.loadTexts:
    olpActiveAlmThsOpTx.setStatus("current")
if mibBuilder.loadTexts:
    olpActiveAlmThsOpTx.setDescription("Active Alarm threshold of optical power")


class _OlpStandbyAlmThsOpRx_Type(Integer32):
    """Custom type olpStandbyAlmThsOpRx based on Integer32"""
    defaultValue = -2000

    subtypeSpec = Integer32.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        ValueRangeConstraint(-6000, 2500),
    )


_OlpStandbyAlmThsOpRx_Type.__name__ = "Integer32"
_OlpStandbyAlmThsOpRx_Object = MibTableColumn
olpStandbyAlmThsOpRx = _OlpStandbyAlmThsOpRx_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 21, 2, 1, 1, 15),
    _OlpStandbyAlmThsOpRx_Type()
)
olpStandbyAlmThsOpRx.setMaxAccess("read-write")
if mibBuilder.loadTexts:
    olpStandbyAlmThsOpRx.setStatus("current")
if mibBuilder.loadTexts:
    olpStandbyAlmThsOpRx.setDescription("Standby Alarm threshold of optical power")


class _OlpStandbyAlmThsOpTx_Type(Integer32):
    """Custom type olpStandbyAlmThsOpTx based on Integer32"""
    defaultValue = -2500

    subtypeSpec = Integer32.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        ValueRangeConstraint(-6000, 2500),
    )


_OlpStandbyAlmThsOpTx_Type.__name__ = "Integer32"
_OlpStandbyAlmThsOpTx_Object = MibTableColumn
olpStandbyAlmThsOpTx = _OlpStandbyAlmThsOpTx_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 21, 2, 1, 1, 16),
    _OlpStandbyAlmThsOpTx_Type()
)
olpStandbyAlmThsOpTx.setMaxAccess("read-write")
if mibBuilder.loadTexts:
    olpStandbyAlmThsOpTx.setStatus("current")
if mibBuilder.loadTexts:
    olpStandbyAlmThsOpTx.setDescription("Standby Alarm threshold of optical power")


class _OlpActiveSwtThsOpRx_Type(Integer32):
    """Custom type olpActiveSwtThsOpRx based on Integer32"""
    defaultValue = -2500

    subtypeSpec = Integer32.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        ValueRangeConstraint(-6000, 2500),
    )


_OlpActiveSwtThsOpRx_Type.__name__ = "Integer32"
_OlpActiveSwtThsOpRx_Object = MibTableColumn
olpActiveSwtThsOpRx = _OlpActiveSwtThsOpRx_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 21, 2, 1, 1, 17),
    _OlpActiveSwtThsOpRx_Type()
)
olpActiveSwtThsOpRx.setMaxAccess("read-write")
if mibBuilder.loadTexts:
    olpActiveSwtThsOpRx.setStatus("current")
if mibBuilder.loadTexts:
    olpActiveSwtThsOpRx.setDescription("switch threshold of optical power")


class _OlpStandbySwtThsOpRx_Type(Integer32):
    """Custom type olpStandbySwtThsOpRx based on Integer32"""
    defaultValue = -2500

    subtypeSpec = Integer32.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        ValueRangeConstraint(-6000, 2500),
    )


_OlpStandbySwtThsOpRx_Type.__name__ = "Integer32"
_OlpStandbySwtThsOpRx_Object = MibTableColumn
olpStandbySwtThsOpRx = _OlpStandbySwtThsOpRx_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 21, 2, 1, 1, 18),
    _OlpStandbySwtThsOpRx_Type()
)
olpStandbySwtThsOpRx.setMaxAccess("read-write")
if mibBuilder.loadTexts:
    olpStandbySwtThsOpRx.setStatus("current")
if mibBuilder.loadTexts:
    olpStandbySwtThsOpRx.setDescription("standby switch threshold of optical power")


class _OlpAlmThsOpRx_Type(Integer32):
    """Custom type olpAlmThsOpRx based on Integer32"""
    defaultValue = -2500

    subtypeSpec = Integer32.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        ValueRangeConstraint(-6000, 2500),
    )


_OlpAlmThsOpRx_Type.__name__ = "Integer32"
_OlpAlmThsOpRx_Object = MibTableColumn
olpAlmThsOpRx = _OlpAlmThsOpRx_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 21, 2, 1, 1, 19),
    _OlpAlmThsOpRx_Type()
)
olpAlmThsOpRx.setMaxAccess("read-write")
if mibBuilder.loadTexts:
    olpAlmThsOpRx.setStatus("current")
if mibBuilder.loadTexts:
    olpAlmThsOpRx.setDescription("Alarm threshold of optical power")


class _OlpAlmThsOpTx_Type(Integer32):
    """Custom type olpAlmThsOpTx based on Integer32"""
    defaultValue = -2500

    subtypeSpec = Integer32.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        ValueRangeConstraint(-6000, 2500),
    )


_OlpAlmThsOpTx_Type.__name__ = "Integer32"
_OlpAlmThsOpTx_Object = MibTableColumn
olpAlmThsOpTx = _OlpAlmThsOpTx_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 21, 2, 1, 1, 20),
    _OlpAlmThsOpTx_Type()
)
olpAlmThsOpTx.setMaxAccess("read-write")
if mibBuilder.loadTexts:
    olpAlmThsOpTx.setStatus("current")
if mibBuilder.loadTexts:
    olpAlmThsOpTx.setDescription("Alarm threshold of optical power")


class _OlpBypassMode_Type(Integer32):
    """Custom type olpBypassMode based on Integer32"""
    defaultValue = 4

    subtypeSpec = Integer32.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        SingleValueConstraint(
            *(1,
              2,
              3,
              4)
        )
    )
    namedValues = NamedValues(
        *(("r1Andr2", 1),
          ("r1", 2),
          ("r2", 3),
          ("r1Orr2", 4))
    )


_OlpBypassMode_Type.__name__ = "Integer32"
_OlpBypassMode_Object = MibTableColumn
olpBypassMode = _OlpBypassMode_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 21, 2, 1, 1, 21),
    _OlpBypassMode_Type()
)
olpBypassMode.setMaxAccess("read-write")
if mibBuilder.loadTexts:
    olpBypassMode.setStatus("current")
if mibBuilder.loadTexts:
    olpBypassMode.setDescription("Description.")


class _OlpBypassBackMode_Type(Integer32):
    """Custom type olpBypassBackMode based on Integer32"""
    defaultValue = 1

    subtypeSpec = Integer32.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        SingleValueConstraint(
            *(1,
              2,
              3,
              4)
        )
    )
    namedValues = NamedValues(
        *(("r1Andr2", 1),
          ("r1", 2),
          ("r2", 3),
          ("r1Orr2", 4))
    )


_OlpBypassBackMode_Type.__name__ = "Integer32"
_OlpBypassBackMode_Object = MibTableColumn
olpBypassBackMode = _OlpBypassBackMode_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 21, 2, 1, 1, 22),
    _OlpBypassBackMode_Type()
)
olpBypassBackMode.setMaxAccess("read-write")
if mibBuilder.loadTexts:
    olpBypassBackMode.setStatus("current")
if mibBuilder.loadTexts:
    olpBypassBackMode.setDescription("Description.")


class _OlpHeartInputMode_Type(Integer32):
    """Custom type olpHeartInputMode based on Integer32"""
    defaultValue = 2

    subtypeSpec = Integer32.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        SingleValueConstraint(
            *(1,
              2)
        )
    )
    namedValues = NamedValues(
        *(("serial", 1),
          ("ethernet", 2))
    )


_OlpHeartInputMode_Type.__name__ = "Integer32"
_OlpHeartInputMode_Object = MibTableColumn
olpHeartInputMode = _OlpHeartInputMode_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 21, 2, 1, 1, 23),
    _OlpHeartInputMode_Type()
)
olpHeartInputMode.setMaxAccess("read-write")
if mibBuilder.loadTexts:
    olpHeartInputMode.setStatus("current")
if mibBuilder.loadTexts:
    olpHeartInputMode.setDescription("Description.")


class _OlpHeartState_Type(Integer32):
    """Custom type olpHeartState based on Integer32"""
    defaultValue = 1

    subtypeSpec = Integer32.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        SingleValueConstraint(
            *(1,
              2,
              3)
        )
    )
    namedValues = NamedValues(
        *(("normal", 1),
          ("notNormal", 2),
          ("untapped", 3))
    )


_OlpHeartState_Type.__name__ = "Integer32"
_OlpHeartState_Object = MibTableColumn
olpHeartState = _OlpHeartState_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 21, 2, 1, 1, 24),
    _OlpHeartState_Type()
)
olpHeartState.setMaxAccess("read-only")
if mibBuilder.loadTexts:
    olpHeartState.setStatus("current")
if mibBuilder.loadTexts:
    olpHeartState.setDescription("Description.")


class _OlpHeartAlive_Type(Integer32):
    """Custom type olpHeartAlive based on Integer32"""
    defaultValue = 0

    subtypeSpec = Integer32.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        SingleValueConstraint(
            *(0,
              1)
        )
    )
    namedValues = NamedValues(
        *(("disable", 0),
          ("enable", 1))
    )


_OlpHeartAlive_Type.__name__ = "Integer32"
_OlpHeartAlive_Object = MibTableColumn
olpHeartAlive = _OlpHeartAlive_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 21, 2, 1, 1, 25),
    _OlpHeartAlive_Type()
)
olpHeartAlive.setMaxAccess("read-write")
if mibBuilder.loadTexts:
    olpHeartAlive.setStatus("current")
if mibBuilder.loadTexts:
    olpHeartAlive.setDescription("Description.")


class _OlpHeartIdle_Type(Integer32):
    """Custom type olpHeartIdle based on Integer32"""
    defaultValue = 1

    subtypeSpec = Integer32.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        ValueRangeConstraint(1, 255),
    )


_OlpHeartIdle_Type.__name__ = "Integer32"
_OlpHeartIdle_Object = MibTableColumn
olpHeartIdle = _OlpHeartIdle_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 21, 2, 1, 1, 26),
    _OlpHeartIdle_Type()
)
olpHeartIdle.setMaxAccess("read-write")
if mibBuilder.loadTexts:
    olpHeartIdle.setStatus("current")
if mibBuilder.loadTexts:
    olpHeartIdle.setDescription("unit:s")


class _OlpHeartInterval_Type(Integer32):
    """Custom type olpHeartInterval based on Integer32"""
    defaultValue = 1

    subtypeSpec = Integer32.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        ValueRangeConstraint(1, 255),
    )


_OlpHeartInterval_Type.__name__ = "Integer32"
_OlpHeartInterval_Object = MibTableColumn
olpHeartInterval = _OlpHeartInterval_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 21, 2, 1, 1, 27),
    _OlpHeartInterval_Type()
)
olpHeartInterval.setMaxAccess("read-write")
if mibBuilder.loadTexts:
    olpHeartInterval.setStatus("current")
if mibBuilder.loadTexts:
    olpHeartInterval.setDescription("unit:20ms")


class _OlpHeartCount_Type(Integer32):
    """Custom type olpHeartCount based on Integer32"""
    defaultValue = 1

    subtypeSpec = Integer32.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        ValueRangeConstraint(1, 255),
    )


_OlpHeartCount_Type.__name__ = "Integer32"
_OlpHeartCount_Object = MibTableColumn
olpHeartCount = _OlpHeartCount_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 21, 2, 1, 1, 28),
    _OlpHeartCount_Type()
)
olpHeartCount.setMaxAccess("read-write")
if mibBuilder.loadTexts:
    olpHeartCount.setStatus("current")
if mibBuilder.loadTexts:
    olpHeartCount.setDescription("Description")


class _OlpAlamTurnEnabled_Type(Integer32):
    """Custom type olpAlamTurnEnabled based on Integer32"""
    defaultValue = 0

    subtypeSpec = Integer32.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        SingleValueConstraint(
            *(0,
              1)
        )
    )
    namedValues = NamedValues(
        *(("disable", 0),
          ("enable", 1))
    )


_OlpAlamTurnEnabled_Type.__name__ = "Integer32"
_OlpAlamTurnEnabled_Object = MibTableColumn
olpAlamTurnEnabled = _OlpAlamTurnEnabled_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 21, 2, 1, 1, 29),
    _OlpAlamTurnEnabled_Type()
)
olpAlamTurnEnabled.setMaxAccess("read-write")
if mibBuilder.loadTexts:
    olpAlamTurnEnabled.setStatus("current")
if mibBuilder.loadTexts:
    olpAlamTurnEnabled.setDescription("Description.")


class _OlpPowerDiffThs_Type(Integer32):
    """Custom type olpPowerDiffThs based on Integer32"""
    defaultValue = 0

    subtypeSpec = Integer32.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        ValueRangeConstraint(0, 2000),
    )


_OlpPowerDiffThs_Type.__name__ = "Integer32"
_OlpPowerDiffThs_Object = MibTableColumn
olpPowerDiffThs = _OlpPowerDiffThs_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 21, 2, 1, 1, 30),
    _OlpPowerDiffThs_Type()
)
olpPowerDiffThs.setMaxAccess("read-write")
if mibBuilder.loadTexts:
    olpPowerDiffThs.setStatus("current")
if mibBuilder.loadTexts:
    olpPowerDiffThs.setDescription("Description")
_OlpPortTable_Object = MibTable
olpPortTable = _OlpPortTable_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 21, 2, 2)
)
if mibBuilder.loadTexts:
    olpPortTable.setStatus("current")
if mibBuilder.loadTexts:
    olpPortTable.setDescription("Description.")
_OlpPortEntry_Object = MibTableRow
olpPortEntry = _OlpPortEntry_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 21, 2, 2, 1)
)
olpPortEntry.setIndexNames(
    (0, "ST-COMMON-MIB", "shelfId"),
    (0, "ST-COMMON-MIB", "slotNo"),
    (0, "ST-COMMON-MIB", "subSlotNo"),
    (0, "ST-COMMON-MIB", "portNo"),
    (0, "ST-COMMON-MIB", "subPortNo"),
)
if mibBuilder.loadTexts:
    olpPortEntry.setStatus("current")
if mibBuilder.loadTexts:
    olpPortEntry.setDescription("Description.")


class _OlpPortType_Type(Integer32):
    """Custom type olpPortType based on Integer32"""
    subtypeSpec = Integer32.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        SingleValueConstraint(
            *(1,
              2,
              3,
              4,
              5,
              6,
              7,
              8,
              9,
              10,
              11,
              12,
              13,
              14,
              15,
              16,
              17,
              18,
              19,
              20,
              21,
              22,
              23,
              24,
              25,
              26,
              27,
              28,
              29,
              30,
              31,
              32,
              33,
              34,
              35,
              36)
        )
    )
    namedValues = NamedValues(
        *(("tx", 1),
          ("rx", 2),
          ("t1", 3),
          ("r1", 4),
          ("t2", 5),
          ("r2", 6),
          ("t3", 7),
          ("r3", 8),
          ("t4", 9),
          ("r4", 10),
          ("rx1", 11),
          ("rx2", 12),
          ("r11", 13),
          ("r12", 14),
          ("r21", 15),
          ("r22", 16),
          ("nm1271", 17),
          ("nm1291", 18),
          ("nm1311", 19),
          ("nm1331", 20),
          ("nm1351", 21),
          ("nm1371", 22),
          ("l1", 23),
          ("l2", 24),
          ("nm1471", 25),
          ("nm1491", 26),
          ("nm1511", 27),
          ("nm1531", 28),
          ("nm1551", 29),
          ("nm1571", 30),
          ("nm1391", 31),
          ("nm1411", 32),
          ("nm1431", 33),
          ("nm1451", 34),
          ("nm1591", 35),
          ("nm1611", 36))
    )


_OlpPortType_Type.__name__ = "Integer32"
_OlpPortType_Object = MibTableColumn
olpPortType = _OlpPortType_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 21, 2, 2, 1, 1),
    _OlpPortType_Type()
)
olpPortType.setMaxAccess("read-only")
if mibBuilder.loadTexts:
    olpPortType.setStatus("current")
if mibBuilder.loadTexts:
    olpPortType.setDescription("Description.")


class _OlpPortOpticalPower_Type(Integer32):
    """Custom type olpPortOpticalPower based on Integer32"""
    defaultValue = -10000

    subtypeSpec = Integer32.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        ValueRangeConstraint(-10000, 2500),
    )


_OlpPortOpticalPower_Type.__name__ = "Integer32"
_OlpPortOpticalPower_Object = MibTableColumn
olpPortOpticalPower = _OlpPortOpticalPower_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 21, 2, 2, 1, 2),
    _OlpPortOpticalPower_Type()
)
olpPortOpticalPower.setMaxAccess("read-only")
if mibBuilder.loadTexts:
    olpPortOpticalPower.setStatus("current")
if mibBuilder.loadTexts:
    olpPortOpticalPower.setDescription("Description.")


class _OlpPortAlmThsOP_Type(Integer32):
    """Custom type olpPortAlmThsOP based on Integer32"""
    defaultValue = -2000

    subtypeSpec = Integer32.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        ValueRangeConstraint(-6000, 2500),
    )


_OlpPortAlmThsOP_Type.__name__ = "Integer32"
_OlpPortAlmThsOP_Object = MibTableColumn
olpPortAlmThsOP = _OlpPortAlmThsOP_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 21, 2, 2, 1, 3),
    _OlpPortAlmThsOP_Type()
)
olpPortAlmThsOP.setMaxAccess("read-write")
if mibBuilder.loadTexts:
    olpPortAlmThsOP.setStatus("current")
if mibBuilder.loadTexts:
    olpPortAlmThsOP.setDescription("Alarm threshold of optical power")


class _OlpPortSwtThsOp_Type(Integer32):
    """Custom type olpPortSwtThsOp based on Integer32"""
    defaultValue = -2500

    subtypeSpec = Integer32.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        ValueRangeConstraint(-6000, 2500),
    )


_OlpPortSwtThsOp_Type.__name__ = "Integer32"
_OlpPortSwtThsOp_Object = MibTableColumn
olpPortSwtThsOp = _OlpPortSwtThsOp_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 21, 2, 2, 1, 4),
    _OlpPortSwtThsOp_Type()
)
olpPortSwtThsOp.setMaxAccess("read-write")
if mibBuilder.loadTexts:
    olpPortSwtThsOp.setStatus("current")
if mibBuilder.loadTexts:
    olpPortSwtThsOp.setDescription("switch threshold of optical power")


class _OlpPortWaveLen_Type(Integer32):
    """Custom type olpPortWaveLen based on Integer32"""
    defaultValue = 2

    subtypeSpec = Integer32.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        SingleValueConstraint(
            *(0,
              1,
              2,
              3,
              4,
              5,
              6,
              7,
              8,
              9,
              10,
              11,
              12,
              13,
              14,
              15,
              16,
              17)
        )
    )
    namedValues = NamedValues(
        *(("nm1310", 0),
          ("nm1490", 1),
          ("nm1550", 2),
          ("nm1270", 3),
          ("nm1290", 4),
          ("nm1330", 5),
          ("nm1350", 6),
          ("nm1370", 7),
          ("nm1390", 8),
          ("nm1410", 9),
          ("nm1430", 10),
          ("nm1450", 11),
          ("nm1470", 12),
          ("nm1510", 13),
          ("nm1530", 14),
          ("nm1570", 15),
          ("nm1590", 16),
          ("nm1610", 17))
    )


_OlpPortWaveLen_Type.__name__ = "Integer32"
_OlpPortWaveLen_Object = MibTableColumn
olpPortWaveLen = _OlpPortWaveLen_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 21, 2, 2, 1, 5),
    _OlpPortWaveLen_Type()
)
olpPortWaveLen.setMaxAccess("read-write")
if mibBuilder.loadTexts:
    olpPortWaveLen.setStatus("current")
if mibBuilder.loadTexts:
    olpPortWaveLen.setDescription("Wavelength.")


class _OlpPortDesc_Type(DisplayString):
    """Custom type olpPortDesc based on DisplayString"""
    defaultValue = OctetString("")

    subtypeSpec = DisplayString.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        ValueSizeConstraint(0, 128),
    )


_OlpPortDesc_Type.__name__ = "DisplayString"
_OlpPortDesc_Object = MibTableColumn
olpPortDesc = _OlpPortDesc_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 21, 2, 2, 1, 6),
    _OlpPortDesc_Type()
)
olpPortDesc.setMaxAccess("read-write")
if mibBuilder.loadTexts:
    olpPortDesc.setStatus("current")
if mibBuilder.loadTexts:
    olpPortDesc.setDescription("Description.")
_OlpCommandTable_Object = MibTable
olpCommandTable = _OlpCommandTable_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 21, 2, 3)
)
if mibBuilder.loadTexts:
    olpCommandTable.setStatus("current")
if mibBuilder.loadTexts:
    olpCommandTable.setDescription("Description.")
_OlpCommandEntry_Object = MibTableRow
olpCommandEntry = _OlpCommandEntry_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 21, 2, 3, 1)
)
olpCommandEntry.setIndexNames(
    (0, "ST-COMMON-MIB", "shelfId"),
    (0, "ST-COMMON-MIB", "slotNo"),
    (0, "ST-COMMON-MIB", "subSlotNo"),
)
if mibBuilder.loadTexts:
    olpCommandEntry.setStatus("current")
if mibBuilder.loadTexts:
    olpCommandEntry.setDescription("Description.")


class _OlpCmdSwitch_Type(Integer32):
    """Custom type olpCmdSwitch based on Integer32"""
    defaultValue = 3

    subtypeSpec = Integer32.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        SingleValueConstraint(
            *(3,
              12)
        )
    )
    namedValues = NamedValues(
        *(("primary", 3),
          ("secondary", 12))
    )


_OlpCmdSwitch_Type.__name__ = "Integer32"
_OlpCmdSwitch_Object = MibTableColumn
olpCmdSwitch = _OlpCmdSwitch_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 21, 2, 3, 1, 1),
    _OlpCmdSwitch_Type()
)
olpCmdSwitch.setMaxAccess("read-write")
if mibBuilder.loadTexts:
    olpCmdSwitch.setStatus("current")
if mibBuilder.loadTexts:
    olpCmdSwitch.setDescription("Command for P2S or S2P")


class _OlpCmdSwitchCounterClear_Type(Integer32):
    """Custom type olpCmdSwitchCounterClear based on Integer32"""
    defaultValue = 1

    subtypeSpec = Integer32.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        SingleValueConstraint(
            1
        )
    )
    namedValues = NamedValues(
        ("clear", 1)
    )


_OlpCmdSwitchCounterClear_Type.__name__ = "Integer32"
_OlpCmdSwitchCounterClear_Object = MibTableColumn
olpCmdSwitchCounterClear = _OlpCmdSwitchCounterClear_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 21, 2, 3, 1, 2),
    _OlpCmdSwitchCounterClear_Type()
)
olpCmdSwitchCounterClear.setMaxAccess("read-write")
if mibBuilder.loadTexts:
    olpCmdSwitchCounterClear.setStatus("current")
if mibBuilder.loadTexts:
    olpCmdSwitchCounterClear.setDescription("Clear switch counter")
_OlpCardTable_Object = MibTable
olpCardTable = _OlpCardTable_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 21, 2, 4)
)
if mibBuilder.loadTexts:
    olpCardTable.setStatus("current")
if mibBuilder.loadTexts:
    olpCardTable.setDescription("Description.")
_OlpCardEntry_Object = MibTableRow
olpCardEntry = _OlpCardEntry_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 21, 2, 4, 1)
)
olpCardEntry.setIndexNames(
    (0, "ST-COMMON-MIB", "shelfId"),
    (0, "ST-COMMON-MIB", "slotNo"),
    (0, "ST-COMMON-MIB", "subSlotNo"),
)
if mibBuilder.loadTexts:
    olpCardEntry.setStatus("current")
if mibBuilder.loadTexts:
    olpCardEntry.setDescription("Description.")
_OlpCardAddr_Type = Integer32
_OlpCardAddr_Object = MibTableColumn
olpCardAddr = _OlpCardAddr_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 21, 2, 4, 1, 1),
    _OlpCardAddr_Type()
)
olpCardAddr.setMaxAccess("read-only")
if mibBuilder.loadTexts:
    olpCardAddr.setStatus("current")
if mibBuilder.loadTexts:
    olpCardAddr.setDescription("card slot id.")


class _OlpCardType_Type(Integer32):
    """Custom type olpCardType based on Integer32"""
    defaultValue = 0


_OlpCardType_Type.__name__ = "Integer32"
_OlpCardType_Object = MibTableColumn
olpCardType = _OlpCardType_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 21, 2, 4, 1, 2),
    _OlpCardType_Type()
)
olpCardType.setMaxAccess("read-only")
if mibBuilder.loadTexts:
    olpCardType.setStatus("current")
if mibBuilder.loadTexts:
    olpCardType.setDescription("Description.")


class _OlpCardSN_Type(DisplayString):
    """Custom type olpCardSN based on DisplayString"""
    defaultValue = OctetString("")

    subtypeSpec = DisplayString.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        ValueSizeConstraint(0, 50),
    )


_OlpCardSN_Type.__name__ = "DisplayString"
_OlpCardSN_Object = MibTableColumn
olpCardSN = _OlpCardSN_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 21, 2, 4, 1, 3),
    _OlpCardSN_Type()
)
olpCardSN.setMaxAccess("read-only")
if mibBuilder.loadTexts:
    olpCardSN.setStatus("current")
if mibBuilder.loadTexts:
    olpCardSN.setDescription("serial number")


class _OlpCardPartN_Type(DisplayString):
    """Custom type olpCardPartN based on DisplayString"""
    defaultValue = OctetString("")

    subtypeSpec = DisplayString.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        ValueSizeConstraint(0, 50),
    )


_OlpCardPartN_Type.__name__ = "DisplayString"
_OlpCardPartN_Object = MibTableColumn
olpCardPartN = _OlpCardPartN_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 21, 2, 4, 1, 4),
    _OlpCardPartN_Type()
)
olpCardPartN.setMaxAccess("read-only")
if mibBuilder.loadTexts:
    olpCardPartN.setStatus("current")
if mibBuilder.loadTexts:
    olpCardPartN.setDescription("partnumber")


class _OlpCardLabel_Type(DisplayString):
    """Custom type olpCardLabel based on DisplayString"""
    defaultValue = OctetString("")

    subtypeSpec = DisplayString.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        ValueSizeConstraint(0, 128),
    )


_OlpCardLabel_Type.__name__ = "DisplayString"
_OlpCardLabel_Object = MibTableColumn
olpCardLabel = _OlpCardLabel_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 21, 2, 4, 1, 5),
    _OlpCardLabel_Type()
)
olpCardLabel.setMaxAccess("read-write")
if mibBuilder.loadTexts:
    olpCardLabel.setStatus("current")
if mibBuilder.loadTexts:
    olpCardLabel.setDescription("card description")


class _OlpCardHwVersion_Type(DisplayString):
    """Custom type olpCardHwVersion based on DisplayString"""
    defaultValue = OctetString("")

    subtypeSpec = DisplayString.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        ValueSizeConstraint(0, 20),
    )


_OlpCardHwVersion_Type.__name__ = "DisplayString"
_OlpCardHwVersion_Object = MibTableColumn
olpCardHwVersion = _OlpCardHwVersion_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 21, 2, 4, 1, 6),
    _OlpCardHwVersion_Type()
)
olpCardHwVersion.setMaxAccess("read-write")
if mibBuilder.loadTexts:
    olpCardHwVersion.setStatus("current")
if mibBuilder.loadTexts:
    olpCardHwVersion.setDescription("hardware version")


class _OlpCardSwVersion_Type(DisplayString):
    """Custom type olpCardSwVersion based on DisplayString"""
    defaultValue = OctetString("")

    subtypeSpec = DisplayString.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        ValueSizeConstraint(0, 30),
    )


_OlpCardSwVersion_Type.__name__ = "DisplayString"
_OlpCardSwVersion_Object = MibTableColumn
olpCardSwVersion = _OlpCardSwVersion_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 21, 2, 4, 1, 7),
    _OlpCardSwVersion_Type()
)
olpCardSwVersion.setMaxAccess("read-only")
if mibBuilder.loadTexts:
    olpCardSwVersion.setStatus("current")
if mibBuilder.loadTexts:
    olpCardSwVersion.setDescription("SW Version")


class _OlpCardCpldVersion_Type(DisplayString):
    """Custom type olpCardCpldVersion based on DisplayString"""
    defaultValue = OctetString("")

    subtypeSpec = DisplayString.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        ValueSizeConstraint(0, 20),
    )


_OlpCardCpldVersion_Type.__name__ = "DisplayString"
_OlpCardCpldVersion_Object = MibTableColumn
olpCardCpldVersion = _OlpCardCpldVersion_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 21, 2, 4, 1, 8),
    _OlpCardCpldVersion_Type()
)
olpCardCpldVersion.setMaxAccess("read-only")
if mibBuilder.loadTexts:
    olpCardCpldVersion.setStatus("current")
if mibBuilder.loadTexts:
    olpCardCpldVersion.setDescription("Description.")
_OlpCardTemperature_Type = Integer32
_OlpCardTemperature_Object = MibTableColumn
olpCardTemperature = _OlpCardTemperature_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 21, 2, 4, 1, 9),
    _OlpCardTemperature_Type()
)
olpCardTemperature.setMaxAccess("read-only")
if mibBuilder.loadTexts:
    olpCardTemperature.setStatus("current")
if mibBuilder.loadTexts:
    olpCardTemperature.setDescription("Temperature near CPU.")
_OlpCardRowStatus_Type = RowStatus
_OlpCardRowStatus_Object = MibTableColumn
olpCardRowStatus = _OlpCardRowStatus_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 21, 2, 4, 1, 10),
    _OlpCardRowStatus_Type()
)
olpCardRowStatus.setMaxAccess("read-only")
if mibBuilder.loadTexts:
    olpCardRowStatus.setStatus("current")
if mibBuilder.loadTexts:
    olpCardRowStatus.setDescription("Description.")
_OaConfigMIB_ObjectIdentity = ObjectIdentity
oaConfigMIB = _OaConfigMIB_ObjectIdentity(
    (1, 3, 6, 1, 4, 1, 52642, 1, 21, 3)
)
_OaModuleTable_Object = MibTable
oaModuleTable = _OaModuleTable_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 21, 3, 4)
)
if mibBuilder.loadTexts:
    oaModuleTable.setStatus("current")
if mibBuilder.loadTexts:
    oaModuleTable.setDescription("ETYn Table")
_OaModuleEntry_Object = MibTableRow
oaModuleEntry = _OaModuleEntry_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 21, 3, 4, 1)
)
oaModuleEntry.setIndexNames(
    (0, "ST-COMMON-MIB", "shelfId"),
    (0, "ST-COMMON-MIB", "slotNo"),
    (0, "ST-COMMON-MIB", "subSlotNo"),
)
if mibBuilder.loadTexts:
    oaModuleEntry.setStatus("current")
if mibBuilder.loadTexts:
    oaModuleEntry.setDescription(" ")


class _OaModuleConfiguration_Type(DisplayString):
    """Custom type oaModuleConfiguration based on DisplayString"""
    subtypeSpec = DisplayString.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        ValueSizeConstraint(0, 64),
    )


_OaModuleConfiguration_Type.__name__ = "DisplayString"
_OaModuleConfiguration_Object = MibTableColumn
oaModuleConfiguration = _OaModuleConfiguration_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 21, 3, 4, 1, 1),
    _OaModuleConfiguration_Type()
)
oaModuleConfiguration.setMaxAccess("read-only")
if mibBuilder.loadTexts:
    oaModuleConfiguration.setStatus("current")
if mibBuilder.loadTexts:
    oaModuleConfiguration.setDescription(" ")


class _OaModuleFirmwareVers_Type(DisplayString):
    """Custom type oaModuleFirmwareVers based on DisplayString"""
    subtypeSpec = DisplayString.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        ValueSizeConstraint(0, 64),
    )


_OaModuleFirmwareVers_Type.__name__ = "DisplayString"
_OaModuleFirmwareVers_Object = MibTableColumn
oaModuleFirmwareVers = _OaModuleFirmwareVers_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 21, 3, 4, 1, 2),
    _OaModuleFirmwareVers_Type()
)
oaModuleFirmwareVers.setMaxAccess("read-only")
if mibBuilder.loadTexts:
    oaModuleFirmwareVers.setStatus("current")
if mibBuilder.loadTexts:
    oaModuleFirmwareVers.setDescription(" ")


class _OaModuleSerialNumber_Type(DisplayString):
    """Custom type oaModuleSerialNumber based on DisplayString"""
    subtypeSpec = DisplayString.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        ValueSizeConstraint(0, 64),
    )


_OaModuleSerialNumber_Type.__name__ = "DisplayString"
_OaModuleSerialNumber_Object = MibTableColumn
oaModuleSerialNumber = _OaModuleSerialNumber_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 21, 3, 4, 1, 3),
    _OaModuleSerialNumber_Type()
)
oaModuleSerialNumber.setMaxAccess("read-only")
if mibBuilder.loadTexts:
    oaModuleSerialNumber.setStatus("current")
if mibBuilder.loadTexts:
    oaModuleSerialNumber.setDescription(" ")


class _OaModuleType_Type(Integer32):
    """Custom type oaModuleType based on Integer32"""
    defaultValue = 1

    subtypeSpec = Integer32.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        SingleValueConstraint(
            *(1,
              2,
              3,
              4,
              5,
              6)
        )
    )
    namedValues = NamedValues(
        *(("singleStage", 1),
          ("multiStage", 2),
          ("twoStageIndependent", 3),
          ("singleC34", 4),
          ("multiCW", 5),
          ("rAAMPLIFICATIOM", 6))
    )


_OaModuleType_Type.__name__ = "Integer32"
_OaModuleType_Object = MibTableColumn
oaModuleType = _OaModuleType_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 21, 3, 4, 1, 4),
    _OaModuleType_Type()
)
oaModuleType.setMaxAccess("read-only")
if mibBuilder.loadTexts:
    oaModuleType.setStatus("current")
if mibBuilder.loadTexts:
    oaModuleType.setDescription("singleStage (1),multiStage (2),twoStageIndependent(3),singleC34 (4),multiCW (5),RAAMPLIFICATIOM (6). ")


class _OaModuleFunction_Type(Integer32):
    """Custom type oaModuleFunction based on Integer32"""
    defaultValue = 0

    subtypeSpec = Integer32.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        SingleValueConstraint(
            *(0,
              1,
              2,
              3)
        )
    )
    namedValues = NamedValues(
        *(("pa", 0),
          ("la", 1),
          ("ba", 2),
          ("da", 3))
    )


_OaModuleFunction_Type.__name__ = "Integer32"
_OaModuleFunction_Object = MibTableColumn
oaModuleFunction = _OaModuleFunction_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 21, 3, 4, 1, 5),
    _OaModuleFunction_Type()
)
oaModuleFunction.setMaxAccess("read-only")
if mibBuilder.loadTexts:
    oaModuleFunction.setStatus("current")
if mibBuilder.loadTexts:
    oaModuleFunction.setDescription("PA��0�� LA��1�� BA��2�� DA��3��")


class _OaModuleMaxOutPower_Type(Integer32):
    """Custom type oaModuleMaxOutPower based on Integer32"""
    defaultValue = 160

    subtypeSpec = Integer32.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        ValueRangeConstraint(-1000, 3000),
    )


_OaModuleMaxOutPower_Type.__name__ = "Integer32"
_OaModuleMaxOutPower_Object = MibTableColumn
oaModuleMaxOutPower = _OaModuleMaxOutPower_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 21, 3, 4, 1, 7),
    _OaModuleMaxOutPower_Type()
)
oaModuleMaxOutPower.setMaxAccess("read-only")
if mibBuilder.loadTexts:
    oaModuleMaxOutPower.setStatus("current")
if mibBuilder.loadTexts:
    oaModuleMaxOutPower.setDescription("Get the max output power in dbm.")


class _OaModuleDefaultGain_Type(Integer32):
    """Custom type oaModuleDefaultGain based on Integer32"""
    defaultValue = 250

    subtypeSpec = Integer32.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        ValueRangeConstraint(0, 300),
    )


_OaModuleDefaultGain_Type.__name__ = "Integer32"
_OaModuleDefaultGain_Object = MibTableColumn
oaModuleDefaultGain = _OaModuleDefaultGain_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 21, 3, 4, 1, 8),
    _OaModuleDefaultGain_Type()
)
oaModuleDefaultGain.setMaxAccess("read-only")
if mibBuilder.loadTexts:
    oaModuleDefaultGain.setStatus("current")
if mibBuilder.loadTexts:
    oaModuleDefaultGain.setDescription("Get the default gain in db")


class _OaModuleVariableGain_Type(Integer32):
    """Custom type oaModuleVariableGain based on Integer32"""
    defaultValue = 1

    subtypeSpec = Integer32.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        SingleValueConstraint(
            *(1,
              2)
        )
    )
    namedValues = NamedValues(
        *(("variable", 1),
          ("fixed", 2))
    )


_OaModuleVariableGain_Type.__name__ = "Integer32"
_OaModuleVariableGain_Object = MibTableColumn
oaModuleVariableGain = _OaModuleVariableGain_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 21, 3, 4, 1, 9),
    _OaModuleVariableGain_Type()
)
oaModuleVariableGain.setMaxAccess("read-only")
if mibBuilder.loadTexts:
    oaModuleVariableGain.setStatus("current")
if mibBuilder.loadTexts:
    oaModuleVariableGain.setDescription("Get the default gain in db")


class _OaModuleMinGain_Type(Integer32):
    """Custom type oaModuleMinGain based on Integer32"""
    defaultValue = 0

    subtypeSpec = Integer32.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        ValueRangeConstraint(0, 4000),
    )


_OaModuleMinGain_Type.__name__ = "Integer32"
_OaModuleMinGain_Object = MibTableColumn
oaModuleMinGain = _OaModuleMinGain_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 21, 3, 4, 1, 10),
    _OaModuleMinGain_Type()
)
oaModuleMinGain.setMaxAccess("read-only")
if mibBuilder.loadTexts:
    oaModuleMinGain.setStatus("current")
if mibBuilder.loadTexts:
    oaModuleMinGain.setDescription("Get the max output power in dbm.")


class _OaModuleMaxGain_Type(Integer32):
    """Custom type oaModuleMaxGain based on Integer32"""
    defaultValue = 350

    subtypeSpec = Integer32.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        ValueRangeConstraint(0, 4000),
    )


_OaModuleMaxGain_Type.__name__ = "Integer32"
_OaModuleMaxGain_Object = MibTableColumn
oaModuleMaxGain = _OaModuleMaxGain_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 21, 3, 4, 1, 11),
    _OaModuleMaxGain_Type()
)
oaModuleMaxGain.setMaxAccess("read-only")
if mibBuilder.loadTexts:
    oaModuleMaxGain.setStatus("current")
if mibBuilder.loadTexts:
    oaModuleMaxGain.setDescription("Get the max output power in dbm.")


class _OaModuleMinPower_Type(Integer32):
    """Custom type oaModuleMinPower based on Integer32"""
    defaultValue = 0

    subtypeSpec = Integer32.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        ValueRangeConstraint(0, 4000),
    )


_OaModuleMinPower_Type.__name__ = "Integer32"
_OaModuleMinPower_Object = MibTableColumn
oaModuleMinPower = _OaModuleMinPower_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 21, 3, 4, 1, 12),
    _OaModuleMinPower_Type()
)
oaModuleMinPower.setMaxAccess("read-only")
if mibBuilder.loadTexts:
    oaModuleMinPower.setStatus("current")
if mibBuilder.loadTexts:
    oaModuleMinPower.setDescription("Get the max output power in dbm.")


class _OaModuleMaxPower_Type(Integer32):
    """Custom type oaModuleMaxPower based on Integer32"""
    defaultValue = 300

    subtypeSpec = Integer32.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        ValueRangeConstraint(0, 4000),
    )


_OaModuleMaxPower_Type.__name__ = "Integer32"
_OaModuleMaxPower_Object = MibTableColumn
oaModuleMaxPower = _OaModuleMaxPower_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 21, 3, 4, 1, 13),
    _OaModuleMaxPower_Type()
)
oaModuleMaxPower.setMaxAccess("read-only")
if mibBuilder.loadTexts:
    oaModuleMaxPower.setStatus("current")
if mibBuilder.loadTexts:
    oaModuleMaxPower.setDescription("Get the max output power in dbm.")


class _OaModuleDcmSupport_Type(Integer32):
    """Custom type oaModuleDcmSupport based on Integer32"""
    defaultValue = 1

    subtypeSpec = Integer32.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        SingleValueConstraint(
            *(1,
              2)
        )
    )
    namedValues = NamedValues(
        *(("support", 1),
          ("nonsupport", 2))
    )


_OaModuleDcmSupport_Type.__name__ = "Integer32"
_OaModuleDcmSupport_Object = MibTableColumn
oaModuleDcmSupport = _OaModuleDcmSupport_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 21, 3, 4, 1, 14),
    _OaModuleDcmSupport_Type()
)
oaModuleDcmSupport.setMaxAccess("read-only")
if mibBuilder.loadTexts:
    oaModuleDcmSupport.setStatus("current")
if mibBuilder.loadTexts:
    oaModuleDcmSupport.setDescription("Get the max output power in dbm.")


class _OaModuleDcmValue_Type(Integer32):
    """Custom type oaModuleDcmValue based on Integer32"""
    subtypeSpec = Integer32.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        ValueRangeConstraint(0, 300),
    )


_OaModuleDcmValue_Type.__name__ = "Integer32"
_OaModuleDcmValue_Object = MibTableColumn
oaModuleDcmValue = _OaModuleDcmValue_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 21, 3, 4, 1, 15),
    _OaModuleDcmValue_Type()
)
oaModuleDcmValue.setMaxAccess("read-only")
if mibBuilder.loadTexts:
    oaModuleDcmValue.setStatus("current")
if mibBuilder.loadTexts:
    oaModuleDcmValue.setUnits("0")
if mibBuilder.loadTexts:
    oaModuleDcmValue.setDescription("Get the max output power in dbm.")


class _OaModuleOscSupport_Type(Integer32):
    """Custom type oaModuleOscSupport based on Integer32"""
    defaultValue = 1

    subtypeSpec = Integer32.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        SingleValueConstraint(
            *(1,
              2)
        )
    )
    namedValues = NamedValues(
        *(("support", 1),
          ("nonsupport", 2))
    )


_OaModuleOscSupport_Type.__name__ = "Integer32"
_OaModuleOscSupport_Object = MibTableColumn
oaModuleOscSupport = _OaModuleOscSupport_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 21, 3, 4, 1, 16),
    _OaModuleOscSupport_Type()
)
oaModuleOscSupport.setMaxAccess("read-only")
if mibBuilder.loadTexts:
    oaModuleOscSupport.setStatus("current")
if mibBuilder.loadTexts:
    oaModuleOscSupport.setDescription("Get the max output power in dbm.")


class _OaModuleOscWave_Type(Integer32):
    """Custom type oaModuleOscWave based on Integer32"""
    defaultValue = 5

    subtypeSpec = Integer32.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        SingleValueConstraint(
            *(4,
              5,
              6)
        )
    )
    namedValues = NamedValues(
        *(("nm1490", 4),
          ("nm1510", 5),
          ("nm1625", 6))
    )


_OaModuleOscWave_Type.__name__ = "Integer32"
_OaModuleOscWave_Object = MibTableColumn
oaModuleOscWave = _OaModuleOscWave_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 21, 3, 4, 1, 17),
    _OaModuleOscWave_Type()
)
oaModuleOscWave.setMaxAccess("read-only")
if mibBuilder.loadTexts:
    oaModuleOscWave.setStatus("current")
if mibBuilder.loadTexts:
    oaModuleOscWave.setDescription("Get the max output power in dbm.")


class _OaModulePumpNum_Type(Integer32):
    """Custom type oaModulePumpNum based on Integer32"""
    defaultValue = 1

    subtypeSpec = Integer32.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        SingleValueConstraint(
            *(1,
              2,
              4,
              6)
        )
    )
    namedValues = NamedValues(
        *(("one", 1),
          ("two", 2),
          ("four", 4),
          ("six", 6))
    )


_OaModulePumpNum_Type.__name__ = "Integer32"
_OaModulePumpNum_Object = MibTableColumn
oaModulePumpNum = _OaModulePumpNum_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 21, 3, 4, 1, 18),
    _OaModulePumpNum_Type()
)
oaModulePumpNum.setMaxAccess("read-only")
if mibBuilder.loadTexts:
    oaModulePumpNum.setStatus("current")
if mibBuilder.loadTexts:
    oaModulePumpNum.setDescription("show the num of pump.")


class _OaModuleWaveNum_Type(Integer32):
    """Custom type oaModuleWaveNum based on Integer32"""
    defaultValue = 0

    subtypeSpec = Integer32.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        SingleValueConstraint(
            *(0,
              1,
              2,
              3,
              4)
        )
    )
    namedValues = NamedValues(
        *(("zero", 0),
          ("one", 1),
          ("two", 2),
          ("three", 3),
          ("four", 4))
    )


_OaModuleWaveNum_Type.__name__ = "Integer32"
_OaModuleWaveNum_Object = MibTableColumn
oaModuleWaveNum = _OaModuleWaveNum_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 21, 3, 4, 1, 19),
    _OaModuleWaveNum_Type()
)
oaModuleWaveNum.setMaxAccess("read-only")
if mibBuilder.loadTexts:
    oaModuleWaveNum.setStatus("current")
if mibBuilder.loadTexts:
    oaModuleWaveNum.setDescription("show the num of wave for RA")


class _OaModulePumpTotalPower_Type(Integer32):
    """Custom type oaModulePumpTotalPower based on Integer32"""
    defaultValue = 0

    subtypeSpec = Integer32.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        ValueRangeConstraint(0, 100000),
    )


_OaModulePumpTotalPower_Type.__name__ = "Integer32"
_OaModulePumpTotalPower_Object = MibTableColumn
oaModulePumpTotalPower = _OaModulePumpTotalPower_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 21, 3, 4, 1, 20),
    _OaModulePumpTotalPower_Type()
)
oaModulePumpTotalPower.setMaxAccess("read-only")
if mibBuilder.loadTexts:
    oaModulePumpTotalPower.setStatus("current")
if mibBuilder.loadTexts:
    oaModulePumpTotalPower.setDescription("Get the Pump Total Power in mv.")


class _OaModuleTILT_Type(Integer32):
    """Custom type oaModuleTILT based on Integer32"""
    defaultValue = 0

    subtypeSpec = Integer32.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        ValueRangeConstraint(-300, 300),
    )


_OaModuleTILT_Type.__name__ = "Integer32"
_OaModuleTILT_Object = MibTableColumn
oaModuleTILT = _OaModuleTILT_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 21, 3, 4, 1, 21),
    _OaModuleTILT_Type()
)
oaModuleTILT.setMaxAccess("read-write")
if mibBuilder.loadTexts:
    oaModuleTILT.setStatus("current")
if mibBuilder.loadTexts:
    oaModuleTILT.setDescription("Get the Pump tilt in dB.")
_OaPumpTable_Object = MibTable
oaPumpTable = _OaPumpTable_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 21, 3, 5)
)
if mibBuilder.loadTexts:
    oaPumpTable.setStatus("current")
if mibBuilder.loadTexts:
    oaPumpTable.setDescription("Description.")
_OaPumpEntry_Object = MibTableRow
oaPumpEntry = _OaPumpEntry_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 21, 3, 5, 1)
)
oaPumpEntry.setIndexNames(
    (0, "ST-COMMON-MIB", "shelfId"),
    (0, "ST-COMMON-MIB", "slotNo"),
    (0, "ST-COMMON-MIB", "subSlotNo"),
)
if mibBuilder.loadTexts:
    oaPumpEntry.setStatus("current")
if mibBuilder.loadTexts:
    oaPumpEntry.setDescription("Description.")


class _OaPumpControlMode_Type(Integer32):
    """Custom type oaPumpControlMode based on Integer32"""
    defaultValue = 1

    subtypeSpec = Integer32.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        SingleValueConstraint(
            *(1,
              2,
              3,
              4,
              5,
              6,
              7)
        )
    )
    namedValues = NamedValues(
        *(("g", 1),
          ("p", 2),
          ("sp", 3),
          ("s", 4),
          ("m", 5),
          ("d", 6),
          ("w", 7))
    )


_OaPumpControlMode_Type.__name__ = "Integer32"
_OaPumpControlMode_Object = MibTableColumn
oaPumpControlMode = _OaPumpControlMode_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 21, 3, 5, 1, 1),
    _OaPumpControlMode_Type()
)
oaPumpControlMode.setMaxAccess("read-write")
if mibBuilder.loadTexts:
    oaPumpControlMode.setStatus("current")
if mibBuilder.loadTexts:
    oaPumpControlMode.setDescription("G,Gain control mode P,hardware Output power control mode SP,software Output power control mode S,Stage control mode M,Manual pump control D,Disable mode W,Pump power control mode")


class _OaPumpModeValue_Type(Integer32):
    """Custom type oaPumpModeValue based on Integer32"""
    defaultValue = 250

    subtypeSpec = Integer32.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        ValueRangeConstraint(-400, 10000),
    )


_OaPumpModeValue_Type.__name__ = "Integer32"
_OaPumpModeValue_Object = MibTableColumn
oaPumpModeValue = _OaPumpModeValue_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 21, 3, 5, 1, 2),
    _OaPumpModeValue_Type()
)
oaPumpModeValue.setMaxAccess("read-write")
if mibBuilder.loadTexts:
    oaPumpModeValue.setStatus("current")
if mibBuilder.loadTexts:
    oaPumpModeValue.setDescription("Show the gain setpoint in dB,or output power in dbm.")


class _OaPumpNum_Type(Integer32):
    """Custom type oaPumpNum based on Integer32"""
    defaultValue = 1

    subtypeSpec = Integer32.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        SingleValueConstraint(
            *(1,
              2,
              4,
              6)
        )
    )
    namedValues = NamedValues(
        *(("one", 1),
          ("two", 2),
          ("four", 4),
          ("six", 6))
    )


_OaPumpNum_Type.__name__ = "Integer32"
_OaPumpNum_Object = MibTableColumn
oaPumpNum = _OaPumpNum_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 21, 3, 5, 1, 3),
    _OaPumpNum_Type()
)
oaPumpNum.setMaxAccess("read-only")
if mibBuilder.loadTexts:
    oaPumpNum.setStatus("current")
if mibBuilder.loadTexts:
    oaPumpNum.setDescription("show the num of pump.")
_OaPumpTemperature_Type = Integer32
_OaPumpTemperature_Object = MibTableColumn
oaPumpTemperature = _OaPumpTemperature_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 21, 3, 5, 1, 4),
    _OaPumpTemperature_Type()
)
oaPumpTemperature.setMaxAccess("read-only")
if mibBuilder.loadTexts:
    oaPumpTemperature.setStatus("current")
if mibBuilder.loadTexts:
    oaPumpTemperature.setDescription("Displays module case temperature in degrees C.")


class _OaPumpIld_Type(Integer32):
    """Custom type oaPumpIld based on Integer32"""
    defaultValue = 0


_OaPumpIld_Type.__name__ = "Integer32"
_OaPumpIld_Object = MibTableColumn
oaPumpIld = _OaPumpIld_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 21, 3, 5, 1, 5),
    _OaPumpIld_Type()
)
oaPumpIld.setMaxAccess("read-only")
if mibBuilder.loadTexts:
    oaPumpIld.setStatus("current")
if mibBuilder.loadTexts:
    oaPumpIld.setDescription("Laser diode current in mA.")


class _OaPumpEol_Type(Integer32):
    """Custom type oaPumpEol based on Integer32"""
    defaultValue = 0


_OaPumpEol_Type.__name__ = "Integer32"
_OaPumpEol_Object = MibTableColumn
oaPumpEol = _OaPumpEol_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 21, 3, 5, 1, 6),
    _OaPumpEol_Type()
)
oaPumpEol.setMaxAccess("read-only")
if mibBuilder.loadTexts:
    oaPumpEol.setStatus("current")
if mibBuilder.loadTexts:
    oaPumpEol.setDescription("Laser diode end-of-life current in mA.")


class _OaPumpTmp_Type(Integer32):
    """Custom type oaPumpTmp based on Integer32"""
    defaultValue = 0


_OaPumpTmp_Type.__name__ = "Integer32"
_OaPumpTmp_Object = MibTableColumn
oaPumpTmp = _OaPumpTmp_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 21, 3, 5, 1, 7),
    _OaPumpTmp_Type()
)
oaPumpTmp.setMaxAccess("read-only")
if mibBuilder.loadTexts:
    oaPumpTmp.setStatus("current")
if mibBuilder.loadTexts:
    oaPumpTmp.setDescription("Pump temperature in degrees C.")


class _OaPumpItc_Type(Integer32):
    """Custom type oaPumpItc based on Integer32"""
    defaultValue = 0


_OaPumpItc_Type.__name__ = "Integer32"
_OaPumpItc_Object = MibTableColumn
oaPumpItc = _OaPumpItc_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 21, 3, 5, 1, 8),
    _OaPumpItc_Type()
)
oaPumpItc.setMaxAccess("read-only")
if mibBuilder.loadTexts:
    oaPumpItc.setStatus("current")
if mibBuilder.loadTexts:
    oaPumpItc.setDescription("TEC current in mA.")


class _OaPumpVtc_Type(Integer32):
    """Custom type oaPumpVtc based on Integer32"""
    defaultValue = 0


_OaPumpVtc_Type.__name__ = "Integer32"
_OaPumpVtc_Object = MibTableColumn
oaPumpVtc = _OaPumpVtc_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 21, 3, 5, 1, 9),
    _OaPumpVtc_Type()
)
oaPumpVtc.setMaxAccess("read-only")
if mibBuilder.loadTexts:
    oaPumpVtc.setStatus("current")
if mibBuilder.loadTexts:
    oaPumpVtc.setDescription("Voltage of Pump")


class _OaPumpIsp_Type(Integer32):
    """Custom type oaPumpIsp based on Integer32"""
    defaultValue = 0

    subtypeSpec = Integer32.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        SingleValueConstraint(
            *(0,
              1)
        )
    )
    namedValues = NamedValues(
        *(("auto", 0),
          ("manual", 1))
    )


_OaPumpIsp_Type.__name__ = "Integer32"
_OaPumpIsp_Object = MibTableColumn
oaPumpIsp = _OaPumpIsp_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 21, 3, 5, 1, 10),
    _OaPumpIsp_Type()
)
oaPumpIsp.setMaxAccess("read-write")
if mibBuilder.loadTexts:
    oaPumpIsp.setStatus("current")
if mibBuilder.loadTexts:
    oaPumpIsp.setDescription("Description.")


class _OaPumpIspValue_Type(Integer32):
    """Custom type oaPumpIspValue based on Integer32"""
    defaultValue = 0

    subtypeSpec = Integer32.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        ValueRangeConstraint(-600, 600),
    )


_OaPumpIspValue_Type.__name__ = "Integer32"
_OaPumpIspValue_Object = MibTableColumn
oaPumpIspValue = _OaPumpIspValue_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 21, 3, 5, 1, 11),
    _OaPumpIspValue_Type()
)
oaPumpIspValue.setMaxAccess("read-write")
if mibBuilder.loadTexts:
    oaPumpIspValue.setStatus("current")
if mibBuilder.loadTexts:
    oaPumpIspValue.setDescription("PUMP current setpoint in mA.")


class _OaPumpAlarmThrILD_Type(Integer32):
    """Custom type oaPumpAlarmThrILD based on Integer32"""
    defaultValue = 95

    subtypeSpec = Integer32.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        ValueRangeConstraint(0, 100),
    )


_OaPumpAlarmThrILD_Type.__name__ = "Integer32"
_OaPumpAlarmThrILD_Object = MibTableColumn
oaPumpAlarmThrILD = _OaPumpAlarmThrILD_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 21, 3, 5, 1, 12),
    _OaPumpAlarmThrILD_Type()
)
oaPumpAlarmThrILD.setMaxAccess("read-write")
if mibBuilder.loadTexts:
    oaPumpAlarmThrILD.setStatus("current")
if mibBuilder.loadTexts:
    oaPumpAlarmThrILD.setDescription("Pump overcurrent alarm threshold.")


class _OaPumpAlarmThrTMP_Type(Integer32):
    """Custom type oaPumpAlarmThrTMP based on Integer32"""
    defaultValue = 50

    subtypeSpec = Integer32.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        ValueRangeConstraint(-500, 800),
    )


_OaPumpAlarmThrTMP_Type.__name__ = "Integer32"
_OaPumpAlarmThrTMP_Object = MibTableColumn
oaPumpAlarmThrTMP = _OaPumpAlarmThrTMP_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 21, 3, 5, 1, 13),
    _OaPumpAlarmThrTMP_Type()
)
oaPumpAlarmThrTMP.setMaxAccess("read-write")
if mibBuilder.loadTexts:
    oaPumpAlarmThrTMP.setStatus("current")
if mibBuilder.loadTexts:
    oaPumpAlarmThrTMP.setDescription("pump(case) temperature alarm threshold.")


class _OaPumpAlarmThrMTH_Type(Integer32):
    """Custom type oaPumpAlarmThrMTH based on Integer32"""
    defaultValue = 650

    subtypeSpec = Integer32.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        ValueRangeConstraint(-500, 800),
    )


_OaPumpAlarmThrMTH_Type.__name__ = "Integer32"
_OaPumpAlarmThrMTH_Object = MibTableColumn
oaPumpAlarmThrMTH = _OaPumpAlarmThrMTH_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 21, 3, 5, 1, 14),
    _OaPumpAlarmThrMTH_Type()
)
oaPumpAlarmThrMTH.setMaxAccess("read-write")
if mibBuilder.loadTexts:
    oaPumpAlarmThrMTH.setStatus("current")
if mibBuilder.loadTexts:
    oaPumpAlarmThrMTH.setDescription("High module(case) temperature alarm threshold.")


class _OaPumpAlarmThrMTL_Type(Integer32):
    """Custom type oaPumpAlarmThrMTL based on Integer32"""
    defaultValue = 0

    subtypeSpec = Integer32.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        ValueRangeConstraint(-500, 800),
    )


_OaPumpAlarmThrMTL_Type.__name__ = "Integer32"
_OaPumpAlarmThrMTL_Object = MibTableColumn
oaPumpAlarmThrMTL = _OaPumpAlarmThrMTL_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 21, 3, 5, 1, 15),
    _OaPumpAlarmThrMTL_Type()
)
oaPumpAlarmThrMTL.setMaxAccess("read-write")
if mibBuilder.loadTexts:
    oaPumpAlarmThrMTL.setStatus("current")
if mibBuilder.loadTexts:
    oaPumpAlarmThrMTL.setDescription("Low module(case) temperature alarm threshold.")


class _OaPumpVoaSta_Type(Integer32):
    """Custom type oaPumpVoaSta based on Integer32"""
    defaultValue = 1

    subtypeSpec = Integer32.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        SingleValueConstraint(
            *(1,
              2,
              3,
              4)
        )
    )
    namedValues = NamedValues(
        *(("ok", 1),
          ("err", 2),
          ("pwr", 3),
          ("bsy", 4))
    )


_OaPumpVoaSta_Type.__name__ = "Integer32"
_OaPumpVoaSta_Object = MibTableColumn
oaPumpVoaSta = _OaPumpVoaSta_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 21, 3, 5, 1, 16),
    _OaPumpVoaSta_Type()
)
oaPumpVoaSta.setMaxAccess("read-only")
if mibBuilder.loadTexts:
    oaPumpVoaSta.setStatus("current")
if mibBuilder.loadTexts:
    oaPumpVoaSta.setDescription("show attenuation of VOA.")


class _OaPumpVoaSet_Type(Integer32):
    """Custom type oaPumpVoaSet based on Integer32"""
    defaultValue = 0

    subtypeSpec = Integer32.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        ValueRangeConstraint(0, 300),
    )


_OaPumpVoaSet_Type.__name__ = "Integer32"
_OaPumpVoaSet_Object = MibTableColumn
oaPumpVoaSet = _OaPumpVoaSet_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 21, 3, 5, 1, 17),
    _OaPumpVoaSet_Type()
)
oaPumpVoaSet.setMaxAccess("read-write")
if mibBuilder.loadTexts:
    oaPumpVoaSet.setStatus("current")
if mibBuilder.loadTexts:
    oaPumpVoaSet.setDescription("Attenuation setpoint in dB.")


class _OaPumpVoaAct_Type(Integer32):
    """Custom type oaPumpVoaAct based on Integer32"""
    defaultValue = 0

    subtypeSpec = Integer32.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        ValueRangeConstraint(0, 300),
    )


_OaPumpVoaAct_Type.__name__ = "Integer32"
_OaPumpVoaAct_Object = MibTableColumn
oaPumpVoaAct = _OaPumpVoaAct_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 21, 3, 5, 1, 18),
    _OaPumpVoaAct_Type()
)
oaPumpVoaAct.setMaxAccess("read-only")
if mibBuilder.loadTexts:
    oaPumpVoaAct.setStatus("current")
if mibBuilder.loadTexts:
    oaPumpVoaAct.setDescription(" Measured attenuation in dB.")


class _OaPumpVoaSupport_Type(Integer32):
    """Custom type oaPumpVoaSupport based on Integer32"""
    defaultValue = 1

    subtypeSpec = Integer32.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        SingleValueConstraint(
            *(1,
              2)
        )
    )
    namedValues = NamedValues(
        *(("support", 1),
          ("nonsupport", 2))
    )


_OaPumpVoaSupport_Type.__name__ = "Integer32"
_OaPumpVoaSupport_Object = MibTableColumn
oaPumpVoaSupport = _OaPumpVoaSupport_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 21, 3, 5, 1, 19),
    _OaPumpVoaSupport_Type()
)
oaPumpVoaSupport.setMaxAccess("read-only")
if mibBuilder.loadTexts:
    oaPumpVoaSupport.setStatus("current")
if mibBuilder.loadTexts:
    oaPumpVoaSupport.setDescription("EDFA module support voa or not.")


class _OaPumpPop_Type(Integer32):
    """Custom type oaPumpPop based on Integer32"""
    defaultValue = 0


_OaPumpPop_Type.__name__ = "Integer32"
_OaPumpPop_Object = MibTableColumn
oaPumpPop = _OaPumpPop_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 21, 3, 5, 1, 20),
    _OaPumpPop_Type()
)
oaPumpPop.setMaxAccess("read-only")
if mibBuilder.loadTexts:
    oaPumpPop.setStatus("current")
if mibBuilder.loadTexts:
    oaPumpPop.setDescription("Laser diode power in mW.")


class _OaPumpMinPower_Type(Integer32):
    """Custom type oaPumpMinPower based on Integer32"""
    defaultValue = 0

    subtypeSpec = Integer32.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        ValueRangeConstraint(0, 4000),
    )


_OaPumpMinPower_Type.__name__ = "Integer32"
_OaPumpMinPower_Object = MibTableColumn
oaPumpMinPower = _OaPumpMinPower_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 21, 3, 5, 1, 21),
    _OaPumpMinPower_Type()
)
oaPumpMinPower.setMaxAccess("read-only")
if mibBuilder.loadTexts:
    oaPumpMinPower.setStatus("current")
if mibBuilder.loadTexts:
    oaPumpMinPower.setDescription("OA P oamodevalue minimum range in dBm, RA oamodevalue minimum range in mW.")


class _OaPumpMaxPower_Type(Integer32):
    """Custom type oaPumpMaxPower based on Integer32"""
    defaultValue = 350

    subtypeSpec = Integer32.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        ValueRangeConstraint(0, 4000),
    )


_OaPumpMaxPower_Type.__name__ = "Integer32"
_OaPumpMaxPower_Object = MibTableColumn
oaPumpMaxPower = _OaPumpMaxPower_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 21, 3, 5, 1, 22),
    _OaPumpMaxPower_Type()
)
oaPumpMaxPower.setMaxAccess("read-only")
if mibBuilder.loadTexts:
    oaPumpMaxPower.setStatus("current")
if mibBuilder.loadTexts:
    oaPumpMaxPower.setDescription("OA P oamodevalue minimum range in dBm, RA W oamodevalue maximum range in mW.")


class _OaPumpMinGain_Type(Integer32):
    """Custom type oaPumpMinGain based on Integer32"""
    defaultValue = 0

    subtypeSpec = Integer32.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        ValueRangeConstraint(0, 4000),
    )


_OaPumpMinGain_Type.__name__ = "Integer32"
_OaPumpMinGain_Object = MibTableColumn
oaPumpMinGain = _OaPumpMinGain_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 21, 3, 5, 1, 23),
    _OaPumpMinGain_Type()
)
oaPumpMinGain.setMaxAccess("read-only")
if mibBuilder.loadTexts:
    oaPumpMinGain.setStatus("current")
if mibBuilder.loadTexts:
    oaPumpMinGain.setDescription("OA G oamodevalue minimum range in dB.")


class _OaPumpMaxGain_Type(Integer32):
    """Custom type oaPumpMaxGain based on Integer32"""
    defaultValue = 350

    subtypeSpec = Integer32.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        ValueRangeConstraint(0, 4000),
    )


_OaPumpMaxGain_Type.__name__ = "Integer32"
_OaPumpMaxGain_Object = MibTableColumn
oaPumpMaxGain = _OaPumpMaxGain_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 21, 3, 5, 1, 24),
    _OaPumpMaxGain_Type()
)
oaPumpMaxGain.setMaxAccess("read-only")
if mibBuilder.loadTexts:
    oaPumpMaxGain.setStatus("current")
if mibBuilder.loadTexts:
    oaPumpMaxGain.setDescription("OA G oamodevalue maximum range in dB.")


class _OaPumpMaxOutPower_Type(Integer32):
    """Custom type oaPumpMaxOutPower based on Integer32"""
    defaultValue = 160

    subtypeSpec = Integer32.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        ValueRangeConstraint(-1000, 3000),
    )


_OaPumpMaxOutPower_Type.__name__ = "Integer32"
_OaPumpMaxOutPower_Object = MibTableColumn
oaPumpMaxOutPower = _OaPumpMaxOutPower_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 21, 3, 5, 1, 25),
    _OaPumpMaxOutPower_Type()
)
oaPumpMaxOutPower.setMaxAccess("read-only")
if mibBuilder.loadTexts:
    oaPumpMaxOutPower.setStatus("current")
if mibBuilder.loadTexts:
    oaPumpMaxOutPower.setDescription("Get the max output power in dbm.")


class _OaPumpDefaultGain_Type(Integer32):
    """Custom type oaPumpDefaultGain based on Integer32"""
    defaultValue = 250

    subtypeSpec = Integer32.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        ValueRangeConstraint(0, 4000),
    )


_OaPumpDefaultGain_Type.__name__ = "Integer32"
_OaPumpDefaultGain_Object = MibTableColumn
oaPumpDefaultGain = _OaPumpDefaultGain_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 21, 3, 5, 1, 26),
    _OaPumpDefaultGain_Type()
)
oaPumpDefaultGain.setMaxAccess("read-only")
if mibBuilder.loadTexts:
    oaPumpDefaultGain.setStatus("current")
if mibBuilder.loadTexts:
    oaPumpDefaultGain.setDescription("OA G oamodevalue Default Gain in dB.")
_OaPortTable_Object = MibTable
oaPortTable = _OaPortTable_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 21, 3, 6)
)
if mibBuilder.loadTexts:
    oaPortTable.setStatus("current")
if mibBuilder.loadTexts:
    oaPortTable.setDescription("Description.")
_OaPortEntry_Object = MibTableRow
oaPortEntry = _OaPortEntry_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 21, 3, 6, 1)
)
oaPortEntry.setIndexNames(
    (0, "ST-COMMON-MIB", "shelfId"),
    (0, "ST-COMMON-MIB", "slotNo"),
    (0, "ST-COMMON-MIB", "subSlotNo"),
    (0, "ST-COMMON-MIB", "portNo"),
    (0, "ST-COMMON-MIB", "subPortNo"),
)
if mibBuilder.loadTexts:
    oaPortEntry.setStatus("current")
if mibBuilder.loadTexts:
    oaPortEntry.setDescription("Description.")


class _OaPortType_Type(Integer32):
    """Custom type oaPortType based on Integer32"""
    subtypeSpec = Integer32.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        SingleValueConstraint(
            *(1,
              2,
              3,
              4,
              5,
              6,
              7,
              8,
              9,
              10,
              11,
              12,
              13,
              14,
              15,
              16,
              17,
              18,
              19,
              20)
        )
    )
    namedValues = NamedValues(
        *(("in1", 1),
          ("out1", 2),
          ("in2", 3),
          ("out2", 4),
          ("in3", 5),
          ("out3", 6),
          ("in4", 7),
          ("out4", 8),
          ("in5", 9),
          ("out5", 10),
          ("in6", 11),
          ("out6", 12),
          ("in7", 13),
          ("out7", 14),
          ("in8", 15),
          ("out8", 16),
          ("in9", 17),
          ("out9", 18),
          ("in10", 19),
          ("out10", 20))
    )


_OaPortType_Type.__name__ = "Integer32"
_OaPortType_Object = MibTableColumn
oaPortType = _OaPortType_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 21, 3, 6, 1, 1),
    _OaPortType_Type()
)
oaPortType.setMaxAccess("read-only")
if mibBuilder.loadTexts:
    oaPortType.setStatus("current")
if mibBuilder.loadTexts:
    oaPortType.setDescription("Description.")


class _OaPortOpticalPower_Type(Integer32):
    """Custom type oaPortOpticalPower based on Integer32"""
    defaultValue = -10000

    subtypeSpec = Integer32.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        ValueRangeConstraint(-10000, 3000),
    )


_OaPortOpticalPower_Type.__name__ = "Integer32"
_OaPortOpticalPower_Object = MibTableColumn
oaPortOpticalPower = _OaPortOpticalPower_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 21, 3, 6, 1, 2),
    _OaPortOpticalPower_Type()
)
oaPortOpticalPower.setMaxAccess("read-only")
if mibBuilder.loadTexts:
    oaPortOpticalPower.setStatus("current")
if mibBuilder.loadTexts:
    oaPortOpticalPower.setDescription("Description.")


class _OaPortAlmThsop_Type(Integer32):
    """Custom type oaPortAlmThsop based on Integer32"""
    defaultValue = -100000

    subtypeSpec = Integer32.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        ValueRangeConstraint(-100000, 3000),
    )


_OaPortAlmThsop_Type.__name__ = "Integer32"
_OaPortAlmThsop_Object = MibTableColumn
oaPortAlmThsop = _OaPortAlmThsop_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 21, 3, 6, 1, 3),
    _OaPortAlmThsop_Type()
)
oaPortAlmThsop.setMaxAccess("read-write")
if mibBuilder.loadTexts:
    oaPortAlmThsop.setStatus("current")
if mibBuilder.loadTexts:
    oaPortAlmThsop.setDescription("Alarm threshold for optical power")


class _OaPortDesc_Type(DisplayString):
    """Custom type oaPortDesc based on DisplayString"""
    subtypeSpec = DisplayString.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        ValueSizeConstraint(0, 64),
    )


_OaPortDesc_Type.__name__ = "DisplayString"
_OaPortDesc_Object = MibTableColumn
oaPortDesc = _OaPortDesc_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 21, 3, 6, 1, 4),
    _OaPortDesc_Type()
)
oaPortDesc.setMaxAccess("read-write")
if mibBuilder.loadTexts:
    oaPortDesc.setStatus("current")
if mibBuilder.loadTexts:
    oaPortDesc.setDescription("Description.")
_Otu10ConfigMIB_ObjectIdentity = ObjectIdentity
otu10ConfigMIB = _Otu10ConfigMIB_ObjectIdentity(
    (1, 3, 6, 1, 4, 1, 52642, 1, 21, 4)
)
_Otu10PortTable_Object = MibTable
otu10PortTable = _Otu10PortTable_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 21, 4, 1)
)
if mibBuilder.loadTexts:
    otu10PortTable.setStatus("current")
if mibBuilder.loadTexts:
    otu10PortTable.setDescription("Description.")
_Otu10PortEntry_Object = MibTableRow
otu10PortEntry = _Otu10PortEntry_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 21, 4, 1, 1)
)
otu10PortEntry.setIndexNames(
    (0, "ST-COMMON-MIB", "shelfId"),
    (0, "ST-COMMON-MIB", "slotNo"),
    (0, "ST-COMMON-MIB", "subSlotNo"),
    (0, "ST-COMMON-MIB", "portNo"),
    (0, "ST-COMMON-MIB", "subPortNo"),
)
if mibBuilder.loadTexts:
    otu10PortEntry.setStatus("current")
if mibBuilder.loadTexts:
    otu10PortEntry.setDescription("Description.")


class _Otu10PortlaserShoutdownControl_Type(Integer32):
    """Custom type otu10PortlaserShoutdownControl based on Integer32"""
    defaultValue = 1

    subtypeSpec = Integer32.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        SingleValueConstraint(
            *(1,
              2,
              3,
              4)
        )
    )
    namedValues = NamedValues(
        *(("alsDisable", 1),
          ("alsRemoteEnable", 2),
          ("forceShutdown", 3),
          ("alsLocalEnable", 4))
    )


_Otu10PortlaserShoutdownControl_Type.__name__ = "Integer32"
_Otu10PortlaserShoutdownControl_Object = MibTableColumn
otu10PortlaserShoutdownControl = _Otu10PortlaserShoutdownControl_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 21, 4, 1, 1, 1),
    _Otu10PortlaserShoutdownControl_Type()
)
otu10PortlaserShoutdownControl.setMaxAccess("read-write")
if mibBuilder.loadTexts:
    otu10PortlaserShoutdownControl.setStatus("current")
if mibBuilder.loadTexts:
    otu10PortlaserShoutdownControl.setDescription("Description.")


class _Otu10PortlaserState_Type(Integer32):
    """Custom type otu10PortlaserState based on Integer32"""
    subtypeSpec = Integer32.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        SingleValueConstraint(
            *(1,
              2)
        )
    )
    namedValues = NamedValues(
        *(("up", 1),
          ("down", 2))
    )


_Otu10PortlaserState_Type.__name__ = "Integer32"
_Otu10PortlaserState_Object = MibTableColumn
otu10PortlaserState = _Otu10PortlaserState_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 21, 4, 1, 1, 2),
    _Otu10PortlaserState_Type()
)
otu10PortlaserState.setMaxAccess("read-only")
if mibBuilder.loadTexts:
    otu10PortlaserState.setStatus("current")
if mibBuilder.loadTexts:
    otu10PortlaserState.setDescription("Description.")


class _Otu10PortSourceSlect_Type(Integer32):
    """Custom type otu10PortSourceSlect based on Integer32"""
    subtypeSpec = Integer32.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        ValueRangeConstraint(1, 10),
    )


_Otu10PortSourceSlect_Type.__name__ = "Integer32"
_Otu10PortSourceSlect_Object = MibTableColumn
otu10PortSourceSlect = _Otu10PortSourceSlect_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 21, 4, 1, 1, 3),
    _Otu10PortSourceSlect_Type()
)
otu10PortSourceSlect.setMaxAccess("read-write")
if mibBuilder.loadTexts:
    otu10PortSourceSlect.setStatus("current")
if mibBuilder.loadTexts:
    otu10PortSourceSlect.setDescription("Description.")


class _Otu10PortRxPowHighThd_Type(Integer32):
    """Custom type otu10PortRxPowHighThd based on Integer32"""
    defaultValue = 500

    subtypeSpec = Integer32.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        ValueRangeConstraint(-6000, 2500),
    )


_Otu10PortRxPowHighThd_Type.__name__ = "Integer32"
_Otu10PortRxPowHighThd_Object = MibTableColumn
otu10PortRxPowHighThd = _Otu10PortRxPowHighThd_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 21, 4, 1, 1, 4),
    _Otu10PortRxPowHighThd_Type()
)
otu10PortRxPowHighThd.setMaxAccess("read-write")
if mibBuilder.loadTexts:
    otu10PortRxPowHighThd.setStatus("current")
if mibBuilder.loadTexts:
    otu10PortRxPowHighThd.setDescription("Description.")


class _Otu10PortRxPowLowThd_Type(Integer32):
    """Custom type otu10PortRxPowLowThd based on Integer32"""
    defaultValue = -2500

    subtypeSpec = Integer32.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        ValueRangeConstraint(-6000, 2500),
    )


_Otu10PortRxPowLowThd_Type.__name__ = "Integer32"
_Otu10PortRxPowLowThd_Object = MibTableColumn
otu10PortRxPowLowThd = _Otu10PortRxPowLowThd_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 21, 4, 1, 1, 5),
    _Otu10PortRxPowLowThd_Type()
)
otu10PortRxPowLowThd.setMaxAccess("read-write")
if mibBuilder.loadTexts:
    otu10PortRxPowLowThd.setStatus("current")
if mibBuilder.loadTexts:
    otu10PortRxPowLowThd.setDescription("Description.")


class _Otu10PortTxPowHighThd_Type(Integer32):
    """Custom type otu10PortTxPowHighThd based on Integer32"""
    defaultValue = 500

    subtypeSpec = Integer32.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        ValueRangeConstraint(-6000, 2500),
    )


_Otu10PortTxPowHighThd_Type.__name__ = "Integer32"
_Otu10PortTxPowHighThd_Object = MibTableColumn
otu10PortTxPowHighThd = _Otu10PortTxPowHighThd_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 21, 4, 1, 1, 6),
    _Otu10PortTxPowHighThd_Type()
)
otu10PortTxPowHighThd.setMaxAccess("read-write")
if mibBuilder.loadTexts:
    otu10PortTxPowHighThd.setStatus("current")
if mibBuilder.loadTexts:
    otu10PortTxPowHighThd.setDescription("Description.")


class _Otu10PortTxPowLowThd_Type(Integer32):
    """Custom type otu10PortTxPowLowThd based on Integer32"""
    defaultValue = -2000

    subtypeSpec = Integer32.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        ValueRangeConstraint(-6000, 2500),
    )


_Otu10PortTxPowLowThd_Type.__name__ = "Integer32"
_Otu10PortTxPowLowThd_Object = MibTableColumn
otu10PortTxPowLowThd = _Otu10PortTxPowLowThd_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 21, 4, 1, 1, 7),
    _Otu10PortTxPowLowThd_Type()
)
otu10PortTxPowLowThd.setMaxAccess("read-write")
if mibBuilder.loadTexts:
    otu10PortTxPowLowThd.setStatus("current")
if mibBuilder.loadTexts:
    otu10PortTxPowLowThd.setDescription("Description.")


class _Otu10ThdHysteresis_Type(Integer32):
    """Custom type otu10ThdHysteresis based on Integer32"""
    defaultValue = 100

    subtypeSpec = Integer32.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        ValueRangeConstraint(0, 1000),
    )


_Otu10ThdHysteresis_Type.__name__ = "Integer32"
_Otu10ThdHysteresis_Object = MibTableColumn
otu10ThdHysteresis = _Otu10ThdHysteresis_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 21, 4, 1, 1, 8),
    _Otu10ThdHysteresis_Type()
)
otu10ThdHysteresis.setMaxAccess("read-write")
if mibBuilder.loadTexts:
    otu10ThdHysteresis.setStatus("current")
if mibBuilder.loadTexts:
    otu10ThdHysteresis.setDescription("Description.")


class _Otu10PortCDRStatus_Type(Integer32):
    """Custom type otu10PortCDRStatus based on Integer32"""
    defaultValue = 1

    subtypeSpec = Integer32.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        SingleValueConstraint(
            *(0,
              1)
        )
    )
    namedValues = NamedValues(
        *(("close", 0),
          ("open", 1))
    )


_Otu10PortCDRStatus_Type.__name__ = "Integer32"
_Otu10PortCDRStatus_Object = MibTableColumn
otu10PortCDRStatus = _Otu10PortCDRStatus_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 21, 4, 1, 1, 9),
    _Otu10PortCDRStatus_Type()
)
otu10PortCDRStatus.setMaxAccess("read-write")
if mibBuilder.loadTexts:
    otu10PortCDRStatus.setStatus("current")
if mibBuilder.loadTexts:
    otu10PortCDRStatus.setDescription("Close or open CDR.")


class _Otu10PortCDRMode_Type(Integer32):
    """Custom type otu10PortCDRMode based on Integer32"""
    defaultValue = 1

    subtypeSpec = Integer32.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        SingleValueConstraint(
            *(0,
              1,
              2)
        )
    )
    namedValues = NamedValues(
        *(("m85G", 0),
          ("m113G", 1),
          ("m117G", 2))
    )


_Otu10PortCDRMode_Type.__name__ = "Integer32"
_Otu10PortCDRMode_Object = MibTableColumn
otu10PortCDRMode = _Otu10PortCDRMode_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 21, 4, 1, 1, 10),
    _Otu10PortCDRMode_Type()
)
otu10PortCDRMode.setMaxAccess("read-write")
if mibBuilder.loadTexts:
    otu10PortCDRMode.setStatus("current")
if mibBuilder.loadTexts:
    otu10PortCDRMode.setDescription("Change CDR mode.")
_Otu10ConfigTable_Object = MibTable
otu10ConfigTable = _Otu10ConfigTable_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 21, 4, 2)
)
if mibBuilder.loadTexts:
    otu10ConfigTable.setStatus("current")
if mibBuilder.loadTexts:
    otu10ConfigTable.setDescription("Description.")
_Otu10ConfigEntry_Object = MibTableRow
otu10ConfigEntry = _Otu10ConfigEntry_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 21, 4, 2, 1)
)
otu10ConfigEntry.setIndexNames(
    (0, "ST-COMMON-MIB", "shelfId"),
    (0, "ST-COMMON-MIB", "slotNo"),
    (0, "ST-COMMON-MIB", "subSlotNo"),
)
if mibBuilder.loadTexts:
    otu10ConfigEntry.setStatus("current")
if mibBuilder.loadTexts:
    otu10ConfigEntry.setDescription("Description.")


class _Otu10WorkMode_Type(Integer32):
    """Custom type otu10WorkMode based on Integer32"""
    defaultValue = 2

    subtypeSpec = Integer32.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        SingleValueConstraint(
            *(1,
              2,
              3,
              4,
              5,
              6)
        )
    )
    namedValues = NamedValues(
        *(("loopback", 1),
          ("forward", 2),
          ("protection", 3),
          ("double", 4),
          ("boardcast", 5),
          ("freedom", 6))
    )


_Otu10WorkMode_Type.__name__ = "Integer32"
_Otu10WorkMode_Object = MibTableColumn
otu10WorkMode = _Otu10WorkMode_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 21, 4, 2, 1, 1),
    _Otu10WorkMode_Type()
)
otu10WorkMode.setMaxAccess("read-write")
if mibBuilder.loadTexts:
    otu10WorkMode.setStatus("current")
if mibBuilder.loadTexts:
    otu10WorkMode.setDescription("Description.")


class _Otu10SwitchMode_Type(Integer32):
    """Custom type otu10SwitchMode based on Integer32"""
    defaultValue = 2

    subtypeSpec = Integer32.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        SingleValueConstraint(
            *(1,
              2)
        )
    )
    namedValues = NamedValues(
        *(("manual", 1),
          ("auto", 2))
    )


_Otu10SwitchMode_Type.__name__ = "Integer32"
_Otu10SwitchMode_Object = MibTableColumn
otu10SwitchMode = _Otu10SwitchMode_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 21, 4, 2, 1, 2),
    _Otu10SwitchMode_Type()
)
otu10SwitchMode.setMaxAccess("read-write")
if mibBuilder.loadTexts:
    otu10SwitchMode.setStatus("current")
if mibBuilder.loadTexts:
    otu10SwitchMode.setDescription("Description.")


class _Otu10SwitchState_Type(Integer32):
    """Custom type otu10SwitchState based on Integer32"""
    defaultValue = 1

    subtypeSpec = Integer32.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        SingleValueConstraint(
            *(1,
              2)
        )
    )
    namedValues = NamedValues(
        *(("primary", 1),
          ("secondary", 2))
    )


_Otu10SwitchState_Type.__name__ = "Integer32"
_Otu10SwitchState_Object = MibTableColumn
otu10SwitchState = _Otu10SwitchState_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 21, 4, 2, 1, 3),
    _Otu10SwitchState_Type()
)
otu10SwitchState.setMaxAccess("read-write")
if mibBuilder.loadTexts:
    otu10SwitchState.setStatus("current")
if mibBuilder.loadTexts:
    otu10SwitchState.setDescription("Description.")


class _Otu10SwitchHoldoffTime_Type(Integer32):
    """Custom type otu10SwitchHoldoffTime based on Integer32"""
    defaultValue = 0

    subtypeSpec = Integer32.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        ValueRangeConstraint(0, 32000),
    )


_Otu10SwitchHoldoffTime_Type.__name__ = "Integer32"
_Otu10SwitchHoldoffTime_Object = MibTableColumn
otu10SwitchHoldoffTime = _Otu10SwitchHoldoffTime_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 21, 4, 2, 1, 4),
    _Otu10SwitchHoldoffTime_Type()
)
otu10SwitchHoldoffTime.setMaxAccess("read-write")
if mibBuilder.loadTexts:
    otu10SwitchHoldoffTime.setStatus("current")
if mibBuilder.loadTexts:
    otu10SwitchHoldoffTime.setDescription("Switch holdoff time of PtoS. (Unit: Millisecond).")


class _Otu10RevertiveOfSwitchMode_Type(Integer32):
    """Custom type otu10RevertiveOfSwitchMode based on Integer32"""
    defaultValue = 0

    subtypeSpec = Integer32.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        SingleValueConstraint(
            *(0,
              1)
        )
    )
    namedValues = NamedValues(
        *(("nonrevertive", 0),
          ("revertive", 1))
    )


_Otu10RevertiveOfSwitchMode_Type.__name__ = "Integer32"
_Otu10RevertiveOfSwitchMode_Object = MibTableColumn
otu10RevertiveOfSwitchMode = _Otu10RevertiveOfSwitchMode_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 21, 4, 2, 1, 5),
    _Otu10RevertiveOfSwitchMode_Type()
)
otu10RevertiveOfSwitchMode.setMaxAccess("read-write")
if mibBuilder.loadTexts:
    otu10RevertiveOfSwitchMode.setStatus("current")
if mibBuilder.loadTexts:
    otu10RevertiveOfSwitchMode.setDescription("Revertive switch mode.")


class _Otu10RSMsht_Type(Integer32):
    """Custom type otu10RSMsht based on Integer32"""
    defaultValue = 1

    subtypeSpec = Integer32.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        ValueRangeConstraint(0, 32000),
    )


_Otu10RSMsht_Type.__name__ = "Integer32"
_Otu10RSMsht_Object = MibTableColumn
otu10RSMsht = _Otu10RSMsht_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 21, 4, 2, 1, 6),
    _Otu10RSMsht_Type()
)
otu10RSMsht.setMaxAccess("read-write")
if mibBuilder.loadTexts:
    otu10RSMsht.setStatus("current")
if mibBuilder.loadTexts:
    otu10RSMsht.setDescription("Switch holdoff time for revertive switch mode.(unit: Minute).")


class _Otu10AutoBackMode_Type(Integer32):
    """Custom type otu10AutoBackMode based on Integer32"""
    defaultValue = 1

    subtypeSpec = Integer32.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        SingleValueConstraint(
            *(0,
              1)
        )
    )
    namedValues = NamedValues(
        *(("notautoback", 0),
          ("autoback", 1))
    )


_Otu10AutoBackMode_Type.__name__ = "Integer32"
_Otu10AutoBackMode_Object = MibTableColumn
otu10AutoBackMode = _Otu10AutoBackMode_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 21, 4, 2, 1, 7),
    _Otu10AutoBackMode_Type()
)
otu10AutoBackMode.setMaxAccess("read-write")
if mibBuilder.loadTexts:
    otu10AutoBackMode.setStatus("current")
if mibBuilder.loadTexts:
    otu10AutoBackMode.setDescription("Auto-back mode.")


class _Otu10ABsht_Type(Integer32):
    """Custom type otu10ABsht based on Integer32"""
    defaultValue = 30

    subtypeSpec = Integer32.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        ValueRangeConstraint(0, 32000),
    )


_Otu10ABsht_Type.__name__ = "Integer32"
_Otu10ABsht_Object = MibTableColumn
otu10ABsht = _Otu10ABsht_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 21, 4, 2, 1, 8),
    _Otu10ABsht_Type()
)
otu10ABsht.setMaxAccess("read-write")
if mibBuilder.loadTexts:
    otu10ABsht.setStatus("current")
if mibBuilder.loadTexts:
    otu10ABsht.setDescription("Holdoff time of auto-back mode(unit: Second).")
_OmumConfigMIB_ObjectIdentity = ObjectIdentity
omumConfigMIB = _OmumConfigMIB_ObjectIdentity(
    (1, 3, 6, 1, 4, 1, 52642, 1, 21, 6)
)
_WaveLengthTable_Object = MibTable
waveLengthTable = _WaveLengthTable_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 21, 6, 1)
)
if mibBuilder.loadTexts:
    waveLengthTable.setStatus("current")
if mibBuilder.loadTexts:
    waveLengthTable.setDescription("Description.")
_WaveLengthEntry_Object = MibTableRow
waveLengthEntry = _WaveLengthEntry_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 21, 6, 1, 1)
)
waveLengthEntry.setIndexNames(
    (0, "ST-COMMON-MIB", "shelfId"),
    (0, "ST-COMMON-MIB", "slotNo"),
    (0, "ST-COMMON-MIB", "subSlotNo"),
)
if mibBuilder.loadTexts:
    waveLengthEntry.setStatus("current")
if mibBuilder.loadTexts:
    waveLengthEntry.setDescription("Description.")


class _WdmType_Type(Integer32):
    """Custom type wdmType based on Integer32"""
    defaultValue = 1

    subtypeSpec = Integer32.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        SingleValueConstraint(
            *(1,
              2)
        )
    )
    namedValues = NamedValues(
        *(("cwdm", 1),
          ("dwdm", 2))
    )


_WdmType_Type.__name__ = "Integer32"
_WdmType_Object = MibTableColumn
wdmType = _WdmType_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 21, 6, 1, 1, 1),
    _WdmType_Type()
)
wdmType.setMaxAccess("read-only")
if mibBuilder.loadTexts:
    wdmType.setStatus("current")
if mibBuilder.loadTexts:
    wdmType.setDescription("Description.")


class _PortSum_Type(Integer32):
    """Custom type portSum based on Integer32"""
    defaultValue = 16

    subtypeSpec = Integer32.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        ValueRangeConstraint(1, 99),
    )


_PortSum_Type.__name__ = "Integer32"
_PortSum_Object = MibTableColumn
portSum = _PortSum_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 21, 6, 1, 1, 2),
    _PortSum_Type()
)
portSum.setMaxAccess("read-only")
if mibBuilder.loadTexts:
    portSum.setStatus("current")
if mibBuilder.loadTexts:
    portSum.setDescription("The total number of ports.")
_WaveLengthInfo_Type = DisplayString
_WaveLengthInfo_Object = MibTableColumn
waveLengthInfo = _WaveLengthInfo_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 21, 6, 1, 1, 3),
    _WaveLengthInfo_Type()
)
waveLengthInfo.setMaxAccess("read-only")
if mibBuilder.loadTexts:
    waveLengthInfo.setStatus("current")
if mibBuilder.loadTexts:
    waveLengthInfo.setDescription("Wavalength infomation of the port.")
_VoaInfoMIB_ObjectIdentity = ObjectIdentity
voaInfoMIB = _VoaInfoMIB_ObjectIdentity(
    (1, 3, 6, 1, 4, 1, 52642, 1, 21, 7)
)
_VoaPluggableTable_Object = MibTable
voaPluggableTable = _VoaPluggableTable_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 21, 7, 1)
)
if mibBuilder.loadTexts:
    voaPluggableTable.setStatus("current")
if mibBuilder.loadTexts:
    voaPluggableTable.setDescription("Description.")
_VoaPluggableEntry_Object = MibTableRow
voaPluggableEntry = _VoaPluggableEntry_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 21, 7, 1, 1)
)
voaPluggableEntry.setIndexNames(
    (0, "ST-COMMON-MIB", "shelfId"),
    (0, "ST-COMMON-MIB", "slotNo"),
    (0, "ST-COMMON-MIB", "subSlotNo"),
    (0, "ST-COMMON-MIB", "portNo"),
    (0, "ST-COMMON-MIB", "subPortNo"),
)
if mibBuilder.loadTexts:
    voaPluggableEntry.setStatus("current")
if mibBuilder.loadTexts:
    voaPluggableEntry.setDescription("Description.")


class _VoaPluggableAttenuation_Type(Integer32):
    """Custom type voaPluggableAttenuation based on Integer32"""
    defaultValue = -10000

    subtypeSpec = Integer32.subtypeSpec
    subtypeSpec += ConstraintsUnion(
        ValueRangeConstraint(-10000, 200),
    )


_VoaPluggableAttenuation_Type.__name__ = "Integer32"
_VoaPluggableAttenuation_Object = MibTableColumn
voaPluggableAttenuation = _VoaPluggableAttenuation_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 21, 7, 1, 1, 1),
    _VoaPluggableAttenuation_Type()
)
voaPluggableAttenuation.setMaxAccess("read-write")
if mibBuilder.loadTexts:
    voaPluggableAttenuation.setStatus("current")
if mibBuilder.loadTexts:
    voaPluggableAttenuation.setDescription("Voa module tx attenuation value(unit: 0.1dBm).")


class _VoaPluggableWaveLengthMin_Type(Integer32):
    """Custom type voaPluggableWaveLengthMin based on Integer32"""
    defaultValue = -10000


_VoaPluggableWaveLengthMin_Type.__name__ = "Integer32"
_VoaPluggableWaveLengthMin_Object = MibTableColumn
voaPluggableWaveLengthMin = _VoaPluggableWaveLengthMin_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 21, 7, 1, 1, 2),
    _VoaPluggableWaveLengthMin_Type()
)
voaPluggableWaveLengthMin.setMaxAccess("read-only")
if mibBuilder.loadTexts:
    voaPluggableWaveLengthMin.setStatus("current")
if mibBuilder.loadTexts:
    voaPluggableWaveLengthMin.setDescription("Wavalength range,min value(unit: nm).")


class _VoaPluggableWaveLengthMax_Type(Integer32):
    """Custom type voaPluggableWaveLengthMax based on Integer32"""
    defaultValue = -10000


_VoaPluggableWaveLengthMax_Type.__name__ = "Integer32"
_VoaPluggableWaveLengthMax_Object = MibTableColumn
voaPluggableWaveLengthMax = _VoaPluggableWaveLengthMax_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 21, 7, 1, 1, 3),
    _VoaPluggableWaveLengthMax_Type()
)
voaPluggableWaveLengthMax.setMaxAccess("read-only")
if mibBuilder.loadTexts:
    voaPluggableWaveLengthMax.setStatus("current")
if mibBuilder.loadTexts:
    voaPluggableWaveLengthMax.setDescription("Wavalength range,max value(unit: nm).")


class _VoaPluggableAttenuationMin_Type(Integer32):
    """Custom type voaPluggableAttenuationMin based on Integer32"""
    defaultValue = 0


_VoaPluggableAttenuationMin_Type.__name__ = "Integer32"
_VoaPluggableAttenuationMin_Object = MibTableColumn
voaPluggableAttenuationMin = _VoaPluggableAttenuationMin_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 21, 7, 1, 1, 4),
    _VoaPluggableAttenuationMin_Type()
)
voaPluggableAttenuationMin.setMaxAccess("read-only")
if mibBuilder.loadTexts:
    voaPluggableAttenuationMin.setStatus("current")
if mibBuilder.loadTexts:
    voaPluggableAttenuationMin.setDescription("Attenuation range,Min value(unit: 0.1dB).")


class _VoaPluggableAttenuationMax_Type(Integer32):
    """Custom type voaPluggableAttenuationMax based on Integer32"""
    defaultValue = 200


_VoaPluggableAttenuationMax_Type.__name__ = "Integer32"
_VoaPluggableAttenuationMax_Object = MibTableColumn
voaPluggableAttenuationMax = _VoaPluggableAttenuationMax_Object(
    (1, 3, 6, 1, 4, 1, 52642, 1, 21, 7, 1, 1, 5),
    _VoaPluggableAttenuationMax_Type()
)
voaPluggableAttenuationMax.setMaxAccess("read-only")
if mibBuilder.loadTexts:
    voaPluggableAttenuationMax.setStatus("current")
if mibBuilder.loadTexts:
    voaPluggableAttenuationMax.setDescription("Attenuation range,Man value(unit: 0.1dB).")

# Managed Objects groups


# Notification objects


# Notifications groups


# Agent capabilities


# Module compliance


# Export all MIB objects to the MIB builder

mibBuilder.exportSymbols(
    "ST-6200-MIB",
    **{"st6200": st6200,
       "olpConfigMIB": olpConfigMIB,
       "olpConfigTable": olpConfigTable,
       "olpConfigEntry": olpConfigEntry,
       "olpSwitchMode": olpSwitchMode,
       "olpSwitchState": olpSwitchState,
       "olpSwitchHoldoffTime": olpSwitchHoldoffTime,
       "olpRevertiveOfSwitchMode": olpRevertiveOfSwitchMode,
       "olpRSMsht": olpRSMsht,
       "olpAutoBackMode": olpAutoBackMode,
       "olpABsht": olpABsht,
       "olpButtonEnabled": olpButtonEnabled,
       "olpConsoleEnable": olpConsoleEnable,
       "olpCardDescription": olpCardDescription,
       "olpSwitchCounter": olpSwitchCounter,
       "olpRemoteState": olpRemoteState,
       "olpActiveAlmThsOpRx": olpActiveAlmThsOpRx,
       "olpActiveAlmThsOpTx": olpActiveAlmThsOpTx,
       "olpStandbyAlmThsOpRx": olpStandbyAlmThsOpRx,
       "olpStandbyAlmThsOpTx": olpStandbyAlmThsOpTx,
       "olpActiveSwtThsOpRx": olpActiveSwtThsOpRx,
       "olpStandbySwtThsOpRx": olpStandbySwtThsOpRx,
       "olpAlmThsOpRx": olpAlmThsOpRx,
       "olpAlmThsOpTx": olpAlmThsOpTx,
       "olpBypassMode": olpBypassMode,
       "olpBypassBackMode": olpBypassBackMode,
       "olpHeartInputMode": olpHeartInputMode,
       "olpHeartState": olpHeartState,
       "olpHeartAlive": olpHeartAlive,
       "olpHeartIdle": olpHeartIdle,
       "olpHeartInterval": olpHeartInterval,
       "olpHeartCount": olpHeartCount,
       "olpAlamTurnEnabled": olpAlamTurnEnabled,
       "olpPowerDiffThs": olpPowerDiffThs,
       "olpPortTable": olpPortTable,
       "olpPortEntry": olpPortEntry,
       "olpPortType": olpPortType,
       "olpPortOpticalPower": olpPortOpticalPower,
       "olpPortAlmThsOP": olpPortAlmThsOP,
       "olpPortSwtThsOp": olpPortSwtThsOp,
       "olpPortWaveLen": olpPortWaveLen,
       "olpPortDesc": olpPortDesc,
       "olpCommandTable": olpCommandTable,
       "olpCommandEntry": olpCommandEntry,
       "olpCmdSwitch": olpCmdSwitch,
       "olpCmdSwitchCounterClear": olpCmdSwitchCounterClear,
       "olpCardTable": olpCardTable,
       "olpCardEntry": olpCardEntry,
       "olpCardAddr": olpCardAddr,
       "olpCardType": olpCardType,
       "olpCardSN": olpCardSN,
       "olpCardPartN": olpCardPartN,
       "olpCardLabel": olpCardLabel,
       "olpCardHwVersion": olpCardHwVersion,
       "olpCardSwVersion": olpCardSwVersion,
       "olpCardCpldVersion": olpCardCpldVersion,
       "olpCardTemperature": olpCardTemperature,
       "olpCardRowStatus": olpCardRowStatus,
       "oaConfigMIB": oaConfigMIB,
       "oaModuleTable": oaModuleTable,
       "oaModuleEntry": oaModuleEntry,
       "oaModuleConfiguration": oaModuleConfiguration,
       "oaModuleFirmwareVers": oaModuleFirmwareVers,
       "oaModuleSerialNumber": oaModuleSerialNumber,
       "oaModuleType": oaModuleType,
       "oaModuleFunction": oaModuleFunction,
       "oaModuleMaxOutPower": oaModuleMaxOutPower,
       "oaModuleDefaultGain": oaModuleDefaultGain,
       "oaModuleVariableGain": oaModuleVariableGain,
       "oaModuleMinGain": oaModuleMinGain,
       "oaModuleMaxGain": oaModuleMaxGain,
       "oaModuleMinPower": oaModuleMinPower,
       "oaModuleMaxPower": oaModuleMaxPower,
       "oaModuleDcmSupport": oaModuleDcmSupport,
       "oaModuleDcmValue": oaModuleDcmValue,
       "oaModuleOscSupport": oaModuleOscSupport,
       "oaModuleOscWave": oaModuleOscWave,
       "oaModulePumpNum": oaModulePumpNum,
       "oaModuleWaveNum": oaModuleWaveNum,
       "oaModulePumpTotalPower": oaModulePumpTotalPower,
       "oaModuleTILT": oaModuleTILT,
       "oaPumpTable": oaPumpTable,
       "oaPumpEntry": oaPumpEntry,
       "oaPumpControlMode": oaPumpControlMode,
       "oaPumpModeValue": oaPumpModeValue,
       "oaPumpNum": oaPumpNum,
       "oaPumpTemperature": oaPumpTemperature,
       "oaPumpIld": oaPumpIld,
       "oaPumpEol": oaPumpEol,
       "oaPumpTmp": oaPumpTmp,
       "oaPumpItc": oaPumpItc,
       "oaPumpVtc": oaPumpVtc,
       "oaPumpIsp": oaPumpIsp,
       "oaPumpIspValue": oaPumpIspValue,
       "oaPumpAlarmThrILD": oaPumpAlarmThrILD,
       "oaPumpAlarmThrTMP": oaPumpAlarmThrTMP,
       "oaPumpAlarmThrMTH": oaPumpAlarmThrMTH,
       "oaPumpAlarmThrMTL": oaPumpAlarmThrMTL,
       "oaPumpVoaSta": oaPumpVoaSta,
       "oaPumpVoaSet": oaPumpVoaSet,
       "oaPumpVoaAct": oaPumpVoaAct,
       "oaPumpVoaSupport": oaPumpVoaSupport,
       "oaPumpPop": oaPumpPop,
       "oaPumpMinPower": oaPumpMinPower,
       "oaPumpMaxPower": oaPumpMaxPower,
       "oaPumpMinGain": oaPumpMinGain,
       "oaPumpMaxGain": oaPumpMaxGain,
       "oaPumpMaxOutPower": oaPumpMaxOutPower,
       "oaPumpDefaultGain": oaPumpDefaultGain,
       "oaPortTable": oaPortTable,
       "oaPortEntry": oaPortEntry,
       "oaPortType": oaPortType,
       "oaPortOpticalPower": oaPortOpticalPower,
       "oaPortAlmThsop": oaPortAlmThsop,
       "oaPortDesc": oaPortDesc,
       "otu10ConfigMIB": otu10ConfigMIB,
       "otu10PortTable": otu10PortTable,
       "otu10PortEntry": otu10PortEntry,
       "otu10PortlaserShoutdownControl": otu10PortlaserShoutdownControl,
       "otu10PortlaserState": otu10PortlaserState,
       "otu10PortSourceSlect": otu10PortSourceSlect,
       "otu10PortRxPowHighThd": otu10PortRxPowHighThd,
       "otu10PortRxPowLowThd": otu10PortRxPowLowThd,
       "otu10PortTxPowHighThd": otu10PortTxPowHighThd,
       "otu10PortTxPowLowThd": otu10PortTxPowLowThd,
       "otu10ThdHysteresis": otu10ThdHysteresis,
       "otu10PortCDRStatus": otu10PortCDRStatus,
       "otu10PortCDRMode": otu10PortCDRMode,
       "otu10ConfigTable": otu10ConfigTable,
       "otu10ConfigEntry": otu10ConfigEntry,
       "otu10WorkMode": otu10WorkMode,
       "otu10SwitchMode": otu10SwitchMode,
       "otu10SwitchState": otu10SwitchState,
       "otu10SwitchHoldoffTime": otu10SwitchHoldoffTime,
       "otu10RevertiveOfSwitchMode": otu10RevertiveOfSwitchMode,
       "otu10RSMsht": otu10RSMsht,
       "otu10AutoBackMode": otu10AutoBackMode,
       "otu10ABsht": otu10ABsht,
       "omumConfigMIB": omumConfigMIB,
       "waveLengthTable": waveLengthTable,
       "waveLengthEntry": waveLengthEntry,
       "wdmType": wdmType,
       "portSum": portSum,
       "waveLengthInfo": waveLengthInfo,
       "voaInfoMIB": voaInfoMIB,
       "voaPluggableTable": voaPluggableTable,
       "voaPluggableEntry": voaPluggableEntry,
       "voaPluggableAttenuation": voaPluggableAttenuation,
       "voaPluggableWaveLengthMin": voaPluggableWaveLengthMin,
       "voaPluggableWaveLengthMax": voaPluggableWaveLengthMax,
       "voaPluggableAttenuationMin": voaPluggableAttenuationMin,
       "voaPluggableAttenuationMax": voaPluggableAttenuationMax}
)
