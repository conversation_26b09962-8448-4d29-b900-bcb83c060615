import {getText} from "@/modules-otn/utils/util";
import ServiceCommon from "@/modules-otn/pages/otn/service/service_common";
import {Tabs} from "antd";
import {useLocation, useNavigate} from "react-router-dom";
import {useEffect, useState} from "react";
import styles from "./service_layer0.module.scss";

const ServiceLayer0 = () => {
    const itemsConfig = [
        {
            tableKey: "power",
            key: "optical-power-management",
            label: getText("optical-power-management")
        },
        {
            tableKey: "edfa",
            key: "amplifier-configuration",
            label: getText("amplifier-configuration")
        },
        {
            tableKey: "wss",
            key: "wss",
            label: getText("wss")
        },
        {
            tableKey: "tff",
            key: "tff-configuration",
            label: getText("tff-configuration")
        },
        {
            tableKey: "oeo",
            key: "otu-configuration",
            label: getText("otu-configuration")
        }
    ];

    const navigate = useNavigate();
    const location = useLocation();
    const [currentActiveKey, setCurrentActiveKey] = useState();
    const pathReg = /(optical-power-management|amplifier-configuration|wss|tff-configuration|otu-configuration)$/;
    useEffect(() => {
        const currentPath = location.pathname;
        if (pathReg.test(currentPath)) {
            setCurrentActiveKey(currentPath.match(pathReg)[0]);
        }
    }, [location.pathname]);

    const onChange = key => {
        const currentPath = location.pathname;
        if (pathReg.test(currentPath)) {
            const matchLength = currentPath.match(pathReg)[0].length;
            const parentPath = currentPath.slice(0, -matchLength);
            navigate(parentPath + key);
        }
    };

    return (
        <Tabs
            className={styles.tabs}
            destroyInactiveTabPane
            items={itemsConfig.map(i => ({
                ...i,
                style: {flex: 1, display: "flex"},
                children: <ServiceCommon tabType={i.tableKey} />
            }))}
            activeKey={currentActiveKey}
            onChange={onChange}
        />
    );
};

export default ServiceLayer0;
