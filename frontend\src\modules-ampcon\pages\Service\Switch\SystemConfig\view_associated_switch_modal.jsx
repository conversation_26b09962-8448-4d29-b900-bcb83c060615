import {forwardRef, useEffect, useImperativeHandle, useState} from "react";
import {
    AmpConCustomModalTable,
    createColumnConfigMultipleParams,
    TableFilterDropdown
} from "@/modules-ampcon/components/custom_table";
import {querySwitchFilterBySystemConfigName} from "@/modules-ampcon/apis/config_api";

const ViewAssociatedSwitchModal = forwardRef((props, ref) => {
    useImperativeHandle(ref, () => ({
        showViewAssociatedSwitchModal: configName => {
            setSelectedSystemConfig(configName);
            setIsShowModal(true);
        },
        hideViewAssociatedSwitchModal: () => {
            setIsShowModal(false);
        }
    }));

    useEffect(() => {}, []);

    const title = "View Associated Switch";

    const matchFieldsList = [
        {name: "host_name", matchMode: "fuzzy"},
        {name: "sn", matchMode: "fuzzy"},
        {name: "platform_model", matchMode: "fuzzy"},
        {name: "status", matchMode: "fuzzy"},
        {name: "mgt_ip", matchMode: "fuzzy"}
    ];
    const searchFieldsList = ["host_name", "sn", "platform_model", "status", "mgt_ip"];

    const [isShowModal, setIsShowModal] = useState(false);
    const [selectedSystemConfig, setSelectedSystemConfig] = useState(null);

    return (
        <AmpConCustomModalTable
            modalClass="ampcon-max-modal"
            title={title}
            selectModalOpen={isShowModal}
            onCancel={() => {
                setIsShowModal(false);
            }}
            columns={[
                createColumnConfigMultipleParams({
                    title: "Switch Name",
                    dataIndex: "host_name",
                    filterDropdownComponent: TableFilterDropdown,
                    width: "15%"
                }),
                createColumnConfigMultipleParams({
                    title: "SN / Service Tag",
                    dataIndex: "sn",
                    filterDropdownComponent: TableFilterDropdown,
                    width: "15%"
                }),
                createColumnConfigMultipleParams({
                    title: "Model",
                    dataIndex: "platform_model",
                    filterDropdownComponent: TableFilterDropdown,
                    width: "15%"
                }),
                createColumnConfigMultipleParams({
                    title: "Version",
                    width: "15%",
                    enableSorter: false,
                    enableFilter: false,
                    render: (_, record) => {
                        if (record.revision === null) {
                            return "";
                        }
                        if (record.version === null) {
                            return `${record.version}`;
                        }
                        return `${record.version}/${record.revision}`;
                    }
                }),
                createColumnConfigMultipleParams({
                    title: "Status",
                    dataIndex: "status",
                    filterDropdownComponent: TableFilterDropdown,
                    width: "15%"
                }),
                createColumnConfigMultipleParams({
                    title: "Mgmt",
                    dataIndex: "mgt_ip",
                    filterDropdownComponent: TableFilterDropdown,
                    width: "15%"
                })
            ]}
            matchFieldsList={matchFieldsList}
            searchFieldsList={searchFieldsList}
            buttonProps={[]}
            fetchAPIInfo={querySwitchFilterBySystemConfigName}
            fetchAPIParams={[selectedSystemConfig]}
        />
    );
});

export default ViewAssociatedSwitchModal;
