import copy
import json
import logging
import redis
import time
import traceback
import threading
from sqlalchemy import and_, or_, func
from flask import Blueprint, jsonify, Response, request
from cryptography.fernet import Fernet
from server.db.models import inventory
from server.db.models.monitor import Event
from server.db.models.inventory import SwitchMenuTreeInfo, SwitchNeInfo, SwitchGis
from server.db.models.otn import OtnTempData, OtnDeviceBasic, FmtDeviceCards, DcsDeviceCards
from server.util import fmt_util
from server.util.permission import admin_permission
from server.util import utils

invent_db = inventory.inven_db
redis_client = redis.StrictRedis(host='redis-service', port=6379, db=0, decode_responses=True)
fmt_module = Blueprint("fmt_module", __name__, template_folder="templates")
dcs_module = Blueprint("dcs_module", __name__, template_folder="templates")
LOG = logging.getLogger(__name__)


@fmt_module.route("/info/query", methods=["GET"])
@admin_permission.require(http_exception=403)
def query_info():
    id = request.args.get('id')
    ip = request.args.get('ip')
    if id is None and ip is None:
        return Response(json.dumps({"data": "", "errorCode": 1, "errorMsg": "At least one of IP and ID is required!"}),
                        mimetype="application/json")
    # 下发指令查询设备数据
    data = fmt_util.beat_sync_otn_device_info_single(id=id, ip=ip)
    errorCode = 0
    if data is None:
        errorCode = 1
    result = {"data": data, "errorCode": errorCode, "errorMsg": ""}
    return Response(json.dumps(result), mimetype="application/json")


@dcs_module.route("/info/query", methods=["GET"])
@admin_permission.require(http_exception=403)
def query_info():
    id = request.args.get('id')
    ip = request.args.get('ip')
    if id is None and ip is None:
        return Response(json.dumps({"data": "", "errorCode": 1, "errorMsg": "At least one of IP and ID is required!"}),
                        mimetype="application/json")
    # 下发指令查询设备数据
    data = fmt_util.beat_sync_otn_device_info_single(id=id, ip=ip)
    errorCode = 0
    if data is None:
        errorCode = 1
    result = {"data": data, "errorCode": errorCode, "errorMsg": ""}
    return Response(json.dumps(result), mimetype="application/json")


@fmt_module.route("/info/get", methods=["GET"])
@admin_permission.require(http_exception=403)
def get_info():
    id = request.args.get('id')
    ip = request.args.get('ip')
    if id is None and ip is None:
        return Response(json.dumps({"data": "", "errorCode": 1, "errorMsg": "At least one of IP and ID is required!"}),
                        mimetype="application/json")

    db_session = invent_db.get_session()
    fmt_temp_data = db_session.query(OtnTempData)
    if id is None:
        data = fmt_temp_data.filter(OtnTempData.ip == ip).first()
    elif ip is None:
        data = fmt_temp_data.filter(OtnTempData.id == id).first()
    else:
        data = fmt_temp_data.filter(OtnTempData.id == id, OtnTempData.ip == ip).first()

    if not data:
        return Response(json.dumps({"data": "", "errorCode": 1, "errorMsg": "Data is empty!"}),
                        mimetype="application/json")
    result = {"data": fmt_util.get_device_detail(data.id), "errorCode": 0, "errorMsg": ""}
    return Response(json.dumps(result), mimetype="application/json")

@dcs_module.route("/info/get", methods=["GET"])
@admin_permission.require(http_exception=403)
def get_info():
    id = request.args.get('id')
    ip = request.args.get('ip')
    if id is None and ip is None:
        return Response(json.dumps({"data": "", "errorCode": 1, "errorMsg": "At least one of IP and ID is required!"}),
                        mimetype="application/json")

    db_session = invent_db.get_session()
    fmt_temp_data = db_session.query(OtnTempData)
    if id is None:
        data = fmt_temp_data.filter(OtnTempData.ip == ip).first()
    elif ip is None:
        data = fmt_temp_data.filter(OtnTempData.id == id).first()
    else:
        data = fmt_temp_data.filter(OtnTempData.id == id, OtnTempData.ip == ip).first()

    if not data:
        return Response(json.dumps({"data": "", "errorCode": 1, "errorMsg": "Data is empty!"}),
                        mimetype="application/json")
    result = {"data": fmt_util.get_device_detail(data.id, "D6000"), "errorCode": 0, "errorMsg": ""}
    return Response(json.dumps(result), mimetype="application/json")


@fmt_module.route("/config/query", methods=["GET"])
@admin_permission.require(http_exception=403)
def get_config():
    ip = request.args.get('ip')
    slotIndex = request.args.get('slotIndex')
    cardId = request.args.get('cardId')
    if ip is None or (slotIndex is None and cardId is None):
        return Response(json.dumps({"data": "", "errorCode": 1,
                                    "errorMsg": "Ip and at least one of slotIndex and cardId is required!"}),
                        mimetype="application/json")
    if slotIndex is None:
        slotIndex = -1
    else:
        slotIndex = int(slotIndex)
    print(f"slotIndex={slotIndex} , type={type(slotIndex)}")
    if cardId is None:
        cardId = ""
    db_session = invent_db.get_session()
    card = db_session.query(FmtDeviceCards).join(OtnDeviceBasic,
                                                 OtnDeviceBasic.id == FmtDeviceCards.device_id).filter(
        and_(OtnDeviceBasic.ip == ip,
             or_(FmtDeviceCards.slot_index == slotIndex, FmtDeviceCards.card_id == cardId))).first()
    if not card:
        return Response(json.dumps({"data": "", "errorCode": 1, "errorMsg": "Device is not exist!"}),
                        mimetype="application/json")
    slotIndex = card.slot_index
    data, errorCode, errorMsg = fmt_util.get_config(ip, slotIndex)
    result = {"data": data, "errorCode": errorCode, "errorMsg": errorMsg}
    return Response(json.dumps(result), mimetype="application/json")


@fmt_module.route("/config/modify", methods=["PUT"])
@admin_permission.require(http_exception=403)
def modify_config():
    data = request.get_json()
    ip = data.get("ip", "")
    slotIndex = data.get("slotIndex", -1)
    cardId = data.get('cardId')
    key = data.get("key")
    value = data.get("value")
    print(f"parameters key:{key}")
    if ip is None or (slotIndex is None and cardId is None) or key is None or value is None:
        return Response(
            json.dumps({"data": "", "errorCode": 1, "errorMsg": "Ip, slotIndex, key and value is required!"}),
            mimetype="application/json")
    db_session = invent_db.get_session()
    card = db_session.query(FmtDeviceCards).join(OtnDeviceBasic,
                                                 OtnDeviceBasic.id == FmtDeviceCards.device_id).filter(
        and_(OtnDeviceBasic.ip == ip,
             or_(FmtDeviceCards.slot_index == slotIndex, FmtDeviceCards.card_id == cardId))).first()
    if not card:
        return Response(json.dumps({"data": "", "errorCode": 1, "errorMsg": "Device is not exist!"}),
                        mimetype="application/json")

    slotIndex = str(card.slot_index)
    data, errorCode, errorMsg = fmt_util.modify_config(ip, slotIndex, key, value, "FMT")
    result = {"data": data, "errorCode": errorCode, "errorMsg": errorMsg}
    return Response(json.dumps(result), mimetype="application/json")

# 这个是串行的进行修改 且没有复用socket_client
# @dcs_module.route("/config/batch_modify", methods=["PUT"])
# @admin_permission.require(http_exception=403)
# def batch_modify_dcs_port():
#     data = request.get_json()
#     ip = data.get("ip", "")
#     slotIndex = data.get("slotIndex", -1)
#     cardId = data.get("cardId")
#     updates = data.get("updates", [])  # 获取批量更新参数
#
#     if not ip or (slotIndex is None and not cardId) or not updates:
#         return Response(
#             json.dumps({"data": "", "errorCode": 1,
#                        "errorMsg": "Ip, slotIndex/cardId and updates are required!"}),
#             mimetype="application/json")
#
#     db_session = invent_db.get_session()
#     card = db_session.query(DcsDeviceCards).join(OtnDeviceBasic,
#                                                  OtnDeviceBasic.id == DcsDeviceCards.device_id).filter(
#         and_(OtnDeviceBasic.ip == ip,
#              or_(DcsDeviceCards.slot_index == slotIndex, DcsDeviceCards.card_id == cardId))).first()
#     if not card:
#         return Response(json.dumps({"data": "", "errorCode": 1, "errorMsg": "Device is not exist!"}),
#                         mimetype="application/json")
#
#     slotIndex = str(card.slot_index)
#     error_flag = 0
#     results = []
#     for item in updates:
#         key = item.get("key")
#         value = item.get("value")
#
#         if not key or not value:
#             results.append({"key": key, "error": "Invalid parameters"})
#             error_flag = 1
#             continue
#         data, errorCode, errorMsg = fmt_util.modify_config(ip, slotIndex, key, value, "D6000")
#         if errorCode != 0:
#             results.append({"key": key, "error": errorMsg})
#             error_flag = 1
#         else:
#             results.append({"key": key, "data": data})
#
#     return Response(
#         json.dumps({
#             "data": results,
#             "errorCode": error_flag,
#             "errorMsg": "Batch operation completed" if error_flag else ""
#         }),
#         mimetype="application/json"
#     )

# 这个是串行的进行修改 且复用socket_client
# @dcs_module.route("/config/batch_modify", methods=["PUT"])
# @admin_permission.require(http_exception=403)
# def batch_modify_dcs_port():
#     data = request.get_json()
#     ip = data.get("ip", "")
#     slotIndex = data.get("slotIndex", -1)
#     cardId = data.get("cardId")
#     updates = data.get("updates", [])
#
#     if not ip or (slotIndex is None and not cardId) or not updates:
#         return Response(
#             json.dumps({"data": "", "errorCode": 1,
#                        "errorMsg": "Ip, slotIndex/cardId and updates are required!"}),
#             mimetype="application/json")
#
#     db_session = invent_db.get_session()
#     card = db_session.query(DcsDeviceCards).join(OtnDeviceBasic,
#                                                  OtnDeviceBasic.id == DcsDeviceCards.device_id).filter(
#         and_(OtnDeviceBasic.ip == ip,
#              or_(DcsDeviceCards.slot_index == slotIndex, DcsDeviceCards.card_id == cardId))).first()
#     if not card:
#         return Response(json.dumps({"data": "", "errorCode": 1, "errorMsg": "Device is not exist!"}),
#                         mimetype="application/json")
#
#     slotIndex = str(card.slot_index)
#
#     data_list, errorCode, errorMsg = fmt_util.batchModify_config(ip, slotIndex, updates, "D6000")
#
#     results = []
#     error_flag = 0  # 标记是否有至少一个错误
#
#     for idx, item in enumerate(updates):
#         key = item.get("key")
#         result_data = data_list[idx] if idx < len(data_list) else ""
#
#         # 如果响应包含错误标记（如 _ERR、PARAM_ERROR、EXCEPTION_）
#         if "_ERR" in result_data:
#             results.append({"key": key, "error": result_data})
#             error_flag = 1
#         else:
#             results.append({"key": key, "data": result_data})
#
#     return Response(
#         json.dumps({
#             "data": results,
#             "errorCode": error_flag,  # 1 表示有至少一个错误
#             "errorMsg": errorMsg if error_flag else ""  # 全局错误消息（可选）
#         }),
#         mimetype="application/json"
#     )

# 并行下发多个配置项
@dcs_module.route("/config/batch_modify", methods=["PUT"])
@admin_permission.require(http_exception=403)
def batch_modify_dcs_port():
    data = request.get_json()
    ip = data.get("ip", "")
    slotIndex = data.get("slotIndex", -1)
    cardId = data.get("cardId")
    updates = data.get("updates", [])

    if not ip or (slotIndex is None and not cardId) or not updates:
        return Response(
            json.dumps({"data": "", "errorCode": 1,
                        "errorMsg": "Ip, slotIndex/cardId and updates are required!"}),
            mimetype="application/json")

    db_session = invent_db.get_session()

    # PSU板卡处理 重定向到NMU板卡（槽位16）
    original_slot_index = slotIndex
    if str(slotIndex) in ["9", "10"]:
        LOG.info(f"PSU configuration request for slot {slotIndex}, redirecting to NMU slot 16")
        nmu_slot_index = 16
        card = db_session.query(DcsDeviceCards).join(OtnDeviceBasic,
                                                     OtnDeviceBasic.id == DcsDeviceCards.device_id).filter(
            and_(OtnDeviceBasic.ip == ip, DcsDeviceCards.slot_index == nmu_slot_index)).first()

        if not card:
            LOG.warning(f"NMU card not found for IP {ip}, slot {nmu_slot_index}, creating virtual NMU record")
            # 查找设备基本信息
            device_basic = db_session.query(OtnDeviceBasic).filter(OtnDeviceBasic.ip == ip).first()
            if not device_basic:
                LOG.error(f"Device not found for IP {ip}")
                return Response(json.dumps({"data": "", "errorCode": 1, "errorMsg": "Device is not exist!"}),
                                mimetype="application/json")

            # 创建NMU卡片记录用于配置
            try:
                with db_session.begin():
                    card_id = f"{device_basic.id}_{nmu_slot_index}"
                    existing_card = db_session.query(DcsDeviceCards).filter(DcsDeviceCards.card_id == card_id).first()

                    if not existing_card:
                        new_nmu_card = DcsDeviceCards(
                            card_id=card_id,
                            device_id=device_basic.id,
                            slot_index=nmu_slot_index,
                            type="NMU",
                            model="NMU",
                            ports_data="{}",
                            temperature=None
                        )
                        db_session.add(new_nmu_card)
                        db_session.flush()
                        card = new_nmu_card
                        LOG.info(f"Created virtual NMU card: {card_id}")
                    else:
                        card = existing_card
                        LOG.info(f"Using existing NMU card: {card_id}")

            except Exception as e:
                LOG.error(f"Failed to create NMU card: {e}")
                return Response(
                    json.dumps({"data": "", "errorCode": 1, "errorMsg": f"Failed to create NMU device: {str(e)}"}),
                    mimetype="application/json")
        else:
            LOG.info(f"Found existing NMU card: {card.card_id}")

        # 字段映射
        mapped_updates = []
        for update in updates:
            key = update.get("key")
            value = update.get("value")

            if key == "power_switch":
                # PSU1 (槽位9) -> power_switch1, PSU2 (槽位10) -> power_switch2
                if str(original_slot_index) == "9":
                    mapped_key = "power_switch1"
                elif str(original_slot_index) == "10":
                    mapped_key = "power_switch2"
                else:
                    mapped_key = key

                LOG.info(f"Mapping PSU field: {key} -> {mapped_key} with value {value}")
                mapped_updates.append({"key": mapped_key, "value": value})
            else:
                mapped_updates.append({"key": key, "value": value})

        slotIndex = str(nmu_slot_index)
        updates = mapped_updates

    else:
        card = db_session.query(DcsDeviceCards).join(OtnDeviceBasic,
                                                     OtnDeviceBasic.id == DcsDeviceCards.device_id).filter(
            and_(OtnDeviceBasic.ip == ip,
                 or_(DcsDeviceCards.slot_index == slotIndex, DcsDeviceCards.card_id == cardId))).first()
        if not card:
            return Response(json.dumps({"data": "", "errorCode": 1, "errorMsg": "Device is not exist!"}),
                            mimetype="application/json")

        slotIndex = str(card.slot_index)

    data_list, errorCode, errorMsg = fmt_util.batchModify_config(ip, slotIndex, updates, "D6000")

    results = []
    for idx, item in enumerate(updates):
        key = item.get("key")

        response = data_list[idx] if idx < len(data_list) else ""

        # 修改响应处理逻辑
        error = None
        if "_ERR" in response:
            error = f"Device error: {response}"
        elif not response.strip():
            if key and key.startswith("power_switch"):
                LOG.info(f"Power switch operation {key} completed with empty response")
                response = "OK"
            else:
                error = "No response from device"
        else:
            if response.strip().upper() in ["OK", "SUCCESS", "COMPLETE"]:
                LOG.info(f"Command {key} executed successfully")
            elif key and key.startswith("power_switch") and len(response.strip()) > 0:
                LOG.info(f"Power operation {key} completed: {response}")
                response = "OK"

        if error:
            results.append({"key": key, "error": error, "response": response})
        else:
            results.append({"key": key, "data": response, "response": response})

    # 错误码反馈
    final_error_code = 1 if any("error" in result for result in results) else 0

    # 电源操作判定
    if final_error_code == 1:
        power_operations = [r for r in results if r.get("key", "").startswith("power_switch")]
        if power_operations and all("error" not in op for op in power_operations):
            other_operations = [r for r in results if not r.get("key", "").startswith("power_switch")]
            if not other_operations or all("error" not in op for op in other_operations):
                final_error_code = 0
                LOG.info("All operations completed successfully, including power operations")

    return Response(
        json.dumps({
            "data": results,
            "errorCode": final_error_code,
            "errorMsg": errorMsg if final_error_code == 1 else ""
        }),
        mimetype="application/json"
    )


@fmt_module.route("/config/set_note", methods=["POST"])
@admin_permission.require(http_exception=403)
def modify_port_note():
    data = request.get_json()
    ip = data.get("ip")
    slotIndex = data.get("slotIndex")
    cardId = data.get('cardId')
    port = data.get("port")
    note = data.get("note")
    if (ip is None) or (slotIndex is None and cardId is None) or (port is None) or (note is None):
        return Response(
            json.dumps({"data": "", "errorCode": 1, "errorMsg": "Ip, slotIndex, port and note is required!"}),
            mimetype="application/json")
    db_session = invent_db.get_session()
    card = db_session.query(FmtDeviceCards).join(OtnDeviceBasic,
                                                 OtnDeviceBasic.id == FmtDeviceCards.device_id).filter(
        and_(OtnDeviceBasic.ip == ip,
             or_(FmtDeviceCards.slot_index == slotIndex, FmtDeviceCards.card_id == cardId))).first()
    if not card:
        return Response(json.dumps({"data": "", "errorCode": 1, "errorMsg": "Device is not exist!"}),
                        mimetype="application/json")

    if slotIndex == "0":
        innerKey = "Slot Note"
    elif card.type == "OEO":
        innerKey = "Service Notes"
    else:
        innerKey = "Port Note"

    newPortData = fmt_util.set_note(port, innerKey, note, card.ports_data)
    db_session.query(FmtDeviceCards).filter(FmtDeviceCards.card_id == card.card_id).update({
        FmtDeviceCards.ports_data: newPortData})

    result = {"data": newPortData, "errorCode": 0, "errorMsg": ""}
    return Response(json.dumps(result), mimetype="application/json")


@dcs_module.route("/config/set_note", methods=["POST"])
@admin_permission.require(http_exception=403)
def modify_port_note():
    data = request.get_json()
    ip = data.get("ip")
    slotIndex = data.get("slotIndex")
    cardId = data.get('cardId')
    port = data.get("port")
    note = data.get("note")
    if (ip is None) or (slotIndex is None and cardId is None) or (port is None) or (note is None):
        return Response(
            json.dumps({"data": "", "errorCode": 1, "errorMsg": "Ip, slotIndex, port and note is required!"}),
            mimetype="application/json")
    db_session = invent_db.get_session()
    try:
        with db_session.begin():
            card = db_session.query(DcsDeviceCards).join(OtnDeviceBasic,
                                                         OtnDeviceBasic.id == DcsDeviceCards.device_id).filter(
                and_(OtnDeviceBasic.ip == ip,
                     or_(DcsDeviceCards.slot_index == slotIndex, DcsDeviceCards.card_id == cardId))).first()
            if not card:
                return Response(json.dumps({"data": "", "errorCode": 1, "errorMsg": "Device does not exist!"}),
                                mimetype="application/json")

            if slotIndex == "0":
                innerKey = "slot_note"
            else:
                innerKey = "port_note"

            newPortData = fmt_util.set_note(port, innerKey, note, card.ports_data)
            db_session.query(DcsDeviceCards).filter(DcsDeviceCards.card_id == card.card_id).update({
                DcsDeviceCards.ports_data: newPortData})

            if slotIndex == "16":
                fmt_util.update_otntempdata(db_session, card.device_id, port, note)
    except Exception as e:
        db_session.rollback()
        return Response(json.dumps({"data": "", "errorCode": 1, "errorMsg": str(e)}),
                        mimetype="application/json")

    result = {"data": newPortData, "errorCode": 0, "errorMsg": ""}
    return Response(json.dumps(result), mimetype="application/json")


@fmt_module.route("/get_fmt_device_card", methods=["POST"])
@admin_permission.require(http_exception=403)
def get_fmt_device_card():
    """
    OA板卡类型小类:
    FMT20PA-EDFA
    FMT17BA-EDFA
    FMT26PA-51EDFA
    FMT17BA-51EDFA
    FMT22BA-EDFA
    FMTPA-Array
    FMTBA-Array
    HPA
    FS-SOA
    EDFA-LA
    """

    fmt_cards_ret = {"data": [], "errorMsg": ""}
    try:
        data = request.get_json()
        ip = data["ip"]
        filter_card = data["filterCard"]
        with invent_db.get_session() as db_session:
            device_obj = db_session.query(OtnDeviceBasic).filter(OtnDeviceBasic.ip == ip).first()
            if not device_obj:
                raise ValueError("Device is not exist!")

            fmt_device_cards_objs = db_session.query(FmtDeviceCards).filter(
                FmtDeviceCards.device_id == device_obj.id,
                FmtDeviceCards.slot_index != 0,
                FmtDeviceCards.ports_data != "{}")

            if filter_card == "OEO":
                fmt_device_cards_objs = fmt_device_cards_objs.filter(or_(
                    FmtDeviceCards.model.like('%OEO%'),
                    FmtDeviceCards.type.like('%OEO%')
                ))
            elif filter_card == "EDFA":
                fmt_device_cards_objs = fmt_device_cards_objs.filter(or_(
                    FmtDeviceCards.model.like('%EDFA%'),
                    FmtDeviceCards.model.like('%FMTPA-Array%'),
                    FmtDeviceCards.model.like('%FMTBA-Array%'),
                    FmtDeviceCards.model.like('%HPA%'),
                    FmtDeviceCards.model.like('%SOA%'),

                ))
        ret = [
            {obj.card_id: f"{obj.model or obj.type}-1-{obj.slot_index}"}
            for obj in fmt_device_cards_objs
            if fmt_device_cards_objs
        ]
    except Exception as e:
        fmt_cards_ret["errorCode"] = 500
        fmt_cards_ret["errorMsg"] = f"Error: {str(e)}"
    else:
        fmt_cards_ret["errorCode"] = 200
        fmt_cards_ret["data"] = ret
    finally:
        return jsonify(fmt_cards_ret)


@fmt_module.route("/get_fmt_device_port", methods=["POST"])
@admin_permission.require(http_exception=403)
def get_fmt_device_port():
    ports_ret = {"data": {}, "errorMsg": ""}
    try:
        data = request.get_json()
        card_id = data["id"]
        tabType = data["type"]

        with invent_db.get_session() as db_session:
            fmt_device_cards_obj = db_session.query(FmtDeviceCards).filter(FmtDeviceCards.card_id == card_id).first()

        if not fmt_device_cards_obj:
            raise ValueError("Card is not exist!")

        try:
            ports_info = json.loads(fmt_device_cards_obj.ports_data)
        except json.JSONDecodeError:
            raise ValueError("Failed to parse ports data")

        if fmt_device_cards_obj.type == "EDFA":
            port_1 = ports_info.get("1", {})
            port_2 = ports_info.get("2", {})

            merged_port = {
                "No": port_1.get("No", ""),
                "Name": f"{'BA' if 'BA' in fmt_device_cards_obj.model else 'PA'}",
                "EDFA Gain Value": port_1.get("EDFA Gain Value", ""),
                "Expected EDFA Gain": port_1.get("Expected EDFA Gain", ""),
                "VOA Attenuation Value": port_1.get("VOA Attenuation Value", ""),
                "VOA Attenuation Expected": port_1.get("VOA Attenuation Expected", ""),
                "Gain Slope": port_1.get("Gain Slope", ""),
                "Input Optical Power": port_1.get("Input Optical Power", ""),
                "Output Optical Power": port_2.get("Output Optical Power", ""),
                "Input Warning Threshold": port_1.get("Input Warning Threshold", ""),
                "Output Warning Threshold": port_2.get("Output Warning Threshold", ""),
                "Port Note": None
            }

            ports_info = {"1": merged_port}
            fmt_device_cards_obj.ports_data = json.dumps(ports_info)

        ports_name = [v["Name"] for k, v in ports_info.items()]
        ports_ret["data"]["port_name"] = ports_name
        ports_ret["data"]["info"] = fmt_device_cards_obj.make_dict()
        ports_ret["data"]["ports_info"] = {"ports_data": fmt_device_cards_obj.ports_data}

    except Exception as e:
        ports_ret["errorCode"] = 500
        ports_ret["errorMsg"] = f"Error: {str(e)}"
    else:
        ports_ret["errorCode"] = 200
    finally:
        return jsonify(ports_ret)


@fmt_module.route("/get_fmt_device_single_port", methods=["POST"])
@admin_permission.require(http_exception=403)
def get_fmt_device_single_port():
    single_ports_ret = {"data": {}, "errorMsg": ""}
    try:
        data = request.get_json()
        port_name = data["portName"]
        card_id = data["cardID"]
        with invent_db.get_session() as db_session:
            fmt_device_cards_obj = db_session.query(FmtDeviceCards).filter(FmtDeviceCards.card_id == card_id).first()
        if not fmt_device_cards_obj:
            raise ValueError("Card not existed!")
        try:
            ports_info = json.loads(fmt_device_cards_obj.ports_data)
        except json.JSONDecodeError:
            raise ValueError("Failed to parse ports data")
        new_ports_info = {}
        if fmt_device_cards_obj.type == "EDFA":
            port_1 = ports_info.get("1", {})
            port_2 = ports_info.get("2", {})

            merged_port = {
                "No": port_1.get("No", ""),
                "Name": f"{'BA' if 'BA' in fmt_device_cards_obj.model else 'PA'}",
                "EDFA Gain Value": port_1.get("EDFA Gain Value", ""),
                "Expected EDFA Gain": port_1.get("Expected EDFA Gain", ""),
                "VOA Attenuation Value": port_1.get("VOA Attenuation Value", ""),
                "VOA Attenuation Expected": port_1.get("VOA Attenuation Expected", ""),
                "Gain Slope": port_1.get("Gain Slope", ""),
                "Input Optical Power": port_1.get("Input Optical Power", ""),
                "Output Optical Power": port_2.get("Output Optical Power", ""),
                "Input Warning Threshold": port_1.get("Input Warning Threshold", ""),
                "Output Warning Threshold": port_2.get("Output Warning Threshold", ""),
                "Port Note": None
            }
            new_ports_info = {"1": merged_port}
        else:
            new_ports_info = {k: v for k, v in copy.deepcopy(ports_info).items() if v["Name"] == port_name}
        tmp_ret = fmt_device_cards_obj.make_dict()
        tmp_ret["ports_data"] = json.dumps(new_ports_info)
    except Exception as e:
        single_ports_ret["errorCode"] = 500
        single_ports_ret["errorMsg"] = f"Error: {str(e)}"
    else:
        single_ports_ret["errorCode"] = 200
        single_ports_ret["data"] = tmp_ret
    finally:
        return jsonify(single_ports_ret)


@dcs_module.route("/config/modify", methods=["PUT"])
@admin_permission.require(http_exception=403)
def modify_dcs_port():
    data = request.get_json()
    ip = data.get("ip", "")
    slotIndex = data.get("slotIndex", -1)
    cardId = data.get("cardId")
    key = data.get("key")
    value = data.get("value")

    print(f"parameters key:{key}")
    if ip is None or (slotIndex is None and cardId is None) or key is None or value is None:
        return Response(
            json.dumps({"data": "", "errorCode": 1, "errorMsg": "Ip, slotIndex, key and value is required!"}),
            mimetype="application/json")
    db_session = invent_db.get_session()
    card = db_session.query(DcsDeviceCards).join(OtnDeviceBasic,
                                                 OtnDeviceBasic.id == DcsDeviceCards.device_id).filter(
        and_(OtnDeviceBasic.ip == ip,
             or_(DcsDeviceCards.slot_index == slotIndex, DcsDeviceCards.card_id == cardId))).first()
    if not card:
        return Response(json.dumps({"data": "", "errorCode": 1, "errorMsg": "Device is not exist!"}),
                        mimetype="application/json")

    slotIndex = str(card.slot_index)
    data, errorCode, errorMsg = fmt_util.modify_config(ip, slotIndex, key, value, "D6000")
    result = {"data": data, "errorCode": errorCode, "errorMsg": errorMsg}
    return Response(json.dumps(result), mimetype="application/json")


@dcs_module.route("/get_dcs_device_card", methods=["POST"])
@admin_permission.require(http_exception=403)
def get_dcs_device_card():
    dcs_cards_ret = {"data": [], "errorMsg": ""}
    try:
        data = request.get_json()
        ip = data["ip"] if isinstance(data["ip"], str) else data["ip"].get("ip")
        filter_card = data["filterCard"]
        with invent_db.get_session() as db_session:
            device_obj = db_session.query(OtnDeviceBasic).filter(OtnDeviceBasic.ip == ip).first()
            if not device_obj:
                raise ValueError("Device is not exist!")

            dcs_device_cards_objs = db_session.query(DcsDeviceCards).filter(
                DcsDeviceCards.device_id == device_obj.id,
                DcsDeviceCards.slot_index != 0,
                DcsDeviceCards.ports_data != "{}")

            dcs_device_cards_objs = dcs_device_cards_objs.filter(or_(
                DcsDeviceCards.slot_index.in_([1, 2, 3, 4])
            ))

        ret = [
            {obj.card_id: f"{obj.type}-1-{obj.slot_index}"}
            for obj in dcs_device_cards_objs
            if dcs_device_cards_objs
        ]
    except Exception as e:
        dcs_cards_ret["errorCode"] = 500
        dcs_cards_ret["errorMsg"] = f"Error: {str(e)}"
    else:
        dcs_cards_ret["errorCode"] = 200
        dcs_cards_ret["data"] = ret
    finally:
        return jsonify(dcs_cards_ret)


@dcs_module.route("/get_dcs_device_port", methods=["POST"])
@admin_permission.require(http_exception=403)
def get_dcs_device_port():
    ports_ret = {"data": {}, "errorMsg": ""}
    try:
        data = request.get_json()
        card_id = data["id"]
        tabType = data["type"]

        with invent_db.get_session() as db_session:
            dcs_device_cards_obj = db_session.query(DcsDeviceCards).filter(DcsDeviceCards.card_id == card_id).first()

        if not dcs_device_cards_obj:
            raise ValueError("Card is not exist!")

        try:
            ports_info = json.loads(dcs_device_cards_obj.ports_data)
        except json.JSONDecodeError:
            raise ValueError("Failed to parse ports data")

        ports_name = [v["name"] for k, v in ports_info.items() if v.get("name") is not None]
        ports_ret["data"]["port_name"] = ports_name
        ports_ret["data"]["info"] = dcs_device_cards_obj.make_dict()
        ports_ret["data"]["ports_info"] = {"ports_data": dcs_device_cards_obj.ports_data}

    except Exception as e:
        ports_ret["errorCode"] = 500
        ports_ret["errorMsg"] = f"Error: {str(e)}"
    else:
        ports_ret["errorCode"] = 200
    finally:
        return jsonify(ports_ret)


@dcs_module.route("/config/query", methods=["GET"])
@admin_permission.require(http_exception=403)
def get_dcs_config():
    ip = request.args.get('ip')
    slotIndex = request.args.get('slotIndex')
    cardId = request.args.get('cardId')
    if ip is None or (slotIndex is None and cardId is None):
        return Response(json.dumps({"data": "", "errorCode": 1,
                                    "errorMsg": "Ip and at least one of slotIndex and cardId is required!"}),
                        mimetype="application/json")
    if slotIndex is None:
        slotIndex = -1
    else:
        slotIndex = int(slotIndex)
    print(f"slotIndex={slotIndex} , type={type(slotIndex)}")
    if cardId is None:
        cardId = ""
    db_session = invent_db.get_session()
    card = db_session.query(DcsDeviceCards).join(OtnDeviceBasic,
                                                 OtnDeviceBasic.id == DcsDeviceCards.device_id).filter(
        and_(OtnDeviceBasic.ip == ip,
             or_(DcsDeviceCards.slot_index == slotIndex, DcsDeviceCards.card_id == cardId))).first()
    if not card:
        return Response(json.dumps({"data": "", "errorCode": 1, "errorMsg": "Device is not exist!"}),
                        mimetype="application/json")
    slotIndex = card.slot_index
    data, errorCode, errorMsg = fmt_util.get_config(ip, slotIndex)
    result = {"data": data, "errorCode": errorCode, "errorMsg": errorMsg}
    return Response(json.dumps(result), mimetype="application/json")


@dcs_module.route("/get_topology", methods=["POST", "GET"])
@admin_permission.require(http_exception=403)
def get_topology():
    target_ip = request.args.get("ip") or (request.json and request.json.get("ip"))
    if not target_ip:
        return {"error": "IP address is required"}, 400

    result = {"lldp_info": {}, "connectionInfo": []}
    lldp_data = None

    redis_key = f"dcs_lldp_info:{target_ip}"
    try:
        redis_data = redis_client.get(redis_key)
        if redis_data:
            lldp_data = json.loads(redis_data)
            ports_with_data = [p for p, info in lldp_data.items() if info]

            if len(ports_with_data) < 2 and "Port5" not in ports_with_data:
                new_data = fmt_util.get_dcs_lldp_info(target_ip)
                if new_data and len([p for p, info in new_data.items() if info]) > len(ports_with_data):
                    lldp_data = new_data
                    redis_client.set(redis_key, json.dumps(new_data))
    except json.JSONDecodeError:
        return {"error": "Invalid LLDP data format"}, 500

    if not lldp_data:
        try:
            lldp_data = fmt_util.get_dcs_lldp_info(target_ip)
            if lldp_data:
                redis_client.set(redis_key, json.dumps(lldp_data))
            else:
                return {"error": "No LLDP data for the specified IP was found."}, 404
        except Exception:
            return {"error": "failed to synchronize LLDP data"}, 500

    for port, port_info in lldp_data.items():
        if port.startswith('Port') and port_info:
            result["lldp_info"][port] = port_info
            neighbor_ip = port_info.get('PortSysIP') or port_info.get('SysIP') or port_info.get('PortIP')
            if neighbor_ip:
                result["connectionInfo"].append({"sourceIP": target_ip, "destIP": neighbor_ip})

    return result, 200


@dcs_module.route('/info/store_connection_info', methods=['POST'])
@admin_permission.require(http_exception=403)
def store_topo_info():
    try:
        data_str = request.data.decode('utf-8')
        if not data_str:
            return jsonify({'message': 'No data provided'}), 200
        data = json.loads(data_str)
        filtered_data = [item for item in data if item and all(item.values())]
        if not filtered_data:
            return jsonify({'message': 'All data entries are empty'}), 200
        existing_data = redis_client.lrange('connection_info', 0, -1)
        existing_data_objs = [json.loads(item) for item in existing_data]
        existing_pairs = {(item['source'], item['target']) for item in existing_data_objs}
        new_data = []
        for item in filtered_data:
            if (item['source'], item['target']) not in existing_pairs:
                new_data.append(item)
                existing_pairs.add((item['source'], item['target']))
        if new_data:
            serialized_new_data = [json.dumps(item) for item in new_data]
            redis_client.rpush('connection_info', *serialized_new_data)
            redis_client.expire('connection_info', 300)
            return jsonify({'message': 'Data stored successfully'}), 200
        else:
            return jsonify({'message': 'No new data to store'}), 200

    except json.JSONDecodeError:
        return jsonify({'message': 'Invalid JSON format'}), 400
    except Exception as e:
        print(f'Error occurred: {e}')
        return jsonify({'message': 'Internal server error'}), 500

@dcs_module.route('/info/get_connection_info', methods=['GET'])
@admin_permission.require(http_exception=403)
def get_topo_info():
    try:
        connection_list = redis_client.lrange('connection_info', 0, -1)
        topo_info_list = [json.loads(item) for item in connection_list]

        return jsonify({'connection_info': topo_info_list}), 200

    except Exception as e:
        print(f'Error occurred: {e}')
        return jsonify({'message': 'Internal server error'}), 500

@dcs_module.route("/get_dcs_lldp_direct", methods=["GET"])
@admin_permission.require(http_exception=403)
def get_dcs_lldp_direct():
    target_ip = request.args.get("ip")
    if not target_ip:
        return jsonify({"error": "IP address is required", "errorCode": 1, "errorMsg": "IP address is required"}), 400

    try:
        lldp_data = fmt_util.get_dcs_lldp_info(target_ip, "D6000")
        
        if not lldp_data:
            return jsonify({"data": None, "errorCode": 1, "errorMsg": "No LLDP data available"}), 404
        
        return jsonify({"data": lldp_data, "errorCode": 0, "errorMsg": ""}), 200
    
    except Exception as e:
        LOG.error(f"Error fetching LLDP data: {str(e)}")
        return jsonify({"data": None, "errorCode": 1, "errorMsg": f"Failed to fetch LLDP data: {str(e)}"}), 500

@dcs_module.route("/get_dcs_local_info", methods=["GET"])
@admin_permission.require(http_exception=403)
def get_dcs_local_info():
    target_ip = request.args.get("ip")
    if not target_ip:
        return jsonify({"error": "IP address is required", "errorCode": 1, "errorMsg": "IP address is required"}), 400

    try:
        local_data = fmt_util.get_dcs_local_info(target_ip, "D6000")
        
        if not local_data:
            return jsonify({"data": None, "errorCode": 1, "errorMsg": "No local data available"}), 404
        
        return jsonify({"data": local_data, "errorCode": 0, "errorMsg": ""}), 200
    
    except Exception as e:
        LOG.error(f"Error fetching local data: {str(e)}")
        return jsonify({"data": None, "errorCode": 1, "errorMsg": f"Failed to fetch local data: {str(e)}"}), 500