import {request} from "@/utils/common/request";

const baseURL = "/ampcon";
const otnUrl = `${baseURL}/otn`;

export function getOTNDeviceList() {
    return request({
        url: `${otnUrl}/action/query`,
        method: "GET"
    });
}

export function addOTNDevice(group, name, model, ip) {
    return request({
        url: `${otnUrl}/action/add`,
        method: "POST",
        data: {
            group,
            name,
            model,
            ip
        }
    });
}

export function delOTNDevice(ip) {
    return request({
        url: `${otnUrl}/action/delete`,
        method: "DELETE",
        params: {
            ip
        }
    });
}

export function editOTNInfo(ip, name) {
    return request({
        url: `${otnUrl}/action/modify`,
        method: "PUT",
        params: {
            ip,
            name
        }
    });
}

export function setOTNTreeLocation(data) {
    return request({
        url: `${otnUrl}/map/set_otn_location`,
        method: "POST",
        data
    });
}

export function setOTNTreeGroup(data) {
    return request({
        url: `${otnUrl}/map/set_otn_tree_group_id`,
        method: "POST",
        data
    });
}

// Tag: All OEO EDFA
export function getOTNDeviceIP(filterCard, {layer}) {
    return request({
        url: `${otnUrl}/get_otn_device_ips`,
        method: "GET",
        params: {
            filterCard,
            layer
        }
    });
}

export function getAmpconOTNCardList() {
    return request({
        url: `${otnUrl}/get_card_list`,
        method: "GET"
    });
}

export function modifyDeviceNote(data) {
    return request({
        url: `${otnUrl}/config/set_device_note`,
        method: "POST",
        data
    });
}

export function getFilterOTNEventAPI(NeIp, page, pageSize, filterFields = [], sortFields = [], searchFields = {}) {
    return request({
        url: `${otnUrl}/get_filter_otn_event`,
        method: "POST",
        data: {
            NeIp,
            page,
            pageSize,
            filterFields,
            sortFields,
            searchFields
        }
    });
}

export function checkNePassword({host, username, password, port}) {
    return request({
        url: `${otnUrl}/check_ne_password`,
        method: "POST",
        data: {
            host,
            username,
            password,
            port
        }
    });
}

export function storeMaskInfo(ip, username, password) {
    return request({
        url: `${otnUrl}/store_mask_info`,
        method: "GET",
        params: {
            ip,
            username,
            password
        }
    });
}

export function getMaskInfo(ip) {
    return request({
        url: `${otnUrl}/get`,
        method: "GET",
        params: {
            ip
        }
    });
}
