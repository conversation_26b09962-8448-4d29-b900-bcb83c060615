import {useEffect, useState, forwardRef, useImper<PERSON><PERSON><PERSON><PERSON>, useCallback, useRef, useMemo} from "react";
import {useForm} from "antd/es/form/Form";
import {
    DatePicker,
    Divider,
    Button,
    Form,
    Checkbox,
    Row,
    Col,
    Card,
    message,
    Select,
    TreeSelect,
    Tooltip,
    Empty
} from "antd";
import EmptyPic from "@/assets/images/App/empty.png";
import Icon from "@ant-design/icons/lib/components/Icon";
import {AmpConCustomModalForm} from "@/modules-ampcon/components/custom_table";
import {fetchNICsTopK, fetchNICsInfo, getNICsHistoryInfo} from "@/modules-ampcon/apis/monitor_api";
import {CustomLineChart} from "@/modules-ampcon/components/echarts_common";
import filterSvg from "./resource/filter.svg?react";
import settingGreySvg from "../../../Topo/resource/site_grey.svg?react";
import settingGreenSvg from "../../../Topo/resource/site_green.svg?react";
import shrinkSvg from "./resource/shrink.svg?react";
import unfoldSvg from "./resource/unfold.svg?react";
import shrinkHoverSvg from "./resource/shrink_hover.svg?react";
import unfoldHoverSvg from "./resource/unfold_hover.svg?react";

import styles from "./nic_monitoring.module.scss";

const {RangePicker} = DatePicker;
const {Option} = Select;

const interfaceCheckboxOptions = {
    node_network_receive_bytes_total: "In-Octets",
    node_network_receive_packets_total: "In-Pkts",
    node_network_receive_drop_total: "In-Discards",
    node_network_receive_errs_total: "In-Errors",
    node_network_transmit_bytes_total: "Out-Octets",
    node_network_transmit_packets_total: "Out-Pkts",
    node_network_transmit_drop_total: "Out-Discards",
    node_network_transmit_errs_total: "Out-Errors"
};

const formatNumber = value => (value > 1e8 ? value.toExponential(3) : value || 0);

const NICsMonitoring = () => {
    const [form] = useForm();
    const [timeRange, setTimeRange] = useState(["", ""]);
    const [counters, setCounters] = useState({
        interface: Object.keys(interfaceCheckboxOptions)
    });
    const [isSelectCountersModalOpen, setSelectCountersModalOpen] = useState(false);
    const [isHovered, setIsHovered] = useState(false);
    const [selectedHost, setSelectedHost] = useState([]);
    const [nicsArray, setNicsArray] = useState([]);

    const monitoringCardRefs = useRef([]);
    const monitoringSettingRef = useRef();

    const refeshCheckedNodes = () => {
        const checkedInfo = monitoringSettingRef.current?.getCheckedKeys();
        setSelectedHost(checkedInfo);
        monitoringCardRefs.current.forEach(ref => ref?.updateCheckedNodes(checkedInfo));
    };

    const onChangeCounters = () => {
        if (form.getFieldsValue().interface !== undefined) {
            setCounters(prevCounters => ({
                ...prevCounters,
                interface: form.getFieldsValue().interface
            }));
        }
        refeshCheckedNodes();
        setSelectCountersModalOpen(false);
    };

    useEffect(() => {
        // 在组件挂载时设置默认值
        form.setFieldsValue(counters);
        getNicsArray(timeRange);
    }, []);

    const getNicsArray = async dateString => {
        const response = await getNICsHistoryInfo(dateString[0], dateString[1]);
        if (response.status !== 200) {
            message.error(response.info);
            return;
        }

        const treeData = response.data.map(server => ({
            title: `${server.hostname}( ${server.ip} )`,
            value: server.ip,
            children: server.children.map(child => ({
                title: child,
                value: `${child}_${server.ip}`
            }))
        }));
        setNicsArray(treeData);
    };

    return (
        <>
            <div>
                <div style={{display: "flex", justifyContent: "space-between"}}>
                    <div style={{fontSize: "21px", fontWeight: "bold"}}>Monitoring</div>
                    <div>
                        Time
                        <RangePicker
                            showTime={{format: "HH:mm"}}
                            format="YYYY-MM-DD HH:mm"
                            style={{height: "32px", marginLeft: "32px"}}
                            // onFocus={handleFocus}
                            onChange={(_, dateString) => {
                                setTimeRange(dateString);
                                getNicsArray(dateString);
                            }}
                            disabledDate={current => {
                                const now = new Date();
                                const oneMonthAgo = new Date();
                                oneMonthAgo.setMonth(now.getMonth() - 1);
                                return current && (current > now || current < oneMonthAgo);
                            }}
                        />
                        <Divider type="vertical" style={{height: "30px", marginLeft: "16px", marginRight: "16px"}} />
                        <Tooltip title="All Counters" placement="right">
                            <Button
                                style={{borderColor: isHovered ? "#34DCCF" : "#d9d9d9"}}
                                icon={<Icon component={isHovered ? settingGreenSvg : settingGreySvg} />}
                                onClick={() => {
                                    setSelectCountersModalOpen(true);
                                }}
                                onMouseEnter={() => {
                                    setIsHovered(true);
                                }}
                                onMouseLeave={() => {
                                    setIsHovered(false);
                                }}
                            />
                        </Tooltip>
                        {/* <Button
                            icon={<Icon component={settingGreenSvg} />}
                            onClick={() => setSelectCountersModalOpen(true)}
                        /> */}
                    </div>
                </div>
            </div>
            <div style={{height: "100%", width: "100%", marginTop: "18px", marginBottom: "18px"}}>
                <Row gutter={[24, 24]}>
                    {counters.interface.length > 0 &&
                        counters.interface.map((interfaceItem, index) => (
                            <Col key={index} span={24} xxl={12} style={{display: "flex", justifyContent: "center"}}>
                                <MonitoringCard
                                    ref={el => {
                                        monitoringCardRefs.current[index] = el;
                                    }}
                                    name={interfaceItem}
                                    type="interface"
                                    timeRange={timeRange}
                                    selectedHost={selectedHost}
                                    nicsArray={nicsArray}
                                />
                            </Col>
                        ))}
                </Row>
            </div>
            <MonitoringSetting
                form={form}
                ref={monitoringSettingRef}
                isModalOpen={isSelectCountersModalOpen}
                onCancel={() => {
                    form.setFieldsValue(counters);
                    setSelectCountersModalOpen(false);
                    monitoringSettingRef.current?.resetCheckedKeys();
                }}
                onChange={onChangeCounters}
                timeRange={timeRange}
                nicsArray={nicsArray}
            />
        </>
    );
};

export const MonitoringCard = forwardRef(
    ({name, type, timeRange, cardstyle, selectedHost, nicsArray, target = ""}, ref) => {
        const [chartData, setChartData] = useState([]);
        const [xAxisData, setXAxisData] = useState([]);
        const [topK, setTopK] = useState(5);
        const [selectedServerHost, setSelectedServerHost] = useState([]);
        const [hoveredIcons, setHoveredIcons] = useState({});
        const [filter, setFilter] = useState([]);
        const [useTopN, setUseTopN] = useState(false);
        const [xAxisInterval, setXAxisInterval] = useState(1);
        const [isTreeDataLoaded, setIsTreeDataLoaded] = useState(false);

        const onSelectChange = value => {
            if (value.length === 0) {
                setSelectedServerHost([]);
                setFilter([]);
                setUseTopN(false);
                setTopK(5);
                return;
            }

            const newSelectedServers = {};

            value.forEach(val => {
                const selectedNode = nicsArray.find(
                    node => node.value === val || (node.children && node.children.some(child => child.value === val))
                );
                if (selectedNode) {
                    const parentValue = selectedNode.value;
                    if (!newSelectedServers[parentValue]) {
                        newSelectedServers[parentValue] = [];
                    }
                    newSelectedServers[parentValue].push(val.split("_")[0]);
                }
            });

            setSelectedServerHost(value);
            setFilter(newSelectedServers);
        };

        const fetchData = useCallback(async () => {
            const finalTopK = useTopN && selectedServerHost.length > 0 ? selectedServerHost.length : topK;

            let response;
            if (type === "interface") {
                response = await fetchNICsTopK(name, finalTopK, target, timeRange[0], timeRange[1], filter);
            } else {
                response = await fetchNICsTopK(name, finalTopK, target, timeRange[0], timeRange[1], filter);
            }

            if (response.status !== 200) {
                message.error(response.info);
                setChartData([]);
                setXAxisData([]);
            } else if (response.data.length > 0) {
                const series = response.data.map(item => {
                    const name = item.hostname ? `${item.hostname}:${item.device}` : `${item.instance}:${item.device}`;
                    return {
                        name,
                        data: item.values.map(([x, y]) => [x, y])
                    };
                });
                setChartData(series);

                const xAxisData = Array.from(
                    new Set(response.data.flatMap(item => item.values.map(([x]) => x)))
                ).sort();

                if (timeRange[0] && timeRange[1]) {
                    const totalPoints = xAxisData.length;
                    const interval = Math.floor(totalPoints / 5);
                    setXAxisInterval(interval);
                } else {
                    setXAxisInterval(1);
                }

                setXAxisData(xAxisData);
            } else {
                setChartData([]);
                setXAxisData([]);
            }
        }, [name, timeRange, topK, useTopN, target, filter]);

        const option = useMemo(
            () => ({
                tooltip: {
                    trigger: "axis",
                    formatter: params => {
                        const sortedParams = params.sort((a, b) => b.value[1] - a.value[1]);

                        let content = `
                <div style="width: 100%; margin: 0; padding: 0;">
                    <div style="background-color: #F8FAFB; width: calc(100% + 22px); padding: 5px;padding-left:14px; margin: -11px -12px 10px -11px;border-bottom: 1px solid #F2F2F2;">
                        <div style="font-size: 16px;front-weight: 600 ; color: #212519">${params[0].name}</div>
                    </div>
            `;
                        sortedParams.forEach(item => {
                            content += `
                        <div style="display: flex; justify-content: space-between; align-items: center;">
                          <div style="display: flex; align-items: center;">
                            <span style="display:inline-block;margin-right:5px;border-radius:1px;width:12px;height:12px;background-color:${item.color};"></span>
                            <span style="front-weight: 400; font-size: 14px; color: #929A9E; margin-letf: 2px;">${item.seriesName}</span>
                          </div>
                          <span style="margin-left: 20px;front-weight: 400; font-size: 14px; color: #212519">${item.value[1]}</span>
                        </div>
                    `;
                        });

                        content += `</div>`;
                        return content;
                    },
                    position(pos, params, el, elRect, size) {
                        const obj = {};
                        const [x, y] = pos;
                        const tooltipWidth = el.getBoundingClientRect().width;
                        const parentRect = el.parentElement.getBoundingClientRect();
                        const rightSpace = parentRect.width - x;
                        if (y > window.innerHeight / 2) {
                            obj.bottom = "30px";
                            delete obj.top;
                        }
                        if (rightSpace < x - 10 - tooltipWidth) {
                            obj.left = `${x - tooltipWidth - 10}px`;
                        } else {
                            obj.left = `${x + 10}px`;
                        }

                        return obj;
                    }
                },
                legend: {
                    data: chartData.map(item => item.name),
                    orient: "horizontal", // 设置图例的方向为水平
                    top: "90%", // 设置图例的垂直位置
                    left: "center", // 设置图例的水平位置
                    right: "5%",
                    textStyle: {
                        // 图例文字样式
                        fontSize: 15
                    },
                    itemWidth: 10, // 图例图形的宽度
                    itemHeight: 10, // 图例图形的高度
                    type: "scroll",
                    itemGap: 30,
                    pageIconColor: "#A2ACB2", // 默认可点击色值
                    pageIconInactiveColor: "#E3E5EB", // 不可点击色值
                    width: "95%",
                    icon: "rect"
                },
                grid: {
                    left: "3%",
                    right: "3%",
                    top: "5%",
                    bottom: "10%",
                    containLabel: true,
                    width: "95%",
                    height: "75%"
                },
                xAxis: {
                    type: "category",
                    data: xAxisData,
                    axisLabel: {
                        interval: xAxisInterval,
                        formatter(value) {
                            const date = new Date(value);
                            const startDate = new Date(timeRange[0] || Date.now() - 5 * 60 * 1000);
                            const endDate = new Date(timeRange[1] || Date.now());
                            const hour = date.getHours().toString().padStart(2, "0");
                            const minute = date.getMinutes().toString().padStart(2, "0");
                            const second = date.getSeconds().toString().padStart(2, "0");
                            if (
                                startDate.getMonth() !== endDate.getMonth() ||
                                startDate.getDate() !== endDate.getDate()
                            ) {
                                return `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, "0")}-${date.getDate().toString().padStart(2, "0")} ${hour}:${minute}`;
                            }
                            return `${hour}:${minute}:${second}`;
                        }
                    },
                    splitLine: {
                        show: true
                    }
                },
                yAxis: {
                    type: "value",
                    axisLabel: {
                        formatter(value) {
                            if (value > 1e9) {
                                return `${value.toExponential(2)}`;
                            }
                            if (value >= 1000000) {
                                return `${value / 1000000}M`;
                            }
                            if (value >= 1000) {
                                return `${value / 1000}k`;
                            }
                            return value;
                        }
                    }
                },
                series: chartData.map(item => ({
                    name: item.name,
                    type: "line",
                    data: item.data,
                    symbol: "none"
                })),
                width: "100%",
                height: "180px"
            }),
            [chartData]
        );

        useImperativeHandle(ref, () => ({
            refreshMonitoring: () => {
                fetchData(selectedServerHost);
            },
            updateCheckedNodes: value => {
                handleSelectedHost(value);
            }
        }));

        const handleSelectedHost = value => {
            if (value.length === 0) {
                setSelectedServerHost([]);
                setFilter([]);
                setIsTreeDataLoaded(true);
                return;
            }

            const newSelectedServers = {};
            value.forEach(val => {
                const selectedNode = nicsArray.find(
                    node => node.value === val || (node.children && node.children.some(child => child.value === val))
                );
                if (selectedNode) {
                    const parentValue = selectedNode.value;
                    if (!newSelectedServers[parentValue]) {
                        newSelectedServers[parentValue] = [];
                    }
                    newSelectedServers[parentValue].push(val.split("_")[0]);
                }
            });

            setSelectedServerHost(value);
            setFilter(newSelectedServers);
            setIsTreeDataLoaded(true);
        };

        useEffect(() => {
            if (isTreeDataLoaded) {
                fetchData();
            }
        }, [name, timeRange, topK, useTopN, selectedServerHost, filter]);

        useEffect(() => {
            handleSelectedHost(selectedHost);
        }, [selectedHost, nicsArray]);

        const handleMouseEnter = id => {
            setHoveredIcons(prev => ({...prev, [id]: true}));
        };

        const handleMouseLeave = id => {
            setHoveredIcons(prev => ({...prev, [id]: false}));
        };

        const getIconComponent = (expanded, isHovered) => {
            if (expanded) {
                return isHovered ? shrinkHoverSvg : shrinkSvg;
            }
            return isHovered ? unfoldHoverSvg : unfoldSvg;
        };

        const switcherIcon = ({expanded, id}) => {
            const IconComponent = getIconComponent(expanded, hoveredIcons[id]);

            return (
                <IconComponent
                    style={{width: "16px", height: "16px", marginTop: "4px", marginRight: "8px"}}
                    alt={expanded ? "shrink" : "unfold"}
                    onMouseEnter={() => handleMouseEnter(id)}
                    onMouseLeave={() => handleMouseLeave(id)}
                />
            );
        };

        const getSelectValue = () => {
            if (selectedServerHost.length === 0) {
                return `Top ${topK}`;
            }
            if (useTopN) {
                return `Total ${selectedServerHost.length}`;
            }
            return `Top ${topK}`;
        };

        let label;

        if (type === "interface") {
            label = <span>{interfaceCheckboxOptions[name]}</span>;
        }

        return (
            <Card
                title={
                    <div
                        className={styles.monitoring}
                        style={{display: "flex", justifyContent: "space-between", alignItems: "center"}}
                    >
                        {label}
                        <div style={{display: "flex", gap: "10px"}}>
                            {/* TreeSelect for grouped items */}
                            <TreeSelect
                                maxTagCount={2}
                                maxTagTextLength={6}
                                treeData={nicsArray}
                                value={selectedServerHost}
                                onChange={onSelectChange}
                                treeCheckable
                                switcherIcon={({expanded, value}) => switcherIcon({expanded, id: value})}
                                placeholder="Filter"
                                style={{width: 280}}
                                allowClear
                                virtual={false}
                                suffixIcon={<Icon component={filterSvg} />}
                            />
                            {/* Top K Select */}
                            <Select
                                style={{width: 120}}
                                value={getSelectValue()}
                                onChange={value => {
                                    const isTopN = value === `Total ${selectedServerHost.length}`;
                                    setUseTopN(isTopN);
                                    if (!isTopN) {
                                        const num = parseInt(value.split(" ")[1], 10);
                                        setTopK(num);
                                    }
                                }}
                                defaultValue="Top 5"
                            >
                                <Option value="Top 5">Top 5</Option>
                                <Option value="Top 10">Top 10</Option>
                                <Option value="Top 25">Top 25</Option>
                                {selectedServerHost.length > 0 && (
                                    <Option value={`Total ${selectedServerHost.length}`}>
                                        {`Total ${selectedServerHost.length}`}
                                    </Option>
                                )}
                            </Select>
                        </div>
                    </div>
                }
                bordered={false}
                style={{
                    height: "350px",
                    width: "100%",
                    ...(cardstyle ?? {})
                }}
                className="linechart"
            >
                {option.series.length === 0 ? (
                    <div style={{display: "flex", justifyContent: "center", alignItems: "center"}}>
                        <Empty image={EmptyPic} description="No Data" imageStyle={{display: "block", margin: 0}} />
                    </div>
                ) : (
                    <CustomLineChart chartOption={option} />
                )}
            </Card>
        );
    }
);

export const MonitoringSetting = forwardRef(({form, isModalOpen, onCancel, onChange, nicsArray}, ref) => {
    const [checkedKeys, setCheckedKeys] = useState([]);
    const [temptCheckedKeys, setTemptCheckedKeys] = useState([]);
    const [hoveredIcons, setHoveredIcons] = useState({});

    const handleCheck = (value, label, extra) => {
        console.log(value, label, extra);
        setCheckedKeys(value);
        return value;
    };

    useEffect(() => {
        if (isModalOpen) {
            setTemptCheckedKeys(checkedKeys);
        }
    }, [isModalOpen]);

    const handleMouseEnter = id => {
        setHoveredIcons(prev => ({...prev, [id]: true}));
    };

    const handleMouseLeave = id => {
        setHoveredIcons(prev => ({...prev, [id]: false}));
    };

    const getIconComponent = (expanded, isHovered) => {
        if (expanded) {
            return isHovered ? shrinkHoverSvg : shrinkSvg;
        }
        return isHovered ? unfoldHoverSvg : unfoldSvg;
    };

    const switcherIcon = ({expanded, id}) => {
        const IconComponent = getIconComponent(expanded, hoveredIcons[id]);

        return (
            <IconComponent
                style={{width: "16px", height: "16px", marginTop: "4px"}}
                alt={expanded ? "shrink" : "unfold"}
                onMouseEnter={() => handleMouseEnter(id)}
                onMouseLeave={() => handleMouseLeave(id)}
            />
        );
    };

    const formItems = () => {
        return (
            <>
                <div style={{fontSize: "18px", fontWeight: "bold", marginBottom: "10px"}}>Port</div>
                <Form.Item name="interface" label="">
                    <Checkbox.Group>
                        <Row gutter={[16, 16]}>
                            {Object.entries(interfaceCheckboxOptions).map(([value, title]) => (
                                <Col span={8}>
                                    <Checkbox value={value}>{title}</Checkbox>
                                </Col>
                            ))}
                        </Row>
                    </Checkbox.Group>
                </Form.Item>
                <>
                    <div style={{fontSize: "18px", fontWeight: "bold", marginBottom: "10px"}}>Device</div>
                    <Row align="middle">
                        <Col span={5}>Select Device</Col>
                        <Col span={10}>
                            <TreeSelect
                                maxTagCount={2}
                                maxTagTextLength={6}
                                treeData={nicsArray}
                                value={checkedKeys}
                                onChange={handleCheck}
                                treeCheckable
                                switcherIcon={({expanded, value}) => switcherIcon({expanded, id: value})}
                                placeholder="Filter"
                                style={{width: 280}}
                                allowClear
                                suffixIcon={<Icon component={filterSvg} />}
                            />
                        </Col>
                    </Row>
                </>
            </>
        );
    };

    useImperativeHandle(ref, () => ({
        setCheckedKeys: keys => setCheckedKeys(keys),
        getCheckedKeys: () => checkedKeys,
        resetCheckedKeys: () => setCheckedKeys(temptCheckedKeys)
    }));

    return (
        <AmpConCustomModalForm
            title="All Counters"
            isModalOpen={isModalOpen}
            formInstance={form}
            layoutProps={{
                labelCol: {
                    span: 5
                }
            }}
            CustomFormItems={formItems}
            onCancel={onCancel}
            onSubmit={onChange}
            modalClass="ampcon-middle-modal"
        />
    );
});

export default NICsMonitoring;
