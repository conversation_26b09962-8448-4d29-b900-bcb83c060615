    ST-6200-MIB DEFINITIONS ::= BEGIN
 
        IMPORTS
            usmUserName            
                FROM SNMP-USER-BASED-SM-MIB            
            TimeTicks, <PERSON><PERSON><PERSON><PERSON><PERSON>, Integer32, Unsigned32, <PERSON><PERSON><PERSON>32, 
            <PERSON>32, OBJECT-TYPE, MODULE-IDENTITY            
                FROM SNMPv2-SMI            
            DisplayString, <PERSON><PERSON><PERSON><PERSON><PERSON>,RowStatus
                FROM SNMPv2-TC            
            shelfId, slotNo, subSlotNo, portNo, subPortNo            
                FROM ST-COMMON-MIB            
            enterpriseProducts            
                FROM ST-ROOT-MIB;
    

        st6200 MODULE-IDENTITY 
            LAST-UPDATED "201803011832Z"		-- March 01, 2018 at 18:32 GMT
            ORGANIZATION 
                ""
            CONTACT-INFO 
                ""
            DESCRIPTION 
                "STN6200 MIB"
            REVISION "201803011137Z"		-- March 01, 2018 at 11:37 GMT
            DESCRIPTION 
                "ISSUE 6:
                -  Add olpCmdSwitchCounterClear to st6200"
            ::= { enterpriseProducts 21 }

        
    
    
--
-- Node definitions
--
-- 
-- Textual conventions
-- 
       
        olpConfigMIB OBJECT IDENTIFIER ::= { st6200 2 }

        
        olpConfigTable OBJECT-TYPE
            SYNTAX SEQUENCE OF OlpConfigEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "Description."
            ::= { olpConfigMIB 1 }

        
        olpConfigEntry OBJECT-TYPE
            SYNTAX OlpConfigEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "Description."
            INDEX { shelfId, slotNo, subSlotNo }
            ::= { olpConfigTable 1 }

        
        OlpConfigEntry ::=
            SEQUENCE { 
                olpSwitchMode
                    INTEGER,
                olpSwitchState
                    INTEGER,
                olpSwitchHoldoffTime
                    Unsigned32,
                olpRevertiveOfSwitchMode
                    INTEGER,
                olpRSMsht
                    Unsigned32,
                olpAutoBackMode
                    INTEGER,
                olpABsht
                    Unsigned32,
                olpButtonEnabled
                    INTEGER,
                olpConsoleEnable
                    INTEGER,
                olpCardDescription
                    DisplayString,
                olpSwitchCounter
                    Unsigned32,
				olpRemoteState
					INTEGER,
				olpActiveAlmThsOpRx
					Integer32,
				olpActiveAlmThsOpTx
					Integer32,
				olpStandbyAlmThsOpRx
					Integer32,
				olpStandbyAlmThsOpTx
					Integer32,
				olpActiveSwtThsOpRx
					Integer32,
				olpStandbySwtThsOpRx
					Integer32,
				olpAlmThsOpRx
					Integer32,
				olpAlmThsOpTx
					Integer32,
				olpBypassMode
                    INTEGER,				
				olpBypassBackMode
                    INTEGER,
				olpHeartInputMode
                    INTEGER,
				olpHeartState
				    INTEGER,
				olpHeartAlive
                    INTEGER,				
				olpHeartIdle
                    Integer32,
				olpHeartInterval
                    Integer32,
				olpHeartCount
                    Integer32,
				olpAlamTurnEnabled
                    INTEGER,
				olpPowerDiffThs
                    Integer32,
             }

        olpSwitchMode OBJECT-TYPE
            SYNTAX INTEGER
                {
                manual(1),
                auto(2),
                force(3),
                lockL1(4),
                lockL2(5),
                clean(6),
                forceL1(7),
                forceL2(8),
                manualL1(9),
                manualL2(10)
                }
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                "Description."
            DEFVAL { auto }
            ::= { olpConfigEntry 1 }

        
        olpSwitchState OBJECT-TYPE
            SYNTAX INTEGER
                {
                primary(3),
                secondary(12)
                }
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "Description."
            DEFVAL { primary }
            ::= { olpConfigEntry 2 }

        
        olpSwitchHoldoffTime OBJECT-TYPE
            SYNTAX Unsigned32 (0..32000)
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                "Switch holdoff time of PtoS. (Unit: Millisecond)"
            DEFVAL { 0 }
            ::= { olpConfigEntry 3 }

        
        olpRevertiveOfSwitchMode OBJECT-TYPE
            SYNTAX INTEGER
                {
                nonrevertive(0),
                revertive(1)
                }
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                "Revertive switch mode"
            DEFVAL { nonrevertive }
            ::= { olpConfigEntry 4 }

        
        olpRSMsht OBJECT-TYPE
            SYNTAX Unsigned32 (1..32000)
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                "Switch holdoff time for revertive switch mode.(unit: Minute)"
            DEFVAL { 1 }
            ::= { olpConfigEntry 5 }

        
        olpAutoBackMode OBJECT-TYPE
            SYNTAX INTEGER
                {
                notautoback(0),
                autoback(1)
                }
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                "Auto-back mode"
            DEFVAL { autoback }
            ::= { olpConfigEntry 6 }

        
        olpABsht OBJECT-TYPE
            SYNTAX Unsigned32 (0..32000)
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                "Holdoff time of auto-back mode(unit: Second)"
            DEFVAL { 30 }
            ::= { olpConfigEntry 7 }

        
        olpButtonEnabled OBJECT-TYPE
            SYNTAX INTEGER
                {
                disable(0),
                enable(1)
                }
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                "Button enable."
            DEFVAL { enable }
            ::= { olpConfigEntry 8 }

        
        olpConsoleEnable OBJECT-TYPE
            SYNTAX INTEGER
                {
                disable(0),
                enable(1)
                }
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                "Console Enable"
            DEFVAL { enable }
            ::= { olpConfigEntry 9 }

        
        olpCardDescription OBJECT-TYPE
            SYNTAX DisplayString (SIZE (0..32))
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                "Card description"
            DEFVAL { "" }
            ::= { olpConfigEntry 10 }

        
        olpSwitchCounter OBJECT-TYPE
            SYNTAX Unsigned32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "Switch counter"
            DEFVAL { 0 }
            ::= { olpConfigEntry 11 }

        olpRemoteState OBJECT-TYPE
            SYNTAX INTEGER
                {
                unnormal(0),
                normal(1)
                }
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "remote state."
            DEFVAL { normal }
            ::= { olpConfigEntry 12 }
			
		olpActiveAlmThsOpRx OBJECT-TYPE
            SYNTAX Integer32 (-6000..2500)
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                "Active Alarm threshold of optical power"
            DEFVAL { -2000 }
            ::= { olpConfigEntry 13 }
		
		olpActiveAlmThsOpTx OBJECT-TYPE
            SYNTAX Integer32 (-6000..2500)
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                "Active Alarm threshold of optical power"
            DEFVAL { -2000 }
            ::= { olpConfigEntry 14 }
			
		olpStandbyAlmThsOpRx OBJECT-TYPE
            SYNTAX Integer32 (-6000..2500)
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                "Standby Alarm threshold of optical power"
            DEFVAL { -2000 }
            ::= { olpConfigEntry 15 }
			
        olpStandbyAlmThsOpTx OBJECT-TYPE
            SYNTAX Integer32 (-6000..2500)
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                "Standby Alarm threshold of optical power"
            DEFVAL { -2500 }
            ::= { olpConfigEntry 16 }
		
		
        olpActiveSwtThsOpRx OBJECT-TYPE
            SYNTAX Integer32 (-6000..2500)
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                "switch threshold of optical power"
            DEFVAL { -2500 }
            ::= { olpConfigEntry 17 }
			
		olpStandbySwtThsOpRx OBJECT-TYPE
            SYNTAX Integer32 (-6000..2500)
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                "standby switch threshold of optical power"
            DEFVAL { -2500 }
            ::= { olpConfigEntry 18 }
		
		olpAlmThsOpRx OBJECT-TYPE
            SYNTAX Integer32 (-6000..2500)
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                "Alarm threshold of optical power"
            DEFVAL { -2500 }
            ::= { olpConfigEntry 19 }

		olpAlmThsOpTx OBJECT-TYPE
            SYNTAX Integer32 (-6000..2500)
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                "Alarm threshold of optical power"
            DEFVAL { -2500 }
            ::= { olpConfigEntry 20 }
		
		olpBypassMode OBJECT-TYPE
            SYNTAX INTEGER
                {
                r1Andr2(1),
                r1(2),
                r2(3),
				r1Orr2(4)
                }
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                "Description."
            DEFVAL { r1Orr2 }
            ::= { olpConfigEntry 21 }
			
		olpBypassBackMode OBJECT-TYPE
            SYNTAX INTEGER
                {
                r1Andr2(1),
                r1(2),
                r2(3),
				r1Orr2(4)
                }
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                "Description."
            DEFVAL { r1Andr2 }
            ::= { olpConfigEntry 22 }

		olpHeartInputMode OBJECT-TYPE
            SYNTAX INTEGER
                {
                serial(1),
                ethernet(2)
                }
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                "Description."
            DEFVAL { ethernet }
            ::= { olpConfigEntry 23 }
			
		olpHeartState OBJECT-TYPE
            SYNTAX INTEGER
                {
                normal(1),
                notNormal(2),
				untapped(3)
                }
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "Description."
            DEFVAL { normal }
            ::= { olpConfigEntry 24 }	
			
		olpHeartAlive OBJECT-TYPE
            SYNTAX INTEGER
                {
                disable(0),
                enable(1)
                }
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                "Description."
            DEFVAL { disable }
            ::= { olpConfigEntry 25 }
			
		olpHeartIdle OBJECT-TYPE
            SYNTAX Integer32 (1..255)
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                "unit:s"
            DEFVAL { 1 }
            ::= { olpConfigEntry 26 }
			
		olpHeartInterval OBJECT-TYPE
            SYNTAX Integer32 (1..255)
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                "unit:20ms"
            DEFVAL { 1 }
            ::= { olpConfigEntry 27 }
			
		olpHeartCount OBJECT-TYPE
            SYNTAX Integer32 (1..255)
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                "Description"
            DEFVAL { 1 }
            ::= { olpConfigEntry 28 }
			
		olpAlamTurnEnabled OBJECT-TYPE
            SYNTAX INTEGER
                {
                disable(0),
                enable(1)
                }
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                "Description."
            DEFVAL { disable }
            ::= { olpConfigEntry 29 }
			
		olpPowerDiffThs OBJECT-TYPE
            SYNTAX Integer32 (0..2000)
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                "Description"
            DEFVAL { 0 }
            ::= { olpConfigEntry 30 }
			
        olpPortTable OBJECT-TYPE
            SYNTAX SEQUENCE OF OlpPortEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "Description."
            ::= { olpConfigMIB 2 }

        
        olpPortEntry OBJECT-TYPE
            SYNTAX OlpPortEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "Description."
            INDEX { shelfId, slotNo, subSlotNo, portNo, subPortNo
                 }
            ::= { olpPortTable 1 }

        
        OlpPortEntry ::=
            SEQUENCE { 
                olpPortType
                    INTEGER,
                olpPortOpticalPower
                    Integer32,
                olpPortAlmThsOP
                    Integer32,
                olpPortSwtThsOp
                    Integer32,
                olpPortWaveLen
                    INTEGER,
                olpPortDesc
                    DisplayString
             }

        olpPortType OBJECT-TYPE
            SYNTAX INTEGER
                {
                tx(1),
                rx(2),
                t1(3),
                r1(4),
                t2(5),
                r2(6),
                t3(7),
                r3(8),
                t4(9),
                r4(10),
                rx1(11),
                rx2(12),
                r11(13),
                r12(14),
                r21(15),
                r22(16),
                nm1271(17),
                nm1291(18),
                nm1311(19),
                nm1331(20),
                nm1351(21),
                nm1371(22),
                l1(23),
                l2(24),
                nm1471(25),
                nm1491(26),
                nm1511(27),
                nm1531(28),
                nm1551(29),
                nm1571(30),
                nm1391(31),
                nm1411(32),
                nm1431(33),
                nm1451(34),
                nm1591(35),
                nm1611(36)
                }
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "Description."
            ::= { olpPortEntry 1 }

        
        olpPortOpticalPower OBJECT-TYPE
            SYNTAX Integer32 (-10000..2500)
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "Description."
            DEFVAL { -10000 }
            ::= { olpPortEntry 2 }

        
        olpPortAlmThsOP OBJECT-TYPE
            SYNTAX Integer32 (-6000..2500)
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                "Alarm threshold of optical power"
            DEFVAL { -2000 }
            ::= { olpPortEntry 3 }

        
        olpPortSwtThsOp OBJECT-TYPE
            SYNTAX Integer32 (-6000..2500)
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                "switch threshold of optical power"
            DEFVAL { -2500 }
            ::= { olpPortEntry 4 }

        
        olpPortWaveLen OBJECT-TYPE
            SYNTAX INTEGER
                {
                nm1310(0),
                nm1490(1),
                nm1550(2),
                nm1270(3),
                nm1290(4),
                nm1330(5),
                nm1350(6),
                nm1370(7),
                nm1390(8),
                nm1410(9),
                nm1430(10),
                nm1450(11),
                nm1470(12),
                nm1510(13),
                nm1530(14),
                nm1570(15),
                nm1590(16),
                nm1610(17)
                }
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                "Wavelength."
            DEFVAL { nm1550 }
            ::= { olpPortEntry 5 }

        
        olpPortDesc OBJECT-TYPE
            SYNTAX DisplayString (SIZE (0..128))
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                "Description."
            DEFVAL { "" }
            ::= { olpPortEntry 6 }

        
        olpCommandTable OBJECT-TYPE
            SYNTAX SEQUENCE OF OlpCommandEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "Description."
            ::= { olpConfigMIB 3 }

        
        olpCommandEntry OBJECT-TYPE
            SYNTAX OlpCommandEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "Description."
            INDEX { shelfId, slotNo, subSlotNo }
            ::= { olpCommandTable 1 }

        
        OlpCommandEntry ::=
            SEQUENCE { 
                olpCmdSwitch
                    INTEGER,
                olpCmdSwitchCounterClear
                    INTEGER
             }

        olpCmdSwitch OBJECT-TYPE
            SYNTAX INTEGER
                {
                primary(3),
                secondary(12)
                }
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                "Command for P2S or S2P"
            DEFVAL { primary }
            ::= { olpCommandEntry 1 }

        
        olpCmdSwitchCounterClear OBJECT-TYPE
            SYNTAX INTEGER { clear(1) }
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                "Clear switch counter"
            DEFVAL { 1 }
            ::= { olpCommandEntry 2 }
	
			
			
         olpCardTable OBJECT-TYPE
            SYNTAX SEQUENCE OF OlpCardEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "Description."
            ::= { olpConfigMIB 4 }

        
        olpCardEntry OBJECT-TYPE
            SYNTAX OlpCardEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "Description."
            INDEX { shelfId, slotNo, subSlotNo }
            ::= { olpCardTable 1 }

        
        OlpCardEntry ::=
            SEQUENCE { 
		olpCardAddr
		    Integer32,
                olpCardType
                    INTEGER,
                olpCardSN
                    DisplayString,
                olpCardPartN
                    DisplayString,
				olpCardLabel
					DisplayString,
                olpCardHwVersion
                    DisplayString,
                olpCardSwVersion
                    DisplayString,
				olpCardCpldVersion
                    DisplayString,	
                olpCardTemperature
                    Integer32,
				olpCardRowStatus
                    RowStatus,	
             }
		olpCardAddr OBJECT-TYPE
            SYNTAX Integer32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "card slot id."
            ::= { olpCardEntry 1 }
			
		olpCardType OBJECT-TYPE
            SYNTAX INTEGER
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "Description."
            DEFVAL { 0 }
            ::= { olpCardEntry 2 }

        
        olpCardSN OBJECT-TYPE
            SYNTAX DisplayString (SIZE (0..50))
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "serial number"
            DEFVAL { "" }
            ::= { olpCardEntry 3 }

        
        olpCardPartN OBJECT-TYPE
            SYNTAX DisplayString (SIZE (0..50))
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "partnumber"
            DEFVAL { "" }
            ::= { olpCardEntry 4 }
			
		olpCardLabel OBJECT-TYPE
            SYNTAX DisplayString (SIZE (0..128))
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                "card description"
            DEFVAL { "" }
            ::= { olpCardEntry 5 }
        
        olpCardHwVersion OBJECT-TYPE
            SYNTAX DisplayString (SIZE (0..20))
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                "hardware version"
            DEFVAL { "" }
            ::= { olpCardEntry 6 }
			
		olpCardSwVersion OBJECT-TYPE
            SYNTAX DisplayString (SIZE (0..30))
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "SW Version"
            DEFVAL { "" }
            ::= { olpCardEntry 7 }	
			
		olpCardCpldVersion OBJECT-TYPE
            SYNTAX DisplayString (SIZE (0..20))
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "Description."
            DEFVAL { "" }
            ::= { olpCardEntry 8 }
		
		olpCardTemperature OBJECT-TYPE
            SYNTAX Integer32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "Temperature near CPU."
            ::= { olpCardEntry 9 }
			
		olpCardRowStatus OBJECT-TYPE
            SYNTAX RowStatus
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "Description."
            ::= { olpCardEntry 10 }
	 
			 
        oaConfigMIB OBJECT IDENTIFIER ::= { st6200 3 }

        
        oaModuleTable OBJECT-TYPE
            SYNTAX SEQUENCE OF OaModuleEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "ETYn Table"
            ::= { oaConfigMIB 4 }

        
        oaModuleEntry OBJECT-TYPE
            SYNTAX OaModuleEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                " "
            INDEX { shelfId, slotNo, subSlotNo }
            ::= { oaModuleTable 1 }

        
        OaModuleEntry ::=
            SEQUENCE { 
                oaModuleConfiguration
                    DisplayString,
                oaModuleFirmwareVers
                    DisplayString,
                oaModuleSerialNumber
                    DisplayString,
                oaModuleType
                    INTEGER,
                oaModuleFunction
                    INTEGER,
                oaModuleMaxOutPower
                    Integer32,
                oaModuleDefaultGain
                    Integer32,
                oaModuleVariableGain
                    INTEGER,
                oaModuleMinGain
                    Integer32,
                oaModuleMaxGain
                    Integer32,
                oaModuleMinPower
                    Integer32,
                oaModuleMaxPower
                    Integer32,
                oaModuleDcmSupport
                    INTEGER,
                oaModuleDcmValue
                    Integer32,
                oaModuleOscSupport
                    INTEGER,
                oaModuleOscWave
                    INTEGER,
                oaModulePumpNum
                    INTEGER,
                oaModuleWaveNum
                    INTEGER,
                oaModulePumpTotalPower
                    Integer32,
                oaModuleTILT
                    Integer32,
             }

        oaModuleConfiguration OBJECT-TYPE
            SYNTAX DisplayString (SIZE (0..64))
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                " "
            ::= { oaModuleEntry 1 }

        
        oaModuleFirmwareVers OBJECT-TYPE
            SYNTAX DisplayString (SIZE (0..64))
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                " "
            ::= { oaModuleEntry 2 }

        
        oaModuleSerialNumber OBJECT-TYPE
            SYNTAX DisplayString (SIZE (0..64))
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                " "
            ::= { oaModuleEntry 3 }

        
        oaModuleType OBJECT-TYPE
            SYNTAX INTEGER
                {
                singleStage(1),
                multiStage(2),
				twoStageIndependent(3),
				singleC34(4),
				multiCW(5),
				rAAMPLIFICATIOM(6)
                }
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "singleStage (1),multiStage  (2),twoStageIndependent(3),singleC34 (4),multiCW (5),RAAMPLIFICATIOM (6). "
            DEFVAL { 1 }
            ::= { oaModuleEntry 4 }

        
        oaModuleFunction OBJECT-TYPE
            SYNTAX INTEGER
                {
                pa(0),
                la(1),
                ba(2),
                da(3)
                }
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "PA��0��
                LA��1��
                BA��2��
                DA��3��"
            DEFVAL { 0 }
            ::= { oaModuleEntry 5 }

        
        oaModuleMaxOutPower OBJECT-TYPE
            SYNTAX Integer32 (-1000..3000)
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "Get the max output power in dbm."
            DEFVAL { 160 }
            ::= { oaModuleEntry 7 }

        
        oaModuleDefaultGain OBJECT-TYPE
            SYNTAX Integer32 (0..300)
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "Get the default gain in db"
            DEFVAL { 250 }
            ::= { oaModuleEntry 8 }

        
        oaModuleVariableGain OBJECT-TYPE
            SYNTAX INTEGER
                {
                variable(1),
                fixed(2)
                }
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "Get the default gain in db"
            DEFVAL { variable }
            ::= { oaModuleEntry 9 }

        
        oaModuleMinGain OBJECT-TYPE
            SYNTAX Integer32 (0..4000)
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "Get the max output power in dbm."
            DEFVAL { 0 }
            ::= { oaModuleEntry 10 }

        
        oaModuleMaxGain OBJECT-TYPE
            SYNTAX Integer32 (0..4000)
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "Get the max output power in dbm."
            DEFVAL { 350 }
            ::= { oaModuleEntry 11 }

        
        oaModuleMinPower OBJECT-TYPE
            SYNTAX Integer32 (0..4000)
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "Get the max output power in dbm."
            DEFVAL { 0 }
            ::= { oaModuleEntry 12 }

        
        oaModuleMaxPower OBJECT-TYPE
            SYNTAX Integer32 (0..4000)
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "Get the max output power in dbm."
            DEFVAL { 300 }
            ::= { oaModuleEntry 13 }

        
        oaModuleDcmSupport OBJECT-TYPE
            SYNTAX INTEGER
                {
                support(1),
                nonsupport(2)
                }
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "Get the max output power in dbm."
            DEFVAL { support }
            ::= { oaModuleEntry 14 }

        
        oaModuleDcmValue OBJECT-TYPE
            SYNTAX Integer32 (0..300)
            UNITS "0"
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "Get the max output power in dbm."
            ::= { oaModuleEntry 15 }

        
        oaModuleOscSupport OBJECT-TYPE
            SYNTAX INTEGER
                {
                support(1),
                nonsupport(2)
                }
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "Get the max output power in dbm."
            DEFVAL { support }
            ::= { oaModuleEntry 16 }

        
        oaModuleOscWave OBJECT-TYPE
            SYNTAX INTEGER
                {
                nm1490(4),
                nm1510(5),
                nm1625(6)
                }
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "Get the max output power in dbm."
            DEFVAL { nm1510 }
            ::= { oaModuleEntry 17 }
			
		oaModulePumpNum OBJECT-TYPE
            SYNTAX INTEGER
                {
                one(1),
                two(2),
                four(4),
                six(6)
                }
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "show the num of pump."
            DEFVAL { 1 }
            ::= { oaModuleEntry 18 }
			
		oaModuleWaveNum OBJECT-TYPE
            SYNTAX INTEGER
			{
				zero(0),
                one(1),
                two(2),
                three(3),
                four(4)
			}
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "show the num of wave for RA"
            DEFVAL { 0 }
            ::= { oaModuleEntry 19 }
			
        oaModulePumpTotalPower OBJECT-TYPE
            SYNTAX Integer32 (0..100000)
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "Get the Pump Total Power in mv."
            DEFVAL { 0 }
            ::= { oaModuleEntry 20 }
			
        oaModuleTILT OBJECT-TYPE
            SYNTAX Integer32 (-300..300)
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                "Get the Pump tilt in dB."
            DEFVAL { 0 }
            ::= { oaModuleEntry 21 }

        
        oaPumpTable OBJECT-TYPE
            SYNTAX SEQUENCE OF OaPumpEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "Description."
            ::= { oaConfigMIB 5 }

        
        oaPumpEntry OBJECT-TYPE
            SYNTAX OaPumpEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "Description."
            INDEX { shelfId, slotNo, subSlotNo }
            ::= { oaPumpTable 1 }

        
        OaPumpEntry ::=
            SEQUENCE { 
                oaPumpControlMode
                    INTEGER,
                oaPumpModeValue
                    Integer32,
                oaPumpNum
                    INTEGER,
                oaPumpTemperature
                    Integer32,
                oaPumpIld
                    Integer32,
                oaPumpEol
                    Integer32,
                oaPumpTmp
                    Integer32,
                oaPumpItc
                    Integer32,
                oaPumpVtc
                    Integer32,
                oaPumpIsp
                    INTEGER,
                oaPumpIspValue
                    Integer32,
                oaPumpAlarmThrILD
                    Integer32,
                oaPumpAlarmThrTMP
                    Integer32,
                oaPumpAlarmThrMTH
                    Integer32,
                oaPumpAlarmThrMTL
                    Integer32,
                oaPumpVoaSta
                    INTEGER,
                oaPumpVoaSet
                    Integer32,
                oaPumpVoaAct
                    Integer32,
                oaPumpVoaSupport
                    INTEGER,
                oaPumpPop
                    Integer32,
                oaPumpMinPower
                    Integer32,
                oaPumpMaxPower
                    Integer32,
                oaPumpMinGain
                    Integer32,
                oaPumpMaxGain
                    Integer32,
                oaPumpMaxOutPower
                    Integer32,
                oaPumpDefaultGain
                    Integer32,
             }

        oaPumpControlMode OBJECT-TYPE
            SYNTAX INTEGER
                {
                g(1),
                p(2),
                sp(3),
                s(4),
                m(5),
                d(6),
                w(7)
                }
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                "G,Gain control mode
                P,hardware Output power control mode
                SP,software Output power control mode
                S,Stage control mode
                M,Manual pump control
                D,Disable mode
                W,Pump power control mode"
            DEFVAL { 1 }
            ::= { oaPumpEntry 1 }

        
        oaPumpModeValue OBJECT-TYPE
            SYNTAX Integer32 (-400..10000)
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                "Show the gain setpoint in dB,or output power in dbm."
            DEFVAL { 250 }
            ::= { oaPumpEntry 2 }

        
        oaPumpNum OBJECT-TYPE
            SYNTAX INTEGER
                {
                one(1),
                two(2),
                four(4),
                six(6)
                }
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "show the num of pump."
            DEFVAL { 1 }
            ::= { oaPumpEntry 3 }

        
        oaPumpTemperature OBJECT-TYPE
            SYNTAX Integer32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "Displays module case temperature in degrees C."
            ::= { oaPumpEntry 4 }

        
        oaPumpIld OBJECT-TYPE
            SYNTAX Integer32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "Laser diode current in mA."
            DEFVAL { 0 }
            ::= { oaPumpEntry 5 }

        
        oaPumpEol OBJECT-TYPE
            SYNTAX Integer32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "Laser diode end-of-life current in mA."
            DEFVAL { 0 }
            ::= { oaPumpEntry 6 }

        
        oaPumpTmp OBJECT-TYPE
            SYNTAX Integer32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "Pump temperature in degrees C."
            DEFVAL { 0 }
            ::= { oaPumpEntry 7 }

        
        oaPumpItc OBJECT-TYPE
            SYNTAX Integer32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "TEC current in mA."
            DEFVAL { 0 }
            ::= { oaPumpEntry 8 }

        
        oaPumpVtc OBJECT-TYPE
            SYNTAX Integer32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "Voltage of Pump"
            DEFVAL { 0 }
            ::= { oaPumpEntry 9 }

        
        oaPumpIsp OBJECT-TYPE
            SYNTAX INTEGER
                {
                auto(0),
                manual(1)
                }
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                "Description."
            DEFVAL { auto }
            ::= { oaPumpEntry 10 }

        
        oaPumpIspValue OBJECT-TYPE
            SYNTAX Integer32 (-600..600)
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                "PUMP current setpoint in mA."
            DEFVAL { 0 }
            ::= { oaPumpEntry 11 }

        
        oaPumpAlarmThrILD OBJECT-TYPE
            SYNTAX Integer32 (0..100)
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                "Pump overcurrent alarm threshold."
            DEFVAL { 95 }
            ::= { oaPumpEntry 12 }

        
        oaPumpAlarmThrTMP OBJECT-TYPE
            SYNTAX Integer32 (-500..800)
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                "pump(case) temperature alarm threshold."
            DEFVAL { 50 }
            ::= { oaPumpEntry 13 }

        
        oaPumpAlarmThrMTH OBJECT-TYPE
            SYNTAX Integer32 (-500..800)
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                "High module(case) temperature alarm threshold."
            DEFVAL { 650 }
            ::= { oaPumpEntry 14 }

        
        oaPumpAlarmThrMTL OBJECT-TYPE
            SYNTAX Integer32 (-500..800)
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                "Low module(case) temperature alarm threshold."
            DEFVAL { 0 }
            ::= { oaPumpEntry 15 }

        
        oaPumpVoaSta OBJECT-TYPE
            SYNTAX INTEGER
                {
                ok(1),
                err(2),
                pwr(3),
                bsy(4)
                }
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "show attenuation of VOA."
            DEFVAL { ok }
            ::= { oaPumpEntry 16 }

        
        oaPumpVoaSet OBJECT-TYPE
            SYNTAX Integer32 (0..300)
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                "Attenuation setpoint in dB."
            DEFVAL { 0 }
            ::= { oaPumpEntry 17 }

        
        oaPumpVoaAct OBJECT-TYPE
            SYNTAX Integer32 (0..300)
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                " Measured attenuation in dB."
            DEFVAL { 0 }
            ::= { oaPumpEntry 18 }

        
        oaPumpVoaSupport OBJECT-TYPE
            SYNTAX INTEGER
                {
                support(1),
                nonsupport(2)
                }
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "EDFA module support voa or not."
            DEFVAL { support }
            ::= { oaPumpEntry 19 }
            
 		oaPumpPop OBJECT-TYPE
            SYNTAX Integer32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "Laser diode power in mW."
            DEFVAL { 0 }
            ::= { oaPumpEntry 20 }

            
        oaPumpMinPower OBJECT-TYPE
            SYNTAX Integer32(0..4000)
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "OA P oamodevalue minimum range  in dBm,
                RA oamodevalue  minimum range  in mW."
            DEFVAL { 0 }
            ::= { oaPumpEntry 21 }
            
        oaPumpMaxPower OBJECT-TYPE
            SYNTAX Integer32(0..4000)
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "OA P oamodevalue minimum range  in dBm,
                RA W oamodevalue  maximum range  in mW."
            DEFVAL { 350 }
            ::= { oaPumpEntry 22 }
        oaPumpMinGain OBJECT-TYPE
            SYNTAX Integer32(0..4000)
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "OA G oamodevalue minimum range  in dB."
            DEFVAL { 0 }
            ::= { oaPumpEntry 23 }

        oaPumpMaxGain OBJECT-TYPE
            SYNTAX Integer32(0..4000)
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "OA G oamodevalue maximum range  in dB."
            DEFVAL { 350 }
            ::= { oaPumpEntry 24 }

        oaPumpMaxOutPower OBJECT-TYPE
            SYNTAX Integer32 (-1000..3000)
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "Get the max output power in dbm."
            DEFVAL { 160 }
            ::= { oaPumpEntry 25 }

        oaPumpDefaultGain OBJECT-TYPE
            SYNTAX Integer32(0..4000)
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "OA G oamodevalue Default Gain in dB."
            DEFVAL { 250 }
            ::= { oaPumpEntry 26 }

        
        oaPortTable OBJECT-TYPE
            SYNTAX SEQUENCE OF OaPortEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "Description."
            ::= { oaConfigMIB 6 }

        
        oaPortEntry OBJECT-TYPE
            SYNTAX OaPortEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "Description."
            INDEX { shelfId, slotNo, subSlotNo, portNo, subPortNo
                 }
            ::= { oaPortTable 1 }

        
        OaPortEntry ::=
            SEQUENCE { 
                oaPortType
                    INTEGER,
                oaPortOpticalPower
                    Integer32,
                oaPortAlmThsop
                    Integer32,
                oaPortDesc
                    DisplayString
             }

        oaPortType OBJECT-TYPE
            SYNTAX INTEGER
                {
                in1(1),
                out1(2),
                in2(3),
                out2(4),
                in3(5),
                out3(6),
                in4(7),
                out4(8),
                in5(9),
                out5(10),
                in6(11),
                out6(12),
                in7(13),
                out7(14),
                in8(15),
                out8(16),
                in9(17),
                out9(18),
                in10(19),
                out10(20)
                }
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "Description."
            ::= { oaPortEntry 1 }

        
        oaPortOpticalPower OBJECT-TYPE
            SYNTAX Integer32 (-10000..3000)
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "Description."
            DEFVAL { -10000 }
            ::= { oaPortEntry 2 }

        
        oaPortAlmThsop OBJECT-TYPE
            SYNTAX Integer32 (-100000..3000)
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                "Alarm threshold for optical power"
            DEFVAL { -100000 }
            ::= { oaPortEntry 3 }

        
        oaPortDesc OBJECT-TYPE
            SYNTAX DisplayString (SIZE (0..64))
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                "Description."
            ::= { oaPortEntry 4 }

        
        otu10ConfigMIB OBJECT IDENTIFIER ::= { st6200 4 }

        
        otu10PortTable OBJECT-TYPE
            SYNTAX SEQUENCE OF Otu10PortEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "Description."
            ::= { otu10ConfigMIB 1 }

        
        otu10PortEntry OBJECT-TYPE
            SYNTAX Otu10PortEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "Description."
            INDEX { shelfId, slotNo, subSlotNo, portNo, subPortNo
                 }
            ::= { otu10PortTable 1 }

        
        Otu10PortEntry ::=
            SEQUENCE { 
                otu10PortlaserShoutdownControl
                    INTEGER,
                otu10PortlaserState
                    INTEGER,
                otu10PortSourceSlect
                    INTEGER,
                otu10PortRxPowHighThd
                    INTEGER,
                otu10PortRxPowLowThd
                    INTEGER,
                otu10PortTxPowHighThd
                    INTEGER,
                otu10PortTxPowLowThd
                    INTEGER,
                otu10ThdHysteresis
                    INTEGER,
                otu10PortCDRStatus
                    INTEGER,
                otu10PortCDRMode
                    INTEGER
             }

        otu10PortlaserShoutdownControl OBJECT-TYPE
            SYNTAX INTEGER
                {
                alsDisable(1),
                alsRemoteEnable(2),
                forceShutdown(3),
                alsLocalEnable(4)
                }
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                "Description."
            DEFVAL { alsDisable }
            ::= { otu10PortEntry 1 }

        
        otu10PortlaserState OBJECT-TYPE
            SYNTAX INTEGER
                {
                up(1),
                down(2)
                }
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "Description."
            ::= { otu10PortEntry 2 }

        otu10PortSourceSlect OBJECT-TYPE
            SYNTAX INTEGER (1..10)
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                "Description."
            ::= { otu10PortEntry 3 }

        otu10PortRxPowHighThd OBJECT-TYPE
            SYNTAX INTEGER (-6000..2500)
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                "Description."
            DEFVAL { 500 }
            ::= { otu10PortEntry 4 }

        otu10PortRxPowLowThd OBJECT-TYPE
            SYNTAX INTEGER (-6000..2500)
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                "Description."
            DEFVAL { -2500 }
            ::= { otu10PortEntry 5 }
        
        otu10PortTxPowHighThd OBJECT-TYPE
            SYNTAX INTEGER (-6000..2500)
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                "Description."
            DEFVAL { 500 }
            ::= { otu10PortEntry 6 }

        otu10PortTxPowLowThd OBJECT-TYPE
            SYNTAX INTEGER (-6000..2500)
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                "Description."
            DEFVAL { -2000 }
            ::= { otu10PortEntry 7 }

        otu10ThdHysteresis OBJECT-TYPE
            SYNTAX INTEGER (0..1000)
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                "Description."
            DEFVAL { 100 }
            ::= { otu10PortEntry 8 }

        otu10PortCDRStatus  OBJECT-TYPE
            SYNTAX INTEGER
                {
                close(0),
                open(1)
                }
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                "Close or open CDR."
            DEFVAL { open }
            ::= { otu10PortEntry 9 }

        otu10PortCDRMode  OBJECT-TYPE
            SYNTAX INTEGER
                {
                m85G(0),
                m113G(1),
                m117G(2)
                }
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                "Change CDR mode."
            DEFVAL { m113G }
            ::= { otu10PortEntry 10 }

        otu10ConfigTable OBJECT-TYPE
            SYNTAX SEQUENCE OF Otu10ConfigEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "Description."
            ::= { otu10ConfigMIB 2 }

        
        otu10ConfigEntry OBJECT-TYPE
            SYNTAX Otu10ConfigEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "Description."
            INDEX { shelfId, slotNo, subSlotNo }
            ::= { otu10ConfigTable 1 }

        
        Otu10ConfigEntry ::=
            SEQUENCE { 
                otu10WorkMode
                    INTEGER,
                otu10SwitchMode
                    INTEGER,
                otu10SwitchState
                    INTEGER,
                otu10SwitchHoldoffTime
                    Integer32,
                otu10RevertiveOfSwitchMode
                    INTEGER,
                otu10RSMsht
                    Integer32,
                otu10AutoBackMode
                    INTEGER,
                otu10ABsht
                    Integer32
             }

        otu10WorkMode OBJECT-TYPE
            SYNTAX INTEGER
                {
                loopback(1),
                forward(2),
                protection(3),
                double(4),
                boardcast(5),
                freedom(6)
                }
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                "Description."
            DEFVAL { 2 }
            ::= { otu10ConfigEntry 1 }

        
        otu10SwitchMode OBJECT-TYPE
            SYNTAX INTEGER
                {
                manual(1),
                auto(2)
                }
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                "Description."
            DEFVAL { 2 }
            ::= { otu10ConfigEntry 2 }

        
        otu10SwitchState OBJECT-TYPE
            SYNTAX INTEGER
                {
                primary(1),
                secondary(2)
                }
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                "Description."
            DEFVAL { 1 }
            ::= { otu10ConfigEntry 3 }

        
        otu10SwitchHoldoffTime OBJECT-TYPE
            SYNTAX Integer32 (0..32000)
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                "Switch holdoff time of PtoS. (Unit: Millisecond)."
            DEFVAL { '0'b }
            ::= { otu10ConfigEntry 4 }

        
        otu10RevertiveOfSwitchMode OBJECT-TYPE
            SYNTAX INTEGER
                {
                nonrevertive(0),
                revertive(1)
                }
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                "Revertive switch mode."
            DEFVAL { 0 }
            ::= { otu10ConfigEntry 5 }

        
        otu10RSMsht OBJECT-TYPE
            SYNTAX Integer32 (0..32000)
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                "Switch holdoff time for revertive switch mode.(unit: Minute)."
            DEFVAL { '1'b }
            ::= { otu10ConfigEntry 6 }

        
        otu10AutoBackMode OBJECT-TYPE
            SYNTAX INTEGER
                {
                notautoback(0),
                autoback(1)
                }
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                "Auto-back mode."
            DEFVAL { 1 }
            ::= { otu10ConfigEntry 7 }

        
        otu10ABsht OBJECT-TYPE
            SYNTAX Integer32 (0..32000)
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                "Holdoff time of auto-back mode(unit: Second)."
            DEFVAL { 30 }
            ::= { otu10ConfigEntry 8 }
			
        
        omumConfigMIB OBJECT IDENTIFIER ::= { st6200 6 }

        
        waveLengthTable OBJECT-TYPE
            SYNTAX SEQUENCE OF WaveLengthEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "Description."
            ::= { omumConfigMIB 1 }

        
        waveLengthEntry OBJECT-TYPE
            SYNTAX WaveLengthEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "Description."
            INDEX { shelfId, slotNo, subSlotNo }
            ::= { waveLengthTable 1 }

        
        WaveLengthEntry ::=
            SEQUENCE { 
                wdmType
                    INTEGER,
                portSum
                    Integer32,
                waveLengthInfo
                    DisplayString
             }

        wdmType OBJECT-TYPE
            SYNTAX INTEGER
                {
                cwdm(1),
                dwdm(2)
                }
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "Description."
            DEFVAL { cwdm }
            ::= { waveLengthEntry 1 }

        
        portSum OBJECT-TYPE
            SYNTAX Integer32 (1..99)
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The total number of ports."
            DEFVAL { 16 }
            ::= { waveLengthEntry 2 }

        
        waveLengthInfo OBJECT-TYPE
            SYNTAX DisplayString
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "Wavalength infomation of the port."
            ::= { waveLengthEntry 3 }

        voaInfoMIB OBJECT IDENTIFIER ::= { st6200 7 }
        
        voaPluggableTable OBJECT-TYPE
            SYNTAX SEQUENCE OF VoaPluggableEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "Description."
            ::= { voaInfoMIB 1 }

        
        voaPluggableEntry OBJECT-TYPE
            SYNTAX VoaPluggableEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "Description."
            INDEX { shelfId, slotNo, subSlotNo, portNo, subPortNo }
            ::= { voaPluggableTable 1 }
			
		VoaPluggableEntry ::=
            SEQUENCE { 
                voaPluggableAttenuation
                    Integer32,
                voaPluggableWaveLengthMin
                    Integer32,
                voaPluggableWaveLengthMax
                    Integer32,
                voaPluggableAttenuationMin
                    Integer32,
                voaPluggableAttenuationMax
                    Integer32
             }

	    voaPluggableAttenuation OBJECT-TYPE
            SYNTAX Integer32 (-10000..200)
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                "Voa module tx attenuation value(unit: 0.1dBm)."
            DEFVAL { -10000 }
            ::= { voaPluggableEntry 1 }
			
		voaPluggableWaveLengthMin OBJECT-TYPE
            SYNTAX Integer32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "Wavalength range,min value(unit: nm)."
            DEFVAL { -10000 }
            ::= { voaPluggableEntry 2 }
			
		voaPluggableWaveLengthMax OBJECT-TYPE
            SYNTAX Integer32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "Wavalength range,max value(unit: nm)."
            DEFVAL { -10000 }
            ::= { voaPluggableEntry 3 }	

		voaPluggableAttenuationMin OBJECT-TYPE
            SYNTAX Integer32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "Attenuation range,Min value(unit: 0.1dB)."
            DEFVAL { 0 }
            ::= { voaPluggableEntry 4 }	
            
        voaPluggableAttenuationMax OBJECT-TYPE
            SYNTAX Integer32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "Attenuation range,Man value(unit: 0.1dB)."
            DEFVAL { 200 }
            ::= { voaPluggableEntry 5 }	
		

    END

--
-- ST-6200-MIB.my
--
