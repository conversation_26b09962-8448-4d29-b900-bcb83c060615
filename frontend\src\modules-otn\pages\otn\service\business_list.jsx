import {Button, Col, Input, message, Row, Select, Space, Switch, Tabs, theme, Typography} from "antd";
import {
    asciiHexConvertToPlainText,
    convertToArray,
    getDeviceStateValue,
    getText,
    NULL_VALUE,
    OCH_MODE_SUPPORT_MAP,
    OPERATIONAL_MODE_MAP,
    plainTextConvertToAsciiHex,
    PORT_MAPPING_MAP,
    sortLabel,
    DebounceButton,
    classNames
} from "@/modules-otn/utils/util";
import {apiEditRpc, getStateData, netconfGetByXML, objectGet} from "@/modules-otn/apis/api";
import React, {useEffect, useRef, useState} from "react";
import {useSelector} from "react-redux";
import Icon from "@ant-design/icons";
import {CustomTable} from "@/modules-otn/components/common/custom_table";
import {addCommonIcon, editTableIcon, refreshEnabledIcon} from "@/modules-otn/pages/otn/device/device_icons";
import {openModalCreate} from "@/modules-otn/components/form/create_form";
import {gLabelList} from "@/store/modules/otn/languageOTNSlice";
import openCustomEditForm from "@/modules-otn/components/form/edit_form_custom";
import {getSignalType, updatePortSignalType} from "@/modules-otn/pages/otn/service/resource_manager";
import useUserRight from "@/modules-otn/hooks/useUserRight";
import {decInfoIcon, hexInfoIcon} from "@/modules-otn/pages/otn/service/service_icon";
import styles from "./service_common.module.scss";
import MutilColumnForm from "../common/mutil_column_form";

const {Paragraph} = Typography;

const HexComponent = ({value}) => {
    const [showType, setShowType] = useState(false);
    const {labelList} = useSelector(state => state.languageOTN);
    return (
        <Paragraph
            style={{margin: 0, fontSize: 12}}
            editable={{
                icon: <Icon component={showType ? hexInfoIcon : decInfoIcon} />,
                tooltip: showType ? labelList.hex : labelList.dec,
                editing: false,
                showType: false,
                onStart: () => {
                    setShowType(!showType);
                }
            }}
        >
            {showType ? asciiHexConvertToPlainText(value) : value}
        </Paragraph>
    );
};

const BusinessList = () => {
    const {labelList} = useSelector(state => state.languageOTN);
    const readyOnlyRight = useUserRight();
    const {
        token: {colorPrimary}
    } = theme.useToken();
    const {neNameMap} = useSelector(state => state.neName);
    const [neOptions, setNeOptions] = useState([{value: "", label: "No Data", disabled: true}]);
    const [cardOptions, setCardOptions] = useState([{value: "", label: "No Data", disabled: true}]);
    const [selectNe, setSelectNe] = useState();
    const selectNeRef = useRef(selectNe);
    const [selectCard, setSelectCard] = useState();
    const selectCardRef = useRef(selectCard);
    const [cardLoading, setCardLoading] = useState(false);
    const [loading, setLoading] = useState(false);
    const [activeTab, setActiveTab] = useState();
    const activeTabRef = useRef(activeTab);

    const [switchLoading, setSwitchLoading] = useState({});
    const [frequencyOptions, setFrequencyOptions] = useState([]);

    const [powerData, setPowerData] = useState([]);
    const [portData, setPortData] = useState([]);
    const [ttiData, setTtiData] = useState([]);
    const [lldpData, setLldpData] = useState([]);

    const DataMapping = {
        port: portData,
        "otn-tti": ttiData,
        lldp: lldpData
    };

    const onSelectNe = value => {
        if (!value) onSelectCard(null);
        setSelectNe(value);
        selectNeRef.current = value;
    };
    useEffect(() => {
        const frequencyOptions = [
            {value: "50GHz", label: "50GHz", children: []},
            {value: "75GHz", label: "75GHz", children: []},
            {value: "100GHz", label: "100GHz", children: []}
        ];
        for (let i = 0; i < 96; i++) {
            const value = 191.35 + i * 0.05;
            const frequencyNm = (
                Math.round(((((2.99792458 * 10 ** 8) / value) * 1000000) / 1000000000) * 100) / 100
            ).toFixed(2);
            frequencyOptions[0].children.push({
                label:
                    i < 48
                        ? `C${i + 13}-${value.toFixed(2)}THz-${frequencyNm}nm`
                        : `H${i - 35}-${value.toFixed(2)}THz-${frequencyNm}nm`,
                value:
                    i < 48
                        ? `C${i + 13}-${value.toFixed(2)}THz-${frequencyNm}nm`
                        : `H${i - 35}-${value.toFixed(2)}THz-${frequencyNm}nm`
            });
        }
        for (let i = 0; i < 48; i++) {
            const value = 191.4 + i * 0.1;
            const frequencyTHz = value.toFixed(2);
            const frequencyNm = (
                Math.round(((((2.99792458 * 10 ** 8) / value) * 1000000) / 1000000000) * 100) / 100
            ).toFixed(2);
            const label = `C${i + 14}-${frequencyTHz}THz-${frequencyNm}nm`;
            const optionValue = `C${i + 14}-${frequencyTHz}THz-${frequencyNm}nm`;
            frequencyOptions[2].children.push({
                label,
                value: optionValue
            });
        }
        for (let i = 0; i < 64; i++) {
            const value = 196.0375 - i * 0.075;
            const frequencyTHz = value.toFixed(4);
            const frequencyNm = (
                Math.round(((((2.99792458 * 10 ** 8) / value) * 1000000) / 1000000000) * 100) / 100
            ).toFixed(2);
            const label = `CM${i + 1}-${frequencyTHz}THz-${frequencyNm}nm`;
            const optionValue = `CM${i + 1}-${frequencyTHz}THz-${frequencyNm}nm`;
            frequencyOptions[1].children.push({
                label,
                value: optionValue
            });
            setFrequencyOptions(frequencyOptions);
        }
    }, []);

    const onSelectCard = value => {
        setSelectCard(value);
        selectCardRef.current = value;
    };

    const getFilterPanel = () => {
        return (
            <MutilColumnForm
                fields={[
                    {
                        label: labelList.ne,
                        render: (
                            <Select
                                value={selectNe}
                                placeholder={labelList.please_select}
                                filterSort={sortLabel()}
                                style={{
                                    width: 280
                                }}
                                disabled={loading}
                                allowClear
                                onDropdownVisibleChange={open => {
                                    if (open) {
                                        objectGet("config:ne", {type: "5"}).then(rs => {
                                            const {apiResult, apiMessage, documents} = rs;
                                            if (apiResult === "fail") {
                                                message.error(apiMessage).then();
                                                return;
                                            }
                                            const _list = documents.map(item => {
                                                return {
                                                    label: item.value.name,
                                                    value: item.value.ne_id,
                                                    type: item.value.type
                                                };
                                            });
                                            _list.sort((a, b) =>
                                                a.label.localeCompare(b.label, "ZH-CN", {numeric: true})
                                            );
                                            setNeOptions(_list);
                                        });
                                    }
                                }}
                                onChange={onSelectNe}
                                options={neOptions}
                            />
                        )
                    },
                    {
                        label: labelList.card,
                        render: (
                            <Select
                                value={selectCard}
                                placeholder={labelList.please_select}
                                // filterSort={sortLabel()}
                                loading={cardLoading}
                                disabled={loading}
                                allowClear
                                style={{
                                    width: 280
                                }}
                                onDropdownVisibleChange={open => {
                                    if (open) {
                                        if (!selectNe) {
                                            setCardOptions([]);
                                            return;
                                        }
                                        setCardLoading(true);
                                        objectGet("ne:5:component", {
                                            ne_id: selectNe,
                                            parent: "CHASSIS-1"
                                        }).then(rs => {
                                            const _data = [];
                                            rs.documents.map(i => {
                                                const cardType = i.value.data.name.split("-")[0];
                                                if (
                                                    ["LINECARD"].includes(cardType) &&
                                                    i.value.data.config?.["vendor-type-preconf"]
                                                ) {
                                                    _data.push({
                                                        label: i.value.data.name.replace(
                                                            cardType,
                                                            i.value.data?.state?.["vendor-type-preconf"] ??
                                                                i.value.data?.config?.["vendor-type-preconf"]
                                                        ),
                                                        // label: i.value.data.name,
                                                        value: i.value.data.name
                                                    });
                                                }
                                            });
                                            _data.sort((a, b) => {
                                                const arrA = a.label.split("-");
                                                const arrB = b.label.split("-");
                                                return arrA[arrA.length - 1] - arrB[arrB.length - 1];
                                            });
                                            setCardOptions(_data);
                                            setCardLoading(false);
                                        });
                                    }
                                }}
                                onChange={onSelectCard}
                                options={cardOptions}
                            />
                        )
                    }
                ]}
            />
        );
    };

    const getEditRander = (state, getXml, legitimate, legitimateMsg, maxLength = 15, reload = false) => {
        try {
            return (
                <Paragraph
                    style={{margin: 0, fontSize: 12, display: "flex", justifyContent: "space-between"}}
                    editable={{
                        maxLength,
                        icon: <Icon component={editTableIcon} />,
                        onChange: newVal => {
                            if (newVal === state) {
                                return;
                            }
                            if (legitimate && !legitimate(newVal)) {
                                message.error(legitimateMsg).then();
                                return;
                            }
                            apiEditRpc({
                                ne_id: selectNe,
                                params: getXml(newVal),
                                msg: false,
                                success: () => {
                                    setTimeout(() => {
                                        if (reload) {
                                            reloadData();
                                        } else {
                                            message.success(labelList.update_success).then();
                                        }
                                    }, 2000);
                                },
                                fail: () => {
                                    message.error(labelList.update_fail).then();
                                    setLoading(false);
                                }
                            }).then();
                        },
                        triggerType: ["icon", "text"]
                    }}
                >
                    {state ?? ""}
                </Paragraph>
            );
        } catch (e) {
            return state;
        }
    };

    const getAsciiHexForSplitTTI = (value, newValue, index) => {
        if (index === "sapi") {
            return `00${plainTextConvertToAsciiHex(newValue ?? "")}`.padEnd(32, "0") + value.substring(32);
        }
        if (index === "dapi") {
            return (
                value.substring(0, 32).padEnd(32, "0") +
                `00${plainTextConvertToAsciiHex(newValue ?? "")}`.padEnd(32, "0") +
                value.substring(64)
            );
        }
        if (index === "oper") {
            return `${value.substring(0, 64).padEnd(64, "0")}${plainTextConvertToAsciiHex(newValue ?? "")}`;
        }
    };

    const switchComponent = (key, value, rowData, disabled, getEditXML) => {
        if (value === NULL_VALUE) {
            return NULL_VALUE;
        }
        return (
            <Switch
                key={key}
                disabled={disabled ?? false}
                // defaultChecked={value === "true"}
                checked={value === "true"}
                loading={switchLoading[key]}
                onChange={newVal => {
                    if (newVal === value) {
                        return;
                    }
                    setSwitchLoading({...switchLoading, [key]: true});
                    apiEditRpc({
                        ne_id: selectNe,
                        params: getEditXML(newVal),
                        success: () => {
                            reloadData();
                            setSwitchLoading({...switchLoading, [key]: false});
                        }
                    }).then();
                }}
            />
        );
    };

    const getStateValue = (cardType, stateValues, componentName, parameter) => {
        return getDeviceStateValue(stateValues, componentName, parameter) ?? NULL_VALUE;
    };

    const formatThreshold = state => {
        if (state) {
            return parseFloat(state).toFixed(2);
        }
        return state ?? NULL_VALUE;
    };

    const getThresholdValue = (thresholdValues, point, parameter, granularity) => {
        return thresholdValues?.find(
            i =>
                i.value.data["pm-point"] === point &&
                i.value.data["pm-parameter"] === parameter &&
                i.value.data["pm-granularity"] === granularity
        );
    };

    const tableConfig = {
        port: [
            {
                dataIndex: "port",
                fixed: "left",
                width: 120
            },
            {
                dataIndex: "signal-type",
                title: "service-type",
                width: 150,
                render: state => {
                    if (state) {
                        return state.substring(state.indexOf("_") + 1);
                    }
                    return state;
                }
            },
            {
                dataIndex: "mapping-path",
                width: 270,
                render: (state, rowData) => {
                    if (rowData["signal-type"] === "NA" || rowData?.port?.indexOf?.("-C") < 0) {
                        return NULL_VALUE;
                    }
                    return PORT_MAPPING_MAP[rowData["actual-vendor-type"]]?.[rowData["signal-type"]] ?? NULL_VALUE;
                }
            },
            {
                dataIndex: "modulation",
                width: 220,
                render: state => {
                    return OPERATIONAL_MODE_MAP[state] ?? NULL_VALUE;
                }
            },
            {
                dataIndex: "module-type",
                width: 150
            },
            {
                dataIndex: "wavelength",
                width: 200
            },
            {
                dataIndex: "target-output-power",
                width: 180
            },
            {
                dataIndex: "fec",
                width: 120,
                render: state => {
                    return state || NULL_VALUE;
                }
            },
            {
                dataIndex: "pre-fec",
                width: 120
            },
            {
                dataIndex: "post-fec",
                width: 120
            },
            {
                dataIndex: "loopback",
                width: 120
            },
            {
                dataIndex: "als",
                width: 150,
                render: state => {
                    return state || NULL_VALUE;
                }
            },
            {
                dataIndex: "port-used",
                width: 120
            },
            {
                dataIndex: "laser-enable",
                width: 150,
                render: (value, rowData) =>
                    switchComponent(
                        rowData.port,
                        value,
                        rowData,
                        readyOnlyRight.disabled || rowData["laser-enable"] === NULL_VALUE,
                        newVal => {
                            return {
                                components: {
                                    component: {
                                        name: rowData.port.replace("PORT", "TRANSCEIVER"),
                                        transceiver: {
                                            config: {
                                                enabled: newVal
                                            }
                                        }
                                    }
                                }
                            };
                        }
                    )
            },
            {
                dataIndex: "operation",
                width: 120,
                fixed: "right",
                render: (_, originValue) => {
                    return (
                        <DebounceButton
                            type="link"
                            disabled={readyOnlyRight.disabled}
                            style={{color: readyOnlyRight.disabled ? "" : colorPrimary}}
                            onClick={() => {
                                const columnsCfg = [
                                    {
                                        dataIndex: "signal-type",
                                        title: "service-type",
                                        inputType: "select",
                                        data: async () => {
                                            return await getSignalType(selectNe, originValue.port);
                                        }
                                    },
                                    {
                                        dataIndex: "modulation",
                                        inputType: "select",
                                        // data: {options: OPERATIONAL_MODE_MAP}
                                        data: async () => {
                                            const _options = [];
                                            const supportType = OCH_MODE_SUPPORT_MAP[originValue["actual-vendor-type"]];
                                            Object.entries(OPERATIONAL_MODE_MAP).map(([k, v]) => {
                                                if (!supportType || supportType.includes(parseInt(k))) {
                                                    _options.push({
                                                        label: v,
                                                        value: k
                                                    });
                                                }
                                            });
                                            return _options;
                                        }
                                    },
                                    {
                                        dataIndex: "wavelength",
                                        inputType: "cascader",
                                        showCheckedStrategy: "SHOW_CHILD",
                                        data: async () => {
                                            return frequencyOptions;
                                        }
                                    },
                                    {
                                        dataIndex: "target-output-power",
                                        inputType: "number",
                                        step: 0.01
                                    },
                                    {
                                        dataIndex: "fec",
                                        title: "fec-type",
                                        inputType: "select",
                                        data: {options: ["ENABLED", "DISABLED"]}
                                    },
                                    {
                                        dataIndex: "loopback",
                                        inputType: "select",
                                        data: {options: ["NONE", "FACILITY", "TERMINAL"]}
                                    },
                                    {
                                        dataIndex: "als",
                                        inputType: "select",
                                        data: {options: ["ENABLED", "DISABLED"]}
                                    },
                                    {
                                        dataIndex: "laser-enable",
                                        inputType: "select",
                                        data: {options: ["true", "false"]}
                                    }
                                ];
                                openCustomEditForm({
                                    title: "Modify",
                                    columnNum: 1,
                                    columns: columnsCfg
                                        .filter(
                                            i =>
                                                (i.dataIndex === "signal-type" &&
                                                    originValue.port.indexOf("C") > -1 &&
                                                    originValue["port-used"] === "False") ||
                                                (i.dataIndex === "wavelength" &&
                                                    originValue["port-used"] === "False" &&
                                                    originValue[i.dataIndex] &&
                                                    originValue[i.dataIndex] !== NULL_VALUE) ||
                                                (i.dataIndex !== "signal-type" &&
                                                    i.dataIndex !== "wavelength" &&
                                                    originValue[i.dataIndex] &&
                                                    originValue[i.dataIndex] !== NULL_VALUE)
                                        )
                                        .map(i => [i]),
                                    getData: async () => {
                                        if (originValue.modulation) {
                                            return {
                                                ...originValue,
                                                "signal-type": originValue["signal-type"].replace("PROT_", ""),
                                                modulation: OPERATIONAL_MODE_MAP[originValue.modulation]
                                            };
                                        }
                                        return {
                                            ...originValue,
                                            "signal-type": originValue["signal-type"].replace("PROT_", "")
                                        };
                                    },
                                    saveFun: async diffValue => {
                                        let rs;
                                        // if (diffValue["signal-type"]) {
                                        //     const lineCardType =
                                        //         selectCardRef.current?.linecard_cardType?.split("-")?.[0];
                                        //     if (
                                        //         lineCardType &&
                                        //         ["2MC2", "4MC4"].includes(lineCardType) &&
                                        //         diffValue["signal-type"] !== "PROT_400GE"
                                        //     ) {
                                        //         for (let i = 0; i < portData.length; i++) {
                                        //             const portItem = portData[i];
                                        //             if (portItem.port.indexOf("C") > -1) {
                                        //                 rs = await updatePortSignalType({
                                        //                     selectNe,
                                        //                     data: portItem,
                                        //                     portType: diffValue["signal-type"],
                                        //                     tableData: portData,
                                        //                     reloadData: () => {},
                                        //                     setLoading
                                        //                 });
                                        //                 if (!rs || rs.apiResult === "fail") {
                                        //                     // message.error(gLabelList.save_failed);
                                        //                     return rs;
                                        //                 }
                                        //             }
                                        //         }
                                        //     } else {
                                        //         rs = await updatePortSignalType({
                                        //             selectNe,
                                        //             data: originValue,
                                        //             portType: diffValue["signal-type"],
                                        //             tableData: portData,
                                        //             reloadData: () => {},
                                        //             setLoading
                                        //         });
                                        //         if (!rs || rs.apiResult === "fail") {
                                        //             // message.error(gLabelList.save_failed);
                                        //             return rs;
                                        //         }
                                        //     }
                                        // }
                                        if (
                                            diffValue.modulation ||
                                            diffValue.wavelength ||
                                            diffValue["target-output-power"]
                                        ) {
                                            const update = {};
                                            if (diffValue.modulation) {
                                                update["operational-mode"] = diffValue.modulation;
                                            }
                                            if (diffValue.wavelength) {
                                                update.frequency =
                                                    diffValue.wavelength[1].match(/(\d+(\.\d+)?)/g)[1] * 1000000;
                                            }
                                            if (diffValue["target-output-power"]) {
                                                update["target-output-power"] = diffValue["target-output-power"];
                                            }
                                            rs = await apiEditRpc({
                                                ne_id: selectNe,
                                                msg: false,
                                                params: {
                                                    components: {
                                                        component: {
                                                            name: originValue.port.replace("PORT", "OCH"),
                                                            "optical-channel": {
                                                                config: update
                                                            }
                                                        }
                                                    }
                                                }
                                            });
                                            if (rs.apiResult === "fail") {
                                                message.error(gLabelList.save_failed);
                                                return rs;
                                            }
                                        }
                                        if (diffValue.fec || diffValue.als) {
                                            const update = {};
                                            if (diffValue.fec) {
                                                update.ethernet = {config: {"client-fec": diffValue.fec}};
                                            }
                                            if (diffValue.als) {
                                                if (originValue.interface_type === "ethernet") {
                                                    let _als;
                                                    if (diffValue.als === "ENABLED") {
                                                        _als = "ETHERNET";
                                                    } else if (diffValue.als === "DISABLED") {
                                                        _als = "LASER_SHUTDOWN";
                                                    }
                                                    if (_als) {
                                                        if (diffValue.fec) {
                                                            update.ethernet.config["client-als"] = _als;
                                                        } else {
                                                            update.ethernet = {config: {"client-als": _als}};
                                                        }
                                                    }
                                                } else {
                                                    update[originValue.interface_type] = {
                                                        config: {
                                                            "client-als": diffValue.als
                                                        }
                                                    };
                                                }
                                            }
                                            rs = await apiEditRpc({
                                                ne_id: selectNe,
                                                msg: false,
                                                params: {
                                                    interfaces: {
                                                        interface: {
                                                            name: originValue["interface-name"],
                                                            ...update
                                                        }
                                                    }
                                                }
                                            });
                                            if (rs.apiResult === "fail") {
                                                message.error(gLabelList.save_failed);
                                                return rs;
                                            }
                                        }
                                        if (diffValue.loopback) {
                                            if (originValue.loopback !== "NONE" && diffValue.loopback !== "NONE") {
                                                rs = await apiEditRpc({
                                                    ne_id: selectNe,
                                                    msg: false,
                                                    params: {
                                                        "terminal-device": {
                                                            "logical-channels": {
                                                                channel: {
                                                                    index: originValue["channel-index"],
                                                                    config: {
                                                                        "loopback-mode": "NONE"
                                                                    }
                                                                }
                                                            }
                                                        }
                                                    }
                                                });
                                                if (rs.apiResult === "fail") {
                                                    message.error(gLabelList.save_failed);
                                                    return rs;
                                                }
                                            }
                                            rs = await apiEditRpc({
                                                ne_id: selectNe,
                                                msg: false,
                                                params: {
                                                    "terminal-device": {
                                                        "logical-channels": {
                                                            channel: {
                                                                index: originValue["channel-index"],
                                                                config: {
                                                                    "loopback-mode": diffValue.loopback
                                                                }
                                                            }
                                                        }
                                                    }
                                                }
                                            });
                                            if (rs.apiResult === "fail") {
                                                message.error(gLabelList.save_failed);
                                                return rs;
                                            }
                                        }

                                        if (diffValue["laser-enable"]) {
                                            rs = await apiEditRpc({
                                                ne_id: selectNe,
                                                msg: false,
                                                params: {
                                                    components: {
                                                        component: {
                                                            name: originValue.port.replace("PORT", "TRANSCEIVER"),
                                                            transceiver: {
                                                                config: {
                                                                    enabled: diffValue["laser-enable"]
                                                                }
                                                            }
                                                        }
                                                    }
                                                }
                                            });
                                            if (rs.apiResult === "fail") {
                                                message.error(gLabelList.save_failed);
                                                return rs;
                                            }
                                        }
                                        setTimeout(() => {
                                            reloadData();
                                        }, 3000);
                                        message.success(gLabelList.save_success);
                                        return rs;
                                    }
                                });
                            }}
                        >
                            Modify
                        </DebounceButton>
                    );
                }
            }
        ],
        "otn-tti": [
            {
                dataIndex: "port",
                width: 120,
                fixed: "left"
            },
            {
                dataIndex: "signal-type",
                width: 120,
                render: state => {
                    if (state) {
                        return state.substring(state.indexOf("_") + 1);
                    }
                    return state;
                }
            },
            {
                dataIndex: "sm-tti-msg-transmit-sapi",
                width: 180,
                render: (state, data) => {
                    if (!data.otn_signal && !data["signal-type"]?.startsWith?.("OTU")) {
                        return data["sm-tti-msg-expected-sapi"];
                    }
                    const val = data["sm-tti-msg-expected"] ?? "";
                    try {
                        if (readyOnlyRight.disabled) {
                            return <HexComponent value={val.substring(0, 32)} />;
                        }
                        if (data["signal-type"].startsWith("OTU")) {
                            return getEditRander(
                                asciiHexConvertToPlainText(val.substring(0, 32)),
                                newVal => {
                                    return {
                                        "terminal-device": {
                                            "logical-channels": {
                                                channel: {
                                                    index: data["sm-channel-index"],
                                                    otn: {
                                                        config: {
                                                            "tti-msg-expected": getAsciiHexForSplitTTI(
                                                                val,
                                                                newVal,
                                                                "sapi"
                                                            )
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    };
                                },
                                null,
                                null,
                                15,
                                true
                            );
                        }
                        return <HexComponent value={val.substring(0, 32)} />;
                    } catch (e) {
                        return val;
                    }
                }
            },
            {
                dataIndex: "sm-tti-msg-transmit-dapi",
                width: 180,
                render: (state, data) => {
                    if (!data.otn_signal && !data["signal-type"]?.startsWith?.("OTU")) {
                        return data["sm-tti-msg-transmit-dapi"];
                    }
                    const val = data["sm-tti-msg-transmit"] ?? "";
                    try {
                        if (readyOnlyRight.disabled) {
                            return <HexComponent value={val.substring(32, 64)} />;
                        }
                        if (data["signal-type"].startsWith("OTU")) {
                            return getEditRander(
                                asciiHexConvertToPlainText(val.substring(32, 64)),
                                newVal => {
                                    return {
                                        "terminal-device": {
                                            "logical-channels": {
                                                channel: {
                                                    index: data["sm-channel-index"],
                                                    otn: {
                                                        config: {
                                                            "tti-msg-transmit": getAsciiHexForSplitTTI(
                                                                val,
                                                                newVal,
                                                                "dapi"
                                                            )
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    };
                                },
                                null,
                                null,
                                15,
                                true
                            );
                        }
                        return <HexComponent value={val.substring(32, 64)} />;
                    } catch (e) {
                        return val;
                    }
                }
            },
            {
                dataIndex: "sm-tti-msg-transmit-oper",
                width: 180,
                render: (state, data) => {
                    if (!data.otn_signal && !data["signal-type"]?.startsWith?.("OTU")) {
                        return data["sm-tti-msg-transmit-oper"];
                    }
                    const val = data["sm-tti-msg-transmit"] ?? "";
                    try {
                        if (readyOnlyRight.disabled) {
                            return <HexComponent value={val.substring(64)} />;
                        }
                        if (data["signal-type"].startsWith("OTU")) {
                            return getEditRander(
                                asciiHexConvertToPlainText(val.substring(64)),
                                newVal => {
                                    return {
                                        "terminal-device": {
                                            "logical-channels": {
                                                channel: {
                                                    index: data["sm-channel-index"],
                                                    otn: {
                                                        config: {
                                                            "tti-msg-transmit": getAsciiHexForSplitTTI(
                                                                val,
                                                                newVal,
                                                                "oper"
                                                            )
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    };
                                },
                                null,
                                null,
                                30,
                                true
                            );
                        }
                        return <HexComponent value={val.substring(64)} />;
                    } catch (e) {
                        return val;
                    }
                }
            },
            {
                dataIndex: "sm-tti-msg-expected-sapi",
                width: 150,
                render: (state, data) => {
                    if (!data.otn_signal && !data["signal-type"]?.startsWith?.("OTU")) {
                        return data["sm-tti-msg-expected-sapi"];
                    }
                    const val = data["sm-tti-msg-expected"] ?? "";
                    try {
                        if (readyOnlyRight.disabled) {
                            return <HexComponent value={val.substring(0, 32)} />;
                        }
                        if (data["signal-type"].startsWith("OTU")) {
                            return getEditRander(
                                asciiHexConvertToPlainText(val.substring(0, 32)),
                                newVal => {
                                    return {
                                        "terminal-device": {
                                            "logical-channels": {
                                                channel: {
                                                    index: data["sm-channel-index"],
                                                    otn: {
                                                        config: {
                                                            "tti-msg-expected": getAsciiHexForSplitTTI(
                                                                val,
                                                                newVal,
                                                                "sapi"
                                                            )
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    };
                                },
                                null,
                                null,
                                15,
                                true
                            );
                        }
                        return <HexComponent value={val.substring(0, 32)} />;
                    } catch (e) {
                        return val;
                    }
                }
            },
            {
                dataIndex: "sm-tti-msg-expected-dapi",
                width: 150,
                render: (state, data) => {
                    if (!data.otn_signal && !data["signal-type"]?.startsWith?.("OTU")) {
                        return data["sm-tti-msg-expected-dapi"];
                    }
                    const val = data["sm-tti-msg-expected"] ?? "";
                    try {
                        if (readyOnlyRight.disabled) {
                            return <HexComponent value={val.substring(32, 64)} />;
                        }
                        if (data["signal-type"].startsWith("OTU")) {
                            return getEditRander(
                                asciiHexConvertToPlainText(val.substring(32, 64)),
                                newVal => {
                                    return {
                                        "terminal-device": {
                                            "logical-channels": {
                                                channel: {
                                                    index: data["sm-channel-index"],
                                                    otn: {
                                                        config: {
                                                            "tti-msg-expected": getAsciiHexForSplitTTI(
                                                                val,
                                                                newVal,
                                                                "dapi"
                                                            )
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    };
                                },
                                null,
                                null,
                                15,
                                true
                            );
                        }
                        return <HexComponent value={val.substring(32, 64)} />;
                    } catch (e) {
                        return val;
                    }
                }
            },
            {
                dataIndex: "sm-tti-msg-expected-oper",
                width: 150,
                render: (state, data) => {
                    if (!data.otn_signal && !data["signal-type"]?.startsWith?.("OTU")) {
                        return data["sm-tti-msg-expected-oper"];
                    }
                    const val = data["sm-tti-msg-expected"] ?? "";
                    try {
                        if (readyOnlyRight.disabled) {
                            return <HexComponent value={val.substring(64)} />;
                        }
                        if (data["signal-type"].startsWith("OTU")) {
                            return getEditRander(
                                asciiHexConvertToPlainText(val.substring(64)),
                                newVal => {
                                    return {
                                        "terminal-device": {
                                            "logical-channels": {
                                                channel: {
                                                    index: data["sm-channel-index"],
                                                    otn: {
                                                        config: {
                                                            "tti-msg-expected": getAsciiHexForSplitTTI(
                                                                val,
                                                                newVal,
                                                                "oper"
                                                            )
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    };
                                },
                                null,
                                null,
                                30,
                                true
                            );
                        }
                        return <HexComponent value={val.substring(64)} />;
                    } catch (e) {
                        return val;
                    }
                }
            },
            {
                dataIndex: "sm-tti-msg-recv-sapi",
                width: 180,
                render: (state, data) => {
                    if (!data.otn_signal) {
                        return data["sm-tti-msg-recv-sapi"];
                    }
                    const val = data["sm-tti-msg-recv"];
                    if (val) {
                        return <HexComponent value={val.substring(0, 32)} />;
                    }
                    return state;
                }
            },
            {
                dataIndex: "sm-tti-msg-recv-dapi",
                width: 180,
                render: (state, data) => {
                    if (!data.otn_signal) {
                        return data["sm-tti-msg-recv-dapi"];
                    }
                    const val = data["sm-tti-msg-recv"];
                    if (val) {
                        return <HexComponent value={val.substring(32, 64)} />;
                    }
                    return state;
                }
            },
            {
                dataIndex: "sm-tti-msg-recv-oper",
                width: 180,
                render: (state, data) => {
                    if (!data.otn_signal) {
                        return data["sm-tti-msg-recv-oper"];
                    }
                    const val = data["sm-tti-msg-recv"];
                    if (val) {
                        return <HexComponent value={val.substring(64)} />;
                    }
                    return state;
                }
            },
            {
                dataIndex: "pm-tti-msg-transmit-sapi",
                width: 180,
                render: (state, data) => {
                    const val = data["pm-tti-msg-transmit"];
                    if (!val) {
                        return state;
                    }
                    try {
                        if (
                            readyOnlyRight.disabled ||
                            (data["signal-type"].startsWith("OTU") && !data["signal-type"].startsWith("OTUC"))
                        ) {
                            return <HexComponent value={val.substring(0, 32)} />;
                            // return asciiHexConvertToPlainText(state);
                        }
                        return getEditRander(
                            asciiHexConvertToPlainText(val.substring(0, 32)),
                            newVal => {
                                return {
                                    "terminal-device": {
                                        "logical-channels": {
                                            channel: {
                                                index: data["pm-channel-index"],
                                                otn: {
                                                    config: {
                                                        "tti-msg-transmit": getAsciiHexForSplitTTI(val, newVal, "sapi")
                                                    }
                                                }
                                            }
                                        }
                                    }
                                };
                            },
                            null,
                            null,
                            15,
                            true
                        );
                    } catch (e) {
                        return val;
                    }
                }
            },
            {
                dataIndex: "pm-tti-msg-transmit-dapi",
                width: 180,
                render: (state, data) => {
                    const val = data["pm-tti-msg-transmit"];
                    if (!val) {
                        return state;
                    }
                    try {
                        if (
                            readyOnlyRight.disabled ||
                            (data["signal-type"].startsWith("OTU") && !data["signal-type"].startsWith("OTUC"))
                        ) {
                            return <HexComponent value={val.substring(32, 64)} />;
                            // return asciiHexConvertToPlainText(state);
                        }
                        return getEditRander(
                            asciiHexConvertToPlainText(val.substring(32, 64)),
                            newVal => {
                                return {
                                    "terminal-device": {
                                        "logical-channels": {
                                            channel: {
                                                index: data["pm-channel-index"],
                                                otn: {
                                                    config: {
                                                        "tti-msg-transmit": getAsciiHexForSplitTTI(val, newVal, "dapi")
                                                    }
                                                }
                                            }
                                        }
                                    }
                                };
                            },
                            null,
                            null,
                            15,
                            true
                        );
                    } catch (e) {
                        return val;
                    }
                }
            },
            {
                dataIndex: "pm-tti-msg-transmit-oper",
                width: 180,
                render: (state, data) => {
                    const val = data["pm-tti-msg-transmit"];
                    if (!val) {
                        return state;
                    }
                    try {
                        if (
                            readyOnlyRight.disabled ||
                            (data["signal-type"].startsWith("OTU") && !data["signal-type"].startsWith("OTUC"))
                        ) {
                            return <HexComponent value={val.substring(64)} />;
                            // return asciiHexConvertToPlainText(state);
                        }
                        return getEditRander(
                            asciiHexConvertToPlainText(val.substring(64)),
                            newVal => {
                                return {
                                    "terminal-device": {
                                        "logical-channels": {
                                            channel: {
                                                index: data["pm-channel-index"],
                                                otn: {
                                                    config: {
                                                        "tti-msg-transmit": getAsciiHexForSplitTTI(val, newVal, "oper")
                                                    }
                                                }
                                            }
                                        }
                                    }
                                };
                            },
                            null,
                            null,
                            30,
                            true
                        );
                    } catch (e) {
                        return val;
                    }
                }
            },
            {
                dataIndex: "pm-tti-msg-expected-sapi",
                width: 150,
                render: (state, data) => {
                    const val = data["pm-tti-msg-expected"] ?? "";
                    try {
                        if (readyOnlyRight.disabled) {
                            return <HexComponent value={val.substring(0, 32)} />;
                        }
                        return getEditRander(
                            asciiHexConvertToPlainText(val.substring(0, 32)),
                            newVal => {
                                return {
                                    "terminal-device": {
                                        "logical-channels": {
                                            channel: {
                                                index: data["pm-channel-index"],
                                                otn: {
                                                    config: {
                                                        "tti-msg-expected": getAsciiHexForSplitTTI(val, newVal, "sapi")
                                                    }
                                                }
                                            }
                                        }
                                    }
                                };
                            },
                            null,
                            null,
                            15,
                            true
                        );
                    } catch (e) {
                        return val;
                    }
                }
            },
            {
                dataIndex: "pm-tti-msg-expected-dapi",
                width: 150,
                render: (state, data) => {
                    const val = data["pm-tti-msg-expected"] ?? "";
                    try {
                        if (readyOnlyRight.disabled) {
                            return <HexComponent value={val.substring(32, 64)} />;
                        }
                        return getEditRander(
                            asciiHexConvertToPlainText(val.substring(32, 64)),
                            newVal => {
                                return {
                                    "terminal-device": {
                                        "logical-channels": {
                                            channel: {
                                                index: data["pm-channel-index"],
                                                otn: {
                                                    config: {
                                                        "tti-msg-expected": getAsciiHexForSplitTTI(val, newVal, "dapi")
                                                    }
                                                }
                                            }
                                        }
                                    }
                                };
                            },
                            null,
                            null,
                            15,
                            true
                        );
                    } catch (e) {
                        return val;
                    }
                }
            },
            {
                dataIndex: "pm-tti-msg-expected-oper",
                width: 150,
                render: (state, data) => {
                    const val = data["pm-tti-msg-expected"] ?? "";
                    try {
                        if (readyOnlyRight.disabled) {
                            return <HexComponent value={val.substring(64)} />;
                        }
                        return getEditRander(
                            asciiHexConvertToPlainText(val.substring(64)),
                            newVal => {
                                return {
                                    "terminal-device": {
                                        "logical-channels": {
                                            channel: {
                                                index: data["pm-channel-index"],
                                                otn: {
                                                    config: {
                                                        "tti-msg-expected": getAsciiHexForSplitTTI(val, newVal, "oper")
                                                    }
                                                }
                                            }
                                        }
                                    }
                                };
                            },
                            null,
                            null,
                            30,
                            true
                        );
                    } catch (e) {
                        return val;
                    }
                }
            },
            {
                dataIndex: "pm-tti-msg-recv-sapi",
                width: 180,
                render: (state, data) => {
                    const val = data["pm-tti-msg-recv"];
                    if (val) {
                        return <HexComponent value={val.substring(0, 32)} />;
                    }
                    return state;
                }
            },
            {
                dataIndex: "pm-tti-msg-recv-dapi",
                width: 180,
                render: (state, data) => {
                    const val = data["pm-tti-msg-recv"];
                    if (val) {
                        return <HexComponent value={val.substring(32, 64)} />;
                    }
                    return state;
                }
            },
            {
                dataIndex: "pm-tti-msg-recv-oper",
                width: 180,
                fixed: "right",
                render: (state, data) => {
                    const val = data["pm-tti-msg-recv"];
                    if (val) {
                        return <HexComponent value={val.substring(64)} />;
                    }
                    return state;
                }
            }
        ],
        "lldp-interfaces": [
            {
                dataIndex: "name"
            },
            {
                dataIndex: "enabled",
                render: (state, rowData) => {
                    if (readyOnlyRight.disabled) {
                        return state;
                    }
                    return switchComponent(rowData.name, state, rowData, rowData?.name?.match?.(/-L\d/g), newVal => {
                        return {
                            lldp: {
                                interfaces: {
                                    interface: {
                                        name: rowData.name,
                                        config: {
                                            enabled: newVal
                                        }
                                    }
                                }
                            }
                        };
                    });
                }
            },
            {
                dataIndex: "system-name"
            },
            {
                dataIndex: "system-description"
            },
            {
                dataIndex: "chassis-id",
                title: "Chassis Mac"
            },
            {
                dataIndex: "age"
            },
            {
                dataIndex: "last-update"
            },
            {
                dataIndex: "ttl"
            },
            {
                dataIndex: "port-id"
            },
            {
                dataIndex: "management-address"
            }
        ],
        "lldp-state": [
            [
                {dataIndex: "enabled", inputType: "switch"},
                {
                    dataIndex: "hello-timer",
                    unit: "seconds",
                    inputType: "input",
                    disabled: true
                },
                {
                    dataIndex: "system-name",
                    inputType: "input",
                    disabled: true
                }
            ],
            [
                {
                    dataIndex: "system-description",
                    inputType: "input",
                    disabled: true
                },
                {
                    dataIndex: "chassis-id",
                    title: "Chassis Mac",
                    inputType: "input",
                    disabled: true
                }
            ]
        ]
    };

    const operationConfig = {
        power: [
            {
                label: "Modify",
                disabled: v => {
                    return (
                        readyOnlyRight.disabled ||
                        (v["overhigh-input-threshold-15min"] === NULL_VALUE &&
                            v["overhigh-output-threshold-15min"] === NULL_VALUE &&
                            v["overlow-input-threshold-15min"] === NULL_VALUE &&
                            v["overlow-output-threshold-15min"] === NULL_VALUE)
                    );
                },
                async onClick() {
                    const originValue = {...this};
                    const columnsCfg = [
                        {
                            dataIndex: "overlow-input-threshold-15min",
                            title: "overlow-input-threshold",
                            inputType: "number",
                            step: 0.01,
                            required: true
                        },
                        {
                            dataIndex: "overhigh-input-threshold-15min",
                            title: "overhigh-input-threshold",
                            inputType: "number",
                            step: 0.01,
                            required: true
                        },
                        {
                            dataIndex: "overlow-output-threshold-15min",
                            title: "overlow-output-threshold",
                            inputType: "number",
                            step: 0.01,
                            required: true
                        },
                        {
                            dataIndex: "overhigh-output-threshold-15min",
                            title: "overhigh-output-threshold",
                            inputType: "number",
                            step: 0.01,
                            required: true
                        }
                    ];
                    openCustomEditForm({
                        title: "Modify",
                        columnNum: 1,
                        columns: columnsCfg.filter(i => originValue[i.dataIndex] !== NULL_VALUE).map(i => [i]),
                        getData: async () => {
                            return originValue;
                        },
                        saveFun: async (diffValue, value) => {
                            const changedKey = Object.keys(diffValue);
                            let thresholdNameKey = originValue["port-name"];
                            let thresholdTypeKey = "PORT";
                            if (originValue["card-name"].startsWith("LINECARD")) {
                                thresholdNameKey = `TRANSCEIVER${thresholdNameKey.substring(
                                    thresholdNameKey.indexOf("-")
                                )}`;
                                thresholdTypeKey = "TRANSCEIVER";
                            }
                            let rs;
                            if (
                                changedKey.includes("overhigh-input-threshold-15min") ||
                                changedKey.includes("overlow-input-threshold-15min")
                            ) {
                                rs = await apiEditRpc({
                                    ne_id: selectNe,
                                    msg: false,
                                    params: {
                                        performance: {
                                            tcas: {
                                                tca: {
                                                    "pm-granularity": "15MIN",
                                                    "pm-parameter": "INPUT-POWER",
                                                    "pm-point": thresholdNameKey,
                                                    "pm-point-type": thresholdTypeKey,
                                                    "threshold-value-high": value["overhigh-input-threshold-15min"],
                                                    "threshold-value-low": value["overlow-input-threshold-15min"]
                                                }
                                            }
                                        }
                                    }
                                });
                                if (rs.apiResult === "fail") {
                                    message.error(gLabelList.save_failed);
                                    return rs;
                                }
                            }
                            if (
                                changedKey.includes("overhigh-output-threshold-15min") ||
                                changedKey.includes("overlow-output-threshold-15min")
                            ) {
                                rs = await apiEditRpc({
                                    ne_id: selectNe,
                                    msg: false,
                                    params: {
                                        performance: {
                                            tcas: {
                                                tca: {
                                                    "pm-granularity": "15MIN",
                                                    "pm-parameter": "OUTPUT-POWER",
                                                    "pm-point": thresholdNameKey,
                                                    "pm-point-type": thresholdTypeKey,
                                                    "threshold-value-high": value["overhigh-output-threshold-15min"],
                                                    "threshold-value-low": value["overlow-output-threshold-15min"]
                                                }
                                            }
                                        }
                                    }
                                });
                                if (rs.apiResult === "fail") {
                                    message.error(gLabelList.save_failed);
                                    return rs;
                                }
                            }
                            message.success(gLabelList.save_success);
                            setTimeout(() => {
                                reloadData();
                            }, 1000);
                            return rs;
                        }
                    });
                }
            }
        ]
    };

    const createDynamicTable = type => {
        if (type === "power") {
            return {
                key: "power",
                label: "Optical Power Management",
                children: (
                    <CustomTable
                        scroll={false}
                        type="power_manager_linecard"
                        initDataSource={powerData}
                        loading={loading}
                        paginationEnable={false}
                        initRowOperation={operationConfig.power}
                    />
                )
            };
        }
        if (type === "lldp") {
            return {
                key: "lldp",
                label: "LLDP",
                style: {flex: 1, display: "flex"},
                children: (
                    <div style={{flex: 1, display: "flex", flexDirection: "column"}}>
                        <div style={{maxWidth: 1500, marginBottom: 14}}>
                            {tableConfig["lldp-state"].map(i => {
                                return (
                                    <Row>
                                        {i.map((item, itemIndex) => {
                                            return (
                                                <Col
                                                    key={`${i.dataIndex}_col`}
                                                    style={{
                                                        width: "calc((100% - 48px) / 3)",
                                                        maxWidth: 440,
                                                        marginLeft: itemIndex === 0 ? 0 : 24,
                                                        overflow: "hidden",
                                                        display: "flex",
                                                        alignItems: "center",
                                                        marginBottom: 24
                                                    }}
                                                >
                                                    <div style={{width: "calc(100% - 280px)"}}>
                                                        {getText(item.title ?? item.dataIndex)}
                                                    </div>
                                                    <div style={{width: 280}}>
                                                        {item.inputType === "switch" && (
                                                            <Switch
                                                                disabled={false}
                                                                checked={lldpData?.[0]?.[item.dataIndex] === "true"}
                                                                onChange={async newVal => {
                                                                    await apiEditRpc({
                                                                        ne_id: selectNe,
                                                                        params: {
                                                                            lldp: {
                                                                                config: {
                                                                                    enabled: newVal
                                                                                }
                                                                            }
                                                                        }
                                                                    });
                                                                    reloadData();
                                                                }}
                                                            />
                                                        )}
                                                        {item.inputType === "input" && (
                                                            <Input
                                                                style={{width: 280}}
                                                                key={`${item.dataIndex}_value`}
                                                                disabled={item.disabled}
                                                                value={lldpData?.[0]?.[item.dataIndex]}
                                                            />
                                                        )}
                                                    </div>
                                                </Col>
                                            );
                                        })}
                                    </Row>
                                );
                            })}
                        </div>
                        <CustomTable
                            scroll={false}
                            type="lldp-interfaces"
                            initColumns={tableConfig["lldp-interfaces"].map(i => ({
                                ...i,
                                title:
                                    (labelList[i.title] ??
                                        getText(i.title) ??
                                        labelList[i.dataIndex] ??
                                        getText(i.dataIndex)) + (i.unit ? ` (${i.unit})` : "")
                            }))}
                            initDataSource={lldpData?.[1] ?? []}
                            loading={loading}
                        />
                    </div>
                )
            };
        }
        return {
            key: type,
            style: {flex: 1, display: "flex", flexDirection: "column"},
            label: labelList[`${type}_manager`],
            children: (
                <>
                    {type === "wss" && (
                        <div style={{marginBottom: 10}}>
                            <Space>
                                <DebounceButton
                                    icon={<Icon component={addCommonIcon} />}
                                    disabled={readyOnlyRight.disabled}
                                    onClick={() => {
                                        openModalCreate({
                                            categoryName: "frequency-channel",
                                            type: "5",
                                            title: `${gLabelList.create} ${getText("frequency-channel")}`,
                                            keys: [selectCardRef.current],
                                            ne_id: selectNe,
                                            callback: () => {
                                                reloadData();
                                            }
                                        });
                                    }}
                                >
                                    {labelList.create}
                                </DebounceButton>
                                <Button
                                    icon={<Icon component={refreshEnabledIcon} />}
                                    onClick={() => {
                                        reloadData();
                                    }}
                                >
                                    {labelList.refresh}
                                </Button>
                            </Space>
                        </div>
                    )}
                    <CustomTable
                        type={`${type}_manager`}
                        scroll={false}
                        initColumns={tableConfig[type].map(i => ({
                            ...i,
                            title: getText(i.title ?? i.dataIndex) + (i.unit ? ` (${i.unit})` : "")
                        }))}
                        initDataSource={DataMapping[type]}
                        loading={loading}
                    />
                </>
            )
        };
    };

    const getDataFromAllData = async () => {
        const configRs = await netconfGetByXML({
            ne_id: selectNe,
            type: "get-config",
            msg: true,
            xml: {
                components: {
                    $: {
                        xmlns: "http://openconfig.net/yang/platform"
                    },
                    component: {}
                },
                "terminal-device": {
                    $: {
                        xmlns: "http://openconfig.net/yang/terminal-device"
                    },
                    "logical-channels": {
                        channel: {}
                    }
                },
                interfaces: {
                    $: {
                        xmlns: "http://openconfig.net/yang/interfaces"
                    },
                    interface: {}
                }
            }
        });
        const stateRs = await getStateData({
            ne_id: selectNe
        });
        return {config: configRs, state: stateRs?.data?.value?.data?.["state-data"]};
    };

    const loadPortManagerData = async businessPortList => {
        try {
            const cardMach = selectCardRef.current;
            getDataFromAllData().then(rs => {
                objectGet("nms:provision", {}).then(prRs => {
                    const services = prRs.documents;

                    const _dataList = [];
                    const components = rs?.config?.components?.component ?? [];
                    const channels = rs?.config?.["terminal-device"]?.["logical-channels"]?.channel ?? [];
                    const interfaces = rs?.config?.interfaces?.interface ?? [];
                    components.forEach(component => {
                        if (component.name.startsWith("LINECARD-")) {
                            if (cardMach && component.name !== cardMach) {
                                return true;
                            }
                            const card = {
                                card: component.name,
                                "actual-vendor-type":
                                    component?.config?.["vendor-type-preconf"] ??
                                    getDeviceStateValue(rs.state, component.name, "actual-vendor-type")
                            }; // card
                            const cardNameKeys = `${component.name.replace("LINECARD", "")}-`;
                            const ports = components.filter(cp => cp.name.startsWith(`PORT${cardNameKeys}`));
                            ports?.forEach(portComponent => {
                                // const portIndex = portComponent.name.split("-").pop();
                                if (!businessPortList.includes(portComponent.name)) {
                                    return true;
                                }
                                const port = {
                                    ...card,
                                    port: portComponent.name,
                                    key: portComponent.name,
                                    // "port-display": `${selectCardRef.current.linecard_cardType.split("-")[0]}-${
                                    //     portIndex.toString().charAt(0) === "C" ? "CLIENT" : "LINE"
                                    // }-${portIndex.toString().substring(1)}`,
                                    "signal-type":
                                        portComponent.port?.config?.["layer-protocol-name"]?._?.split(":")?.pop() ??
                                        getDeviceStateValue(rs.state, portComponent.name, "layer-protocol-name"),
                                    "reverse-mode":
                                        getDeviceStateValue(rs.state, portComponent.name, "reverse-mode") ?? ""
                                };

                                const transceiverName = portComponent.name.replace("PORT", "TRANSCEIVER");
                                const transceiverList = components.find(i => i.name === transceiverName); // transceiver
                                if (transceiverList) {
                                    port["module-type"] =
                                        getDeviceStateValue(rs.state, portComponent.name, "actual-vendor-type") ??
                                        transceiverList?.config?.["actual-vendor-type"] ??
                                        NULL_VALUE;
                                    port["laser-enable"] = transceiverList.transceiver?.config?.enabled;
                                    // port["laser-enable"] =
                                    //     getDeviceStateValue(rs.state, portComponent.name, "enabled") ?? NULL_VALUE;
                                } else {
                                    port["module-type"] = NULL_VALUE;
                                }
                                // loopback
                                const _portNameInfo = portComponent.name.replace("PORT", "");
                                channels.forEach(c => {
                                    const channelName = c.config?.description ?? c.state?.description;
                                    if (
                                        channelName &&
                                        channelName.endsWith(_portNameInfo) &&
                                        c.config?.["loopback-mode"]
                                    ) {
                                        port["channel-index"] = c.index;
                                        port.loopback = getDeviceStateValue(
                                            rs.state,
                                            portComponent.name,
                                            "loopback-mode"
                                        );
                                        return false;
                                    }
                                });
                                if (port.port.indexOf("L") > -1) {
                                    const ochPort = portComponent.name.replace("PORT", "OCH");
                                    port.modulation = getDeviceStateValue(rs.state, ochPort, "operational-mode");
                                    port.wavelength = components.reduce((acc, item) => {
                                        if (
                                            item["optical-channel"] &&
                                            item["optical-channel"].config &&
                                            item.name === ochPort
                                        ) {
                                            acc.push(item["optical-channel"].config.frequency);
                                        }
                                        return acc;
                                    }, []);
                                    const frequencyValue = frequencyOptions.reduce((acc, item) => {
                                        if (item?.children) {
                                            const filteredChildren = item.children.filter(
                                                labal =>
                                                    labal.label.includes((port.wavelength / 1000000).toFixed(2)) ||
                                                    labal.label.includes((port.wavelength / 1000000).toFixed(4))
                                            );

                                            if (filteredChildren.length > 0) {
                                                acc = filteredChildren;
                                            }
                                        }
                                        return acc;
                                    }, {});
                                    port.wavelength = frequencyValue?.[0]?.label;
                                    port["target-output-power"] = getDeviceStateValue(
                                        rs.state,
                                        ochPort,
                                        "target-output-power"
                                    );
                                    port["pre-fec"] = getDeviceStateValue(rs.state, ochPort, "pre-fec-ber");
                                    port["post-fec"] = getDeviceStateValue(rs.state, ochPort, "post-fec-ber");
                                    channels.forEach(c => {
                                        const channelName = c.config?.description ?? c.state?.description;
                                        if (
                                            channelName &&
                                            channelName.endsWith(_portNameInfo) &&
                                            c.otn &&
                                            c.config?.description.startsWith("OTUC")
                                        ) {
                                            port["signal-type"] = c.config?.description?.split("-")?.[0];
                                            return false;
                                        }
                                    });
                                    port["port-used"] = services.find(
                                        service =>
                                            service.value.type === "och" &&
                                            ((service.value.a.ne_id === selectNe &&
                                                service.value.a.port === port.port) ||
                                                (service.value.z.ne_id === selectNe &&
                                                    service.value.z.port === port.port))
                                    )
                                        ? "True"
                                        : "False";
                                } else {
                                    port.wavelength = NULL_VALUE;
                                    port["target-output-power"] = NULL_VALUE;
                                    port["pre-fec"] = NULL_VALUE;
                                    port["post-fec"] = NULL_VALUE;
                                    port["port-used"] = services.find(
                                        service =>
                                            service.value.type === "client" &&
                                            service.value.ne.find(
                                                neInfo => neInfo.ne_id === selectNe && neInfo.port === port.port
                                            )
                                    )
                                        ? "True"
                                        : "False";
                                }
                                // interface
                                const _interface = interfaces.filter(i => i.name === `INTERFACE${_portNameInfo}`);
                                if (_interface.length > 0) {
                                    let subObj;
                                    if (_interface[0].fc) {
                                        subObj = _interface[0].fc;
                                        port.interface_type = "fc";
                                    } else if (_interface[0].sdh) {
                                        subObj = _interface[0].sdh;
                                        port.interface_type = "sdh";
                                    } else if (_interface[0].otn) {
                                        subObj = _interface[0].otn;
                                        port.interface_type = "otn";
                                    } else if (_interface[0].ethernet) {
                                        port.interface_type = "ethernet";
                                        subObj = _interface[0].ethernet;
                                    }
                                    if (subObj) {
                                        port.fec =
                                            subObj?.config?.["client-fec"]?._ ??
                                            getDeviceStateValue(rs.state, portComponent.name, "client-fec");
                                        let _als = getDeviceStateValue(rs.state, portComponent.name, "client-als");
                                        port.als_org = _als;
                                        if (!_als) {
                                            _als = "";
                                        } else if (_als === "LASER_SHUTDOWN") {
                                            _als = "DISABLED";
                                        } else if (_als === "ETHERNET") {
                                            _als = "ENABLED";
                                        }
                                        port.als = _als;
                                    }
                                    port["interface-name"] = _interface[0].name;
                                }
                                _dataList.push(port);
                            });
                        }
                    });
                    _dataList.sort((a, b) => {
                        if (a.card !== b.card) {
                            return a.card.localeCompare(b.card, "ZH-CN", {numeric: true});
                        }
                        return a.port.localeCompare(b.port, "ZH-CN", {numeric: true});
                    });
                    setPortData(_dataList);
                    setLoading(false);
                });
            });
        } catch (e) {
            setPortData([]);
            setLoading(false);
            console.log(e);
        }
    };

    const loadTTIData = async businessPortList => {
        try {
            const cardMach = selectCardRef.current;
            getDataFromAllData().then(rs => {
                const _dataList = [];
                const components = rs?.config?.components?.component ?? [];
                const channels = rs?.config?.["terminal-device"]?.["logical-channels"]?.channel ?? [];
                components.forEach(component => {
                    if (component.name.startsWith("LINECARD-")) {
                        if (cardMach && component.name !== cardMach) {
                            return true;
                        }
                        const card = {
                            card: component.name,
                            "actual-vendor-type":
                                component?.config?.["vendor-type-preconf"] ?? component?.state?.["actual-vendor-type"]
                        }; // card
                        const cardNameKeys = `${component.name.replace("LINECARD", "")}-`;
                        const ports = components.filter(cp => cp.name.startsWith(`PORT${cardNameKeys}`));
                        ports?.forEach(portComponent => {
                            if (!portComponent) {
                                return true;
                            }
                            if (!businessPortList.includes(portComponent.name)) {
                                return true;
                            }
                            const port = {
                                ...card,
                                port: portComponent.name,
                                key: portComponent.name,
                                "signal-type":
                                    portComponent.port?.config?.["layer-protocol-name"]?._?.split(":")?.pop() ??
                                    getDeviceStateValue(rs.state, portComponent.name, "layer-protocol-name")
                            };
                            const _portNameInfo = portComponent.name.substring(portComponent.name.indexOf("-"));
                            for (let i = 0; i < channels.length; i++) {
                                const c = channels[i];
                                const channelName = c.config?.description ?? c.state?.description;
                                if (channelName.endsWith(_portNameInfo) && c.otn) {
                                    if (c.config?.description?.startsWith("OTU")) {
                                        port.otn_signal = true;
                                        port["sm-tti-msg-expected"] = c.otn.config["tti-msg-expected"];
                                        port["sm-tti-msg-recv"] = getDeviceStateValue(
                                            rs.state,
                                            portComponent.name,
                                            "sm-tti-msg-recv"
                                        );
                                        port["sm-tti-msg-transmit"] = c.otn.config["tti-msg-transmit"];
                                        port["sm-channel-index"] = c.index;
                                        // eslint-disable-next-line prefer-destructuring
                                        port["signal-type"] = c.config.description.split("-")[0];
                                        break;
                                    } else {
                                        port.otn_signal = false;
                                        port["pm-tti-msg-expected"] = c.otn?.config?.["tti-msg-expected"];
                                        port["pm-tti-msg-recv"] = getDeviceStateValue(
                                            rs.state,
                                            portComponent.name,
                                            "pm-tti-msg-recv"
                                        );
                                        port["pm-tti-msg-transmit"] = c.otn?.config?.["tti-msg-transmit"];
                                        port["pm-channel-index"] = c.index;
                                    }
                                }
                            }
                            _dataList.push(port);
                        });
                    }
                });
                _dataList.sort((a, b) => a.port.localeCompare(b.port, "ZH-CN", {numeric: true}));
                setTtiData(_dataList);
                setLoading(false);
            });
        } catch (e) {
            console.log(e);
            setTtiData([]);
            setLoading(false);
        }
    };

    const loadLLDPData = async businessPortList => {
        try {
            // const cardMach = selectCardRef.current;
            netconfGetByXML({
                msg: true,
                ne_id: selectNe,
                xml: {
                    lldp: {
                        $: {
                            xmlns: "http://openconfig.net/yang/lldp"
                        }
                    }
                }
            }).then(rs => {
                let interfaces = [];
                convertToArray(rs?.lldp?.interfaces?.interface).map(i => {
                    const it = {
                        name: i.name,
                        enabled: i.state?.enabled
                    };
                    if (i.neighbors?.neighbor) {
                        convertToArray(i.neighbors.neighbor).map(n => {
                            interfaces.push({...it, ...n.state});
                        });
                    } else {
                        interfaces.push(it);
                    }
                });
                const filterInfo = businessPortList.map(i => i.split("PORT")[1]);
                interfaces = interfaces.map(i => ({...i, key: i.name}));
                const state = rs?.lldp?.state;
                // state.enable = rs?.lldp?.state?.enabled;
                setLldpData([state, interfaces.filter(i => filterInfo.includes(i.name.split("INTERFACE")[1]))]);
                setLoading(false);
            });
        } catch (e) {
            setLoading(false);
            setLldpData([]);
            console.log(e);
        }
    };

    const loadOpticalPowerData = async businessPortList => {
        try {
            if (!selectNe) {
                setPowerData([]);
                return;
            }
            const thresholdValue = (await objectGet("ne:5:tca", {ne_id: selectNe})).documents;
            const stateData =
                (
                    await getStateData({
                        ne_id: selectNe
                    })
                )?.data?.value?.data?.["state-data"] ?? {};
            objectGet("ne:5:component", {
                ne_id: selectNe,
                parent: "CHASSIS-1"
            }).then(rs => {
                const _data = [];
                rs.documents.forEach(_card => {
                    const cardType = _card.value.data.name.split("-")[0];
                    if (["LINECARD"].includes(cardType) && _card.value.data.config?.["vendor-type-preconf"]) {
                        const cardName = _card.value.data.name;
                        if (cardName !== selectCardRef.current) {
                            return true;
                        }
                        _card.value.data.subcomponents?.subcomponent?.forEach?.(_port => {
                            const portName = _port.name;
                            if (!businessPortList.includes(portName)) {
                                return true;
                            }
                            let thresholdNameKey = portName;
                            if (cardType === "LINECARD") {
                                thresholdNameKey = `TRANSCEIVER${thresholdNameKey.substring(
                                    thresholdNameKey.indexOf("-")
                                )}`;
                            }
                            const thresholdValue_15min_IN =
                                getThresholdValue(thresholdValue, thresholdNameKey, "INPUT-POWER", "15MIN")?.value
                                    ?.data ?? {};
                            const thresholdValue_15min_OUT =
                                getThresholdValue(thresholdValue, thresholdNameKey, "OUTPUT-POWER", "15MIN")?.value
                                    ?.data ?? {};
                            _data.push({
                                "ne-name": neNameMap[selectNe],
                                ne_id: selectNe,
                                "card-type": _card.value.data?.state?.["actual-vendor-type"] ?? cardType,
                                "card-name": cardName,
                                "slot-no": cardName.split("-")[2],
                                "port-name": portName,
                                "input-optical-power": getStateValue(cardType, stateData, portName, "input-power"),
                                "output-optical-power": getStateValue(cardType, stateData, portName, "output-power"),
                                "overlow-input-threshold-15min": formatThreshold(
                                    thresholdValue_15min_IN?.["threshold-value-low"]
                                ),
                                "overhigh-input-threshold-15min": formatThreshold(
                                    thresholdValue_15min_IN?.["threshold-value-high"]
                                ),
                                "overlow-output-threshold-15min": formatThreshold(
                                    thresholdValue_15min_OUT?.["threshold-value-low"]
                                ),
                                "overhigh-output-threshold-15min": formatThreshold(
                                    thresholdValue_15min_OUT?.["threshold-value-high"]
                                )
                            });
                        });
                    }
                });
                _data.sort((a, b) => (a["slot-no"] >= b["slot-no"] ? 1 : -1));
                setPowerData(_data.map((i, index) => ({...i, index: index + 1})));
                setLoading(false);
            });
        } catch (e) {
            setPowerData([]);
            setLoading(false);
            console.log(e);
        }
    };

    const loadData = async () => {
        objectGet("nms:provision", {type: "client"}).then(rs => {
            if (rs.documents.length === 0) {
                setPowerData([]);
                setLldpData([]);
                setPortData([]);
                setTtiData([]);
                return;
            }
            const neInfoSet = new Set();
            rs.documents.forEach(service => {
                if (selectNeRef.current === service.value.a.ne_id && selectCardRef.current === service.value.a.card) {
                    neInfoSet.add(`${service.value.a.ne_id}_${service.value.a.port}`);
                }
                if (selectNeRef.current === service.value.z.ne_id && selectCardRef.current === service.value.z.card) {
                    neInfoSet.add(`${service.value.z.ne_id}_${service.value.z.port}`);
                }
            });
            const businessPortList = Array.from(neInfoSet).map(i => i.split("_")[1]);
            setLoading(true);
            if (!activeTab || activeTab === "port") {
                loadPortManagerData(businessPortList).then();
            } else if (activeTab === "otn-tti") {
                loadTTIData(businessPortList).then();
            } else if (activeTab === "lldp") {
                loadLLDPData(businessPortList).then();
            } else if (activeTab === "power") {
                loadOpticalPowerData(businessPortList).then();
            }
        });
    };

    const reloadData = () => {
        loadData(false).then();
    };

    useEffect(() => {
        setPowerData([]);
        setLldpData([]);
        setPortData([]);
        setTtiData([]);
        if (!selectNe || !selectCard) {
            return;
        }
        loadData(true).then();
    }, [activeTab, selectNe, selectCard]);

    return (
        <div className={styles.container}>
            {getFilterPanel()}
            <div className={classNames([styles.down, styles.service_optics_down])}>
                <Tabs
                    destroyInactiveTabPane
                    onTabClick={tab => {
                        setActiveTab(tab);
                        activeTabRef.current = tab;
                    }}
                    items={["port", "power", "otn-tti", "lldp"].map(i => createDynamicTable(i))}
                />
            </div>
        </div>
    );
};

export default BusinessList;
