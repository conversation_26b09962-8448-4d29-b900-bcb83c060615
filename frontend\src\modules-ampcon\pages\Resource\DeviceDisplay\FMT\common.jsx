import {
    Button,
    Col,
    Space,
    Input,
    InputNumber,
    Divider,
    Empty,
    Form,
    message,
    Modal,
    Row,
    Select,
    Spin,
    Table,
    Typography
} from "antd";
import React, {Fragment, useState, useEffect} from "react";
import dayjs from "dayjs";
import EmptyPic from "@/assets/images/App/empty.png";
import {
    queryFMTConfig,
    modifyFMTConfig,
    modifyFMTPortNote,
    getFMTDevicePort,
    getFMTDeviceSinglePort
} from "@/modules-ampcon/apis/fmt";
import Icon, {EditOutlined} from "@ant-design/icons";
import {DebounceButton, NULL_VALUE} from "../utils";
import {editTableIcon, exportDisabledSvg, exportSvg, refreshDisabledSvg, refreshSvg} from "@/utils/common/iconSvg";

const {Paragraph} = Typography;
/**
    OA板卡类型小类:
    FMT20PA-EDFA
    FMT17BA-EDFA
    FMT26PA-51EDFA
    FMT17BA-51EDFA
    FMT22BA-EDFA
    FMTPA-Array
    FMTBA-Array
    HPA
    FS-SOA
    EDFA-LA
* */

const CommonInputBtn = ({label, name, labelCol, wrapperCol, addonAfter, required, form, modifyData, reference}) => {
    const [contentActive, setContentActive] = useState(false);
    const [btnContent, setBtnContent] = useState("Set");
    const [btnLoading, setBtnLoading] = useState(false);
    const handleClick = async () => {
        if (!contentActive) {
            setContentActive(true);
            setBtnContent("Send");
        } else {
            setBtnLoading(true);
            const modifyFMTData = {
                ...modifyData,
                value: form.getFieldValue(name) > 0 ? `+${form.getFieldValue(name)}` : form.getFieldValue(name),
                key: `${name}`
            };
            await modifyFMTConfig(modifyFMTData).then(rs => {
                try {
                    if (rs.errorCode === 0) {
                        message.success("Update FMT config success");
                    }
                } catch (e) {
                    message.error(e);
                } finally {
                    setBtnLoading(false);
                    setContentActive(false);
                    setBtnContent("Set");
                }
            });
        }
    };
    return (
        <Row>
            <Col span={17}>
                <Form.Item
                    labelCol={{span: labelCol}}
                    wrapperCol={{span: wrapperCol}}
                    label={label}
                    name={name}
                    // rules={[{required, message: "This field is required."}]}
                >
                    <InputNumber
                        step={0.01}
                        max={reference ? reference + 3 : undefined}
                        min={reference ? reference - 3 : undefined}
                        formatter={value => (value && Number(value) > 0 ? `+${value}` : value)}
                        parser={value => value.replace(/[^\d.-]/g, "")}
                        addonAfter={addonAfter}
                        style={{width: "280px"}}
                        disabled={!contentActive}
                    />
                </Form.Item>
            </Col>
            <Col span={4} offset={1}>
                <Button type="primary" style={{width: 98}} onClick={handleClick} loading={btnLoading}>
                    {btnContent}
                </Button>
            </Col>
        </Row>
    );
};
const CommonSelectBtn = ({label, name, labelCol, wrapperCol, required, form, modifyData, options = []}) => {
    const [contentActive, setContentActive] = useState(false);
    const [btnContent, setBtnContent] = useState("Set");
    const [btnLoading, setBtnLoading] = useState(false);

    const handleClick = async () => {
        if (!contentActive) {
            setContentActive(true);
            setBtnContent("Send");
        } else {
            setBtnLoading(true);
            const modifyFMTData = {
                ...modifyData,
                value: form.getFieldValue(name),
                key: `${name}`
            };
            await modifyFMTConfig(modifyFMTData).then(rs => {
                try {
                    if (rs.errorCode === 0) {
                        message.success("Update FMT config success");
                    }
                } catch (e) {
                    message.error(e);
                } finally {
                    setBtnLoading(false);
                    setContentActive(false);
                    setBtnContent("Set");
                }
            });
        }
    };

    return (
        <Row>
            <Col span={17}>
                <Form.Item
                    labelCol={{span: labelCol}}
                    wrapperCol={{span: wrapperCol}}
                    key={name}
                    label={label}
                    name={name}
                    // rules={[{required, message: "This field is required."}]}
                >
                    <Select style={{width: "280px"}} disabled={!contentActive}>
                        {options.map((option, index) => (
                            <Select.Option key={index} value={option.value}>
                                {option.label}
                            </Select.Option>
                        ))}
                    </Select>
                </Form.Item>
            </Col>
            <Col span={4} offset={1}>
                <Button type="primary" style={{width: 98}} onClick={handleClick} loading={btnLoading}>
                    {btnContent}
                </Button>
            </Col>
        </Row>
    );
};

const EDFABoardTypeItems = ({ItemType, form, modifyData, reference}) => {
    switch (ItemType) {
        case "power":
            return (
                <>
                    {["rx", "tx"].map(type => (
                        <CommonInputBtn
                            key={type}
                            label={type === "rx" ? "Input warning threshold" : "Output warning threshold"}
                            name={type === "rx" ? "rx_power_alarm_threshold" : "tx_power_alarm_threshold"}
                            addonAfter="dBm"
                            labelCol={9}
                            wrapperCol={15}
                            required
                            form={form}
                            modifyData={modifyData}
                        />
                    ))}
                </>
            );
        case "edfa":
            return (
                <>
                    <CommonInputBtn
                        label="Gain Adjustment"
                        name="gain_adjustment"
                        labelCol={9}
                        wrapperCol={15}
                        addonAfter="dB"
                        required
                        form={form}
                        modifyData={modifyData}
                        reference={parseFloat(reference)}
                    />
                    <CommonSelectBtn
                        label="Pump1 State"
                        name="pump1_state"
                        labelCol={9}
                        wrapperCol={15}
                        required
                        form={form}
                        modifyData={modifyData}
                        options={[
                            {label: "Open", value: "0"},
                            {label: "Close", value: "1"}
                        ]}
                    />
                </>
            );
        default:
            return null;
    }
};

const OLPBoardTypeItems = ({form, modifyData}) => {
    const items = ["r1", "r2", "tx"];

    return (
        <>
            {items.map(type => (
                <CommonInputBtn
                    key={type}
                    label={`${type.toUpperCase()} Alarm threshold`}
                    name={type}
                    labelCol={9}
                    wrapperCol={15}
                    addonAfter="dBm"
                    required
                    form={form}
                    modifyData={modifyData}
                />
            ))}
        </>
    );
};

const OEOBoardTypeItems = ({ItemType, form, modifyData}) => {
    const portList = ["a1", "a2", "b1", "b2", "c1", "c2", "d1", "d2"];
    const [disabledStates, setDisabledStates] = useState(portList.reduce((acc, key) => ({...acc, [key]: true}), {}));
    const [btnLoading, setBtnLoading] = useState(portList.reduce((acc, key) => ({...acc, [key]: false}), {}));
    switch (ItemType) {
        case "power":
            return (
                <>
                    {["a1", "a2", "b1", "b2", "c1", "c2", "d1", "d2"].map(type => (
                        <CommonInputBtn
                            labelCol={9}
                            wrapperCol={15}
                            key={type}
                            label={`Input Alarm ${type.toUpperCase()} threshold`}
                            name={`input_alarm_threshold_${type}`}
                            addonAfter="dBm"
                            required
                            form={form}
                            modifyData={modifyData}
                        />
                    ))}
                </>
            );
        case "oeo":
            const handleSetClick = async key => {
                setDisabledStates(prevState => ({
                    ...prevState,
                    [key]: !prevState[key]
                }));

                if (!disabledStates[key]) {
                    setBtnLoading(prevState => ({
                        ...prevState,
                        [key]: true
                    }));
                    const promises = ["control_mode_", "work_model_", "input_alarm_threshold_"].map(channel => {
                        return modifyFMTConfig({
                            ...modifyData,
                            key: channel + key,
                            value: form.getFieldValue(channel + key)
                        })
                            .then(rs => {
                                if (rs.errorCode === 0) {
                                    return `Update ${channel}${key} success`;
                                }
                                return `${channel}${key} failed: ${rs.errorMessage || "unknown error"}`;
                            })
                            .catch(error => `${channel}${key} failed: ${error.message}`);
                    });

                    const results = await Promise.all(promises);
                    setBtnLoading(prevState => ({
                        ...prevState,
                        [key]: false
                    }));
                    message.info({
                        content: (
                            <div style={{textAlign: "left"}}>
                                <ul>
                                    {results.map((result, index) => (
                                        <li key={index}>{result}</li>
                                    ))}
                                </ul>
                            </div>
                        )
                    });
                }
            };
            return (
                <>
                    <Row style={{display: "flex", fontWeight: "bold", padding: "10px"}}>
                        <Col style={{marginLeft: 40}}>
                            <div>Control mode</div>
                        </Col>
                        <Col style={{marginLeft: 50}}>
                            <div>Work model</div>
                        </Col>
                        <Col style={{marginLeft: 100}}>
                            <div>Input Alarm threshold</div>
                        </Col>
                    </Row>

                    {portList.map(port => (
                        <Row gutter={8}>
                            <Col span={2} style={{marginTop: "6px"}}>
                                <strong>{port.toUpperCase()}</strong>
                            </Col>
                            <Col span={4} style={{marginRight: 20}}>
                                <Form.Item name={`control_mode_${port}`}>
                                    <Select style={{width: "100%"}} disabled={disabledStates[port]}>
                                        <Select.Option value="1">Open</Select.Option>
                                        <Select.Option value="0">Close</Select.Option>
                                    </Select>
                                </Form.Item>
                            </Col>
                            <Col span={6} style={{marginRight: 20}}>
                                <Form.Item name={`work_model_${port}`}>
                                    <Select style={{width: "100%"}} disabled={disabledStates[port]}>
                                        <Select.Option value="1">Normal Model</Select.Option>
                                        <Select.Option value="2">Bypass Model</Select.Option>
                                        <Select.Option value="3">Loopback Model</Select.Option>
                                    </Select>
                                </Form.Item>
                            </Col>
                            <Col span={6} style={{marginRight: 8}}>
                                <Form.Item name={`input_alarm_threshold_${port}`}>
                                    <InputNumber step={0.01} addonAfter="dBm" disabled={disabledStates[port]} />
                                </Form.Item>
                            </Col>
                            <Col span={3}>
                                <Button
                                    type="primary"
                                    style={{width: 98}}
                                    onClick={() => handleSetClick(port)}
                                    loading={btnLoading[port]}
                                >
                                    {disabledStates[port] ? "Set" : "Send"}
                                </Button>
                            </Col>
                        </Row>
                    ))}
                </>
            );
        default:
            return null;
    }
};

const VOABoardTypeItems = ({form, modifyData}) => {
    return (
        <>
            {["voa1", "voa2", "voa3", "voa4"].map(type => (
                <CommonInputBtn
                    labelCol={9}
                    wrapperCol={15}
                    key={type}
                    label={`${type.toUpperCase()} threshold`}
                    name={`${type}_threshold`}
                    addonAfter="dBm"
                    required
                    form={form}
                    modifyData={modifyData}
                />
            ))}
        </>
    );
};

const OPDBoardTypeItems = ({form, modifyData}) => {
    return (
        <>
            {Array.from({length: 16}, (_, index) => {
                const item = (index + 1).toString();
                return (
                    <CommonSelectBtn
                        labelCol={9}
                        wrapperCol={15}
                        key={item}
                        label={`Wavelength${item}`}
                        name={`wavelength_${item}`}
                        addonAfter="nm"
                        required
                        form={form}
                        modifyData={modifyData}
                        options={[
                            {value: "0", label: "1310"},
                            {value: "1", label: "1550"}
                        ]}
                    />
                );
            })}
        </>
    );
};

const CommonModal = ({
    isModalOpen,
    form,
    setIsModalOpen,
    currentRecord,
    tabType,
    isShowSpin,
    modifyData,
    reference
}) => {
    return (
        <Modal
            title={
                <div>
                    Modify <Divider style={{marginTop: 8, marginBottom: 0}} />
                </div>
            }
            className="ampcon-middle-modal"
            open={isModalOpen}
            destroyOnClose
            onCancel={() => {
                form.resetFields();
                setIsModalOpen(false);
            }}
            onText="OK"
            footer={null}
            styles={{
                body: {
                    maxHeight: "400px",
                    overflowY: "auto"
                }
            }}
        >
            <Spin spinning={isShowSpin} tip="Loading...">
                <Form
                    form={form}
                    initialValues={currentRecord}
                    labelAlign="left"
                    layout="horizontal"
                    onFinish={values => console.log(values)}
                    style={{minHeight: "268px"}}
                >
                    {tabType === "power" &&
                        (() => {
                            if (currentRecord?.type?.includes("EDFA")) {
                                return <EDFABoardTypeItems form={form} ItemType={tabType} modifyData={modifyData} />;
                            }
                            if (currentRecord?.type?.includes("OEO")) {
                                return <OEOBoardTypeItems form={form} ItemType={tabType} modifyData={modifyData} />;
                            }
                            if (currentRecord?.type?.includes("OLP")) {
                                return <OLPBoardTypeItems form={form} modifyData={modifyData} />;
                            }
                            if (currentRecord?.type?.includes("VOA")) {
                                return <VOABoardTypeItems form={form} modifyData={modifyData} />;
                            }
                            if (currentRecord?.type?.includes("OPD")) {
                                return <OPDBoardTypeItems form={form} modifyData={modifyData} />;
                            }
                            return null;
                        })()}

                    {tabType === "edfa" && (
                        <EDFABoardTypeItems
                            form={form}
                            ItemType={tabType}
                            modifyData={modifyData}
                            reference={reference}
                        />
                    )}

                    {tabType === "oeo" && <OEOBoardTypeItems form={form} ItemType={tabType} modifyData={modifyData} />}
                </Form>
            </Spin>
        </Modal>
    );
};

const editComponent = (value, rowData, submit) => {
    // if (!value || value === NULL_VALUE) {
    //     return value;
    // }
    return (
        <Paragraph
            style={{margin: 0, display: "flex"}}
            editable={{
                maxLength: 32,
                icon: <Icon component={editTableIcon} />,
                text: value,
                onChange: newVal => {
                    if (!newVal || newVal === value) {
                        return;
                    }
                    if (submit) {
                        submit(newVal);
                        return;
                    }
                },
                triggerType: ["icon", "text"]
            }}
        >
            <div style={{flex: 1}}>{value}</div>
        </Paragraph>
    );
};

export const DynamicTable = ({
    tabType,
    data,
    CardId,
    CardName,
    NeIP,
    NeName = "",
    PortName = "",
    showExportButton = true,
    showRefreshButton = true
}) => {
    const [isModalOpen, setIsModalOpen] = useState("");
    const [currentRecord, setCurrentRecord] = useState();
    const [isShowSpin, setIsShowSpin] = useState(false);
    const [form] = Form.useForm();
    const modifyData = {cardId: CardId, ip: NeIP};
    const reference = CardName ? CardName.match(/FMT(\d+)/)?.[1] : null; // undefined 返回null
    const {ports_data, ...rootData} = data || {};
    const parsedPortsData = ports_data ? JSON.parse(ports_data) : {};
    const refreshDisabled = !CardId;

    const [pagination, setPagination] = useState({
        current: 1,
        pageSize: 10,
        total: Object.keys(parsedPortsData).length
    });

    const [tableData, setTableData] = useState([]);

    useEffect(() => {
        if (data?.ports_data) {
            const parsedPortsData = JSON.parse(data.ports_data);
            const initialData = Object.entries(parsedPortsData).map(([portId, portData]) => ({
                key: portId,
                ...Object.fromEntries(Object.entries(data).map(([key, value]) => [key, value === "" ? "--" : value])),
                ...Object.fromEntries(
                    Object.entries(portData).map(([key, value]) => [key, value === "" ? "--" : value])
                ),
                "NE Name": NeName,
                "NE ID": `${NeIP}:4001`,
                "Card Name": CardName
            }));
            setTableData(initialData);
        } else {
            setTableData([]);
        }
    }, [data]);

    if (!data || !data.ports_data) {
        return <Empty image={EmptyPic} description="No Data" imageStyle={{margin: 0}} />;
    }

    const allKeys = {
        ...rootData,
        ...Object.values(parsedPortsData)[0]
    };

    const filteredKeys = [
        "NE Name",
        "NE ID",
        "Card Name",
        ...Object.keys(allKeys).filter(
            key =>
                key !== "Port Note" &&
                key !== "work_mode" &&
                key !== "temperature" &&
                key !== "serial_number" &&
                key !== "production_date" &&
                key !== "hardware_version" &&
                key !== "card_id" &&
                key !== "device_id" &&
                // key !== "slot_index" &&
                key !== "firmware_version" &&
                key !== "software_version"
        )
    ];

    const getTitle = key => {
        if (key === "slot_index") {
            return "Slot No.";
        }
        if (key === "type") {
            return "Card Type";
        }
        if (key === "Name") {
            return "Port Name";
        }
        if (data.type === "OPD" && key === "Power") {
            return "Optical Power";
        }
        return key.replace(/_/g, " ").replace(/(^|\s)\S/g, function (match) {
            return match.toUpperCase();
        });
    };

    const columns = filteredKeys.map((key, index) => {
        const column = {
            title: getTitle(key),
            dataIndex: key,
            key,
            sorter: (a, b) => {
                if (typeof a[key] === "number" && typeof b[key] === "number") {
                    return a[key] - b[key];
                }
                if (typeof a[key] === "string" && typeof b[key] === "string") {
                    return a[key].localeCompare(b[key]);
                }
                return 0;
            }
        };
        if (tabType === "oeo" && key === "Service Notes") {
            column.render = (value, rowData) => {
                return editComponent(value, rowData, async newVal => {
                    await modifyFMTPortNote({ip: NeIP, cardId: CardId, port: rowData.key, note: newVal}).then(rs => {
                        if (rs && rs.errorCode === 0) {
                            message.success("Update service notes success");
                        } else {
                            message.error("Update service notes failed");
                        }
                    });
                });
            };
        }
        if (data.type === "OPD" && key === "Wavelength") {
            column.render = value => {
                if (value === "0") {
                    return "1310";
                }
                if (value === "1") {
                    return "1550";
                }
                return NULL_VALUE;
            };
        }

        return column;
    });

    let prioritizedColumns = [];
    let removeColumns = [];

    if (tabType === "edfa") {
        prioritizedColumns = [
            "No",
            "Name",
            "Input Optical Power",
            "Output Optical Power",
            "VOA Attenuation Value",
            "VOA Attenuation Expected",
            "EDFA Gain Value",
            "Expected EDFA Gain",
            "Gain Slope"
        ];
        removeColumns = [
            "create_time",
            "modified_time",
            "Laser Switch",
            "NE ID",
            "NE Name",
            "Card Name",
            "Input Warning Threshold",
            "Output Warning Threshold"
        ];
    } else if (tabType === "oeo") {
        prioritizedColumns = [
            "No",
            "Name",
            "Module Wavelength",
            "Input Optical Power",
            "Output Optical Power",
            "Input Alarm Threshold",
            "Transmission Distance",
            "Module Temperature",
            "Rate",
            "Service Notes"
        ];
        removeColumns = ["create_time", "modified_time", "Work Mode", "NE ID", "NE Name", "Card Name"];
    } else if (tabType === "power") {
        if (data.type === "OEO") {
            prioritizedColumns = [
                "No",
                "NE Name",
                "NE ID",
                "type",
                "Card Name",
                "slot_index",
                "Name",
                "Input Optical Power",
                "Output Optical Power",
                "Input Alarm Threshold"
            ];
            removeColumns = [
                "model",
                "Module Temperature",
                "Module Wavelength",
                "Rate",
                "Service Notes",
                "Module State",
                "Transmission Distance",
                "Work Mode",
                "create_time",
                "modified_time"
            ];
        } else if (data.type === "OLP") {
            prioritizedColumns = [
                "No",
                "NE Name",
                "NE ID",
                "type",
                "Card Name",
                "slot_index",
                "Name",
                "VOA Attenuation",
                "Optical Power",
                "Optical Power Threshold"
            ];
            removeColumns = [
                "model",
                "Work Status",
                "Manual Switching",
                "Protection Group",
                "create_time",
                "modified_time"
            ];
        } else if (data.type === "EDFA") {
            prioritizedColumns = [
                "No",
                "NE Name",
                "NE ID",
                "type",
                "Card Name",
                "slot_index",
                "Name",
                "VOA Attenuation",
                "Input Optical Power",
                "Output Optical Power",
                "Input Warning Threshold",
                "Output Warning Threshold"
            ];
            removeColumns = [
                "model",
                "EDFA Gain Value",
                "Expected EDFA Gain",
                "VOA Attenuation Value",
                "VOA Attenuation Expected",
                "Gain Slope",
                "Laser Switch",
                "create_time",
                "modified_time"
            ];
        } else if (data.type === "VOA") {
            prioritizedColumns = [
                "No",
                "NE Name",
                "NE ID",
                "type",
                "Card Name",
                "slot_index",
                "Name",
                "Actual Attenuation",
                "Expected Attenuation",
                "Actual Power",
                "Expected Power"
            ];
            removeColumns = ["model", "Work Mode", "Threshold", "create_time", "modified_time"];
        } else if (data.type === "OPD") {
            prioritizedColumns = [
                "No",
                "NE Name",
                "NE ID",
                "type",
                "Card Name",
                "slot_index",
                "Name",
                "VOA Attenuation",
                "Power",
                "Wavelength"
            ];
            removeColumns = ["model", "Route Type", "create_time", "modified_time"];
        }
    }

    removeColumns.forEach(key => {
        const index = columns.findIndex(column => column.key === key);
        if (index !== -1) {
            columns.splice(index, 1);
        }
    });

    columns.sort((a, b) => {
        if (prioritizedColumns) {
            const indexA = prioritizedColumns.indexOf(a.key);
            const indexB = prioritizedColumns.indexOf(b.key);
            if (indexA === -1 && indexB === -1) return 0;
            if (indexA === -1) return 1;
            if (indexB === -1) return -1;
            return indexA - indexB;
        }
    });

    columns.forEach(column => {
        if (column.key === "No") {
            column.title = "Index";
            column.fixed = "left";
        }
        if (column.title.includes("Power") || column.title.includes("Alarm Threshold")) {
            column.title = `${column.title}(dBm)`;
        }
        if (column.title.includes("Temperature")) {
            column.title = `${column.title}(℃)`;
        }
        if (
            column.title.includes("Attenuation") ||
            column.title.includes("Gain Value") ||
            column.title.includes("EDFA Gain")
        ) {
            column.title = `${column.title}(dB)`;
        }
        if (column.title.includes("Gain Slope")) {
            column.title = `${column.title}(dB/40nm)`;
        }
        if (column.title.includes("Wavelength")) {
            column.title = `${column.title}(nm)`;
        }
    });

    columns.push({
        title: "Operation",
        key: "operation",
        fixed: "right",
        render: (_, record, index) => {
            const {current, pageSize} = pagination;
            const isFirstRowOnPage = index === 0;

            return {
                children: isFirstRowOnPage ? (
                    <Button
                        type="link"
                        onClick={() => {
                            if (tabType === "power") {
                                setIsShowSpin(true);
                                queryFMTConfig(NeIP, null, CardId).then(rs => {
                                    const {configData} = rs.data;
                                    let formValues;
                                    if (rs.errorCode === 0) {
                                        if (record?.type?.includes("EDFA")) {
                                            formValues = {
                                                rx_power_alarm_threshold: Number(configData.rx_power_alarm_threshold),
                                                tx_power_alarm_threshold: Number(configData.tx_power_alarm_threshold)
                                            };
                                        } else if (record?.type?.includes("OLP")) {
                                            formValues = {
                                                r1: Number(configData.r1),
                                                r2: Number(configData.r2),
                                                tx: Number(configData.tx)
                                            };
                                        } else if (record?.type?.includes("OEO")) {
                                            const keys = ["a1", "a2", "b1", "b2", "c1", "c2", "d1", "d2"];
                                            formValues = keys.reduce((acc, key, index) => {
                                                acc[`input_alarm_threshold_${key}`] = Number(
                                                    configData.input_alarm_threshold_[index]
                                                );
                                                return acc;
                                            }, {});
                                        } else if (record?.type?.includes("VOA")) {
                                            const keys = ["voa1", "voa2", "voa3", "voa4"];
                                            formValues = keys.reduce((acc, key) => {
                                                acc[`${key}_threshold`] = Number(configData[`${key}_threshold`]);
                                                return acc;
                                            }, {});
                                        } else if (record?.type?.includes("OPD")) {
                                            formValues =
                                                !configData || configData.length === 0
                                                    ? {}
                                                    : configData.wavelength_.reduce((acc, value, index) => {
                                                          acc[`wavelength_${index + 1}`] = value;
                                                          return acc;
                                                      }, {});
                                        }
                                        form.setFieldsValue(formValues);
                                        setIsShowSpin(false);
                                    } else {
                                        message.error("Get FMT Config failed");
                                        setIsShowSpin(false);
                                    }
                                });
                            } else if (tabType === "edfa") {
                                setIsShowSpin(true);
                                queryFMTConfig(NeIP, null, CardId).then(rs => {
                                    const {configData} = rs.data;
                                    if (rs.errorCode === 0) {
                                        if (rs.data?.boardInfo?.type?.includes("EDFA")) {
                                            form.setFieldsValue({
                                                gain_adjustment: Number(configData.gain_adjustment),
                                                pump1_state: configData.pump1_state
                                            });
                                        }
                                        setIsShowSpin(false);
                                    }
                                });
                            } else if (tabType === "oeo") {
                                setIsShowSpin(true);
                                queryFMTConfig(NeIP, null, CardId).then(rs => {
                                    const {configData} = rs.data;
                                    if (rs.errorCode === 0) {
                                        if (rs.data?.boardInfo?.type?.includes("OEO")) {
                                            const keys = ["a1", "a2", "b1", "b2", "c1", "c2", "d1", "d2"];
                                            const control_mode = configData.control_mode_ || [];
                                            const input_alarm_threshold = configData.input_alarm_threshold_ || [];
                                            const work_model = configData.work_model_ || [];
                                            const formValues = keys.reduce((acc, key, index) => {
                                                acc[`control_mode_${key}`] = control_mode[index];
                                                acc[`work_model_${key}`] = work_model[index];
                                                acc[`input_alarm_threshold_${key}`] = Number(
                                                    input_alarm_threshold[index]
                                                );
                                                return acc;
                                            }, {});
                                            form.setFieldsValue(formValues);
                                        }
                                        setIsShowSpin(false);
                                    }
                                });
                            }
                            setCurrentRecord(record);
                            setIsModalOpen(true);
                        }}
                    >
                        Modify
                    </Button>
                ) : null,
                props: {
                    rowSpan: isFirstRowOnPage ? pageSize : 0
                }
            };
        }
    });
    const onExportToExcel = () => {
        try {
            if (!tableData?.length) {
                message.warning("No Data");
                return;
            }

            // 获取表头
            const exportColumns = columns.filter(col => !col.hidden && col.title !== "Operation");
            const headers = exportColumns.map(col => col.title).join(",");

            // 构建数据行
            const data = tableData.reduce((res, row) => {
                const lineData = `${columns
                    .filter(col => !col.hidden)
                    .map(col => {
                        const matchValue = row[col.dataIndex];
                        return `"${matchValue ?? ""}"`;
                    })
                    .join(",")}\n`;
                return `${res}${lineData}`;
            }, `\uFEFF${headers}\n`);

            // 创建下载
            const blob = new Blob([data], {type: "text/csv"});
            const downloadLink = document.createElement("a");
            downloadLink.href = URL.createObjectURL(blob);
            const fileName = `${tabType}_${dayjs().format("YYYY-MM-DD_HH:mm:ss")}.csv`;
            downloadLink.download = fileName;
            downloadLink.click();

            message.success("Export Success");
        } catch (error) {
            message.error(`Export Failed：${error.message}`);
        }
    };
    const doRefresh = async type => {
        try {
            setIsShowSpin(true);
            let response;
            if (PortName) {
                response = await getFMTDeviceSinglePort(CardId, PortName);
            } else {
                response = await getFMTDevicePort(CardId, type);
            }

            if (response?.data) {
                let newData;
                if (type === "power") {
                    // Get info section for power type
                    if (PortName) {
                        newData = response.data;
                    } else {
                        newData = response.data.info;
                    }
                } else if (["oeo", "edfa"].includes(type)) {
                    // Get ports_info section for oeo/edfa types
                    newData = response.data.ports_info;
                }

                // Parse ports_data if exists
                const newPortsData = newData?.ports_data ? JSON.parse(newData.ports_data) : {};
                const newDataSource = Object.entries(newPortsData).map(([portId, portData]) => ({
                    key: portId,
                    ...Object.fromEntries(
                        Object.entries(newData).map(([key, value]) => [key, value === "" ? "--" : value])
                    ),
                    ...Object.fromEntries(
                        Object.entries(portData).map(([key, value]) => [key, value === "" ? "--" : value])
                    ),
                    "NE Name": NeName,
                    "NE ID": NeIP,
                    "Card Name": CardName
                }));

                setTableData(newDataSource);
                setPagination(prev => ({
                    ...prev,
                    total: newDataSource.length
                }));
                message.success("Refresh Success");
            } else {
                message.error("Refresh Failed");
            }
        } catch (error) {
            message.error(`Refresh Failed：${error.message}`);
        } finally {
            setIsShowSpin(false);
        }
    };
    return (
        <>
            <div style={{display: "flex", justifyContent: "space-between"}}>
                <Space style={{marginBottom: 24}} size={16}>
                    {showExportButton && (
                        <DebounceButton
                            icon={<Icon component={!tableData.length ? exportDisabledSvg : exportSvg} />}
                            onClick={onExportToExcel}
                            title="Export"
                            disabled={!tableData.length}
                        >
                            Export
                        </DebounceButton>
                    )}
                    {showRefreshButton && (
                        <DebounceButton
                            onClick={() => doRefresh(tabType)}
                            icon={<Icon component={refreshDisabled ? refreshDisabledSvg : refreshSvg} />}
                            disabled={refreshDisabled}
                        >
                            Refresh
                        </DebounceButton>
                    )}
                </Space>
            </div>
            <CommonModal
                isModalOpen={isModalOpen}
                setIsModalOpen={setIsModalOpen}
                tabType={tabType}
                form={form}
                currentRecord={currentRecord}
                isShowSpin={isShowSpin}
                modifyData={modifyData}
                reference={reference}
            />
            <Table
                columns={columns}
                dataSource={tableData}
                bordered
                pagination={{
                    current: pagination.current,
                    pageSize: pagination.pageSize,
                    total: tableData.length,
                    onChange: (page, pageSize) => {
                        setPagination({...pagination, current: page, pageSize});
                    },
                    showSizeChanger: true,
                    pageSizeOptions: ["10", "20", "50", "100"],
                    showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} items`
                }}
                style={{whiteSpace: "nowrap"}}
            />
        </>
    );
};
