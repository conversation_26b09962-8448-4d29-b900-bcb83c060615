import {message, <PERSON><PERSON>, Card, Divider, List, Row, Col, Tabs, DatePicker, Tag, Table, Flex, Form, Checkbox} from "antd";
import {useState, useEffect, useRef, useMemo} from "react";
import {useLocation, useNavigate} from "react-router-dom";
import {useForm} from "antd/es/form/Form";
import Icon from "@ant-design/icons/lib/components/Icon";
// import {settingGreenSvg} from "@/utils/common/iconSvg";
import {LeftOutlined} from "@ant-design/icons";
import {TelemetrySetting, TelemetryCard} from "@/modules-ampcon/pages/Dashboard/Telemetry/telemetry_view";
import {
    fetchInterfaceInfo,
    fetchModulesInfo,
    fetchAIInfo,
    fetchMacTable,
    fetchArpTable
} from "@/modules-ampcon/apis/monitor_api";
import {fetchAllSwitch} from "@/modules-ampcon/apis/dashboard_api";
import {AmpConCustomModalForm, AmpConCustomTelemteryTable} from "@/modules-ampcon/components/custom_table";
import settingGreySvg from "../../Topo/resource/site_grey.svg?react";
import settingGreenSvg from "../../Topo/resource/site_green.svg?react";

const {RangePicker} = DatePicker;

const importAllImages = () => {
    const images = {};
    const modules = import.meta.glob("/src/assets/switchs/*.png");

    // eslint-disable-next-line guard-for-in
    for (const path in modules) {
        const key = path.split("/").pop().replace(".png", "");
        images[key] = modules[path];
    }

    return images;
};

const switchImages = importAllImages();

const usageOptions = {
    cpu: "CPU",
    memory: "Memory"
};

const interfaceCheckboxOptions = {
    "in-octets": "In-Octets",
    "in-pkts": "In-Pkts",
    "in-discards": "In-Discards",
    "in-errors": "In-Errors",
    "in-fcs-errors": "In-Fcs-Errors",
    "out-octets": "Out-Octets",
    "out-pkts": "Out-Pkts",
    "out-discards": "Out-Discards",
    "out-errors": "Out-Errors",
    "out-bits-rate": "Out-Bits-Rate",
    "in-bits-rate": "In-Bits-Rate",
    "out-pkts-rate": "Out-Pkts-Rate",
    "in-pkts-rate": "In-Pkts-Rate"
};

const modulesCheckboxOptions = {
    "output-power": "Output-Power",
    "input-power": "Input-Power",
    "laser-temperature": "Laser-Temperature",
    attenuation: "Output-Power - Input-Power"
};

const aiCheckboxOptions = {
    "ecn-marked-packets": "Ecn-Marked-Packets",
    "send-pfc-pause-frames": "Send-Pfc-Pause-Frames",
    "receive-pfc-pause-frames": "Receive-Pfc-Pause-Frames",
    "pfc-deadlock-monitor-count": "Pfc-Deadlock-Monitor-Count",
    "pfc-deadlock-recovery-count": "Pfc-Deadlock-Recovery-Count"
};

const extractParts = str => {
    const match = str.match(/^([A-Za-z-]+)-(\d+)\/(\d+)\/(\d+)$/);
    if (!match) {
        return {prefix: str, numbers: []};
    }
    const prefix = match[1];
    const numbers = match.slice(2).map(num => parseInt(num, 10));
    return {prefix, numbers};
};

const compareNumbers = (a, b) => {
    const aParts = extractParts(a);
    const bParts = extractParts(b);
    if (aParts.prefix !== bParts.prefix) {
        return aParts.prefix.localeCompare(bParts.prefix);
    }
    for (let i = 0; i < Math.min(aParts.numbers.length, bParts.numbers.length); i++) {
        if (aParts.numbers[i] !== bParts.numbers[i]) {
            return aParts.numbers[i] - bParts.numbers[i];
        }
    }
    return aParts.numbers.length - bParts.numbers.length;
};

const formatNumber = value => (value > 1e8 ? value.toExponential(3) : value || 0);

const SwitchTelemetry = () => {
    const location = useLocation();
    const navigate = useNavigate();
    const [switchInfo, setSwichInfo] = useState([]);
    const [record, setRecord] = useState({});
    const [activeKey, setActiveKey] = useState("switchOverview");

    const tabRef = useRef(null);
    const [tableWidth, setTableWidth] = useState(0);

    const handleResize = () => {
        if (tabRef.current) {
            setTableWidth(tabRef.current.offsetWidth - 50);
        }
    };

    const makeSwitchInfo = async sn => {
        const response = await fetchAllSwitch(
            1,
            10,
            [{field: "sn", filters: [{value: sn, matchMode: "exact"}]}],
            [],
            {}
        );
        if (response.status === 200) {
            const record = response.data[0];
            const data = [
                {
                    title: "SN",
                    value: record.sn
                },
                {
                    title: "Hardware-ID",
                    value: record.hwid
                },
                {
                    title: "MAC Address",
                    value: record.mac_addr
                },
                {
                    title: "Hostname",
                    value: record.host_name
                },
                {
                    title: "IP Address",
                    value: record.mgt_ip
                },
                {
                    title: "Version",
                    value: record.version
                }
            ];
            setSwichInfo(data);
            setRecord(record);
        }
    };

    const items = [
        {
            key: "switchOverview",
            label: "Switch Overview",
            children: <SwitchOverview active={activeKey === "switchOverview"} sn={record.sn} showAI={record.gnmi_ai} />
        },
        {
            key: "portOverview",
            label: "Port Overview",
            children: <PortOverview active={activeKey === "portOverview"} sn={record.sn} tableWidth={tableWidth} />
        },
        {
            key: "modulesOverview",
            label: "Modules Overview",
            children: (
                <ModulesOverview active={activeKey === "modulesOverview"} sn={record.sn} tableWidth={tableWidth} />
            )
        },
        {
            key: "macTableOverview",
            label: "MacTable Overview",
            children: (
                <MacTableOverview active={activeKey === "macTableOverview"} sn={record.sn} tableWidth={tableWidth} />
            )
        },
        {
            key: "arpTableOverview",
            label: "ArpTable Overview",
            children: (
                <ArpTableOverview active={activeKey === "arpTableOverview"} sn={record.sn} tableWidth={tableWidth} />
            )
        },
        record.gnmi_ai === true
            ? {
                  key: "aiOverview",
                  label: "AI Overview",
                  children: <AIOverview active={activeKey === "aiOverview"} sn={record.sn} tableWidth={tableWidth} />
              }
            : null
    ];

    useEffect(() => {
        const pathParts = location.pathname.split("/");
        const sn = pathParts[pathParts.length - 1];
        makeSwitchInfo(sn);
    }, [location.pathname]);

    useEffect(() => {
        handleResize();
        window.addEventListener("resize", handleResize);

        return () => {
            window.removeEventListener("resize", handleResize);
        };
    }, []);

    return (
        <div>
            <a
                onClick={() => {
                    if (import.meta.env.VITE_APP_EXPORT_MODULE === "AmpCon-SUPER") {
                        navigate("/service/switch/switch");
                    } else {
                        navigate("/service/switch");
                    }
                }}
            >
                <Button
                    type="link"
                    icon={<LeftOutlined />}
                    style={{
                        border: "none",
                        fontSize: "14px",
                        color: "#14C9BB",
                        marginBottom: "20px"
                    }}
                >
                    Back
                </Button>
            </a>
            <Card title="Device Information">
                <div
                    style={{
                        display: "flex",
                        flexDirection: "row",
                        justifyContent: "flex-start",
                        alignItems: "center",
                        marginTop: "12px"
                    }}
                >
                    <div style={{marginLeft: "36px"}}>
                        <img src={switchImages[record.platform_model]?.name || switchImages.default_switch.name} alt="Model" />
                        <div style={{display: "flex"}}>
                            <div style={{color: "#929A9E", marginLeft: "8px", marginRight: "8px"}}>Model:</div>
                            <div>{record.platform_model}</div>
                        </div>
                    </div>
                    <Divider type="vertical" style={{height: "100px", marginLeft: "60px", marginRight: "60px"}} />
                    <List
                        dataSource={switchInfo}
                        split={false}
                        grid={{
                            gutter: 2,
                            column: 3
                        }}
                        renderItem={item => (
                            <List.Item key={item.title}>
                                <Row gutter={30}>
                                    <Col span={9}>
                                        <div style={{color: "#929A9E", minWidth: "150px"}}>{item.title}:</div>
                                    </Col>
                                    <Col span={15}>
                                        <div style={{minWidth: "150px"}}>{item.value}</div>
                                    </Col>
                                </Row>
                            </List.Item>
                        )}
                    />
                </div>
            </Card>
            <div ref={tabRef}>
                <Tabs onChange={setActiveKey} items={items} style={{marginTop: "24px"}} />
            </div>
        </div>
    );
};

const getSorter = (dataIndex, isString) => {
    return isString ? (a, b) => a[dataIndex].localeCompare(b[dataIndex]) : (a, b) => a[dataIndex] - b[dataIndex];
};

const SwitchOverview = ({active, sn, showAI}) => {
    const [form] = useForm();
    const [timeRange, setTimeRange] = useState(["", ""]);
    const [counters, setCounters] = useState({
        usage: Object.keys(usageOptions),
        interface: Object.keys(interfaceCheckboxOptions),
        modules: Object.keys(modulesCheckboxOptions),
        ai: []
    });
    const [isSelectCountersModalOpen, setSelectCountersModalOpen] = useState(false);
    const cardRefs = useRef({});
    const [isHovered, setIsHovered] = useState(false);

    const addToRefs = (el, name) => {
        if (el) {
            cardRefs.current[name] = el;
        }
    };

    const onChangeCounters = () => {
        if (form.getFieldsValue().usage !== undefined) {
            setCounters(prevCounters => ({
                ...prevCounters,
                usage: form.getFieldsValue().usage
            }));
        }
        if (form.getFieldsValue().interface !== undefined) {
            setCounters(prevCounters => ({
                ...prevCounters,
                interface: form.getFieldsValue().interface
            }));
        }
        if (form.getFieldsValue().modules !== undefined) {
            setCounters(prevCounters => ({
                ...prevCounters,
                modules: form.getFieldsValue().modules
            }));
        }
        if (showAI && form.getFieldsValue().ai !== undefined) {
            setCounters(prevCounters => ({
                ...prevCounters,
                ai: form.getFieldsValue().ai
            }));
        }
        setSelectCountersModalOpen(false);
    };

    useEffect(() => {
        if (active) {
            Object.keys(cardRefs.current).forEach(key => {
                const ref = cardRefs.current[key];
                if (ref && ref.refreshTelemetry) {
                    ref.refreshTelemetry();
                }
            });
        }
    }, [active]);

    useEffect(() => {
        if (showAI) {
            const newCounters = {
                ...counters,
                ai: Object.keys(aiCheckboxOptions)
            };
            setCounters(newCounters);
            form.setFieldsValue(newCounters);
        } else {
            form.setFieldsValue(counters);
        }
    }, [showAI]);

    const allCards = useMemo(
        () => [
            counters.usage.length > 0 && (
                <TelemetryCard
                    key="usage"
                    name={counters.usage.length === 2 ? "both" : counters.usage[0]}
                    type="usage"
                    timeRange={timeRange}
                    ref={el => addToRefs(el, counters.usage.length === 2 ? "both" : counters.usage[0])}
                    target={sn}
                    cardstyle={{
                        borderColor: "#E7E7E7",
                        borderWidth: "1px",
                        borderStyle: "solid",
                        boxShadow: "none"
                    }}
                />
            ),
            counters.interface.length > 0 &&
                counters.interface.map(interfaceItem => (
                    <TelemetryCard
                        name={interfaceItem}
                        ref={el => addToRefs(el, interfaceItem)}
                        type="interface"
                        timeRange={timeRange}
                        target={sn}
                        cardstyle={{
                            borderColor: "#E7E7E7",
                            borderWidth: "1px",
                            borderStyle: "solid",
                            boxShadow: "none"
                        }}
                    />
                )),

            counters.modules.length > 0 &&
                counters.modules.map(modulesItem => (
                    <TelemetryCard
                        name={modulesItem}
                        ref={el => addToRefs(el, modulesItem)}
                        type="modules"
                        timeRange={timeRange}
                        target={sn}
                        cardstyle={{
                            borderColor: "#E7E7E7",
                            borderWidth: "1px",
                            borderStyle: "solid",
                            boxShadow: "none"
                        }}
                    />
                )),

            counters.ai.length > 0 &&
                counters.ai.map(aiItem => (
                    <TelemetryCard
                        name={aiItem}
                        ref={el => addToRefs(el, aiItem)}
                        type="ai"
                        timeRange={timeRange}
                        target={sn}
                        cardstyle={{
                            borderColor: "#E7E7E7",
                            borderWidth: "1px",
                            borderStyle: "solid",
                            boxShadow: "none"
                        }}
                    />
                ))
        ],
        [counters, timeRange, sn]
    );

    return (
        <>
            <div style={{display: "flex", justifyContent: "flex-end"}}>
                <div style={{display: "flex", alignItems: "center", justifyContent: "center"}}>Time</div>
                <RangePicker
                    showTime={{format: "HH:mm"}}
                    format="YYYY-MM-DD HH:mm"
                    style={{height: "32px", marginLeft: "32px"}}
                    onChange={(_, dateString) => {
                        setTimeRange(dateString);
                    }}
                    disabledDate={current => {
                        const now = new Date();
                        const oneMonthAgo = new Date();
                        oneMonthAgo.setMonth(now.getMonth() - 1);
                        return current && (current > now || current < oneMonthAgo);
                    }}
                />
                <Divider type="vertical" style={{height: "30px", marginLeft: "16px", marginRight: "16px"}} />
                <Button
                    style={{borderColor: isHovered ? "#34DCCF" : "#d9d9d9"}}
                    icon={<Icon component={isHovered ? settingGreenSvg : settingGreySvg} />}
                    onClick={() => setSelectCountersModalOpen(true)}
                    onMouseEnter={() => {
                        setIsHovered(true);
                    }}
                    onMouseLeave={() => {
                        setIsHovered(false);
                    }}
                />
                {/* <Button icon={<Icon component={settingGreenSvg} />} onClick={() => setSelectCountersModalOpen(true)} /> */}
            </div>
            {sn ? (
                <div style={{height: "100%", width: "100%", marginTop: "18px", marginBottom: "18px"}}>
                    <Row gutter={[24, 24]}>
                        {allCards.flat().map((card, index) => (
                            <Col key={index} span={24} xxl={12} style={{display: "flex", justifyContent: "center"}}>
                                {card}
                            </Col>
                        ))}
                    </Row>
                </div>
            ) : null}
            <TelemetrySetting
                form={form}
                isModalOpen={isSelectCountersModalOpen}
                onCancel={() => {
                    form.setFieldsValue(counters);
                    setSelectCountersModalOpen(false);
                }}
                onChange={onChangeCounters}
                showAI={showAI}
            />
        </>
    );
};

const PortOverview = ({active, sn, tableWidth}) => {
    const [formattedData, setFormattedData] = useState([]);

    const columnsConfig = [
        {title: "Port Name", dataIndex: "name", fixed: "left", isString: true, sorter: false},
        {
            title: "Port State",
            dataIndex: "oper_status",
            isString: true,
            render: (_, record) => (
                <Tag className={record.oper_status === "UP" ? "up-tag" : "down-tag"}>{record.oper_status}</Tag>
            )
        },
        {title: "MTU", dataIndex: "mtu", width: 100},
        {title: "Loopback Mode", dataIndex: "loopback_mode", isString: true, width: 100},
        {title: "Port Speed", dataIndex: "port_speed", isString: true},
        {title: "In Bandwidth Utilization", dataIndex: "in_bits_port_speed_usage", isString: true},
        {title: "Out Bandwidth Utilization", dataIndex: "out_bits_port_speed_usage", isString: true},
        {title: "Auto Negotiate", dataIndex: "auto_negotiate", isString: true, width: 100},
        {title: "Mac Addr", dataIndex: "hw_mac_address", isString: true, width: 140},
        {title: "Duplex Mode", dataIndex: "duplex_mode", isString: true},
        {title: "In Broadcast Pkts", dataIndex: "in_broadcast_pkts"},
        {title: "In Discards", dataIndex: "in_discards"},
        {title: "In Errors", dataIndex: "in_errors"},
        {title: "In Fcs Errors", dataIndex: "in_fcs_errors"},
        {title: "In Multicast Pkts", dataIndex: "in_multicast_pkts"},
        {title: "In Octets", dataIndex: "in_octets"},
        {title: "In Pkts", dataIndex: "in_pkts"},
        {title: "In Unicast Pkts", dataIndex: "in_unicast_pkts"},
        {title: "Out Broadcast Pkts", dataIndex: "out_broadcast_pkts"},
        {title: "Out Discards", dataIndex: "out_discards"},
        {title: "Out Errors", dataIndex: "out_errors"},
        {title: "Out Multicast Pkts", dataIndex: "out_multicast_pkts"},
        {title: "Out Octets", dataIndex: "out_octets"},
        {title: "Out Pkts", dataIndex: "out_pkts"},
        {title: "Out Unicast Pkts", dataIndex: "out_unicast_pkts"},
        {title: "In Oversize Frames", dataIndex: "in_oversize_frames"},
        {title: "In Undersize Frames", dataIndex: "in_undersize_frames"},
        {title: "Out Bits Rate", dataIndex: "out_bits_rate"},
        {title: "In Bits Rate", dataIndex: "in_bits_rate"},
        {title: "Out Pkts Rate", dataIndex: "out_pkts_rate"},
        {title: "In Pkts Rate", dataIndex: "in_pkts_rate"},
        {title: "In Frames 64 Octets", dataIndex: "in_frames_64_octets"},
        {title: "In Frames 65-127 Octets", dataIndex: "in_frames_65_127_octets"},
        {title: "In Frames 128-255 Octets", dataIndex: "in_frames_128_255_octets"},
        {title: "In Frames 256-511 Octets", dataIndex: "in_frames_256_511_octets"},
        {title: "In Frames 512-1023 Octets", dataIndex: "in_frames_512_1023_octets"},
        {title: "In Frames 1024-1518 Octets", dataIndex: "in_frames_1024_1518_octets"}
    ];

    const columns = columnsConfig.map(obj => ({
        ...obj,
        sorter: obj.sorter === false ? obj.sorter : getSorter(obj.dataIndex, obj.isString),
        width: obj.width || 120
    }));

    const fetchData = async () => {
        const response = await fetchInterfaceInfo(sn);
        if (response.status !== 200) {
            message.error(response.info);
        } else {
            const formattedData = Object.keys(response.data).map(key => {
                const port = response.data[key];
                const port_speed =
                    port?.ethernet_state?.negotiated_port_speed?.match(/SPEED_(.*)/)?.[1] ||
                    port?.ethernet_state?.port_speed?.match(/SPEED_(.*)/)?.[1] ||
                    "-";
                return {
                    name: port?.config?.name || "-",
                    oper_status: port?.state?.oper_status || "-",
                    mtu: port?.state?.mtu || "-",
                    loopback_mode: port?.state?.loopback_mode || "-",
                    port_speed: port_speed === "2500MB" ? "2.5GB" : port_speed,
                    in_bits_port_speed_usage: port?.ethernet_state?.in_bits_port_speed_usage || "-",
                    out_bits_port_speed_usage: port?.ethernet_state?.out_bits_port_speed_usage || "-",
                    duplex_mode:
                        port?.ethernet_state?.negotiated_duplex_mode || port?.ethernet_state?.duplex_mode || "-",
                    auto_negotiate: port?.ethernet_state?.auto_negotiate || "-",
                    hw_mac_address: port?.ethernet_state?.hw_mac_address || "-",
                    in_broadcast_pkts: formatNumber(port?.state?.in_broadcast_pkts),
                    in_discards: formatNumber(port?.state?.counters?.in_discards),
                    in_errors: formatNumber(port?.state?.counters?.in_errors),
                    in_fcs_errors: formatNumber(port?.state?.counters?.in_fcs_errors),
                    in_multicast_pkts: formatNumber(port?.state?.counters?.in_multicast_pkts),
                    in_octets: formatNumber(port?.state?.counters?.in_octets),
                    in_pkts: formatNumber(port?.state?.counters?.in_pkts),
                    in_unicast_pkts: formatNumber(port?.state?.counters?.in_unicast_pkts),
                    out_broadcast_pkts: formatNumber(port?.state?.counters?.out_broadcast_pkts),
                    out_discards: formatNumber(port?.state?.counters?.out_discards),
                    out_errors: formatNumber(port?.state?.counters?.out_errors),
                    out_multicast_pkts: formatNumber(port?.state?.counters?.out_multicast_pkts),
                    out_octets: formatNumber(port?.state?.counters?.out_octets),
                    out_pkts: formatNumber(port?.state?.counters?.out_pkts),
                    out_unicast_pkts: formatNumber(port?.state?.counters?.out_unicast_pkts),
                    in_oversize_frames: formatNumber(port?.state?.counters?.in_oversize_frames),
                    in_undersize_frames: formatNumber(port?.state?.counters?.in_undersize_frames),
                    out_bits_rate: formatNumber(port?.ethernet_state?.out_bits_rate),
                    in_bits_rate: formatNumber(port?.ethernet_state?.in_bits_rate),
                    out_pkts_rate: formatNumber(port?.ethernet_state?.out_pkts_rate),
                    in_pkts_rate: formatNumber(port?.ethernet_state?.in_pkts_rate),
                    in_frames_1024_1518_octets: formatNumber(
                        port?.ethernet_state?.counters?.[
                            "openconfig_if_ethernet_ext:in_distribution_in_frames_1024_1518_octets"
                        ]
                    ),
                    in_frames_128_255_octets: formatNumber(
                        port?.ethernet_state?.counters?.[
                            "openconfig_if_ethernet_ext:in_distribution_in_frames_128_255_octets"
                        ]
                    ),
                    in_frames_256_511_octets: formatNumber(
                        port?.ethernet_state?.counters?.[
                            "openconfig_if_ethernet_ext:in_distribution_in_frames_256_511_octets"
                        ]
                    ),
                    in_frames_512_1023_octets: formatNumber(
                        port?.ethernet_state?.counters?.[
                            "openconfig_if_ethernet_ext:in_distribution_in_frames_512_1023_octets"
                        ]
                    ),
                    in_frames_64_octets: formatNumber(
                        port?.ethernet_state?.counters?.[
                            "openconfig_if_ethernet_ext:in_distribution_in_frames_64_octets"
                        ]
                    ),
                    in_frames_65_127_octets: formatNumber(
                        port?.ethernet_state?.counters?.[
                            "openconfig_if_ethernet_ext:in_distribution_in_frames_65_127_octets"
                        ]
                    )
                };
            });
            formattedData.sort((a, b) => compareNumbers(a.name, b.name));
            setFormattedData(formattedData);
        }
    };

    useEffect(() => {
        if (active) {
            fetchData();
        }
    }, [active, sn]);

    return (
        <AmpConCustomTelemteryTable
            // style={{height: "100vh"}}
            columnsConfig={columns}
            data={formattedData}
            tableWidth={tableWidth}
        />
    );
};

const AIOverview = ({active, sn, tableWidth}) => {
    const [formattedData, setFormattedData] = useState([]);

    const columnsConfig = [
        {
            title: "Interface Name",
            dataIndex: "name",
            fixed: "left",
            isString: true,
            sorter: false,
            render: (text, record) => {
                return {
                    children: text,
                    props: {
                        rowSpan: record.rowSpan
                    }
                };
            }
        },
        {
            title: "Ecn Marked Packets",
            dataIndex: "ecn_marked_packets",
            render: (text, record) => {
                return {
                    children: text,
                    props: {
                        rowSpan: record.rowSpan
                    }
                };
            }
        },
        {
            title: "Ecn Marked Packets Rate",
            dataIndex: "ecn_marked_packets_rate",
            render: (text, record) => {
                return {
                    children: text,
                    props: {
                        rowSpan: record.rowSpan
                    }
                };
            }
        },
        {title: "Queue Name", dataIndex: "queue_name", isString: true},
        {title: "Pfc Deadlock Monitor Count", dataIndex: "pfc_deadlock_monitor_count"},
        {title: "Pfc Deadlock Recovery Count", dataIndex: "pfc_deadlock_recovery_count"},
        {title: "Receive Pfc Pause Frames", dataIndex: "receive_pfc_pause_frames"},
        {title: "Receive Pfc Pause Frames Rate", dataIndex: "receive_pfc_pause_frames_rate"},
        {title: "Send Pfc Pause Frames", dataIndex: "send_pfc_pause_frames"},
        {title: "Send Pfc Pause Frames Rate", dataIndex: "send_pfc_pause_frames_rate"}
    ];

    const columns = columnsConfig.map(obj => ({
        ...obj,
        sorter: obj.sorter === false ? obj.sorter : getSorter(obj.dataIndex, obj.isString),
        width: obj.width || 120
    }));

    const reformatData = (data, key) => {
        return data
            .reduce((result, item) => {
                if (result.indexOf(item[key]) < 0) {
                    result.push(item[key]);
                }
                return result;
            }, [])
            .reduce((result, value, rownum) => {
                const children = data.filter(item => item[key] === value);
                result = result.concat(
                    children.map((item, index) => ({
                        ...item,
                        rowSpan: index === 0 ? children.length : 0,
                        rownum: rownum + 1
                    }))
                );
                return result;
            }, []);
    };

    const fetchData = async () => {
        const response = await fetchAIInfo(sn);
        if (response.status !== 200) {
            message.error(response.info);
        } else {
            const rawData = response.data.map(item => {
                return {
                    name: item.interface_name || "-",
                    ecn_marked_packets: item?.ecn_marked_packets || "-",
                    ecn_marked_packets_rate: item?.ecn_marked_packets_rate || "-",
                    queue_name: item?.queue_name || "-",
                    pfc_deadlock_monitor_count: item?.pfc_deadlock_monitor_count.toString() || "-",
                    pfc_deadlock_recovery_count: item?.pfc_deadlock_recovery_count.toString() || "-",
                    receive_pfc_pause_frames: item?.receive_pfc_pause_frames.toString() || "-",
                    receive_pfc_pause_frames_rate: item?.receive_pfc_pause_frames_rate.toString() || "-",
                    send_pfc_pause_frames: item?.send_pfc_pause_frames.toString() || "-",
                    send_pfc_pause_frames_rate: item?.send_pfc_pause_frames_rate.toString() || "-"
                };
            });
            const formattedData = reformatData(rawData, "name");
            formattedData.sort((a, b) => compareNumbers(a.name, b.name));
            setFormattedData(formattedData);
        }
    };

    useEffect(() => {
        if (active) {
            fetchData();
        }
    }, [active, sn]);

    return <AmpConCustomTelemteryTable columnsConfig={columns} data={formattedData} tableWidth={tableWidth} />;
};

const ModulesOverview = ({active, sn, tableWidth}) => {
    const [formattedData, setFormattedData] = useState([]);

    const columnsConfig = [
        {title: "Port Name", dataIndex: "name", fixed: "left", isString: true, sorter: false},
        {title: "Connector Type", dataIndex: "connector_type", isString: true, width: 190},
        {title: "Form Factor", dataIndex: "form_factor", isString: true},
        {title: "Vendor", dataIndex: "vendor", isString: true},
        {title: "Vendor Part", dataIndex: "vendor_part", isString: true},

        {
            title: "Transmission Distance",
            dataIndex: "transmission_distance",
            isString: true,
            render: text => <div style={{whiteSpace: "pre-line"}}>{text}</div>
        },
        {title: "Transmission Rate", dataIndex: "transmission_rate", isString: true},
        {title: "WaveLength", dataIndex: "wavelength"},
        {title: "Input Power", dataIndex: "input_power"},
        {title: "Output Power", dataIndex: "output_power"},
        {title: "Output Power - Input Power", dataIndex: "attenuation"},
        {title: "Temperature", dataIndex: "temperature"}
    ];

    const columns = columnsConfig.map(obj => ({
        ...obj,
        sorter: obj.sorter === false ? obj.sorter : getSorter(obj.dataIndex, obj.isString),
        width: obj.width || 120
    }));

    const fetchData = async () => {
        const response = await fetchModulesInfo(sn);
        if (response.status !== 200) {
            message.error(response.info);
        } else {
            const formattedData = Object.keys(response.data).map(key => {
                const port = response.data[key];
                return {
                    name: key,
                    connector_type: port?.transceiver_state?.connector_type.split(":")[1] || "-",
                    form_factor: port?.transceiver_state?.form_factor.split(":")[1] || "-",
                    vendor: port?.transceiver_state?.vendor || "-",
                    vendor_part: port?.transceiver_state?.vendor_part || "-",
                    transmission_distance:
                        port?.transceiver_state?.fsconfig_platform_transceiver_extensions_transmission_distance
                            .split(",")
                            .join("\n") || "-",
                    transmission_rate:
                        port?.transceiver_state?.fsconfig_platform_transceiver_extensions_transmission_rate || "-",
                    wavelength: port?.transceiver_state?.fsconfig_platform_transceiver_extensions_wavelength || 0,
                    input_power: port?.transceiver_state?.input_power || 0,
                    output_power: port?.transceiver_state?.output_power || 0,
                    temperature: port?.transceiver_state?.temperature || 0,
                    attenuation: port?.transceiver_state?.attenuation || 0
                };
            });
            formattedData.sort((a, b) => compareNumbers(a.name, b.name));
            setFormattedData(formattedData);
        }
    };

    useEffect(() => {
        if (active) {
            fetchData();
        }
    }, [active, sn]);

    return <AmpConCustomTelemteryTable columnsConfig={columns} data={formattedData} tableWidth={tableWidth} />;
};

const MacTableOverview = ({active, sn, tableWidth}) => {
    const [formattedData, setFormattedData] = useState([]);

    const columnsConfig = [
        {title: "MAC address", dataIndex: "mac", fixed: "left", isString: true},
        {title: "Interface", dataIndex: "interface", isString: true},
        {title: "Vlan", dataIndex: "vlan", isString: true},
        {title: "Age", dataIndex: "age", isString: true},
        {title: "Type", dataIndex: "type", isString: true}
    ];

    const columns = columnsConfig.map(obj => ({
        ...obj,
        sorter: obj.sorter === false ? obj.sorter : getSorter(obj.dataIndex, obj.isString),
        width: obj.width || 120
    }));

    const fetchData = async () => {
        const response = await fetchMacTable(sn);
        if (response.status !== 200) {
            message.error(response.info);
        } else {
            const formattedData = response.data.map(item => {
                return {
                    name: item.mac_address,
                    mac: item.mac_address,
                    interface: item.interface,
                    vlan: item.entry_vlan,
                    age: item.age,
                    type: item.entry_type
                };
            });
            formattedData.sort((a, b) => compareNumbers(a.name, b.name));
            setFormattedData(formattedData);
        }
    };

    useEffect(() => {
        if (active) {
            fetchData();
        }
    }, [active, sn]);

    return <AmpConCustomTelemteryTable columnsConfig={columns} data={formattedData} tableWidth={tableWidth} showSetting={false} />;
};

const ArpTableOverview = ({active, sn, tableWidth}) => {
    const [formattedData, setFormattedData] = useState([]);

    const columnsConfig = [
        {title: "Address", dataIndex: "ip", fixed: "left", isString: true},
        {title: "HW Address", dataIndex: "hw_address", isString: true},
        {title: "Type", dataIndex: "type", isString: true},
        {title: "Interface", dataIndex: "interface", isString: true}
    ];

    const columns = columnsConfig.map(obj => ({
        ...obj,
        sorter: obj.sorter === false ? obj.sorter : getSorter(obj.dataIndex, obj.isString),
        width: obj.width || 120
    }));

    const fetchData = async () => {
        const response = await fetchArpTable(sn);
        if (response.status !== 200) {
            message.error(response.info);
        } else {
            const formattedData = response.data.map(item => {
                return {
                    name: item.ip,
                    ip: item.ip,
                    interface: item.interface_name,
                    hw_address: item.link_layer_address,
                    type: item.origin
                };
            });
            formattedData.sort((a, b) => compareNumbers(a.name, b.name));
            setFormattedData(formattedData);
        }
    };

    useEffect(() => {
        if (active) {
            fetchData();
        }
    }, [active, sn]);

    return <AmpConCustomTelemteryTable columnsConfig={columns} data={formattedData} tableWidth={tableWidth} showSetting={false} />;
};

export default SwitchTelemetry;
