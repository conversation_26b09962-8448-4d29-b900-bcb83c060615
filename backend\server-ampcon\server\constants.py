
import os
import re
import subprocess

# version and revision
import configparser

# VERSION = '1.13.1'
# try:
#     base_dir = os.path.dirname(os.path.abspath(__file__))
# except NameError:
#     base_dir = None
# if base_dir is not None and os.path.exists(os.path.join(base_dir, ".commit")):
#     with open(os.path.join(base_dir, ".commit")) as fp:
#         REVISION = fp.read().strip()
# else:
#     REVISION = None

VERSION = None
try:
    base_dir = os.path.dirname(os.path.abspath(__file__))
except NameError:
    base_dir = None
with open(os.path.join(base_dir, ".env"), "r") as file:
    for line in file:
        if line.startswith("REACT_APP_VERSION="):
            VERSION = line.split("=")[1].strip()



# static email address
RMA_EMAIL = '<EMAIL>'

# upgrade default post-xorplus scripts path
UPGRADE_DEFAULT_SCRIPTS = 'image/upgrade_scripts/'

# upgrade tmp post-xorplus scripts path
UPGRADE_TMP_SCRIPTS = 'tmp/'

# upgrade group post-xorplus scripts path
UPGRADE_GROUP_SCRIPTS = 'group/'

POST_XORPLUS = 'post-xorplus'

AGENT_CONF = '/opt/auto-deploy/auto-deploy.conf'

SYSTEM_BACKUP_DIR = 'system_backup'

# openvpn configuration file
OPENVPN_CONFIG_FILE = '/etc/openvpn/server.conf'

# automation.ini
AUTOMATION_CONFIG_FILE = '/usr/share/automation/server/automation.ini'

# ansible playbook dir
ANSIBLE_PLAYBOOK_DIR = '/usr/share/automation/server/ansible_playbook/'

# ampcon base dir
AMPCON_BASE_DIR = '/usr/share/automation/server/'

# template_form_format.json file
TEMPLATE_FORM_FORMAT_FILE = '/usr/share/automation/server/template_form_format.json'

# for parse image info
IMAGE_NAME_REGEX_MAPPING = {
    'white_box_stable_release': re.compile(r'^onie-installer-PicOS-(?P<version>[a-zA-Z\d.]+)-(?P<revision>[a-zA-Z\d]+)-(?P<platform>x86h|as4610|x86)\.bin$'),
    'black_box_stable_release': re.compile(r'^(?P<platform>[a-zA-Z\d]+)-PicOS-(?P<version>[a-zA-Z\d.]+)-(?P<revision>[a-zA-Z\d]+).bin$'),
    'white_box_stable_x86h_release': re.compile(r'^PicOS-(?P<version>[a-zA-Z\d.]+)-(?P<revision>[a-zA-Z\d]+)-fs-(?P<platform>x86h)\.bin$'),
    'black_box_x86_stable_release': re.compile(r'^PicOS-(?P<version>[a-zA-Z\d.]+)-(?P<revision>[a-zA-Z\d]+)-fs-(?P<platform>x86)\.bin$'),
    'old_white_box': re.compile(r'^onie-installer-picos-(?P<version>[\d.]+)-(?P<revision>[a-zA-Z\d]+)-(?P<platform>x86|as4610|n3100|n3000)\.bin$'),
    'new_white_box_stable_release': re.compile(r'^onie-installer-picos-(?P<version>[\d.]+)-(?P<platform>[a-zA-Z\d]+)\.bin'),
    'new_white_box_transition_release': re.compile(r'^onie-installer-PICOS-(?P<version>[\d.]+)-fs-(?P<platform>[a-zA-Z\d]+)\.bin$'),
    'new_white_box_transition_research': re.compile(r'^onie-installer-PICOS-(?P<version>[\d.]+)-(?P<revision>[a-zA-Z\d]+)-fs-(?P<platform>[a-zA-Z\d]+)\.bin$'),
    'new_black_box_data_center_research': re.compile(r'^picos-(?P<version>[\d.]+)-(?P<revision>[a-zA-Z\d]+)-fs-(?P<platform>[a-zA-Z\d]+)\.bin$'),
    'new_black_box_campus_research': re.compile(r'^(?P<platform>[a-zA-Z\d]+)_picos-(?P<version>[\d.]+)-(?P<revision>[a-zA-Z\d]+)-fs.bin$'),
    'new_black_box_data_center_release': re.compile(r'^picos-(?P<version>[\d.]+)-fs-(?P<platform>[a-zA-Z\d]+)\.bin$'),
    'new_black_box_campus_release': re.compile(r'^(?P<platform>[a-zA-Z\d]+)_picos-(?P<version>[\d.]+)-fs.bin$'),
    'new_s3410_busy_box_release': re.compile(r'^(?P<platform>[a-zA-Z\d]+)_picos-(?P<version>[\d.]+)-(?P<revision>[a-zA-Z\d]+).bin$')
}

class SSH_MODE:
    LINUX = 0
    CLI = 1
    CONFIGURE = 2


class ImportType:
    DEPLOY = 0
    IMPORT = 1
    RMA = 2


# switch status
class SwitchStatus:
    INIT = 'Init'
    STAGING = 'Registered Not-staged'
    CONFIGURED = 'Configured'
    STAGED = 'Staged'
    REGISTERED = 'Registered'
    PROVISIONING_SUCCESS = 'Provisioning Success'
    PROVISIONING_FAILED = 'Provisioning Failed'
    IMPORTED = 'Imported'
    DECOM = 'DECOM'
    DECOM_INIT = 'DECOM-Init'
    DECOM_PENDING = 'DECOM-Pending'
    DECOM_MANUAL = 'DECOM-Manual'
    RMA = 'RMA'


class LicenseStatus:
    ACTIVE = 'Active'
    EXPIRING = 'Expiring'
    NOLICENSE = 'No License'
    EXPIRED = 'Expired'
    UNKNOWN = 'Unknown'

# switch ansible status
REACHABLE = 0
UN_REACHABLE = 1
UNKNOWN = 2

# rma_status
RMA_UNKNOWN = 0
RMA_UN_REACHABLE = 1
RMA_FAILED = 2
RMA_ACTIVE = 3
RMA_UPGRADING = 4

# rma_back_status
RMA_BACK_UNKNOWN = 0
RMA_BACK_AUTO = 1
RMA_BACK_MANUAL = 2

# switch_config_backup_status
SWITCH_BACK_UNKNOWN = 0
SWITCH_BACK_AUTO = 1
SWITCH_BACK_MANUAL = 2

# switch default user
DEFAULT_USER = 'admin'
DEFAULT_PASSWORD = 'pica8'

BASE_DIR = '/home/<USER>/'
CONFIG_PATH = BASE_DIR + 'auto.config'
FLAG_FILE = BASE_DIR + 'auto_flag'
REBOOT_SLEEP_TIME = 240
ANSIBLE_TMP = BASE_DIR + '.ansible'
BACKUP_FILE = '/etc/picos/backup_files.lst'


OVSDB_CONFIG_NORMAL = 'normal'
OVSDB_CONFIG_ERROR = 'error'


INTERNAL_PLAYBOOK_GIT_DOWNLOAD_URL = 'https://github.com/pica8/Ansible/archive/refs/heads/main.zip'
INTERNAL_TEMPLATE_GIT_DOWNLOAD_URL = 'https://github.com/pica8/Jinja_Templates/archive/refs/heads/main.zip'


PICOS_V_SN = 'PICOS-V'
try:
    PICOS_V_IP = re.search('ip=\'(.*?)\'', subprocess.getstatusoutput('''virsh net-dumpxml default |grep "mac='.*' name='.*' ip='.*'"''')[1]).group(1).strip()
except:
    PICOS_V_IP = '**************'
PICOS_V_USERNAME = 'admin'
PICOS_V_PASSWORD = 'pica8'
GROUP_TYPE_DICT = {'upgrade': 'group_upgrade', 'push': 'group_push_image'}
DEFAULT_MAX_BACKUP_COUNT = 3
RANGE_MAX_BACKUP_COUNT = (1, 20)

DB_BACKUP_IGNORE_TABLE = ('db_backup', 'ansible_playbook', 'ansible_job', 'ansible_job_result', 'switch_systeminfo', 'switch_image_info')

FAKE_PASSWORD = '********'

CLI_UPDATE_URL = 'https://csp.pica8.com/sw-prod/Ampcon/PICOS-cli-Templates/automation.zip'

CLI_UPDATE_SQL_PATH = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'tmp', 'cli_tree')

SWITCH_MODEL_UPDATE_URL = 'https://csp.pica8.com/sw-prod/Ampcon/PICOS-cli-Templates/switch_model_update.zip'

SWITCH_MODEL_UPDATE_PATH = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'tmp', 'switch_model')

NAME_MATCH_REGEX = re.compile('^[\w_\-\s\:]+$')

LATITUDE_REGEX = re.compile('^(\-|\+)?([0-8]?\d{1}\.\d{0,6}|90\.0{0,6}|[0-8]?\d{1}|90)$')

LONGITUDE_REGEX = re.compile('^(\-|\+)?(((\d|[1-9]\d|1[0-7]\d|0{1,3})\.\d{0,6})|(\d|[1-9]\d|1[0-7]\d|0{1,3})|180\.0{0,6}|180)$')

GLOBAL_CONFIG_TAG = 'Global'

BLACK_BOX_MODEL_LIST = ['S5860-20SQ', 'S5860-24XB-U', 'S5810-48TS-P', 'S5810-28TS', 'S5810-28FS', 'S5810-48TS', 'S5810-48FS', 'N8560-32C', 'S5860-24MG-U', 'S5860-48XMG-U', 'S5860-24XMG', 'S5860-48MG-U', 'S5860-48XMG']

BUSY_BOX_MODEL_LIST = ['S3410L-24TF', 'S3410L-24TF-P', 'S3410-24TS', 'S3410-24TS-P', 'S3410C-16TF', 'S3410C-16TF-P', 'S3410C-16TMS-P', 'S3410C-8TMS-P']

SWITCH_MODEL_USE_UPGRADE_INSTEAD_OF_UPGRADE2 = ['S5810-48TS-P', 'S5810-28TS', 'S5810-28FS', 'S5810-48TS', 'S5810-48FS']

SWITCH_MODEL_STOP_PICOS_BEFORE_UPGRADE = ['S5860-20SQ', 'S5860-24XB-U', 'S5810-48TS-P', 'S5810-28TS', 'S5810-28FS', 'S5810-48TS', 'S5810-48FS', 'S5860-24MG-U', 'S5860-48XMG-U', 'S5860-24XMG', 'S5860-48MG-U', 'S5860-48XMG', 'S3410L-24TF', 'S3410L-24TF-P', 'S3410-24TS', 'S3410-24TS-P', 'S3410C-16TF', 'S3410C-16TF-P', 'S3410C-16TMS-P', 'S3410C-8TMS-P']

SWITCH_MODEL_SAVE_CONFIG_BEFORE_STOP_PICOS = ['S5860-20SQ', 'S5860-24XB-U', 'S5810-48TS-P', 'S5810-28TS', 'S5810-28FS', 'S5810-48TS', 'S5810-48FS', 'S5860-24MG-U', 'S5860-48XMG-U', 'S5860-24XMG', 'S5860-48MG-U', 'S5860-48XMG', 'S3410L-24TF', 'S3410L-24TF-P', 'S3410-24TS', 'S3410-24TS-P', 'S3410C-16TF', 'S3410C-16TF-P', 'S3410C-16TMS-P', 'S3410C-8TMS-P']

SWITCH_MODEL_ENABLE_ECN_PFC = ["as7726_32x", "as7326_56x", "S5248F-ON", "S5296F-ON", "S5232F-ON", "N8560-32C", "as9716_32d"]

SWITCH_USE_MNT_PATH_INSTEAD_OF_CFTMP_WHEN_UPGRADING = ['S3410L-24TF', 'S3410L-24TF-P', 'S3410-24TS', 'S3410-24TS-P', 'S3410C-16TF', 'S3410C-16TF-P', 'S3410C-16TMS-P', 'S3410C-8TMS-P']

server_config = configparser.ConfigParser()
server_config.read(AUTOMATION_CONFIG_FILE)
try:
    connect_config = server_config.get('database', 'connection')
    pattern = re.compile(r'mysql\+pymysql://(.*)@')
    database_connection = pattern.findall(connect_config)[0]
    DATABASE_USER, DATABASE_PASSWD = database_connection.split(':')
except:
    DATABASE_USER = 'root'
    DATABASE_PASSWD = 'root'

class outswitch_status:
    UNKNOWN = 0
    UN_REACHABLE = 1
    FAILED = 2
    ACTIVE = 3
    DECOM = 4
    # DECOM_PENDING = 5
    DECOM_MANUAL = 5
    UPGRADING = 0
    UPGRADED = 1
    UPGRADE_FAILED = 2
    NO_UPGRADE = 3

