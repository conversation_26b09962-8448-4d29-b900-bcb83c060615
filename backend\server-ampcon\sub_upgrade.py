import json
import os
import sys
import re
import configparser
from datetime import datetime

import pymysql


def exec_cmd(cmd):
    r = os.popen(cmd)
    text = r.read()
    r.close()
    return text


def import_sql(path, database_user, database_passwd, database_host):
    if os.path.isfile(path):
        exec_cmd(
            f'cd {os.path.split(path)[0]} && mysql -u{database_user} -p{database_passwd} -h{database_host} -D automation < {os.path.split(path)[1]}')


def get_server_config():
    update_automation_config_path = os.path.join(sys.path[0], "server/automation.ini")
    if not os.path.isfile(update_automation_config_path):
        return None
    update_server_config = configparser.ConfigParser()
    update_server_config.read(update_automation_config_path)
    update_supported_list = update_server_config.get('DEFAULT', 'supports_models').replace('\n', '').split(',')
    update_supported_list = list(set(update_supported_list))
    database_connection = update_server_config.get('database', 'connection')
    database_parse_result = re.search(r'.*://(\w+):(\w+)@([\w-]+)/.*', database_connection)
    database_config = {
        'username': database_parse_result.group(1),
        'password': database_parse_result.group(2),
        'host': database_parse_result.group(3),
    }
    return {
        'update_supported_list': update_supported_list,
        'database_config': database_config
    }


def import_automation_sql(database_user, database_passwd, database_host):
    import_sql('./server/custom_sql/cli_tree.sql', database_user, database_passwd, database_host)
    import_sql('./server/custom_sql/compatibility.sql', database_user, database_passwd, database_host)
    import_sql('./server/custom_sql/hardware_mapping.sql', database_user, database_passwd, database_host)
    import_sql('./server/custom_sql/model_physic_port.sql', database_user, database_passwd, database_host)
    import_sql('./server/custom_sql/switch_systeminfo.sql', database_user, database_passwd, database_host)


def update_images_info(database_user, database_passwd, database_host):
    conn = pymysql.connect(
        host=database_host,
        port=3306,
        user=database_user,
        password=database_passwd,
        database='automation',
        charset='utf8'
    )
    cursor = conn.cursor(cursor=pymysql.cursors.DictCursor)
    with open("./server/default_imgs.json", "r") as f2:
        di = json.load(f2)

    with open("./server/model_platform_mapping.json", "r") as f:
        mp = json.load(f)

    # for parse image info
    IMAGE_NAME_REGEX_MAPPING = {
        'white_box_stable_release': re.compile(
            r'^onie-installer-PicOS-(?P<version>[a-zA-Z\d.]+)-(?P<revision>[a-zA-Z\d]+)-(?P<platform>x86h|as4610|x86)\.bin$'),
        'black_box_stable_release': re.compile(
            r'^(?P<platform>[a-zA-Z\d]+)-PicOS-(?P<version>[a-zA-Z\d.]+)-(?P<revision>[a-zA-Z\d]+).bin$'),
        'white_box_stable_x86h_release': re.compile(
            r'^PicOS-(?P<version>[a-zA-Z\d.]+)-(?P<revision>[a-zA-Z\d]+)-fs-(?P<platform>x86h)\.bin$'),
        'black_box_x86_stable_release': re.compile(
            r'^PicOS-(?P<version>[a-zA-Z\d.]+)-(?P<revision>[a-zA-Z\d]+)-fs-(?P<platform>x86)\.bin$'),
        'old_white_box': re.compile(
            r'^onie-installer-picos-(?P<version>[\d.]+)-(?P<revision>[a-zA-Z\d]+)-(?P<platform>x86|as4610|n3100|n3000)\.bin$'),
        'new_white_box_stable_release': re.compile(
            r'^onie-installer-picos-(?P<version>[\d.]+)-(?P<platform>[a-zA-Z\d]+)\.bin'),
        'new_white_box_transition_release': re.compile(
            r'^onie-installer-PICOS-(?P<version>[\d.]+)-fs-(?P<platform>[a-zA-Z\d]+)\.bin$'),
        'new_white_box_transition_research': re.compile(
            r'^onie-installer-PICOS-(?P<version>[\d.]+)-(?P<revision>[a-zA-Z\d]+)-fs-(?P<platform>[a-zA-Z\d]+)\.bin$'),
        'new_black_box_data_center_research': re.compile(
            r'^picos-(?P<version>[\d.]+)-(?P<revision>[a-zA-Z\d]+)-fs-(?P<platform>[a-zA-Z\d]+)\.bin$'),
        'new_black_box_campus_research': re.compile(
            r'^(?P<platform>[a-zA-Z\d]+)_picos-(?P<version>[\d.]+)-(?P<revision>[a-zA-Z\d]+)-fs.bin$'),
        'new_black_box_data_center_release': re.compile(
            r'^picos-(?P<version>[\d.]+)-fs-(?P<platform>[a-zA-Z\d]+)\.bin$'),
        'new_black_box_campus_release': re.compile(r'^(?P<platform>[a-zA-Z\d]+)_picos-(?P<version>[\d.]+)-fs.bin$'),
        'new_s3410_busy_box_release': re.compile(r'^(?P<platform>[a-zA-Z\d]+)_picos-(?P<version>[\d.]+)-(?P<revision>[a-zA-Z\d]+).bin$')
    }

    def get_image_type(image_name):
        for image_type, regex in IMAGE_NAME_REGEX_MAPPING.items():
            match = regex.match(image_name)
            if match:
                return image_type
        return None

    def get_image_info(image_name):
        image_type = get_image_type(image_name)
        if not image_type:
            return None
        else:
            match = IMAGE_NAME_REGEX_MAPPING[image_type].match(image_name)
            if match:
                temp = match.groupdict()
                return {
                    'version': temp['version'],
                    'revision': temp['revision'] if 'revision' in temp else '',
                    'platform': temp[
                        'platform'].lower() if image_type == 'new_black_box_campus_research' or image_type == 'new_black_box_campus_release' or image_type == 'new_s3410_busy_box_release' or image_type == 'black_box_stable_release' else
                    temp['platform'],
                }
            else:
                return None

    def get_image_name_list():
        image_name_list = []
        cursor.execute("select distinct image_name from switch_image_info")
        for row in cursor.fetchall():
            image_name_list.append(row['image_name'])
        return image_name_list

    for k, v in di.items():
        image_name = v["img"].split("/")[-1]
        image_path = "img/%s" % image_name
        image_md5_path = "img/%s" % v["md5"].split("/")[-1]
        # for black box x86 use different bin file
        if k == 'x86-fs':
            k = 'x86'
        elif k == 'x86h':
            k = 'x86'
        platform = k
        v_dict = get_image_info(image_name)
        version = v_dict['version']
        revision = v_dict['revision']
        #for exclude duplicate image name
        if image_name in get_image_name_list():
            init_switch_image_sql = "update switch_image_info set image_name=%s, image_path=%s, image_md5_path=%s, platform=%s, version=%s, revision=%s, create_time=%s, modified_time=%s where image_name=%s"
            cursor.execute(init_switch_image_sql,
                           (image_name, image_path, image_md5_path, platform, version, revision,datetime.now().strftime("%Y-%m-%d %H:%M:%S"), datetime.now().strftime("%Y-%m-%d %H:%M:%S"),image_name))
        else:
            init_switch_image_sql = "insert into switch_image_info(image_name, image_path, image_md5_path, platform, version, revision, create_time, modified_time) values (%s, %s, %s, %s, %s, %s, %s, %s);"
            cursor.execute(init_switch_image_sql,
                           (image_name, image_path, image_md5_path, platform, version, revision,
                            datetime.now().strftime("%Y-%m-%d %H:%M:%S"), datetime.now().strftime("%Y-%m-%d %H:%M:%S")))

    cursor.execute("select * from switch_systeminfo")
    for row in cursor.fetchall():
        up_to_date_image_path = "img/%s" % (di[mp[row["model"]]]["img"].split("/")[-1])
        up_to_date_image_md5_path = "img/%s" % (di[mp[row["model"]]]["md5"].split("/")[-1])
        v_dict = get_image_info(up_to_date_image_path.split("/")[-1])
        update_to_date_version = "%s/%s" % (v_dict['version'], v_dict['revision'])
        update_sql = "update switch_systeminfo set platform = %s, up_to_date_version=%s, up_to_date_image_path=%s, up_to_date_image_md5_path=%s, up_to_date_onie_path=%s where model=%s"
        cursor.execute(update_sql, (
            'x86' if mp[row["model"]] == 'x86-fs' or mp[row["model"]] == 'x86h' else mp[row["model"]], update_to_date_version, up_to_date_image_path,
            up_to_date_image_md5_path,
            up_to_date_image_path, row["model"]))
    conn.commit()


if __name__ == '__main__':
    server_config = get_server_config()
    import_automation_sql(server_config['database_config']['username'], server_config['database_config']['password'],
                          server_config['database_config']['host'])
    update_images_info(server_config['database_config']['username'], server_config['database_config']['password'],
                       server_config['database_config']['host'])