import {useSelector} from "react-redux";
import ServiceView5 from "@/modules-otn/pages/otn/service/service_view_5";

const ServiceViewContainer = props => {
    const {
        tableFilter: {key, provision}
    } = useSelector(state => state.map);
    const type = key?.startsWith("nms:connection");
    if (provision) {
        if (!type) return <ServiceView5 clientSelectTransfer={props.clientSelectTransfer} />;
    }
};

export default ServiceViewContainer;
